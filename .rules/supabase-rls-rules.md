---
inclusion: always
---

# 🔐 Supabase RLS Guardrails for AI Code Generation

## Purpose
This document defines rules for AI-assisted code generation to ensure **Row-Level Security (RLS)** is always considered when working with Supabase.  
The goal is to prevent accidental data leaks or insecure defaults.

---

## ✅ Core Rules

1. **Assume RLS is enabled**
   - All queries must respect RLS policies.
   - Never assume unrestricted access to tables.

2. **Enable RLS by default**
   - When generating new tables, always include:

```sql
ALTER TABLE <table_name> ENABLE ROW LEVEL SECURITY;
```

3. **Always generate at least one restrictive policy**
   - Example:

```sql
CREATE POLICY user_select_own ON <table_name>
  FOR SELECT TO authenticated
  USING (auth.uid() = user_id);
```

4. **Client-side code must align with RLS**
   - Scope queries to the authenticated user or policy conditions.
   - Example:

```js
const { data } = await supabase
  .from("profiles")
  .select("*")
  .eq("id", user.id); // matches RLS policy
```

5. **Warn if <PERSON><PERSON> is missing**
   - If a table is created without <PERSON><PERSON>, output a warning:

> ⚠️ Warning: RLS is not enabled on `<table_name>`.  
> Consider enabling it before exposing via Supabase APIs.

6. **Restrict `service_role` usage**
   - Only suggest `service_role` for trusted server-side code.
   - Never use it in client-side examples.

---

## ❌ Anti-Patterns (Do Not Generate)

**Creating tables without RLS:**

```sql
CREATE TABLE messages (
  id uuid,
  user_id uuid,
  content text
);
```

⚠️ **Problem:** Missing `ALTER TABLE ... ENABLE ROW LEVEL SECURITY;`

---

**Client-side queries that fetch all rows:**

```js
const { data } = await supabase.from("profiles").select("*");
```

⚠️ **Problem:** Ignores RLS, may leak data

---

## ✅ Best Practices (Do Generate)

**Secure table creation:**

```sql
CREATE TABLE messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  content text
);

ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

CREATE POLICY user_select_own_messages ON messages
  FOR SELECT TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY user_insert_own_messages ON messages
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = user_id);
```

---

**Client-side query respecting RLS:**

```js
const { data } = await supabase
  .from("messages")
  .select("*")
  .eq("user_id", user.id);
```

---

## 🛡️ Enforcement Strategy

**System Prompting**
Always assume RLS is enabled. Generate SQL and Supabase client code that respects RLS.
If RLS is not enabled, warn the user.
When creating new tables, enable RLS and add restrictive policies by default.

**Validation Hook**
- Check generated SQL for `ENABLE ROW LEVEL SECURITY`.
- Check for at least one `CREATE POLICY`.
- If missing, auto-fix or warn.

---

## 🚀 Summary

- **Default secure**: RLS ON, restrictive policies.  
- **Client-side safe**: Queries scoped to `auth.uid()` or policy conditions.  
- **Server-side explicit**: `service_role` only when requested.  
- **Warn on insecure defaults**.