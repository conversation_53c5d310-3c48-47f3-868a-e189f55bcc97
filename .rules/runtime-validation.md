---
inclusion: always
---

# Runtime Validation Conventions

These rules define how runtime validation must be applied.  
They are **mandatory** for all untrusted/external data.

---

## 1. Always Validate External Data
- All API responses, DB results, user input, and env vars must be validated at runtime.

---

## 2. Use Zod (Preferred)
- Use Zod for all runtime validation.
- Prefer `.pipe()` for chaining transformations and refinements.

**Example:**
```ts
const AgeSchema = z.string()
  .pipe(z.coerce.number())
  .pipe(z.number().int().positive());
```

---

## 3. Generate Schemas from Backend
- Prefer generated schemas over handwritten ones.
- Tools: `supabase-zod-types`, `zod-prisma-types`, `zod-openapi`, `graphql-codegen`

---

## 4. Validate Environment Variables
- Parse `import.meta.env` with Zod at startup.
- Use `.pipe()` for coercion.

**Example:**
```ts
const EnvSchema = z.object({
  VITE_ENV: z.string().pipe(z.enum(['development', 'staging', 'production'])),
  VITE_PORT: z.string().pipe(z.coerce.number().int().positive()).default('3000'),
});
export const env = EnvSchema.parse(import.meta.env);
```

---

## 5. API Contracts
- Define request/response schemas in `src/types/contracts/`.
- Validate both client and server.
- Use `.pipe()` for query param coercion.

**Example:**
```ts
const GetUserRequestSchema = z.object({
  userId: z.string().uuid(),
  page: z.string().pipe(z.coerce.number().int().min(1)).default('1'),
});
```

---

## 6. Error Handling
- Use `safeParse` for non-throwing validation.

---

## 7. Testing with Validation
- Use schemas in test factories to ensure mocks are valid.
- Use `.pipe()` in test schemas when coercion is needed.

---
✅ **Summary**:  
Always validate external data, use Zod, prefer `.pipe()`, validate env vars, enforce API contracts, use `safeParse` for graceful handling, and validate test mocks.

---