# Supabase Migrations Guide

## 1. Golden Rule
>
> **Never** change the production database schema directly via the dashboard or raw SQL without creating a migration file.

---

## 2. Workflow for Schema Changes

### Step 1 — Create a new migration

```bash
supabase migration new <descriptive_name>
```

- `<descriptive_name>` should describe the change, e.g. `add_users_table` or `alter_orders_add_status`.
- This creates a `.sql` file in `supabase/migrations`.

---

### Step 2 — Edit the migration file

Write the SQL for your schema change inside the new migration file.

Example:

```sql
CREATE TABLE users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  email text UNIQUE NOT NULL,
  created_at timestamptz DEFAULT now()
);
```

---

### Step 3 — Apply migration locally

```bash
supabase db push
```

- Applies the migration to your **local** DB.
- Verify the change works locally before touching prod.

---

### Step 4 — Test locally

- Run your app against the local DB.
- Ensure queries, APIs, and auth still work.

---

### Step 5 — Push to production

```bash
supabase db push --linked
```

- Applies the migration to the **remote** DB.
- Only do this after local testing is complete.

---

## 3. Workflow for Pulling Remote Changes

If someone else made schema changes in prod (via migration):

```bash
supabase db pull
```

- Updates your local schema and migrations folder to match prod.

---

## 4. Workflow for Hotfixes in Production

If an urgent schema change must be made directly in prod:

1. Make the change in prod (via dashboard or SQL editor).
2. Immediately run:

   ```bash
   supabase db pull
   ```

   to sync the change into your local migrations folder.
3. Commit the updated migration to Git.

---

## 5. Data Changes

- **Migrations are for schema changes only** (tables, columns, indexes, constraints).
- For data changes (seeding, updates), use:
  - `supabase/seed.sql` for initial data
  - Separate SQL scripts for one-off data fixes

---

## 6. Baseline Resets

If migration history becomes corrupted or irrelevant (e.g., schema changes were made directly in prod without migrations):

### When to do it

- Migration history is broken and `supabase db pull` fails with a mismatch.
- Old migrations are incomplete or missing.
- You want to start fresh but **keep the current prod schema and data**.

### Steps

1. **Backup prod**  

   ```bash
   supabase db dump --linked --file pre_baseline_reset.sql
   ```

   *(If `db dump` fails due to password, export schema/data from the Supabase dashboard.)*

2. **Delete local migrations**  

   ```bash
   rm -rf supabase/migrations
   ```

3. **Pull current prod schema into a baseline migration**  

   ```bash
   supabase db pull
   ```

   This creates a single `.sql` file in `supabase/migrations` — your **baseline**.

4. **Mark baseline as applied locally**  

   ```bash
   supabase migration repair --status applied <baseline_id>
   ```

   Replace `<baseline_id>` with the timestamp from the baseline migration filename.

5. **Mark baseline as applied in prod** *(optional but recommended if team-wide reset)*  

   ```bash
   supabase migration repair --linked --status applied <baseline_id>
   ```

   This updates **only** the `schema_migrations` table in prod — no schema/data changes.

6. **Commit to Git**  

   ```bash
   git add supabase/migrations
   git commit -m "Baseline migration reset"
   git push
   ```

7. **Team reset**  
   Every dev runs:

   ```bash
   rm -rf supabase/migrations
   git pull
   supabase migration repair --status applied <baseline_id>
   supabase db pull --data # --data is optional only if data is needed, be careful of production data
   ```

---

## 7. Git Rules

- Always commit migration files to Git.
- Never edit old migration files after they’ve been applied to prod.
- One migration file per logical change.

---

## 8. AI Agent Safety Rules

When an AI agent is tasked with making schema changes:

- **Always** start with `supabase migration new`.
- **Never** run `supabase db push --linked` without explicit human approval.
- **Never** delete or modify old migrations unless doing a coordinated baseline reset.
- **Always** run migrations locally first and confirm success before touching prod.

---

## 9. Example AI Agent Prompt for Safe Migrations
>
> You are an AI developer working on a Supabase project.  
> When making schema changes:
>
> 1. Create a new migration with `supabase migration new <name>`.
> 2. Edit the migration file with the required SQL.
> 3. Apply locally with `supabase db push`.
> 4. Test locally.
> 5. Only push to prod with `supabase db push --linked` after human approval.
> 6. If pulling changes from prod, use `supabase db pull`.
> 7. Never modify or delete old migrations unless performing a coordinated baseline reset.

---
