---
inclusion: always
---

# 📂 Project Folder Structure

This is the **recommended folder structure** for a React + Supabase project.  
It is designed for **scalability, clarity, and type safety**.  

- **supabase/** → backend (migrations, edge functions, generated types)  
- **src/** → frontend React app (features, components, hooks, services, state)  
- **public/** → static assets  
- **config/meta files** → environment, TypeScript, Tailwind, Vite configs  

---

## Folder Tree

```plaintext
project-root/
├── supabase/                  # Supabase backend
│   ├── migrations/            # SQL migration files
│   ├── functions/             # Edge functions
│   │   └── example-fn/
│   │       ├── index.ts
│   │       └── types.ts
│   ├── types/                 # Supabase generated + shared types
│   │   ├── database.ts
│   │   └── index.ts
│   └── config.toml
│
├── src/                       # React frontend
│   ├── app/                   # App-level setup
│   │   ├── providers/         # Context providers (React Query, Theme, etc.)
│   │   ├── routes/            # Route definitions
│   │   └── App.tsx
│   │
│   ├── components/            # UI components
│   │   ├── ui/                # shadcn/ui (generated via CLI)
│   │   ├── common/            # Shared components (Navbar, Footer, etc.)
│   │   └── forms/             # Form components (React Hook Form wrappers)
│   │
│   ├── features/              # Feature-based modules
│   │   ├── auth/
│   │   │   ├── components/
│   │   │   ├── hooks/
│   │   │   └── services/
│   │   ├── profile/
│   │   │   ├── components/
│   │   │   ├── hooks/
│   │   │   └── services/
│   │   └── posts/
│   │       ├── components/
│   │       ├── hooks/
│   │       └── services/
│   │
│   ├── hooks/                 # Global reusable hooks
│   ├── lib/                   # Core libraries & singletons
│   │   ├── supabase.ts        # Supabase client init
│   │   ├── queryClient.ts     # React Query client
│   │   ├── store.ts           # Nanostore setup
│   │   └── utils.ts           # Utility functions (cn, formatters, etc.)
│   │
│   ├── services/              # Cross-cutting services
│   │   ├── apiClient.ts       # Fetch wrapper for edge functions
│   │   └── errorHandler.ts    # Normalized error handling
│   │
│   ├── stores/                # Nanostore atoms/maps
│   │   └── theme.ts
│   │
│   ├── styles/                # Global styles
│   │   ├── globals.css
│   │   └── tailwind.css
│   │
│   ├── types/                 # Shared TypeScript types
│   │   ├── api.ts
│   │   ├── index.ts
│   │   └── env.d.ts
│   │
│   └── main.tsx               # Vite entrypoint
│
├── public/                    # Static assets
│   └── favicon.ico
│
├── .env                       # Local environment variables
├── .env.example               # Example env file
├── tsconfig.json
├── tailwind.config.ts
├── postcss.config.js
├── package.json
└── vite.config.ts
```

---

## 🔑 Key Principles

1. **Feature-based structure**  
   - Each feature (auth, profile, posts) has its own `components/`, `hooks/`, and `services/`.  
   - Keeps related code together and makes scaling easier.  

2. **Separation of concerns**  
   - `lib/` → singletons (Supabase client, query client, utils).  
   - `services/` → cross-cutting logic (API wrappers, error handling).  
   - `stores/` → Nanostore atoms/maps for client state.  
   - `types/` → shared TypeScript types.  

3. **Supabase integration**  
   - `supabase/migrations/` → schema changes.  
   - `supabase/functions/` → edge functions.  
   - `supabase/types/` → generated DB types.  
   - `src/lib/supabase.ts` → single Supabase client instance.  

4. **Styling**  
   - `components/ui/` → shadcn/ui components (generated via CLI).  
   - `styles/` → global Tailwind styles.  
   - Use `cn` utility for class merging.  

5. **Scalability**  
   - New features live in `src/features/<feature>/`.  
   - Shared logic lives in `src/lib/`, `src/services/`, or `src/hooks/`.  
   - Keeps the project clean as it grows.

---
