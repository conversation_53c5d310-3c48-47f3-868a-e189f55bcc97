---
inclusion: always
---

# 🧪 AI Agent Rule: Unit Testing for React + Supabase (Vite + Vitest)

## 1. General Principles
- **Test Pyramid Alignment**  
  - Focus on **unit tests** for components, hooks, and utilities.  
  - Use **integration tests** sparingly for Supabase queries and auth flows.  
  - Leave **end-to-end (E2E)** to tools like Playwright or Cypress.  

- **Isolation First**  
  - Mock Supabase client calls in unit tests.  
  - Avoid hitting the real database unless explicitly running integration tests.  

- **Determinism**  
  - Tests must be **repeatable** and **independent** of external state.  
  - No reliance on network or Supabase availability in unit tests.  

---

## 2. Test Structure
- **File Naming**  
  - Place tests alongside source files:  
    - `ComponentName.test.tsx`  
    - `useHookName.test.ts`  
  - Or in a `__tests__` folder if grouping is preferred.  

- **Test Categories**
  1. **React Components**  
     - Render with `@testing-library/react`.  
     - Assert DOM output, props, and event handling.  
     - Mock Supabase hooks or context providers.  

  2. **Custom Hooks**  
     - Use `renderHook` from `@testing-library/react-hooks`.  
     - Mock Supabase client responses.  

  3. **Utility Functions**  
     - Pure unit tests with no React or Supabase dependencies.  

  4. **Supabase Client Wrappers**  
     - If you have a `supabaseClient.ts` wrapper, mock it with `vi.mock`.  
     - Provide fake responses for `.from().select()`, `.insert()`, etc.  

---

## 3. Mocking Supabase
- **Strategy**  
  - Use `vi.mock` to replace Supabase client imports.  
  - Provide predictable mock responses for queries.  

- **Example**

```ts
// __mocks__/supabaseClient.ts
export const supabase = {
  from: vi.fn().mockReturnThis(),
  select: vi.fn().mockResolvedValue({ data: [], error: null }),
  insert: vi.fn().mockResolvedValue({ data: [{ id: 1 }], error: null }),
  auth: {
    signInWithPassword: vi.fn().mockResolvedValue({
      data: { user: {} },
      error: null,
    }),
    signOut: vi.fn().mockResolvedValue({ error: null }),
  },
};
```

Usage in tests:

```ts
import { supabase } from "../__mocks__/supabaseClient";
vi.mock("../supabaseClient", () => ({ supabase }));
```

---

## 4. React Component Test Example

```tsx
import { render, screen, fireEvent } from "@testing-library/react";
import { supabase } from "../__mocks__/supabaseClient";
import LoginForm from "./LoginForm";

vi.mock("../supabaseClient", () => ({ supabase }));

test("logs in user on submit", async () => {
  render(<LoginForm />);
  fireEvent.change(screen.getByLabelText(/email/i), {
    target: { value: "<EMAIL>" },
  });
  fireEvent.change(screen.getByLabelText(/password/i), {
    target: { value: "password123" },
  });
  fireEvent.click(screen.getByRole("button", { name: /login/i }));

  expect(supabase.auth.signInWithPassword).toHaveBeenCalledWith({
    email: "<EMAIL>",
    password: "password123",
  });
});
```

---

## 5. Vitest Config
- Ensure JSX + TSX support in `vite.config.ts`:

```ts
/// <reference types="vitest" />
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: "./src/setupTests.ts",
  },
});
```

- `setupTests.ts` can configure `@testing-library/jest-dom`:

```ts
import "@testing-library/jest-dom";
```

---

## 6. Best Practices
- ✅ Keep Supabase mocked in **unit tests**  
- ✅ Use **factories** or **fixtures** for mock data  
- ✅ Test **error states** (e.g., Supabase returning `error`)  
- ✅ Test **loading states** in components using async queries  
- ❌ Don’t test Supabase itself — only your integration with it  
- ❌ Don’t rely on real network/database in unit tests  

---

## 7. Optional: Integration Test Layer
- For higher confidence, create a separate test suite (`integration/`) that:  
  - Uses a **test Supabase project** or **local Supabase instance**  
  - Runs against real queries with seeded data  
  - Is excluded from CI unless explicitly triggered  

---

✅ Following this rule ensures your **unit tests stay fast, isolated, and reliable**, while still giving you a path to test real Supabase interactions when needed.

---
