# Task Execution Rules

## Core Principles

When executing tasks from specs, follow these rules to maintain focus and efficiency:

### 1. Task Scope Only
- **ONLY** implement what is explicitly requested in the task
- Do NOT create additional files beyond what the task requires
- Do NOT add "bonus" features or enhancements unless specifically asked
- Stay focused on the exact requirements listed in the task

### 2. No Automatic Documentation or Examples
- Do NOT create example files (e.g., `*Example.tsx`, `*Demo.tsx`) unless the task specifically requests them
- Do NOT create documentation files (e.g., `*.md` guides) unless the task specifically requests them
- Do NOT create migration guides, usage examples, or tutorial content unless explicitly required
- The task requirements are the complete scope - nothing more

### 3. Testing Requirements
- Only create tests if the task explicitly mentions testing
- If tests are required, create only the minimum tests needed to satisfy the task requirements
- Do not create comprehensive test suites unless specifically requested

### 4. File Creation Guidelines
- Only create files that are directly needed for the task implementation
- Do not create supporting files, utilities, or helpers unless they are essential for the task
- Do not create demonstration or showcase files

### 5. Task Completion Criteria
- A task is complete when all explicit requirements are satisfied
- Do not add additional work "for completeness" or "best practices"
- Mark the task as complete immediately after implementing the required functionality

## Examples

### ❌ What NOT to do:
```
Task: "Create a user authentication hook"
- ✅ Create the hook
- ❌ Create UserAuthExample.tsx
- ❌ Create auth-migration-guide.md
- ❌ Create comprehensive test suite (unless tests were requested)
- ❌ Create utility functions "that might be useful"
```

### ✅ What TO do:
```
Task: "Create a user authentication hook with unit tests"
- ✅ Create the hook
- ✅ Create unit tests (because explicitly requested)
- ✅ Mark task complete
```

## Exception Cases

The only exceptions to these rules are:

1. **When the task explicitly requests examples or documentation**
2. **When creating a file is essential for the core functionality to work**
3. **When the task specifically mentions "comprehensive" or "complete" implementation**

## Summary

- Implement exactly what is asked
- No extra files or documentation
- No examples unless requested
- Complete the task and move on