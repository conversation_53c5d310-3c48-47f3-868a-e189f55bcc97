# Database Type Generation Configuration
# This file contains configuration for generating TypeScript types from Supabase

# Supabase Project Configuration
SUPABASE_PROJECT_ID=ovfwiyqhubxeqvbrggbe

# Type Generation Settings
TYPES_OUTPUT_PATH=src/types/database.ts
TYPES_BACKUP_PATH=src/types/database.backup.ts

# Development vs Production
# When VITE_APP_SUPABASE_URL contains localhost/127.0.0.1, local generation is used
# Otherwise, remote generation with PROJECT_ID is used

# Local Development URLs (auto-detected)
LOCAL_SUPABASE_URL=http://127.0.0.1:54321

# Notes:
# - This file is used by scripts/generate-types.js
# - Local generation requires: npx supabase start
# - Remote generation requires: npx supabase login
# - Types are auto-generated - do not edit manually
