// ABOUTME: Test script to verify last accessed team persistence functionality
// Run this in the browser console to check if localStorage is working correctly

const STORAGE_KEY = 'shot_coach_last_team';

console.log('=== Testing Last Accessed Team Persistence ===');

// Function to get current user ID from supabase
function getCurrentUserId() {
  const session = localStorage.getItem('supabase.auth.token');
  if (session) {
    try {
      const parsed = JSON.parse(session);
      return parsed?.currentSession?.user?.id || null;
    } catch (e) {
      console.error('Error parsing session:', e);
      return null;
    }
  }
  return null;
}

// Function to check localStorage for last team
function checkLastTeam() {
  const userId = getCurrentUserId();
  if (!userId) {
    console.log('❌ No user logged in');
    return;
  }
  
  const storageKey = `${STORAGE_KEY}_${userId}`;
  const lastTeamId = localStorage.getItem(storageKey);
  
  console.log('Current User ID:', userId);
  console.log('Storage Key:', storageKey);
  console.log('Last Team ID in localStorage:', lastTeamId);
  
  // Check all localStorage keys related to SHOT
  console.log('\n📦 All SHOT-related localStorage keys:');
  Object.keys(localStorage).forEach(key => {
    if (key.includes('shot') || key.includes('team')) {
      console.log(`  ${key}: ${localStorage.getItem(key)}`);
    }
  });
}

// Function to manually set a test team
function setTestTeam(teamId) {
  const userId = getCurrentUserId();
  if (!userId) {
    console.log('❌ No user logged in');
    return;
  }
  
  const storageKey = `${STORAGE_KEY}_${userId}`;
  localStorage.setItem(storageKey, teamId);
  console.log(`✅ Set last team to: ${teamId}`);
}

// Function to simulate team switch
function simulateTeamSwitch(teamId) {
  console.log(`\n🔄 Simulating team switch to: ${teamId}`);
  
  // This simulates what CoachContext.setCurrentTeam does
  const userId = getCurrentUserId();
  if (userId) {
    localStorage.setItem(`${STORAGE_KEY}_${userId}`, teamId);
    console.log('✅ Team saved to localStorage');
  }
}

// Run tests
console.log('\n1️⃣ Current State Check:');
checkLastTeam();

console.log('\n2️⃣ Instructions:');
console.log('- To check current state: checkLastTeam()');
console.log('- To set a test team: setTestTeam("team-id-here")');
console.log('- To simulate team switch: simulateTeamSwitch("team-id-here")');
console.log('\n3️⃣ Next Steps:');
console.log('1. Note the current team ID');
console.log('2. Switch to a different team in the UI');
console.log('3. Run checkLastTeam() again to see if it saved');
console.log('4. Log out and log back in');
console.log('5. Run checkLastTeam() to see if it persists');

// Export functions to window for manual testing
window.shotTeamTest = {
  checkLastTeam,
  setTestTeam,
  simulateTeamSwitch,
  getCurrentUserId
};

console.log('\n✅ Test functions available at: window.shotTeamTest');