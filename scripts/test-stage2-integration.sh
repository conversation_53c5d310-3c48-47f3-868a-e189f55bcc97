#!/bin/bash

# Test script for Stage 2: API & Client Integration

echo "🚀 Testing Stage 2: API & Client Integration"
echo "==========================================="

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Supabase is running
echo -e "\n${YELLOW}1. Checking Supabase status...${NC}"
if supabase status > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Supabase is running${NC}"
else
    echo -e "${RED}✗ Supabase is not running. Starting it now...${NC}"
    supabase start
    sleep 5
fi

# Get service role key and API URL
SERVICE_ROLE_KEY=$(supabase status | grep -o 'service_role key: [^ ]*' | cut -d' ' -f3)
ANON_KEY=$(supabase status | grep -o 'anon key: [^ ]*' | cut -d' ' -f3)
API_URL=$(supabase status | grep -o 'API URL: [^ ]*' | cut -d' ' -f3)

echo -e "\n${YELLOW}2. Starting edge functions...${NC}"

# Function to start edge function in background
start_function() {
    local func_name=$1
    echo -e "Starting $func_name..."
    supabase functions serve $func_name --no-verify-jwt > /tmp/${func_name}.log 2>&1 &
    echo $! > /tmp/${func_name}.pid
    sleep 2
}

# Start all required functions
start_function "mock-google-api"
start_function "content-sync"
start_function "content-api"

echo -e "${GREEN}✓ Edge functions started${NC}"

# Perform initial sync
echo -e "\n${YELLOW}3. Performing initial content sync...${NC}"
SYNC_RESPONSE=$(curl -s -X POST http://localhost:54321/functions/v1/content-sync \
    -H "Authorization: Bearer $SERVICE_ROLE_KEY" \
    -H "Content-Type: application/json")

if echo "$SYNC_RESPONSE" | jq -e '.success == true' > /dev/null 2>&1; then
    CREATED=$(echo "$SYNC_RESPONSE" | jq -r '.articlesCreated')
    UPDATED=$(echo "$SYNC_RESPONSE" | jq -r '.articlesUpdated')
    echo -e "${GREEN}✓ Sync successful - Created: $CREATED, Updated: $UPDATED${NC}"
else
    echo -e "${RED}✗ Sync failed${NC}"
    echo "$SYNC_RESPONSE" | jq .
fi

# Test content API endpoints
echo -e "\n${YELLOW}4. Testing Content API endpoints...${NC}"

# Test GET /pulse endpoint
echo -e "\n  ${BLUE}Testing GET /pulse...${NC}"
PULSE_RESPONSE=$(curl -s "http://localhost:54321/functions/v1/content-api/pulse?page=1&per_page=5")
if echo "$PULSE_RESPONSE" | jq -e '.data | length > 0' > /dev/null 2>&1; then
    ARTICLE_COUNT=$(echo "$PULSE_RESPONSE" | jq '.data | length')
    TOTAL=$(echo "$PULSE_RESPONSE" | jq -r '.pagination.total')
    echo -e "${GREEN}  ✓ Retrieved $ARTICLE_COUNT articles (Total: $TOTAL)${NC}"
else
    echo -e "${RED}  ✗ Failed to retrieve articles${NC}"
    echo "$PULSE_RESPONSE" | jq .
fi

# Test search functionality
echo -e "\n  ${BLUE}Testing search functionality...${NC}"
SEARCH_RESPONSE=$(curl -s -X POST "http://localhost:54321/functions/v1/content-api/search" \
    -H "Content-Type: application/json" \
    -d '{"query": "test", "limit": 3}')
if echo "$SEARCH_RESPONSE" | jq -e '.data' > /dev/null 2>&1; then
    SEARCH_COUNT=$(echo "$SEARCH_RESPONSE" | jq '.data | length')
    echo -e "${GREEN}  ✓ Search returned $SEARCH_COUNT results${NC}"
else
    echo -e "${RED}  ✗ Search failed${NC}"
    echo "$SEARCH_RESPONSE" | jq .
fi

# Test pillar filtering
echo -e "\n  ${BLUE}Testing pillar filtering...${NC}"
FILTER_RESPONSE=$(curl -s "http://localhost:54321/functions/v1/content-api/pulse?pillar=TAKE%20YOUR%20SHOT")
if echo "$FILTER_RESPONSE" | jq -e '.data | length > 0' > /dev/null 2>&1; then
    FILTER_COUNT=$(echo "$FILTER_RESPONSE" | jq '.data | length')
    echo -e "${GREEN}  ✓ Filter returned $FILTER_COUNT articles${NC}"
else
    echo -e "${RED}  ✗ Filter failed${NC}"
fi

# Test React app integration
echo -e "\n${YELLOW}5. Testing React app integration...${NC}"

# Check if dev server is running
if curl -s http://localhost:5173 > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Dev server is running${NC}"
    
    echo -e "\n${BLUE}Testing new Pulse page...${NC}"
    echo -e "  Visit: ${GREEN}http://localhost:5173/v2/pulse${NC}"
    echo -e "\n${BLUE}Testing comparison page...${NC}"
    echo -e "  Visit: ${GREEN}http://localhost:5173/v2/pulse-comparison${NC}"
else
    echo -e "${YELLOW}! Dev server not running. Start it with: npm run dev${NC}"
fi

# Performance comparison
echo -e "\n${YELLOW}6. Performance Comparison${NC}"
echo -e "${RED}Old Implementation (Google API):${NC}"
echo "  - Load time: 2-3 seconds"
echo "  - No caching between users"
echo "  - No search capability"

echo -e "\n${GREEN}New Implementation (Supabase):${NC}"
# Measure API response time
START_TIME=$(date +%s%N)
curl -s "http://localhost:54321/functions/v1/content-api/pulse?page=1&per_page=20" > /dev/null
END_TIME=$(date +%s%N)
RESPONSE_TIME=$(( ($END_TIME - $START_TIME) / 1000000 ))

echo "  - API Response time: ${RESPONSE_TIME}ms"
echo "  - Full-text search enabled"
echo "  - Server-side caching"
echo "  - Real-time updates"

# Show sample data
echo -e "\n${YELLOW}7. Sample article data:${NC}"
echo "$PULSE_RESPONSE" | jq -r '.data[0] | {title, pillar, desk, source}'

# Cleanup function
cleanup() {
    echo -e "\n${YELLOW}8. Cleaning up...${NC}"
    for func in mock-google-api content-sync content-api; do
        if [ -f /tmp/${func}.pid ]; then
            kill $(cat /tmp/${func}.pid) 2>/dev/null
            rm /tmp/${func}.pid
        fi
    done
    echo -e "${GREEN}✓ Cleanup complete${NC}"
}

# Set trap for cleanup
trap cleanup EXIT

echo -e "\n${GREEN}=== Stage 2 Integration Test Complete ===${NC}"
echo -e "\n${YELLOW}Next steps:${NC}"
echo "1. Visit http://localhost:5173/v2/pulse to see the new implementation"
echo "2. Visit http://localhost:5173/v2/pulse-comparison to compare old vs new"
echo "3. Try searching for articles"
echo "4. Test filtering by pillar"
echo "5. Check real-time updates by running sync again"

echo -e "\n${BLUE}Press Ctrl+C to exit and cleanup...${NC}"

# Keep script running to maintain edge functions
while true; do
    sleep 1
done