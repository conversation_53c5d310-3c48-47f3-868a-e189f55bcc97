// Configuration for scripts - uses environment variables
// Run scripts with: VITE_APP_SUPABASE_URL=... VITE_APP_SUPABASE_SERVICE_KEY=... node script.js

const getConfig = () => {
  const supabaseUrl = process.env.VITE_APP_SUPABASE_URL;
  const serviceKey = process.env.VITE_APP_SUPABASE_SERVICE_KEY;
  const anonKey = process.env.VITE_APP_SUPABASE_ANON_KEY;

  if (!supabaseUrl) {
    throw new Error('VITE_APP_SUPABASE_URL environment variable is required');
  }

  return {
    supabaseUrl,
    serviceKey,
    anonKey,
    hasServiceKey: !!serviceKey,
    hasAnonKey: !!anonKey
  };
};

module.exports = { getConfig };