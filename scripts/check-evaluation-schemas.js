// ABOUTME: Script to check the complete schema of all evaluation-related tables
// Examines pre_evaluations, player_evaluations, and evaluations tables

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkEvaluationSchemas() {
  console.log('=== CHECKING EVALUATION TABLE SCHEMAS ===\n');

  try {
    // 1. Check if evaluations table exists
    console.log('1. CHECKING FOR EVALUATIONS TABLE:');
    console.log('----------------------------------');
    const { data: evaluationsData, error: evalError } = await supabase
      .from('evaluations')
      .select('*')
      .limit(1);
    
    if (evalError) {
      if (evalError.message.includes('does not exist')) {
        console.log('❌ evaluations table does NOT exist');
      } else {
        console.log('✅ evaluations table exists');
        console.log('Sample row:', evaluationsData?.[0] ? Object.keys(evaluationsData[0]) : 'No data');
      }
    } else {
      console.log('✅ evaluations table exists');
      console.log('Columns:', evaluationsData?.[0] ? Object.keys(evaluationsData[0]) : 'No data');
    }

    // 2. Get pre_evaluations schema in detail
    console.log('\n\n2. PRE_EVALUATIONS TABLE SCHEMA:');
    console.log('--------------------------------');
    const { data: preEval, error: preError } = await supabase
      .from('pre_evaluations')
      .select('*')
      .limit(1);
    
    if (!preError && preEval?.[0]) {
      const columns = Object.keys(preEval[0]);
      console.log(`Total columns: ${columns.length}`);
      console.log('\nColumns grouped by purpose:');
      
      // Group columns by category
      const idColumns = columns.filter(c => c.includes('id') || c === 'id');
      const statusColumns = columns.filter(c => c.includes('status') || c.includes('state'));
      const timeColumns = columns.filter(c => c.includes('_at') || c.includes('time'));
      const textColumns = columns.filter(c => c.includes('text') || c.includes('response'));
      const scoreColumns = columns.filter(c => c.includes('rating') || c.includes('score') || c.includes('points'));
      
      console.log('\nID Fields:', idColumns);
      console.log('Status Fields:', statusColumns);
      console.log('Time Fields:', timeColumns);
      console.log('Text Fields:', textColumns);
      console.log('Score Fields:', scoreColumns);
      
      console.log('\nAll columns:', columns.join(', '));
    }

    // 3. Get player_evaluations schema
    console.log('\n\n3. PLAYER_EVALUATIONS TABLE SCHEMA:');
    console.log('-----------------------------------');
    const { data: playerEval, error: playerError } = await supabase
      .from('player_evaluations')
      .select('*')
      .limit(1);
    
    if (!playerError && playerEval?.[0]) {
      const columns = Object.keys(playerEval[0]);
      console.log(`Total columns: ${columns.length}`);
      console.log('Columns:', columns.join(', '));
      
      // Check relationship fields
      console.log('\nKey relationship fields:');
      if (columns.includes('pre_evaluation_id')) {
        console.log('  - pre_evaluation_id: Links to pre_evaluations table');
      }
      if (columns.includes('evaluation_id')) {
        console.log('  - evaluation_id: Links to evaluations table (if exists)');
      }
      if (columns.includes('player_id')) {
        console.log('  - player_id: Links to profiles table');
      }
    }

    // 4. Check relationships between tables
    console.log('\n\n4. TABLE RELATIONSHIPS:');
    console.log('----------------------');
    
    // Count pre_evaluations
    const { count: preCount } = await supabase
      .from('pre_evaluations')
      .select('*', { count: 'exact', head: true });
    
    // Count player_evaluations
    const { count: playerCount } = await supabase
      .from('player_evaluations')
      .select('*', { count: 'exact', head: true });
    
    // Count player_evaluations with pre_evaluation_id
    const { count: linkedCount } = await supabase
      .from('player_evaluations')
      .select('*', { count: 'exact', head: true })
      .not('pre_evaluation_id', 'is', null);
    
    console.log(`pre_evaluations table: ${preCount} records`);
    console.log(`player_evaluations table: ${playerCount} records`);
    console.log(`player_evaluations linked to pre_evaluations: ${linkedCount} records`);
    
    // 5. Check for evaluation_criteria usage
    console.log('\n\n5. EVALUATION_CRITERIA USAGE:');
    console.log('-----------------------------');
    const { data: criteria, error: critError } = await supabase
      .from('evaluation_criteria')
      .select('category, position, criteria_name')
      .limit(10);
    
    if (!critError && criteria) {
      const categories = [...new Set(criteria.map(c => c.category))];
      const positions = [...new Set(criteria.map(c => c.position))];
      console.log('Categories:', categories.join(', '));
      console.log('Positions:', positions.join(', '));
    }

    // 6. Check data flow
    console.log('\n\n6. DATA FLOW ANALYSIS:');
    console.log('---------------------');
    
    // Get a recent pre_evaluation with player_evaluations
    const { data: recentPre } = await supabase
      .from('pre_evaluations')
      .select(`
        id,
        player_id,
        player_position,
        status,
        created_at,
        player_evaluations (
          id,
          category,
          position,
          criteria_name,
          score
        )
      `)
      .not('player_evaluations', 'is', null)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();
    
    if (recentPre) {
      console.log('Example pre_evaluation with linked player_evaluations:');
      console.log(`  Pre-eval ID: ${recentPre.id}`);
      console.log(`  Player position: ${recentPre.player_position || 'NULL'}`);
      console.log(`  Status: ${recentPre.status}`);
      console.log(`  Linked player_evaluations: ${recentPre.player_evaluations?.length || 0}`);
      
      if (recentPre.player_evaluations?.length > 0) {
        const evalsByCategory = {};
        recentPre.player_evaluations.forEach(pe => {
          evalsByCategory[pe.category] = evalsByCategory[pe.category] || [];
          evalsByCategory[pe.category].push({
            position: pe.position,
            criteria: pe.criteria_name
          });
        });
        
        console.log('\n  Evaluations by category:');
        Object.entries(evalsByCategory).forEach(([cat, evals]) => {
          console.log(`    ${cat}: ${evals.length} criteria`);
          console.log(`      Positions: ${[...new Set(evals.map(e => e.position))].join(', ')}`);
        });
      }
    }

  } catch (error) {
    console.error('Error:', error.message);
  }

  process.exit(0);
}

checkEvaluationSchemas();