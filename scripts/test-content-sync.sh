#!/bin/bash

# Test script for Stage 1: Content Sync Foundation

echo "🚀 Testing Content Sync Foundation"
echo "=================================="

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if Supabase is running
echo -e "\n${YELLOW}1. Checking Supabase status...${NC}"
if supabase status > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Supabase is running${NC}"
else
    echo -e "${RED}✗ Supabase is not running. Starting it now...${NC}"
    supabase start
    sleep 5
fi

# Apply migrations
echo -e "\n${YELLOW}2. Applying database migrations...${NC}"
supabase db reset --linked
echo -e "${GREEN}✓ Migrations applied${NC}"

# Get service role key
SERVICE_ROLE_KEY=$(supabase status | grep -o 'service_role key: [^ ]*' | cut -d' ' -f3)
API_URL=$(supabase status | grep -o 'API URL: [^ ]*' | cut -d' ' -f3)

echo -e "\n${YELLOW}3. Starting edge functions...${NC}"
# Start mock Google API in background
supabase functions serve mock-google-api --no-verify-jwt &
MOCK_PID=$!
sleep 3

# Start content-sync function in background
supabase functions serve content-sync --no-verify-jwt &
SYNC_PID=$!
sleep 3

echo -e "${GREEN}✓ Edge functions started${NC}"

# Test mock Google API
echo -e "\n${YELLOW}4. Testing mock Google API...${NC}"
MOCK_RESPONSE=$(curl -s http://localhost:54321/functions/v1/mock-google-api)
if echo "$MOCK_RESPONSE" | jq . > /dev/null 2>&1; then
    ARTICLE_COUNT=$(echo "$MOCK_RESPONSE" | jq '. | length')
    echo -e "${GREEN}✓ Mock API returned $ARTICLE_COUNT articles${NC}"
else
    echo -e "${RED}✗ Mock API failed${NC}"
    echo "$MOCK_RESPONSE"
fi

# Test content sync
echo -e "\n${YELLOW}5. Testing content sync function...${NC}"
SYNC_RESPONSE=$(curl -s -X POST http://localhost:54321/functions/v1/content-sync \
    -H "Authorization: Bearer $SERVICE_ROLE_KEY" \
    -H "Content-Type: application/json")

if echo "$SYNC_RESPONSE" | jq . > /dev/null 2>&1; then
    SUCCESS=$(echo "$SYNC_RESPONSE" | jq -r '.success')
    CREATED=$(echo "$SYNC_RESPONSE" | jq -r '.articlesCreated')
    UPDATED=$(echo "$SYNC_RESPONSE" | jq -r '.articlesUpdated')
    
    if [ "$SUCCESS" = "true" ]; then
        echo -e "${GREEN}✓ Sync completed successfully${NC}"
        echo "  - Articles created: $CREATED"
        echo "  - Articles updated: $UPDATED"
    else
        echo -e "${RED}✗ Sync failed${NC}"
        echo "$SYNC_RESPONSE" | jq .
    fi
else
    echo -e "${RED}✗ Sync request failed${NC}"
    echo "$SYNC_RESPONSE"
fi

# Check database
echo -e "\n${YELLOW}6. Checking database content...${NC}"
PSQL_CMD="SELECT COUNT(*) as total, 
    COUNT(DISTINCT pillar) as pillars,
    MAX(created_at) as latest_sync
FROM content.pulse_articles;"

DB_RESULT=$(psql postgresql://postgres:postgres@localhost:54322/postgres -t -c "$PSQL_CMD")
echo -e "${GREEN}✓ Database query successful${NC}"
echo "$DB_RESULT"

# Show sample articles
echo -e "\n${YELLOW}7. Sample articles in database:${NC}"
SAMPLE_CMD="SELECT external_id, title, pillar, desk 
FROM content.pulse_articles 
ORDER BY pub_date DESC 
LIMIT 3;"

psql postgresql://postgres:postgres@localhost:54322/postgres -c "$SAMPLE_CMD"

# Check sync log
echo -e "\n${YELLOW}8. Latest sync log entry:${NC}"
LOG_CMD="SELECT sync_status, sync_started_at, sync_completed_at, 
    articles_fetched, articles_created, articles_updated 
FROM content.pulse_sync_log 
ORDER BY sync_started_at DESC 
LIMIT 1;"

psql postgresql://postgres:postgres@localhost:54322/postgres -c "$LOG_CMD"

# Cleanup
echo -e "\n${YELLOW}9. Cleaning up...${NC}"
kill $MOCK_PID 2>/dev/null
kill $SYNC_PID 2>/dev/null
echo -e "${GREEN}✓ Test complete!${NC}"

echo -e "\n${GREEN}=== Stage 1 Foundation Test Summary ===${NC}"
echo "✓ Content schema created"
echo "✓ Mock Google API functional"
echo "✓ Content sync function working"
echo "✓ Data successfully stored in Supabase"

echo -e "\n${YELLOW}Next steps:${NC}"
echo "1. Review the synced data in Supabase Studio"
echo "2. Test with real Google API URL (set GOOGLE_API_URL env var)"
echo "3. Proceed to Stage 2: API & Client Integration"