#!/usr/bin/env node

/**
 * Debug widget-api Edge Function response
 */

const WIDGET_API_URL = 'https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/widget-api/pulse-feed?count=5&theme=dark';

async function debugWidgetAPI() {
  console.log('🔍 Debugging widget-api response...\n');
  console.log('URL:', WIDGET_API_URL);
  console.log('\n');
  
  try {
    const response = await fetch(WIDGET_API_URL, {
      headers: {
        'Origin': 'http://localhost:3000'
      }
    });
    
    console.log('Response Status:', response.status);
    console.log('Response Status Text:', response.statusText);
    console.log('\nResponse Headers:');
    response.headers.forEach((value, key) => {
      console.log(`  ${key}: ${value}`);
    });
    
    const text = await response.text();
    console.log('\nResponse Body:');
    console.log(text);
    
    // Try to parse as JSON if possible
    try {
      const json = JSON.parse(text);
      console.log('\nParsed JSON:');
      console.log(JSON.stringify(json, null, 2));
    } catch (e) {
      // Not JSON, that's ok
    }
    
  } catch (error) {
    console.error('❌ Fetch Error:', error.message);
    console.error(error.stack);
  }
}

// Also test the root endpoint
async function testRootEndpoint() {
  console.log('\n\n🔍 Testing root widget-api endpoint...\n');
  
  try {
    const response = await fetch('https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/widget-api', {
      headers: {
        'Origin': 'http://localhost:3000'
      }
    });
    
    console.log('Root Status:', response.status);
    const text = await response.text();
    
    try {
      const json = JSON.parse(text);
      console.log('Root Response:', JSON.stringify(json, null, 2));
    } catch {
      console.log('Root Response:', text.substring(0, 200));
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

async function testWithQueryParam() {
  console.log('\n\n🔍 Testing with query param endpoint...\n');
  
  try {
    const response = await fetch('https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/widget-api?endpoint=pulse-feed&count=5&theme=dark', {
      headers: {
        'Origin': 'http://localhost:3000'
      }
    });
    
    console.log('Query Param Status:', response.status);
    const text = await response.text();
    
    if (response.status === 200) {
      console.log('✅ Query param approach works!');
      console.log('Response length:', text.length, 'bytes');
    } else {
      console.log('Response:', text.substring(0, 200));
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Run all tests
async function runTests() {
  await debugWidgetAPI();
  await testRootEndpoint();
  await testWithQueryParam();
}

runTests().catch(console.error);