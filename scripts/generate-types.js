#!/usr/bin/env node

/**
 * Database Type Generation Script
 *
 * This script generates TypeScript types from the Supabase database schema.
 * It can work with both local and remote Supabase instances.
 *
 * Usage:
 *   npm run types:generate          # Generate from remote project
 *   npm run types:generate:local    # Generate from local instance
 */

import { execSync } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const CONFIG = {
  // Remote project configuration
  REMOTE_PROJECT_ID: 'ovfwiyqhubxeqvbrggbe',
  
  // Output paths
  OUTPUT_PATH: path.join(__dirname, '../src/types/database.ts'),
  BACKUP_PATH: path.join(__dirname, '../src/types/database.backup.ts'),
  
  // Local development detection
  LOCAL_URL: 'http://127.0.0.1:54321',
};

/**
 * Check if we're using local development setup
 */
function isLocalDevelopment() {
  const supabaseUrl = process.env.VITE_APP_SUPABASE_URL;
  return supabaseUrl && supabaseUrl.includes('127.0.0.1');
}

/**
 * Backup existing types file if it exists
 */
function backupExistingTypes() {
  if (fs.existsSync(CONFIG.OUTPUT_PATH)) {
    console.log('📦 Backing up existing types...');
    fs.copyFileSync(CONFIG.OUTPUT_PATH, CONFIG.BACKUP_PATH);
    console.log(`✅ Backup created: ${CONFIG.BACKUP_PATH}`);
  }
}

/**
 * Generate types from local Supabase instance
 */
function generateLocalTypes() {
  console.log('🔄 Generating types from local Supabase instance...');
  
  try {
    execSync(
      `npx supabase gen types typescript --local > "${CONFIG.OUTPUT_PATH}"`,
      { stdio: 'inherit', cwd: path.join(__dirname, '..') }
    );
    
    console.log('✅ Local database types generated successfully!');
    return true;
  } catch (error) {
    console.error('❌ Error generating local types:', error.message);
    console.log('\n💡 Make sure Supabase is running locally:');
    console.log('   npx supabase start');
    return false;
  }
}

/**
 * Generate types from remote Supabase project
 */
function generateRemoteTypes() {
  console.log('🔄 Generating types from remote Supabase project...');
  console.log(`📡 Project ID: ${CONFIG.REMOTE_PROJECT_ID}`);
  
  try {
    execSync(
      `npx supabase gen types typescript --project-id ${CONFIG.REMOTE_PROJECT_ID} > "${CONFIG.OUTPUT_PATH}"`,
      { stdio: 'inherit', cwd: path.join(__dirname, '..') }
    );
    
    console.log('✅ Remote database types generated successfully!');
    return true;
  } catch (error) {
    console.error('❌ Error generating remote types:', error.message);
    console.log('\n💡 Make sure you have access to the Supabase project:');
    console.log('   npx supabase login');
    console.log('   npx supabase projects list');
    return false;
  }
}

/**
 * Validate generated types file
 */
function validateGeneratedTypes() {
  if (!fs.existsSync(CONFIG.OUTPUT_PATH)) {
    console.error('❌ Types file was not generated');
    return false;
  }
  
  const content = fs.readFileSync(CONFIG.OUTPUT_PATH, 'utf8');
  
  // Check if file has meaningful content
  if (content.length < 100) {
    console.error('❌ Generated types file appears to be empty or invalid');
    return false;
  }
  
  // Check for expected TypeScript interface structure
  if (!content.includes('export interface Database') && !content.includes('export type Database')) {
    console.error('❌ Generated types file does not contain expected Database interface');
    return false;
  }
  
  console.log('✅ Generated types file validated successfully');
  console.log(`📁 Output: ${CONFIG.OUTPUT_PATH}`);
  console.log(`📊 File size: ${Math.round(content.length / 1024)}KB`);
  
  return true;
}

/**
 * Add helpful header comment to generated types
 */
function addHeaderComment() {
  const content = fs.readFileSync(CONFIG.OUTPUT_PATH, 'utf8');
  const timestamp = new Date().toISOString();
  const source = isLocalDevelopment() ? 'local development' : `remote project ${CONFIG.REMOTE_PROJECT_ID}`;
  
  const header = `/**
 * Database Types
 * 
 * Auto-generated TypeScript types for Supabase database schema.
 * 
 * Generated: ${timestamp}
 * Source: ${source}
 * 
 * DO NOT EDIT MANUALLY - This file is auto-generated.
 * To regenerate: npm run types:generate
 */

`;

  fs.writeFileSync(CONFIG.OUTPUT_PATH, header + content);
  console.log('✅ Added header comment to generated types');
}

/**
 * Main execution function
 */
function main() {
  console.log('🚀 Starting database type generation...\n');
  
  // Ensure output directory exists
  const outputDir = path.dirname(CONFIG.OUTPUT_PATH);
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
    console.log(`📁 Created output directory: ${outputDir}`);
  }
  
  // Backup existing types
  backupExistingTypes();
  
  // Determine generation method
  const useLocal = isLocalDevelopment();
  console.log(`🎯 Target: ${useLocal ? 'Local development' : 'Remote project'}`);
  
  // Generate types
  let success = false;
  if (useLocal) {
    success = generateLocalTypes();
  } else {
    success = generateRemoteTypes();
  }
  
  if (!success) {
    console.log('\n🔄 Attempting fallback method...');
    if (useLocal) {
      success = generateRemoteTypes();
    } else {
      success = generateLocalTypes();
    }
  }
  
  if (!success) {
    console.error('\n❌ Failed to generate types with both methods');
    process.exit(1);
  }
  
  // Validate and enhance generated types
  if (validateGeneratedTypes()) {
    addHeaderComment();
    console.log('\n🎉 Database type generation completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Update Supabase client: npm run types:update-client');
    console.log('   2. Run type check: npm run type-check');
    console.log('   3. Update service layer with new types');
  } else {
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Database Type Generation Script

Usage:
  node scripts/generate-types.js [options]

Options:
  --help, -h     Show this help message
  --local        Force local generation
  --remote       Force remote generation

Environment Variables:
  VITE_APP_SUPABASE_URL    Supabase URL (auto-detects local vs remote)

Examples:
  npm run types:generate           # Auto-detect local vs remote
  npm run types:generate:local     # Force local generation
  npm run types:generate:remote    # Force remote generation
`);
  process.exit(0);
}

// Override detection if specified
if (args.includes('--local')) {
  process.env.VITE_APP_SUPABASE_URL = CONFIG.LOCAL_URL;
} else if (args.includes('--remote')) {
  delete process.env.VITE_APP_SUPABASE_URL;
}

// Run the script
main();
