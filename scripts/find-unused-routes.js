#!/usr/bin/env node

// ABOUTME: Script to find all page components that are NOT referenced in active routes
// This helps identify potential dead code by comparing pages directory with route definitions

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const PROJECT_ROOT = path.join(__dirname, '..');
const PAGES_DIR = path.join(PROJECT_ROOT, 'src/pages');
const ROUTES_FILE = path.join(PROJECT_ROOT, 'src/AppRoutes.tsx');

// Arrays to store results
const allPageFiles = [];
const referencedPages = new Set();
const commentedPages = new Set();

// Helper to recursively find all .tsx files in pages directory
function findPageFiles(dir, baseDir = '') {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const fullPath = path.join(dir, file);
    const relativePath = path.join(baseDir, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      findPageFiles(fullPath, relativePath);
    } else if (file.endsWith('.tsx') && !file.includes('.test.') && !file.includes('.spec.')) {
      allPageFiles.push({
        name: file,
        path: relativePath,
        fullPath: fullPath
      });
    }
  });
}

// Parse routes file to find referenced components
function parseRoutesFile() {
  const routesContent = fs.readFileSync(ROUTES_FILE, 'utf8');
  const lines = routesContent.split('\n');
  
  lines.forEach((line, index) => {
    // Skip if line is commented
    const isCommented = line.trim().startsWith('//') || 
                       (line.includes('/*') && line.includes('*/'));
    
    // Look for component references in Route definitions
    if (line.includes('component=') || line.includes('import')) {
      // Extract component name from imports
      const importMatch = line.match(/import\s+(\w+)\s+from\s+['"](.+)['"]/);
      if (importMatch) {
        const componentName = importMatch[1];
        const importPath = importMatch[2];
        
        if (isCommented) {
          commentedPages.add(componentName);
        } else {
          referencedPages.add(componentName);
        }
      }
      
      // Extract component from Route component prop
      const routeMatch = line.match(/component={?(\w+)}?/);
      if (routeMatch && !isCommented) {
        referencedPages.add(routeMatch[1]);
      }
      
      // Extract lazy loaded components
      const lazyMatch = line.match(/lazy\(\(\)\s*=>\s*import\(['"](.+)['"]\)/);
      if (lazyMatch && !isCommented) {
        const importPath = lazyMatch[1];
        const componentName = path.basename(importPath);
        referencedPages.add(componentName);
      }
    }
  });
}

// Main analysis
console.log('🔍 Analyzing route usage...\n');

// Find all page files
findPageFiles(PAGES_DIR);
console.log(`📁 Found ${allPageFiles.length} total page files\n`);

// Parse routes to find references
parseRoutesFile();
console.log(`✅ Found ${referencedPages.size} pages referenced in active routes`);
console.log(`💬 Found ${commentedPages.size} pages only in commented routes\n`);

// Categorize files
const unusedPages = [];
const commentedOnlyPages = [];
const activePage = [];

allPageFiles.forEach(file => {
  const baseName = file.name.replace('.tsx', '');
  
  if (referencedPages.has(baseName)) {
    activePage.push(file);
  } else if (commentedPages.has(baseName)) {
    commentedOnlyPages.push(file);
  } else {
    unusedPages.push(file);
  }
});

// Report results
console.log('📊 ANALYSIS RESULTS:');
console.log('===================\n');

console.log(`✅ ACTIVE PAGES (${activePage.length}):`);
console.log('These pages are referenced in active routes\n');
activePage.sort((a, b) => a.path.localeCompare(b.path));
activePage.forEach(file => {
  console.log(`  ✓ ${file.path}`);
});

console.log(`\n💬 COMMENTED ONLY (${commentedOnlyPages.length}):`);
console.log('These pages are only referenced in commented routes\n');
commentedOnlyPages.sort((a, b) => a.path.localeCompare(b.path));
commentedOnlyPages.forEach(file => {
  console.log(`  // ${file.path}`);
});

console.log(`\n❌ POTENTIALLY UNUSED (${unusedPages.length}):`);
console.log('These pages have NO route references (active or commented)\n');
unusedPages.sort((a, b) => a.path.localeCompare(b.path));
unusedPages.forEach(file => {
  console.log(`  ✗ ${file.path}`);
});

// Summary statistics
console.log('\n📈 SUMMARY:');
console.log('===========');
console.log(`Total pages: ${allPageFiles.length}`);
console.log(`Active: ${activePage.length} (${Math.round(activePage.length / allPageFiles.length * 100)}%)`);
console.log(`Commented only: ${commentedOnlyPages.length} (${Math.round(commentedOnlyPages.length / allPageFiles.length * 100)}%)`);
console.log(`Potentially unused: ${unusedPages.length} (${Math.round(unusedPages.length / allPageFiles.length * 100)}%)`);

// Export results to JSON for further processing
const results = {
  timestamp: new Date().toISOString(),
  summary: {
    total: allPageFiles.length,
    active: activePage.length,
    commentedOnly: commentedOnlyPages.length,
    unused: unusedPages.length
  },
  files: {
    active: activePage.map(f => f.path),
    commentedOnly: commentedOnlyPages.map(f => f.path),
    unused: unusedPages.map(f => f.path)
  }
};

fs.writeFileSync(
  path.join(PROJECT_ROOT, 'route-analysis-results.json'),
  JSON.stringify(results, null, 2)
);

console.log('\n💾 Results saved to: route-analysis-results.json');

// Warnings
if (unusedPages.length > 0) {
  console.log('\n⚠️  WARNING: Found potentially unused pages!');
  console.log('   These files may be safe to delete, but verify they are not:');
  console.log('   - Imported by other components');
  console.log('   - Used in dynamic routing');
  console.log('   - Referenced in test files');
}