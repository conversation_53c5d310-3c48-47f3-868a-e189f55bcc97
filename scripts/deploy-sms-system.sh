#!/bin/bash

# Change to the project directory
cd /Users/<USER>/newSHOT/shot

# Deploy the Edge Function
echo "Deploying send-sms Edge Function..."
npx supabase functions deploy send-sms --project-ref ovfwiyqhubxeqvbrggbe

# Apply the database migration
echo "Applying database migration..."
npx supabase db push --project-ref ovfwiyqhubxeqvbrggbe

# Run a test of the SMS system
echo "Testing SMS system..."
npx supabase db execute --project-ref ovfwiyqhubxeqvbrggbe \
  "SELECT * FROM test_sms_system('+447507940322');"

# Check the job status
echo "Checking job status..."
npx supabase db execute --project-ref ovfwiyqhubxeqvbrggbe \
  "SELECT * FROM check_sms_job_status();"

# Get queue stats
echo "Getting queue stats..."
npx supabase db execute --project-ref ovfwiyqhubxeqvbrggbe \
  "SELECT * FROM get_sms_queue_stats();"

echo "SMS notification system deployment and testing complete!"
