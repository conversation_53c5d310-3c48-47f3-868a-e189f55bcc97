// <PERSON>ript to verify debug mode implementation for ShadowEventCard
// Run this after the page loads to test debug functionality

(function() {
  console.log('=== Debug Mode Implementation Verification ===');
  
  // Step 1: Enable debug mode
  console.log('\n1. Enabling debug mode...');
  const debugFlags = {
    debug_mode: true,
    show_component_paths: true
  };
  sessionStorage.setItem('superadmin_session_flags', JSON.stringify(debugFlags));
  console.log('✅ Debug mode enabled in sessionStorage');
  
  // Step 2: Check for shadow hosts
  console.log('\n2. Looking for ShadowEventCard elements...');
  const shadowHosts = document.querySelectorAll('[data-shadow-rendered], div[style*="display: block"]');
  let foundCards = 0;
  
  shadowHosts.forEach((host, index) => {
    if (host.shadowRoot) {
      const eventCard = host.shadowRoot.querySelector('.event-card');
      if (eventCard) {
        foundCards++;
        console.log(`\n📋 Event Card ${foundCards}:`);
        
        // Check for debug button
        const debugButton = host.shadowRoot.querySelector('.debug-button, [data-action="debug"]');
        if (debugButton) {
          console.log('✅ Debug button found!');
          console.log('   Button text:', debugButton.textContent);
          console.log('   Button class:', debugButton.className);
          
          // Test clicking the button
          console.log('   Testing click...');
          debugButton.click();
          console.log('   Click event triggered - check for modal');
        } else {
          console.log('❌ Debug button NOT found');
          
          // Check if isDebugMode would return true
          try {
            const flags = sessionStorage.getItem('superadmin_session_flags');
            const parsed = JSON.parse(flags);
            console.log('   Debug mode in storage:', parsed.debug_mode);
          } catch (e) {
            console.log('   Error checking debug mode:', e);
          }
        }
        
        // Show event card details
        const title = eventCard.querySelector('.event-title');
        if (title) {
          console.log('   Event title:', title.textContent);
        }
      }
    }
  });
  
  if (foundCards === 0) {
    console.log('❌ No ShadowEventCard elements found');
    console.log('   Make sure you are on a page that displays events');
  } else {
    console.log(`\n✅ Found ${foundCards} event card(s)`);
  }
  
  // Step 3: Instructions
  console.log('\n3. Next steps:');
  console.log('- If debug buttons are not showing, refresh the page');
  console.log('- The debug button (🐛) should appear in the bottom-right of each event card');
  console.log('- Click the debug button to see detailed event data');
  console.log('- If still not working, check browser console for errors during render');
  
  // Step 4: Manual debug button injection test
  window.injectDebugButton = function(cardIndex = 0) {
    const hosts = Array.from(document.querySelectorAll('*')).filter(el => el.shadowRoot);
    let count = 0;
    
    for (const host of hosts) {
      const card = host.shadowRoot.querySelector('.event-card');
      if (card) {
        if (count === cardIndex) {
          // Check if button already exists
          if (host.shadowRoot.querySelector('.debug-button')) {
            console.log('Debug button already exists!');
            return;
          }
          
          const btn = document.createElement('button');
          btn.className = 'debug-button';
          btn.setAttribute('data-action', 'debug');
          btn.textContent = '🐛';
          btn.style.cssText = `
            position: absolute;
            bottom: 8px;
            right: 8px;
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.3);
            border-radius: 6px;
            padding: 4px 8px;
            cursor: pointer;
            font-size: 14px;
            color: #DC2626;
            z-index: 10;
          `;
          btn.onclick = () => alert('Debug button clicked! (Manual injection)');
          card.appendChild(btn);
          console.log('✅ Debug button manually injected to card', cardIndex);
          return;
        }
        count++;
      }
    }
    console.log('❌ Could not find event card at index', cardIndex);
  };
  
  console.log('\n- Run window.injectDebugButton() to manually test button visibility');
  
})();