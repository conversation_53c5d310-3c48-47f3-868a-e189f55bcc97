#!/usr/bin/env node

// ABOUTME: Script to move deprecated files for a specific area
// Follows the implementation plan's deprecation strategy

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PROJECT_ROOT = path.join(__dirname, '..');
const DEPRECATED_ROOT = path.join(PROJECT_ROOT, 'src/_deprecated');

// Function to use git mv to preserve history
function gitMove(source, target) {
  try {
    // Ensure target directory exists
    const targetDir = path.dirname(target);
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
    }
    
    // Use git mv to preserve history
    execSync(`git mv "${source}" "${target}"`, { cwd: PROJECT_ROOT });
    return { success: true };
  } catch (error) {
    // If git mv fails, try regular move
    if (fs.existsSync(source)) {
      fs.renameSync(source, target);
      return { success: true, warning: 'Used fs.rename instead of git mv' };
    }
    return { success: false, error: error.message };
  }
}

// Main function to deprecate files for an area
function deprecateArea(area) {
  console.log(`\n🚀 Deprecating Files for Area: ${area}`);
  console.log('=====================================\n');

  const areaDir = path.join(DEPRECATED_ROOT, area);
  const fileListPath = path.join(areaDir, 'file-list.json');

  if (!fs.existsSync(fileListPath)) {
    console.error(`❌ No file list found for area: ${area}`);
    console.log('   Run organize-deprecated-by-area.js first');
    process.exit(1);
  }

  const { files } = JSON.parse(fs.readFileSync(fileListPath, 'utf8'));
  
  console.log(`📋 Found ${files.length} files to deprecate\n`);

  const results = {
    moved: [],
    failed: [],
    warnings: []
  };

  // Process each file
  files.forEach((file, index) => {
    const sourcePath = path.join(PROJECT_ROOT, 'src/pages', file.path);
    const targetPath = path.join(areaDir, 'pages', file.path);

    process.stdout.write(`[${index + 1}/${files.length}] ${file.path}... `);

    const result = gitMove(sourcePath, targetPath);
    
    if (result.success) {
      console.log('✅');
      results.moved.push(file.path);
      if (result.warning) {
        results.warnings.push({ file: file.path, warning: result.warning });
      }
    } else {
      console.log('❌');
      results.failed.push({ file: file.path, error: result.error });
    }
  });

  // Update file list with results
  const updatedFileList = {
    area,
    deprecatedDate: new Date().toISOString(),
    stats: {
      total: files.length,
      moved: results.moved.length,
      failed: results.failed.length
    },
    files: files.map(f => ({
      ...f,
      status: results.moved.includes(f.path) ? 'deprecated' : 'failed',
      deprecatedDate: results.moved.includes(f.path) ? new Date().toISOString() : null
    }))
  };

  fs.writeFileSync(fileListPath, JSON.stringify(updatedFileList, null, 2));

  // Create deprecation log
  const logContent = `# Deprecation Log - ${area}

Date: ${new Date().toISOString()}

## Summary
- Total files: ${files.length}
- Successfully moved: ${results.moved.length}
- Failed: ${results.failed.length}
- Warnings: ${results.warnings.length}

## Successfully Moved Files
${results.moved.map(f => `- ✅ ${f}`).join('\n')}

## Failed Files
${results.failed.map(f => `- ❌ ${f.file}: ${f.error}`).join('\n')}

## Warnings
${results.warnings.map(f => `- ⚠️  ${f.file}: ${f.warning}`).join('\n')}

## Next Steps
1. Review failed files manually
2. Update imports in remaining code
3. Test application functionality
4. Monitor for any broken features
`;

  fs.writeFileSync(path.join(areaDir, 'deprecation-log.md'), logContent);

  // Summary
  console.log('\n📊 Deprecation Summary:');
  console.log(`✅ Moved: ${results.moved.length} files`);
  console.log(`❌ Failed: ${results.failed.length} files`);
  console.log(`⚠️  Warnings: ${results.warnings.length}`);
  console.log(`\n📁 Files moved to: ${areaDir}`);

  if (results.failed.length > 0) {
    console.log('\n⚠️  Some files failed to move. Check deprecation-log.md for details');
  }

  // Add deprecation warnings to moved files
  console.log('\n📝 Adding deprecation warnings to moved files...');
  addDeprecationWarnings(areaDir, results.moved);
}

// Add deprecation warnings to the top of moved files
function addDeprecationWarnings(areaDir, movedFiles) {
  movedFiles.forEach(filePath => {
    const fullPath = path.join(areaDir, 'pages', filePath);
    if (fs.existsSync(fullPath) && fullPath.endsWith('.tsx')) {
      try {
        const content = fs.readFileSync(fullPath, 'utf8');
        const warning = `/**
 * @deprecated This file has been deprecated as part of the 7-area architecture migration.
 * Date: ${new Date().toISOString()}
 * Reason: No active route references found
 * Migration: See /docs/implementation-plans for migration strategy
 * 
 * DO NOT USE THIS FILE FOR NEW FEATURES
 * This file will be deleted after 2 months of production validation
 */

`;
        if (!content.includes('@deprecated')) {
          fs.writeFileSync(fullPath, warning + content);
        }
      } catch (error) {
        console.log(`   ⚠️  Could not add warning to ${filePath}`);
      }
    }
  });
}

// Command line interface
const area = process.argv[2];
const validAreas = ['identity', 'perform', 'schedule', 'assess', 'clubhouse', 'control', 'pulse', 'locker', 'foundation', 'unknown'];

if (!area || !validAreas.includes(area)) {
  console.log('Usage: node deprecate-area.js <area>');
  console.log('\nValid areas:');
  validAreas.forEach(a => console.log(`  - ${a}`));
  console.log('\nRun organize-deprecated-by-area.js first to see what will be moved');
  process.exit(1);
}

deprecateArea(area);