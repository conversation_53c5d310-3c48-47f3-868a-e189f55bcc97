#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to update Shadow DOM component backgrounds to use CSS variables
 * This ensures consistent theming across all components
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Color mappings
const colorMappings = {
  // Black variations to gray-800
  '#000000': 'var(--shot-bg-primary)',
  'rgba(0, 0, 0,': 'rgba(31, 41, 55,', // gray-800 RGB
  '#0F0F0F': 'var(--shot-bg-primary)',
  '#1a1a1a': 'var(--shot-bg-secondary)',
  'rgba(26, 26, 26,': 'rgba(55, 65, 81,', // gray-700 RGB
  
  // Dark grays to proper grays
  '#2a2a2a': 'var(--shot-bg-secondary)',
  'rgba(42, 42, 42,': 'rgba(55, 65, 81,', // gray-700 RGB
  '#3a3a3a': 'var(--shot-bg-tertiary)',
  'rgba(52, 52, 52,': 'rgba(75, 85, 99,', // gray-600 RGB
  
  // Add CSS variable definitions in styles
  'background: #1F2937': 'background: var(--shot-bg-primary)',
  'background: rgb(31, 41, 55)': 'background: var(--shot-bg-primary)',
  'background-color: #1F2937': 'background-color: var(--shot-bg-primary)',
  'background-color: rgb(31, 41, 55)': 'background-color: var(--shot-bg-primary)',
};

// Find all Shadow component files
const componentFiles = glob.sync('src/components/shadow/**/*Shadow*.tsx', {
  cwd: path.resolve(__dirname, '..'),
  absolute: true
});

// Also include foundation shadow components
const foundationFiles = glob.sync('src/foundation/design-system/components/**/*Shadow*.tsx', {
  cwd: path.resolve(__dirname, '..'),
  absolute: true
});

const allFiles = [...componentFiles, ...foundationFiles];

console.log(`Found ${allFiles.length} Shadow component files to update`);

let updatedCount = 0;

allFiles.forEach(filePath => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Check if file contains shadow DOM styles
    if (!content.includes('styleElement.textContent') && !content.includes('styles =')) {
      return;
    }
    
    // Apply color mappings
    Object.entries(colorMappings).forEach(([oldColor, newColor]) => {
      if (content.includes(oldColor)) {
        content = content.replace(new RegExp(escapeRegExp(oldColor), 'g'), newColor);
        modified = true;
      }
    });
    
    // Add CSS variable import if not present and file was modified
    if (modified && !content.includes('/* CSS Variables */')) {
      // Find where styles are defined
      const styleIndex = content.search(/const styles = `|styleElement\.textContent = `/);
      if (styleIndex !== -1) {
        const insertIndex = content.indexOf('`', styleIndex) + 1;
        const cssVarImport = `
      /* CSS Variables */
      :host {
        --shot-bg-primary: #1F2937;
        --shot-bg-secondary: #374151;
        --shot-bg-tertiary: #4B5563;
        --shot-text-primary: #FFFFFF;
        --shot-text-secondary: #D1D5DB;
        --shot-border-primary: #4B5563;
      }
      `;
        content = content.slice(0, insertIndex) + cssVarImport + content.slice(insertIndex);
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content);
      updatedCount++;
      console.log(`✅ Updated: ${path.basename(filePath)}`);
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log(`\n✨ Updated ${updatedCount} files`);

function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}