#!/usr/bin/env node

/**
 * <PERSON>ript to clean up CSS imports and consolidate to single theme
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// CSS files to remove or disable
const cssFilesToRemove = [
  'src/styles/black-theme-global.css',
  'src/styles/v2-design-system.css',
  'src/theme/ionic-dark-theme.css',
  'src/theme/shot-brand.css',
  'src/theme/shot-brand-enhanced.css',
  'src/styles/global-background-override.css',
  'src/theme/variables.css' // Keep but modify
];

// CSS imports to remove from TypeScript files
const cssImportsToRemove = [
  'black-theme-global.css',
  'v2-design-system.css',
  'ionic-dark-theme.css',
  'shot-brand.css',
  'shot-brand-enhanced.css',
  'variables.css',
  '@ionic/react/css/core.css',
  '@ionic/react/css/normalize.css',
  '@ionic/react/css/structure.css',
  '@ionic/react/css/typography.css',
  '@ionic/react/css/padding.css',
  '@ionic/react/css/float-elements.css',
  '@ionic/react/css/text-alignment.css',
  '@ionic/react/css/text-transformation.css',
  '@ionic/react/css/flex-utils.css',
  '@ionic/react/css/display.css'
];

console.log('🧹 Starting CSS cleanup...\n');

// Step 1: Disable old CSS files
cssFilesToRemove.forEach(file => {
  const filePath = path.join(__dirname, '..', file);
  if (fs.existsSync(filePath)) {
    const disabledPath = filePath + '.disabled';
    fs.renameSync(filePath, disabledPath);
    console.log(`✅ Disabled: ${file}`);
  }
});

// Step 2: Find all TypeScript files
const tsFiles = glob.sync('src/**/*.{ts,tsx}', {
  cwd: path.resolve(__dirname, '..'),
  absolute: true
});

console.log(`\n📁 Found ${tsFiles.length} TypeScript files to check\n`);

let updatedCount = 0;

// Step 3: Remove CSS imports from TypeScript files
tsFiles.forEach(filePath => {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Check for CSS imports to remove
    cssImportsToRemove.forEach(cssFile => {
      const importRegex = new RegExp(`import\\s+['"].*${escapeRegExp(cssFile)}['"];?\\s*\n?`, 'g');
      if (content.match(importRegex)) {
        content = content.replace(importRegex, '');
        modified = true;
        console.log(`🔧 Removed import of ${cssFile} from ${path.basename(filePath)}`);
      }
    });
    
    if (modified) {
      fs.writeFileSync(filePath, content);
      updatedCount++;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

// Step 4: Create a simplified variables.css if needed
const variablesCss = `/* Simplified Ionic variables - references global theme */
:root {
  /* Point Ionic to our theme variables */
  --ion-background-color: var(--shot-background);
  --ion-text-color: var(--shot-text);
  --ion-item-background: var(--shot-surface);
  --ion-card-background: var(--shot-surface);
  --ion-toolbar-background: var(--shot-background);
  
  /* Keep essential Ionic colors */
  --ion-color-primary: var(--shot-purple);
  --ion-color-success: var(--shot-teal);
  --ion-color-warning: var(--shot-gold);
  --ion-color-danger: var(--shot-red);
}
`;

fs.writeFileSync(path.join(__dirname, '..', 'src/theme/variables-simplified.css'), variablesCss);
console.log('\n✅ Created simplified variables.css');

console.log(`\n✨ Cleanup complete! Updated ${updatedCount} files.`);
console.log('\n📝 Next steps:');
console.log('1. Check if any components need the simplified variables.css');
console.log('2. Test the app to ensure styling works correctly');
console.log('3. Delete the .disabled files once confirmed working');

function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}