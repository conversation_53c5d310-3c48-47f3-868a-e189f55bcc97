// ABOUTME: Script to check current position data in the live database
// This helps us create a migration that won't break existing data

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkPositionsInDatabase() {
  console.log('=== CHECKING POSITIONS IN DATABASE ===\n');

  // 1. Check sport_types table for sport_positions JSONB
  console.log('1. SPORT_TYPES TABLE - sport_positions field:');
  console.log('---------------------------------------------');
  try {
    const { data: sportTypes, error } = await supabase
      .from('sport_types')
      .select('sport_code, sport_positions')
      .eq('sport_code', 'football');
    
    if (error) throw error;
    
    if (sportTypes && sportTypes.length > 0) {
      console.log('Football sport_positions:');
      console.log(JSON.stringify(sportTypes[0].sport_positions, null, 2));
    }
  } catch (error) {
    console.error('Error fetching sport_types:', error.message);
  }

  // 2. Check evaluation_criteria positions
  console.log('\n\n2. EVALUATION_CRITERIA TABLE - unique positions:');
  console.log('-----------------------------------------------');
  try {
    const { data: positions, error } = await supabase
      .from('evaluation_criteria')
      .select('position')
      .eq('sport_type', 'football')
      .eq('framework_version', 'SHOT-2025');
    
    if (error) throw error;
    
    const uniquePositions = [...new Set(positions.map(p => p.position))].sort();
    console.log('Unique positions in evaluation_criteria:');
    uniquePositions.forEach(pos => console.log(`  - ${pos}`));
  } catch (error) {
    console.error('Error fetching evaluation_criteria:', error.message);
  }

  // 3. Check if POSITIONAL category exists
  console.log('\n\n3. EVALUATION_CRITERIA - POSITIONAL category:');
  console.log('--------------------------------------------');
  try {
    const { data: positionalCriteria, error } = await supabase
      .from('evaluation_criteria')
      .select('position, category, area, question')
      .eq('category', 'POSITIONAL')
      .eq('sport_type', 'football');
    
    if (error) throw error;
    
    if (positionalCriteria && positionalCriteria.length > 0) {
      console.log(`Found ${positionalCriteria.length} POSITIONAL criteria:`);
      positionalCriteria.forEach(criteria => {
        console.log(`  Position: ${criteria.position}, Area: ${criteria.area}`);
      });
    } else {
      console.log('No POSITIONAL criteria found in the database');
    }
  } catch (error) {
    console.error('Error fetching POSITIONAL criteria:', error.message);
  }

  // 4. Check team_members positions
  console.log('\n\n4. TEAM_MEMBERS TABLE - sample positions:');
  console.log('----------------------------------------');
  try {
    // Get a count of each position
    const { data: teamMembers, error } = await supabase
      .from('team_members')
      .select('position')
      .not('position', 'is', null);
    
    if (error) throw error;
    
    const positionCounts = {};
    teamMembers.forEach(member => {
      const pos = member.position || 'null';
      positionCounts[pos] = (positionCounts[pos] || 0) + 1;
    });
    
    console.log('Position distribution in team_members:');
    Object.entries(positionCounts)
      .sort((a, b) => b[1] - a[1])
      .forEach(([position, count]) => {
        console.log(`  ${position}: ${count} players`);
      });
  } catch (error) {
    console.error('Error fetching team_members:', error.message);
  }

  // 5. Check specific team positions
  console.log('\n\n5. SAMPLE TEAM POSITIONS:');
  console.log('-------------------------');
  try {
    const { data: sampleTeam, error } = await supabase
      .from('team_members')
      .select('position, team_id')
      .eq('team_id', '2dd31c63-ef3a-4c86-b5f2-fc7ce24fc352')
      .not('position', 'is', null)
      .limit(10);
    
    if (error) throw error;
    
    console.log('Positions in team 2dd31c63-ef3a-4c86-b5f2-fc7ce24fc352:');
    sampleTeam.forEach(member => {
      console.log(`  - ${member.position}`);
    });
  } catch (error) {
    console.error('Error fetching sample team:', error.message);
  }

  process.exit(0);
}

checkPositionsInDatabase();