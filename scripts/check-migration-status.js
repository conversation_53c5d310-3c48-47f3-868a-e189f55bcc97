// ABOUTME: Script to check if our migration has been applied to production
// This checks for the new functions and trigger we created

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkMigrationStatus() {
  console.log('=== CHECKING MIGRATION STATUS ===\n');

  // 1. Check if map_position_to_evaluation function exists
  console.log('1. CHECKING FOR map_position_to_evaluation FUNCTION:');
  console.log('----------------------------------------------------');
  try {
    // Test the function by calling it
    const { data, error } = await supabase.rpc('map_position_to_evaluation', {
      p_position: 'goalkeeper'
    });
    
    if (error) {
      if (error.message.includes('does not exist')) {
        console.log('❌ Function does NOT exist - migration not applied');
      } else {
        console.log('❓ Function exists but error:', error.message);
      }
    } else {
      console.log('✅ Function exists! Result:', data);
    }
  } catch (error) {
    console.error('Error checking function:', error.message);
  }

  // 2. Check if create_basic_pre_evaluations accepts positions parameter
  console.log('\n\n2. CHECKING create_basic_pre_evaluations SIGNATURE:');
  console.log('---------------------------------------------------');
  try {
    // Check a recent pre-evaluation to see if player_position is populated
    const { data: recentPreEval, error } = await supabase
      .from('pre_evaluations')
      .select('id, player_position, created_at')
      .order('created_at', { ascending: false })
      .limit(5);
    
    if (!error && recentPreEval) {
      console.log('Recent pre-evaluations:');
      recentPreEval.forEach(pe => {
        console.log(`  ${pe.created_at}: player_position = ${pe.player_position || 'NULL'}`);
      });
      
      // Check if any have positions set
      const hasPositions = recentPreEval.some(pe => pe.player_position !== null);
      if (hasPositions) {
        console.log('✅ Some pre-evaluations have positions - new function likely in use');
      } else {
        console.log('❓ No positions found - might be old function or no positioned players');
      }
    }
  } catch (error) {
    console.error('Error checking pre-evaluations:', error.message);
  }

  // 3. Check sport_positions in sport_types
  console.log('\n\n3. CHECKING sport_positions FORMAT:');
  console.log('-----------------------------------');
  try {
    const { data: sportType, error } = await supabase
      .from('sport_types')
      .select('sport_positions')
      .eq('sport_code', 'football')
      .single();
    
    if (!error && sportType) {
      const positions = sportType.sport_positions?.positions || [];
      console.log('Position values:');
      positions.forEach(pos => {
        if (pos.value) {
          console.log(`  ${pos.value} (${pos.label})`);
        }
      });
      
      // Check if using new format (uppercase positions)
      const hasUppercase = positions.some(pos => 
        ['GOALKEEPER', 'CENTRE BACK', 'FULL BACK', 'MIDFIELD', 'STRIKER', 'WINGER'].includes(pos.value)
      );
      
      if (hasUppercase) {
        console.log('✅ Using NEW position format (uppercase)');
      } else {
        console.log('❌ Still using old position format (lowercase)');
      }
    }
  } catch (error) {
    console.error('Error checking sport_positions:', error.message);
  }

  // 4. Check team_members positions
  console.log('\n\n4. CHECKING team_members POSITIONS:');
  console.log('-----------------------------------');
  try {
    const { data: positions, error } = await supabase
      .from('team_members')
      .select('position')
      .not('position', 'is', null)
      .limit(20);
    
    if (!error && positions) {
      const positionCounts = {};
      positions.forEach(tm => {
        positionCounts[tm.position] = (positionCounts[tm.position] || 0) + 1;
      });
      
      console.log('Position distribution:');
      Object.entries(positionCounts).forEach(([pos, count]) => {
        console.log(`  ${pos}: ${count}`);
      });
      
      // Check if positions are uppercase
      const hasUppercase = Object.keys(positionCounts).some(pos => 
        ['GOALKEEPER', 'CENTRE BACK', 'FULL BACK', 'MIDFIELD', 'STRIKER', 'WINGER'].includes(pos)
      );
      
      if (hasUppercase) {
        console.log('✅ Positions have been migrated to uppercase');
      } else {
        console.log('❌ Positions still in lowercase - migration not applied');
      }
    }
  } catch (error) {
    console.error('Error checking positions:', error.message);
  }

  // 5. Check for trigger on pre_evaluations
  console.log('\n\n5. CHECKING FOR TRIGGER EFFECTS:');
  console.log('---------------------------------');
  try {
    // Find a recent pre-evaluation and check if it has related player_evaluations
    const { data: recentPre, error: preError } = await supabase
      .from('pre_evaluations')
      .select('id, player_id, player_position')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();
    
    if (!preError && recentPre) {
      // Check for related player_evaluations
      const { data: playerEvals, error: peError } = await supabase
        .from('player_evaluations')
        .select('category, position')
        .eq('pre_evaluation_id', recentPre.id);
      
      if (!peError && playerEvals) {
        console.log(`Pre-evaluation ${recentPre.id}:`);
        console.log(`  Player position: ${recentPre.player_position || 'NULL'}`);
        console.log(`  Related player_evaluations: ${playerEvals.length}`);
        if (playerEvals.length > 0) {
          const categories = [...new Set(playerEvals.map(pe => pe.category))];
          console.log(`  Categories: ${categories.join(', ')}`);
          console.log(`  Position in evaluations: ${playerEvals[0].position}`);
        }
      }
    }
  } catch (error) {
    console.error('Error checking trigger effects:', error.message);
  }

  console.log('\n\n=== MIGRATION STATUS SUMMARY ===');
  console.log('Check the results above to determine if migration was applied.');

  process.exit(0);
}

checkMigrationStatus();