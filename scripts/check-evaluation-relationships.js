// ABOUTME: Script to check relationships between evaluation tables
// This script examines how pre_evaluations and player_evaluations are related

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

async function checkEvaluationRelationships() {
  console.log('=== EVALUATION RELATIONSHIPS ANALYSIS ===\n');

  try {
    // 1. Check if pre_evaluation_id exists in player_evaluations
    console.log('1. CHECKING PRE_EVALUATION_ID RELATIONSHIPS:');
    console.log('--------------------------------------------');
    
    const { data: playerEvalsWithPreEval, error: peError } = await supabase
      .from('player_evaluations')
      .select('id, pre_evaluation_id, player_id, position')
      .not('pre_evaluation_id', 'is', null)
      .limit(10);

    if (peError) {
      console.log('Error:', peError);
    } else {
      console.log(`Found ${playerEvalsWithPreEval?.length || 0} player_evaluations with pre_evaluation_id\n`);
      playerEvalsWithPreEval?.forEach(record => {
        console.log(`- Player eval ${record.id}: pre_eval=${record.pre_evaluation_id}, position=${record.position}`);
      });
    }

    // 2. Check unique positions in player_evaluations
    console.log('\n\n2. UNIQUE POSITIONS IN PLAYER_EVALUATIONS:');
    console.log('-------------------------------------------');
    
    const { data: positions, error: posError } = await supabase
      .from('player_evaluations')
      .select('position')
      .not('position', 'is', null);

    if (posError) {
      console.log('Error:', posError);
    } else {
      const uniquePositions = [...new Set(positions?.map(p => p.position))].sort();
      console.log(`Found ${uniquePositions.length} unique positions:`);
      uniquePositions.forEach(pos => console.log(`- ${pos}`));
    }

    // 3. Check if player_position exists in pre_evaluations
    console.log('\n\n3. PLAYER POSITIONS IN PRE_EVALUATIONS:');
    console.log('----------------------------------------');
    
    const { data: preEvalsWithPosition, error: preEvalPosError } = await supabase
      .from('pre_evaluations')
      .select('id, player_position')
      .not('player_position', 'is', null)
      .limit(10);

    if (preEvalPosError) {
      console.log('Error:', preEvalPosError);
    } else {
      console.log(`Found ${preEvalsWithPosition?.length || 0} pre_evaluations with player_position\n`);
      preEvalsWithPosition?.forEach(record => {
        console.log(`- Pre-eval ${record.id}: position=${JSON.stringify(record.player_position)}`);
      });
    }

    // 4. Check responses structure in pre_evaluations
    console.log('\n\n4. RESPONSES STRUCTURE IN PRE_EVALUATIONS:');
    console.log('-------------------------------------------');
    
    const { data: preEvalsWithResponses, error: respError } = await supabase
      .from('pre_evaluations')
      .select('id, responses')
      .not('responses', 'is', null)
      .neq('responses->questions', '[]')
      .limit(3);

    if (respError) {
      console.log('Error:', respError);
    } else {
      console.log(`Found ${preEvalsWithResponses?.length || 0} pre_evaluations with responses\n`);
      preEvalsWithResponses?.forEach(record => {
        console.log(`Pre-eval ${record.id}:`);
        if (record.responses?.questions && Array.isArray(record.responses.questions)) {
          console.log(`- Has ${record.responses.questions.length} questions`);
          const firstQuestion = record.responses.questions[0];
          if (firstQuestion) {
            console.log(`- First question structure: ${Object.keys(firstQuestion).join(', ')}`);
          }
        }
        console.log('');
      });
    }

    // 5. Check evaluation_criteria_data in player_evaluations
    console.log('\n\n5. EVALUATION CRITERIA DATA IN PLAYER_EVALUATIONS:');
    console.log('--------------------------------------------------');
    
    const { data: playerEvalsWithCriteria, error: critError } = await supabase
      .from('player_evaluations')
      .select('id, position, evaluation_criteria_data')
      .not('evaluation_criteria_data', 'is', null)
      .limit(5);

    if (critError) {
      console.log('Error:', critError);
    } else {
      console.log(`Found ${playerEvalsWithCriteria?.length || 0} player_evaluations with evaluation_criteria_data\n`);
      playerEvalsWithCriteria?.forEach(record => {
        console.log(`- Player eval ${record.id}: position=${record.position}`);
        if (record.evaluation_criteria_data) {
          console.log(`  Criteria data: ${JSON.stringify(record.evaluation_criteria_data).substring(0, 100)}...`);
        }
      });
    }

    // 6. Count evaluations by status
    console.log('\n\n6. EVALUATION STATUS DISTRIBUTION:');
    console.log('----------------------------------');
    
    // Pre-evaluations by status
    const { data: preEvalStatuses, error: preStatusError } = await supabase
      .from('pre_evaluations')
      .select('status')
      .not('status', 'is', null);

    if (preStatusError) {
      console.log('Pre-eval status error:', preStatusError);
    } else {
      const statusCounts = preEvalStatuses?.reduce((acc, curr) => {
        acc[curr.status] = (acc[curr.status] || 0) + 1;
        return acc;
      }, {});
      console.log('\nPre-evaluations by status:');
      Object.entries(statusCounts || {}).forEach(([status, count]) => {
        console.log(`- ${status}: ${count}`);
      });
    }

    // Player evaluations by evaluation_status
    const { data: playerEvalStatuses, error: playerStatusError } = await supabase
      .from('player_evaluations')
      .select('evaluation_status')
      .not('evaluation_status', 'is', null);

    if (playerStatusError) {
      console.log('Player eval status error:', playerStatusError);
    } else {
      const statusCounts = playerEvalStatuses?.reduce((acc, curr) => {
        acc[curr.evaluation_status] = (acc[curr.evaluation_status] || 0) + 1;
        return acc;
      }, {});
      console.log('\nPlayer evaluations by status:');
      Object.entries(statusCounts || {}).forEach(([status, count]) => {
        console.log(`- ${status}: ${count}`);
      });
    }

  } catch (error) {
    console.error('Error checking relationships:', error);
  }
}

checkEvaluationRelationships();