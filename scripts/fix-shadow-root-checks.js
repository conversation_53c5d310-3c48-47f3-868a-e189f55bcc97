#!/usr/bin/env node

// ABOUTME: Script to fix shadow root check issue in foundation components
// Removes early return when shadow root exists to allow content updates

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Get all TypeScript/JSX files in the foundation directory
function getFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  files.forEach(file => {
    const filePath = path.join(dir, file);
    if (fs.statSync(filePath).isDirectory()) {
      getFiles(filePath, fileList);
    } else if (/\.(tsx?|jsx?)$/.test(file)) {
      fileList.push(filePath);
    }
  });
  return fileList;
}

const foundationDir = path.resolve(__dirname, '../src/foundation');
const files = getFiles(foundationDir);

let filesFixed = 0;
let totalFiles = 0;

files.forEach(file => {
  try {
    const content = fs.readFileSync(file, 'utf8');
    
    // Check if file contains the problematic pattern
    if (content.includes('if (') && content.includes('.shadowRoot)') && content.includes('return;')) {
      totalFiles++;
      
      // Split content into lines for easier processing
      const lines = content.split('\n');
      const newLines = [];
      let skipNextLines = 0;
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const trimmedLine = line.trim();
        
        // Skip lines if we're in a block to remove
        if (skipNextLines > 0) {
          if (trimmedLine === '}') {
            skipNextLines--;
          }
          continue;
        }
        
        // Check for the problematic pattern
        if (trimmedLine.includes('// Check if shadow root already exists') ||
            (trimmedLine.startsWith('if (') && trimmedLine.includes('.shadowRoot)'))) {
          // Look ahead to see if this is followed by return
          let hasReturn = false;
          for (let j = i + 1; j < Math.min(i + 10, lines.length); j++) {
            if (lines[j].trim() === 'return;') {
              hasReturn = true;
              break;
            }
            if (lines[j].trim() === '}') {
              break;
            }
          }
          
          if (hasReturn) {
            // Skip this block
            if (trimmedLine.includes('// Check if shadow root already exists')) {
              // Skip comment line
              continue;
            }
            // Skip the if block
            skipNextLines = 1;
            continue;
          }
        }
        
        newLines.push(line);
      }
      
      const newContent = newLines.join('\n');
      
      if (newContent !== content) {
        fs.writeFileSync(file, newContent, 'utf8');
        filesFixed++;
        console.log(`✅ Fixed: ${path.relative(process.cwd(), file)}`);
      }
    }
  } catch (error) {
    console.error(`❌ Error processing ${file}:`, error.message);
  }
});

console.log(`\n📊 Summary:`);
console.log(`   Files scanned: ${files.length}`);
console.log(`   Files with shadow root issue: ${totalFiles}`);
console.log(`   Files fixed: ${filesFixed}`);

if (filesFixed > 0) {
  console.log('\n✨ Shadow root checks have been removed. Components should now render properly.');
  console.log('⚠️  Please test the application to ensure everything works correctly.');
} else {
  console.log('\n✅ No files needed fixing.');
}