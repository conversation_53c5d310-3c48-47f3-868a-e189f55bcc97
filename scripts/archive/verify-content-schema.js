#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_APP_SUPABASE_URL;
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Verifying Content Schema Migration...\n');

async function checkSchema() {
  console.log('1️⃣  Checking if content schema exists...');
  
  const { data: schemas, error: schemaError } = await supabase
    .rpc('to_json', { query: `SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'content'` });
  
  if (schemaError) {
    console.error('❌ Error checking schema:', schemaError.message);
    return false;
  }
  
  if (!schemas || schemas.length === 0) {
    console.error('❌ Content schema not found!');
    return false;
  }
  
  console.log('✅ Content schema exists\n');
  return true;
}

async function checkTables() {
  console.log('2️⃣  Checking tables in content schema...');
  
  const expectedTables = [
    'pulse_articles',
    'pulse_sync_log',
    'api_keys',
    'widget_configurations'
  ];
  
  for (const table of expectedTables) {
    const { data, error } = await supabase
      .from(`content.${table}`)
      .select('*')
      .limit(1);
    
    if (error && !error.message.includes('permission')) {
      console.error(`❌ Table content.${table} not found or has errors:`, error.message);
    } else {
      console.log(`✅ Table content.${table} exists`);
    }
  }
  
  console.log('');
}

async function checkFunctions() {
  console.log('3️⃣  Checking functions in content schema...');
  
  const functions = [
    { name: 'check_sync_health', params: {} },
    { name: 'get_sync_stats', params: { time_period: '24 hours' } },
    { name: 'get_latest_sync_status', params: {} },
    { name: 'search_pulse_articles', params: { search_query: 'test' } }
  ];
  
  for (const func of functions) {
    try {
      const { data, error } = await supabase
        .rpc(func.name, func.params, { schema: 'content' });
      
      if (error) {
        console.error(`❌ Function content.${func.name} error:`, error.message);
      } else {
        console.log(`✅ Function content.${func.name} exists and is callable`);
      }
    } catch (e) {
      console.error(`❌ Function content.${func.name} error:`, e.message);
    }
  }
  
  console.log('');
}

async function checkCronJobs() {
  console.log('4️⃣  Checking cron jobs...');
  
  try {
    const { data, error } = await supabase
      .rpc('to_json', { 
        query: `SELECT jobname, schedule, active FROM cron.job WHERE jobname IN ('sync-pulse-content', 'retry-failed-content-syncs')` 
      });
    
    if (error) {
      console.error('❌ Error checking cron jobs:', error.message);
      return;
    }
    
    if (!data || data.length === 0) {
      console.error('❌ No cron jobs found!');
      return;
    }
    
    data.forEach(job => {
      console.log(`✅ Cron job '${job.jobname}' - Schedule: ${job.schedule} - Active: ${job.active}`);
    });
  } catch (e) {
    console.error('❌ Error checking cron jobs:', e.message);
  }
  
  console.log('');
}

async function checkRLSPolicies() {
  console.log('5️⃣  Checking RLS policies...');
  
  try {
    const { data, error } = await supabase
      .rpc('to_json', { 
        query: `
          SELECT tablename, policyname 
          FROM pg_policies 
          WHERE schemaname = 'content' 
          ORDER BY tablename, policyname
        ` 
      });
    
    if (error) {
      console.error('❌ Error checking RLS policies:', error.message);
      return;
    }
    
    if (!data || data.length === 0) {
      console.error('❌ No RLS policies found for content schema!');
      return;
    }
    
    const tableGroups = {};
    data.forEach(policy => {
      if (!tableGroups[policy.tablename]) {
        tableGroups[policy.tablename] = [];
      }
      tableGroups[policy.tablename].push(policy.policyname);
    });
    
    Object.entries(tableGroups).forEach(([table, policies]) => {
      console.log(`✅ Table ${table} has ${policies.length} RLS policies:`);
      policies.forEach(p => console.log(`   - ${p}`));
    });
  } catch (e) {
    console.error('❌ Error checking RLS policies:', e.message);
  }
  
  console.log('');
}

async function testContentSync() {
  console.log('6️⃣  Testing content sync functionality...');
  
  try {
    // Check latest sync status
    const { data: syncStatus, error: statusError } = await supabase
      .rpc('get_latest_sync_status', {}, { schema: 'content' });
    
    if (statusError) {
      console.error('❌ Error getting sync status:', statusError.message);
    } else if (!syncStatus || syncStatus.length === 0) {
      console.log('⚠️  No sync history found (this is normal for new installations)');
    } else {
      const status = syncStatus[0];
      console.log(`✅ Latest sync status: ${status.status || 'No syncs yet'}`);
      if (status.started_at) {
        console.log(`   Started: ${new Date(status.started_at).toLocaleString()}`);
        console.log(`   Articles synced: ${status.articles_synced || 0}`);
      }
    }
    
    // Check sync health
    const { data: health, error: healthError } = await supabase
      .rpc('check_sync_health', {}, { schema: 'content' });
    
    if (healthError) {
      console.error('❌ Error checking sync health:', healthError.message);
    } else if (health && health.length > 0) {
      const h = health[0];
      console.log(`✅ Sync health: ${h.alert_level} - ${h.message}`);
    }
  } catch (e) {
    console.error('❌ Error testing sync functionality:', e.message);
  }
  
  console.log('');
}

async function main() {
  const schemaExists = await checkSchema();
  
  if (!schemaExists) {
    console.log('\n❌ Content schema does not exist. Please run the migrations first.');
    process.exit(1);
  }
  
  await checkTables();
  await checkFunctions();
  await checkCronJobs();
  await checkRLSPolicies();
  await testContentSync();
  
  console.log('\n✅ Verification complete!');
  console.log('\nNext steps:');
  console.log('1. Visit /v2/admin/content-sync to see the monitoring dashboard');
  console.log('2. Trigger a manual sync to populate initial data');
  console.log('3. Check /v2/pulse to see the articles');
}

main().catch(console.error);