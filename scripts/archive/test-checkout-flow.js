// ABOUTME: Test script to verify checkout flow is working
// Run this to test the edge function and checkout process

const SUPABASE_URL = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

async function testCheckout() {
  console.log('Testing checkout flow...\n');
  
  // Test data
  const testCheckoutData = {
    items: [
      {
        id: 'test-item-1',
        productId: 123,
        name: 'SHOT Cap - Founder Member',
        price: 49.00,
        quantity: 1,
        imageUrl: 'https://example.com/cap.jpg'
      }
    ],
    shippingAddress: {
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      phone: '+1234567890',
      address1: '123 Test Street',
      address2: 'Apt 4B',
      city: 'New York',
      state: 'NY',
      postalCode: '10001',
      country: 'US'
    },
    cartSessionId: `test-cart-${Date.now()}`
  };

  try {
    console.log('Calling edge function at:', `${SUPABASE_URL}/functions/v1/locker-checkout`);
    console.log('Request data:', JSON.stringify(testCheckoutData, null, 2));
    
    const response = await fetch(`${SUPABASE_URL}/functions/v1/locker-checkout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
      },
      body: JSON.stringify(testCheckoutData),
    });

    console.log('\nResponse status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const data = await response.json();
    console.log('\nResponse data:', JSON.stringify(data, null, 2));

    if (response.ok) {
      console.log('\n✅ Checkout test successful!');
      console.log('Order ID:', data.orderId);
      console.log('Total amount:', data.amount);
      console.log('Status:', data.status || 'pending');
    } else {
      console.log('\n❌ Checkout test failed');
      console.log('Error:', data.error || data.message);
    }
  } catch (error) {
    console.error('\n❌ Error testing checkout:', error.message);
  }
}

// Run the test
testCheckout();