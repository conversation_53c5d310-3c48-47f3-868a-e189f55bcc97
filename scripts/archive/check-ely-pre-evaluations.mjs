#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkElyPreEvaluations() {
  console.log('Checking for Ely team pre-evaluations...\n');

  try {
    // First, find the Ely team
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('id, name')
      .ilike('name', '%ely%');

    if (teamsError || !teams || teams.length === 0) {
      console.log('Could not find Ely team');
      return;
    }

    console.log('Found teams:', teams.map(t => `${t.name} (${t.id})`).join(', '));
    
    for (const team of teams) {
      console.log(`\n\nChecking pre-evaluations for team: ${team.name}`);
      
      // Get pre-evaluations for this team
      const { data: preEvals, error: preEvalsError } = await supabase
        .from('pre_evaluations')
        .select(`
          id,
          event_id,
          player_id,
          status,
          completed_at,
          responses,
          confidence_level,
          mood_rating,
          questions_answered,
          total_questions,
          request_method,
          profiles!pre_evaluations_player_id_fkey (
            full_name
          ),
          events!pre_evaluations_event_id_fkey (
            name,
            start_datetime
          )
        `)
        .eq('team_id', team.id)
        .order('completed_at', { ascending: false })
        .limit(10);

      if (preEvalsError) {
        console.error('Error fetching pre-evaluations:', preEvalsError);
        continue;
      }

      if (!preEvals || preEvals.length === 0) {
        console.log('No pre-evaluations found for this team');
        continue;
      }

      console.log(`\nFound ${preEvals.length} pre-evaluations:`);
      
      preEvals.forEach((preEval, index) => {
        const playerName = preEval.profiles?.full_name || 'Unknown';
        const eventName = preEval.events?.name || 'Unknown Event';
        const eventDate = preEval.events?.start_datetime ? new Date(preEval.events.start_datetime).toLocaleDateString() : 'No date';
        
        console.log(`\n${index + 1}. Player: ${playerName}`);
        console.log(`   Event: ${eventName} (${eventDate})`);
        console.log(`   Status: ${preEval.status}`);
        console.log(`   Completed: ${preEval.completed_at ? new Date(preEval.completed_at).toLocaleString() : 'Not completed'}`);
        console.log(`   Method: ${preEval.request_method || 'Not specified'}`);
        console.log(`   Questions: ${preEval.questions_answered}/${preEval.total_questions}`);
        console.log(`   Confidence: ${preEval.confidence_level || 'Not set'}`);
        console.log(`   Mood: ${preEval.mood_rating || 'Not set'}`);
        
        if (preEval.responses) {
          console.log(`   Responses structure:`);
          console.log(JSON.stringify(preEval.responses, null, 2).split('\n').slice(0, 20).join('\n'));
          
          // Check if responses contain actual evaluation data
          if (preEval.responses.questions && Array.isArray(preEval.responses.questions)) {
            console.log(`   Number of question responses: ${preEval.responses.questions.length}`);
            
            // Show first few questions
            preEval.responses.questions.slice(0, 3).forEach((q, qi) => {
              console.log(`   Question ${qi + 1}: ${q.question || 'No question text'}`);
              console.log(`   Answer: ${q.answer || q.rating || 'No answer'}`);
            });
          }
        }
      });

      // Look specifically for today's pre-evaluations
      const today = new Date().toISOString().split('T')[0];
      const { data: todayPreEvals, error: todayError } = await supabase
        .from('pre_evaluations')
        .select(`
          *,
          profiles!pre_evaluations_player_id_fkey (
            full_name
          )
        `)
        .eq('team_id', team.id)
        .gte('completed_at', today)
        .eq('status', 'completed');

      if (todayPreEvals && todayPreEvals.length > 0) {
        console.log(`\n\n🔥 TODAY'S PRE-EVALUATIONS (${todayPreEvals.length}):`);
        todayPreEvals.forEach(pe => {
          console.log(`\n- ${pe.profiles?.full_name || 'Unknown'} completed at ${new Date(pe.completed_at).toLocaleTimeString()}`);
          console.log(`  Method: ${pe.request_method}`);
          if (pe.responses && pe.responses.questions) {
            console.log(`  Full responses:`);
            console.log(JSON.stringify(pe.responses, null, 2));
          }
        });
      }

      // Check for Mark specifically
      const { data: markProfile } = await supabase
        .from('profiles')
        .select('id, full_name')
        .ilike('full_name', '%mark%')
        .single();

      if (markProfile) {
        console.log(`\n\n🔍 Checking for Mark's pre-evaluations (${markProfile.full_name}):`);
        
        const { data: markPreEvals } = await supabase
          .from('pre_evaluations')
          .select('*, events!pre_evaluations_event_id_fkey(name)')
          .eq('player_id', markProfile.id)
          .eq('team_id', team.id)
          .order('completed_at', { ascending: false })
          .limit(5);

        if (markPreEvals && markPreEvals.length > 0) {
          markPreEvals.forEach(pe => {
            console.log(`\n- Event: ${pe.events?.name}`);
            console.log(`  Status: ${pe.status}`);
            console.log(`  Completed: ${pe.completed_at ? new Date(pe.completed_at).toLocaleString() : 'Not completed'}`);
            console.log(`  Method: ${pe.request_method}`);
            if (pe.responses) {
              console.log(`  Responses:`);
              console.log(JSON.stringify(pe.responses, null, 2));
            }
          });
        }
      }
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the check
checkElyPreEvaluations();