#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_APP_SUPABASE_URL;
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Testing Pulse Content System...\n');

async function runTests() {
  let allPassed = true;
  
  // Test 1: Check pulse_articles view
  console.log('1️⃣  Testing pulse_articles access...');
  try {
    const { data, error, count } = await supabase
      .from('pulse_articles')
      .select('*', { count: 'exact' })
      .limit(5);
    
    if (error) {
      console.error('❌ Error accessing pulse_articles:', error.message);
      allPassed = false;
    } else {
      console.log(`✅ pulse_articles accessible - Found ${count || 0} articles`);
      if (data && data.length > 0) {
        console.log(`   Sample article: "${data[0].title}"`);
      }
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
    allPassed = false;
  }
  
  // Test 2: Check pulse_sync_log view
  console.log('\n2️⃣  Testing pulse_sync_log access...');
  try {
    const { data, error } = await supabase
      .from('pulse_sync_log')
      .select('*')
      .order('sync_started_at', { ascending: false })
      .limit(1);
    
    if (error) {
      console.error('❌ Error accessing pulse_sync_log:', error.message);
      allPassed = false;
    } else {
      console.log('✅ pulse_sync_log accessible');
      if (data && data.length > 0) {
        console.log(`   Latest sync: ${data[0].sync_status} at ${new Date(data[0].sync_started_at).toLocaleString()}`);
      } else {
        console.log('   No sync records yet (this is normal for new installations)');
      }
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
    allPassed = false;
  }
  
  // Test 3: Check functions
  console.log('\n3️⃣  Testing RPC functions...');
  
  // Test check_sync_health
  try {
    const { data, error } = await supabase
      .rpc('check_sync_health');
    
    if (error) {
      console.error('❌ Error calling check_sync_health:', error.message);
      allPassed = false;
    } else {
      console.log('✅ check_sync_health() works');
      if (data && data.length > 0) {
        console.log(`   Health status: ${data[0].alert_level} - ${data[0].message}`);
      }
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
    allPassed = false;
  }
  
  // Test get_sync_stats
  try {
    const { data, error } = await supabase
      .rpc('get_sync_stats', { time_period: '24 hours' });
    
    if (error) {
      console.error('❌ Error calling get_sync_stats:', error.message);
      allPassed = false;
    } else {
      console.log('✅ get_sync_stats() works');
      if (data && data.length > 0) {
        const stats = data[0];
        console.log(`   Total syncs: ${stats.total_syncs || 0}`);
        console.log(`   Success rate: ${stats.success_rate || 0}%`);
      }
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
    allPassed = false;
  }
  
  // Test search_pulse_articles
  try {
    const { data, error } = await supabase
      .rpc('search_pulse_articles', { search_query: 'test' });
    
    if (error) {
      console.error('❌ Error calling search_pulse_articles:', error.message);
      allPassed = false;
    } else {
      console.log('✅ search_pulse_articles() works');
      console.log(`   Found ${data ? data.length : 0} articles matching "test"`);
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
    allPassed = false;
  }
  
  // Test 4: Verify RLS policies
  console.log('\n4️⃣  Testing RLS policies...');
  
  // Test with anon key (public access)
  const supabaseAnon = createClient(
    supabaseUrl,
    process.env.VITE_APP_SUPABASE_ANON_KEY
  );
  
  try {
    const { data, error } = await supabaseAnon
      .from('pulse_articles')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ Public read access blocked:', error.message);
      allPassed = false;
    } else {
      console.log('✅ Public can read pulse_articles (RLS working correctly)');
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
    allPassed = false;
  }
  
  // Summary
  console.log('\n' + '='.repeat(50));
  if (allPassed) {
    console.log('✅ All tests passed! The pulse section is working correctly.');
    console.log('\nNext steps:');
    console.log('1. Visit /v2/admin/content-sync to see the dashboard');
    console.log('2. Trigger a manual sync to populate articles');
    console.log('3. Visit /v2/pulse to see the articles');
  } else {
    console.log('❌ Some tests failed. Please check the errors above.');
  }
}

runTests().catch(console.error);