#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_APP_SUPABASE_URL;
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Checking what actually exists in the database...\n');

async function checkTables() {
  // Check if pulse_articles exists in public schema
  console.log('Checking public schema:');
  try {
    const { data, error } = await supabase
      .from('pulse_articles')
      .select('*')
      .limit(1);
    
    if (!error) {
      console.log('✅ pulse_articles exists in PUBLIC schema');
    } else {
      console.log('❌ pulse_articles NOT in public schema:', error.message);
    }
  } catch (e) {
    console.log('❌ Error:', e.message);
  }
  
  // Check if pulse_sync_log exists in public schema
  try {
    const { data, error } = await supabase
      .from('pulse_sync_log')
      .select('*')
      .limit(1);
    
    if (!error) {
      console.log('✅ pulse_sync_log exists in PUBLIC schema');
    } else {
      console.log('❌ pulse_sync_log NOT in public schema:', error.message);
    }
  } catch (e) {
    console.log('❌ Error:', e.message);
  }
  
  // Test functions without schema prefix
  console.log('\nChecking functions in public schema:');
  try {
    const { data, error } = await supabase
      .rpc('check_sync_health');
    
    if (!error) {
      console.log('✅ check_sync_health exists in PUBLIC schema');
    } else {
      console.log('❌ check_sync_health NOT in public schema:', error.message);
    }
  } catch (e) {
    console.log('❌ Error:', e.message);
  }
  
  try {
    const { data, error } = await supabase
      .rpc('get_sync_stats', { time_period: '24 hours' });
    
    if (!error) {
      console.log('✅ get_sync_stats exists in PUBLIC schema');
    } else {
      console.log('❌ get_sync_stats NOT in public schema:', error.message);
    }
  } catch (e) {
    console.log('❌ Error:', e.message);
  }
}

checkTables().catch(console.error);