#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_APP_SUPABASE_URL;
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Verifying Content Schema Migration...\n');

async function runChecks() {
  console.log('1️⃣  Testing tables...');
  
  // Test pulse_articles table
  try {
    const { data, error } = await supabase
      .from('pulse_articles')
      .select('count', { count: 'exact', head: true })
      .eq('id', '00000000-0000-0000-0000-000000000000'); // Dummy query
    
    if (error && error.message.includes('relation "public.pulse_articles" does not exist')) {
      console.log('❌ pulse_articles table not found in public schema');
      
      // Try with content schema
      const { data: contentData, error: contentError } = await supabase
        .from('content.pulse_articles')
        .select('count', { count: 'exact', head: true });
      
      if (!contentError) {
        console.log('✅ content.pulse_articles table exists');
      } else {
        console.log('❌ content.pulse_articles error:', contentError.message);
      }
    } else {
      console.log('⚠️  pulse_articles might be in public schema');
    }
  } catch (e) {
    console.error('❌ Error checking pulse_articles:', e.message);
  }
  
  // Test pulse_sync_log table
  try {
    const { data, error } = await supabase
      .from('content.pulse_sync_log')
      .select('*')
      .order('sync_started_at', { ascending: false })
      .limit(1);
    
    if (!error) {
      console.log('✅ content.pulse_sync_log table exists');
      if (data && data.length > 0) {
        console.log(`   Latest sync: ${data[0].sync_status} at ${new Date(data[0].sync_started_at).toLocaleString()}`);
      } else {
        console.log('   No sync records yet');
      }
    } else {
      console.log('❌ content.pulse_sync_log error:', error.message);
    }
  } catch (e) {
    console.error('❌ Error checking pulse_sync_log:', e.message);
  }
  
  console.log('\n2️⃣  Testing functions...');
  
  // Test check_sync_health
  try {
    const { data, error } = await supabase
      .rpc('check_sync_health', {}, { schema: 'content' });
    
    if (!error) {
      console.log('✅ content.check_sync_health() function works');
      if (data && data.length > 0) {
        console.log(`   Health: ${data[0].alert_level} - ${data[0].message}`);
      }
    } else {
      console.log('❌ content.check_sync_health() error:', error.message);
    }
  } catch (e) {
    console.error('❌ Error calling check_sync_health:', e.message);
  }
  
  // Test get_sync_stats
  try {
    const { data, error } = await supabase
      .rpc('get_sync_stats', { time_period: '24 hours' }, { schema: 'content' });
    
    if (!error) {
      console.log('✅ content.get_sync_stats() function works');
      if (data && data.length > 0) {
        const stats = data[0];
        console.log(`   Total syncs: ${stats.total_syncs || 0}`);
        console.log(`   Success rate: ${stats.success_rate || 0}%`);
      }
    } else {
      console.log('❌ content.get_sync_stats() error:', error.message);
    }
  } catch (e) {
    console.error('❌ Error calling get_sync_stats:', e.message);
  }
  
  // Test get_latest_sync_status
  try {
    const { data, error } = await supabase
      .rpc('get_latest_sync_status', {}, { schema: 'content' });
    
    if (!error) {
      console.log('✅ content.get_latest_sync_status() function works');
    } else {
      console.log('❌ content.get_latest_sync_status() error:', error.message);
    }
  } catch (e) {
    console.error('❌ Error calling get_latest_sync_status:', e.message);
  }
  
  console.log('\n3️⃣  Testing RLS policies...');
  
  // Test public read on pulse_articles
  try {
    const { data, error } = await supabase
      .from('content.pulse_articles')
      .select('*')
      .limit(1);
    
    if (!error) {
      console.log('✅ Can read from content.pulse_articles (RLS allows public read)');
    } else if (error.message.includes('permission denied')) {
      console.log('❌ RLS policies might be blocking reads on pulse_articles');
    } else {
      console.log('⚠️  Unexpected error reading pulse_articles:', error.message);
    }
  } catch (e) {
    console.error('❌ Error testing RLS:', e.message);
  }
  
  console.log('\n4️⃣  Summary:');
  console.log('If you see mostly ✅ marks above, the migrations were applied successfully!');
  console.log('\nNext steps:');
  console.log('1. Visit /v2/admin/content-sync to see the monitoring dashboard');
  console.log('2. Trigger a manual sync to populate initial data');
  console.log('3. Check /v2/pulse to see the articles once synced');
}

runChecks().catch(console.error);