#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const supabaseUrl = process.env.VITE_APP_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_APP_SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔧 Creating missing evaluation views...\n');

async function executeSQLFile() {
  try {
    // Read the SQL file
    const sqlPath = path.join(__dirname, 'create-missing-evaluation-views.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');
    
    console.log('📄 Executing SQL to create views...');
    
    // Execute the SQL
    const { data, error } = await supabase.rpc('exec_sql', { 
      sql_query: sqlContent 
    }).catch(async (err) => {
      // If exec_sql doesn't exist, try running statements individually
      console.log('⚠️  exec_sql not available, executing statements individually...');
      
      const statements = sqlContent
        .split(';')
        .filter(stmt => stmt.trim().length > 0)
        .map(stmt => stmt.trim() + ';');
      
      for (const statement of statements) {
        if (statement.includes('--') && !statement.includes('CREATE') && !statement.includes('DROP')) {
          continue; // Skip comment-only lines
        }
        
        console.log(`\nExecuting: ${statement.substring(0, 50)}...`);
        
        // For CREATE VIEW statements, we need to use a different approach
        const { error: stmtError } = await supabase.from('_dummy_').select('*').limit(0);
        // This is a hack - we can't directly execute DDL through Supabase client
        
        console.log('❌ Cannot execute DDL statements through Supabase client.');
        console.log('Please run the following SQL directly in your Supabase SQL editor:');
        console.log('\n' + '='.repeat(60));
        console.log(sqlContent);
        console.log('='.repeat(60) + '\n');
        return false;
      }
    });
    
    if (error) {
      console.error('❌ Error creating views:', error.message);
      return false;
    }
    
    console.log('✅ Views created successfully!');
    return true;
    
  } catch (error) {
    console.error('❌ Exception:', error.message);
    return false;
  }
}

async function testViews() {
  console.log('\n🧪 Testing the created views...');
  
  const MARK_USER_ID = '57a84e11-58e1-4e53-aebb-05d61b2c89dd';
  
  // Test player_evaluation_summary_by_event
  try {
    const { data, error, count } = await supabase
      .from('player_evaluation_summary_by_event')
      .select('*', { count: 'exact' })
      .eq('player_id', MARK_USER_ID)
      .limit(3);
    
    if (error) {
      console.error('❌ Error testing player_evaluation_summary_by_event:', error.message);
    } else {
      console.log(`✅ player_evaluation_summary_by_event works! Found ${count || 0} records for Mark`);
      if (data && data.length > 0) {
        console.log('   Sample record:', {
          event_name: data[0].event_name,
          event_date: data[0].event_date,
          avg_coach_rating: data[0].avg_coach_rating,
          evaluation_count: data[0].evaluation_count
        });
      }
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
  
  // Test player_evaluation_history_view
  try {
    const { data, error } = await supabase
      .from('player_evaluation_history_view')
      .select('*')
      .eq('player_id', MARK_USER_ID)
      .single();
    
    if (error) {
      console.error('❌ Error testing player_evaluation_history_view:', error.message);
    } else {
      console.log('✅ player_evaluation_history_view works!');
      if (data) {
        console.log('   Mark\'s stats:', {
          total_evaluations: data.total_evaluations,
          total_events: data.total_events,
          overall_avg_coach_rating: data.overall_avg_coach_rating
        });
      }
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

async function runFix() {
  const success = await executeSQLFile();
  
  if (!success) {
    console.log('\n📋 Alternative: Copy the SQL above and run it in your Supabase dashboard:');
    console.log('1. Go to your Supabase project dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Paste the SQL and click "Run"');
    console.log('4. Then run: node scripts/fix-mark-evaluation-views.js --test-only');
    
    // Check if we should only test
    if (process.argv.includes('--test-only')) {
      await testViews();
    }
  } else {
    await testViews();
  }
  
  console.log('\n✅ Done! The evaluation views should now be working for Mark.');
}

runFix().catch(console.error);