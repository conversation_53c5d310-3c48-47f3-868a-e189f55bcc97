#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

// Supabase configuration from CLAUDE.md
const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkEvaluationViews() {
  console.log('Checking production database for evaluation-related views...\n');

  try {
    // First, let's check known view names from the SQL files
    const knownViews = [
      'event_evaluation_summary',
      'player_evaluation_summary_by_event', 
      'player_evaluation_history_view',
      'event_evaluation_with_pre',
      'coach_evaluation_overview',
      'pre_evaluation_summary',
      'evaluation_with_pre_eval',
      'player_pre_evaluation_view'
    ];

    console.log('Checking for known evaluation views:');
    for (const viewName of knownViews) {
      try {
        const { data, error } = await supabase
          .from(viewName)
          .select('*')
          .limit(1);

        if (!error) {
          console.log(`✅ ${viewName} - EXISTS`);
          
          // Try to get column info
          if (data && data.length > 0) {
            const columns = Object.keys(data[0]);
            console.log(`   Columns: ${columns.slice(0, 5).join(', ')}${columns.length > 5 ? '...' : ''}`);
          }
        } else {
          console.log(`❌ ${viewName} - NOT FOUND`);
        }
      } catch (e) {
        console.log(`❌ ${viewName} - ERROR`);
      }
    }

    // Check for pre_evaluations table structure
    console.log('\n\nChecking pre_evaluations table structure:');
    const { data: preEvalSample, error: preEvalError } = await supabase
      .from('pre_evaluations')
      .select('*')
      .limit(1);

    if (!preEvalError && preEvalSample) {
      console.log('✅ pre_evaluations table exists');
      if (preEvalSample.length > 0) {
        console.log('   Sample columns:', Object.keys(preEvalSample[0]).join(', '));
      }
    } else {
      console.log('❌ pre_evaluations table not found');
    }

    // Check for pre_evaluation_responses table
    console.log('\nChecking pre_evaluation_responses table:');
    const { data: preEvalRespSample, error: preEvalRespError } = await supabase
      .from('pre_evaluation_responses') 
      .select('*')
      .limit(1);

    if (!preEvalRespError && preEvalRespSample) {
      console.log('✅ pre_evaluation_responses table exists');
      if (preEvalRespSample.length > 0) {
        console.log('   Sample columns:', Object.keys(preEvalRespSample[0]).join(', '));
      }
    } else {
      console.log('❌ pre_evaluation_responses table not found');
    }

    // Check if there's a combined view joining evaluations and pre-evaluations
    console.log('\n\nLooking for views that combine evaluations and pre-evaluations:');
    
    // Try to query player_evaluations with pre-evaluation data
    const { data: evalData, error: evalError } = await supabase
      .from('player_evaluations')
      .select(`
        *,
        pre_evaluations:event_id (
          id,
          player_id,
          status
        )
      `)
      .limit(1);

    if (!evalError) {
      console.log('✅ Can join player_evaluations with pre_evaluations');
    } else {
      console.log('❌ Cannot directly join evaluations with pre-evaluations');
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the check
checkEvaluationViews();