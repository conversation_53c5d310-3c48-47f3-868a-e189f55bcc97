// ABOUTME: Quick script to check teams and clubs table schema
// This helps us write correct SQL for the view

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkSchema() {
  // Check teams table
  const { data: team, error: teamError } = await supabase
    .from('teams')
    .select('*')
    .limit(1);
  
  if (!teamError && team && team.length > 0) {
    console.log('Teams table columns:');
    console.log(Object.keys(team[0]).join(', '));
  }
  
  // Check clubs table  
  const { data: club, error: clubError } = await supabase
    .from('clubs')
    .select('*')
    .limit(1);
    
  if (!clubError && club && club.length > 0) {
    console.log('\nClubs table columns:');
    console.log(Object.keys(club[0]).join(', '));
  }
}

checkSchema();