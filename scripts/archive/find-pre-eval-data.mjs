#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function findPreEvalData() {
  console.log('Finding pre-evaluation data with actual responses...\n');

  try {
    // First, let's find pre-evaluations that have actual response data
    const { data: preEvals, error } = await supabase
      .from('pre_evaluations')
      .select('*')
      .eq('status', 'completed')
      .not('responses', 'is', null)
      .order('completed_at', { ascending: false })
      .limit(20);

    if (error) {
      console.error('Error:', error);
      return;
    }

    console.log(`Found ${preEvals?.length || 0} completed pre-evaluations\n`);

    // Group by response structure type
    const responseTypes = new Map();
    
    preEvals?.forEach(pe => {
      const structureKey = JSON.stringify(Object.keys(pe.responses).sort());
      if (!responseTypes.has(structureKey)) {
        responseTypes.set(structureKey, []);
      }
      responseTypes.get(structureKey).push(pe);
    });

    console.log(`Found ${responseTypes.size} different response structures:\n`);

    let validDataFound = false;

    for (const [structure, evals] of responseTypes) {
      console.log(`\nStructure type: ${structure}`);
      console.log(`Count: ${evals.length}`);
      
      // Check the first one in detail
      const sample = evals[0];
      console.log('\nSample pre-evaluation:');
      console.log(`- ID: ${sample.id}`);
      console.log(`- Player ID: ${sample.player_id}`);
      console.log(`- Event ID: ${sample.event_id}`);
      console.log(`- Team ID: ${sample.team_id}`);
      console.log(`- Completed: ${sample.completed_at ? new Date(sample.completed_at).toLocaleString() : 'Not set'}`);
      console.log(`- Method: ${sample.request_method}`);
      console.log(`- Questions answered: ${sample.questions_answered}/${sample.total_questions}`);
      
      console.log('\n- Response data:');
      console.log(JSON.stringify(sample.responses, null, 2));
      
      // Check if this has actual evaluation data
      if (sample.responses.questions && Array.isArray(sample.responses.questions) && sample.responses.questions.length > 0) {
        validDataFound = true;
        console.log('\n✅ This has valid evaluation data!');
        
        // Extract ratings by category
        const categoryRatings = {};
        sample.responses.questions.forEach(q => {
          if (q.category && (q.rating || q.answer)) {
            const category = q.category.toLowerCase();
            if (!categoryRatings[category]) {
              categoryRatings[category] = [];
            }
            categoryRatings[category].push({
              question: q.question,
              rating: q.rating || q.answer,
              area: q.area
            });
          }
        });
        
        console.log('\nRatings by category:');
        Object.entries(categoryRatings).forEach(([cat, ratings]) => {
          console.log(`\n${cat.toUpperCase()}:`);
          ratings.forEach(r => {
            console.log(`  - ${r.question}: ${r.rating}`);
          });
        });
      }
    }

    if (!validDataFound) {
      console.log('\n❌ No pre-evaluations found with valid response data');
    }

    // Look for today's data specifically
    const today = new Date().toISOString().split('T')[0];
    console.log(`\n\n🔥 Checking for today's pre-evaluations (${today}):`);
    
    const { data: todayData, error: todayError } = await supabase
      .from('pre_evaluations')
      .select('*')
      .gte('completed_at', today)
      .eq('status', 'completed')
      .order('completed_at', { ascending: false });

    if (todayData && todayData.length > 0) {
      console.log(`\nFound ${todayData.length} pre-evaluations completed today:`);
      
      todayData.forEach((pe, index) => {
        console.log(`\n${index + 1}. Pre-evaluation ${pe.id}`);
        console.log(`   Completed: ${new Date(pe.completed_at).toLocaleTimeString()}`);
        console.log(`   Method: ${pe.request_method}`);
        console.log(`   Team ID: ${pe.team_id}`);
        
        if (pe.responses && pe.responses.questions && pe.responses.questions.length > 0) {
          console.log(`   ✅ Has ${pe.responses.questions.length} responses`);
          // Show first few questions
          pe.responses.questions.slice(0, 3).forEach(q => {
            console.log(`   - ${q.question}: ${q.rating || q.answer}`);
          });
        } else {
          console.log(`   ❌ Empty responses`);
        }
      });
    } else {
      console.log('No pre-evaluations completed today');
    }

    // Search for specific player names mentioned
    console.log('\n\n🔍 Looking for Mark\'s pre-evaluations:');
    
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('id, full_name')
      .or('full_name.ilike.%mark%,first_name.ilike.%mark%');

    if (profiles && profiles.length > 0) {
      for (const profile of profiles) {
        console.log(`\nChecking ${profile.full_name} (${profile.id}):`);
        
        const { data: markPreEvals } = await supabase
          .from('pre_evaluations')
          .select('*')
          .eq('player_id', profile.id)
          .eq('status', 'completed')
          .order('completed_at', { ascending: false })
          .limit(3);

        if (markPreEvals && markPreEvals.length > 0) {
          markPreEvals.forEach(pe => {
            console.log(`\n- Completed: ${pe.completed_at ? new Date(pe.completed_at).toLocaleString() : 'Not set'}`);
            console.log(`  Method: ${pe.request_method}`);
            console.log(`  Has responses: ${pe.responses && pe.responses.questions ? 'Yes' : 'No'}`);
            
            if (pe.responses && pe.responses.questions && pe.responses.questions.length > 0) {
              console.log(`  Sample responses:`);
              pe.responses.questions.slice(0, 3).forEach(q => {
                console.log(`  - ${q.category}: ${q.rating || q.answer}`);
              });
            }
          });
        } else {
          console.log('  No completed pre-evaluations found');
        }
      }
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the check
findPreEvalData();