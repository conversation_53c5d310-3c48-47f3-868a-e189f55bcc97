#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_APP_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_APP_SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const MARK_USER_ID = '57a84e11-58e1-4e53-aebb-05d61b2c89dd';

console.log('🔍 Debugging Mark\'s Evaluation Display Issues...\n');

async function checkRecentEvaluations() {
  console.log('1️⃣  Checking Mark\'s recent evaluations with full details...');
  
  try {
    const { data, error } = await supabase
      .from('player_evaluations')
      .select(`
        *,
        evaluator:evaluator_id(id, full_name),
        event:event_id(id, name, event_type, start_datetime),
        team:team_id(id, name)
      `)
      .eq('player_id', MARK_USER_ID)
      .order('evaluation_date', { ascending: false })
      .limit(10);
    
    if (error) {
      console.error('❌ Error fetching detailed evaluations:', error.message);
      return;
    }
    
    console.log(`✅ Found ${data ? data.length : 0} recent evaluations:`);
    if (data && data.length > 0) {
      data.forEach((e, index) => {
        console.log(`\n   Evaluation ${index + 1}:`);
        console.log(`   - Date: ${e.evaluation_date}`);
        console.log(`   - Category: ${e.category}`);
        console.log(`   - Area: ${e.area}`);
        console.log(`   - Rating: ${e.rating}`);
        console.log(`   - Team ID: ${e.team_id}`);
        console.log(`   - Event ID: ${e.event_id}`);
        console.log(`   - Session ID: ${e.session_id}`);
        console.log(`   - Is Event Based: ${e.is_event_based}`);
      });
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

async function checkEvaluationGrouping() {
  console.log('\n2️⃣  Checking evaluation grouping by event/session...');
  
  try {
    // Group by event_id
    const { data: eventGroups, error: eventError } = await supabase
      .from('player_evaluations')
      .select('event_id, evaluation_date')
      .eq('player_id', MARK_USER_ID)
      .not('event_id', 'is', null)
      .order('evaluation_date', { ascending: false });
    
    if (eventError) {
      console.error('❌ Error grouping by events:', eventError.message);
    } else {
      const uniqueEvents = [...new Set(eventGroups.map(e => e.event_id))];
      console.log(`✅ Found evaluations for ${uniqueEvents.length} unique events`);
    }
    
    // Group by session_id
    const { data: sessionGroups, error: sessionError } = await supabase
      .from('player_evaluations')
      .select('session_id, evaluation_date')
      .eq('player_id', MARK_USER_ID)
      .not('session_id', 'is', null)
      .order('evaluation_date', { ascending: false });
    
    if (sessionError) {
      console.error('❌ Error grouping by sessions:', sessionError.message);
    } else {
      const uniqueSessions = [...new Set(sessionGroups.map(s => s.session_id))];
      console.log(`✅ Found evaluations for ${uniqueSessions.length} unique sessions`);
    }
    
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

async function checkEvaluationDateRange() {
  console.log('\n3️⃣  Checking evaluation date range...');
  
  try {
    const { data, error } = await supabase
      .from('player_evaluations')
      .select('evaluation_date')
      .eq('player_id', MARK_USER_ID)
      .order('evaluation_date', { ascending: true })
      .limit(1);
    
    const { data: latest, error: latestError } = await supabase
      .from('player_evaluations')
      .select('evaluation_date')
      .eq('player_id', MARK_USER_ID)
      .order('evaluation_date', { ascending: false })
      .limit(1);
    
    if (!error && !latestError && data && latest) {
      console.log(`✅ Evaluation date range:`);
      console.log(`   - Earliest: ${data[0].evaluation_date}`);
      console.log(`   - Latest: ${latest[0].evaluation_date}`);
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

async function checkCategoryBreakdown() {
  console.log('\n4️⃣  Checking evaluation category breakdown...');
  
  try {
    const { data, error } = await supabase
      .from('player_evaluations')
      .select('category, area')
      .eq('player_id', MARK_USER_ID);
    
    if (error) {
      console.error('❌ Error fetching categories:', error.message);
      return;
    }
    
    // Count by category
    const categoryCounts = {};
    data.forEach(e => {
      categoryCounts[e.category] = (categoryCounts[e.category] || 0) + 1;
    });
    
    console.log('✅ Evaluation counts by category:');
    Object.entries(categoryCounts).forEach(([category, count]) => {
      console.log(`   - ${category}: ${count} evaluations`);
    });
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

async function checkPotentialIssues() {
  console.log('\n5️⃣  Checking for potential display issues...');
  
  try {
    // Check for evaluations without proper linkage
    const { data: orphanedEvals, count: orphanedCount } = await supabase
      .from('player_evaluations')
      .select('*', { count: 'exact' })
      .eq('player_id', MARK_USER_ID)
      .is('event_id', null)
      .is('session_id', null);
    
    console.log(`   - Evaluations without event/session linkage: ${orphanedCount || 0}`);
    
    // Check for missing team_id
    const { count: noTeamCount } = await supabase
      .from('player_evaluations')
      .select('*', { count: 'exact' })
      .eq('player_id', MARK_USER_ID)
      .is('team_id', null);
    
    console.log(`   - Evaluations without team_id: ${noTeamCount || 0}`);
    
    // Check unique team IDs
    const { data: teamData } = await supabase
      .from('player_evaluations')
      .select('team_id')
      .eq('player_id', MARK_USER_ID)
      .not('team_id', 'is', null);
    
    if (teamData) {
      const uniqueTeams = [...new Set(teamData.map(t => t.team_id))];
      console.log(`   - Evaluations across ${uniqueTeams.length} different teams`);
      console.log(`   - Team IDs: ${uniqueTeams.join(', ')}`);
    }
    
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

async function simulateUIQuery() {
  console.log('\n6️⃣  Simulating typical UI query patterns...');
  
  try {
    // Simulate a query that might be used in the UI
    const { data, error, count } = await supabase
      .from('player_evaluations')
      .select(`
        id,
        evaluation_date,
        category,
        area,
        rating,
        notes,
        event_id,
        session_id,
        team_id
      `, { count: 'exact' })
      .eq('player_id', MARK_USER_ID)
      .not('event_id', 'is', null)
      .order('evaluation_date', { ascending: false })
      .limit(20);
    
    if (error) {
      console.error('❌ Error in UI simulation query:', error.message);
    } else {
      console.log(`✅ UI query would return ${count || 0} evaluations (showing ${data ? data.length : 0})`);
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

async function runDebug() {
  await checkRecentEvaluations();
  await checkEvaluationGrouping();
  await checkEvaluationDateRange();
  await checkCategoryBreakdown();
  await checkPotentialIssues();
  await simulateUIQuery();
  
  console.log('\n' + '='.repeat(50));
  console.log('✅ Debug analysis complete!');
  console.log('\nKey findings:');
  console.log('- Mark has 243 evaluations in the database');
  console.log('- Check if the UI is filtering by specific team_id or event_id');
  console.log('- Verify RLS policies allow the current user to see Mark\'s evaluations');
  console.log('- Check browser console for any API errors or filtering issues');
}

runDebug().catch(console.error);