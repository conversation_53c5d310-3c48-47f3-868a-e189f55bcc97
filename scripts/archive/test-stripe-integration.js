// ABOUTME: Test script to verify Stripe integration is working
// This creates a test checkout to verify the edge function can create payment intents

const SUPABASE_URL = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mjg1NTExMjAsImV4cCI6MjA0NDEyNzEyMH0.fUqHSW87vqKLBt2c4l-JHoR-smcnSGGI8PEATd8nnrU';

async function testStripeIntegration() {
  console.log('🔍 Testing Stripe Integration...\n');
  
  try {
    // First, we need to sign in to get a user token
    console.log('1. Signing in with test credentials...');
    const signInResponse = await fetch(`${SUPABASE_URL}/auth/v1/token?grant_type=password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': SUPABASE_ANON_KEY
      },
      body: JSON.stringify({
        email: '<EMAIL>', // You'll need to use your actual email
        password: 'your-password'   // You'll need to use your actual password
      })
    });

    if (!signInResponse.ok) {
      console.log('\n❌ Unable to sign in. Please update the script with your actual email and password.');
      console.log('\nTo test manually:');
      console.log('1. Go to your app at http://localhost:5160');
      console.log('2. Sign in with your account');
      console.log('3. Add a product to cart');
      console.log('4. Go through checkout');
      console.log('5. Look for the payment form on the payment step');
      return;
    }

    const { access_token } = await signInResponse.json();
    console.log('✅ Signed in successfully\n');

    // Test checkout data
    const testCheckoutData = {
      items: [{
        id: 'test-item-1',
        productId: 123,
        name: 'Test Product',
        price: 49.99,
        quantity: 1
      }],
      shippingAddress: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '+**********',
        address1: '123 Test Street',
        city: 'New York',
        state: 'NY',
        postalCode: '10001',
        country: 'US'
      },
      cartSessionId: `test-${Date.now()}`
    };

    console.log('2. Calling checkout edge function...');
    const response = await fetch(`${SUPABASE_URL}/functions/v1/locker-checkout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${access_token}`,
      },
      body: JSON.stringify(testCheckoutData)
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('\n✅ Stripe Integration is Working!\n');
      console.log('Response details:');
      console.log('- Order ID:', data.orderId);
      console.log('- Amount:', `$${data.amount}`);
      console.log('- Status:', data.status);
      
      if (data.clientSecret) {
        console.log('- Payment Intent Created:', '✓');
        console.log('- Client Secret:', data.clientSecret.substring(0, 20) + '...');
        console.log('\n🎉 Stripe is fully configured and working!');
      } else {
        console.log('\n⚠️  Stripe keys might not be configured in Supabase');
        console.log('Message:', data.message);
      }
    } else {
      console.log('\n❌ Error:', data.error || data.message);
      if (data.error?.includes('STRIPE_SECRET_KEY')) {
        console.log('\n💡 The Stripe secret key is not set in Supabase.');
        console.log('Run: supabase secrets set STRIPE_SECRET_KEY=sk_test_YOUR_KEY');
      }
    }
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
  }
}

console.log('Stripe Integration Test');
console.log('======================\n');
console.log('This script will verify that your Stripe integration is properly configured.\n');

testStripeIntegration();