#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkPreEvalStructure() {
  console.log('Checking pre-evaluation response structure...\n');

  try {
    // Get a sample pre-evaluation with responses
    const { data: preEvals, error } = await supabase
      .from('pre_evaluations')
      .select('id, event_id, player_id, status, responses, confidence_level, mood_rating')
      .not('responses', 'is', null)
      .eq('status', 'completed')
      .limit(3);

    if (error) {
      console.error('Error fetching pre-evaluations:', error);
      return;
    }

    if (!preEvals || preEvals.length === 0) {
      console.log('No completed pre-evaluations found with responses');
      
      // Try to get any pre-evaluation
      const { data: anyPreEval, error: anyError } = await supabase
        .from('pre_evaluations')
        .select('id, event_id, player_id, status, responses')
        .limit(5);
        
      if (anyPreEval && anyPreEval.length > 0) {
        console.log('\nFound pre-evaluations with these statuses:');
        anyPreEval.forEach(pe => {
          console.log(`- ID: ${pe.id}, Status: ${pe.status}, Has responses: ${pe.responses ? 'Yes' : 'No'}`);
        });
      }
      return;
    }

    console.log(`Found ${preEvals.length} completed pre-evaluations with responses:\n`);

    preEvals.forEach((preEval, index) => {
      console.log(`\nPre-Evaluation ${index + 1}:`);
      console.log(`- ID: ${preEval.id}`);
      console.log(`- Event ID: ${preEval.event_id}`);
      console.log(`- Player ID: ${preEval.player_id}`);
      console.log(`- Status: ${preEval.status}`);
      console.log(`- Confidence Level: ${preEval.confidence_level}`);
      console.log(`- Mood Rating: ${preEval.mood_rating}`);
      
      if (preEval.responses) {
        console.log(`- Responses structure:`);
        console.log(JSON.stringify(preEval.responses, null, 2));
      }
    });

    // Also check the evaluation criteria to understand the expected structure
    console.log('\n\nChecking evaluation criteria structure:');
    const { data: criteria, error: criteriaError } = await supabase
      .from('evaluation_criteria')
      .select('category, area, question_pre, answers_pre')
      .not('question_pre', 'is', null)
      .limit(3);

    if (criteria && criteria.length > 0) {
      console.log('\nSample evaluation criteria with pre-evaluation questions:');
      criteria.forEach(c => {
        console.log(`\n- Category: ${c.category}, Area: ${c.area}`);
        console.log(`  Question: ${c.question_pre}`);
        if (c.answers_pre) {
          console.log(`  Answer options: ${JSON.stringify(c.answers_pre)}`);
        }
      });
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the check
checkPreEvalStructure();