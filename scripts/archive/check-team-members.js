// Script to check team members data
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkTeamMembers() {
  const teamId = '44ca42bc-e566-44c9-9bdb-4c8cf599a241';
  
  console.log('Checking team:', teamId);
  
  // Check if team exists
  const { data: team, error: teamError } = await supabase
    .from('teams')
    .select('*')
    .eq('team_id', teamId)
    .single();
    
  console.log('Team data:', team);
  
  // Check team members
  const { data: members, error: membersError } = await supabase
    .from('team_members')
    .select('*')
    .eq('team_id', teamId);
    
  console.log('Team members count:', members?.length || 0);
  console.log('Team members:', members);
  
  // Check total team members in system
  const { count } = await supabase
    .from('team_members')
    .select('*', { count: 'exact', head: true });
    
  console.log('Total team members in system:', count);
  
  // Get a sample of team members
  const { data: sampleMembers } = await supabase
    .from('team_members')
    .select('*, profiles(full_name)')
    .limit(5);
    
  console.log('Sample team members:', sampleMembers);
}

checkTeamMembers().catch(console.error);