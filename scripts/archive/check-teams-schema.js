import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkSchema() {
  // Check teams table columns
  const { data: columns, error } = await supabase
    .from('teams')
    .select('*')
    .limit(0);
    
  if (error) {
    console.error('Error:', error);
    return;
  }
  
  // Get one row to see the actual structure
  const { data: sample, error: sampleError } = await supabase
    .from('teams')
    .select('*')
    .limit(1);
    
  if (!sampleError && sample && sample.length > 0) {
    console.log('Teams table columns:', Object.keys(sample[0]));
  }
  
  // Check clubs table too
  const { data: clubSample, error: clubError } = await supabase
    .from('clubs')
    .select('*')
    .limit(1);
    
  if (!clubError && clubSample && clubSample.length > 0) {
    console.log('\nClubs table columns:', Object.keys(clubSample[0]));
  }
}

checkSchema();