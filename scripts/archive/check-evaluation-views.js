#!/usr/bin/env node

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration from CLAUDE.md
const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkEvaluationViews() {
  console.log('Checking production database for evaluation-related views...\n');

  try {
    // Query to find all views in the public schema
    const { data: views, error: viewsError } = await supabase
      .rpc('get_schema_views', {}, { count: 'exact' })
      .catch(() => ({ data: null, error: 'RPC not available' }));

    // If RPC doesn't exist, use direct query
    if (!views || viewsError) {
      console.log('Using direct query to information_schema...');
      
      const query = `
        SELECT 
          table_name as view_name,
          view_definition
        FROM information_schema.views
        WHERE table_schema = 'public'
          AND (
            table_name LIKE '%evaluation%' 
            OR table_name LIKE '%pre_eval%'
            OR table_name LIKE '%assessment%'
          )
        ORDER BY table_name;
      `;

      const { data, error } = await supabase.rpc('execute_sql', { query_text: query })
        .catch(() => ({ data: null, error: 'Direct SQL not available' }));

      if (!data || error) {
        // Try another approach - query pg_views directly
        const { data: pgViews, error: pgError } = await supabase
          .from('pg_views')
          .select('viewname, definition')
          .eq('schemaname', 'public')
          .or('viewname.ilike.%evaluation%,viewname.ilike.%pre_eval%,viewname.ilike.%assessment%');

        if (pgError) {
          console.log('Trying raw SQL query...');
          // Use a different approach - check specific known view names
          const knownViews = [
            'event_evaluation_summary',
            'player_evaluation_summary_by_event',
            'player_evaluation_history_view',
            'event_evaluation_with_pre',
            'coach_evaluation_overview',
            'pre_evaluation_summary'
          ];

          for (const viewName of knownViews) {
            try {
              const { data: testData, error: testError } = await supabase
                .from(viewName)
                .select('*')
                .limit(0);

              if (!testError) {
                console.log(`✅ View exists: ${viewName}`);
              }
            } catch (e) {
              // View doesn't exist
            }
          }
        } else if (pgViews && pgViews.length > 0) {
          console.log(`Found ${pgViews.length} evaluation-related views:\n`);
          pgViews.forEach(view => {
            console.log(`📊 ${view.viewname}`);
            if (view.definition) {
              console.log(`   Definition preview: ${view.definition.substring(0, 200)}...`);
            }
            console.log('');
          });
        }
      } else if (data && data.length > 0) {
        console.log(`Found ${data.length} evaluation-related views:\n`);
        data.forEach(view => {
          console.log(`📊 ${view.view_name}`);
          console.log(`   Definition preview: ${view.view_definition.substring(0, 200)}...`);
          console.log('');
        });
      }
    }

    // Check for pre_evaluations table
    console.log('\nChecking for pre_evaluations table...');
    const { data: preEvalData, error: preEvalError } = await supabase
      .from('pre_evaluations')
      .select('*')
      .limit(1);

    if (!preEvalError) {
      console.log('✅ pre_evaluations table exists');
      
      // Get column information
      const { data: columns, error: colError } = await supabase
        .rpc('get_table_columns', { table_name: 'pre_evaluations' })
        .catch(() => ({ data: null, error: 'RPC not available' }));

      if (!columns || colError) {
        // Try to infer columns from a sample
        if (preEvalData && preEvalData.length > 0) {
          console.log('   Columns:', Object.keys(preEvalData[0]).join(', '));
        }
      } else {
        console.log('   Columns:', columns.map(c => c.column_name).join(', '));
      }
    } else {
      console.log('❌ pre_evaluations table not found or not accessible');
    }

    // Check for pre_evaluation_responses table
    console.log('\nChecking for pre_evaluation_responses table...');
    const { data: preEvalRespData, error: preEvalRespError } = await supabase
      .from('pre_evaluation_responses')
      .select('*')
      .limit(1);

    if (!preEvalRespError) {
      console.log('✅ pre_evaluation_responses table exists');
      if (preEvalRespData && preEvalRespData.length > 0) {
        console.log('   Columns:', Object.keys(preEvalRespData[0]).join(', '));
      }
    } else {
      console.log('❌ pre_evaluation_responses table not found or not accessible');
    }

  } catch (error) {
    console.error('Error checking views:', error);
  }
}

// Run the check
checkEvaluationViews();