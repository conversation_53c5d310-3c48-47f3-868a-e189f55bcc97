#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_APP_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_APP_SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.VITE_APP_SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

// Create both service and anon clients to test permissions
const supabaseService = createClient(supabaseUrl, supabaseServiceKey);
const supabaseAnon = createClient(supabaseUrl, supabaseAnonKey);

const MARK_USER_ID = '57a84e11-58e1-4e53-aebb-05d61b2c89dd';

console.log('🔍 Complete Evaluation Display Debugging for Mark\n');

async function testWithServiceRole() {
  console.log('1️⃣  Testing with Service Role (full permissions)...\n');
  
  try {
    // Test player_evaluation_summary_by_event
    const { data: summaries, error: summaryError, count } = await supabaseService
      .from('player_evaluation_summary_by_event')
      .select('*', { count: 'exact' })
      .eq('player_id', MARK_USER_ID)
      .order('event_date', { ascending: false })
      .limit(3);
    
    if (summaryError) {
      console.error('❌ Service Role - Summary view error:', summaryError);
    } else {
      console.log(`✅ Service Role - Summary view: ${count} total records`);
      if (summaries && summaries.length > 0) {
        console.log('\n   Latest evaluation:');
        const latest = summaries[0];
        console.log(`   - Event: ${latest.event_name}`);
        console.log(`   - Date: ${latest.event_date}`);
        console.log(`   - Team: ${latest.team_name}`);
        console.log(`   - Evaluations: ${latest.evaluation_count}`);
        console.log(`   - Coach Avg: ${latest.avg_coach_rating}`);
        console.log(`   - Self Avg: ${latest.avg_self_rating}`);
        console.log(`   - Categories:`, JSON.stringify(latest.category_breakdown, null, 2));
      }
    }
    
    // Test player_evaluation_history_view
    const { data: history, error: historyError } = await supabaseService
      .from('player_evaluation_history_view')
      .select('*')
      .eq('player_id', MARK_USER_ID);
    
    if (historyError) {
      console.error('\n❌ Service Role - History view error:', historyError);
    } else if (history && history.length > 0) {
      console.log('\n✅ Service Role - History view:');
      const stats = history[0];
      console.log(`   - Total evaluations: ${stats.total_evaluations}`);
      console.log(`   - Total events: ${stats.total_events}`);
      console.log(`   - Overall coach avg: ${stats.overall_avg_coach_rating}`);
      console.log(`   - Overall self avg: ${stats.overall_avg_self_rating}`);
      console.log(`   - Date range: ${stats.first_evaluation_date} to ${stats.latest_evaluation_date}`);
    }
    
  } catch (e) {
    console.error('❌ Service Role Exception:', e.message);
  }
}

async function testWithAnonKey() {
  console.log('\n\n2️⃣  Testing with Anon Key (simulating browser access)...\n');
  
  try {
    // Test player_evaluation_summary_by_event
    const { data: summaries, error: summaryError, count } = await supabaseAnon
      .from('player_evaluation_summary_by_event')
      .select('*', { count: 'exact' })
      .eq('player_id', MARK_USER_ID)
      .limit(1);
    
    if (summaryError) {
      console.error('❌ Anon Key - Summary view error:', summaryError);
      console.log('   This suggests RLS policies are blocking access');
    } else {
      console.log(`✅ Anon Key - Summary view accessible: ${count} records`);
    }
    
    // Test player_evaluation_history_view
    const { data: history, error: historyError } = await supabaseAnon
      .from('player_evaluation_history_view')
      .select('*')
      .eq('player_id', MARK_USER_ID);
    
    if (historyError) {
      console.error('\n❌ Anon Key - History view error:', historyError);
      console.log('   This suggests RLS policies are blocking access');
    } else {
      console.log('\n✅ Anon Key - History view accessible');
    }
    
  } catch (e) {
    console.error('❌ Anon Key Exception:', e.message);
  }
}

async function checkRLSPolicies() {
  console.log('\n\n3️⃣  Checking RLS policies on base tables...\n');
  
  try {
    // Check if anon can access player_evaluations directly
    const { data, error, count } = await supabaseAnon
      .from('player_evaluations')
      .select('*', { count: 'exact' })
      .eq('player_id', MARK_USER_ID)
      .limit(1);
    
    if (error) {
      console.error('❌ Cannot access player_evaluations with anon key:', error.message);
      console.log('   Views inherit RLS from base tables - this is likely the issue!');
    } else {
      console.log(`✅ Can access player_evaluations with anon key: ${count} records`);
    }
    
  } catch (e) {
    console.error('❌ RLS Check Exception:', e.message);
  }
}

async function simulateComponentQuery() {
  console.log('\n\n4️⃣  Simulating exact component queries...\n');
  
  // This simulates what PlayerEvaluationHistory.tsx does
  try {
    console.log('Simulating line 62-72 from PlayerEvaluationHistory.tsx:');
    const { data: summaries, error: summaryError } = await supabaseAnon
      .from('player_evaluation_summary_by_event')
      .select('*')
      .eq('player_id', MARK_USER_ID)
      .order('event_date', { ascending: false });
    
    if (summaryError) {
      console.error('❌ Component query would fail:', summaryError);
      console.log('\n   💡 Solution: The user viewing the page needs to be authenticated');
      console.log('   💡 Or: Mark\'s evaluations need proper team/coach associations');
    } else {
      console.log(`✅ Component query would succeed: ${summaries ? summaries.length : 0} records`);
    }
    
  } catch (e) {
    console.error('❌ Component Simulation Exception:', e.message);
  }
}

async function checkUserContext() {
  console.log('\n\n5️⃣  Checking who can see Mark\'s data...\n');
  
  try {
    // Get Mark's teams
    const { data: teams } = await supabaseService
      .from('team_players')
      .select('team_id, teams!inner(name)')
      .eq('user_id', MARK_USER_ID);
    
    if (teams && teams.length > 0) {
      console.log(`Mark is in ${teams.length} team(s):`);
      teams.forEach(t => {
        console.log(`   - Team: ${t.teams?.name || 'Unknown'} (${t.team_id})`);
      });
      
      // Check who are the coaches
      for (const team of teams) {
        const { data: coaches } = await supabaseService
          .from('team_coaches')
          .select('user_id, profiles!inner(full_name)')
          .eq('team_id', team.team_id);
        
        if (coaches && coaches.length > 0) {
          console.log(`\n   Coaches for team ${team.teams?.name}:`);
          coaches.forEach(c => {
            console.log(`     - ${c.profiles?.full_name || 'Unknown'} (${c.user_id})`);
          });
        }
      }
    } else {
      console.log('❌ Mark is not associated with any teams!');
    }
    
  } catch (e) {
    console.error('❌ User Context Exception:', e.message);
  }
}

async function generateSolution() {
  console.log('\n\n' + '='.repeat(60));
  console.log('📋 SUMMARY & SOLUTIONS');
  console.log('='.repeat(60));
  
  console.log('\n✅ What we know:');
  console.log('- Mark has 243 evaluations in the database');
  console.log('- Mark has been evaluated in 52 different events');
  console.log('- The evaluation views exist and work with service role');
  
  console.log('\n❓ Potential issues:');
  console.log('1. RLS policies might be blocking access for regular users');
  console.log('2. The user viewing the page might not have permission to see Mark\'s data');
  console.log('3. The component might be using wrong authentication');
  
  console.log('\n🔧 Solutions to try:');
  console.log('1. Make sure the user is logged in when viewing the evaluation page');
  console.log('2. Check if the logged-in user is a coach for Mark\'s team');
  console.log('3. Update RLS policies to allow players to see their own evaluations');
  console.log('4. Check browser console for specific error messages');
  
  console.log('\n📝 Next steps:');
  console.log('1. Open browser developer tools (F12)');
  console.log('2. Go to the Network tab');
  console.log('3. Navigate to Mark\'s evaluation page');
  console.log('4. Look for failed requests to "player_evaluation_summary_by_event"');
  console.log('5. Check the response for specific error messages');
}

async function runCompleteDebug() {
  await testWithServiceRole();
  await testWithAnonKey();
  await checkRLSPolicies();
  await simulateComponentQuery();
  await checkUserContext();
  await generateSolution();
}

runCompleteDebug().catch(console.error);