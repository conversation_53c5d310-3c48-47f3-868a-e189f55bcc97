#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_APP_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_APP_SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🔍 Testing Mark\'s Evaluation Data...\n');

async function findMarkUser() {
  console.log('1️⃣  Searching for Mark in profiles...');
  
  try {
    // Search for users with "Mark" in their name
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('id, full_name, username')
      .or('full_name.ilike.%mark%,username.ilike.%mark%');
    
    if (error) {
      console.error('❌ Error searching profiles:', error.message);
      return null;
    }
    
    if (!profiles || profiles.length === 0) {
      console.log('⚠️  No users found with "Mark" in their name');
      return null;
    }
    
    console.log(`✅ Found ${profiles.length} user(s) with "Mark" in their name:`);
    profiles.forEach(p => {
      console.log(`   - ${p.full_name || 'No name'} (${p.username || 'No username'}) - ID: ${p.id}`);
    });
    
    return profiles;
  } catch (e) {
    console.error('❌ Exception:', e.message);
    return null;
  }
}

async function checkEvaluations(userId, userName) {
  console.log(`\n2️⃣  Checking evaluations for ${userName} (${userId})...`);
  
  // Check player_evaluations table
  try {
    const { data, error, count } = await supabase
      .from('player_evaluations')
      .select('*', { count: 'exact' })
      .eq('player_id', userId)
      .order('evaluation_date', { ascending: false })
      .limit(5);
    
    if (error) {
      console.error('❌ Error fetching evaluations:', error.message);
    } else {
      console.log(`✅ Found ${count || 0} evaluations in player_evaluations table`);
      if (data && data.length > 0) {
        console.log('   Recent evaluations:');
        data.forEach(e => {
          console.log(`   - ${e.evaluation_date}: ${e.category} - ${e.area} (Rating: ${e.rating})`);
        });
      }
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

async function checkEventParticipation(userId, userName) {
  console.log(`\n3️⃣  Checking event participation for ${userName}...`);
  
  try {
    const { data, error, count } = await supabase
      .from('event_participants')
      .select(`
        *,
        events!inner(
          id,
          name,
          event_type,
          start_datetime
        )
      `, { count: 'exact' })
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10);
    
    if (error) {
      console.error('❌ Error fetching event participation:', error.message);
    } else {
      console.log(`✅ Found ${count || 0} event participations`);
      if (data && data.length > 0) {
        console.log('   Recent events:');
        data.forEach(ep => {
          console.log(`   - ${ep.events.name} (${ep.events.event_type}) - Status: ${ep.invitation_status}`);
        });
      }
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

async function checkEvaluationViews(userId, userName) {
  console.log(`\n4️⃣  Checking evaluation views for ${userName}...`);
  
  // Check event_evaluation_summary view
  try {
    const { data, error } = await supabase
      .from('event_evaluation_summary')
      .select('*')
      .eq('evaluated_participants', userId)
      .limit(5);
    
    if (error) {
      console.error('❌ Error accessing event_evaluation_summary:', error.message);
    } else {
      console.log(`✅ event_evaluation_summary accessible`);
      if (data && data.length > 0) {
        console.log(`   Found ${data.length} events with evaluations`);
      }
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

async function checkTeamMembership(userId, userName) {
  console.log(`\n5️⃣  Checking team membership for ${userName}...`);
  
  try {
    const { data, error } = await supabase
      .from('team_players')
      .select(`
        *,
        teams!inner(
          id,
          name,
          club_id
        )
      `)
      .eq('user_id', userId);
    
    if (error) {
      console.error('❌ Error fetching team membership:', error.message);
    } else {
      console.log(`✅ Found ${data ? data.length : 0} team memberships`);
      if (data && data.length > 0) {
        console.log('   Teams:');
        data.forEach(tm => {
          console.log(`   - ${tm.teams.name} (Team ID: ${tm.team_id})`);
        });
      }
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

async function checkEvaluationSessions(userId, userName) {
  console.log(`\n6️⃣  Checking evaluation sessions for ${userName}...`);
  
  try {
    // First get teams for the user
    const { data: teams } = await supabase
      .from('team_players')
      .select('team_id')
      .eq('user_id', userId);
    
    if (teams && teams.length > 0) {
      const teamIds = teams.map(t => t.team_id);
      
      const { data, error } = await supabase
        .from('evaluation_sessions')
        .select('*')
        .in('team_id', teamIds)
        .order('session_date', { ascending: false })
        .limit(10);
      
      if (error) {
        console.error('❌ Error fetching evaluation sessions:', error.message);
      } else {
        console.log(`✅ Found ${data ? data.length : 0} evaluation sessions for user's teams`);
        if (data && data.length > 0) {
          console.log('   Recent sessions:');
          data.forEach(s => {
            console.log(`   - ${s.session_date}: ${s.session_type} (Completed: ${s.is_completed})`);
          });
        }
      }
    }
  } catch (e) {
    console.error('❌ Exception:', e.message);
  }
}

async function runTests() {
  // Find Mark users
  const markUsers = await findMarkUser();
  
  if (!markUsers || markUsers.length === 0) {
    console.log('\n❌ Cannot proceed without finding Mark user');
    return;
  }
  
  // Check evaluations for each Mark user found
  for (const user of markUsers) {
    console.log('\n' + '='.repeat(50));
    console.log(`Checking data for: ${user.full_name || user.username} (${user.id})`);
    console.log('='.repeat(50));
    
    await checkEvaluations(user.id, user.full_name || user.username);
    await checkEventParticipation(user.id, user.full_name || user.username);
    await checkEvaluationViews(user.id, user.full_name || user.username);
    await checkTeamMembership(user.id, user.full_name || user.username);
    await checkEvaluationSessions(user.id, user.full_name || user.username);
  }
  
  // Summary
  console.log('\n' + '='.repeat(50));
  console.log('✅ Evaluation data check complete!');
  console.log('\nNext steps:');
  console.log('1. If no evaluations found, check if Mark has been evaluated in any events');
  console.log('2. Verify Mark is marked as "attended" in event_participants');
  console.log('3. Check if evaluation data exists but RLS policies are blocking access');
  console.log('4. Review browser console for any API errors when loading the page');
}

runTests().catch(console.error);