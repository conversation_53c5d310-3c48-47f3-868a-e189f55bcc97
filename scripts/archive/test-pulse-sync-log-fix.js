#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_APP_SUPABASE_URL;
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

console.log('🧪 Testing pulse_sync_log insert fix...\n');

async function testSyncLogInsert() {
  console.log('1️⃣  Testing direct insert to pulse_sync_log...');
  
  try {
    // Test inserting without providing an ID
    const { data, error } = await supabase
      .from('pulse_sync_log')
      .insert({
        sync_status: 'running',
        metadata: { 
          source: 'test-script', 
          purpose: 'testing ID generation fix',
          timestamp: new Date().toISOString()
        }
      })
      .select()
      .single();
    
    if (error) {
      console.error('❌ Error inserting:', error.message);
      console.error('   Details:', error);
      return false;
    }
    
    console.log('✅ Insert successful!');
    console.log('   Generated ID:', data.id);
    console.log('   Sync started at:', data.sync_started_at);
    
    // Update the record to mark it as completed
    console.log('\n2️⃣  Testing update of the record...');
    
    const { error: updateError } = await supabase
      .from('pulse_sync_log')
      .update({
        sync_completed_at: new Date().toISOString(),
        sync_status: 'completed',
        articles_fetched: 10,
        articles_created: 5,
        articles_updated: 5
      })
      .eq('id', data.id);
    
    if (updateError) {
      console.error('❌ Error updating:', updateError.message);
      return false;
    }
    
    console.log('✅ Update successful!');
    
    // Read back the updated record
    console.log('\n3️⃣  Reading back the record...');
    
    const { data: readData, error: readError } = await supabase
      .from('pulse_sync_log')
      .select('*')
      .eq('id', data.id)
      .single();
    
    if (readError) {
      console.error('❌ Error reading:', readError.message);
      return false;
    }
    
    console.log('✅ Read successful!');
    console.log('   Final record:', JSON.stringify(readData, null, 2));
    
    return true;
    
  } catch (e) {
    console.error('❌ Exception:', e.message);
    return false;
  }
}

// Run the test
testSyncLogInsert().then(success => {
  console.log('\n' + '='.repeat(50));
  if (success) {
    console.log('✅ All tests passed! The pulse_sync_log insert is working correctly.');
    console.log('\nThe fix has resolved the ID generation issue. You can now:');
    console.log('1. Run the migration to apply the fix permanently');
    console.log('2. Test the Edge Functions to ensure they work properly');
  } else {
    console.log('❌ Test failed. The issue persists.');
    console.log('\nPlease check:');
    console.log('1. The migration has been applied correctly');
    console.log('2. The trigger function is properly created');
  }
  process.exit(success ? 0 : 1);
});