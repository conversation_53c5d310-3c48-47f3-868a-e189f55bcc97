// Script to find teams that have players
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseKey);

async function findTeamsWithPlayers() {
  // Get all team members grouped by team
  const { data: teamMembers, error } = await supabase
    .from('team_members')
    .select('team_id, user_id, profiles!team_members_user_id_fkey(full_name)');
    
  if (error) {
    console.error('Error:', error);
    return;
  }
  
  // Count members per team
  const teamCounts = {};
  teamMembers.forEach(member => {
    teamCounts[member.team_id] = (teamCounts[member.team_id] || 0) + 1;
  });
  
  console.log('Teams with player counts:');
  console.log(teamCounts);
  
  // Get team details for teams with players
  const teamsWithPlayers = Object.keys(teamCounts);
  if (teamsWithPlayers.length > 0) {
    const { data: teams } = await supabase
      .from('teams')
      .select('team_id, team_name, club_id')
      .in('team_id', teamsWithPlayers);
      
    console.log('\nTeam details:');
    teams?.forEach(team => {
      console.log(`- ${team.team_name} (${team.team_id}): ${teamCounts[team.team_id]} players`);
      console.log(`  Club ID: ${team.club_id}`);
    });
  }
  
  // Show sample team members
  console.log('\nSample team members:');
  const { data: sample } = await supabase
    .from('team_members')
    .select('*, teams(team_name), profiles!team_members_user_id_fkey(full_name)')
    .limit(5);
  console.log(sample);
}

findTeamsWithPlayers().catch(console.error);