#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function findTeamsWithPreEvals() {
  console.log('Finding teams with pre-evaluations...\n');

  try {
    // Get all teams that have pre-evaluations
    const { data: teamsWithPreEvals, error } = await supabase
      .from('pre_evaluations')
      .select(`
        team_id,
        teams!pre_evaluations_team_id_fkey (
          id,
          name
        )
      `)
      .eq('status', 'completed')
      .order('completed_at', { ascending: false });

    if (error) {
      console.error('Error:', error);
      return;
    }

    // Get unique teams
    const uniqueTeams = new Map();
    teamsWithPreEvals?.forEach(item => {
      if (item.teams && !uniqueTeams.has(item.team_id)) {
        uniqueTeams.set(item.team_id, item.teams);
      }
    });

    console.log(`Found ${uniqueTeams.size} teams with completed pre-evaluations:\n`);
    
    // Check each team
    for (const [teamId, team] of uniqueTeams) {
      console.log(`\n📋 Team: ${team.name} (${teamId})`);
      
      // Get recent pre-evaluations for this team
      const { data: preEvals, error: preEvalsError } = await supabase
        .from('pre_evaluations')
        .select(`
          id,
          player_id,
          event_id,
          status,
          completed_at,
          request_method,
          responses,
          questions_answered,
          total_questions,
          confidence_level,
          mood_rating,
          profiles!pre_evaluations_player_id_fkey (
            full_name
          ),
          events!pre_evaluations_event_id_fkey (
            name,
            start_datetime
          )
        `)
        .eq('team_id', teamId)
        .eq('status', 'completed')
        .order('completed_at', { ascending: false })
        .limit(5);

      if (!preEvalsError && preEvals && preEvals.length > 0) {
        console.log(`   Recent completed pre-evaluations:`);
        
        preEvals.forEach((pe, index) => {
          const playerName = pe.profiles?.full_name || 'Unknown';
          const eventName = pe.events?.name || 'Unknown Event';
          const completedTime = pe.completed_at ? new Date(pe.completed_at).toLocaleString() : 'Not set';
          
          console.log(`\n   ${index + 1}. ${playerName} - ${eventName}`);
          console.log(`      Completed: ${completedTime}`);
          console.log(`      Method: ${pe.request_method || 'Not specified'}`);
          console.log(`      Questions: ${pe.questions_answered}/${pe.total_questions}`);
          
          // Check response structure
          if (pe.responses) {
            console.log(`      Response structure:`);
            
            // Check if it has the expected structure
            if (pe.responses.questions && Array.isArray(pe.responses.questions) && pe.responses.questions.length > 0) {
              console.log(`      ✅ Has ${pe.responses.questions.length} question responses`);
              
              // Show sample questions
              const sampleQuestions = pe.responses.questions.slice(0, 2);
              sampleQuestions.forEach((q, qi) => {
                console.log(`      Question ${qi + 1}: ${q.question || 'No question'}`);
                console.log(`      Category: ${q.category || 'No category'}`);
                console.log(`      Rating: ${q.rating || q.answer || 'No rating'}`);
              });
            } else {
              console.log(`      ❌ Empty responses: ${JSON.stringify(pe.responses)}`);
            }
          }
        });
      }
    }

    // Look for today's pre-evaluations specifically
    const today = new Date().toISOString().split('T')[0];
    console.log(`\n\n🔥 TODAY'S COMPLETED PRE-EVALUATIONS (${today}):`);
    
    const { data: todayPreEvals, error: todayError } = await supabase
      .from('pre_evaluations')
      .select(`
        *,
        profiles!pre_evaluations_player_id_fkey (
          full_name
        ),
        teams!pre_evaluations_team_id_fkey (
          name
        ),
        events!pre_evaluations_event_id_fkey (
          name
        )
      `)
      .gte('completed_at', today)
      .eq('status', 'completed')
      .order('completed_at', { ascending: false });

    if (todayPreEvals && todayPreEvals.length > 0) {
      todayPreEvals.forEach(pe => {
        const playerName = pe.profiles?.full_name || 'Unknown';
        const teamName = pe.teams?.name || 'Unknown Team';
        const eventName = pe.events?.name || 'Unknown Event';
        const completedTime = new Date(pe.completed_at).toLocaleTimeString();
        
        console.log(`\n- ${playerName} (${teamName}) - ${eventName}`);
        console.log(`  Completed at: ${completedTime}`);
        console.log(`  Method: ${pe.request_method}`);
        
        if (pe.responses && pe.responses.questions && pe.responses.questions.length > 0) {
          console.log(`  ✅ Has ${pe.responses.questions.length} responses`);
          console.log(`  Full response data:`);
          console.log(JSON.stringify(pe.responses, null, 2));
        }
      });
    } else {
      console.log('No pre-evaluations completed today');
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the check
findTeamsWithPreEvals();