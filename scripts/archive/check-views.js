// ABOUTME: Script to check existing views in the database
// This helps us understand what performance views already exist

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkViews() {
  try {
    // Query to find all views using RPC
    const { data: views, error } = await supabase.rpc('get_database_views', {
      schema_name: 'public'
    }).select('*');

    if (error) {
      // Fallback: try direct SQL query
      const { data: viewsSQL, error: sqlError } = await supabase
        .rpc('get_views_list')
        .select('*');
      
      if (sqlError) {
        console.log('Creating RPC function to list views...');
        // Let's check event_comprehensive_summary directly
        const { data: testView, error: testError } = await supabase
          .from('event_comprehensive_summary')
          .select('*')
          .limit(1);
        
        if (!testError) {
          console.log('✓ event_comprehensive_summary view exists!');
          if (testView && testView.length > 0) {
            console.log('\nColumns available:');
            console.log(Object.keys(testView[0]).join(', '));
          }
        } else {
          console.log('event_comprehensive_summary does not exist or is not accessible');
        }
        return;
      }
    }

    if (error) {
      console.error('Error fetching views:', error);
      return;
    }

    console.log('Existing views in the database:');
    console.log('================================');
    views.forEach(view => {
      console.log(`${view.schemaname}.${view.viewname}`);
    });

    // Check specifically for views that might help with team page
    console.log('\nViews that might help with team page performance:');
    console.log('================================================');
    const relevantViews = views.filter(v => 
      v.viewname.includes('team') || 
      v.viewname.includes('event') || 
      v.viewname.includes('player') ||
      v.viewname.includes('coach') ||
      v.viewname.includes('evaluation')
    );
    
    relevantViews.forEach(view => {
      console.log(`- ${view.viewname}`);
    });

    // Check if event_comprehensive_summary exists
    const eventView = views.find(v => v.viewname === 'event_comprehensive_summary');
    if (eventView) {
      console.log('\n✓ event_comprehensive_summary view exists!');
      
      // Get a sample of the view structure
      const { data: sample, error: sampleError } = await supabase
        .from('event_comprehensive_summary')
        .select('*')
        .limit(1);
      
      if (!sampleError && sample && sample.length > 0) {
        console.log('\nColumns in event_comprehensive_summary:');
        console.log(Object.keys(sample[0]).join(', '));
      }
    }

  } catch (err) {
    console.error('Error:', err);
  }
}

checkViews();