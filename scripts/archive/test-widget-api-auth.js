#!/usr/bin/env node

/**
 * Test widget-api authentication with the correct anon key
 */

const SUPABASE_URL = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mjg1NTExMjAsImV4cCI6MjA0NDEyNzEyMH0.fUqHSW87vqKLBt2c4l-JHoR-smcnSGGI8PEATd8nnrU';

async function testDirectSupabaseAccess() {
  console.log('🔍 Testing direct Supabase access with anon key...\n');
  
  try {
    // Test accessing pulse_articles directly
    const response = await fetch(
      `${SUPABASE_URL}/rest/v1/pulse_articles?select=*&limit=5`,
      {
        headers: {
          'apikey': ANON_KEY,
          'Authorization': `Bearer ${ANON_KEY}`,
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      }
    );
    
    console.log('Status:', response.status);
    console.log('Status Text:', response.statusText);
    
    const text = await response.text();
    
    if (response.ok) {
      const data = JSON.parse(text);
      console.log('✅ Success! Retrieved', data.length, 'articles');
      console.log('First article:', data[0]?.title || 'No articles found');
    } else {
      console.log('❌ Failed:', text);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

async function testWidgetAPI() {
  console.log('\n🔍 Testing widget-api Edge Function...\n');
  
  try {
    const response = await fetch(
      `${SUPABASE_URL}/functions/v1/widget-api/pulse-feed?count=5&theme=dark`,
      {
        headers: {
          'Origin': 'http://localhost:3000'
        }
      }
    );
    
    console.log('Status:', response.status);
    console.log('Content-Type:', response.headers.get('content-type'));
    
    if (response.status === 401) {
      console.log('❌ 401 Unauthorized - Edge Function needs to be updated');
      console.log('The function is likely using content.pulse_articles instead of the public view');
    } else if (response.ok) {
      console.log('✅ Success! Widget API is working');
    } else {
      const text = await response.text();
      console.log('❌ Error:', text);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

async function testContentAPI() {
  console.log('\n🔍 Testing content-api Edge Function (alternative)...\n');
  
  try {
    const response = await fetch(
      `${SUPABASE_URL}/functions/v1/content-api/pulse?per_page=5`,
      {
        headers: {
          'Origin': 'http://localhost:3000'
        }
      }
    );
    
    console.log('Status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Success! Content API works');
      console.log('Articles returned:', data.data?.length || 0);
    } else {
      const text = await response.text();
      console.log('❌ Error:', text);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Run all tests
async function runTests() {
  console.log('🧪 Widget API Authentication Test');
  console.log('=================================\n');
  
  await testDirectSupabaseAccess();
  await testWidgetAPI();
  await testContentAPI();
  
  console.log('\n✅ Tests complete');
}

runTests().catch(console.error);