#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.VITE_APP_SUPABASE_URL;
const supabaseServiceKey = process.env.VITE_APP_SUPABASE_SERVICE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const MARK_USER_ID = '57a84e11-58e1-4e53-aebb-05d61b2c89dd';

console.log('🔧 Checking Mark\'s team associations...\n');

async function analyzeAndFixTeams() {
  // Get unique teams Mark has been evaluated for
  const { data: evaluationTeams, error: teamError } = await supabase
    .from('player_evaluations')
    .select('team_id')
    .eq('player_id', MARK_USER_ID)
    .not('team_id', 'is', null);
  
  if (teamError) {
    console.error('❌ Error fetching evaluation teams:', teamError);
    return;
  }
  
  const uniqueTeamIds = [...new Set(evaluationTeams.map(e => e.team_id))];
  console.log(`Mark has evaluations for ${uniqueTeamIds.length} teams:`);
  
  // Get team details
  for (const teamId of uniqueTeamIds) {
    const { data: team } = await supabase
      .from('teams')
      .select('name, club_id')
      .eq('id', teamId)
      .single();
    
    console.log(`\n📍 Team: ${team?.name || 'Unknown'} (${teamId})`);
    
    // Check if Mark is in team_players
    const { data: playerRecord, error: playerError } = await supabase
      .from('team_players')
      .select('*')
      .eq('team_id', teamId)
      .eq('user_id', MARK_USER_ID)
      .single();
    
    if (playerError && playerError.code === 'PGRST116') {
      console.log('   ❌ Mark is NOT in team_players for this team');
      console.log('   🔧 This could be why evaluations aren\'t showing!');
      
      // Option to fix (commented out for safety)
      console.log(`   
   To fix, run this SQL in Supabase:
   INSERT INTO team_players (team_id, user_id, created_at)
   VALUES ('${teamId}', '${MARK_USER_ID}', NOW())
   ON CONFLICT (team_id, user_id) DO NOTHING;
      `);
    } else if (playerRecord) {
      console.log('   ✅ Mark is properly associated with this team');
    }
  }
  
  console.log('\n' + '='.repeat(60));
  console.log('SOLUTION:');
  console.log('='.repeat(60));
  console.log('\nThe issue is likely that Mark has evaluations but is not properly');
  console.log('associated with teams in the team_players table.');
  console.log('\nTo fix this, you need to add Mark to the team_players table for');
  console.log('each team he has been evaluated for.');
  console.log('\nYou can run the SQL commands shown above in your Supabase SQL editor.');
}

analyzeAndFixTeams().catch(console.error);