// ABOUTME: Script to create the team_page_data view in the database
// This view consolidates team data to reduce queries on TeamFlat page

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseKey);

async function createTeamPageView() {
  console.log('Creating team_page_data view...');
  
  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE OR REPLACE VIEW team_page_data AS
      SELECT 
          t.team_id,
          t.team_name,
          t.age_group,
          t.sport_type,
          t.logo_url AS team_photo_url,
          t.is_active,
          t.club_id,
          c.club_name,
          c.logo_url AS club_logo_url,
          
          -- Coach count
          (SELECT COUNT(DISTINCT tc.user_id) 
           FROM team_coaches tc 
           WHERE tc.team_id = t.team_id 
           AND tc.is_active = true) AS coach_count,
          
          -- Active player count
          (SELECT COUNT(DISTINCT tm.user_id) 
           FROM team_members tm 
           WHERE tm.team_id = t.team_id 
           AND tm.status = 'active') AS active_player_count,
          
          -- Total player count
          (SELECT COUNT(DISTINCT tm.user_id) 
           FROM team_members tm 
           WHERE tm.team_id = t.team_id) AS total_player_count,
          
          -- Outstanding evaluations count
          (SELECT COUNT(*)
           FROM evaluation_assignments ea
           JOIN events e ON ea.event_id = e.event_id
           WHERE e.team_id = t.team_id
           AND ea.status IN ('pending', 'in_progress')
           AND e.deleted_at IS NULL) AS outstanding_evaluations
           
      FROM teams t
      JOIN clubs c ON t.club_id = c.club_id
      WHERE t.deleted_at IS NULL;
    `
  });

  if (error) {
    console.error('Error creating view:', error);
    
    // Try alternative approach without exec_sql
    console.log('Trying alternative approach...');
    
    // First check if we can query teams
    const { data: teams, error: teamError } = await supabase
      .from('teams')
      .select('id, team_name')
      .limit(1);
    
    if (!teamError) {
      console.log('✓ Can access teams table');
      console.log('\nTo create the view, please run this SQL in your Supabase SQL editor:');
      console.log('\n--- BEGIN SQL ---');
      console.log(`CREATE OR REPLACE VIEW team_page_data AS
SELECT 
    t.team_id,
    t.team_name,
    t.age_group,
    t.sport_type,
    t.logo_url AS team_photo_url,
    t.is_active,
    t.club_id,
    c.club_name,
    c.logo_url AS club_logo_url,
    
    -- Coach count
    (SELECT COUNT(DISTINCT tc.user_id) 
     FROM team_coaches tc 
     WHERE tc.team_id = t.team_id 
     AND tc.is_active = true) AS coach_count,
    
    -- Active player count
    (SELECT COUNT(DISTINCT tm.user_id) 
     FROM team_members tm 
     WHERE tm.team_id = t.team_id 
     AND tm.status = 'active') AS active_player_count,
    
    -- Total player count
    (SELECT COUNT(DISTINCT tm.user_id) 
     FROM team_members tm 
     WHERE tm.team_id = t.team_id) AS total_player_count,
    
    -- Outstanding evaluations count
    (SELECT COUNT(*)
     FROM evaluation_assignments ea
     JOIN events e ON ea.event_id = e.event_id
     WHERE e.team_id = t.team_id
     AND ea.status IN ('pending', 'in_progress')
     AND e.deleted_at IS NULL) AS outstanding_evaluations
     
FROM teams t
JOIN clubs c ON t.club_id = c.club_id
WHERE t.deleted_at IS NULL;`);
      console.log('--- END SQL ---');
    }
  } else {
    console.log('✓ team_page_data view created successfully!');
    
    // Test the view
    const { data: testData, error: testError } = await supabase
      .from('team_page_data')
      .select('*')
      .limit(1);
    
    if (!testError && testData) {
      console.log('\n✓ View is working! Sample columns:');
      if (testData.length > 0) {
        console.log(Object.keys(testData[0]).join(', '));
      }
    }
  }
}

createTeamPageView();