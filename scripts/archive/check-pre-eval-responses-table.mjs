#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkPreEvalResponsesTables() {
  console.log('Checking for pre-evaluation response data in different tables...\n');

  try {
    // Check if there's a separate pre_evaluation_responses table
    const tables = [
      'pre_evaluation_responses',
      'pre_evaluation_answers',
      'pre_evaluation_ratings',
      'evaluation_responses',
      'player_pre_evaluations',
      'event_pre_evaluations'
    ];

    console.log('Checking for related tables:');
    for (const tableName of tables) {
      try {
        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .limit(1);
        
        if (!error) {
          console.log(`✅ ${tableName} exists`);
          
          // Get more data
          const { data: sampleData } = await supabase
            .from(tableName)
            .select('*')
            .limit(5);
            
          if (sampleData && sampleData.length > 0) {
            console.log(`   Columns: ${Object.keys(sampleData[0]).join(', ')}`);
            console.log(`   Sample data:`);
            console.log(JSON.stringify(sampleData[0], null, 2));
          }
        } else {
          console.log(`❌ ${tableName} not found`);
        }
      } catch (e) {
        // Table doesn't exist
      }
    }

    // Check pre_evaluations table structure more thoroughly
    console.log('\n\nChecking pre_evaluations table structure:');
    
    const { data: preEvalSample } = await supabase
      .from('pre_evaluations')
      .select('*')
      .limit(1);
      
    if (preEvalSample && preEvalSample.length > 0) {
      const columns = Object.keys(preEvalSample[0]);
      console.log('All columns:', columns.join(', '));
      
      // Check for any columns that might contain rating data
      const ratingColumns = columns.filter(col => 
        col.includes('rating') || 
        col.includes('score') || 
        col.includes('answer') ||
        col.includes('response') ||
        col.includes('evaluation')
      );
      
      if (ratingColumns.length > 0) {
        console.log('\nPotential rating columns:', ratingColumns.join(', '));
        
        // Get a sample with these columns
        const { data: ratingData } = await supabase
          .from('pre_evaluations')
          .select(ratingColumns.join(','))
          .not('status', 'eq', 'pending')
          .limit(5);
          
        if (ratingData && ratingData.length > 0) {
          console.log('\nSample rating data:');
          ratingData.forEach((row, i) => {
            console.log(`\nRow ${i + 1}:`);
            ratingColumns.forEach(col => {
              if (row[col] !== null && row[col] !== undefined) {
                console.log(`  ${col}: ${JSON.stringify(row[col])}`);
              }
            });
          });
        }
      }
    }

    // Check if responses are stored in a JSONB column with different structure
    console.log('\n\nAnalyzing pre_evaluations response patterns:');
    
    const { data: variedResponses } = await supabase
      .from('pre_evaluations')
      .select('id, responses, evaluation_status, questions_answered, total_questions')
      .not('responses', 'is', null)
      .limit(10);
      
    if (variedResponses) {
      const patterns = new Map();
      
      variedResponses.forEach(row => {
        const responseStr = JSON.stringify(row.responses);
        const pattern = responseStr.length > 50 ? 'has_data' : 'empty';
        
        if (!patterns.has(pattern)) {
          patterns.set(pattern, []);
        }
        patterns.get(pattern).push(row);
      });
      
      console.log(`\nResponse patterns found:`);
      for (const [pattern, rows] of patterns) {
        console.log(`\n${pattern}: ${rows.length} rows`);
        if (pattern === 'has_data' && rows.length > 0) {
          console.log('Sample with data:');
          console.log(JSON.stringify(rows[0], null, 2));
        }
      }
    }

    // Check for pre-evaluation data linked through event_id
    console.log('\n\nChecking for pre-evaluations with actual question/answer data:');
    
    const { data: detailedPreEvals } = await supabase
      .from('pre_evaluations')
      .select(`
        id,
        event_id,
        player_id,
        status,
        evaluation_status,
        questions_answered,
        total_questions,
        confidence_level,
        mood_rating,
        goals_text,
        concerns_text,
        responses
      `)
      .eq('status', 'completed')
      .gt('questions_answered', 0)
      .order('completed_at', { ascending: false })
      .limit(10);
      
    if (detailedPreEvals && detailedPreEvals.length > 0) {
      console.log(`\nFound ${detailedPreEvals.length} pre-evaluations with answered questions:`);
      
      detailedPreEvals.forEach((pe, i) => {
        console.log(`\n${i + 1}. Pre-eval ${pe.id}`);
        console.log(`   Questions: ${pe.questions_answered}/${pe.total_questions}`);
        console.log(`   Status: ${pe.status} / ${pe.evaluation_status}`);
        console.log(`   Confidence: ${pe.confidence_level}`);
        console.log(`   Mood: ${pe.mood_rating}`);
        if (pe.goals_text) console.log(`   Goals: ${pe.goals_text}`);
        if (pe.concerns_text) console.log(`   Concerns: ${pe.concerns_text}`);
        
        // Check responses thoroughly
        if (pe.responses) {
          const responseKeys = Object.keys(pe.responses);
          console.log(`   Response keys: ${responseKeys.join(', ')}`);
          
          // Check for nested data
          responseKeys.forEach(key => {
            const value = pe.responses[key];
            if (typeof value === 'object' && value !== null) {
              console.log(`   ${key}: ${JSON.stringify(value)}`);
            }
          });
        }
      });
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the check
checkPreEvalResponsesTables();