#!/usr/bin/env node

/**
 * Test script for widget-api Edge Function
 * Tests both CORS and actual widget responses
 */

const API_BASE = 'https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/widget-api';

async function testCORS() {
  console.log('\n🔍 Testing CORS Preflight...');
  
  try {
    const response = await fetch(`${API_BASE}/pulse-feed`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'content-type',
      }
    });
    
    console.log('Status:', response.status);
    console.log('CORS Headers:');
    console.log('  Allow-Origin:', response.headers.get('Access-Control-Allow-Origin'));
    console.log('  Allow-Methods:', response.headers.get('Access-Control-Allow-Methods'));
    console.log('  Allow-Headers:', response.headers.get('Access-Control-Allow-Headers'));
    
    if (response.status === 200) {
      console.log('✅ CORS preflight passed');
    } else {
      console.log('❌ CORS preflight failed');
    }
  } catch (error) {
    console.error('❌ CORS test error:', error.message);
  }
}

async function testWidgetAPI(endpoint, params = {}) {
  const queryString = new URLSearchParams(params).toString();
  const url = `${API_BASE}${endpoint}${queryString ? '?' + queryString : ''}`;
  
  console.log(`\n🔍 Testing: ${url}`);
  
  try {
    const response = await fetch(url, {
      headers: {
        'Origin': 'http://localhost:3000',
      }
    });
    
    console.log('Status:', response.status);
    console.log('Content-Type:', response.headers.get('content-type'));
    
    const text = await response.text();
    
    if (!response.ok) {
      console.error('❌ Request failed:', text);
      return;
    }
    
    // Check if HTML or JSON
    if (text.includes('<!DOCTYPE html>') || text.includes('<html>')) {
      console.log('✅ Received HTML widget');
      console.log('Size:', text.length, 'bytes');
      console.log('Preview:', text.substring(0, 200) + '...');
    } else {
      try {
        const json = JSON.parse(text);
        console.log('✅ Received JSON response');
        console.log(JSON.stringify(json, null, 2));
      } catch {
        console.log('✅ Received response');
        console.log(text.substring(0, 200));
      }
    }
    
  } catch (error) {
    console.error('❌ Request error:', error.message);
  }
}

async function runTests() {
  console.log('🧪 Widget API Test Suite');
  console.log('========================');
  
  // Test CORS
  await testCORS();
  
  // Test pulse-feed endpoint
  await testWidgetAPI('/pulse-feed', {
    count: 5,
    theme: 'dark',
    showImages: true
  });
  
  // Test with different theme
  await testWidgetAPI('/pulse-feed', {
    count: 3,
    theme: 'light',
    pillar: 'TAKE YOUR SHOT'
  });
  
  // Test invalid endpoint
  await testWidgetAPI('/invalid-endpoint');
  
  console.log('\n✅ Tests complete');
}

// Run tests
runTests().catch(console.error);