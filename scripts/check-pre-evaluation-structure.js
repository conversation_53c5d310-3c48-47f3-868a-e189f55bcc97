// ABOUTME: Script to check pre-evaluation structure and how questions are determined
// This helps us understand how to add position-specific questions

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkPreEvaluationStructure() {
  console.log('=== CHECKING PRE-EVALUATION STRUCTURE ===\n');

  // 1. Check pre_evaluations table structure
  console.log('1. PRE_EVALUATIONS TABLE COLUMNS:');
  console.log('----------------------------------');
  try {
    const { data: columns, error } = await supabase
      .rpc('get_table_columns', { table_name: 'pre_evaluations' });
    
    if (error) {
      // Try alternative method
      const { data: sample, error: sampleError } = await supabase
        .from('pre_evaluations')
        .select('*')
        .limit(1);
      
      if (!sampleError && sample && sample.length > 0) {
        console.log('Columns found:', Object.keys(sample[0]));
      }
    } else {
      console.log('Columns:', columns);
    }
  } catch (error) {
    console.error('Error checking pre_evaluations structure:', error.message);
  }

  // 2. Check if pre_evaluations has player_position field
  console.log('\n\n2. CHECKING FOR player_position FIELD:');
  console.log('--------------------------------------');
  try {
    const { data: samplePreEval, error } = await supabase
      .from('pre_evaluations')
      .select('id, player_id, player_position')
      .limit(5);
    
    if (error) {
      if (error.message.includes('player_position')) {
        console.log('player_position field DOES NOT EXIST in pre_evaluations table');
      } else {
        throw error;
      }
    } else {
      console.log('player_position field exists. Sample data:');
      console.log(samplePreEval);
    }
  } catch (error) {
    console.error('Error checking player_position:', error.message);
  }

  // 3. Check event_participants for participant_position
  console.log('\n\n3. EVENT_PARTICIPANTS POSITION DATA:');
  console.log('------------------------------------');
  try {
    const { data: participants, error } = await supabase
      .from('event_participants')
      .select('event_id, user_id, participant_position')
      .eq('event_id', '8b405fdf-093a-48c0-ac62-800ec68d99c7')
      .limit(10);
    
    if (error) throw error;
    
    console.log('Sample event participants with positions:');
    participants.forEach(p => {
      console.log(`  User: ${p.user_id}, Position: ${p.participant_position || 'NOT SET'}`);
    });
  } catch (error) {
    console.error('Error checking event_participants:', error.message);
  }

  // 4. Check how player_evaluations are linked to pre_evaluations
  console.log('\n\n4. PLAYER_EVALUATIONS LINK TO PRE_EVALUATIONS:');
  console.log('----------------------------------------------');
  try {
    const { data: playerEvals, error } = await supabase
      .from('player_evaluations')
      .select('id, player_id, pre_evaluation_id, category, area, position')
      .not('pre_evaluation_id', 'is', null)
      .limit(10);
    
    if (error) throw error;
    
    console.log(`Found ${playerEvals.length} player_evaluations linked to pre_evaluations`);
    if (playerEvals.length > 0) {
      console.log('Sample data:');
      playerEvals.forEach(pe => {
        console.log(`  Pre-eval: ${pe.pre_evaluation_id}, Category: ${pe.category}, Position: ${pe.position}`);
      });
    }
  } catch (error) {
    console.error('Error checking player_evaluations:', error.message);
  }

  // 5. Check for functions that create player_evaluations from pre_evaluations
  console.log('\n\n5. DATABASE FUNCTIONS RELATED TO PRE-EVALUATIONS:');
  console.log('-------------------------------------------------');
  try {
    const { data: functions, error } = await supabase
      .rpc('get_db_functions')
      .or('routine_name.ilike.%pre_eval%,routine_name.ilike.%player_evaluation%');
    
    if (error) {
      // Try alternative query
      console.log('Could not fetch functions list');
    } else {
      console.log('Functions found:', functions);
    }
  } catch (error) {
    console.error('Error checking functions:', error.message);
  }

  process.exit(0);
}

// Helper function attempt
async function getDbFunctions() {
  try {
    const { data, error } = await supabase.rpc('query_pg_proc', {
      query: `SELECT proname FROM pg_proc WHERE proname LIKE '%pre_eval%' OR proname LIKE '%player_eval%'`
    });
    return data;
  } catch (error) {
    return null;
  }
}

checkPreEvaluationStructure();