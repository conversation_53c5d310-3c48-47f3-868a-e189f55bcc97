#!/bin/bash

# Development script to set version info
echo "Setting version information for development..."

# Get version from package.json
VERSION=$(node -p "require('./package.json').version" 2>/dev/null || echo "dev")

# Get git information
BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

# Create or update .env.local with build info
echo "VITE_APP_VERSION=$VERSION" > .env.local
echo "VITE_GIT_BRANCH=$BRANCH" >> .env.local
echo "VITE_GIT_COMMIT=$COMMIT" >> .env.local
echo "VITE_BUILD_TIME=$BUILD_TIME" >> .env.local
echo "VITE_SHOW_VERSION_IN_HEADER=true" >> .env.local

echo "Development version info set:"
echo "  Version: $VERSION"
echo "  Branch: $BRANCH"
echo "  Commit: $COMMIT"
echo "  Build Time: $BUILD_TIME"
echo "  Header display: enabled"
