#!/usr/bin/env node

// ABOUTME: Script to organize deprecated files according to the 7-area architecture
// Aligns with the implementation plan's deprecation strategy

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PROJECT_ROOT = path.join(__dirname, '..');
const DEPRECATED_ROOT = path.join(PROJECT_ROOT, 'src/_deprecated');
const RESULTS_FILE = path.join(PROJECT_ROOT, 'route-analysis-results.json');

// Area mapping based on file patterns and implementation plans
const AREA_MAPPINGS = {
  identity: {
    name: 'Identity - Authentication & Family',
    patterns: [
      'Login.tsx', 'UpdatedLogin.tsx', 'SimpleLogin.tsx', 'NonIonicLogin.tsx',
      'Signup.tsx', 'Registration.tsx', 'InvitedRegistrationPage.tsx',
      'ResetPassword.tsx', 'Family.tsx', 'EditFamily.tsx', 'AddFamily.tsx',
      'Account.tsx', 'Profile.tsx', 'EmailConfirmationHandler.tsx',
      'Onboarding/', 'section/Parent/FamilyManagement.tsx'
    ]
  },
  perform: {
    name: 'Perform - Sports Performance',
    patterns: [
      'section/Coach/CoachDashboard.tsx', 'section/Coach/UpdatedCoachDashboard.tsx',
      'section/Coach/CoachHome.tsx', 'section/Coach/UpdatedCoachHome.tsx',
      'section/Coach/PlayerManagement.tsx', 'section/Coach/PlayerObjectives.tsx',
      'section/Coach/TeamObjectives.tsx', 'section/Coach/TeamEvaluation.tsx',
      'section/Coach/SessionPlanner.tsx', 'section/Coach/MatchSetup.tsx',
      'section/Coach/dashboards/', 'section/Player/',
      'perform/MemberPerform.tsx', 'v2/perform/', 'Player.tsx'
    ]
  },
  schedule: {
    name: 'Schedule - Events & Calendar',
    patterns: [
      'Events.tsx', 'EventDetails.tsx',
      'section/Coach/events/', 'section/Coach/EventManagement.tsx',
      'section/Coach/createEvent.tsx', 'section/Coach/supporting/EventManagement/'
    ]
  },
  assess: {
    name: 'Assess - Evaluations & Development',
    patterns: [
      'ImproveIDP.tsx', 'ViewIDP.tsx',
      'section/Coach/WeeklyEvaluation.tsx', 'section/Coach/evaluations/',
      'section/Coach/components/PlayerSelfEvaluation/',
      'section/Coach/supporting/EvaluationIntegration/'
    ]
  },
  clubhouse: {
    name: 'Clubhouse - Club Management',
    patterns: [
      'section/Coach/supporting/ClubManagement/',
      'section/Coach/AddClub.tsx', 'Home.tsx', 'Dashboard.tsx'
    ]
  },
  control: {
    name: 'Control - Platform Administration',
    patterns: [
      'SuperAdmin/', 'section/Admin/',
      'section/Coach/UserManagement.tsx',
      'section/Coach/CommunicationManagement.tsx'
    ]
  },
  pulse: {
    name: 'Pulse - Social & Content',
    patterns: [
      'v2/pulse/', 'Activities.tsx',
      'section/Follow/WelcomeFollow.tsx'
    ]
  },
  locker: {
    name: 'Locker - E-commerce',
    patterns: [
      'Membership.tsx', 'Payments.tsx', 'LifestyleHome.tsx'
    ]
  },
  foundation: {
    name: 'Foundation - Core Infrastructure',
    patterns: [
      'Splash.tsx', 'Loading.tsx', 'GenerateCode.tsx',
      'section/Navigation/', 'v2/DesignSystem/'
    ]
  },
  unknown: {
    name: 'Unknown - Uncategorized',
    patterns: [] // Catch-all for files that don't match any pattern
  }
};

// Function to determine which area a file belongs to
function getAreaForFile(filePath) {
  for (const [area, config] of Object.entries(AREA_MAPPINGS)) {
    for (const pattern of config.patterns) {
      if (filePath.includes(pattern)) {
        return area;
      }
    }
  }
  return 'unknown';
}

// Function to ensure directory exists
function ensureDir(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

// Function to create area README
function createAreaReadme(area, areaConfig, files) {
  return `# Deprecated Files - ${areaConfig.name}

## Overview
This directory contains deprecated files that were part of the ${area} feature area.
These files have been moved here as part of the 7-area architecture migration.

## Migration Status
- **Area**: ${area}
- **Risk Level**: ${getAreaRiskLevel(area)}
- **Migration Priority**: ${getAreaPriority(area)}
- **Files in this directory**: ${files.length}

## Files
${files.map(f => `- ${f.path} - ${f.reason || 'Unused in routes'}`).join('\n')}

## Migration Plan
According to the implementation plan, the ${area} area should be migrated in the following order:
${getMigrationOrder()}

## Deletion Schedule
- **Testing Period**: 2 weeks after migration complete
- **Production Validation**: 2 months
- **Safe to Delete**: After production validation + team approval

## Rollback Instructions
To restore these files to their original location:
\`\`\`bash
npm run restore-deprecated-area ${area}
\`\`\`

## References
- Implementation Plan: /docs/implementation-plans/${getAreaPlanFile(area)}
- Migration Status: Track in GitHub issues
`;
}

// Helper functions for README generation
function getAreaRiskLevel(area) {
  const riskLevels = {
    foundation: 'LOW',
    identity: 'HIGH',
    locker: 'LOW',
    pulse: 'LOW',
    control: 'MEDIUM',
    schedule: 'MEDIUM',
    clubhouse: 'MEDIUM',
    assess: 'HIGH',
    perform: 'EXTREME'
  };
  return riskLevels[area] || 'UNKNOWN';
}

function getAreaPriority(area) {
  const priorities = {
    foundation: '1st',
    identity: '2nd',
    locker: '3rd',
    pulse: '4th',
    control: '5th',
    schedule: '6th',
    clubhouse: '7th',
    assess: '8th',
    perform: '9th'
  };
  return priorities[area] || 'TBD';
}

function getMigrationOrder() {
  return `
1. Foundation Modules
2. Identity
3. Locker
4. Pulse
5. Control
6. Schedule
7. Clubhouse
8. Assess
9. Perform`;
}

function getAreaPlanFile(area) {
  const planFiles = {
    identity: '06-identity-implementation-plan.md',
    perform: '02-perform-implementation-plan.md',
    locker: '03-locker-implementation-plan.md',
    pulse: '04-pulse-implementation-plan.md',
    clubhouse: '01-clubhouse-implementation-plan.md',
    schedule: '07-schedule-implementation-plan.md',
    assess: '08-assess-implementation-plan.md',
    control: '09-control-implementation-plan.md',
    foundation: '10-foundation-modules-plan.md'
  };
  return planFiles[area] || '00-implementation-summary.md';
}

// Main execution
function organizeDeprecatedFiles(dryRun = false) {
  console.log(`\n📦 Organizing Deprecated Files by Area ${dryRun ? '(DRY RUN)' : ''}`);
  console.log('==========================================\n');

  // Load analysis results
  const results = JSON.parse(fs.readFileSync(RESULTS_FILE, 'utf8'));
  
  // Create deprecated directory structure
  if (!dryRun) {
    ensureDir(DEPRECATED_ROOT);
    Object.keys(AREA_MAPPINGS).forEach(area => {
      ensureDir(path.join(DEPRECATED_ROOT, area));
    });
  }

  // Organize files by area
  const filesByArea = {};
  Object.keys(AREA_MAPPINGS).forEach(area => {
    filesByArea[area] = [];
  });

  // Process unused files
  results.files.unused.forEach(relativePath => {
    const area = getAreaForFile(relativePath);
    filesByArea[area].push({
      path: relativePath,
      reason: 'No active route reference'
    });
  });

  // Display organization plan
  console.log('📊 File Organization Plan:\n');
  Object.entries(filesByArea).forEach(([area, files]) => {
    if (files.length > 0) {
      console.log(`${area.toUpperCase()} (${files.length} files)`);
      console.log(`Risk: ${getAreaRiskLevel(area)}, Priority: ${getAreaPriority(area)}`);
      files.slice(0, 5).forEach(f => console.log(`  - ${f.path}`));
      if (files.length > 5) {
        console.log(`  ... and ${files.length - 5} more files`);
      }
      console.log('');
    }
  });

  if (!dryRun) {
    // Create area directories and READMEs
    Object.entries(filesByArea).forEach(([area, files]) => {
      if (files.length > 0) {
        const areaDir = path.join(DEPRECATED_ROOT, area);
        const readme = createAreaReadme(area, AREA_MAPPINGS[area], files);
        fs.writeFileSync(path.join(areaDir, 'README.md'), readme);
        
        // Create file list for tracking
        fs.writeFileSync(
          path.join(areaDir, 'file-list.json'),
          JSON.stringify({ area, files, date: new Date().toISOString() }, null, 2)
        );
      }
    });

    console.log(`\n✅ Created deprecation structure in: ${DEPRECATED_ROOT}`);
    console.log('\n⚠️  Note: Files have NOT been moved yet!');
    console.log('   Run with specific area to actually move files:');
    console.log('   npm run deprecate-area identity');
  }
}

// Command line interface
const args = process.argv.slice(2);
const dryRun = args.includes('--dry-run');

organizeDeprecatedFiles(dryRun);