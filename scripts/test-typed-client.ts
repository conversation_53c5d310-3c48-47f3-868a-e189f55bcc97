/**
 * Test script for the typed Supabase client
 * 
 * This script validates that the Supabase client works correctly with generated types
 * and provides proper type safety for database operations.
 */

import { supabase, type Profile, type Team, type Tables, castJsonField, getTeamIdFromProfile } from '../src/lib/supabase';

// Test type safety for queries
async function testTypedQueries() {
  console.log('🧪 Testing typed database queries...');
  
  try {
    // Test profiles query with type safety
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, full_name, avatar_url, registration_metadata')
      .limit(1);
    
    if (profilesError) {
      console.log('⚠️  Profiles query error (expected in some environments):', profilesError.message);
    } else if (profiles && profiles.length > 0) {
      const profile = profiles[0] as Profile;

      // Test that TypeScript knows the correct types
      console.log('✅ Profile ID type:', typeof profile.id); // Should be string
      console.log('✅ Profile full_name type:', typeof profile.full_name); // Should be string | null

      // Test JSON field handling
      const teamId = getTeamIdFromProfile(profile);
      console.log('✅ Team ID from metadata:', teamId);
    }
    
    // Test teams query
    const { data: teams, error: teamsError } = await supabase
      .from('teams')
      .select('team_id, team_name')
      .limit(1);
    
    if (teamsError) {
      console.log('⚠️  Teams query error (expected in some environments):', teamsError.message);
    } else if (teams && teams.length > 0) {
      const team = teams[0] as Team;
      console.log('✅ Team name type:', typeof team.team_name); // Should be string | null
    }
    
    console.log('✅ Typed queries test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error in typed queries test:', error);
  }
}

// Test type helpers
function testTypeHelpers() {
  console.log('🧪 Testing type helpers...');
  
  // Test that we can use the exported types
  const mockProfile: Profile = {
    id: 'test-id',
    full_name: 'Test User',
    avatar_url: null,
    registration_metadata: { team_id: 'test-team-id' },
    age_transition_date: null,
    age_verified: null,
    allergies: null,
    communication_preferences: null,
    consent_records: null,
    context_preferences: null,
    date_of_birth: null,
    display_name: null,
    email_verified: null,
    emergency_contact: null,
    is_minor: null,
    last_accessed_team: null,
    last_login: null,
    location: null,
    marketing_email: null,
    marketing_notifications: null,
    medical_conditions: null,
    nickname: null,
    notification_preferences: null,
    onboarding_completed: null,
    parent_handoff_completed: null,
    parent_id: null,
    personal_invite_code: null,
    phone: null,
    phone_normalised: null,
    phone_verified: null,
    phone_verified_at: null,
    photography_allowed: null,
    preferred_contact_method: null,
    primary_sport_head_id: null,
    privacy_settings: null,
    privileges: null,
    registration_source: null,
    sp_balance: null,
    sport_affiliations: null,
    sport_head_id: null,
    terms_accepted: null,
    updated_at: null,
    username: null,
    website: null
  };
  
  // Test helper functions
  const teamId = getTeamIdFromProfile(mockProfile);
  console.log('✅ Helper function result:', teamId);
  
  // Test JSON casting
  const metadata = castJsonField<{ team_id?: string }>(mockProfile.registration_metadata);
  console.log('✅ JSON casting result:', metadata?.team_id);
  
  console.log('✅ Type helpers test completed successfully!');
}

// Test that we can create type-safe insert/update objects
function testInsertUpdateTypes() {
  console.log('🧪 Testing insert/update types...');
  
  // This should compile without errors if types are correct
  const profileInsert: Tables<'profiles'> = {
    id: 'new-user-id',
    full_name: 'New User',
    avatar_url: null,
    registration_metadata: { team_id: 'team-123' },
    age_transition_date: null,
    age_verified: null,
    allergies: null,
    communication_preferences: null,
    consent_records: null,
    context_preferences: null,
    date_of_birth: null,
    display_name: null,
    email_verified: null,
    emergency_contact: null,
    is_minor: null,
    last_accessed_team: null,
    last_login: null,
    location: null,
    marketing_email: null,
    marketing_notifications: null,
    medical_conditions: null,
    nickname: null,
    notification_preferences: null,
    onboarding_completed: null,
    parent_handoff_completed: null,
    parent_id: null,
    personal_invite_code: null,
    phone: null,
    phone_normalised: null,
    phone_verified: null,
    phone_verified_at: null,
    photography_allowed: null,
    preferred_contact_method: null,
    primary_sport_head_id: null,
    privacy_settings: null,
    privileges: null,
    registration_source: null,
    sp_balance: null,
    sport_affiliations: null,
    sport_head_id: null,
    terms_accepted: null,
    updated_at: null,
    username: null,
    website: null
  };
  
  console.log('✅ Insert type validation passed');
  console.log('✅ Insert/update types test completed successfully!');
}

// Main test function
async function runTests() {
  console.log('🚀 Starting typed Supabase client tests...\n');
  
  testTypeHelpers();
  console.log('');
  
  testInsertUpdateTypes();
  console.log('');
  
  await testTypedQueries();
  console.log('');
  
  console.log('🎉 All typed client tests completed!');
  console.log('📋 The Supabase client is properly configured with generated types.');
}

export { runTests };
