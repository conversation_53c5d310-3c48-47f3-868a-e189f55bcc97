#!/bin/bash

# Production deployment script for Content Sync system

echo "🚀 Content Sync Production Deployment"
echo "===================================="

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if running in production mode
if [ "$1" != "--production" ]; then
    echo -e "${YELLOW}This script deploys to PRODUCTION. Use --production flag to confirm.${NC}"
    echo "For testing, use: ./scripts/test-stage2-integration.sh"
    exit 1
fi

# Production checks
echo -e "\n${YELLOW}Pre-deployment checks...${NC}"

# Check if logged in to Supabase
if ! supabase projects list > /dev/null 2>&1; then
    echo -e "${RED}✗ Not logged in to Supabase${NC}"
    echo "Run: supabase login"
    exit 1
fi

# Check if linked to a project
if ! supabase status > /dev/null 2>&1; then
    echo -e "${RED}✗ Not linked to a Supabase project${NC}"
    echo "Run: supabase link --project-ref your-project-ref"
    exit 1
fi

echo -e "${GREEN}✓ Supabase connection verified${NC}"

# Deployment steps
echo -e "\n${BLUE}Starting deployment...${NC}"

# Step 1: Push database migrations
echo -e "\n${YELLOW}1. Applying database migrations...${NC}"
supabase db push

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Database migrations applied${NC}"
else
    echo -e "${RED}✗ Database migration failed${NC}"
    exit 1
fi

# Step 2: Deploy Edge Functions
echo -e "\n${YELLOW}2. Deploying Edge Functions...${NC}"

# Deploy mock-google-api (for testing only - skip in production)
# supabase functions deploy mock-google-api

# Deploy content-sync-v2 function
supabase functions deploy content-sync-v2
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ content-sync-v2 deployed${NC}"
else
    echo -e "${RED}✗ content-sync-v2 deployment failed${NC}"
    exit 1
fi

# Deploy content-api function
supabase functions deploy content-api
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ content-api deployed${NC}"
else
    echo -e "${RED}✗ content-api deployment failed${NC}"
    exit 1
fi

# Step 3: Set up environment variables
echo -e "\n${YELLOW}3. Setting environment variables...${NC}"

# Check if GOOGLE_API_URL is set
if [ -z "$GOOGLE_API_URL" ]; then
    echo -e "${YELLOW}! GOOGLE_API_URL not set. Using mock API.${NC}"
    echo "To use real Google API, set:"
    echo "export GOOGLE_API_URL=https://script.google.com/macros/s/..."
else
    # Set the Google API URL secret
    supabase secrets set GOOGLE_API_URL="$GOOGLE_API_URL"
    echo -e "${GREEN}✓ GOOGLE_API_URL configured${NC}"
fi

# Step 4: Configure cron jobs
echo -e "\n${YELLOW}4. Configuring cron jobs...${NC}"

# Note: Cron jobs are set up via migrations, but we'll verify they exist
CRON_CHECK=$(supabase db query "SELECT jobname FROM cron.job WHERE jobname IN ('sync-pulse-content', 'retry-failed-content-syncs')")

if echo "$CRON_CHECK" | grep -q "sync-pulse-content"; then
    echo -e "${GREEN}✓ Content sync cron job active${NC}"
else
    echo -e "${YELLOW}! Content sync cron job not found${NC}"
fi

if echo "$CRON_CHECK" | grep -q "retry-failed-content-syncs"; then
    echo -e "${GREEN}✓ Retry cron job active${NC}"
else
    echo -e "${YELLOW}! Retry cron job not found${NC}"
fi

# Step 5: Initial sync
echo -e "\n${YELLOW}5. Running initial content sync...${NC}"

# Get the function URL
FUNCTION_URL=$(supabase functions list | grep content-sync-v2 | awk '{print $2}')

if [ -z "$FUNCTION_URL" ]; then
    echo -e "${YELLOW}! Could not determine function URL${NC}"
    echo "Manually trigger sync from the dashboard after deployment"
else
    echo "Triggering sync at: $FUNCTION_URL"
    # Note: In production, you'd use the service role key
    # curl -X POST "$FUNCTION_URL" \
    #   -H "Authorization: Bearer $SERVICE_ROLE_KEY" \
    #   -H "Content-Type: application/json" \
    #   -d '{"source": "deployment"}'
fi

# Step 6: Verification
echo -e "\n${YELLOW}6. Deployment verification...${NC}"

# Check sync logs
RECENT_SYNCS=$(supabase db query "SELECT COUNT(*) as count FROM content.pulse_sync_log WHERE sync_started_at > NOW() - INTERVAL '1 hour'")

echo -e "\n${GREEN}=== Deployment Summary ===${NC}"
echo "✓ Database migrations applied"
echo "✓ Edge Functions deployed"
echo "✓ Cron jobs configured"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Visit the monitoring dashboard: https://your-app.com/v2/admin/content-sync"
echo "2. Verify initial sync completed successfully"
echo "3. Check that articles are loading at: /v2/pulse"
echo "4. Monitor sync health for the first few hours"
echo ""
echo -e "${BLUE}Important URLs:${NC}"
echo "- Monitoring Dashboard: /v2/admin/content-sync"
echo "- New Pulse Page: /v2/pulse"
echo "- Comparison Page: /v2/pulse-comparison"
echo ""
echo -e "${GREEN}Deployment complete!${NC}"

# Production monitoring reminder
echo -e "\n${YELLOW}Set up monitoring alerts:${NC}"
echo "1. Create alert for sync failures"
echo "2. Monitor API response times"
echo "3. Set up error tracking (Sentry, etc.)"
echo "4. Configure uptime monitoring"