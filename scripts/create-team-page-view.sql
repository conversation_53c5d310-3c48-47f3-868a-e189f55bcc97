-- ABOUTME: Creates a comprehensive view for team page data
-- This view combines team, coaches, and player counts to reduce queries

CREATE OR REPLACE VIEW team_page_data AS
SELECT 
    t.id AS team_id,
    t.team_name,
    t.age_group,
    t.sport_type,
    t.team_photo_url,
    t.is_active,
    t.club_id,
    c.club_name,
    c.logo_url AS club_logo_url,
    
    -- Coach count
    (SELECT COUNT(DISTINCT tc.user_id) 
     FROM team_coaches tc 
     WHERE tc.team_id = t.id 
     AND tc.is_active = true) AS coach_count,
    
    -- Active player count
    (SELECT COUNT(DISTINCT tm.user_id) 
     FROM team_members tm 
     WHERE tm.team_id = t.id 
     AND tm.status = 'active') AS active_player_count,
    
    -- Total player count
    (SELECT COUNT(DISTINCT tm.user_id) 
     FROM team_members tm 
     WHERE tm.team_id = t.id) AS total_player_count,
    
    -- Outstanding evaluations count
    (SELECT COUNT(*)
     FROM evaluation_assignments ea
     JOIN events e ON ea.event_id = e.id
     WHERE e.team_id = t.id
     AND ea.status IN ('pending', 'in_progress')
     AND e.deleted_at IS NULL) AS outstanding_evaluations
     
FROM teams t
JOIN clubs c ON t.club_id = c.id
WHERE t.deleted_at IS NULL;