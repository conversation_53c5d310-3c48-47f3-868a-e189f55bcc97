#!/usr/bin/env python3
# ABOUTME: Script to check if required RPC functions exist in the database
# Verifies cart_upsert, rpc_get_cart_session, and rpc_cart_exists functions

import requests
import json

# Database connection details
SUPABASE_URL = 'https://ovfwiyqhubxeqvbrggbe.supabase.co'
SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y'

headers = {
    'apikey': SERVICE_ROLE_KEY,
    'Authorization': f'Bearer {SERVICE_ROLE_KEY}',
    'Content-Type': 'application/json'
}

print("Checking RPC Functions for Cart Operations")
print("="*60)

# Test RPC functions
rpc_functions = [
    {
        'name': 'cart_upsert',
        'payload': {
            'session_id_param': 'test_session_123',
            'items_param': [],
            'profile_id_param': None
        }
    },
    {
        'name': 'rpc_get_cart_session',
        'payload': {
            'p_session_id': 'test_session_123'
        }
    },
    {
        'name': 'rpc_cart_exists',
        'payload': {
            'p_session_id': 'test_session_123'
        }
    }
]

for func in rpc_functions:
    print(f"\nTesting RPC function: {func['name']}")
    print("-"*40)
    
    response = requests.post(
        f"{SUPABASE_URL}/rest/v1/rpc/{func['name']}",
        headers=headers,
        json=func['payload']
    )
    
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        print("✓ Function exists and is callable")
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2)}")
    elif response.status_code == 404:
        print("✗ Function does not exist")
    else:
        print(f"✗ Error: {response.status_code}")
        print(f"Response: {response.text}")

# Also check the cart_sessions table
print("\n\nChecking cart_sessions table")
print("-"*40)

table_response = requests.get(
    f"{SUPABASE_URL}/rest/v1/cart_sessions",
    headers=headers,
    params={'limit': 1}
)

if table_response.status_code == 200:
    print("✓ cart_sessions table exists")
    data = table_response.json()
    if data:
        print(f"Columns: {', '.join(data[0].keys())}")
else:
    print(f"✗ cart_sessions table not found or error: {table_response.status_code}")
    print(f"Response: {table_response.text}")

print("\n" + "="*60)