#!/usr/bin/env node

// ABOUTME: Script to migrate Shadow component imports to new design system
// Run with: node scripts/migrate-shadow-imports.js [--dry-run]

const fs = require('fs');
const path = require('path');

// Component migration map
const MIGRATION_MAP = {
  // Atoms
  "from '@/components/shadow/ShadowButton'": "from '@/foundation/design-system/components/atoms/Button'",
  "from '../components/shadow/ShadowButton'": "from '@/foundation/design-system/components/atoms/Button'",
  "from '../../components/shadow/ShadowButton'": "from '@/foundation/design-system/components/atoms/Button'",
  "from './components/shadow/ShadowButton'": "from '@/foundation/design-system/components/atoms/Button'",
  
  "from '@/components/shadow/ShadowAvatar'": "from '@/foundation/design-system/components/atoms/Avatar'",
  "from '../components/shadow/ShadowAvatar'": "from '@/foundation/design-system/components/atoms/Avatar'",
  "from '../../components/shadow/ShadowAvatar'": "from '@/foundation/design-system/components/atoms/Avatar'",
  "from './components/shadow/ShadowAvatar'": "from '@/foundation/design-system/components/atoms/Avatar'",
  
  "from '@/components/shadow/ShadowStatusBadge'": "from '@/foundation/design-system/components/atoms/Badge'",
  "from '../components/shadow/ShadowStatusBadge'": "from '@/foundation/design-system/components/atoms/Badge'",
  "from '../../components/shadow/ShadowStatusBadge'": "from '@/foundation/design-system/components/atoms/Badge'",
  "from './components/shadow/ShadowStatusBadge'": "from '@/foundation/design-system/components/atoms/Badge'",
  
  "from '@/components/shadow/ShadowToast'": "from '@/foundation/design-system/components/atoms/Toast'",
  "from '../components/shadow/ShadowToast'": "from '@/foundation/design-system/components/atoms/Toast'",
  "from '../../components/shadow/ShadowToast'": "from '@/foundation/design-system/components/atoms/Toast'",
  "from './components/shadow/ShadowToast'": "from '@/foundation/design-system/components/atoms/Toast'",
  
  // Molecules
  "from '@/components/shadow/ShadowModal'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../components/shadow/ShadowModal'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../../components/shadow/ShadowModal'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from './components/shadow/ShadowModal'": "from '@/foundation/design-system/components/molecules/Modals'",
  
  // Colors
  "from '@/styles/shotColors'": "from '@/foundation/design-system/tokens/colors'",
  "from '../styles/shotColors'": "from '@/foundation/design-system/tokens/colors'",
  "from '../../styles/shotColors'": "from '@/foundation/design-system/tokens/colors'",
  "from './styles/shotColors'": "from '@/foundation/design-system/tokens/colors'",
  
  // Named imports need special handling
  "{ SHOT_COLORS }": "{ colors as SHOT_COLORS }",
  "{ getButtonStyle }": "{ createButtonStyles as getButtonStyle }",
};

// Parse command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');

console.log(`🔄 Shadow Component Import Migration Script`);
console.log(`Mode: ${isDryRun ? 'DRY RUN' : 'LIVE'}\n`);

// Recursive function to find all TypeScript/JavaScript files
function findFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Skip certain directories
      if (!['node_modules', '.git', 'dist', 'build', '_deprecated', 'foundation'].includes(file)) {
        findFiles(filePath, fileList);
      }
    } else if (file.match(/\.(ts|tsx|js|jsx)$/)) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Find all TypeScript/JavaScript files
const files = findFiles('src');
console.log(`Found ${files.length} files to check\n`);

let totalChanges = 0;
let filesChanged = 0;

files.forEach(filePath => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let newContent = content;
    let changes = 0;
    
    // Apply each migration rule
    Object.entries(MIGRATION_MAP).forEach(([oldImport, newImport]) => {
      const regex = new RegExp(escapeRegExp(oldImport), 'g');
      const matches = content.match(regex);
      
      if (matches) {
        newContent = newContent.replace(regex, newImport);
        changes += matches.length;
      }
    });
    
    // Handle imports that might have quotes variations
    newContent = newContent.replace(
      /from ["']([.\/]*components\/shadow\/Shadow\w+)["']/g,
      (match, importPath) => {
        const componentMatch = importPath.match(/Shadow(\w+)$/);
        if (componentMatch) {
          const componentName = 'Shadow' + componentMatch[1];
          const newPath = getNewPathForComponent(componentName);
          if (newPath) {
            return `from '${newPath}'`;
          }
        }
        return match;
      }
    );
    
    if (newContent !== content) {
      changes = (content.match(/from ["'][^"']*shadow[^"']*["']/g) || []).length;
    }
    
    if (changes > 0) {
      console.log(`📝 ${filePath}: ${changes} import(s) to update`);
      
      if (!isDryRun) {
        fs.writeFileSync(filePath, newContent, 'utf8');
      }
      
      totalChanges += changes;
      filesChanged++;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log(`\n✅ Migration ${isDryRun ? 'would update' : 'complete'}`);
console.log(`Files ${isDryRun ? 'to be changed' : 'changed'}: ${filesChanged}`);
console.log(`Total imports ${isDryRun ? 'to be updated' : 'updated'}: ${totalChanges}`);

if (isDryRun) {
  console.log(`\nRun without --dry-run to apply changes`);
}

// Helper functions
function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function getNewPathForComponent(componentName) {
  const componentMap = {
    // Atoms
    'ShadowButton': '@/foundation/design-system/components/atoms/Button',
    'ShadowAvatar': '@/foundation/design-system/components/atoms/Avatar',
    'ShadowStatusBadge': '@/foundation/design-system/components/atoms/Badge',
    'ShadowToast': '@/foundation/design-system/components/atoms/Toast',
    // Molecules
    'ShadowModal': '@/foundation/design-system/components/molecules/Modals',
  };
  
  return componentMap[componentName] || null;
}