#!/usr/bin/env node

// ABOUTME: Script to migrate Shadow component imports to new design system
// Run with: node scripts/migrate-shadow-imports.mjs [--dry-run]

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Component migration map
const MIGRATION_MAP = {
  // Atoms
  "from '@/components/shadow/ShadowButton'": "from '@/foundation/design-system/components/atoms/Button'",
  "from '../components/shadow/ShadowButton'": "from '@/foundation/design-system/components/atoms/Button'",
  "from '../../components/shadow/ShadowButton'": "from '@/foundation/design-system/components/atoms/Button'",
  "from './components/shadow/ShadowButton'": "from '@/foundation/design-system/components/atoms/Button'",
  
  "from '@/components/shadow/ShadowAvatar'": "from '@/foundation/design-system/components/atoms/Avatar'",
  "from '../components/shadow/ShadowAvatar'": "from '@/foundation/design-system/components/atoms/Avatar'",
  "from '../../components/shadow/ShadowAvatar'": "from '@/foundation/design-system/components/atoms/Avatar'",
  "from './components/shadow/ShadowAvatar'": "from '@/foundation/design-system/components/atoms/Avatar'",
  
  "from '@/components/shadow/ShadowStatusBadge'": "from '@/foundation/design-system/components/atoms/Badge'",
  "from '../components/shadow/ShadowStatusBadge'": "from '@/foundation/design-system/components/atoms/Badge'",
  "from '../../components/shadow/ShadowStatusBadge'": "from '@/foundation/design-system/components/atoms/Badge'",
  "from './components/shadow/ShadowStatusBadge'": "from '@/foundation/design-system/components/atoms/Badge'",
  
  "from '@/components/shadow/ShadowToast'": "from '@/foundation/design-system/components/atoms/Toast'",
  "from '../components/shadow/ShadowToast'": "from '@/foundation/design-system/components/atoms/Toast'",
  "from '../../components/shadow/ShadowToast'": "from '@/foundation/design-system/components/atoms/Toast'",
  "from './components/shadow/ShadowToast'": "from '@/foundation/design-system/components/atoms/Toast'",
  
  // Atoms - Buttons
  "from '@/components/shadow/ShadowBackButton'": "from '@/foundation/design-system/components/atoms/Button'",
  "from '../components/shadow/ShadowBackButton'": "from '@/foundation/design-system/components/atoms/Button'",
  "from '../../components/shadow/ShadowBackButton'": "from '@/foundation/design-system/components/atoms/Button'",
  "from '../../../components/shadow/ShadowBackButton'": "from '@/foundation/design-system/components/atoms/Button'",
  "from '../../../../components/shadow/ShadowBackButton'": "from '@/foundation/design-system/components/atoms/Button'",
  "from '../../../../../components/shadow/ShadowBackButton'": "from '@/foundation/design-system/components/atoms/Button'",
  
  // Molecules
  "from '@/components/shadow/ShadowModal'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../components/shadow/ShadowModal'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../../components/shadow/ShadowModal'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from './components/shadow/ShadowModal'": "from '@/foundation/design-system/components/molecules/Modals'",
  
  "from '@/components/shadow/ShadowInfoCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../components/shadow/ShadowInfoCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../components/shadow/ShadowInfoCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from './components/shadow/ShadowInfoCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  
  // Molecules - Cards
  "from '@/components/shadow/ShadowStatCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../components/shadow/ShadowStatCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../components/shadow/ShadowStatCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../../components/shadow/ShadowStatCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../../../components/shadow/ShadowStatCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../../../../components/shadow/ShadowStatCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  
  // Molecules - Headers
  "from '@/components/shadow/ShadowSectionHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  "from '../components/shadow/ShadowSectionHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  "from '../../components/shadow/ShadowSectionHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  "from '../../../components/shadow/ShadowSectionHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  "from '../../../../components/shadow/ShadowSectionHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  "from '../../../../../components/shadow/ShadowSectionHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  
  "from '@/components/shadow/ShadowHeaderSubHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  "from '../components/shadow/ShadowHeaderSubHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  "from '../../components/shadow/ShadowHeaderSubHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  "from '../../../components/shadow/ShadowHeaderSubHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  "from '../../../../components/shadow/ShadowHeaderSubHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  "from '../../../../../components/shadow/ShadowHeaderSubHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  
  // Organisms
  "from '@/components/shadow/ShadowNavigationDrawer'": "from '@/foundation/design-system/components/organisms/Navigation'",
  "from '../components/shadow/ShadowNavigationDrawer'": "from '@/foundation/design-system/components/organisms/Navigation'",
  "from '../../components/shadow/ShadowNavigationDrawer'": "from '@/foundation/design-system/components/organisms/Navigation'",
  "from '../../../components/shadow/ShadowNavigationDrawer'": "from '@/foundation/design-system/components/organisms/Navigation'",
  "from '../../../../components/shadow/ShadowNavigationDrawer'": "from '@/foundation/design-system/components/organisms/Navigation'",
  "from '../../../../../components/shadow/ShadowNavigationDrawer'": "from '@/foundation/design-system/components/organisms/Navigation'",
  
  "from '@/components/shadow/ShadowAccountSwitcher'": "from '@/foundation/design-system/components/organisms/Account'",
  "from '../components/shadow/ShadowAccountSwitcher'": "from '@/foundation/design-system/components/organisms/Account'",
  "from '../../components/shadow/ShadowAccountSwitcher'": "from '@/foundation/design-system/components/organisms/Account'",
  "from '../../../components/shadow/ShadowAccountSwitcher'": "from '@/foundation/design-system/components/organisms/Account'",
  "from '../../../../components/shadow/ShadowAccountSwitcher'": "from '@/foundation/design-system/components/organisms/Account'",
  "from '../../../../../components/shadow/ShadowAccountSwitcher'": "from '@/foundation/design-system/components/organisms/Account'",
  
  // Molecules - More
  "from '@/components/shadow/ShadowNotificationModal'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../components/shadow/ShadowNotificationModal'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../../components/shadow/ShadowNotificationModal'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../../../components/shadow/ShadowNotificationModal'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../../../../components/shadow/ShadowNotificationModal'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../../../../../components/shadow/ShadowNotificationModal'": "from '@/foundation/design-system/components/molecules/Modals'",
  
  "from '@/components/shadow/ShadowIconHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  "from '../components/shadow/ShadowIconHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  "from '../../components/shadow/ShadowIconHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  "from '../../../components/shadow/ShadowIconHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  "from '../../../../components/shadow/ShadowIconHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  "from '../../../../../components/shadow/ShadowIconHeader'": "from '@/foundation/design-system/components/molecules/Headers'",
  
  "from '@/components/shadow/ShadowActionGrid'": "from '@/foundation/design-system/components/molecules/Navigation'",
  "from '../components/shadow/ShadowActionGrid'": "from '@/foundation/design-system/components/molecules/Navigation'",
  "from '../../components/shadow/ShadowActionGrid'": "from '@/foundation/design-system/components/molecules/Navigation'",
  "from '../../../components/shadow/ShadowActionGrid'": "from '@/foundation/design-system/components/molecules/Navigation'",
  "from '../../../../components/shadow/ShadowActionGrid'": "from '@/foundation/design-system/components/molecules/Navigation'",
  "from '../../../../../components/shadow/ShadowActionGrid'": "from '@/foundation/design-system/components/molecules/Navigation'",
  
  "from '@/components/shadow/ShadowActionCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../components/shadow/ShadowActionCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../components/shadow/ShadowActionCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../../components/shadow/ShadowActionCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../../../components/shadow/ShadowActionCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../../../../components/shadow/ShadowActionCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  
  // New migrated components
  "from '@/components/shadow/ShadowHorizontalScroller'": "from '@/foundation/design-system/components/molecules/Lists'",
  "from '../components/shadow/ShadowHorizontalScroller'": "from '@/foundation/design-system/components/molecules/Lists'",
  "from '../../components/shadow/ShadowHorizontalScroller'": "from '@/foundation/design-system/components/molecules/Lists'",
  "from '../../../components/shadow/ShadowHorizontalScroller'": "from '@/foundation/design-system/components/molecules/Lists'",
  "from '../../../../components/shadow/ShadowHorizontalScroller'": "from '@/foundation/design-system/components/molecules/Lists'",
  "from '../../../../../components/shadow/ShadowHorizontalScroller'": "from '@/foundation/design-system/components/molecules/Lists'",
  
  "from '@/components/shadow/ShadowModalEnhanced'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../components/shadow/ShadowModalEnhanced'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../../components/shadow/ShadowModalEnhanced'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../../../components/shadow/ShadowModalEnhanced'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../../../../components/shadow/ShadowModalEnhanced'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../../../../../components/shadow/ShadowModalEnhanced'": "from '@/foundation/design-system/components/molecules/Modals'",
  
  "from '@/components/shadow/ShadowNavigationDrawerEnhanced'": "from '@/foundation/design-system/components/molecules/Navigation'",
  "from '../components/shadow/ShadowNavigationDrawerEnhanced'": "from '@/foundation/design-system/components/molecules/Navigation'",
  "from '../../components/shadow/ShadowNavigationDrawerEnhanced'": "from '@/foundation/design-system/components/molecules/Navigation'",
  "from '../../../components/shadow/ShadowNavigationDrawerEnhanced'": "from '@/foundation/design-system/components/molecules/Navigation'",
  "from '../../../../components/shadow/ShadowNavigationDrawerEnhanced'": "from '@/foundation/design-system/components/molecules/Navigation'",
  "from '../../../../../components/shadow/ShadowNavigationDrawerEnhanced'": "from '@/foundation/design-system/components/molecules/Navigation'",
  
  "from '@/components/shadow/ShadowEventCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../components/shadow/ShadowEventCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../components/shadow/ShadowEventCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../../components/shadow/ShadowEventCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../../../components/shadow/ShadowEventCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../../../../components/shadow/ShadowEventCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  
  "from '@/components/shadow/ShadowModalWithContent'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../components/shadow/ShadowModalWithContent'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../../components/shadow/ShadowModalWithContent'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../../../components/shadow/ShadowModalWithContent'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../../../../components/shadow/ShadowModalWithContent'": "from '@/foundation/design-system/components/molecules/Modals'",
  "from '../../../../../components/shadow/ShadowModalWithContent'": "from '@/foundation/design-system/components/molecules/Modals'",
  
  // Form components
  "from '@/components/shadow/form/ShadowTextInput'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../components/shadow/form/ShadowTextInput'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../components/shadow/form/ShadowTextInput'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../components/shadow/form/ShadowTextInput'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../components/shadow/form/ShadowTextInput'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../../components/shadow/form/ShadowTextInput'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../../../components/shadow/form/ShadowTextInput'": "from '@/foundation/design-system/components/atoms/Input'",
  
  "from '@/components/shadow/form/ShadowTextarea'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../components/shadow/form/ShadowTextarea'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../components/shadow/form/ShadowTextarea'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../components/shadow/form/ShadowTextarea'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../components/shadow/form/ShadowTextarea'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../../components/shadow/form/ShadowTextarea'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../../../components/shadow/form/ShadowTextarea'": "from '@/foundation/design-system/components/atoms/Input'",
  
  "from '@/components/shadow/form/ShadowSelect'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../components/shadow/form/ShadowSelect'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../components/shadow/form/ShadowSelect'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../components/shadow/form/ShadowSelect'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../components/shadow/form/ShadowSelect'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../../components/shadow/form/ShadowSelect'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../../../components/shadow/form/ShadowSelect'": "from '@/foundation/design-system/components/atoms/Input'",
  
  // Player list components
  "from '@/components/shadow/players/ShadowPlayersList'": "from '@/foundation/design-system/components/molecules/Lists'",
  "from '../components/shadow/players/ShadowPlayersList'": "from '@/foundation/design-system/components/molecules/Lists'",
  "from '../../components/shadow/players/ShadowPlayersList'": "from '@/foundation/design-system/components/molecules/Lists'",
  "from '../../../components/shadow/players/ShadowPlayersList'": "from '@/foundation/design-system/components/molecules/Lists'",
  "from '../../../../components/shadow/players/ShadowPlayersList'": "from '@/foundation/design-system/components/molecules/Lists'",
  "from '../../../../../components/shadow/players/ShadowPlayersList'": "from '@/foundation/design-system/components/molecules/Lists'",
  "from '../../../../../../components/shadow/players/ShadowPlayersList'": "from '@/foundation/design-system/components/molecules/Lists'",
  
  // Commerce components
  "from '@/components/shadow/commerce/ShadowProductCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../components/shadow/commerce/ShadowProductCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../components/shadow/commerce/ShadowProductCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../../components/shadow/commerce/ShadowProductCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../../../components/shadow/commerce/ShadowProductCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  "from '../../../../../components/shadow/commerce/ShadowProductCard'": "from '@/foundation/design-system/components/molecules/Cards'",
  
  // Navigation components
  "from '@/components/shadow/ShadowCollapsibleDrawer'": "from '@/foundation/design-system/components/molecules/Navigation'",
  "from '../components/shadow/ShadowCollapsibleDrawer'": "from '@/foundation/design-system/components/molecules/Navigation'",
  "from '../../components/shadow/ShadowCollapsibleDrawer'": "from '@/foundation/design-system/components/molecules/Navigation'",
  "from '../../../components/shadow/ShadowCollapsibleDrawer'": "from '@/foundation/design-system/components/molecules/Navigation'",
  "from '../../../../components/shadow/ShadowCollapsibleDrawer'": "from '@/foundation/design-system/components/molecules/Navigation'",
  "from '../../../../../components/shadow/ShadowCollapsibleDrawer'": "from '@/foundation/design-system/components/molecules/Navigation'",
  
  // Display components
  "from '@/components/shadow/ShadowCircleTeaser'": "from '@/foundation/design-system/components/atoms/Display'",
  "from '../components/shadow/ShadowCircleTeaser'": "from '@/foundation/design-system/components/atoms/Display'",
  "from '../../components/shadow/ShadowCircleTeaser'": "from '@/foundation/design-system/components/atoms/Display'",
  "from '../../../components/shadow/ShadowCircleTeaser'": "from '@/foundation/design-system/components/atoms/Display'",
  "from '../../../../components/shadow/ShadowCircleTeaser'": "from '@/foundation/design-system/components/atoms/Display'",
  "from '../../../../../components/shadow/ShadowCircleTeaser'": "from '@/foundation/design-system/components/atoms/Display'",
  
  // Form components - Additional
  "from '@/components/shadow/form/ShadowCheckbox'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../components/shadow/form/ShadowCheckbox'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../components/shadow/form/ShadowCheckbox'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../components/shadow/form/ShadowCheckbox'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../components/shadow/form/ShadowCheckbox'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../../components/shadow/form/ShadowCheckbox'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../../../components/shadow/form/ShadowCheckbox'": "from '@/foundation/design-system/components/atoms/Input'",
  
  "from '@/components/shadow/form/ShadowFormGroup'": "from '@/foundation/design-system/components/molecules/Forms'",
  "from '../components/shadow/form/ShadowFormGroup'": "from '@/foundation/design-system/components/molecules/Forms'",
  "from '../../components/shadow/form/ShadowFormGroup'": "from '@/foundation/design-system/components/molecules/Forms'",
  "from '../../../components/shadow/form/ShadowFormGroup'": "from '@/foundation/design-system/components/molecules/Forms'",
  "from '../../../../components/shadow/form/ShadowFormGroup'": "from '@/foundation/design-system/components/molecules/Forms'",
  "from '../../../../../components/shadow/form/ShadowFormGroup'": "from '@/foundation/design-system/components/molecules/Forms'",
  "from '../../../../../../components/shadow/form/ShadowFormGroup'": "from '@/foundation/design-system/components/molecules/Forms'",
  
  // Form components - Radio and specialized inputs
  "from '@/components/shadow/form/ShadowRadioGroup'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../components/shadow/form/ShadowRadioGroup'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../components/shadow/form/ShadowRadioGroup'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../components/shadow/form/ShadowRadioGroup'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../components/shadow/form/ShadowRadioGroup'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../../components/shadow/form/ShadowRadioGroup'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../../../components/shadow/form/ShadowRadioGroup'": "from '@/foundation/design-system/components/atoms/Input'",
  
  "from '@/components/shadow/form/ShadowSportSelector'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../components/shadow/form/ShadowSportSelector'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../components/shadow/form/ShadowSportSelector'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../components/shadow/form/ShadowSportSelector'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../components/shadow/form/ShadowSportSelector'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../../components/shadow/form/ShadowSportSelector'": "from '@/foundation/design-system/components/atoms/Input'",
  "from '../../../../../../components/shadow/form/ShadowSportSelector'": "from '@/foundation/design-system/components/atoms/Input'",
  
  "from '@/components/shadow/form/ShadowAddressFormEnhanced'": "from '@/foundation/design-system/components/molecules/Forms'",
  "from '../components/shadow/form/ShadowAddressFormEnhanced'": "from '@/foundation/design-system/components/molecules/Forms'",
  "from '../../components/shadow/form/ShadowAddressFormEnhanced'": "from '@/foundation/design-system/components/molecules/Forms'",
  "from '../../../components/shadow/form/ShadowAddressFormEnhanced'": "from '@/foundation/design-system/components/molecules/Forms'",
  "from '../../../../components/shadow/form/ShadowAddressFormEnhanced'": "from '@/foundation/design-system/components/molecules/Forms'",
  "from '../../../../../components/shadow/form/ShadowAddressFormEnhanced'": "from '@/foundation/design-system/components/molecules/Forms'",
  "from '../../../../../../components/shadow/form/ShadowAddressFormEnhanced'": "from '@/foundation/design-system/components/molecules/Forms'",
  
  // Colors
  "from '@/styles/shotColors'": "from '@/foundation/design-system/tokens/colors'",
  "from '../styles/shotColors'": "from '@/foundation/design-system/tokens/colors'",
  "from '../../styles/shotColors'": "from '@/foundation/design-system/tokens/colors'",
  "from './styles/shotColors'": "from '@/foundation/design-system/tokens/colors'",
  
  // Named imports need special handling
  "{ SHOT_COLORS }": "{ colors as SHOT_COLORS }",
  "{ getButtonStyle }": "{ createButtonStyles as getButtonStyle }",
};

// Parse command line arguments
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run');

console.log(`🔄 Shadow Component Import Migration Script`);
console.log(`Mode: ${isDryRun ? 'DRY RUN' : 'LIVE'}\n`);

// Recursive function to find all TypeScript/JavaScript files
function findFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Skip certain directories
      if (!['node_modules', '.git', 'dist', 'build', '_deprecated', 'foundation'].includes(file)) {
        findFiles(filePath, fileList);
      }
    } else if (file.match(/\.(ts|tsx|js|jsx)$/)) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Find all TypeScript/JavaScript files
const files = findFiles('src');
console.log(`Found ${files.length} files to check\n`);

let totalChanges = 0;
let filesChanged = 0;

files.forEach(filePath => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let newContent = content;
    let changes = 0;
    
    // Apply each migration rule
    Object.entries(MIGRATION_MAP).forEach(([oldImport, newImport]) => {
      const regex = new RegExp(escapeRegExp(oldImport), 'g');
      const matches = content.match(regex);
      
      if (matches) {
        newContent = newContent.replace(regex, newImport);
        changes += matches.length;
      }
    });
    
    // Handle imports that might have quotes variations
    newContent = newContent.replace(
      /from ["']([.\/]*components\/shadow\/Shadow\w+)["']/g,
      (match, importPath) => {
        const componentMatch = importPath.match(/Shadow(\w+)$/);
        if (componentMatch) {
          const componentName = 'Shadow' + componentMatch[1];
          const newPath = getNewPathForComponent(componentName);
          if (newPath) {
            return `from '${newPath}'`;
          }
        }
        return match;
      }
    );
    
    if (newContent !== content) {
      changes = (content.match(/from ["'][^"']*shadow[^"']*["']/g) || []).length;
    }
    
    if (changes > 0) {
      console.log(`📝 ${filePath}: ${changes} import(s) to update`);
      
      if (!isDryRun) {
        fs.writeFileSync(filePath, newContent, 'utf8');
      }
      
      totalChanges += changes;
      filesChanged++;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
});

console.log(`\n✅ Migration ${isDryRun ? 'would update' : 'complete'}`);
console.log(`Files ${isDryRun ? 'to be changed' : 'changed'}: ${filesChanged}`);
console.log(`Total imports ${isDryRun ? 'to be updated' : 'updated'}: ${totalChanges}`);

if (isDryRun) {
  console.log(`\nRun without --dry-run to apply changes`);
}

// Helper functions
function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function getNewPathForComponent(componentName) {
  const componentMap = {
    // Atoms
    'ShadowButton': '@/foundation/design-system/components/atoms/Button',
    'ShadowAvatar': '@/foundation/design-system/components/atoms/Avatar',
    'ShadowStatusBadge': '@/foundation/design-system/components/atoms/Badge',
    'ShadowToast': '@/foundation/design-system/components/atoms/Toast',
    // Molecules
    'ShadowModal': '@/foundation/design-system/components/molecules/Modals',
    'ShadowInfoCard': '@/foundation/design-system/components/molecules/Cards',
  };
  
  return componentMap[componentName] || null;
}