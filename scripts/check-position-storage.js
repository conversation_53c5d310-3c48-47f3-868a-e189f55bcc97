// ABOUTME: Script to find where player positions are stored for events
// This helps us understand how to get positions when creating pre-evaluations

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkPositionStorage() {
  console.log('=== CHECKING POSITION STORAGE ===\n');

  const eventId = '8b405fdf-093a-48c0-ac62-800ec68d99c7';
  const teamId = '2dd31c63-ef3a-4c86-b5f2-fc7ce24fc352';

  // 1. Check event participants
  console.log('1. EVENT_PARTICIPANTS TABLE:');
  console.log('----------------------------');
  try {
    const { data: participants, error } = await supabase
      .from('event_participants')
      .select('*')
      .eq('event_id', eventId)
      .limit(3);
    
    if (error) throw error;
    
    console.log('Sample participant columns:', participants.length > 0 ? Object.keys(participants[0]) : 'No data');
    console.log('Participants:', participants.map(p => ({ user_id: p.user_id, participant_id: p.participant_id })));
  } catch (error) {
    console.error('Error:', error.message);
  }

  // 2. Check team members positions
  console.log('\n\n2. TEAM_MEMBERS POSITIONS:');
  console.log('--------------------------');
  try {
    // Get participants for the event
    const { data: participants, error: pError } = await supabase
      .from('event_participants')
      .select('user_id')
      .eq('event_id', eventId);
    
    if (pError) throw pError;
    
    const userIds = participants.map(p => p.user_id);
    
    // Get their positions from team_members
    const { data: teamMembers, error: tmError } = await supabase
      .from('team_members')
      .select('user_id, position')
      .eq('team_id', teamId)
      .in('user_id', userIds);
    
    if (tmError) throw tmError;
    
    console.log('Team member positions:');
    teamMembers.forEach(tm => {
      console.log(`  User ${tm.user_id}: ${tm.position || 'NO POSITION'}`);
    });
  } catch (error) {
    console.error('Error:', error.message);
  }

  // 3. Check how pre-evaluations link to positions
  console.log('\n\n3. EXISTING PRE-EVALUATIONS WITH POSITIONS:');
  console.log('-------------------------------------------');
  try {
    const { data: preEvals, error } = await supabase
      .from('pre_evaluations')
      .select('id, player_id, player_position, event_id')
      .eq('event_id', eventId)
      .limit(5);
    
    if (error) throw error;
    
    console.log('Pre-evaluations for this event:');
    preEvals.forEach(pe => {
      console.log(`  Player ${pe.player_id}: Position = ${pe.player_position || 'NULL'}`);
    });
  } catch (error) {
    console.error('Error:', error.message);
  }

  // 4. Check how player_evaluations are created for pre-evaluations
  console.log('\n\n4. HOW PLAYER_EVALUATIONS ARE CREATED:');
  console.log('--------------------------------------');
  try {
    // Check if there's a pattern in existing player_evaluations
    const { data: playerEvals, error } = await supabase
      .from('player_evaluations')
      .select('player_id, position, category, pre_evaluation_id')
      .not('pre_evaluation_id', 'is', null)
      .eq('event_id', eventId)
      .limit(20);
    
    if (error) throw error;
    
    // Group by player to see what categories they get
    const playerCategories = {};
    playerEvals.forEach(pe => {
      if (!playerCategories[pe.player_id]) {
        playerCategories[pe.player_id] = {
          position: pe.position,
          categories: new Set()
        };
      }
      playerCategories[pe.player_id].categories.add(pe.category);
    });
    
    console.log('Player evaluation patterns:');
    Object.entries(playerCategories).forEach(([playerId, data]) => {
      console.log(`  Player ${playerId}: Position=${data.position}, Categories=${Array.from(data.categories).join(', ')}`);
    });
  } catch (error) {
    console.error('Error:', error.message);
  }

  process.exit(0);
}

checkPositionStorage();