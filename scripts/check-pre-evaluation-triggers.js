// ABOUTME: Script to find triggers related to pre-evaluations and player_evaluations
// This helps us understand how player_evaluations are created

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTriggers() {
  console.log('=== CHECKING DATABASE TRIGGERS ===\n');

  // Check for triggers on pre_evaluations table using pg_trigger
  console.log('1. TRIGGERS ON pre_evaluations TABLE:');
  console.log('-------------------------------------');
  try {
    // Direct SQL query to get triggers
    const { data, error } = await supabase.rpc('execute_sql', {
      query: `
        SELECT 
          t.tgname as trigger_name,
          pg_get_triggerdef(t.oid) as trigger_definition
        FROM pg_trigger t
        JOIN pg_class c ON t.tgrelid = c.oid
        WHERE c.relname = 'pre_evaluations'
          AND t.tgisinternal = false
      `
    });
    
    if (error) {
      // Try alternative approach
      console.log('Could not fetch triggers directly, trying alternative...');
      
      // Check recent pre-evaluations and their related player_evaluations
      const { data: preEvals, error: preError } = await supabase
        .from('pre_evaluations')
        .select('id, created_at, player_id, player_position')
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (!preError && preEvals) {
        console.log('\nRecent pre-evaluations:');
        for (const pe of preEvals) {
          console.log(`\nPre-eval ${pe.id}:`);
          console.log(`  Created: ${pe.created_at}`);
          console.log(`  Player: ${pe.player_id}`);
          console.log(`  Position: ${pe.player_position || 'NULL'}`);
          
          // Check for related player_evaluations
          const { data: playerEvals, error: peError } = await supabase
            .from('player_evaluations')
            .select('id, category, position, created_at')
            .eq('pre_evaluation_id', pe.id)
            .order('category');
          
          if (!peError && playerEvals) {
            console.log(`  Player evaluations created: ${playerEvals.length}`);
            if (playerEvals.length > 0) {
              console.log(`  Categories: ${playerEvals.map(pe => pe.category).join(', ')}`);
              console.log(`  Position in evaluations: ${playerEvals[0].position || 'NULL'}`);
            }
          }
        }
      }
    } else {
      console.log('Triggers found:', data);
    }
  } catch (error) {
    console.error('Error checking triggers:', error.message);
  }

  // Check how player_evaluations are created
  console.log('\n\n2. PLAYER_EVALUATIONS CREATION PATTERN:');
  console.log('---------------------------------------');
  try {
    // Find player_evaluations that have pre_evaluation_id
    const { data: evalPattern, error } = await supabase
      .from('player_evaluations')
      .select('player_id, pre_evaluation_id, category, position, created_at')
      .not('pre_evaluation_id', 'is', null)
      .order('created_at', { ascending: false })
      .limit(20);
    
    if (error) throw error;
    
    // Group by pre_evaluation_id to see the pattern
    const grouped = {};
    evalPattern.forEach(ev => {
      if (!grouped[ev.pre_evaluation_id]) {
        grouped[ev.pre_evaluation_id] = {
          categories: new Set(),
          position: ev.position,
          created_at: ev.created_at
        };
      }
      grouped[ev.pre_evaluation_id].categories.add(ev.category);
    });
    
    console.log('Pre-evaluation to player_evaluations pattern:');
    Object.entries(grouped).slice(0, 3).forEach(([preEvalId, data]) => {
      console.log(`\nPre-eval ${preEvalId}:`);
      console.log(`  Categories created: ${Array.from(data.categories).join(', ')}`);
      console.log(`  Position used: ${data.position || 'NULL'}`);
      console.log(`  Created at: ${data.created_at}`);
    });
  } catch (error) {
    console.error('Error checking pattern:', error.message);
  }

  // Check if there's a function that creates player_evaluations
  console.log('\n\n3. CHECKING FOR FUNCTIONS:');
  console.log('--------------------------');
  try {
    // Look for pre-evaluation in existing player_evaluations to understand the pattern
    const { data: sample, error } = await supabase
      .from('player_evaluations')
      .select('*')
      .not('pre_evaluation_id', 'is', null)
      .eq('category', 'TECHNICAL')
      .limit(1)
      .single();
    
    if (!error && sample) {
      console.log('Sample player_evaluation with pre_evaluation_id:');
      console.log('  Framework version:', sample.framework_version);
      console.log('  Position:', sample.position);
      console.log('  Week number:', sample.week_number);
      console.log('  Question:', sample.question?.substring(0, 50) + '...');
    }
  } catch (error) {
    console.error('Error checking sample:', error.message);
  }

  process.exit(0);
}

checkTriggers();