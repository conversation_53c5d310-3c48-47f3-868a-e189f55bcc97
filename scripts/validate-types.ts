/**
 * Database Types Validation Script
 * 
 * This script validates that the generated database types work correctly
 * and can be used for type-safe database operations.
 */

import type { Database } from '../src/types/database';

// Test that we can extract table types
type ProfilesTable = Database['public']['Tables']['profiles'];
type ProfileRow = ProfilesTable['Row'];
type ProfileInsert = ProfilesTable['Insert'];
type ProfileUpdate = ProfilesTable['Update'];

// Test that specific fields exist and have correct types
type RegistrationMetadata = ProfileRow['registration_metadata']; // Should be Json | null
type FullName = ProfileRow['full_name']; // Should be string | null
type Id = ProfileRow['id']; // Should be string

// Test teams table
type TeamsTable = Database['public']['Tables']['teams'];
type TeamRow = TeamsTable['Row'];
type TeamName = TeamRow['team_name']; // Should be string | null

// Test that we can create type-safe query helpers
type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
type InsertTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert'];
type UpdateTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update'];

// Test specific table types
type Profile = Tables<'profiles'>;
type Team = Tables<'teams'>;
type Event = Tables<'events'>;

// Test that we can use these types in function signatures
function validateProfile(profile: Profile): boolean {
  return typeof profile.id === 'string';
}

function createProfile(data: InsertTables<'profiles'>): ProfileInsert {
  return data;
}

function updateProfile(data: UpdateTables<'profiles'>): ProfileUpdate {
  return data;
}

// Test that registration_metadata field is properly typed
function getTeamIdFromProfile(profile: Profile): string | null {
  if (profile.registration_metadata && typeof profile.registration_metadata === 'object') {
    const metadata = profile.registration_metadata as { team_id?: string };
    return metadata.team_id || null;
  }
  return null;
}

// Test that we can work with JSONB fields
function validateRegistrationMetadata(metadata: Profile['registration_metadata']): boolean {
  if (!metadata) return true;
  
  // Should be Json type (which includes objects)
  if (typeof metadata === 'object') {
    return true;
  }
  
  return false;
}

// Export types for use in other files
export type {
  Database,
  Profile,
  Team,
  Event,
  Tables,
  InsertTables,
  UpdateTables,
  ProfileRow,
  ProfileInsert,
  ProfileUpdate,
  TeamRow
};

// Export validation functions
export {
  validateProfile,
  createProfile,
  updateProfile,
  getTeamIdFromProfile,
  validateRegistrationMetadata
};

console.log('✅ Database types validation completed successfully!');
console.log('📋 All type definitions are working correctly.');
