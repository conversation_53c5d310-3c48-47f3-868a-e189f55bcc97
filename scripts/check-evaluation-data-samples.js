// ABOUTME: Script to check sample evaluation data to understand structure
// This script examines sample records from evaluation tables

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

async function checkEvaluationData() {
  console.log('=== EVALUATION DATA ANALYSIS ===\n');

  try {
    // 1. Sample pre_evaluations data
    console.log('1. SAMPLE PRE_EVALUATIONS DATA:');
    console.log('--------------------------------');
    const { data: preEvalSample, error: preEvalError } = await supabase
      .from('pre_evaluations')
      .select('*')
      .limit(3);

    if (preEvalError) {
      console.log('Error fetching pre_evaluations:', preEvalError);
    } else if (preEvalSample && preEvalSample.length > 0) {
      console.log(`Found ${preEvalSample.length} sample records\n`);
      
      // Show structure from first record
      const firstRecord = preEvalSample[0];
      console.log('Table columns:');
      Object.keys(firstRecord).forEach(key => {
        const value = firstRecord[key];
        const type = Array.isArray(value) ? 'array' : typeof value;
        console.log(`- ${key} (${type})`);
      });
      
      console.log('\nFirst record details:');
      console.log(JSON.stringify(firstRecord, null, 2));
    } else {
      console.log('No pre_evaluations records found.');
    }

    // 2. Sample player_evaluations data
    console.log('\n\n2. SAMPLE PLAYER_EVALUATIONS DATA:');
    console.log('-----------------------------------');
    const { data: playerEvalSample, error: playerEvalError } = await supabase
      .from('player_evaluations')
      .select('*')
      .limit(3);

    if (playerEvalError) {
      console.log('Error fetching player_evaluations:', playerEvalError);
    } else if (playerEvalSample && playerEvalSample.length > 0) {
      console.log(`Found ${playerEvalSample.length} sample records\n`);
      
      // Show structure from first record
      const firstRecord = playerEvalSample[0];
      console.log('Table columns:');
      Object.keys(firstRecord).forEach(key => {
        const value = firstRecord[key];
        const type = Array.isArray(value) ? 'array' : typeof value;
        console.log(`- ${key} (${type})`);
      });
      
      console.log('\nFirst record details:');
      console.log(JSON.stringify(firstRecord, null, 2));
    } else {
      console.log('No player_evaluations records found.');
    }

    // 3. Check for position-specific data
    console.log('\n\n3. POSITION DATA IN EVALUATIONS:');
    console.log('---------------------------------');
    
    // Check pre_evaluations with positions
    const { data: preEvalWithPositions, error: preEvalPosError } = await supabase
      .from('pre_evaluations')
      .select('id, positions')
      .not('positions', 'is', null)
      .limit(5);

    if (preEvalPosError) {
      console.log('Error checking positions in pre_evaluations:', preEvalPosError);
    } else {
      console.log(`\npre_evaluations with positions: ${preEvalWithPositions?.length || 0} records`);
      preEvalWithPositions?.forEach(record => {
        console.log(`- ID ${record.id}: ${JSON.stringify(record.positions)}`);
      });
    }

    // Check player_evaluations with position data
    const { data: playerEvalWithPositions, error: playerEvalPosError } = await supabase
      .from('player_evaluations')
      .select('id, position, player_id')
      .not('position', 'is', null)
      .limit(5);

    if (playerEvalPosError) {
      console.log('Error checking positions in player_evaluations:', playerEvalPosError);
    } else {
      console.log(`\nplayer_evaluations with position: ${playerEvalWithPositions?.length || 0} records`);
      playerEvalWithPositions?.forEach(record => {
        console.log(`- ID ${record.id}: position="${record.position}", player_id=${record.player_id}`);
      });
    }

    // 4. Check relationships
    console.log('\n\n4. RELATIONSHIP ANALYSIS:');
    console.log('-------------------------');
    
    // Find a pre_evaluation with related player_evaluations
    const { data: preEvalWithPlayers, error: relError } = await supabase
      .from('pre_evaluations')
      .select(`
        id,
        event_id,
        team_id,
        player_evaluations (
          id,
          player_id,
          position,
          evaluation_data
        )
      `)
      .limit(1);

    if (relError) {
      console.log('Error checking relationships:', relError);
    } else if (preEvalWithPlayers && preEvalWithPlayers.length > 0) {
      const preEval = preEvalWithPlayers[0];
      console.log(`\nPre-evaluation ID ${preEval.id}:`);
      console.log(`- Event ID: ${preEval.event_id}`);
      console.log(`- Team ID: ${preEval.team_id}`);
      console.log(`- Related player evaluations: ${preEval.player_evaluations?.length || 0}`);
      
      if (preEval.player_evaluations && preEval.player_evaluations.length > 0) {
        console.log('\nFirst player evaluation:');
        console.log(JSON.stringify(preEval.player_evaluations[0], null, 2));
      }
    }

  } catch (error) {
    console.error('Error checking evaluation data:', error);
  }
}

checkEvaluationData();