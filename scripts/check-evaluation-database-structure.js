// ABOUTME: Script to check evaluation-related database structure (functions, triggers, policies)
// This script examines the evaluation system's database components using the service role key

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);

async function checkEvaluationStructure() {
  console.log('=== EVALUATION DATABASE STRUCTURE CHECK ===\n');

  try {
    // 1. Check functions related to evaluations
    console.log('1. FUNCTIONS RELATED TO EVALUATIONS:');
    console.log('------------------------------------');
    const { data: functions, error: functionsError } = await supabase
      .from('pg_proc')
      .select(`
        proname,
        pg_get_functiondef(oid) as definition
      `)
      .or('proname.ilike.%evaluation%,proname.ilike.%eval%')
      .eq('pronamespace', '2200'); // public schema OID

    if (functionsError) {
      console.log('Error fetching functions:', functionsError);
    } else if (functions && functions.length > 0) {
      functions.forEach(func => {
        console.log(`\nFunction: ${func.proname}`);
        console.log('Definition:', func.definition ? func.definition.substring(0, 200) + '...' : 'N/A');
      });
    } else {
      console.log('No functions found with "evaluation" or "eval" in the name.');
    }

    // 2. Check triggers on evaluation tables
    console.log('\n\n2. TRIGGERS ON EVALUATION TABLES:');
    console.log('----------------------------------');
    
    // Check pre_evaluations triggers
    const { data: preEvalTriggers, error: preEvalTriggersError } = await supabase
      .from('information_schema.triggers')
      .select('*')
      .eq('event_object_table', 'pre_evaluations')
      .eq('event_object_schema', 'public');

    if (preEvalTriggersError) {
      console.log('Error fetching pre_evaluations triggers:', preEvalTriggersError);
    } else {
      console.log('\nTriggers on pre_evaluations:');
      if (preEvalTriggers && preEvalTriggers.length > 0) {
        preEvalTriggers.forEach(trigger => {
          console.log(`- ${trigger.trigger_name} (${trigger.event_manipulation} ${trigger.action_timing})`);
        });
      } else {
        console.log('No triggers found on pre_evaluations table.');
      }
    }

    // Check player_evaluations triggers
    const { data: playerEvalTriggers, error: playerEvalTriggersError } = await supabase
      .from('information_schema.triggers')
      .select('*')
      .eq('event_object_table', 'player_evaluations')
      .eq('event_object_schema', 'public');

    if (playerEvalTriggersError) {
      console.log('Error fetching player_evaluations triggers:', playerEvalTriggersError);
    } else {
      console.log('\nTriggers on player_evaluations:');
      if (playerEvalTriggers && playerEvalTriggers.length > 0) {
        playerEvalTriggers.forEach(trigger => {
          console.log(`- ${trigger.trigger_name} (${trigger.event_manipulation} ${trigger.action_timing})`);
        });
      } else {
        console.log('No triggers found on player_evaluations table.');
      }
    }

    // 3. Check RLS policies
    console.log('\n\n3. RLS POLICIES ON EVALUATION TABLES:');
    console.log('-------------------------------------');
    
    // Note: We'll need to query pg_policies view which might not be accessible via REST API
    // Let's try a different approach
    
    // 4. Check views related to evaluations
    console.log('\n\n4. VIEWS RELATED TO EVALUATIONS:');
    console.log('---------------------------------');
    const { data: views, error: viewsError } = await supabase
      .from('information_schema.views')
      .select('table_name, view_definition')
      .eq('table_schema', 'public')
      .or('table_name.ilike.%evaluation%,table_name.ilike.%eval%');

    if (viewsError) {
      console.log('Error fetching views:', viewsError);
    } else if (views && views.length > 0) {
      views.forEach(view => {
        console.log(`\nView: ${view.table_name}`);
        console.log('Definition:', view.view_definition ? view.view_definition.substring(0, 200) + '...' : 'N/A');
      });
    } else {
      console.log('No views found with "evaluation" or "eval" in the name.');
    }

    // 5. Record counts
    console.log('\n\n5. RECORD COUNTS:');
    console.log('-----------------');
    
    const { count: preEvalCount, error: preEvalCountError } = await supabase
      .from('pre_evaluations')
      .select('*', { count: 'exact', head: true });
    
    const { count: playerEvalCount, error: playerEvalCountError } = await supabase
      .from('player_evaluations')
      .select('*', { count: 'exact', head: true });

    console.log(`pre_evaluations: ${preEvalCount || 0} records`);
    console.log(`player_evaluations: ${playerEvalCount || 0} records`);

    // 6. Check table columns for both tables
    console.log('\n\n6. TABLE STRUCTURES:');
    console.log('--------------------');
    
    // Check pre_evaluations columns
    const { data: preEvalCols, error: preEvalColsError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable, column_default')
      .eq('table_schema', 'public')
      .eq('table_name', 'pre_evaluations')
      .order('ordinal_position');

    if (preEvalColsError) {
      console.log('Error fetching pre_evaluations columns:', preEvalColsError);
    } else {
      console.log('\npre_evaluations columns:');
      preEvalCols?.forEach(col => {
        console.log(`- ${col.column_name} (${col.data_type}, ${col.is_nullable === 'YES' ? 'nullable' : 'not null'}${col.column_default ? ', default: ' + col.column_default : ''})`);
      });
    }

    // Check player_evaluations columns
    const { data: playerEvalCols, error: playerEvalColsError } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable, column_default')
      .eq('table_schema', 'public')
      .eq('table_name', 'player_evaluations')
      .order('ordinal_position');

    if (playerEvalColsError) {
      console.log('Error fetching player_evaluations columns:', playerEvalColsError);
    } else {
      console.log('\nplayer_evaluations columns:');
      playerEvalCols?.forEach(col => {
        console.log(`- ${col.column_name} (${col.data_type}, ${col.is_nullable === 'YES' ? 'nullable' : 'not null'}${col.column_default ? ', default: ' + col.column_default : ''})`);
      });
    }

  } catch (error) {
    console.error('Error checking evaluation structure:', error);
  }
}

checkEvaluationStructure();