#!/usr/bin/env node

// ABOUTME: Script to restore deprecated files from a specific stage
// This allows easy rollback if something breaks

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PROJECT_ROOT = path.join(__dirname, '..');
const DEPRECATED_ROOT = path.join(PROJECT_ROOT, 'src/_deprecated');

function restoreStage(stageNumber) {
  console.log(`\n🔄 Restoring Deprecated Files - Stage ${stageNumber}`);
  console.log('=========================================\n');
  
  const stageDir = path.join(DEPRECATED_ROOT, `stage${stageNumber}`);
  const stageInfoPath = path.join(stageDir, 'stage-info.json');
  
  if (!fs.existsSync(stageInfoPath)) {
    console.error(`❌ Stage ${stageNumber} not found`);
    process.exit(1);
  }
  
  const stageInfo = JSON.parse(fs.readFileSync(stageInfoPath, 'utf8'));
  console.log(`📋 Stage: ${stageInfo.name}`);
  console.log(`📅 Staged on: ${new Date(stageInfo.date).toLocaleDateString()}\n`);
  
  let restoredCount = 0;
  let failedCount = 0;
  
  stageInfo.files.forEach(file => {
    if (file.status === 'moved') {
      const sourcePath = file.moved;
      const targetPath = path.join(PROJECT_ROOT, 'src/pages', file.original);
      
      console.log(`♻️  Restoring: ${file.original}`);
      
      const targetDir = path.dirname(targetPath);
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }
      
      if (fs.existsSync(sourcePath)) {
        fs.renameSync(sourcePath, targetPath);
        restoredCount++;
      } else {
        console.log(`   ❌ Source file not found`);
        failedCount++;
      }
    }
  });
  
  // Clean up empty directories
  try {
    fs.rmSync(stageDir, { recursive: true });
    console.log(`\n🗑️  Removed stage directory: ${stageDir}`);
  } catch (err) {
    console.log(`\n⚠️  Could not remove stage directory: ${err.message}`);
  }
  
  console.log('\n📊 Restore Summary:');
  console.log(`✅ Restored: ${restoredCount} files`);
  console.log(`❌ Failed: ${failedCount} files`);
}

// Command line interface
const stageArg = process.argv[2];
if (!stageArg) {
  console.log('Usage: node restore-deprecated-stage.js <stage_number>');
  process.exit(1);
}

const stageNumber = parseInt(stageArg);
if (isNaN(stageNumber)) {
  console.error('❌ Invalid stage number');
  process.exit(1);
}

restoreStage(stageNumber);