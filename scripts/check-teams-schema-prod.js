// ABOUTME: Script to check the teams table schema in production
// This helps us use the correct column names in our migration

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function checkTeamsSchema() {
  console.log('=== CHECKING TEAMS TABLE SCHEMA ===\n');

  // 1. Get a sample record to see column names
  console.log('1. TEAMS TABLE COLUMNS:');
  console.log('-----------------------');
  try {
    const { data: sample, error } = await supabase
      .from('teams')
      .select('*')
      .limit(1);
    
    if (error) throw error;
    
    if (sample && sample.length > 0) {
      console.log('Columns found:', Object.keys(sample[0]));
      console.log('\nSample record:');
      console.log(JSON.stringify(sample[0], null, 2));
    } else {
      console.log('No teams found in database');
    }
  } catch (error) {
    console.error('Error checking teams table:', error.message);
  }

  // 2. Check team_members columns too
  console.log('\n\n2. TEAM_MEMBERS TABLE COLUMNS:');
  console.log('-------------------------------');
  try {
    const { data: tmSample, error } = await supabase
      .from('team_members')
      .select('*')
      .limit(1);
    
    if (error) throw error;
    
    if (tmSample && tmSample.length > 0) {
      console.log('Columns found:', Object.keys(tmSample[0]));
    }
  } catch (error) {
    console.error('Error checking team_members table:', error.message);
  }

  process.exit(0);
}

checkTeamsSchema();