#!/bin/bash

echo "🚀 Deploying Edge Functions..."

# Check if Supabase CLI is available
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI not found. Installing..."
    npm install -g supabase
fi

# Check if we're linked to a project
if ! supabase status &> /dev/null; then
    echo "❌ Not linked to a Supabase project"
    echo "Run: supabase link --project-ref ovfwiyqhubxeqvbrggbe"
    exit 1
fi

echo "✅ Supabase CLI ready"

# Deploy test function
echo ""
echo "1️⃣ Deploying test-content-sync..."
supabase functions deploy test-content-sync
if [ $? -eq 0 ]; then
    echo "✅ test-content-sync deployed"
else
    echo "❌ Failed to deploy test-content-sync"
fi

# Deploy main content sync function
echo ""
echo "2️⃣ Deploying content-sync-v2..."
supabase functions deploy content-sync-v2
if [ $? -eq 0 ]; then
    echo "✅ content-sync-v2 deployed"
else
    echo "❌ Failed to deploy content-sync-v2"
fi

# Deploy content API function
echo ""
echo "3️⃣ Deploying content-api..."
supabase functions deploy content-api
if [ $? -eq 0 ]; then
    echo "✅ content-api deployed"
else
    echo "❌ Failed to deploy content-api"
fi

echo ""
echo "🎉 Deployment complete!"
echo ""
echo "Next steps:"
echo "1. Go to your Supabase Dashboard > Edge Functions"
echo "2. Click on 'test-content-sync' and run it to debug"
echo "3. Once working, use 'content-sync-v2' for actual syncing"