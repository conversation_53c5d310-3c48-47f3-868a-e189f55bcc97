#!/bin/bash

# Build script that sets version info for debugging
# This should be run before your build process

echo "Setting version information..."

# Get version from package.json
VERSION=$(node -p "require('./package.json').version")

# Get git information
BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

# Create or update .env.local with build info
echo "VITE_APP_VERSION=$VERSION" > .env.local
echo "VITE_GIT_BRANCH=$BRANCH" >> .env.local
echo "VITE_GIT_COMMIT=$COMMIT" >> .env.local
echo "VITE_BUILD_TIME=$BUILD_TIME" >> .env.local

# For production builds, enable debug display
if [ "$NODE_ENV" = "production" ] || [ "$1" = "prod" ]; then
    echo "VITE_SHOW_DEBUG_IN_PROD=true" >> .env.local
    echo "VITE_SHOW_VERSION_IN_HEADER=true" >> .env.local
    echo "Production debug mode enabled"
else
    # In development, always show version in header
    echo "VITE_SHOW_VERSION_IN_HEADER=true" >> .env.local
fi

echo "Version info set:"
echo "  Version: $VERSION"
echo "  Branch: $BRANCH"
echo "  Commit: $COMMIT"
echo "  Build Time: $BUILD_TIME"

# Run the actual build
npm run build
