// ABOUTME: Script to analyze the complexity and redundancy in evaluation tables
// Examines relationships, data duplication, and structural issues

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function analyzeEvaluationComplexity() {
  console.log('=== EVALUATION SYSTEM COMPLEXITY ANALYSIS ===\n');

  try {
    // 1. Analyze pre_evaluations purpose and data
    console.log('1. PRE_EVALUATIONS TABLE ANALYSIS:');
    console.log('----------------------------------');
    
    // Get sample data to understand the purpose
    const { data: preEvals } = await supabase
      .from('pre_evaluations')
      .select('*')
      .limit(5);
    
    console.log('Purpose: Appears to track evaluation requests/invitations sent to players');
    console.log('\nKey fields and their apparent purpose:');
    console.log('  - status: Track if evaluation is pending/completed');
    console.log('  - requested_at/started_at/completed_at: Lifecycle tracking');
    console.log('  - access_token: Secure link for players to access evaluation');
    console.log('  - responses: Store player self-evaluation responses');
    console.log('  - player_position: Position at time of evaluation request');
    console.log('  - template_id: Link to evaluation template used');
    
    // Check unique statuses
    const { data: statuses } = await supabase
      .from('pre_evaluations')
      .select('status')
      .limit(1000);
    
    const uniqueStatuses = [...new Set(statuses?.map(s => s.status) || [])];
    console.log('\nUnique status values:', uniqueStatuses);

    // 2. Analyze player_evaluations purpose
    console.log('\n\n2. PLAYER_EVALUATIONS TABLE ANALYSIS:');
    console.log('-------------------------------------');
    
    console.log('Purpose: Store actual evaluation scores/ratings');
    console.log('\nKey fields and their apparent purpose:');
    console.log('  - evaluator_id: Who performed the evaluation (coach)');
    console.log('  - category: Evaluation category (PHYSICAL, TECHNICAL, etc.)');
    console.log('  - rating: The actual score given');
    console.log('  - position: Player position for position-specific criteria');
    console.log('  - pre_evaluation_id: Links to pre_evaluation if from that flow');
    
    // Check how many have pre_evaluation_id vs standalone
    const { count: withPreEval } = await supabase
      .from('player_evaluations')
      .select('*', { count: 'exact', head: true })
      .not('pre_evaluation_id', 'is', null);
    
    const { count: withoutPreEval } = await supabase
      .from('player_evaluations')
      .select('*', { count: 'exact', head: true })
      .is('pre_evaluation_id', null);
    
    console.log(`\nEvaluations linked to pre_evaluations: ${withPreEval}`);
    console.log(`Standalone evaluations: ${withoutPreEval}`);

    // 3. Check for data redundancy
    console.log('\n\n3. DATA REDUNDANCY ANALYSIS:');
    console.log('----------------------------');
    
    // Check if same data exists in both tables
    const { data: samplePre } = await supabase
      .from('pre_evaluations')
      .select(`
        id,
        player_id,
        team_id,
        event_id,
        player_position,
        player_evaluations (
          player_id,
          team_id,
          event_id,
          position
        )
      `)
      .not('player_evaluations', 'is', null)
      .limit(5);
    
    console.log('Checking for duplicate data storage:');
    let redundancyFound = false;
    samplePre?.forEach(pre => {
      if (pre.player_evaluations?.length > 0) {
        const pe = pre.player_evaluations[0];
        if (pe.player_id === pre.player_id) {
          console.log('  ✓ player_id stored in both tables');
          redundancyFound = true;
        }
        if (pe.team_id === pre.team_id) {
          console.log('  ✓ team_id stored in both tables');
          redundancyFound = true;
        }
        if (pe.event_id === pre.event_id) {
          console.log('  ✓ event_id stored in both tables');
          redundancyFound = true;
        }
      }
    });
    
    if (!redundancyFound) {
      console.log('  No obvious redundancy found in sample data');
    }

    // 4. Analyze complexity issues
    console.log('\n\n4. COMPLEXITY ISSUES:');
    console.log('--------------------');
    
    console.log('Identified complexity problems:');
    console.log('  1. Two separate flows: pre-evaluation (request) vs direct evaluation');
    console.log('  2. Position stored in multiple places:');
    console.log('     - team_members.position (current position)');
    console.log('     - pre_evaluations.player_position (position at request time)');
    console.log('     - player_evaluations.position (position for criteria matching)');
    console.log('  3. Status tracking in multiple fields:');
    console.log('     - pre_evaluations.status');
    console.log('     - pre_evaluations.evaluation_status');
    console.log('     - player_evaluations.evaluation_status');
    console.log('  4. Overlapping purposes - both tables track evaluations differently');

    // 5. Check evaluation_criteria usage
    console.log('\n\n5. EVALUATION_CRITERIA INTEGRATION:');
    console.log('-----------------------------------');
    
    const { data: criteriaCheck } = await supabase
      .from('player_evaluations')
      .select('position, category, evaluation_criteria_data')
      .not('evaluation_criteria_data', 'is', null)
      .limit(5);
    
    if (criteriaCheck?.length > 0) {
      console.log('evaluation_criteria_data is used to store criteria details');
      console.log('Sample:', JSON.stringify(criteriaCheck[0].evaluation_criteria_data, null, 2));
    } else {
      console.log('evaluation_criteria_data appears unused or empty');
    }

    // 6. Provide simplification recommendations
    console.log('\n\n6. SIMPLIFICATION RECOMMENDATIONS:');
    console.log('---------------------------------');
    
    console.log('Option 1: Merge tables (recommended):');
    console.log('  - Combine pre_evaluations and player_evaluations');
    console.log('  - Use single status field with clear states');
    console.log('  - Store position once at evaluation time');
    console.log('  - Reduces joins and complexity');
    
    console.log('\nOption 2: Clear separation:');
    console.log('  - pre_evaluations: Only for requests/invitations');
    console.log('  - player_evaluations: Only for actual scores');
    console.log('  - Remove duplicate fields between tables');
    console.log('  - Clear foreign key relationships');
    
    console.log('\nPosition handling recommendation:');
    console.log('  - Store position at evaluation time (snapshot)');
    console.log('  - Use this position for criteria matching');
    console.log('  - Avoids confusion with current vs historical position');

  } catch (error) {
    console.error('Error:', error.message);
  }

  process.exit(0);
}

analyzeEvaluationComplexity();