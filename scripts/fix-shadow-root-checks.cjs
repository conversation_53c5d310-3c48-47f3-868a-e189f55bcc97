#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Find all TypeScript/React files in the foundation design system
const files = glob.sync('src/foundation/design-system/components/**/*.tsx', {
  cwd: process.cwd(),
  absolute: true
});

let fixedCount = 0;

files.forEach(filePath => {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Pattern 1: Direct attachShadow without check
  const pattern1 = /(\s*)(shadowRef\.current\s*=\s*\w+\.current\.attachShadow\(\s*{\s*mode:\s*'open'\s*}\s*\);)/g;
  if (pattern1.test(content)) {
    content = content.replace(pattern1, (match, indent, attachShadow) => {
      const varName = attachShadow.match(/(\w+)\.current\.attachShadow/)[1];
      return `${indent}// Check if shadow root already exists
${indent}if (${varName}.current.shadowRoot) {
${indent}  shadowRef.current = ${varName}.current.shadowRoot;
${indent}  return;
${indent}}
${indent}
${indent}${attachShadow}`;
    });
    modified = true;
  }

  // Pattern 2: const shadowRoot = container.attachShadow without check
  const pattern2 = /(\s*)(const\s+shadowRoot\s*=\s*(\w+)\.attachShadow\(\s*{\s*mode:\s*'open'\s*}\s*\);)/g;
  if (pattern2.test(content)) {
    content = content.replace(pattern2, (match, indent, attachShadow, containerVar) => {
      // Skip if already has a check
      if (content.includes(`${containerVar}.shadowRoot`)) {
        return match;
      }
      return `${indent}// Check if shadow root already exists
${indent}if (${containerVar}.shadowRoot) {
${indent}  shadowRootRef.current = ${containerVar}.shadowRoot;
${indent}  return;
${indent}}
${indent}
${indent}${attachShadow}`;
    });
    modified = true;
  }

  // Pattern 3: const shadow = host.attachShadow without check
  const pattern3 = /(\s*)(const\s+shadow\s*=\s*(\w+)\.attachShadow\(\s*{\s*mode:\s*'open'\s*}\s*\);)/g;
  if (pattern3.test(content)) {
    content = content.replace(pattern3, (match, indent, attachShadow, hostVar) => {
      // Skip if already has a check
      if (content.includes(`${hostVar}.shadowRoot`)) {
        return match;
      }
      return `${indent}// Check if shadow root already exists
${indent}if (${hostVar}.shadowRoot) {
${indent}  return ${hostVar}.shadowRoot;
${indent}}
${indent}
${indent}${attachShadow}`;
    });
    modified = true;
  }

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`Fixed: ${path.relative(process.cwd(), filePath)}`);
    fixedCount++;
  }
});

console.log(`\nFixed ${fixedCount} files with shadow root checks.`);