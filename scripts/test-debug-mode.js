// Test and enable debug mode
// Run this in your browser console

console.log('=== Debug Mode Test ===');

// Check current debug mode status
const currentFlags = JSON.parse(sessionStorage.getItem('superadmin_session_flags') || '{}');
console.log('Current debug mode:', currentFlags.debug_mode || false);

// Enable debug mode
console.log('\nEnabling debug mode...');
sessionStorage.setItem('superadmin_session_flags', JSON.stringify({ debug_mode: true }));

// Verify it's enabled
const newFlags = JSON.parse(sessionStorage.getItem('superadmin_session_flags') || '{}');
console.log('Debug mode now:', newFlags.debug_mode);

console.log('\n✅ Debug mode has been enabled!');
console.log('🔄 Please refresh the page to see debug buttons on event cards.');

// Check if any ShadowEventCards exist on the page
setTimeout(() => {
  const shadowHosts = document.querySelectorAll('[data-shadow-rendered]');
  console.log(`\nFound ${shadowHosts.length} ShadowEventCard components on this page`);
  
  // Check each for debug button
  shadowHosts.forEach((host, i) => {
    const shadowRoot = host.shadowRoot;
    const debugBtn = shadowRoot?.querySelector('.debug-button');
    console.log(`Card ${i + 1}: ${debugBtn ? '✅ Has debug button' : '❌ No debug button'}`);
  });
}, 1000);