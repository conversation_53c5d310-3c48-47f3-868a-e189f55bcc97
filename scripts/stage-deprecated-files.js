#!/usr/bin/env node

// ABOUTME: Script to stage deprecated files for safe removal
// This creates a deprecated folder structure and moves unused files in batches

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PROJECT_ROOT = path.join(__dirname, '..');
const DEPRECATED_ROOT = path.join(PROJECT_ROOT, 'src/_deprecated');
const RESULTS_FILE = path.join(PROJECT_ROOT, 'route-analysis-results.json');

// Load the analysis results
const results = JSON.parse(fs.readFileSync(RESULTS_FILE, 'utf8'));

// Staging configuration
const STAGES = {
  1: {
    name: 'Authentication Pages',
    description: 'Old login variants and auth pages',
    patterns: ['Login.tsx', 'UpdatedLogin.tsx', 'SimpleLogin.tsx', 'NonIonicLogin.tsx', 'Signup.tsx']
  },
  2: {
    name: 'V2 Design System',
    description: 'Deprecated V2 design system pages',
    patterns: ['v2/DesignSystem/', 'v2/perform/', 'v2/pulse/']
  },
  3: {
    name: 'Coach Components',
    description: 'Unused coach section components',
    patterns: ['section/Coach/components/', 'section/Coach/dashboards/']
  },
  4: {
    name: 'Event Management',
    description: 'Old event management pages',
    patterns: ['section/Coach/events/components/', 'section/Coach/supporting/EventManagement/']
  },
  5: {
    name: 'Top Level Pages',
    description: 'Unused root-level pages',
    patterns: ['Home.tsx', 'Dashboard.tsx', 'Activities.tsx', 'Events.tsx', 'Family.tsx']
  }
};

// Function to ensure directory exists
function ensureDir(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

// Function to get stage for a file
function getStageForFile(filePath) {
  for (const [stageNum, stage] of Object.entries(STAGES)) {
    for (const pattern of stage.patterns) {
      if (filePath.includes(pattern)) {
        return parseInt(stageNum);
      }
    }
  }
  return 99; // Default stage for uncategorized files
}

// Function to move file preserving directory structure
function moveFile(sourcePath, targetPath) {
  const targetDir = path.dirname(targetPath);
  ensureDir(targetDir);
  
  if (fs.existsSync(sourcePath)) {
    fs.renameSync(sourcePath, targetPath);
    return true;
  }
  return false;
}

// Main execution
function stageDeprecatedFiles(stageNumber) {
  console.log(`\n🚀 Staging Deprecated Files - Stage ${stageNumber}`);
  console.log('=====================================\n');
  
  // Create deprecated directory structure
  ensureDir(DEPRECATED_ROOT);
  
  const stageDir = path.join(DEPRECATED_ROOT, `stage${stageNumber}`);
  ensureDir(stageDir);
  
  // Create stage info file
  const stageInfo = {
    stage: stageNumber,
    name: STAGES[stageNumber]?.name || `Stage ${stageNumber}`,
    description: STAGES[stageNumber]?.description || 'Uncategorized files',
    date: new Date().toISOString(),
    files: []
  };
  
  let movedCount = 0;
  let failedCount = 0;
  
  // Process unused files
  results.files.unused.forEach(relativePath => {
    const fileStage = getStageForFile(relativePath);
    
    if (fileStage === stageNumber) {
      const sourcePath = path.join(PROJECT_ROOT, 'src/pages', relativePath);
      const targetPath = path.join(stageDir, 'pages', relativePath);
      
      console.log(`📦 Moving: ${relativePath}`);
      
      if (moveFile(sourcePath, targetPath)) {
        stageInfo.files.push({
          original: relativePath,
          moved: targetPath,
          status: 'moved'
        });
        movedCount++;
      } else {
        console.log(`   ❌ Failed to move (file not found)`);
        stageInfo.files.push({
          original: relativePath,
          moved: targetPath,
          status: 'not_found'
        });
        failedCount++;
      }
    }
  });
  
  // Save stage info
  fs.writeFileSync(
    path.join(stageDir, 'stage-info.json'),
    JSON.stringify(stageInfo, null, 2)
  );
  
  // Create README for the stage
  const readme = `# Deprecated Files - ${stageInfo.name}

## Description
${stageInfo.description}

## Stage Information
- **Stage**: ${stageNumber}
- **Date**: ${new Date(stageInfo.date).toLocaleDateString()}
- **Files Moved**: ${movedCount}
- **Files Not Found**: ${failedCount}

## Files in this Stage
${stageInfo.files.map(f => `- ${f.original} (${f.status})`).join('\n')}

## Rollback Instructions
To restore these files, run:
\`\`\`bash
npm run restore-deprecated-stage ${stageNumber}
\`\`\`

## Deletion Schedule
- **Testing Period**: 2 weeks
- **Production Validation**: 2 months
- **Safe to Delete**: ${new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toLocaleDateString()}
`;
  
  fs.writeFileSync(path.join(stageDir, 'README.md'), readme);
  
  // Summary
  console.log('\n📊 Stage Summary:');
  console.log(`✅ Moved: ${movedCount} files`);
  console.log(`❌ Not found: ${failedCount} files`);
  console.log(`📁 Location: ${stageDir}`);
  
  // Show next stages
  console.log('\n🔜 Remaining Stages:');
  Object.entries(STAGES).forEach(([num, stage]) => {
    if (parseInt(num) > stageNumber) {
      console.log(`   Stage ${num}: ${stage.name}`);
    }
  });
}

// Command line interface
const stageArg = process.argv[2];
if (!stageArg) {
  console.log('Usage: node stage-deprecated-files.js <stage_number>');
  console.log('\nAvailable Stages:');
  Object.entries(STAGES).forEach(([num, stage]) => {
    console.log(`  ${num}: ${stage.name} - ${stage.description}`);
  });
  process.exit(1);
}

const stageNumber = parseInt(stageArg);
if (isNaN(stageNumber)) {
  console.error('❌ Invalid stage number');
  process.exit(1);
}

stageDeprecatedFiles(stageNumber);