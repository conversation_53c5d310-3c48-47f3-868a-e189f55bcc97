// ABOUTME: Communication type definitions
// Core types for notifications, messages, and communication channels

export type NotificationChannel = 'sms' | 'email' | 'push' | 'in-app';
export type NotificationPriority = 'high' | 'normal' | 'low';
export type NotificationStatus = 'pending' | 'sent' | 'delivered' | 'failed' | 'read';

export interface Recipient {
  id: string;
  name?: string;
  email?: string;
  phone?: string;
  pushToken?: string;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  channel: NotificationChannel;
  subject?: string;
  body: string;
  variables?: string[];
}

export interface Message {
  id?: string;
  to: Recipient | Recipient[];
  from?: string;
  subject?: string;
  body: string;
  templateId?: string;
  data?: Record<string, any>;
  priority?: NotificationPriority;
  scheduledFor?: Date;
  metadata?: Record<string, any>;
}

export interface Notification {
  id: string;
  channel: NotificationChannel;
  recipientId: string;
  message: Message;
  status: NotificationStatus;
  sentAt?: Date;
  deliveredAt?: Date;
  readAt?: Date;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SMSMessage extends Message {
  channel: 'sms';
  mediaUrls?: string[];
}

export interface EmailMessage extends Message {
  channel: 'email';
  cc?: Recipient[];
  bcc?: Recipient[];
  attachments?: Attachment[];
  html?: string;
}

export interface PushMessage extends Message {
  channel: 'push';
  title: string;
  icon?: string;
  image?: string;
  actions?: PushAction[];
  data?: Record<string, any>;
}

export interface InAppMessage extends Message {
  channel: 'in-app';
  type: 'info' | 'success' | 'warning' | 'error';
  duration?: number;
  actions?: InAppAction[];
}

export interface Attachment {
  filename: string;
  content: string | Buffer;
  contentType?: string;
}

export interface PushAction {
  action: string;
  title: string;
  icon?: string;
}

export interface InAppAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

export interface CommunicationConfig {
  sms?: {
    provider: 'twilio' | 'aws-sns';
    config: Record<string, any>;
  };
  email?: {
    provider: 'sendgrid' | 'aws-ses' | 'smtp';
    config: Record<string, any>;
  };
  push?: {
    provider: 'firebase' | 'onesignal';
    config: Record<string, any>;
  };
}

export interface NotificationPreferences {
  userId: string;
  channels: {
    sms: boolean;
    email: boolean;
    push: boolean;
    inApp: boolean;
  };
  quiet: {
    enabled: boolean;
    startTime?: string;
    endTime?: string;
  };
  categories: Record<string, boolean>;
}

export interface NotificationResult {
  success: boolean;
  notificationId?: string;
  error?: string;
  details?: any;
}

export interface ToastOptions {
  type?: 'info' | 'success' | 'warning' | 'error';
  duration?: number;
  position?: 'top' | 'bottom' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  action?: InAppAction;
}