// ABOUTME: Notification provider that manages app-wide notification state
// Provides context for in-app notifications and toast messages

import React, { createContext, useContext, useState, useCallback } from 'react';
import { InAppMessage, ToastOptions, NotificationResult } from '../types';
import { CommunicationService } from '../services/CommunicationService';
import { CommunicationConfig } from '../types';

interface NotificationContextValue {
  notifications: InAppNotification[];
  toasts: Toast[];
  showToast: (message: string, options?: ToastOptions) => void;
  showNotification: (notification: InAppMessage) => NotificationResult;
  dismissNotification: (id: string) => void;
  dismissToast: (id: string) => void;
  clearAllNotifications: () => void;
  unreadCount: number;
}

interface InAppNotification extends InAppMessage {
  id: string;
  timestamp: Date;
  read: boolean;
}

interface Toast {
  id: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  duration: number;
  position: string;
  action?: {
    label: string;
    action: () => void;
  };
}

const NotificationContext = createContext<NotificationContextValue | undefined>(undefined);

export interface NotificationProviderProps {
  children: React.ReactNode;
  config?: CommunicationConfig;
  maxNotifications?: number;
  maxToasts?: number;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
  config,
  maxNotifications = 100,
  maxToasts = 5,
}) => {
  const [notifications, setNotifications] = useState<InAppNotification[]>([]);
  const [toasts, setToasts] = useState<Toast[]>([]);
  const [communicationService] = useState(() => 
    config ? new CommunicationService(config) : null
  );

  // Show toast message
  const showToast = useCallback((message: string, options: ToastOptions = {}) => {
    const toast: Toast = {
      id: `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      message,
      type: options.type || 'info',
      duration: options.duration || 5000,
      position: options.position || 'bottom',
      action: options.action,
    };

    setToasts(prev => {
      const newToasts = [toast, ...prev];
      // Keep only max number of toasts
      return newToasts.slice(0, maxToasts);
    });

    // Auto dismiss after duration
    if (toast.duration > 0) {
      setTimeout(() => {
        dismissToast(toast.id);
      }, toast.duration);
    }
  }, [maxToasts]);

  // Show in-app notification
  const showNotification = useCallback((message: InAppMessage): NotificationResult => {
    try {
      const notification: InAppNotification = {
        ...message,
        id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(),
        read: false,
        channel: 'in-app',
      };

      setNotifications(prev => {
        const newNotifications = [notification, ...prev];
        // Keep only max number of notifications
        return newNotifications.slice(0, maxNotifications);
      });

      // Also show as toast if specified
      if (notification.duration && notification.duration > 0) {
        showToast(notification.body, {
          type: notification.type,
          duration: notification.duration,
        });
      }

      return {
        success: true,
        notificationId: notification.id,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }, [maxNotifications, showToast]);

  // Dismiss notification
  const dismissNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  // Dismiss toast
  const dismissToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(t => t.id !== id));
  }, []);

  // Clear all notifications
  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.read).length;

  const value: NotificationContextValue = {
    notifications,
    toasts,
    showToast,
    showNotification,
    dismissNotification,
    dismissToast,
    clearAllNotifications,
    unreadCount,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

// Hook to use notification context
export const useNotificationContext = (): NotificationContextValue => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotificationContext must be used within a NotificationProvider');
  }
  return context;
};