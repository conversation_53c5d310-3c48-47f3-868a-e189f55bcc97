// ABOUTME: Hook for showing toast notifications
// Simplified interface for displaying temporary messages

import { useNotificationContext } from '../providers/NotificationProvider';
import { ToastOptions } from '../types';

/**
 * Hook for showing toast messages
 * 
 * @example
 * ```typescript
 * const toast = useToast();
 * 
 * // Simple usage
 * toast('Operation completed successfully!');
 * 
 * // With options
 * toast.success('Profile updated!');
 * toast.error('Failed to save changes');
 * toast.warning('Connection unstable');
 * toast.info('New update available');
 * ```
 */
export const useToast = () => {
  const { showToast } = useNotificationContext();

  // Base toast function
  const toast = (message: string, options?: ToastOptions) => {
    showToast(message, options);
  };

  // Convenience methods
  toast.success = (message: string, options?: Omit<ToastOptions, 'type'>) => {
    showToast(message, { ...options, type: 'success' });
  };

  toast.error = (message: string, options?: Omit<ToastOptions, 'type'>) => {
    showToast(message, { ...options, type: 'error' });
  };

  toast.warning = (message: string, options?: Omit<ToastOptions, 'type'>) => {
    showToast(message, { ...options, type: 'warning' });
  };

  toast.info = (message: string, options?: Omit<ToastOptions, 'type'>) => {
    showToast(message, { ...options, type: 'info' });
  };

  return toast;
};