// ABOUTME: SMS notification service implementation
// Handles sending SMS messages through configured provider

import { Message, NotificationResult, Recipient } from '../types';

export interface SMSConfig {
  provider: 'twilio' | 'aws-sns';
  accountSid?: string;
  authToken?: string;
  fromNumber?: string;
  region?: string;
}

export class SMSService {
  private config: SMSConfig;

  constructor(config: SMSConfig) {
    this.config = config;
  }

  /**
   * Send SMS message
   */
  async send(message: Message): Promise<NotificationResult> {
    try {
      const recipients = Array.isArray(message.to) ? message.to : [message.to];
      const results: NotificationResult[] = [];

      for (const recipient of recipients) {
        if (!recipient.phone) {
          results.push({
            success: false,
            error: 'No phone number provided for recipient',
          });
          continue;
        }

        const result = await this.sendToProvider(recipient, message);
        results.push(result);
      }

      // If all failed, return failure
      if (results.every(r => !r.success)) {
        return {
          success: false,
          error: 'All SMS messages failed to send',
          details: results,
        };
      }

      // Return success if at least one sent
      return {
        success: true,
        notificationId: this.generateId(),
        details: results,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        details: error,
      };
    }
  }

  /**
   * Send to SMS provider
   */
  private async sendToProvider(
    recipient: Recipient,
    message: Message
  ): Promise<NotificationResult> {
    // TODO: Implement actual provider integration
    // This is a mock implementation
    
    console.log(`[SMS] Sending to ${recipient.phone}: ${message.body}`);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // Mock success
    return {
      success: true,
      notificationId: this.generateId(),
      details: {
        provider: this.config.provider,
        to: recipient.phone,
        messageLength: message.body.length,
      },
    };
  }

  /**
   * Validate phone number
   */
  private validatePhoneNumber(phone: string): boolean {
    // Basic validation - in production, use a proper library
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  }

  /**
   * Format message with template data
   */
  private formatMessage(template: string, data?: Record<string, any>): string {
    if (!data) return template;
    
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return data[key] !== undefined ? String(data[key]) : match;
    });
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `sms_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check SMS credits/limits
   */
  async checkLimits(): Promise<{ available: boolean; remaining?: number }> {
    // TODO: Implement provider-specific limit checking
    return { available: true, remaining: 1000 };
  }
}