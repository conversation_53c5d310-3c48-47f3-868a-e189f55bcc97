// ABOUTME: Main communication service that orchestrates all notification channels
// Provides a unified interface for sending notifications across different channels

import { 
  NotificationChannel, 
  Message, 
  NotificationResult, 
  CommunicationConfig,
  Notification,
  NotificationStatus,
  NotificationPreferences
} from '../types';
import { SMSService } from './SMSService';
import { EmailService } from './EmailService';
import { PushService } from './PushService';
import { InAppNotificationService } from './InAppNotificationService';

export class CommunicationService {
  private smsService?: SMSService;
  private emailService?: EmailService;
  private pushService?: PushService;
  private inAppService: InAppNotificationService;
  private config: CommunicationConfig;

  constructor(config: CommunicationConfig) {
    this.config = config;
    
    // Initialize services based on config
    if (config.sms) {
      this.smsService = new SMSService(config.sms.config);
    }
    if (config.email) {
      this.emailService = new EmailService(config.email.config);
    }
    if (config.push) {
      this.pushService = new PushService(config.push.config);
    }
    
    // In-app service is always available
    this.inAppService = new InAppNotificationService();
  }

  /**
   * Send a notification through specified channel
   */
  async send(
    channel: NotificationChannel, 
    message: Message
  ): Promise<NotificationResult> {
    try {
      // Check user preferences if recipient has ID
      if ('id' in message.to && typeof message.to.id === 'string') {
        const preferences = await this.getUserPreferences(message.to.id);
        if (!this.isChannelEnabled(channel, preferences)) {
          return {
            success: false,
            error: 'Channel disabled by user preferences',
          };
        }
      }

      // Route to appropriate service
      switch (channel) {
        case 'sms':
          if (!this.smsService) {
            throw new Error('SMS service not configured');
          }
          return await this.smsService.send(message);
          
        case 'email':
          if (!this.emailService) {
            throw new Error('Email service not configured');
          }
          return await this.emailService.send(message);
          
        case 'push':
          if (!this.pushService) {
            throw new Error('Push service not configured');
          }
          return await this.pushService.send(message);
          
        case 'in-app':
          return await this.inAppService.send(message);
          
        default:
          throw new Error(`Unknown channel: ${channel}`);
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        details: error,
      };
    }
  }

  /**
   * Send notification to multiple channels
   */
  async sendMultiChannel(
    channels: NotificationChannel[], 
    message: Message
  ): Promise<Record<NotificationChannel, NotificationResult>> {
    const results: Record<string, NotificationResult> = {};
    
    await Promise.all(
      channels.map(async (channel) => {
        results[channel] = await this.send(channel, message);
      })
    );
    
    return results;
  }

  /**
   * Schedule a notification for future delivery
   */
  async schedule(
    channel: NotificationChannel,
    message: Message,
    scheduledFor: Date
  ): Promise<NotificationResult> {
    // TODO: Implement scheduling logic
    // For now, just add to queue with scheduled time
    const scheduledMessage = { ...message, scheduledFor };
    
    // In a real implementation, this would add to a job queue
    return {
      success: true,
      notificationId: this.generateId(),
      details: { scheduledFor },
    };
  }

  /**
   * Get notification status
   */
  async getStatus(notificationId: string): Promise<Notification | null> {
    // TODO: Implement status tracking
    // This would query a notifications database
    return null;
  }

  /**
   * Cancel a scheduled notification
   */
  async cancel(notificationId: string): Promise<boolean> {
    // TODO: Implement cancellation
    // This would remove from job queue or mark as cancelled
    return false;
  }

  /**
   * Get user notification preferences
   */
  private async getUserPreferences(userId: string): Promise<NotificationPreferences | null> {
    // TODO: Fetch from database
    // For now, return default preferences
    return {
      userId,
      channels: {
        sms: true,
        email: true,
        push: true,
        inApp: true,
      },
      quiet: {
        enabled: false,
      },
      categories: {},
    };
  }

  /**
   * Check if channel is enabled for user
   */
  private isChannelEnabled(
    channel: NotificationChannel,
    preferences: NotificationPreferences | null
  ): boolean {
    if (!preferences) return true;
    
    // Check quiet hours
    if (preferences.quiet.enabled && preferences.quiet.startTime && preferences.quiet.endTime) {
      const now = new Date();
      const currentTime = now.getHours() * 60 + now.getMinutes();
      const [startHour, startMinute] = preferences.quiet.startTime.split(':').map(Number);
      const [endHour, endMinute] = preferences.quiet.endTime.split(':').map(Number);
      const startTime = startHour * 60 + startMinute;
      const endTime = endHour * 60 + endMinute;
      
      if (currentTime >= startTime && currentTime <= endTime) {
        return false;
      }
    }
    
    return preferences.channels[channel] ?? true;
  }

  /**
   * Generate unique notification ID
   */
  private generateId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Batch send notifications
   */
  async sendBatch(
    notifications: Array<{
      channel: NotificationChannel;
      message: Message;
    }>
  ): Promise<NotificationResult[]> {
    return Promise.all(
      notifications.map(({ channel, message }) => this.send(channel, message))
    );
  }
}