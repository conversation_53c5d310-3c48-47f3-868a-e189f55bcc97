// ABOUTME: Global team selector component for coaches to switch between their teams
// Uses CoachContext for centralized state management and appears in main navigation

import React from 'react';
import { useCoachContext, useHasMultipleTeams } from '../contexts/CoachContext';
import ShadowTeamSelector from '../../components/shadow/form/ShadowTeamSelector';
import { useHistory } from 'react-router-dom';

interface TeamSelectorProps {
  variant?: 'default' | 'compact';
  className?: string;
  onTeamChange?: (teamId: string) => void;
}

export const TeamSelector: React.FC<TeamSelectorProps> = ({ 
  variant = 'default', 
  className,
  onTeamChange 
}) => {
  const { teams, currentTeam, switchTeam, loading } = useCoachContext();
  const hasMultipleTeams = useHasMultipleTeams();
  const history = useHistory();

  // Don't show selector if coach only has one team
  if (!hasMultipleTeams || loading) {
    return null;
  }

  const handleTeamChange = (teamId: string) => {
    const team = teams.find(t => t.id === teamId);
    if (!team) return;

    // Update context
    switchTeam(teamId);

    // Call custom handler if provided
    if (onTeamChange) {
      onTeamChange(teamId);
    } else {
      // Default behavior: navigate to the new team's page
      // This will be updated when we migrate to Perform area
      const currentPath = history.location.pathname;
      
      // If we're on a team-specific page, switch to the new team
      if (currentPath.includes('/team/')) {
        const newPath = currentPath.replace(/\/team\/[^/]+/, `/team/${teamId}`);
        history.push(newPath);
      } else if (team.clubId) {
        // Navigate to team page
        history.push(`/coach/club/${team.clubId}/team/${teamId}`);
      }
    }
  };

  // Format teams for the selector
  const formattedTeams = teams.map(team => ({
    id: team.id,
    name: team.name,
    ageGroup: team.ageGroup,
    sport: team.sport,
    clubName: team.clubName,
    isActive: team.isActive
  }));

  return (
    <div className={className}>
      <ShadowTeamSelector
        teams={formattedTeams}
        selectedTeamId={currentTeam?.id}
        onTeamChange={handleTeamChange}
        variant={variant}
        placeholder="Select team..."
        loading={loading}
      />
    </div>
  );
};

// Header-specific team selector with styling
export const HeaderTeamSelector: React.FC = () => {
  const hasMultipleTeams = useHasMultipleTeams();
  const { currentTeam } = useCoachContext();

  if (!hasMultipleTeams) {
    // Just show current team name if only one team
    return currentTeam ? (
      <div className="text-sm text-gray-400">
        {currentTeam.name}
      </div>
    ) : null;
  }

  return (
    <div className="flex items-center gap-2">
      <span className="text-xs text-gray-500">Team:</span>
      <TeamSelector variant="compact" />
    </div>
  );
};