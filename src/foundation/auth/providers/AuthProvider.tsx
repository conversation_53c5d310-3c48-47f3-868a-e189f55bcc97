// ABOUTME: Main authentication provider that wraps the app
// Manages auth state and provides auth context to all components

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { AuthService } from '../services/AuthService';
import { 
  AuthContextValue, 
  AuthState, 
  User, 
  Session, 
  LoginCredentials, 
  SignupData,
  AuthConfig 
} from '../types';

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

export interface AuthProviderProps {
  children: React.ReactNode;
  config: AuthConfig;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children, config }) => {
  const [authService] = useState(() => new AuthService(config));
  const [state, setState] = useState<AuthState>({
    user: null,
    session: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });

  // Initialize auth state
  useEffect(() => {
    const initAuth = async () => {
      try {
        const result = await authService.getSession();
        if (result) {
          setState({
            user: result.user,
            session: result.session,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } else {
          setState(prev => ({ ...prev, isLoading: false }));
        }
      } catch (error: any) {
        setState(prev => ({ 
          ...prev, 
          isLoading: false, 
          error: error 
        }));
      }
    };

    initAuth();

    // Subscribe to auth changes
    const { data: { subscription } } = authService.onAuthStateChange((user, session) => {
      setState({
        user,
        session,
        isAuthenticated: !!user && !!session,
        isLoading: false,
        error: null,
      });
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [authService]);

  // Login action
  const login = useCallback(async (credentials: LoginCredentials) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      const { user, session } = await authService.login(credentials);
      setState({
        user,
        session,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error 
      }));
      throw error;
    }
  }, [authService]);

  // Signup action
  const signup = useCallback(async (data: SignupData) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      const { user, session } = await authService.signup(data);
      setState({
        user,
        session,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error 
      }));
      throw error;
    }
  }, [authService]);

  // Logout action
  const logout = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      await authService.logout();
      setState({
        user: null,
        session: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
    } catch (error: any) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error 
      }));
      throw error;
    }
  }, [authService]);

  // Refresh session
  const refreshSession = useCallback(async () => {
    try {
      const { user, session } = await authService.refreshSession();
      setState(prev => ({
        ...prev,
        user,
        session,
        error: null,
      }));
    } catch (error: any) {
      setState(prev => ({ ...prev, error }));
      throw error;
    }
  }, [authService]);

  // Reset password
  const resetPassword = useCallback(async (email: string) => {
    try {
      await authService.resetPassword(email);
    } catch (error: any) {
      setState(prev => ({ ...prev, error }));
      throw error;
    }
  }, [authService]);

  // Update password
  const updatePassword = useCallback(async (newPassword: string) => {
    try {
      await authService.updatePassword(newPassword);
    } catch (error: any) {
      setState(prev => ({ ...prev, error }));
      throw error;
    }
  }, [authService]);

  // Permission helpers
  const hasRole = useCallback((roleName: string): boolean => {
    return state.user?.roles.some(role => role.name === roleName) || false;
  }, [state.user]);

  const hasPermission = useCallback((permission: string): boolean => {
    return state.user?.permissions.some(p => p.name === permission) || false;
  }, [state.user]);

  const can = useCallback((action: string, resource: string): boolean => {
    return state.user?.permissions.some(p => 
      p.action === action && p.resource === resource
    ) || false;
  }, [state.user]);

  const value: AuthContextValue = {
    ...state,
    login,
    signup,
    logout,
    refreshSession,
    resetPassword,
    updatePassword,
    hasRole,
    hasPermission,
    can,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Hook to use auth context
export const useAuthContext = (): AuthContextValue => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
};