// ABOUTME: Authentication type definitions
// Core types for authentication, authorization, and user management

export interface User {
  id: string;
  email: string;
  displayName?: string;
  avatarUrl?: string;
  roles: Role[];
  permissions: Permission[];
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface Session {
  id: string;
  userId: string;
  accessToken: string;
  refreshToken?: string;
  expiresAt: Date;
  createdAt: Date;
}

export interface Role {
  id: string;
  name: string;
  description?: string;
  permissions: Permission[];
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

export interface AuthState {
  user: User | null;
  session: Session | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: AuthError | null;
}

export interface AuthError {
  code: string;
  message: string;
  details?: any;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface SignupData {
  email: string;
  password: string;
  displayName?: string;
  metadata?: Record<string, any>;
}

export interface AuthConfig {
  baseUrl: string;
  supabaseUrl: string;
  supabaseAnonKey: string;
  sessionRefreshInterval?: number;
  redirectAfterLogin?: string;
  redirectAfterLogout?: string;
}

export type AuthProvider = 'email' | 'google' | 'facebook' | 'twitter';

export interface AuthContextValue {
  // State
  user: User | null;
  session: Session | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: AuthError | null;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  signup: (data: SignupData) => Promise<void>;
  logout: () => Promise<void>;
  refreshSession: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updatePassword: (newPassword: string) => Promise<void>;
  
  // Utilities
  hasRole: (roleName: string) => boolean;
  hasPermission: (permission: string) => boolean;
  can: (action: string, resource: string) => boolean;
}