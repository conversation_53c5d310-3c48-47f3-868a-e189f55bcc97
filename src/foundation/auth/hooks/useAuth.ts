// ABOUTME: Primary authentication hook for accessing auth state and actions
// Simplifies auth usage throughout the application

import { useAuthContext } from '../providers/AuthProvider';

/**
 * Main authentication hook
 * 
 * @example
 * ```typescript
 * const { user, login, logout, isAuthenticated } = useAuth();
 * 
 * const handleLogin = async () => {
 *   try {
 *     await login({ email, password });
 *     // Navigate to dashboard
 *   } catch (error) {
 *     // Handle error
 *   }
 * };
 * ```
 */
export const useAuth = () => {
  const context = useAuthContext();
  
  return {
    // State
    user: context.user,
    isAuthenticated: context.isAuthenticated,
    isLoading: context.isLoading,
    error: context.error,
    
    // Actions
    login: context.login,
    signup: context.signup,
    logout: context.logout,
    resetPassword: context.resetPassword,
    updatePassword: context.updatePassword,
    
    // Utilities
    hasRole: context.hasRole,
    hasPermission: context.hasPermission,
    can: context.can,
  };
};