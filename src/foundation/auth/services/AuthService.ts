// ABOUTME: Core authentication service handling all auth operations
// Integrates with Supabase for authentication backend

import { SupabaseClient, Session as SupabaseSession } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { 
  User, 
  Session, 
  LoginCredentials, 
  SignupData, 
  AuthError,
  AuthConfig 
} from '../types';

export class AuthService {
  private supabase: SupabaseClient;
  private config: AuthConfig;

  constructor(config: AuthConfig) {
    this.config = config;
    this.supabase = supabase;
  }

  /**
   * Login with email and password
   */
  async login(credentials: LoginCredentials): Promise<{ user: User; session: Session }> {
    try {
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) throw this.mapError(error);
      if (!data.user || !data.session) throw this.createError('LOGIN_FAILED', 'Lo<PERSON> failed');

      const user = await this.mapUser(data.user);
      const session = this.mapSession(data.session);

      return { user, session };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Sign up new user
   */
  async signup(signupData: SignupData): Promise<{ user: User; session: Session }> {
    try {
      const { data, error } = await this.supabase.auth.signUp({
        email: signupData.email,
        password: signupData.password,
        options: {
          data: {
            display_name: signupData.displayName,
            ...signupData.metadata,
          },
        },
      });

      if (error) throw this.mapError(error);
      if (!data.user || !data.session) throw this.createError('SIGNUP_FAILED', 'Signup failed');

      const user = await this.mapUser(data.user);
      const session = this.mapSession(data.session);

      return { user, session };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Logout current user
   */
  async logout(): Promise<void> {
    try {
      const { error } = await this.supabase.auth.signOut();
      if (error) throw this.mapError(error);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get current session
   */
  async getSession(): Promise<{ user: User; session: Session } | null> {
    try {
      const { data: { session } } = await this.supabase.auth.getSession();
      
      if (!session) return null;

      const user = session.user;
      if (!user) return null;

      const mappedUser = await this.mapUser(user);
      const mappedSession = this.mapSession(session);

      return { user: mappedUser, session: mappedSession };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Refresh current session
   */
  async refreshSession(): Promise<{ user: User; session: Session }> {
    try {
      const { data, error } = await this.supabase.auth.refreshSession();

      if (error) throw this.mapError(error);
      if (!data.user || !data.session) throw this.createError('REFRESH_FAILED', 'Session refresh failed');

      const user = await this.mapUser(data.user);
      const session = this.mapSession(data.session);

      return { user, session };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Reset password for email
   */
  async resetPassword(email: string): Promise<void> {
    try {
      const { error } = await this.supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${this.config.baseUrl}/reset-password`,
      });

      if (error) throw this.mapError(error);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Update password for current user
   */
  async updatePassword(newPassword: string): Promise<void> {
    try {
      const { error } = await this.supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) throw this.mapError(error);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Subscribe to auth state changes
   */
  onAuthStateChange(callback: (user: User | null, session: Session | null) => void) {
    return this.supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        const user = await this.mapUser(session.user);
        const mappedSession = this.mapSession(session);
        callback(user, mappedSession);
      } else {
        callback(null, null);
      }
    });
  }

  /**
   * Map Supabase user to our User type
   */
  private async mapUser(supabaseUser: any): Promise<User> {
    // TODO: Fetch roles and permissions from database
    return {
      id: supabaseUser.id,
      email: supabaseUser.email!,
      displayName: supabaseUser.user_metadata?.display_name || supabaseUser.email!.split('@')[0],
      avatarUrl: supabaseUser.user_metadata?.avatar_url,
      roles: [], // TODO: Fetch from database
      permissions: [], // TODO: Fetch from database
      metadata: supabaseUser.user_metadata,
      createdAt: new Date(supabaseUser.created_at),
      updatedAt: new Date(supabaseUser.updated_at || supabaseUser.created_at),
    };
  }

  /**
   * Map Supabase session to our Session type
   */
  private mapSession(supabaseSession: SupabaseSession): Session {
    return {
      id: supabaseSession.access_token,
      userId: supabaseSession.user.id,
      accessToken: supabaseSession.access_token,
      refreshToken: supabaseSession.refresh_token,
      expiresAt: new Date(supabaseSession.expires_at! * 1000),
      createdAt: new Date(),
    };
  }

  /**
   * Map Supabase error to AuthError
   */
  private mapError(error: any): AuthError {
    return {
      code: error.code || 'UNKNOWN_ERROR',
      message: error.message || 'An unknown error occurred',
      details: error,
    };
  }

  /**
   * Create custom error
   */
  private createError(code: string, message: string, details?: any): AuthError {
    return { code, message, details };
  }

  /**
   * Handle and format errors
   */
  private handleError(error: any): AuthError {
    if (error.code && error.message) {
      return error as AuthError;
    }
    return this.createError('UNKNOWN_ERROR', error.message || 'An unknown error occurred', error);
  }
}