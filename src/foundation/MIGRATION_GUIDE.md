# Foundation Modules Migration Guide

This guide helps you migrate from the old scattered implementations to the new foundation modules.

## Quick Start

### 1. Design System Migration

#### Old Way
```typescript
import ShadowButton from '@/components/shadow/ShadowButton';
import { SHOT_COLORS } from '@/styles/shotColors';
```

#### New Way
```typescript
import { ShadowButton, theme } from '@/foundation/design-system';
// or
import { ShadowButton } from '@/foundation';
```

### 2. Authentication Migration

#### Old Way
```typescript
// Multiple login pages, scattered auth logic
import { supabase } from '@/lib/supabase';
const { data, error } = await supabase.auth.signIn({...});
```

#### New Way
```typescript
import { useAuth, AuthGuard } from '@/foundation/auth';

const { login, user, isAuthenticated } = useAuth();
await login({ email, password });

// Protected routes
<AuthGuard>
  <ProtectedComponent />
</AuthGuard>
```

### 3. Communication Migration

#### Old Way
```typescript
import { SmsNotificationService } from '@/services/SmsNotificationService';
const sms = new SmsNotificationService();
await sms.send(...);
```

#### New Way
```typescript
import { useToast, CommunicationService } from '@/foundation/communication';

// For toasts
const toast = useToast();
toast.success('Operation completed!');

// For other notifications
const comm = new CommunicationService(config);
await comm.send('sms', { to: recipient, body: 'Message' });
```

## Component Migration Map

| Old Import | New Import |
|------------|------------|
| `components/shadow/ShadowButton` | `@/foundation/design-system/components/atoms/Button` |
| `components/shadow/ShadowAvatar` | `@/foundation/design-system/components/atoms/Avatar` |
| `components/shadow/ShadowModal` | `@/foundation/design-system/components/molecules/Modals` |
| `styles/shotColors` | `@/foundation/design-system/tokens/colors` |

## Step-by-Step Migration

### Phase 1: Update Imports (Non-breaking)
1. Install path aliases in `tsconfig.json`:
   ```json
   {
     "compilerOptions": {
       "paths": {
         "@/foundation/*": ["src/foundation/*"]
       }
     }
   }
   ```

2. Update imports gradually using find/replace

3. Both old and new paths work during transition

### Phase 2: Update Component Usage
1. Check for prop changes (most are backward compatible)
2. Update any direct style references to use design tokens
3. Test thoroughly

### Phase 3: Remove Old Code
1. Move old files to `src/_deprecated/foundation/`
2. Remove old imports after confirming all usage updated
3. Delete deprecated files after 2 months in production

## Common Gotchas

### Design System
- Size props changed: `small/medium/large` → `sm/md/lg`
- Color props now use theme tokens instead of hex values
- Some components have new required props for accessibility

### Authentication
- Must wrap app with `AuthProvider` at root level
- Session management is now automatic
- Permissions are fetched from database, not hardcoded

### Communication
- Must wrap app with `NotificationProvider` for in-app notifications
- SMS/Email require provider configuration
- Toast positioning options have changed

## Testing Your Migration

1. **Unit Tests**: Update test imports and mocks
2. **Integration Tests**: Verify auth flows and notifications
3. **E2E Tests**: Ensure user journeys still work
4. **Visual Tests**: Check component appearance matches

## Need Help?

- Check component Storybook for examples
- Review type definitions for API changes
- Look at migration examples in codebase
- Ask team for assistance with complex migrations