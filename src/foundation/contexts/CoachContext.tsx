// ABOUTME: Central coach state management context for managing coach teams and current team selection
// Provides persistent state for coach's teams and remembers last viewed team across sessions

import type React from 'react';
import { createContext, useContext, useState, useEffect, type ReactNode } from 'react'
import { TeamService } from '../../services/TeamService';
import { supabase } from '@/lib/supabase';
import { useCurrentUser } from '@/hooks/useCurrentUser';

interface Team {
  id: string;
  name: string;
  ageGroup?: string;
  sport?: string;
  clubName?: string;
  clubId?: string;
  isActive: boolean;
}

interface CoachContextType {
  // State
  teams: Team[];
  currentTeam: Team | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  setCurrentTeam: (team: Team) => Promise<void>;
  refreshTeams: () => Promise<void>;
  switchTeam: (teamId: string) => void;
}

const CoachContext = createContext<CoachContextType | undefined>(undefined);

const STORAGE_KEY = 'shot_coach_last_team';

export const CoachProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user, profile } = useCurrentUser();
  const [teams, setTeams] = useState<Team[]>([]);
  const [currentTeam, setCurrentTeamState] = useState<Team | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load teams when user is available
  useEffect(() => {
    console.log('🏈 [CoachContext] useEffect triggered:', {
      userId: user?.id,
      hasProfile: !!profile,
      isCoach: profile?.privileges?.includes('coach')
    });
    
    if (user?.id && profile?.privileges?.includes('coach')) {
      loadUserAndTeams();
    } else if (user?.id) {
      setTeams([]);
      setCurrentTeamState(null);
      setLoading(false);
    }
  }, [user, profile]);

  // Load user and their teams
  const loadUserAndTeams = async () => {
    if (!user?.id) {
      return;
    }
    
    console.log('🏈 [CoachContext] loadUserAndTeams starting for user:', user.id);
    
    try {
      setLoading(true);
      setError(null);

      // Load coach teams
      const teamsData = await TeamService.getTeamsForCoach(user.id);
      console.log('🏈 [CoachContext] Teams loaded from DB:', teamsData);
      
      // Format teams for consistent interface
      const formattedTeams: Team[] = teamsData.map(team => ({
        id: team.team_id,
        name: team.team_name,
        ageGroup: team.age_group,
        sport: team.sport_type,
        clubName: team.club_name,
        clubId: team.club_id,
        isActive: team.is_active !== false
      }));

      console.log('🏈 [CoachContext] Formatted teams:', formattedTeams);
      setTeams(formattedTeams);

      // First try to load from localStorage for immediate access
      let lastTeamId = localStorage.getItem(`${STORAGE_KEY}_${user.id}`);
      console.log('🏈 [CoachContext] Last team ID from localStorage:', lastTeamId);
      
      // If not in localStorage, check the database
      if (!lastTeamId) {
        console.log('🏈 [CoachContext] No localStorage team, checking DB...');
        try {
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('last_accessed_team')
            .eq('id', user.id)
            .single();
          
          console.log('🏈 [CoachContext] DB profile data:', { profileData, profileError });
          
          if (!profileError && profileData?.last_accessed_team) {
            lastTeamId = profileData.last_accessed_team;
            // Save to localStorage for next time
            console.log('🏈 [CoachContext] Saving to localStorage:', lastTeamId);
            localStorage.setItem(`${STORAGE_KEY}_${user.id}`, lastTeamId);
          }
        } catch (err) {
          console.error('🏈 [CoachContext] Error loading last accessed team from profile:', err);
        }
      }
      
      if (lastTeamId && formattedTeams.length > 0) {
        // Try to find the last viewed team
        const lastTeam = formattedTeams.find(t => t.id === lastTeamId);
        console.log('🏈 [CoachContext] Found last team:', lastTeam);
        
        if (lastTeam) {
          console.log('🏈 [CoachContext] Setting current team to:', lastTeam.name);
          setCurrentTeamState(lastTeam);
        } else {
          // Last team no longer accessible, use first team
          console.log('🏈 [CoachContext] Last team not found, using first team');
          setCurrentTeamState(formattedTeams[0]);
        }
      } else if (formattedTeams.length > 0) {
        // No last team saved, use first team
        console.log('🏈 [CoachContext] No last team saved, using first team');
        setCurrentTeamState(formattedTeams[0]);
      }

    } catch (err) {
      console.error('Error loading coach teams:', err);
      setError('Failed to load teams');
    } finally {
      setLoading(false);
    }
  };

  // Set current team and persist to localStorage and database
  const setCurrentTeam = async (team: Team) => {
    console.log('🏈 [CoachContext] setCurrentTeam called with:', team);
    setCurrentTeamState(team);
    
    if (user?.id) {
      // Save to localStorage for immediate access
      const storageKey = `${STORAGE_KEY}_${user.id}`;
      console.log('🏈 [CoachContext] Saving to localStorage with key:', storageKey, 'value:', team.id);
      localStorage.setItem(storageKey, team.id);
      
      // Also save to database for persistence across sessions
      try {
        console.log('🏈 [CoachContext] Calling update_last_accessed_team RPC...');
        const { error } = await supabase.rpc('update_last_accessed_team', {
          p_user_id: user.id,
          p_team_id: team.id
        });
        
        if (error) {
          console.error('🏈 [CoachContext] Error saving last accessed team to database:', error);
        } else {
          console.log(`🏈 [CoachContext] Successfully saved last accessed team to DB: ${team.name} (${team.id})`);
        }
      } catch (err) {
        console.error('🏈 [CoachContext] Failed to update last accessed team:', err);
      }
    }
  };

  // Switch to a different team by ID
  const switchTeam = (teamId: string) => {
    console.log('🏈 [CoachContext] switchTeam called with teamId:', teamId);
    const team = teams.find(t => t.id === teamId);
    console.log('🏈 [CoachContext] Found team:', team);
    if (team) {
      setCurrentTeam(team);
    }
  };

  // Refresh teams from server
  const refreshTeams = async () => {
    if (!user?.id) return;
    
    console.log('🏈 [CoachContext] refreshTeams called');
    
    try {
      setLoading(true);
      const teamsData = await TeamService.getTeamsForCoach(user.id);
      console.log('🏈 [CoachContext] Refreshed teams from DB:', teamsData);
      
      const formattedTeams: Team[] = teamsData.map(team => ({
        id: team.team_id,
        name: team.team_name,
        ageGroup: team.age_group,
        sport: team.sport_type,
        clubName: team.club_name,
        clubId: team.club_id,
        isActive: team.is_active !== false
      }));

      console.log('🏈 [CoachContext] Formatted refreshed teams:', formattedTeams);
      setTeams(formattedTeams);

      // Update current team if it's in the refreshed list
      if (currentTeam) {
        console.log('🏈 [CoachContext] Current team before refresh:', currentTeam);
        const updatedCurrentTeam = formattedTeams.find(t => t.id === currentTeam.id);
        
        if (updatedCurrentTeam) {
          // Update the current team with fresh data
          console.log('🏈 [CoachContext] Updating current team with fresh data:', updatedCurrentTeam);
          setCurrentTeamState(updatedCurrentTeam);
        } else {
          // Current team no longer accessible, switch to first available
          console.log('🏈 [CoachContext] Current team not found in refreshed list');
          if (formattedTeams.length > 0) {
            setCurrentTeam(formattedTeams[0]);
          } else {
            setCurrentTeamState(null);
          }
        }
      }
    } catch (err) {
      console.error('🏈 [CoachContext] Error refreshing teams:', err);
      setError('Failed to refresh teams');
    } finally {
      setLoading(false);
    }
  };


  const value: CoachContextType = {
    teams,
    currentTeam,
    loading,
    error,
    setCurrentTeam,
    refreshTeams,
    switchTeam,
  };

  return <CoachContext.Provider value={value}>{children}</CoachContext.Provider>;
};

// Hook to use coach context
export const useCoachContext = () => {
  const context = useContext(CoachContext);
  if (context === undefined) {
    throw new Error('useCoachContext must be used within a CoachProvider');
  }
  return context;
};

// Helper hook to get current team with loading state
export const useCurrentTeam = () => {
  const { currentTeam, loading } = useCoachContext();
  return { team: currentTeam, loading };
};

// Helper hook to check if user has multiple teams
export const useHasMultipleTeams = () => {
  const { teams } = useCoachContext();
  return teams.length > 1;
};