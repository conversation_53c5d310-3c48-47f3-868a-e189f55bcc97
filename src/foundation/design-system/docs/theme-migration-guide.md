# Theme Migration Guide for Shadow DOM Components

## Overview
This guide helps migrate Shadow DOM components from the old theme structure to the new foundation theme structure.

## Color Mappings

### Old → New Color References

```typescript
// Brand Colors
theme.colors.brand.purple → theme.colors.primary.purple or '#6B00DB'
theme.colors.brand.teal → theme.colors.primary.teal or '#1ABC9C'
theme.colors.brand.gold → theme.colors.primary.gold or '#F7B613'
theme.colors.brand.red → theme.colors.primary.red or '#E63946'
theme.colors.brand.green → theme.colors.semantic.success or '#10B981'
theme.colors.brand.orange → theme.colors.semantic.warning or '#F59E0B'

// Dark variants (not in new theme, use darker shades)
theme.colors.brandDark.purple → '#5600b3'
theme.colors.brandDark.green → '#059669'
theme.colors.brandDark.orange → '#D97706'

// Text Colors
theme.colors.text.primary → theme.colors.dark.text.primary or '#FFFFFF'
theme.colors.text.secondary → theme.colors.dark.text.secondary or '#B3B3B3'
theme.colors.text.muted → theme.colors.dark.text.muted or '#999999'
theme.colors.text.inverse → '#FFFFFF'

// UI Colors
theme.colors.ui.background → theme.colors.dark.background or '#0A0A0A'
theme.colors.ui.surface → theme.colors.dark.surface or '#1A1A1A'
theme.colors.ui.border → theme.colors.dark.border or '#3A3A3A'
theme.colors.ui.divider → theme.colors.dark.border or '#3A3A3A'

// State Colors
theme.colors.state.hover → 'rgba(255, 255, 255, 0.05)'
theme.colors.state.active → 'rgba(255, 255, 255, 0.1)'
theme.colors.state.focus → theme.colors.primary.purple or '#6B00DB'

// Utility Colors
theme.colors.utility.red → theme.colors.semantic.error or '#EF4444'
theme.colors.utility.redDark → '#DC2626'
theme.colors.utility.green → theme.colors.semantic.success or '#10B981'
theme.colors.utility.greenDark → '#059669'
```

## Typography Mappings

```typescript
// Font Family
theme.typography.fontFamily.base → theme.typography.fontFamily.primary
theme.typography.fontFamily.mono → theme.typography.fontFamily.mono

// Font Size - same structure
theme.typography.fontSize.* → theme.typography.fontSize.*

// Font Weight - same structure
theme.typography.fontWeight.* → theme.typography.fontWeight.*

// Line Height - same structure
theme.typography.lineHeight.* → theme.typography.lineHeight.*

// Letter Spacing - same structure
theme.typography.letterSpacing.* → theme.typography.letterSpacing.*
```

## Spacing Mappings

```typescript
// Spacing values exist but might be in different structure
theme.spacing[0] → '0'
theme.spacing[1] → '0.25rem'
theme.spacing[2] → '0.5rem'
theme.spacing[3] → '0.75rem'
theme.spacing[4] → '1rem'
theme.spacing[5] → '1.25rem'
theme.spacing[6] → '1.5rem'
theme.spacing[8] → '2rem'
```

## Animation Mappings

```typescript
// Animations exist in index.ts
theme.animations.duration.* → Use hard-coded values or import from index
theme.animations.easing.* → Use hard-coded values or import from index
theme.animations.transition.all → 'all 0.2s ease'
theme.animations.transition.colors → 'color 0.2s ease, background-color 0.2s ease'
```

## Shadow Mappings

```typescript
// Shadows might not exist in new theme
theme.shadows.sm → '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
theme.shadows.md → '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
theme.shadows.lg → '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)'
theme.shadows.xl → '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
theme.shadows['2xl'] → '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
```

## Migration Strategy

1. **For Critical Components**: Replace theme references with hard-coded values
2. **For New Components**: Use the actual theme structure from `src/foundation/design-system/tokens`
3. **Import Path**: Use `import { theme } from '../../../tokens'` (not `../../../tokens/theme`)

## Example Migration

```typescript
// Before
import { theme } from '../../../tokens/theme';
const color = theme.colors.brand.purple;

// After (Option 1 - Use actual theme)
import { theme } from '../../../tokens';
const color = theme.colors.primary.purple;

// After (Option 2 - Hard-code for Shadow DOM)
const color = '#6B00DB';
```