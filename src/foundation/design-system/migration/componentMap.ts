// ABOUTME: Component migration map for gradual transition
// Maps old component imports to new design system components

/**
 * Migration map for Shadow components
 * This allows gradual migration by updating imports without changing component usage
 */
export const componentMigrationMap = {
  // Atoms
  'components/shadow/ShadowButton': '@/foundation/design-system/components/atoms/Button/ShadowButton',
  'components/shadow/ShadowFlashingButton': '@/foundation/design-system/components/atoms/Button/ShadowFlashingButton',
  'components/shadow/ShadowAvatar': '@/foundation/design-system/components/atoms/Avatar/ShadowAvatar',
  'components/shadow/ShadowStatusBadge': '@/foundation/design-system/components/atoms/Badge/ShadowStatusBadge',
  'components/shadow/ShadowIconHeader': '@/foundation/design-system/components/atoms/Header/ShadowIconHeader',
  'components/shadow/ShadowToast': '@/foundation/design-system/components/atoms/Toast/ShadowToast',
  
  // Molecules - Cards
  'components/shadow/ShadowActionCard': '@/foundation/design-system/components/molecules/Cards/ShadowActionCard',
  'components/shadow/ShadowInfoCard': '@/foundation/design-system/components/molecules/Cards/ShadowInfoCard',
  'components/shadow/ShadowStatCard': '@/foundation/design-system/components/molecules/Cards/ShadowStatCard',
  'components/shadow/ShadowEventCard': '@/foundation/design-system/components/molecules/Cards/ShadowEventCard',
  'components/shadow/ShadowProductCard': '@/foundation/design-system/components/molecules/Cards/ShadowProductCard',
  'components/shadow/ShadowCategoryCard': '@/foundation/design-system/components/molecules/Cards/ShadowCategoryCard',
  'components/shadow/ShadowPreEvaluationCard': '@/foundation/design-system/components/molecules/Cards/ShadowPreEvaluationCard',
  'components/shadow/ShadowEventDetailCard': '@/foundation/design-system/components/molecules/Cards/ShadowEventDetailCard',
  
  // Molecules - Modals
  'components/shadow/ShadowModal': '@/foundation/design-system/components/molecules/Modals/ShadowModal',
  'components/shadow/ShadowModalEnhanced': '@/foundation/design-system/components/molecules/Modals/ShadowModalEnhanced',
  'components/shadow/ShadowModalWithContent': '@/foundation/design-system/components/molecules/Modals/ShadowModalWithContent',
  'components/shadow/ShadowNotificationModal': '@/foundation/design-system/components/molecules/Modals/ShadowNotificationModal',
  'components/shadow/ShadowFormModal': '@/foundation/design-system/components/molecules/Modals/ShadowFormModal',
  'components/shadow/ShadowModalWithForm': '@/foundation/design-system/components/molecules/Modals/ShadowModalWithForm',
  
  // Molecules - Headers
  'components/shadow/ShadowSectionHeader': '@/foundation/design-system/components/molecules/Headers/ShadowSectionHeader',
  'components/shadow/ShadowHeaderSubHeader': '@/foundation/design-system/components/molecules/Headers/ShadowHeaderSubHeader',
  
  // Organisms
  'components/shadow/ShadowNavigationDrawer': '@/foundation/design-system/components/organisms/Navigation/ShadowNavigationDrawer',
  
  // Add more mappings as components are migrated
};

/**
 * Helper to get new import path
 */
export const getNewImportPath = (oldPath: string): string => {
  // Remove leading slash or ./
  const normalizedPath = oldPath.replace(/^\.?\//, '');
  
  // Check if we have a migration path
  for (const [old, newPath] of Object.entries(componentMigrationMap)) {
    if (normalizedPath.includes(old)) {
      return normalizedPath.replace(old, newPath);
    }
  }
  
  return oldPath;
};