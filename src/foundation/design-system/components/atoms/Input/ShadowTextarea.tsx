// ABOUTME: Textarea component with Shadow DOM encapsulation and advanced features
// Supports auto-resize, character counting, and comprehensive form validation

import React, { useRef, useEffect, useState } from 'react';

export type TextareaSize = 'small' | 'medium' | 'large';
export type ResizeMode = 'none' | 'both' | 'horizontal' | 'vertical';

export interface ShadowTextareaChangeEvent {
  target: {
    name: string;
    value: string;
    type: 'textarea';
  };
}

export interface ShadowTextareaProps {
  name: string;
  value: string;
  onChange: (event: ShadowTextareaChangeEvent) => void;
  size?: TextareaSize;
  placeholder?: string;
  label?: string;
  helpText?: string;
  error?: boolean;
  errorMessage?: string;
  disabled?: boolean;
  required?: boolean;
  rows?: number;
  maxLength?: number;
  minLength?: number;
  resize?: ResizeMode;
  autoResize?: boolean;
  showCharacterCount?: boolean;
  className?: string;
  autoComplete?: string;
}

export const ShadowTextarea: React.FC<ShadowTextareaProps> = ({
  name,
  value,
  onChange,
  size = 'medium',
  placeholder,
  label,
  helpText,
  error = false,
  errorMessage,
  disabled = false,
  required = false,
  rows = 4,
  maxLength,
  minLength,
  resize = 'vertical',
  autoResize = false,
  showCharacterCount = false,
  className = '',
  autoComplete
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const [currentValue, setCurrentValue] = useState(value);
  const [characterCount, setCharacterCount] = useState(value.length);

  // Size configurations
  const sizeConfig = {
    small: { 
      textarea: 'py-2 px-3 text-sm', 
      label: 'text-sm', 
      container: 'text-sm',
      minHeight: '80px'
    },
    medium: { 
      textarea: 'py-3 px-4 text-base', 
      label: 'text-sm', 
      container: 'text-base',
      minHeight: '100px'
    },
    large: { 
      textarea: 'py-4 px-5 text-lg', 
      label: 'text-base', 
      container: 'text-lg',
      minHeight: '120px'
    }
  };

  // Handle textarea changes
  const handleTextareaChange = (newValue: string) => {
    setCurrentValue(newValue);
    setCharacterCount(newValue.length);
    
    const event: ShadowTextareaChangeEvent = {
      target: {
        name,
        value: newValue,
        type: 'textarea'
      }
    };
    onChange(event);
  };

  // Auto-resize functionality
  const handleAutoResize = (textarea: HTMLTextAreaElement) => {
    if (autoResize) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.max(textarea.scrollHeight, parseInt(sizeConfig[size].minHeight)) + 'px';
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    try {
      // Create shadow DOM


      

      const shadowRoot = container.attachShadow({ mode: 'open' });
      shadowRootRef.current = shadowRoot;

      const config = sizeConfig[size];

      const styles = `
        :host {
          display: block;
          width: 100%;
          font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .textarea-container {
          position: relative;
          width: 100%;
        }

        .label {
          display: block;
          color: #E5E7EB;
          font-weight: 500;
          margin-bottom: 8px;
          font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .label.required::after {
          content: '*';
          color: #EF4444;
          margin-left: 4px;
        }

        .textarea-wrapper {
          position: relative;
        }

        .textarea {
          width: 100%;
          background: #1F2937;
          border: 1px solid ${error ? '#EF4444' : '#4B5563'};
          border-radius: 8px;
          color: #FFFFFF;
          font-size: inherit;
          font-family: inherit;
          line-height: 1.5;
          transition: all 0.2s ease;
          outline: none;
          resize: ${resize};
          min-height: ${config.minHeight};
          ${config.textarea}
          ${autoResize ? 'resize: none; overflow: hidden;' : ''}
        }

        .textarea:focus {
          border-color: ${error ? '#EF4444' : '#14B8A6'};
          box-shadow: 0 0 0 2px ${error ? 'rgba(239, 68, 68, 0.1)' : 'rgba(20, 184, 166, 0.1)'};
        }

        .textarea:disabled {
          background: #111827;
          border-color: #374151;
          color: #6B7280;
          cursor: not-allowed;
          resize: none;
        }

        .textarea::placeholder {
          color: #9CA3AF;
        }

        .footer {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-top: 6px;
          gap: 12px;
        }

        .help-text {
          font-size: ${size === 'large' ? '14px' : '12px'};
          color: ${error ? '#EF4444' : '#9CA3AF'};
          line-height: 1.4;
          flex: 1;
        }

        .character-count {
          font-size: ${size === 'large' ? '12px' : '11px'};
          color: ${error ? '#EF4444' : maxLength && characterCount > maxLength * 0.9 ? '#F59E0B' : '#9CA3AF'};
          font-weight: 500;
          white-space: nowrap;
          ${maxLength && characterCount > maxLength ? 'color: #EF4444;' : ''}
        }

        .visually-hidden {
          position: absolute;
          width: 1px;
          height: 1px;
          padding: 0;
          margin: -1px;
          overflow: hidden;
          clip: rect(0, 0, 0, 0);
          white-space: nowrap;
          border: 0;
        }
      `;

      const containerHTML = `
        <div class="textarea-container">
          ${label ? `
            <label class="label ${config.label} ${required ? 'required' : ''}">
              ${label}
            </label>
          ` : ''}
          
          <div class="textarea-wrapper">
            <textarea
              class="textarea ${config.container}"
              name="${name}"
              rows="${rows}"
              placeholder="${placeholder || ''}"
              ${disabled ? 'disabled' : ''}
              ${required ? 'required' : ''}
              ${maxLength ? `maxlength="${maxLength}"` : ''}
              ${minLength ? `minlength="${minLength}"` : ''}
              ${autoComplete ? `autocomplete="${autoComplete}"` : ''}
              aria-describedby="${helpText || errorMessage ? `${name}-help` : ''} ${showCharacterCount && maxLength ? `${name}-count` : ''}"
            >${currentValue}</textarea>
          </div>
          
          ${(helpText || errorMessage || showCharacterCount) ? `
            <div class="footer">
              ${(helpText || errorMessage) ? `
                <div class="help-text" id="${name}-help">
                  ${error && errorMessage ? errorMessage : helpText || ''}
                </div>
              ` : '<div></div>'}
              
              ${showCharacterCount ? `
                <div class="character-count" id="${name}-count" aria-live="polite">
                  <span class="visually-hidden">Character count: </span>
                  ${characterCount}${maxLength ? ` / ${maxLength}` : ''}
                </div>
              ` : ''}
            </div>
          ` : ''}
        </div>
      `;

      // Apply styles and HTML
      shadowRoot.innerHTML = `<style>${styles}</style>${containerHTML}`;

      // Event listeners
      const textarea = shadowRoot.querySelector('textarea');

      if (textarea) {
        // Input event for real-time updates
        textarea.addEventListener('input', (e) => {
          const target = e.target as HTMLTextAreaElement;
          handleTextareaChange(target.value);
          
          // Auto-resize if enabled
          handleAutoResize(target);
        });

        // Change event for form submission
        textarea.addEventListener('change', (e) => {
          const target = e.target as HTMLTextAreaElement;
          handleTextareaChange(target.value);
        });

        // Initial auto-resize if enabled
        if (autoResize) {
          handleAutoResize(textarea);
        }
      }

      return () => {
        if (shadowRootRef.current) {
          shadowRootRef.current.innerHTML = '';
        }
      };
    } catch (error) {
      console.error('Failed to create shadow DOM for ShadowTextarea:', error);
    }
  }, [
    name, currentValue, size, placeholder, label, helpText, 
    error, errorMessage, disabled, required, rows, maxLength, 
    minLength, resize, autoResize, showCharacterCount, autoComplete,
    characterCount
  ]);

  // Update current value when value prop changes
  useEffect(() => {
    if (value !== currentValue) {
      setCurrentValue(value);
      setCharacterCount(value.length);
    }
  }, [value]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{ display: 'block', width: '100%' }}
    />
  );
};

export default ShadowTextarea;