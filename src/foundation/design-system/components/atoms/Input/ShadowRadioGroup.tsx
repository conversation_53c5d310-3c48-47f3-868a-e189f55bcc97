// ABOUTME: Radio button group component with Shadow DOM encapsulation
// Provides accessible radio inputs with custom styling and validation support

import React, { useEffect, useRef } from 'react';

export interface RadioOption {
  value: string;
  label: string;
}

export interface ShadowRadioGroupProps {
  name: string;
  value?: string;
  options: RadioOption[];
  label?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  layout?: 'vertical' | 'horizontal';
  className?: string;
  onChange?: (value: string) => void;
  helpText?: string;
}

export interface ShadowRadioGroupChangeEvent {
  target: {
    name: string;
    value: string;
    type: 'radio';
  };
}

const ShadowRadioGroup: React.FC<ShadowRadioGroupProps> = ({ 
  name,
  value = '', 
  options = [],
  label = '',
  required = false,
  disabled = false,
  error = '',
  layout = 'vertical',
  className = '',
  onChange,
  helpText = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const groupId = `radio-group-${Math.random().toString(36).substring(2, 9)}`;

  useEffect(() => {
    if (!containerRef.current) return;

    // Create a div that will host our shadow DOM
    const host = document.createElement('div');
    host.className = className; // Apply any external classes to the host
    // Check if shadow root already exists

    if (host.shadowRoot) {

      return host.shadowRoot;

    }

    

    const shadow = host.attachShadow({ mode: 'open' });

    // Define styles
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700&display=swap');
        
        :host {
          display: block;
          width: 100%;
          margin-bottom: 16px;
        }

        .radio-group {
          position: relative;
          width: 100%;
        }

        fieldset {
          border: none;
          padding: 0;
          margin: 0;
        }

        legend {
          display: block;
          font-family: 'Poppins', sans-serif;
          font-weight: 500;
          margin-bottom: 10px;
          color: white;
          padding: 0;
          width: 100%;
        }

        .required::after {
          content: " *";
          color: #ff5d73;
        }

        .radio-options {
          display: flex;
          flex-direction: ${layout === 'horizontal' ? 'row' : 'column'};
          gap: ${layout === 'horizontal' ? '16px' : '8px'};
          flex-wrap: wrap;
        }

        .radio-option {
          display: flex;
          align-items: center;
          position: relative;
        }

        .input-wrapper {
          position: relative;
          display: inline-block;
          width: 20px;
          height: 20px;
          flex-shrink: 0;
          margin-right: 10px;
        }

        /* Hide the default radio but keep it clickable */
        input[type="radio"] {
          position: absolute;
          opacity: 0;
          width: 20px;
          height: 20px;
          cursor: pointer;
          z-index: 1;
        }

        /* Custom radio style */
        .custom-radio {
          position: absolute;
          top: 0;
          left: 0;
          width: 20px;
          height: 20px;
          background-color: #333;
          border: 2px solid #555;
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.2s;
        }

        /* Checked state */
        input[type="radio"]:checked + .custom-radio {
          border-color: #6B00DB; /* shot-purple */
        }

        /* The dot */
        input[type="radio"]:checked + .custom-radio::after {
          content: '';
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #6B00DB; /* shot-purple */
        }

        /* Focus state */
        input[type="radio"]:focus + .custom-radio {
          box-shadow: 0 0 0 2px rgba(107, 0, 219, 0.2);
        }

        /* Disabled state */
        input[type="radio"]:disabled + .custom-radio {
          background-color: #444;
          border-color: #555;
          cursor: not-allowed;
        }

        input[type="radio"]:disabled:checked + .custom-radio::after {
          background-color: #555;
        }

        input[type="radio"]:disabled ~ label {
          color: #888;
          cursor: not-allowed;
        }

        label {
          font-family: 'Poppins', sans-serif;
          color: white;
          font-size: 1rem;
          cursor: pointer;
          user-select: none;
        }

        /* Error state */
        .error .custom-radio {
          border-color: #ff5d73;
        }

        .error-message {
          font-family: 'Poppins', sans-serif;
          color: #ff5d73;
          font-size: 0.8125rem;
          margin-top: 6px;
        }

        /* Help text */
        .help-text {
          font-family: 'Poppins', sans-serif;
          color: #888;
          font-size: 0.8125rem;
          margin-top: 6px;
        }

        /* Responsive adjustments */
        @media (max-width: 640px) {
          .radio-options {
            flex-direction: column;
            gap: 12px;
          }
        }
      </style>
    `;

    // Create the radio options HTML
    const optionsHtml = options.map((option, index) => {
      const optionId = `${name}-${index}`;
      return `
        <div class="radio-option">
          <span class="input-wrapper">
            <input 
              type="radio" 
              id="${optionId}"
              name="${name}"
              value="${option.value}"
              ${value === option.value ? 'checked' : ''}
              ${disabled ? 'disabled' : ''}
              ${required ? 'required' : ''}
              aria-describedby="${error ? `${name}-error` : helpText ? `${name}-help` : ''}"
            />
            <span class="custom-radio"></span>
          </span>
          <label for="${optionId}">${option.label}</label>
        </div>
      `;
    }).join('');

    // Create the HTML
    shadow.innerHTML = `
      ${styles}
      <div class="radio-group ${error ? 'error' : ''}">
        <fieldset role="radiogroup" aria-labelledby="${label ? groupId : ''}">
          ${label ? `<legend id="${groupId}" class="${required ? 'required' : ''}">${label}</legend>` : ''}
          <div class="radio-options">
            ${optionsHtml}
          </div>
        </fieldset>
        ${error ? `<div id="${name}-error" class="error-message" role="alert">${error}</div>` : ''}
        ${!error && helpText ? `<div id="${name}-help" class="help-text">${helpText}</div>` : ''}
      </div>
    `;

    // Add event handlers
    const radioInputs = shadow.querySelectorAll('input[type="radio"]');
    if (radioInputs.length && onChange) {
      radioInputs.forEach(radio => {
        radio.addEventListener('change', (e: Event) => {
          const target = e.target as HTMLInputElement;
          onChange(target.value);
        });
      });
    }

    containerRef.current.appendChild(host);

    // Cleanup
    return () => {
      if (containerRef.current && host.parentNode) {
        containerRef.current.removeChild(host);
      }
    };
  }, [name, value, options, label, required, disabled, error, layout, className, onChange, helpText, groupId]);

  return <div ref={containerRef} />;
};

export default ShadowRadioGroup;