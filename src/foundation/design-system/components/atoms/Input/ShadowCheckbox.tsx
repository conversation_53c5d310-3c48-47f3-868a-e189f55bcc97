// ABOUTME: Checkbox component with Shadow DOM encapsulation and form integration
// Supports validation, help text, and custom styling with SHOT brand colors

import React, { useEffect, useRef } from 'react';

export interface ShadowCheckboxChangeEvent {
  target: {
    name: string;
    checked: boolean;
    value: boolean;
    type: 'checkbox';
  };
}

export interface ShadowCheckboxProps {
  name: string;
  checked: boolean;
  onChange: (event: ShadowCheckboxChangeEvent) => void;
  label: string;
  required?: boolean;
  disabled?: boolean;
  error?: boolean;
  errorMessage?: string;
  helpText?: string;
  className?: string;
}

export const ShadowCheckbox: React.FC<ShadowCheckboxProps> = ({
  name,
  checked,
  onChange,
  label,
  required = false,
  disabled = false,
  error = false,
  errorMessage,
  helpText,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const checkboxId = useRef(`checkbox-${name}-${Math.random().toString(36).substring(2, 9)}`).current;

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    try {
      // Create shadow DOM


      

      const shadowRoot = container.attachShadow({ mode: 'open' });
      shadowRootRef.current = shadowRoot;

      const styles = `
        :host {
          display: block;
          width: 100%;
          font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .checkbox-container {
          display: flex;
          align-items: flex-start;
          position: relative;
          min-height: 24px;
        }

        .input-wrapper {
          position: relative;
          display: inline-block;
          width: 20px;
          height: 20px;
          flex-shrink: 0;
          margin-right: 12px;
          margin-top: 2px;
        }

        /* Hide the default checkbox but keep it clickable */
        input[type="checkbox"] {
          position: absolute;
          opacity: 0;
          width: 20px;
          height: 20px;
          cursor: ${disabled ? 'not-allowed' : 'pointer'};
          z-index: 1;
          margin: 0;
        }

        /* Custom checkbox style */
        .custom-checkbox {
          position: absolute;
          top: 0;
          left: 0;
          width: 20px;
          height: 20px;
          background: #1F2937;
          border: 2px solid ${error ? '#EF4444' : '#4B5563'};
          border-radius: 4px;
          cursor: ${disabled ? 'not-allowed' : 'pointer'};
          transition: all 0.2s ease;
        }

        /* Hover state */
        input[type="checkbox"]:not(:disabled):hover + .custom-checkbox {
          border-color: ${error ? '#EF4444' : '#14B8A6'};
          background: #374151;
        }

        /* Checked state */
        input[type="checkbox"]:checked + .custom-checkbox {
          background: #14B8A6;
          border-color: #14B8A6;
        }

        /* Checkmark */
        input[type="checkbox"]:checked + .custom-checkbox::after {
          content: '';
          position: absolute;
          left: 5px;
          top: 1px;
          width: 6px;
          height: 11px;
          border: solid white;
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
        }

        /* Focus state */
        input[type="checkbox"]:focus + .custom-checkbox {
          box-shadow: 0 0 0 2px ${error ? 'rgba(239, 68, 68, 0.2)' : 'rgba(20, 184, 166, 0.2)'};
        }

        input[type="checkbox"]:focus-visible + .custom-checkbox {
          outline: 2px solid ${error ? '#EF4444' : '#14B8A6'};
          outline-offset: 2px;
        }

        /* Disabled state */
        input[type="checkbox"]:disabled + .custom-checkbox {
          background: #111827;
          border-color: #374151;
          cursor: not-allowed;
        }

        input[type="checkbox"]:disabled:checked + .custom-checkbox {
          background: #6B7280;
          border-color: #6B7280;
        }

        input[type="checkbox"]:disabled ~ .label-text {
          color: #6B7280;
          cursor: not-allowed;
        }

        .label-text {
          font-size: 14px;
          color: #E5E7EB;
          cursor: ${disabled ? 'not-allowed' : 'pointer'};
          user-select: none;
          line-height: 1.5;
          font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-weight: 500;
        }

        .label-text.required::after {
          content: '*';
          color: #EF4444;
          margin-left: 4px;
        }

        /* Error state */
        .error-message {
          color: #EF4444;
          font-size: 12px;
          margin-top: 6px;
          margin-left: 32px;
          line-height: 1.4;
        }

        /* Help text */
        .help-text {
          color: #9CA3AF;
          font-size: 12px;
          margin-top: 6px;
          margin-left: 32px;
          line-height: 1.4;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
          .label-text {
            font-size: 13px;
          }

          .error-message,
          .help-text {
            font-size: 11px;
          }
        }

        /* Animations */
        @keyframes checkmark {
          0% {
            opacity: 0;
            transform: rotate(45deg) scale(0);
          }
          100% {
            opacity: 1;
            transform: rotate(45deg) scale(1);
          }
        }

        input[type="checkbox"]:checked + .custom-checkbox::after {
          animation: checkmark 0.2s ease-out;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
          .custom-checkbox {
            border-width: 3px;
          }

          input[type="checkbox"]:checked + .custom-checkbox {
            background: #FFFFFF;
            border-color: #FFFFFF;
          }

          input[type="checkbox"]:checked + .custom-checkbox::after {
            border-color: #000000;
          }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
          .custom-checkbox,
          input[type="checkbox"]:checked + .custom-checkbox::after {
            transition: none;
            animation: none;
          }
        }
      `;

      const containerHTML = `
        <div>
          <div class="checkbox-container">
            <span class="input-wrapper">
              <input 
                type="checkbox" 
                id="${checkboxId}"
                name="${name}"
                ${checked ? 'checked' : ''}
                ${required ? 'required' : ''}
                ${disabled ? 'disabled' : ''}
                aria-describedby="${errorMessage ? `${checkboxId}-error` : helpText ? `${checkboxId}-help` : ''}"
                aria-invalid="${error}"
              />
              <span class="custom-checkbox"></span>
            </span>
            <label 
              for="${checkboxId}" 
              class="label-text ${required ? 'required' : ''}"
            >
              ${label}
            </label>
          </div>
          ${errorMessage ? `
            <div class="error-message" id="${checkboxId}-error" role="alert">
              ${errorMessage}
            </div>
          ` : helpText ? `
            <div class="help-text" id="${checkboxId}-help">
              ${helpText}
            </div>
          ` : ''}
        </div>
      `;

      // Apply styles and HTML
      shadowRoot.innerHTML = `<style>${styles}</style>${containerHTML}`;

      // Event listeners
      const checkbox = shadowRoot.querySelector('input[type="checkbox"]');
      
      if (checkbox) {
        const handleChange = (e: Event) => {
          const target = e.target as HTMLInputElement;
          const event: ShadowCheckboxChangeEvent = {
            target: {
              name,
              checked: target.checked,
              value: target.checked,
              type: 'checkbox'
            }
          };
          onChange(event);
        };

        checkbox.addEventListener('change', handleChange);
      }

      return () => {
        if (shadowRootRef.current) {
          shadowRootRef.current.innerHTML = '';
        }
      };
    } catch (error) {
      console.error('Failed to create shadow DOM for ShadowCheckbox:', error);
    }
  }, [name, checked, onChange, label, required, disabled, error, errorMessage, helpText, checkboxId]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{ display: 'block', width: '100%' }}
    />
  );
};

export default ShadowCheckbox;