// ABOUTME: Standard text input component that uses centralized form styling
// Non-shadow DOM version for use in regular React components

import React, { forwardRef } from 'react';
import { useFormStyles } from '../../providers/FormStyleProvider';

export interface StandardTextInputProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search' | 'date';
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  label?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  autoComplete?: string;
}

export const StandardTextInput = forwardRef<HTMLInputElement, StandardTextInputProps>(({
  type = 'text',
  name,
  value,
  onChange,
  placeholder,
  label,
  error,
  required = false,
  disabled = false,
  className = '',
  autoComplete,
}, ref) => {
  return (
    <div className="shot-form-field">
      {label && (
        <label htmlFor={name} className={`shot-form-label ${required ? 'required' : ''}`}>
          {label}
        </label>
      )}
      <input
        ref={ref}
        id={name}
        name={name}
        type={type}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        required={required}
        disabled={disabled}
        autoComplete={autoComplete}
        className={`shot-form-input ${error ? 'error' : ''} ${className}`}
      />
      {error && <div className="shot-form-error">{error}</div>}
    </div>
  );
});

StandardTextInput.displayName = 'StandardTextInput';

export default StandardTextInput;