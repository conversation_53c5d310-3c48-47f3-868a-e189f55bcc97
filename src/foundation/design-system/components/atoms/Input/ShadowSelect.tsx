// ABOUTME: Select dropdown component with Shadow DOM encapsulation and comprehensive features
// Supports multiple options, validation, and accessibility features

import React, { useRef, useEffect, useState } from 'react';

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export type SelectSize = 'small' | 'medium' | 'large';

export interface ShadowSelectChangeEvent {
  target: {
    name: string;
    value: string;
    type: 'select';
  };
}

export interface ShadowSelectProps {
  name: string;
  value: string;
  onChange: (event: ShadowSelectChangeEvent) => void;
  options: SelectOption[];
  size?: SelectSize;
  placeholder?: string;
  label?: string;
  helpText?: string;
  error?: boolean;
  errorMessage?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  onFocus?: () => void;
  onBlur?: () => void;
}

export const ShadowSelect: React.FC<ShadowSelectProps> = ({
  name,
  value,
  onChange,
  options,
  size = 'medium',
  placeholder = 'Select an option...',
  label,
  helpText,
  error = false,
  errorMessage,
  disabled = false,
  required = false,
  className = '',
  onFocus,
  onBlur
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const [currentValue, setCurrentValue] = useState(value);

  // Size configurations
  const sizeConfig = {
    small: { 
      select: 'py-2 px-3 pr-8 text-sm', 
      label: 'text-sm', 
      container: 'text-sm',
      arrow: 'right-2'
    },
    medium: { 
      select: 'py-3 px-4 pr-10 text-base', 
      label: 'text-sm', 
      container: 'text-base',
      arrow: 'right-3'
    },
    large: { 
      select: 'py-4 px-5 pr-12 text-lg', 
      label: 'text-base', 
      container: 'text-lg',
      arrow: 'right-4'
    }
  };

  // Handle select changes
  const handleSelectChange = (newValue: string) => {
    setCurrentValue(newValue);
    
    const event: ShadowSelectChangeEvent = {
      target: {
        name,
        value: newValue,
        type: 'select'
      }
    };
    onChange(event);
  };

  // Get selected option label
  const getSelectedLabel = () => {
    const selectedOption = options.find(option => option.value === currentValue);
    return selectedOption ? selectedOption.label : placeholder;
  };

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    try {
      // Create shadow DOM


      

      const shadowRoot = container.attachShadow({ mode: 'open' });
      shadowRootRef.current = shadowRoot;

      const config = sizeConfig[size];

      const styles = `
        :host {
          display: block;
          width: 100%;
          font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .select-container {
          position: relative;
          width: 100%;
        }

        .label {
          display: block;
          color: #E5E7EB;
          font-weight: 500;
          margin-bottom: 8px;
          font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .label.required::after {
          content: '*';
          color: #EF4444;
          margin-left: 4px;
        }

        .select-wrapper {
          position: relative;
          display: inline-block;
          width: 100%;
        }

        .select {
          width: 100%;
          background: #1F2937;
          border: 1px solid ${error ? '#EF4444' : '#4B5563'};
          border-radius: 8px;
          color: #FFFFFF;
          font-size: inherit;
          font-family: inherit;
          cursor: pointer;
          transition: all 0.2s ease;
          outline: none;
          appearance: none;
          -webkit-appearance: none;
          -moz-appearance: none;
          background-image: none;
          ${config.select}
        }

        .select:focus {
          border-color: ${error ? '#EF4444' : '#14B8A6'};
          box-shadow: 0 0 0 2px ${error ? 'rgba(239, 68, 68, 0.1)' : 'rgba(20, 184, 166, 0.1)'};
        }

        .select:disabled {
          background: #111827;
          border-color: #374151;
          color: #6B7280;
          cursor: not-allowed;
        }

        .select option {
          background: #1F2937;
          color: #FFFFFF;
          padding: 8px 12px;
        }

        .select option:disabled {
          color: #6B7280;
          cursor: not-allowed;
        }

        .select option:hover:not(:disabled) {
          background: #374151;
        }

        .select option:checked {
          background: #14B8A6;
          color: #FFFFFF;
        }

        .arrow {
          position: absolute;
          top: 50%;
          ${config.arrow};
          transform: translateY(-50%);
          pointer-events: none;
          color: ${error ? '#EF4444' : disabled ? '#6B7280' : '#9CA3AF'};
          transition: color 0.2s ease;
        }

        .select:focus + .arrow {
          color: ${error ? '#EF4444' : '#14B8A6'};
        }

        .arrow svg {
          width: ${size === 'small' ? '16px' : size === 'medium' ? '20px' : '24px'};
          height: ${size === 'small' ? '16px' : size === 'medium' ? '20px' : '24px'};
        }

        .help-text {
          font-size: ${size === 'large' ? '14px' : '12px'};
          color: ${error ? '#EF4444' : '#9CA3AF'};
          margin-top: 6px;
          line-height: 1.4;
        }

        /* Custom select styling for better cross-browser compatibility */
        @supports (-webkit-appearance: none) {
          .select {
            background-image: none;
          }
        }

        @supports (-moz-appearance: none) {
          .select {
            background-image: none;
          }
        }
      `;

      const containerHTML = `
        <div class="select-container">
          ${label ? `
            <label class="label ${config.label} ${required ? 'required' : ''}">
              ${label}
            </label>
          ` : ''}
          
          <div class="select-wrapper">
            <select
              class="select ${config.container}"
              name="${name}"
              ${disabled ? 'disabled' : ''}
              ${required ? 'required' : ''}
              aria-describedby="${helpText || errorMessage ? `${name}-help` : ''}"
            >
              ${!currentValue && placeholder ? `<option value="" disabled>${placeholder}</option>` : ''}
              ${options.map(option => `
                <option 
                  value="${option.value}" 
                  ${option.value === currentValue ? 'selected' : ''}
                  ${option.disabled ? 'disabled' : ''}
                >
                  ${option.label}
                </option>
              `).join('')}
            </select>
            
            <div class="arrow">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="6,9 12,15 18,9"></polyline>
              </svg>
            </div>
          </div>
          
          ${(helpText || errorMessage) ? `
            <div class="help-text" id="${name}-help">
              ${error && errorMessage ? errorMessage : helpText || ''}
            </div>
          ` : ''}
        </div>
      `;

      // Apply styles and HTML
      shadowRoot.innerHTML = `<style>${styles}</style>${containerHTML}`;

      // Event listeners
      const select = shadowRoot.querySelector('select');

      if (select) {
        select.addEventListener('change', (e) => {
          const target = e.target as HTMLSelectElement;
          handleSelectChange(target.value);
        });

        select.addEventListener('focus', () => {
          onFocus?.();
        });

        select.addEventListener('blur', () => {
          onBlur?.();
        });
      }

      return () => {
        if (shadowRootRef.current) {
          shadowRootRef.current.innerHTML = '';
        }
      };
    } catch (error) {
      console.error('Failed to create shadow DOM for ShadowSelect:', error);
    }
  }, [
    name, currentValue, options, size, placeholder, label, helpText, 
    error, errorMessage, disabled, required, onFocus, onBlur
  ]);

  // Update current value when value prop changes
  useEffect(() => {
    if (value !== currentValue) {
      setCurrentValue(value);
    }
  }, [value]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{ display: 'block', width: '100%' }}
    />
  );
};

export default ShadowSelect;