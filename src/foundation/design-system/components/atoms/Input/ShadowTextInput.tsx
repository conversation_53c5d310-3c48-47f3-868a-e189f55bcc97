// ABOUTME: Text input component with Shadow DOM encapsulation and comprehensive form features
// Supports validation, icons, password toggle, and multiple input types

import React, { useRef, useEffect, useState } from 'react';
import { getFormStylesForShadowDOM } from '../../providers/FormStyleProvider';

export type InputType = 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search' | 'date';
export type InputSize = 'small' | 'medium' | 'large';
export type IconPosition = 'left' | 'right';

export interface ShadowInputChangeEvent {
  target: {
    name: string;
    value: string;
    type: string;
  };
}

export interface ShadowTextInputProps {
  type?: InputType;
  size?: InputSize;
  name: string;
  value: string;
  onChange: (event: ShadowInputChangeEvent) => void;
  placeholder?: string;
  label?: string;
  helpText?: string;
  error?: boolean;
  errorMessage?: string;
  disabled?: boolean;
  required?: boolean;
  icon?: string;
  iconPosition?: IconPosition;
  className?: string;
  autoComplete?: string;
  maxLength?: number;
  minLength?: number;
}

export const ShadowTextInput: React.FC<ShadowTextInputProps> = ({
  type = 'text',
  size = 'medium',
  name,
  value,
  onChange,
  placeholder,
  label,
  helpText,
  error = false,
  errorMessage,
  disabled = false,
  required = false,
  icon,
  iconPosition = 'left',
  className = '',
  autoComplete,
  maxLength,
  minLength
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const onChangeRef = useRef(onChange);

  // Update onChange ref when it changes
  useEffect(() => {
    onChangeRef.current = onChange;
  }, [onChange]);

  // Icon path definitions
  const iconPaths = {
    user: 'M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2M12 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8z',
    mail: 'M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2zM22 6l-10 7L2 6',
    lock: 'M19 11H5V7a7 7 0 0 1 14 0v4zM5 11h14v8H5v-8z',
    phone: 'M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72',
    search: 'm21 21-6-6m2-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0z',
    calendar: 'M8 2v4m8-4v4M3 10h18M5 4h14a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2z',
    globe: 'M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z',
    hash: 'M4 9h16M4 15h16M10 3L8 21M16 3l-2 18',
    eye: 'M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8zM12 12a3 3 0 1 0 0-6 3 3 0 0 0 0 6z',
    'eye-off': 'M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M1 1l22 22'
  };

  // Size configurations
  const sizeConfig = {
    small: { 
      input: 'py-2 px-3 text-sm', 
      label: 'text-sm', 
      icon: 'width: 16px; height: 16px;',
      container: 'text-sm',
      iconPadding: '36px'
    },
    medium: { 
      input: 'py-3 px-4 text-base', 
      label: 'text-sm', 
      icon: 'width: 20px; height: 20px;',
      container: 'text-base',
      iconPadding: '44px'
    },
    large: { 
      input: 'py-4 px-5 text-lg', 
      label: 'text-base', 
      icon: 'width: 24px; height: 24px;',
      container: 'text-lg',
      iconPadding: '52px'
    }
  };

  // Initialize Shadow DOM only once
  useEffect(() => {
    const container = containerRef.current;
    if (!container || shadowRootRef.current) return;

    // Create shadow root
    shadowRootRef.current = container.attachShadow({ mode: 'open' });

    const config = sizeConfig[size];
    const formStyles = getFormStylesForShadowDOM();

    // Create the complete structure
    const wrapper = document.createElement('div');
    wrapper.innerHTML = `
      <style>
        :host {
          display: block;
          width: 100%;
          font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        ${formStyles}

        .input-container {
          position: relative;
          width: 100%;
        }

        .label {
          display: block;
          font-weight: 500;
          margin-bottom: 8px;
          font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          ${config.label}
        }

        .label.required::after {
          content: ' *';
          color: #EF4444;
        }

        .input-wrapper {
          position: relative;
          display: flex;
          align-items: center;
        }

        .input {
          font-family: inherit;
          ${config.input}
          width: 100%;
          background: rgba(255, 255, 255, 0.05);
          border: 2px solid rgba(255, 255, 255, 0.1);
          color: white;
          border-radius: 12px;
          transition: all 0.3s ease;
        }

        .input:focus {
          outline: none;
          border-color: #FF6B35;
          background: rgba(255, 255, 255, 0.08);
          box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.2);
        }

        .input.error {
          border-color: #EF4444;
        }

        .input:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .icon {
          position: absolute;
          color: #9CA3AF;
          pointer-events: none;
          ${config.icon}
        }

        .icon.clickable {
          pointer-events: auto;
          cursor: pointer;
        }

        .icon.clickable:hover {
          color: #FFFFFF;
        }

        .icon.left {
          left: ${size === 'small' ? '12px' : size === 'medium' ? '14px' : '16px'};
        }

        .icon.right {
          right: ${size === 'small' ? '12px' : size === 'medium' ? '14px' : '16px'};
        }

        .help-text {
          font-size: ${size === 'large' ? '14px' : '12px'};
          color: #9CA3AF;
          margin-top: 6px;
          line-height: 1.4;
        }

        .help-text.error {
          color: #EF4444;
        }

        svg {
          display: block;
          width: 100%;
          height: 100%;
        }
      </style>
      <div class="input-container">
        <label class="label" style="display: none;"></label>
        <div class="input-wrapper">
          <div class="icon left" style="display: none;">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path></path>
            </svg>
          </div>
          <input class="input ${config.container}" type="text" />
          <div class="icon right" style="display: none;">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path></path>
            </svg>
          </div>
        </div>
        <div class="help-text" style="display: none;"></div>
      </div>
    `;

    shadowRootRef.current.appendChild(wrapper.firstElementChild);
    shadowRootRef.current.appendChild(wrapper.lastElementChild);

    // Get references
    inputRef.current = shadowRootRef.current.querySelector('input');

    // Setup event handlers
    if (inputRef.current) {
      const handleInput = (e: Event) => {
        const target = e.target as HTMLInputElement;
        const event: ShadowInputChangeEvent = {
          target: {
            name: target.name,
            value: target.value,
            type: target.type
          }
        };
        onChangeRef.current(event);
      };

      inputRef.current.addEventListener('input', handleInput);
      inputRef.current.addEventListener('change', handleInput);
    }

    return () => {
      if (shadowRootRef.current) {
        shadowRootRef.current.innerHTML = '';
      }
    };
  }, []); // Only run once

  // Update input properties
  useEffect(() => {
    if (!inputRef.current || !shadowRootRef.current) return;

    const config = sizeConfig[size];
    const actualType = type === 'password' && showPassword ? 'text' : type;

    // Update input attributes
    inputRef.current.type = actualType;
    inputRef.current.name = name;
    inputRef.current.value = value;
    inputRef.current.placeholder = placeholder || '';
    inputRef.current.disabled = disabled;
    inputRef.current.required = required;
    if (autoComplete) inputRef.current.setAttribute('autocomplete', autoComplete);
    if (maxLength) inputRef.current.maxLength = maxLength;
    if (minLength) inputRef.current.minLength = minLength;

    // Update input classes
    const classList = ['input', config.container];
    if (error) classList.push('error');
    
    // Handle icon padding
    if (icon && iconPosition === 'left') {
      inputRef.current.style.paddingLeft = config.iconPadding;
    } else {
      inputRef.current.style.paddingLeft = '';
    }
    
    if ((icon || type === 'password') && iconPosition === 'right') {
      inputRef.current.style.paddingRight = config.iconPadding;
    } else {
      inputRef.current.style.paddingRight = '';
    }
    
    inputRef.current.className = classList.join(' ');
  }, [value, type, name, placeholder, disabled, required, error, size, icon, iconPosition, showPassword, autoComplete, maxLength, minLength]);

  // Update label
  useEffect(() => {
    if (!shadowRootRef.current) return;
    const labelElement = shadowRootRef.current.querySelector('.label');
    if (labelElement) {
      if (label) {
        labelElement.textContent = label;
        labelElement.setAttribute('style', '');
        labelElement.className = `label ${required ? 'required' : ''}`;
      } else {
        labelElement.setAttribute('style', 'display: none;');
      }
    }
  }, [label, required]);

  // Update icons
  useEffect(() => {
    if (!shadowRootRef.current) return;

    const leftIcon = shadowRootRef.current.querySelector('.icon.left') as HTMLElement;
    const rightIcon = shadowRootRef.current.querySelector('.icon.right') as HTMLElement;
    const displayIcon = type === 'password' && iconPosition === 'right' 
      ? (showPassword ? 'eye-off' : 'eye')
      : icon;

    // Left icon
    if (leftIcon) {
      if (displayIcon && iconPosition === 'left') {
        const path = leftIcon.querySelector('path');
        if (path) path.setAttribute('d', iconPaths[displayIcon as keyof typeof iconPaths] || '');
        leftIcon.style.display = '';
      } else {
        leftIcon.style.display = 'none';
      }
    }

    // Right icon
    if (rightIcon) {
      if (displayIcon && iconPosition === 'right') {
        const path = rightIcon.querySelector('path');
        if (path) path.setAttribute('d', iconPaths[displayIcon as keyof typeof iconPaths] || '');
        rightIcon.style.display = '';
        
        // Handle password toggle
        if (type === 'password') {
          rightIcon.classList.add('clickable');
          rightIcon.onclick = () => setShowPassword(!showPassword);
        } else {
          rightIcon.classList.remove('clickable');
          rightIcon.onclick = null;
        }
      } else {
        rightIcon.style.display = 'none';
      }
    }
  }, [icon, iconPosition, type, showPassword]);

  // Update help text
  useEffect(() => {
    if (!shadowRootRef.current) return;
    const helpTextElement = shadowRootRef.current.querySelector('.help-text') as HTMLElement;
    if (helpTextElement) {
      if (helpText || errorMessage) {
        helpTextElement.textContent = error && errorMessage ? errorMessage : helpText || '';
        helpTextElement.className = `help-text ${error ? 'error' : ''}`;
        helpTextElement.style.display = '';
      } else {
        helpTextElement.style.display = 'none';
      }
    }
  }, [helpText, errorMessage, error]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{ display: 'block', width: '100%' }}
    />
  );
};

export default ShadowTextInput;