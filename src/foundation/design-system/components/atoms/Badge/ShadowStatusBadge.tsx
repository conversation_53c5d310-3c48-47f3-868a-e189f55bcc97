// ABOUTME: Shadow DOM Status Badge component for displaying status indicators
// Shows status with icon and appropriate color coding

import React, { useEffect, useRef } from 'react';
export interface ShadowStatusBadgeProps {
  status: 'draft' | 'published' | 'cancelled' | 'completed' | 'pending' | 'active';
  className?: string;
  size?: 'sm' | 'md';
}

const statusConfig = {
  draft: {
    bg: 'rgba(247, 182, 19, 0.2)',
    color: '#F59E0B',
    border: 'rgba(247, 182, 19, 0.3)',
    label: 'Draft',
  },
  published: {
    bg: 'rgba(26, 188, 156, 0.2)',
    color: '#10B981',
    border: 'rgba(26, 188, 156, 0.3)',
    label: 'Published',
  },
  cancelled: {
    bg: 'rgba(239, 68, 68, 0.2)',
    color: '#EF4444',
    border: 'rgba(239, 68, 68, 0.3)',
    label: 'Cancelled',
  },
  completed: {
    bg: 'rgba(107, 0, 219, 0.2)',
    color: '#6B00DB',
    border: 'rgba(107, 0, 219, 0.3)',
    label: 'Completed',
  },
  pending: {
    bg: 'rgba(251, 191, 36, 0.2)',
    color: '#fbbf24',
    border: 'rgba(251, 191, 36, 0.3)',
    label: 'Pending',
  },
  active: {
    bg: 'rgba(59, 130, 246, 0.2)',
    color: '#3B82F6',
    border: 'rgba(59, 130, 246, 0.3)',
    label: 'Active',
  },
};

export const ShadowStatusBadge: React.FC<ShadowStatusBadgeProps> = ({
  status,
  className = '',
  size = 'md',
}) => {
  const hostRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);

  useEffect(() => {
    if (!hostRef.current || shadowRef.current) return;

    // Create shadow root


    

    shadowRef.current = hostRef.current.attachShadow({ mode: 'open' });

    // Create styles
    const config = statusConfig[status];
    
    const styles = `
      <style>
      :host {
        display: inline-block;
      }

      .status-badge {
        display: inline-flex;
        align-items: center;
        padding: ${size === 'sm' ? `0.25rem 0.5rem` : `0.5rem 0.75rem`};
        border-radius: 0.25rem;
        font-family: 'Poppins', sans-serif;
        font-size: ${size === 'sm' ? '0.75rem' : '0.875rem'};
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.025em;
        transition: all 0.2s ease;
        background-color: ${config.bg};
        color: ${config.color};
        border: 1px solid ${config.border};
      }
      
      .status-icon {
        width: ${size === 'sm' ? '0.75rem' : '0.875rem'};
        height: ${size === 'sm' ? '0.75rem' : '0.875rem'};
        margin-right: 0.25rem;
        fill: none;
        stroke: currentColor;
        stroke-width: 2;
      }
      
      ${className ? `.custom { ${className} }` : ''}
    </style>
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Create badge element
    const badge = document.createElement('div');
    badge.className = 'status-badge';
    if (className) badge.classList.add('custom');

    // Create icon based on status
    const iconSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    iconSvg.setAttribute('class', 'status-icon');
    iconSvg.setAttribute('viewBox', '0 0 24 24');

    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path.setAttribute('stroke-linecap', 'round');
    path.setAttribute('stroke-linejoin', 'round');

    // Set icon path based on status
    switch (status) {
      case 'draft':
      case 'pending':
        path.setAttribute('d', 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z');
        break;
      case 'published':
      case 'completed':
      case 'active':
        path.setAttribute('d', 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z');
        break;
      case 'cancelled':
        path.setAttribute('d', 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z');
        break;
    }

    iconSvg.appendChild(path);
    badge.appendChild(iconSvg);

    // Add label
    const label = document.createElement('span');
    label.textContent = config.label;
    badge.appendChild(label);

    // Append to shadow root
    shadowRef.current.appendChild(badge);

    // Cleanup
    return () => {
      if (shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, [status, className, size]);

  return <div ref={hostRef} />;
};