// ABOUTME: Shadow DOM Toast component for temporary notifications
// Displays brief messages with auto-dismiss functionality

import React, { useEffect, useRef } from 'react';
export interface ShadowToastProps {
  isOpen: boolean;
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  position?: 'top' | 'bottom' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  onClose: () => void;
  className?: string;
}

const typeConfig = {
  success: {
    bg: 'rgba(26, 188, 156, 0.1)',
    border: 'rgba(26, 188, 156, 0.3)',
    icon: '✓',
    iconColor: '#10B981',
  },
  error: {
    bg: 'rgba(239, 68, 68, 0.1)',
    border: 'rgba(239, 68, 68, 0.3)',
    icon: '✕',
    iconColor: '#EF4444',
  },
  warning: {
    bg: 'rgba(247, 182, 19, 0.1)',
    border: 'rgba(247, 182, 19, 0.3)',
    icon: '⚠',
    iconColor: '#F59E0B',
  },
  info: {
    bg: 'rgba(107, 0, 219, 0.1)',
    border: 'rgba(107, 0, 219, 0.3)',
    icon: 'ℹ',
    iconColor: '#6B00DB',
  },
};

const positionStyles = {
  'top': 'top: 20px; left: 50%; transform: translateX(-50%);',
  'bottom': 'bottom: 20px; left: 50%; transform: translateX(-50%);',
  'top-left': 'top: 20px; left: 20px;',
  'top-right': 'top: 20px; right: 20px;',
  'bottom-left': 'bottom: 20px; left: 20px;',
  'bottom-right': 'bottom: 20px; right: 20px;',
};

export const ShadowToast: React.FC<ShadowToastProps> = ({
  isOpen,
  message,
  type = 'info',
  duration = 3000,
  position = 'bottom',
  onClose,
  className = ''
}) => {
  const hostRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (isOpen && duration > 0) {
      timeoutRef.current = setTimeout(() => {
        onClose();
      }, duration);
    }
    
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isOpen, duration, onClose]);

  useEffect(() => {
    if (!hostRef.current || shadowRef.current) return;

    // Create shadow root


    

    shadowRef.current = hostRef.current.attachShadow({ mode: 'open' });

    // Create styles
    const config = typeConfig[type];
    
    const styles = `
      <style>
      :host {
        position: fixed;
        ${positionStyles[position]}
        z-index: 9999;
        display: ${isOpen ? 'block' : 'none'};
      }
      
      .toast {
        background: #1A1A1A;
        border: 1px solid ${config.border};
        background-color: ${config.bg};
        border-radius: 0.5rem;
        padding: 1rem 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.75rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        animation: ${position.includes('top') ? 'slideDown' : 'slideUp'} 300ms ease-out;
        font-family: 'Poppins', sans-serif;
        font-size: 1rem;
        min-width: 300px;
        max-width: 500px;
      }
      
      .toast-icon {
        font-size: 1.25rem;
        color: ${config.iconColor};
        flex-shrink: 0;
        line-height: 1;
      }
      
      .toast-message {
        flex: 1;
        color: #FFFFFF;
        line-height: ${1.5};
      }
      
      .toast-close {
        background: none;
        border: none;
        color: #B3B3B3;
        cursor: pointer;
        padding: 0.25rem;
        margin: -0.25rem;
        border-radius: 0.25rem;
        transition: color 0.2s ease, background-color 0.2s ease;
        font-size: 1.125rem;
        line-height: 1;
      }
      
      .toast-close:hover {
        color: #FFFFFF;
        background: rgba(255, 255, 255, 0.05);
      }
      
      @keyframes slideUp {
        from {
          transform: translateY(20px);
          opacity: 0;
        }
        to {
          transform: translateY(0);
          opacity: 1;
        }
      }
      
      @keyframes slideDown {
        from {
          transform: translateY(-20px);
          opacity: 0;
        }
        to {
          transform: translateY(0);
          opacity: 1;
        }
      }
      
      ${className ? `.custom { ${className} }` : ''}
    </style>
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Cleanup
    return () => {
      if (shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, [type, position, className]);

  useEffect(() => {
    if (!shadowRef.current || !isOpen) return;

    // Clear existing content except style
    const style = shadowRef.current.querySelector('style');
    const existingContent = shadowRef.current.querySelector('.toast');
    if (existingContent) existingContent.remove();

    if (!isOpen) return;

    const config = typeConfig[type];

    // Create toast container
    const toast = document.createElement('div');
    toast.className = 'toast';
    if (className) toast.classList.add('custom');

    // Icon
    const iconEl = document.createElement('span');
    iconEl.className = 'toast-icon';
    iconEl.textContent = config.icon;
    toast.appendChild(iconEl);

    // Message
    const messageEl = document.createElement('span');
    messageEl.className = 'toast-message';
    messageEl.textContent = message;
    toast.appendChild(messageEl);

    // Close button
    const closeBtn = document.createElement('button');
    closeBtn.className = 'toast-close';
    closeBtn.innerHTML = '×';
    closeBtn.onclick = onClose;
    toast.appendChild(closeBtn);

    shadowRef.current.appendChild(toast);
  }, [isOpen, message, type, className, onClose]);

  return <div ref={hostRef} style={{ display: isOpen ? 'block' : 'none' }} />;
};