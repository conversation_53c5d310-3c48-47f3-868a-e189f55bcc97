// ABOUTME: Circular teaser component with gradient ring and Shadow DOM encapsulation
// Used for feature highlights and navigation elements with animated interactions

import React, { useEffect, useRef } from 'react';

export type CircleSize = 'small' | 'medium' | 'large';
export type GradientColor = [string, string];

export interface ShadowCircleTeaserProps {
  title: string;
  gradientColors?: GradientColor;
  onClick?: () => void;
  size?: CircleSize;
  className?: string;
}

export const ShadowCircleTeaser: React.FC<ShadowCircleTeaserProps> = ({ 
  title,
  gradientColors = ['#14B8A6', '#0F766E'],
  onClick,
  size = 'medium',
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    try {
      // Check if shadow root already exists
      let shadowRoot = shadowRootRef.current;
      
      if (!shadowRoot) {
        // Only create shadow DOM if it doesn't exist
        shadowRoot = container.attachShadow({ mode: 'open' });
        shadowRootRef.current = shadowRoot;
      }

      const [color1, color2] = gradientColors;

      // Size configurations
      const sizeConfig = {
        small: { 
          outer: 80, 
          inner: 74, 
          padding: 12, 
          fontSize: 11,
          borderWidth: 3
        },
        medium: { 
          outer: 100, 
          inner: 94, 
          padding: 15, 
          fontSize: 13,
          borderWidth: 3
        },
        large: { 
          outer: 120, 
          inner: 114, 
          padding: 18, 
          fontSize: 15,
          borderWidth: 3
        }
      };

      const config = sizeConfig[size];

      const styles = `
        :host {
          display: inline-block;
          flex-shrink: 0;
          width: ${config.outer}px;
          font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .circle-teaser {
          width: ${config.outer}px;
          text-align: center;
          cursor: ${onClick ? 'pointer' : 'default'};
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
        }

        .circle-teaser:hover {
          transform: ${onClick ? 'scale(1.05)' : 'none'};
        }

        .circle-teaser:active {
          transform: ${onClick ? 'scale(0.95)' : 'none'};
        }

        .circle-ring {
          width: ${config.outer}px;
          height: ${config.outer}px;
          border-radius: 50%;
          background: linear-gradient(135deg, ${color1}, ${color2});
          padding: ${config.borderWidth}px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .circle-ring::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          border-radius: 50%;
          background: linear-gradient(135deg, ${color2}, ${color1});
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .circle-teaser:hover .circle-ring::before {
          opacity: ${onClick ? '1' : '0'};
        }

        .circle-teaser:hover .circle-ring {
          filter: brightness(1.1);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 
                      0 4px 10px rgba(20, 184, 166, 0.2);
        }

        .circle-inner {
          width: ${config.inner}px;
          height: ${config.inner}px;
          border-radius: 50%;
          background: #1F2937;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          padding: ${config.padding}px;
          box-sizing: border-box;
          position: relative;
          z-index: 1;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .circle-text {
          font-size: ${config.fontSize}px;
          font-weight: 600;
          color: #FFFFFF;
          text-align: center;
          line-height: 1.3;
          word-wrap: break-word;
          max-width: 100%;
          letter-spacing: 0.3px;
        }

        /* Accessibility improvements */
        .circle-teaser:focus {
          outline: none;
        }

        .circle-teaser:focus-visible .circle-ring {
          outline: 2px solid #14B8A6;
          outline-offset: 2px;
        }

        /* Animation */
        @keyframes fadeInScale {
          from {
            opacity: 0;
            transform: scale(0.8);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }

        .circle-teaser {
          animation: fadeInScale 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Pulse animation for interactive elements */
        ${onClick ? `
          @keyframes pulse {
            0% {
              box-shadow: 0 0 0 0 rgba(20, 184, 166, 0.7);
            }
            70% {
              box-shadow: 0 0 0 10px rgba(20, 184, 166, 0);
            }
            100% {
              box-shadow: 0 0 0 0 rgba(20, 184, 166, 0);
            }
          }

          .circle-teaser:hover .circle-ring {
            animation: pulse 2s infinite;
          }
        ` : ''}

        /* Mobile responsive adjustments */
        @media (max-width: 768px) {
          .circle-text {
            font-size: ${Math.max(config.fontSize - 1, 10)}px;
          }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
          .circle-inner {
            border: 2px solid #FFFFFF;
          }
          
          .circle-text {
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
          }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
          .circle-teaser {
            animation: none;
            transition: none;
          }
          
          .circle-teaser:hover {
            transform: none;
          }
          
          .circle-ring {
            transition: none;
          }
        }
      `;

      const containerHTML = `
        <div 
          class="circle-teaser" 
          role="${onClick ? 'button' : 'presentation'}"
          ${onClick ? 'tabindex="0"' : ''}
          ${onClick ? `aria-label="Circle teaser: ${title}"` : ''}
        >
          <div class="circle-ring">
            <div class="circle-inner">
              <span class="circle-text">${title}</span>
            </div>
          </div>
        </div>
      `;

      // Apply styles and HTML
      shadowRoot.innerHTML = `<style>${styles}</style>${containerHTML}`;

      // Event listeners
      const teaserElement = shadowRoot.querySelector('.circle-teaser');
      
      if (teaserElement && onClick) {
        const handleClick = () => onClick();
        const handleKeyDown = (e: KeyboardEvent) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onClick();
          }
        };

        teaserElement.addEventListener('click', handleClick);
        teaserElement.addEventListener('keydown', handleKeyDown);
      }

      return () => {
        if (shadowRootRef.current) {
          shadowRootRef.current.innerHTML = '';
        }
      };
    } catch (error) {
      console.error('Failed to create shadow DOM for ShadowCircleTeaser:', error);
    }
  }, [title, gradientColors, onClick, size]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{ display: 'inline-block' }}
    />
  );
};

export default ShadowCircleTeaser;