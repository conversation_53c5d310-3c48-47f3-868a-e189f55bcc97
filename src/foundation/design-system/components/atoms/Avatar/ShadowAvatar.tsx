// ABOUTME: Shadow DOM Avatar component with design system integration
// Displays user avatar with image or initial fallback

import React, { useEffect, useRef } from 'react';
export interface ShadowAvatarProps {
  src?: string;
  name?: string;
  size?: 'sm' | 'md' | 'lg' | 'small' | 'medium' | 'large';
  onClick?: () => void;
  showBorder?: boolean;
  borderColor?: string;
  className?: string;
}

const sizeMap = {
  sm: { container: '32px', font: '0.875rem' },
  md: { container: '48px', font: '1.125rem' },
  lg: { container: '80px', font: '1.5rem' },
};

export const ShadowAvatar: React.FC<ShadowAvatarProps> = ({
  src,
  name = 'User',
  size = 'md',
  onClick,
  showBorder = true,
  borderColor = '#6B00DB',
  className = '',
}) => {
  const hostRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);

  useEffect(() => {
    if (!hostRef.current || shadowRef.current) return;

    // Create shadow root


    

    shadowRef.current = hostRef.current.attachShadow({ mode: 'open' });

    // Create styles
    // Handle both naming conventions for size
    const normalizedSize = size === 'small' ? 'sm' : size === 'medium' ? 'md' : size === 'large' ? 'lg' : size;
    const sizeConfig = sizeMap[normalizedSize] || sizeMap.md;
    
    const styles = `
      <style>
      :host {
        display: inline-block;
        --border-color: ${borderColor};
      }

      .avatar-container {
        display: inline-block;
        position: relative;
        cursor: ${onClick ? 'pointer' : 'default'};
        transition: transform 0.2s ease;
      }

      .avatar-container:hover {
        transform: ${onClick ? 'scale(1.05)' : 'none'};
      }

      .avatar {
        width: ${sizeConfig.container};
        height: ${sizeConfig.container};
        border-radius: 50%;
        overflow: hidden;
        background: #1A1A1A;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        ${showBorder ? `border: 3px solid var(--border-color);` : ''}
      }

      .avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .avatar-placeholder {
        width: 100%;
        height: 100%;
        background: #6B00DB;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #FFFFFF;
        font-weight: 700;
        font-size: ${sizeConfig.font};
        font-family: 'Poppins', sans-serif;
        text-transform: uppercase;
      }
      
      ${className ? `.custom { ${className} }` : ''}
    </style>
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Create avatar element
    const container = document.createElement('div');
    container.className = 'avatar-container';
    if (className) container.classList.add('custom');

    const avatar = document.createElement('div');
    avatar.className = 'avatar';

    if (src) {
      const img = document.createElement('img');
      img.src = src;
      img.alt = name;
      avatar.appendChild(img);
    } else {
      const placeholder = document.createElement('div');
      placeholder.className = 'avatar-placeholder';
      placeholder.textContent = name.charAt(0).toUpperCase();
      avatar.appendChild(placeholder);
    }

    container.appendChild(avatar);

    // Add click handler
    if (onClick) {
      container.addEventListener('click', onClick);
    }

    // Append to shadow root
    shadowRef.current.appendChild(container);

    // Cleanup
    return () => {
      if (shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, [src, name, size, onClick, showBorder, borderColor, className]);

  return <div ref={hostRef} />;
};