// ABOUTME: Shadow DOM Flashing Button component - CTA button with animated effects
// Provides attention-grabbing buttons for critical actions with customizable variants

import React, { useEffect, useRef } from 'react';
import { theme } from '../../../tokens';

export interface ShadowFlashingButtonProps {
  text: string;
  onClick: () => void;
  disabled?: boolean;
  variant?: 'purple' | 'teal' | 'gold';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  fullWidth?: boolean;
}

const generateUniqueId = () => `btn-${Math.random().toString(36).substr(2, 9)}`;

export const ShadowFlashingButton: React.FC<ShadowFlashingButtonProps> = ({
  text,
  onClick,
  disabled = false,
  variant = 'purple',
  size = 'md',
  className = '',
  fullWidth = true
}) => {
  const hostRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const buttonRef = useRef<HTMLButtonElement | null>(null);
  const onClickRef = useRef<(() => void) | undefined>(onClick);

  // Update onClick ref when it changes
  useEffect(() => {
    onClickRef.current = onClick;
  }, [onClick]);

  // Create Shadow DOM only once
  useEffect(() => {
    const container = hostRef.current;
    if (!container || shadowRootRef.current) return;

    try {
      // Check if shadow root already exists
      if (container.shadowRoot) {
        shadowRootRef.current = container.shadowRoot;
        return;
      }

      const shadowRoot = container.attachShadow({ mode: 'open' });
      shadowRootRef.current = shadowRoot;

      const getVariantColors = () => {
        switch (variant) {
          case 'teal':
            return {
              bg: theme.colors.primary.teal,
              hover: '#0DD9BD',
              flash: '#3AFFEE',
              text: theme.colors.neutral.black
            };
          case 'gold':
            return {
              bg: theme.colors.primary.gold,
              hover: '#E6C200',
              flash: '#FFED4E',
              text: theme.colors.neutral.black
            };
          default: // purple
            return {
              bg: theme.colors.primary.purple,
              hover: '#5400A8',
              flash: '#8B20FF',
              text: theme.colors.neutral.white
            };
        }
      };

      const colors = getVariantColors();
      
      const getSizeStyles = () => {
        switch (size) {
          case 'sm':
            return `
              padding: ${theme.spacing[2]} ${theme.spacing[4]};
              font-size: ${theme.typography.fontSize.sm};
            `;
          case 'lg':
            return `
              padding: ${theme.spacing[5]} ${theme.spacing[8]};
              font-size: ${theme.typography.fontSize.lg};
            `;
          default: // md
            return `
              padding: ${theme.spacing[4]} ${theme.spacing[6]};
              font-size: ${theme.typography.fontSize.base};
            `;
        }
      };

      const style = document.createElement('style');
      style.textContent = `
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap');
        
        :host {
          display: ${fullWidth ? 'block' : 'inline-block'};
          width: ${fullWidth ? '100%' : 'auto'};
        }

        @keyframes flash {
          0%, 100% {
            background: ${colors.bg};
            box-shadow: 0 0 0 0 ${colors.bg}70;
          }
          50% {
            background: ${colors.flash};
            box-shadow: 0 0 20px 5px ${colors.bg}50;
          }
        }

        @keyframes pulse {
          0% {
            box-shadow: 0 0 0 0 ${colors.bg}70;
          }
          70% {
            box-shadow: 0 0 0 15px ${colors.bg}00;
          }
          100% {
            box-shadow: 0 0 0 0 ${colors.bg}00;
          }
        }

        .button {
          width: ${fullWidth ? '100%' : 'auto'};
          ${getSizeStyles()}
          background: ${colors.bg};
          color: ${colors.text};
          font-weight: ${theme.typography.fontWeight.bold};
          text-transform: uppercase;
          letter-spacing: 0.1em;
          border: none;
          border-radius: ${theme.spacing[2]};
          cursor: pointer;
          transition: all ${theme.animations.duration.normal} ${theme.animations.easing.standard};
          font-family: 'Poppins', sans-serif;
          position: relative;
          overflow: hidden;
          animation: flash 1.5s ease-in-out infinite, pulse 2s infinite;
        }

        .button:hover:not(:disabled) {
          background: ${colors.hover};
          transform: translateY(-1px);
          box-shadow: 0 10px 15px -3px ${colors.bg}30;
        }

        .button:active:not(:disabled) {
          transform: translateY(0);
        }

        .button:focus-visible {
          outline: 2px solid ${colors.bg};
          outline-offset: 2px;
        }

        .button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          animation: none;
        }

        /* Shine effect on hover */
        .button::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s ease;
        }

        .button:hover::before {
          left: 100%;
        }
      `;

      const button = document.createElement('button');
      button.className = 'button';
      button.textContent = text;
      button.disabled = disabled;
      button.type = 'button';
      
      // Add click handler
      button.addEventListener('click', () => {
        if (!disabled && onClickRef.current) {
          onClickRef.current();
        }
      });
      buttonRef.current = button;

      shadowRoot.appendChild(style);
      shadowRoot.appendChild(button);
    } catch (error) {
      console.error('Failed to create shadow DOM for ShadowFlashingButton:', error);
    }
  }, []); // Only run once on mount

  // Update button properties when they change
  useEffect(() => {
    if (buttonRef.current) {
      buttonRef.current.textContent = text;
      buttonRef.current.disabled = disabled;
    }
  }, [text, disabled]);

  return <div ref={hostRef} className={className} />;
};