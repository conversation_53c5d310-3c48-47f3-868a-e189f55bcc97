// ABOUTME: Shadow DOM Button component using design system tokens
// Migrated from src/components/shadow/ShadowButton.tsx with proper Shadow DOM isolation

import React, { useEffect, useRef } from 'react';
import { createButtonStyles } from '../../../utils/styling';
export interface ShadowButtonProps {
  text?: string;
  variant?: 'primary' | 'secondary' | 'success' | 'error' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  className?: string;
  flash?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  type?: 'button' | 'submit' | 'reset';
  children?: React.ReactNode;
}

export const ShadowButton: React.FC<ShadowButtonProps> = ({ 
  text = 'Click me', 
  variant = 'primary', 
  size = 'md', 
  onClick,
  className = '',
  flash = false,
  disabled = false,
  fullWidth = false,
  type = 'button',
  children
}) => {
  const hostRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);
  const buttonRef = useRef<HTMLButtonElement | null>(null);
  const onClickRef = useRef<(() => void) | undefined>(onClick);

  // Update onClick ref when it changes
  useEffect(() => {
    onClickRef.current = onClick;
  }, [onClick]);

  // Create Shadow DOM only once
  useEffect(() => {
    if (!hostRef.current || shadowRef.current) return;

    // Create shadow root


    

    shadowRef.current = hostRef.current.attachShadow({ mode: 'open' });

    // Create styles
    const buttonStyles = createButtonStyles({ variant, size, fullWidth });
    
    const styles = `
      <style>
      :host {
        display: ${fullWidth ? 'block' : 'inline-block'};
        ${disabled ? 'opacity: 0.5; cursor: not-allowed;' : ''}
      }
      
      button {
        ${buttonStyles}
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-family: 'Poppins', sans-serif;
        ${flash ? `animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;` : ''}
        ${disabled ? 'pointer-events: none;' : ''}
      }
      
      button:focus-visible {
        outline: 2px solid ${'#6B00DB'};
        outline-offset: 2px;
      }
      
      @keyframes pulse {
        0%, 100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }
      
      /* Custom classes support */
      ${className ? `.custom { ${className} }` : ''}
    </style>
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Create button element
    const button = document.createElement('button');
    button.type = type;
    button.textContent = children ? children.toString() : text;
    if (className) button.classList.add('custom');
    if (disabled) button.disabled = true;
    
    // Add click handler
    button.addEventListener('click', () => {
      if (!disabled && onClickRef.current) {
        onClickRef.current();
      }
    });
    buttonRef.current = button;

    // Append to shadow root
    shadowRef.current.appendChild(button);

    // Cleanup
    return () => {
      if (shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, []); // Only run once on mount

  // Update button content when text or children change
  useEffect(() => {
    if (buttonRef.current) {
      buttonRef.current.textContent = children ? children.toString() : text;
      buttonRef.current.disabled = disabled;
    }
  }, [text, children, disabled]);

  return <div ref={hostRef} />;
};