// ABOUTME: Shadow DOM Back Button component for navigation
// Provides a styled back button with customizable text and colors

import React, { useEffect, useRef } from 'react';
import { theme } from '../../../tokens';

export interface ShadowBackButtonProps {
  text?: string;
  color?: 'white' | 'teal' | 'purple' | 'gold' | 'green';
  iconColor?: 'white' | 'teal' | 'purple' | 'gold' | 'green';
  onClick?: () => void;
  className?: string;
}

const colorMap = {
  white: theme.colors.neutral.white,
  teal: theme.colors.primary.teal,
  purple: theme.colors.primary.purple,
  gold: theme.colors.primary.gold,
  green: theme.colors.semantic.success
};

export const ShadowBackButton: React.FC<ShadowBackButtonProps> = ({ 
  text = 'Back',
  color = 'white',
  iconColor,
  onClick,
  className = ''
}) => {
  const hostRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);

  const finalIconColor = iconColor || color;

  useEffect(() => {
    if (!hostRef.current || shadowRef.current) return;

    // Create shadow root


    

    shadowRef.current = hostRef.current.attachShadow({ mode: 'open' });

    // Create styles
    const textColor = colorMap[color];
    const iconColorValue = colorMap[finalIconColor];
    
    const styles = `
      @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
      
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }
      
      :host {
        display: inline-block;
      }

      .back-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        font-family: 'Poppins', sans-serif;
        font-size: 1rem;
        font-weight: 600;
        letter-spacing: 0.025em;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.5rem 0;
        transition: all 0.3s ease;
        text-transform: uppercase;
        position: relative;
        color: ${textColor};
      }

      .back-button:hover {
        transform: translateX(-2px);
      }

      .back-button:active {
        transform: translateX(-1px);
      }

      /* Focus state */
      .back-button:focus-visible {
        outline: 2px solid ${colorMap.purple};
        outline-offset: 2px;
        border-radius: 0.25rem;
      }

      /* Icon wrapper */
      .icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        transition: transform 0.3s ease;
      }

      .back-button:hover .icon-wrapper {
        transform: translateX(-2px);
      }

      .icon-wrapper svg {
        display: block;
        width: 20px;
        height: 20px;
        stroke: ${iconColorValue};
        fill: none;
      }

      /* Button text */
      .button-text {
        line-height: 1.25;
        margin: 0;
      }

      /* Add subtle underline on hover */
      .button-text::after {
        content: '';
        position: absolute;
        left: 28px; /* Offset for icon */
        right: 0;
        bottom: 0.25rem;
        height: 1px;
        background: currentColor;
        opacity: 0;
        transform: scaleX(0);
        transform-origin: left;
        transition: all 0.3s ease;
      }

      .back-button:hover .button-text::after {
        opacity: 0.3;
        transform: scaleX(1);
      }

      /* Mobile adjustments */
      @media (max-width: 640px) {
        .back-button {
          font-size: 0.875rem;
        }

        .icon-wrapper svg {
          width: 18px;
          height: 18px;
        }
      }
      
      ${className ? `.custom { ${className} }` : ''}
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Cleanup
    return () => {
      if (shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, [color, finalIconColor, className]);

  useEffect(() => {
    if (!shadowRef.current) return;

    // Clear existing content except style
    const style = shadowRef.current.querySelector('style');
    shadowRef.current.innerHTML = '';
    if (style) shadowRef.current.appendChild(style);

    // Create the button
    const button = document.createElement('button');
    button.className = 'back-button';
    if (className) button.classList.add('custom');

    // Create icon wrapper
    const iconWrapper = document.createElement('div');
    iconWrapper.className = 'icon-wrapper';
    iconWrapper.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <path d="M15 18l-6-6 6-6" />
      </svg>
    `;

    // Create text span
    const textSpan = document.createElement('span');
    textSpan.className = 'button-text';
    textSpan.textContent = text;

    // Assemble button
    button.appendChild(iconWrapper);
    button.appendChild(textSpan);

    // Add click handler
    button.addEventListener('click', () => {
      if (onClick) {
        onClick();
      } else {
        // Default behavior: go back in browser history
        window.history.back();
      }
    });

    shadowRef.current.appendChild(button);
  }, [text, onClick]);

  return <div ref={hostRef} />;
};