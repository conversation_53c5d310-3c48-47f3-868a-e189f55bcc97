// ABOUTME: Core tag component for displaying individual tags with various styles
// Used as the base for tag clouds, filters, and topic labels throughout the app

import React from 'react';
import { Hash, X } from 'lucide-react';

interface ShadowTagProps {
  text: string;
  variant?: 'default' | 'active' | 'removable';
  size?: 'small' | 'medium' | 'large';
  icon?: boolean;
  onRemove?: () => void;
  onClick?: () => void;
}

export const ShadowTag: React.FC<ShadowTagProps> = ({
  text,
  variant = 'default',
  size = 'medium',
  icon = true,
  onRemove,
  onClick
}) => {
  const shadowRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (!shadowRef.current || shadowRef.current.shadowRoot) return;

    const shadowRoot = shadowRef.current.attachShadow({ mode: 'open' });
    
    const sizeClasses = {
      small: 'padding: 2px 8px; font-size: 12px;',
      medium: 'padding: 4px 12px; font-size: 14px;',
      large: 'padding: 6px 16px; font-size: 16px;'
    };

    const variantClasses = {
      default: 'background: #2a2a2a; color: #cccccc;',
      active: 'background: #40E0D0; color: #1a1a1a;',
      removable: 'background: #3a3a3a; color: #ffffff;'
    };

    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap');
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        :host {
          display: inline-block;
          font-family: 'Poppins', sans-serif;
        }

        .tag {
          display: inline-flex;
          align-items: center;
          gap: 4px;
          ${sizeClasses[size]}
          ${variantClasses[variant]}
          border-radius: 9999px;
          transition: all 0.2s ease;
          cursor: ${onClick ? 'pointer' : 'default'};
          user-select: none;
        }

        .tag:hover {
          ${variant === 'default' ? 'background: #3a3a3a; color: #ffffff;' : ''}
          ${variant === 'active' ? 'background: #5ae5d6;' : ''}
          ${variant === 'removable' ? 'background: #4a4a4a;' : ''}
        }

        .icon {
          width: ${size === 'small' ? '10px' : size === 'large' ? '14px' : '12px'};
          height: ${size === 'small' ? '10px' : size === 'large' ? '14px' : '12px'};
        }

        .remove-btn {
          margin-left: 4px;
          cursor: pointer;
          transition: opacity 0.2s ease;
        }

        .remove-btn:hover {
          opacity: 0.7;
        }
      </style>
    `;

    const content = `
      <div class="tag" ${onClick ? 'role="button" tabindex="0"' : ''}>
        ${icon ? `
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M4 9h16M4 15h16M10 3L8 21M16 3l-2 18"/>
          </svg>
        ` : ''}
        <span>${text}</span>
        ${onRemove ? `
          <svg class="icon remove-btn" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M18 6L6 18M6 6l12 12"/>
          </svg>
        ` : ''}
      </div>
    `;

    shadowRoot.innerHTML = styles + content;

    // Add event listeners
    if (onClick) {
      const tagElement = shadowRoot.querySelector('.tag');
      tagElement?.addEventListener('click', onClick);
    }

    if (onRemove) {
      const removeBtn = shadowRoot.querySelector('.remove-btn');
      removeBtn?.addEventListener('click', (e) => {
        e.stopPropagation();
        onRemove();
      });
    }

    // Cleanup
    return () => {
      shadowRoot.innerHTML = '';
    };
  }, [text, variant, size, icon, onClick, onRemove]);

  return <div ref={shadowRef} />;
};