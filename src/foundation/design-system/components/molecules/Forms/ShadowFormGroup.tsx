// ABOUTME: Form group component with Shadow DOM for organizing form fields
// Provides flexible layouts (vertical, horizontal, grid) with consistent styling

import React, { useEffect, useRef, useState, ReactNode } from 'react';
import ReactDOM from 'react-dom';

export type FormLayout = 'vertical' | 'horizontal' | 'grid';
export type FormGap = 'small' | 'medium' | 'large';
export type FormColumns = 1 | 2 | 3 | 4;

export interface ShadowFormGroupProps {
  children: ReactNode;
  legend?: string;
  layout?: FormLayout;
  columns?: FormColumns;
  gap?: FormGap;
  className?: string;
  style?: React.CSSProperties;
}

export const ShadowFormGroup: React.FC<ShadowFormGroupProps> = ({
  children,
  legend,
  layout = 'vertical',
  columns = 1,
  gap = 'medium',
  className = '',
  style = {}
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const [childrenContainer, setChildrenContainer] = useState<HTMLDivElement | null>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    try {
      // Create shadow DOM


      

      const shadowRoot = container.attachShadow({ mode: 'open' });
      shadowRootRef.current = shadowRoot;

      // Gap configurations
      const gapConfig = {
        small: '8px',
        medium: '16px',
        large: '24px'
      };

      const styles = `
        :host {
          display: block;
          width: 100%;
          font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .form-group {
          background: #1F2937;
          border: 1px solid #374151;
          border-radius: 12px;
          padding: 24px;
          width: 100%;
          box-sizing: border-box;
          transition: all 0.3s ease;
        }

        .form-group:hover {
          border-color: #4B5563;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .form-group-content {
          display: ${layout === 'grid' ? 'grid' : 'flex'};
          flex-direction: ${layout === 'horizontal' ? 'row' : 'column'};
          ${layout === 'horizontal' ? 'align-items: flex-end;' : ''}
          ${layout === 'horizontal' ? 'flex-wrap: wrap;' : ''}
          ${layout === 'grid' ? `grid-template-columns: repeat(${columns}, minmax(0, 1fr));` : ''}
          gap: ${gapConfig[gap]};
          width: 100%;
        }
        
        legend {
          font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-weight: 600;
          color: #FFFFFF;
          font-size: 16px;
          margin: 0 0 20px 0;
          padding: 0;
          display: block;
          letter-spacing: 0.3px;
        }

        /* Separator line after legend */
        legend::after {
          content: '';
          display: block;
          width: 60px;
          height: 3px;
          background: #14B8A6;
          margin-top: 8px;
          border-radius: 2px;
          transition: width 0.3s ease;
        }

        .form-group:hover legend::after {
          width: 100px;
        }

        /* Children container styles */
        .children-container {
          width: 100%;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
          .form-group {
            padding: 16px;
          }

          legend {
            font-size: 15px;
            margin-bottom: 16px;
          }

          .form-group-content {
            display: flex !important;
            flex-direction: column !important;
            gap: ${gapConfig[gap]} !important;
          }
        }

        @media (max-width: 480px) {
          .form-group {
            padding: 12px;
            border-radius: 8px;
          }

          legend {
            font-size: 14px;
            margin-bottom: 12px;
          }

          .form-group-content {
            gap: ${gap === 'large' ? gapConfig.medium : gap === 'medium' ? gapConfig.small : gapConfig.small} !important;
          }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
          .form-group {
            border: 2px solid #FFFFFF;
          }

          legend {
            text-decoration: underline;
          }
        }

        /* Animation for content appearance */
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .form-group-content {
          animation: fadeIn 0.3s ease-out;
        }

        /* Focus within styles */
        .form-group:focus-within {
          border-color: #14B8A6;
          box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.1);
        }

        /* Print styles */
        @media print {
          .form-group {
            border: 1px solid #000;
            background: white;
            box-shadow: none;
          }

          legend {
            color: #000;
          }
        }
      `;

      const containerHTML = `
        <fieldset class="form-group" ${legend ? '' : 'aria-label="Form group"'}>
          ${legend ? `<legend>${legend}</legend>` : ''}
          <div class="form-group-content">
            <div class="children-container"></div>
          </div>
        </fieldset>
      `;

      // Apply styles and HTML
      shadowRoot.innerHTML = `<style>${styles}</style>${containerHTML}`;

      // Get the children container for React portal
      const childrenDiv = shadowRoot.querySelector('.children-container') as HTMLDivElement;
      setChildrenContainer(childrenDiv);

      return () => {
        if (shadowRootRef.current) {
          shadowRootRef.current.innerHTML = '';
        }
      };
    } catch (error) {
      console.error('Failed to create shadow DOM for ShadowFormGroup:', error);
    }
  }, [legend, layout, columns, gap]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{
        display: 'block',
        width: '100%',
        marginBottom: '24px',
        ...style
      }}
    >
      {childrenContainer && ReactDOM.createPortal(children, childrenContainer)}
    </div>
  );
};

export default ShadowFormGroup;