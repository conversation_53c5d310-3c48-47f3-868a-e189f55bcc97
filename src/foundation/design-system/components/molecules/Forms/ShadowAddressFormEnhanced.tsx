// ABOUTME: Enhanced Shadow DOM address form with dynamic country-specific fields
// Adapts form layout and validation based on selected country configuration

import React, { useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom/client';
import { AddressData } from '../../../../../types/addressConfig';
import { getAddressConfig, validateAddressField, SUPPORTED_COUNTRIES } from '../../../../../config/addressFormats';

export interface ShadowAddressFormEnhancedProps {
  formData: AddressData;
  onChange: (data: AddressData) => void;
  errors?: Partial<Record<keyof AddressData, string>>;
  title?: string;
  showEmailPhone?: boolean;
  defaultCountry?: string;
  className?: string;
}

const ShadowAddressFormEnhanced: React.FC<ShadowAddressFormEnhancedProps> = ({
  formData,
  onChange,
  errors = {},
  title = 'Shipping Address',
  showEmailPhone = true,
  defaultCountry = 'GB',
  className = ''
}) => {
  const [country, setCountry] = useState(formData.country || defaultCountry);
  const [localErrors, setLocalErrors] = useState<Partial<Record<keyof AddressData, string>>>({});
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const reactRootRef = useRef<ReactDOM.Root | null>(null);

  // Get country configuration
  const config = getAddressConfig(country);

  useEffect(() => {
    // Update form data when country changes
    if (country !== formData.country) {
      onChange({ ...formData, country });
    }
  }, [country]);

  const handleFieldChange = (fieldName: keyof AddressData, value: string) => {
    // Validate field
    const validation = validateAddressField(country, fieldName, value);
    
    if (!validation.isValid) {
      setLocalErrors(prev => ({ ...prev, [fieldName]: validation.error }));
    } else {
      setLocalErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }

    // Update form data
    onChange({ ...formData, [fieldName]: value });
  };

  useEffect(() => {
    if (!containerRef.current) return;

    // Create a div that will host our shadow DOM
    const host = document.createElement('div');
    host.className = className;
    // Check if shadow root already exists

    if (host.shadowRoot) {

      return host.shadowRoot;

    }

    

    const shadow = host.attachShadow({ mode: 'open' });
    shadowRootRef.current = shadow;

    // Define styles
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700&display=swap');
        
        :host {
          display: block;
          width: 100%;
        }

        .address-form-container {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          font-family: 'Poppins', sans-serif;
        }

        .form-title {
          font-size: 1.25rem;
          font-weight: 600;
          color: white;
          margin-bottom: 0.5rem;
        }

        .country-section {
          margin-bottom: 1rem;
          padding-bottom: 1rem;
          border-bottom: 1px solid #374151;
        }

        .country-label {
          display: block;
          font-size: 0.875rem;
          font-weight: 500;
          color: #d1d5db;
          margin-bottom: 0.5rem;
        }

        .country-selector {
          position: relative;
          width: 100%;
        }

        .country-select {
          width: 100%;
          padding: 0.625rem 0.75rem;
          padding-left: 3rem;
          background-color: #1f2937;
          border: 1px solid #374151;
          border-radius: 0.5rem;
          color: white;
          font-size: 1rem;
          font-family: 'Poppins', sans-serif;
          appearance: none;
          cursor: pointer;
          transition: border-color 0.2s, box-shadow 0.2s;
        }

        .country-select:focus {
          outline: none;
          border-color: #eab308;
          box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
        }

        .country-flag {
          position: absolute;
          left: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          font-size: 1.5rem;
          pointer-events: none;
        }

        .select-arrow {
          position: absolute;
          right: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          pointer-events: none;
          color: #9CA3AF;
        }

        .form-row {
          display: grid;
          gap: 1rem;
        }

        .form-row.two-column {
          grid-template-columns: 1fr 1fr;
        }

        .form-row.single-column {
          grid-template-columns: 1fr;
        }

        .form-field {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        label {
          display: block;
          font-size: 0.875rem;
          font-weight: 500;
          color: #d1d5db;
        }

        label.required::after {
          content: ' *';
          color: #ef4444;
        }

        .form-input,
        .form-select {
          width: 100%;
          padding: 0.625rem 0.75rem;
          background-color: #1f2937;
          border: 1px solid #374151;
          border-radius: 0.5rem;
          color: white;
          font-size: 1rem;
          font-family: 'Poppins', sans-serif;
          transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-input::placeholder {
          color: #6b7280;
        }

        .form-input:focus,
        .form-select:focus {
          outline: none;
          border-color: #eab308;
          box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
        }

        .form-input.error,
        .form-select.error {
          border-color: #ef4444;
        }

        .error-message {
          font-size: 0.75rem;
          color: #ef4444;
          margin-top: 0.25rem;
        }

        @media (max-width: 640px) {
          .form-row.two-column {
            grid-template-columns: 1fr;
          }
        }
      </style>
    `;

    // Create the HTML structure
    shadow.innerHTML = `
      ${styles}
      <div class="address-form-container">
        ${title ? `<h3 class="form-title">${title}</h3>` : ''}
        
        <div class="country-section">
          <label class="country-label required">Country/Region</label>
          <div class="country-selector">
            <span class="country-flag">${SUPPORTED_COUNTRIES.find(c => c.code === country)?.flag || '🌍'}</span>
            <select class="country-select" id="country-select">
              ${SUPPORTED_COUNTRIES.map(c => `
                <option value="${c.code}" ${c.code === country ? 'selected' : ''}>
                  ${c.name}
                </option>
              `).join('')}
            </select>
            <svg class="select-arrow" width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
              <path d="M2.5 4.5L6 8L9.5 4.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
            </svg>
          </div>
        </div>

        <div class="form-fields" id="form-fields-root"></div>
      </div>
    `;

    // Add event listener for country change
    const countrySelect = shadow.getElementById('country-select') as HTMLSelectElement;
    if (countrySelect) {
      countrySelect.addEventListener('change', (e) => {
        const target = e.target as HTMLSelectElement;
        setCountry(target.value);
      });
    }

    // Create React root for form fields
    const formFieldsContainer = shadow.getElementById('form-fields-root');
    if (formFieldsContainer) {
      const reactRoot = ReactDOM.createRoot(formFieldsContainer);
      reactRootRef.current = reactRoot;
    }

    containerRef.current.appendChild(host);

    // Cleanup
    return () => {
      // Use setTimeout to avoid React's synchronous unmount warning
      setTimeout(() => {
        if (reactRootRef.current) {
          reactRootRef.current.unmount();
          reactRootRef.current = null;
        }
      }, 0);
      if (containerRef.current && host.parentNode) {
        containerRef.current.removeChild(host);
      }
    };
  }, [className, title]);

  // Render form fields when dependencies change
  useEffect(() => {
    if (!reactRootRef.current || !shadowRootRef.current) return;

    const renderField = (fieldName: keyof AddressData) => {
      const field = config.fields.find(f => f.name === fieldName);
      if (!field || field.hidden) return null;

      // Skip email/phone if not showing them
      if (!showEmailPhone && (fieldName === 'email' || fieldName === 'phone')) {
        return null;
      }

      const error = errors[fieldName] || localErrors[fieldName];
      const value = formData[fieldName] || '';

      // Handle special labels
      let label = field.label;
      if (fieldName === 'postalCode') label = config.postalCodeLabel;
      if (fieldName === 'state') label = config.stateLabel;

      return (
        <div key={fieldName} className={`form-field ${field.type === 'select' ? '' : 'text-field'}`}>
          <label htmlFor={fieldName} className={field.required ? 'required' : ''}>
            {label}
          </label>
          
          {field.type === 'select' && field.options ? (
            <select
              id={fieldName}
              name={fieldName}
              value={value}
              onChange={(e) => handleFieldChange(fieldName, e.target.value)}
              className={`form-select ${error ? 'error' : ''}`}
              required={field.required}
            >
              <option value="">{field.placeholder}</option>
              {field.options.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          ) : (
            <input
              type={field.type || 'text'}
              id={fieldName}
              name={fieldName}
              value={value}
              onChange={(e) => handleFieldChange(fieldName, e.target.value)}
              placeholder={field.placeholder}
              className={`form-input ${error ? 'error' : ''}`}
              autoComplete={field.autocomplete}
              required={field.required}
              maxLength={field.validation?.maxLength}
            />
          )}
          
          {error && <span className="error-message">{error}</span>}
        </div>
      );
    };

    // Group fields for layout
    const renderFieldGroup = () => {
      const fieldGroups = [
        ['firstName', 'lastName'],
        ['email'],
        ['phone'],
        ['address1'],
        ['address2'],
        ['city'],
        ['state', 'postalCode']
      ];

      return fieldGroups.map((group, index) => {
        const fields = group.map(fieldName => renderField(fieldName as keyof AddressData)).filter(Boolean);
        if (fields.length === 0) return null;

        return (
          <div key={index} className={`form-row ${group.length > 1 ? 'two-column' : 'single-column'}`}>
            {fields}
          </div>
        );
      });
    };

    const FormFields = () => <>{renderFieldGroup()}</>;
    reactRootRef.current.render(<FormFields />);

  }, [country, formData, errors, localErrors, showEmailPhone, config]);

  // Update country select when country prop changes
  useEffect(() => {
    if (!shadowRootRef.current) return;
    const countrySelect = shadowRootRef.current.getElementById('country-select') as HTMLSelectElement;
    if (countrySelect && countrySelect.value !== country) {
      countrySelect.value = country;
    }
  }, [country]);

  return <div ref={containerRef} />;
};

export default ShadowAddressFormEnhanced;