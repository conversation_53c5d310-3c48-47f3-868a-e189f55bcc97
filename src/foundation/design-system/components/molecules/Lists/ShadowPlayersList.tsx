// ABOUTME: Players list component with comprehensive filtering, selection, and evaluation display
// Supports multiple variants (guardian, coach, parent, team) with rich interactivity

import React, { useRef, useEffect, useState } from 'react';

export interface PlayerData {
  id: string;
  name: string;
  nickname?: string;
  avatar?: string;
  position?: string;
  age?: number;
  status?: string;
  teams?: Array<{ name: string; age_group?: string }>;
  ratings?: {
    technical?: number;
    physical?: number;
    psychological?: number;
    social?: number;
    positional?: number;
  };
  secondLine?: string;
  evaluated?: boolean;
  actions?: Array<{
    id: string;
    label: string;
    color?: string;
    hoverColor?: string;
    icon?: string;
    tooltip?: string;
    onClick?: (playerId: string) => void;
  }>;
  // For position management
  positionOptions?: Array<{ value: string; label: string }>;
  onPositionChange?: (playerId: string, newPosition: string) => void;
}

export type PlayerListVariant = 'guardian' | 'coach' | 'parent' | 'team';
export type BorderColor = 'green' | 'purple' | 'teal' | 'none';
export type DotSize = 'small' | 'medium' | 'large';

export interface ShadowPlayersListProps {
  players: PlayerData[];
  variant?: PlayerListVariant;
  borderColor?: BorderColor;
  dotSize?: DotSize;
  searchable?: boolean;
  selectable?: boolean;
  multiSelect?: boolean;
  useRadio?: boolean;
  selectedPlayers?: string[];
  onPlayerClick?: (playerId: string, playerData: PlayerData) => void;
  onSelectionChange?: (selectedPlayerIds: string[]) => void;
  onActionClick?: (actionId: string, playerId: string) => void;
  className?: string;
  title?: string;
  showPositionManagement?: boolean;
  updatingPosition?: boolean;
}

export const ShadowPlayersList: React.FC<ShadowPlayersListProps> = ({
  players = [],
  variant = 'guardian',
  borderColor = 'none',
  dotSize = 'medium',
  searchable = false,
  selectable = false,
  multiSelect = false,
  useRadio = false,
  selectedPlayers = [],
  onPlayerClick,
  onSelectionChange,
  onActionClick,
  className = '',
  title,
  showPositionManagement = false,
  updatingPosition = false
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [internalSelected, setInternalSelected] = useState<string[]>(selectedPlayers);
  const [isEditMode, setIsEditMode] = useState(false);

  // Filter players based on search query
  const filteredPlayers = searchQuery
    ? players.filter(player =>
        player.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (player.position && player.position.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (player.secondLine && player.secondLine.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    : players;

  // Handle player selection - memoized to prevent recreating on every render
  const handlePlayerSelection = React.useCallback((playerId: string) => {
    setInternalSelected(prev => {
      let newSelection: string[];
      
      if (multiSelect) {
        if (prev.includes(playerId)) {
          newSelection = prev.filter(id => id !== playerId);
        } else {
          newSelection = [...prev, playerId];
        }
      } else {
        newSelection = prev.includes(playerId) ? [] : [playerId];
      }
      
      onSelectionChange?.(newSelection);
      return newSelection;
    });
  }, [multiSelect, onSelectionChange]);

  // Get rating color - memoized to prevent recreating on every render
  const getRatingColor = React.useCallback((rating?: number): string => {
    if (!rating) return '#6B7280'; // grey
    if (rating >= 4) return '#10B981'; // green
    if (rating >= 3) return '#F59E0B'; // orange
    return '#EF4444'; // red
  }, []);

  // Get status color - memoized to prevent recreating on every render
  const getStatusColor = React.useCallback((status?: string): string => {
    switch (status?.toLowerCase()) {
      case 'evaluated': return '#10B981';
      case 'pending': return '#F59E0B';
      case 'not-started': return '#6B7280';
      case 'eval_ready': return '#FFA500'; // Orange for pre-eval
      default: return '#6B7280';
    }
  }, []);

  // Update internal selected state when prop changes
  useEffect(() => {
    if (JSON.stringify(internalSelected) !== JSON.stringify(selectedPlayers)) {
      setInternalSelected(selectedPlayers);
    }
  }, [selectedPlayers]); // eslint-disable-line react-hooks/exhaustive-deps

  // Create shadow root only once
  useEffect(() => {
    const container = containerRef.current;
    if (!container || shadowRootRef.current) return;

    try {
      // Check if shadow root already exists
      if (container.shadowRoot) {
        shadowRootRef.current = container.shadowRoot;
        return;
      }

      const shadowRoot = container.attachShadow({ mode: 'open' });
      shadowRootRef.current = shadowRoot;
    } catch (error) {
      console.error('Failed to create shadow DOM for ShadowPlayersList:', error);
    }
  }, []); // Empty dependency array - only run once

  // Icon mapping for Lucide icons
  const getIconSVG = (iconName: string) => {
    const icons: { [key: string]: string } = {
      'Archive': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="21 8 21 21 3 21 3 8"></polyline><rect x="1" y="3" width="22" height="5"></rect><line x1="10" y1="12" x2="14" y2="12"></line></svg>',
      'UserCheck': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><polyline points="17 11 19 13 23 9"></polyline></svg>',
      'UserMinus': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><line x1="23" y1="11" x2="17" y2="11"></line></svg>',
      'UserPlus': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><line x1="20" y1="8" x2="20" y2="14"></line><line x1="23" y1="11" x2="17" y2="11"></line></svg>',
      'ChevronDown': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>',
      'ChevronUp': '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="18 15 12 9 6 15"></polyline></svg>'
    };
    return icons[iconName] || '';
  };

  // Update shadow DOM content
  useEffect(() => {
    const shadowRoot = shadowRootRef.current;
    if (!shadowRoot) return;

      const borderColorMap = {
        green: '#10B981',
        purple: '#8B5CF6',
        teal: '#14B8A6',
        none: 'transparent'
      };

      const dotSizeMap = {
        small: '6px',
        medium: '8px',
        large: '10px'
      };

      const styles = `
        :host {
          display: block;
          width: 100%;
          font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .players-container {
          background: #1F2937;
          border-radius: 12px;
          overflow: hidden;
          border: 2px solid ${borderColorMap[borderColor]};
        }

        .search-container {
          padding: 16px;
          border-bottom: 1px solid #374151;
          background: #111827;
        }

        .header-section {
          padding: 16px;
          background: #111827;
          border-bottom: 1px solid #374151;
        }

        .header-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: ${searchable ? '12px' : '0'};
        }

        .title {
          color: #FFFFFF;
          font-size: 18px;
          font-weight: 600;
          margin: 0;
          font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .edit-positions-button {
          background: transparent;
          border: 1px solid #8B5CF6;
          color: #8B5CF6;
          padding: 6px 16px;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .edit-positions-button:hover {
          background: #8B5CF6;
          color: white;
        }

        .edit-positions-button.active {
          background: #8B5CF6;
          color: white;
        }

        .search-input {
          width: 100%;
          background: #374151;
          border: 1px solid #4B5563;
          border-radius: 8px;
          padding: 12px 16px;
          color: #FFFFFF;
          font-size: 14px;
          outline: none;
          transition: all 0.2s ease;
        }

        .search-input:focus {
          border-color: #14B8A6;
          box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.1);
        }

        .search-input::placeholder {
          color: #9CA3AF;
        }

        .players-list {
          max-height: ${showPositionManagement && isEditMode ? '600px' : '400px'};
          overflow-y: auto;
        }

        .player-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: ${showPositionManagement && isEditMode ? '20px 16px' : '16px'};
          border-bottom: 1px solid #374151;
          cursor: ${selectable || onPlayerClick ? 'pointer' : 'default'};
          transition: all 0.2s ease;
          position: relative;
        }

        .player-item:hover {
          background: ${selectable || onPlayerClick ? '#374151' : 'transparent'};
        }

        .player-item:last-child {
          border-bottom: none;
        }

        .player-item.selected {
          background: rgba(20, 184, 166, 0.1);
          border-left: 3px solid #14B8A6;
        }

        .selection-control {
          margin-right: 8px;
        }

        .selection-control input[type="checkbox"],
        .selection-control input[type="radio"] {
          width: 16px;
          height: 16px;
          accent-color: #14B8A6;
        }

        .player-avatar {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 600;
          font-size: 18px;
          flex-shrink: 0;
        }

        .player-avatar img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: cover;
        }

        .player-info {
          flex: 1;
          min-width: 0;
        }

        .player-name {
          color: #FFFFFF;
          font-weight: 600;
          font-size: 16px;
          margin: 0 0 4px 0;
          font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .player-details {
          color: #9CA3AF;
          font-size: 14px;
          margin: 0;
          line-height: 1.4;
        }

        .position-management {
          display: flex;
          gap: 12px;
          align-items: center;
          margin-top: 8px;
        }

        .position-select {
          flex: 1;
          background: #374151;
          border: 1px solid #4B5563;
          border-radius: 6px;
          padding: 6px 12px;
          color: #FFFFFF;
          font-size: 14px;
          outline: none;
          cursor: pointer;
        }

        .position-select:focus {
          border-color: #8B5CF6;
          box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
        }

        .position-select:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .teams-list {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-top: 4px;
        }

        .team-badge {
          background: rgba(20, 184, 166, 0.1);
          color: #14B8A6;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        }

        .ratings-container {
          display: flex;
          gap: 4px;
          align-items: center;
        }

        .rating-dot {
          width: ${dotSizeMap[dotSize]};
          height: ${dotSizeMap[dotSize]};
          border-radius: 50%;
          flex-shrink: 0;
        }

        .status-badge {
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 11px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
        
        .pre-eval-badge {
          background-color: #FFA500 !important;
          color: #000 !important;
          padding: 4px 8px !important;
          border-radius: 4px !important;
          font-size: 12px !important;
          font-weight: 600 !important;
          border: none !important;
          text-transform: none !important;
          letter-spacing: normal !important;
        }

        .actions-container {
          display: flex;
          gap: 8px;
          margin-left: 12px;
        }

        .action-button {
          padding: 5px 10px;
          border: none;
          border-radius: 4px;
          font-size: 11px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          text-transform: none;
          letter-spacing: normal;
        }

        .action-button:hover {
          opacity: 0.9;
        }

        .action-button svg {
          width: 16px;
          height: 16px;
        }

        /* Tooltip styles */
        .action-button[title]:hover::after {
          content: attr(title);
          position: absolute;
          bottom: -30px;
          left: 50%;
          transform: translateX(-50%);
          background: rgba(0, 0, 0, 0.9);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 11px;
          white-space: nowrap;
          z-index: 1000;
          pointer-events: none;
          font-weight: normal;
          text-transform: none;
          letter-spacing: normal;
        }

        .action-button[title]:hover::before {
          content: '';
          position: absolute;
          bottom: -6px;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 4px solid transparent;
          border-right: 4px solid transparent;
          border-bottom: 4px solid rgba(0, 0, 0, 0.9);
          z-index: 1000;
        }

        .no-players {
          padding: 40px 20px;
          text-align: center;
          color: #9CA3AF;
          font-style: italic;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
          .players-list {
            max-height: ${showPositionManagement && isEditMode ? 'calc(100vh - 300px)' : '400px'};
          }
          
          .player-item {
            padding: ${showPositionManagement && isEditMode ? '16px 12px' : '12px'};
          }

          .player-avatar {
            width: 40px;
            height: 40px;
            font-size: 16px;
          }

          .player-name {
            font-size: 15px;
          }

          .player-details {
            font-size: 13px;
          }
          
          .position-management {
            margin-top: 12px;
          }
          
          .position-select {
            padding: 8px 12px;
            font-size: 13px;
          }

          .rating-dot {
            width: ${dotSize === 'large' ? '8px' : dotSize === 'medium' ? '6px' : '4px'};
            height: ${dotSize === 'large' ? '8px' : dotSize === 'medium' ? '6px' : '4px'};
          }
        }
      `;

      const containerHTML = `
        <div class="players-container">
          ${(title || (showPositionManagement && variant === 'team')) ? `
            <div class="header-section">
              ${title ? `
                <div class="header-row">
                  <h2 class="title">${title}</h2>
                  ${showPositionManagement && variant === 'team' ? `
                    <button 
                      class="edit-positions-button ${isEditMode ? 'active' : ''}"
                      data-edit-positions-toggle
                    >
                      ${isEditMode ? 'Done Editing' : 'Edit Positions'}
                    </button>
                  ` : ''}
                </div>
              ` : ''}
              ${searchable ? `
                <input 
                  type="text" 
                  class="search-input" 
                  placeholder="Search players..." 
                  data-search-input
                />
              ` : ''}
            </div>
          ` : searchable ? `
            <div class="search-container">
              <input 
                type="text" 
                class="search-input" 
                placeholder="Search players..." 
                data-search-input
              />
            </div>
          ` : ''}
          
          <div class="players-list">
            ${filteredPlayers.length === 0 ? `
              <div class="no-players">
                ${searchQuery ? `No players found matching "${searchQuery}"` : 'No players available'}
              </div>
            ` : filteredPlayers.map(player => `
              <div 
                class="player-item ${internalSelected.includes(player.id) ? 'selected' : ''}" 
                data-player-id="${player.id}"
              >
                ${selectable ? `
                  <div class="selection-control">
                    <input 
                      type="${useRadio ? 'radio' : 'checkbox'}" 
                      ${useRadio ? `name="player-selection"` : ''}
                      ${internalSelected.includes(player.id) ? 'checked' : ''}
                      data-select-player="${player.id}"
                    />
                  </div>
                ` : ''}
                
                <div class="player-avatar">
                  ${player.avatar ? `<img src="${player.avatar}" alt="${player.name}">` : player.name.charAt(0).toUpperCase()}
                </div>
                
                <div class="player-info">
                  <h3 class="player-name">
                    ${variant === 'parent' ? (player.nickname || player.name.split(' ')[0]) : player.name}
                  </h3>
                  ${showPositionManagement && isEditMode && player.positionOptions ? `
                    <div class="position-management">
                      <select 
                        class="position-select" 
                        data-position-select="${player.id}"
                        ${updatingPosition ? 'disabled' : ''}
                      >
                        ${player.positionOptions.map(option => `
                          <option value="${option.value}" ${option.value === player.position ? 'selected' : ''}>
                            ${option.label}
                          </option>
                        `).join('')}
                      </select>
                    </div>
                  ` : `
                    <p class="player-details">
                      ${variant === 'parent' && player.teams ? '' : 
                        [
                          player.position,
                          player.age ? `Age ${player.age}` : '',
                          player.secondLine
                        ].filter(Boolean).join(' • ')
                      }
                    </p>
                  `}
                  
                  ${variant === 'parent' && player.teams ? `
                    <div class="teams-list">
                      ${player.teams.map(team => `
                        <span class="team-badge">
                          ${team.name}${team.age_group ? ` (${team.age_group})` : ''}
                        </span>
                      `).join('')}
                    </div>
                  ` : ''}
                </div>
                
                ${player.ratings && variant !== 'parent' ? `
                  <div class="ratings-container">
                    <div class="rating-dot" style="background-color: ${getRatingColor(player.ratings.technical)}" title="Technical"></div>
                    <div class="rating-dot" style="background-color: ${getRatingColor(player.ratings.physical)}" title="Physical"></div>
                    <div class="rating-dot" style="background-color: ${getRatingColor(player.ratings.psychological)}" title="Psychological"></div>
                    <div class="rating-dot" style="background-color: ${getRatingColor(player.ratings.social)}" title="Social"></div>
                    <div class="rating-dot" style="background-color: ${getRatingColor(player.ratings.positional)}" title="Positional"></div>
                  </div>
                ` : ''}
                
                ${player.status && variant !== 'parent' && player.status.toUpperCase() !== 'NOT_STARTED' ? `
                  <div class="status-badge ${player.status.toUpperCase() === 'EVAL_READY' ? 'pre-eval-badge' : ''}" 
                       style="${player.status.toUpperCase() !== 'EVAL_READY' ? `background-color: ${getStatusColor(player.status)}22; color: ${getStatusColor(player.status)};` : ''}">
                    ${player.status.toUpperCase() === 'EVAL_READY' ? 'Pre-eval' : player.status}
                  </div>
                ` : ''}
                
                ${!isEditMode && player.actions ? `
                  <div class="actions-container">
                    ${player.actions.map(action => `
                      <button 
                        class="action-button" 
                        style="background-color: ${action.color || '#6B7280'}; color: white;"
                        data-action-id="${action.id}"
                        data-player-id="${player.id}"
                        data-hover-color="${action.hoverColor || action.color || '#6B7280'}"
                        title="${action.tooltip || ''}"
                      >
                        ${action.icon && action.icon !== '' ? getIconSVG(action.icon) : action.label}
                      </button>
                    `).join('')}
                  </div>
                ` : ''}
              </div>
            `).join('')}
          </div>
        </div>
      `;

      // Apply styles and HTML
      shadowRoot.innerHTML = `<style>${styles}</style>${containerHTML}`;

      // Store all event handlers for cleanup
      const eventHandlers: Array<{ element: Element; event: string; handler: EventListener }> = [];

      // Edit positions toggle handler
      const editPositionsButton = shadowRoot.querySelector('[data-edit-positions-toggle]') as HTMLButtonElement;
      if (editPositionsButton) {
        const handleEditToggle = () => {
          setIsEditMode(!isEditMode);
        };
        editPositionsButton.addEventListener('click', handleEditToggle);
        eventHandlers.push({ element: editPositionsButton, event: 'click', handler: handleEditToggle });
      }

      // Search input handler
      const searchInput = shadowRoot.querySelector('[data-search-input]') as HTMLInputElement;
      if (searchInput) {
        // Set the value to match state
        searchInput.value = searchQuery;
        
        // Add event listener
        const handleSearchInput = (e: Event) => {
          const target = e.target as HTMLInputElement;
          setSearchQuery(target.value);
        };
        searchInput.addEventListener('input', handleSearchInput);
        eventHandlers.push({ element: searchInput, event: 'input', handler: handleSearchInput });
      }

      // Player selection listeners
      shadowRoot.querySelectorAll('[data-select-player]').forEach(checkbox => {
        const handleChange = (e: Event) => {
          const playerId = (e.target as HTMLInputElement).getAttribute('data-select-player');
          if (playerId) {
            handlePlayerSelection(playerId);
          }
        };
        checkbox.addEventListener('change', handleChange);
        eventHandlers.push({ element: checkbox, event: 'change', handler: handleChange });
      });

      // Player click listeners
      shadowRoot.querySelectorAll('.player-item').forEach(item => {
        const handleClick = (e: Event) => {
          const playerId = item.getAttribute('data-player-id');
          const player = players.find(p => p.id === playerId);
          
          if (playerId && player && onPlayerClick) {
            // Don't trigger if clicking on action buttons or selection controls
            const target = e.target as HTMLElement;
            if (!target.closest('.actions-container') && !target.closest('.selection-control')) {
              onPlayerClick(playerId, player);
            }
          }
        };
        item.addEventListener('click', handleClick);
        eventHandlers.push({ element: item, event: 'click', handler: handleClick });
      });

      // Action button listeners
      shadowRoot.querySelectorAll('[data-action-id]').forEach(button => {
        const buttonElement = button as HTMLButtonElement;
        const originalColor = buttonElement.style.backgroundColor;
        const hoverColor = buttonElement.getAttribute('data-hover-color') || originalColor;
        
        const handleClick = (e: Event) => {
          e.stopPropagation();
          const actionId = buttonElement.getAttribute('data-action-id');
          const playerId = buttonElement.getAttribute('data-player-id');
          
          if (actionId && playerId && onActionClick) {
            onActionClick(actionId, playerId);
          }
        };
        
        const handleMouseEnter = () => {
          buttonElement.style.backgroundColor = hoverColor;
        };
        
        const handleMouseLeave = () => {
          buttonElement.style.backgroundColor = originalColor;
        };
        
        button.addEventListener('click', handleClick);
        button.addEventListener('mouseenter', handleMouseEnter);
        button.addEventListener('mouseleave', handleMouseLeave);
        
        eventHandlers.push({ element: button, event: 'click', handler: handleClick });
        eventHandlers.push({ element: button, event: 'mouseenter', handler: handleMouseEnter });
        eventHandlers.push({ element: button, event: 'mouseleave', handler: handleMouseLeave });
      });

      // Position select listeners
      shadowRoot.querySelectorAll('[data-position-select]').forEach(select => {
        const handleChange = (e: Event) => {
          const playerId = (e.currentTarget as HTMLSelectElement).getAttribute('data-position-select');
          const newPosition = (e.currentTarget as HTMLSelectElement).value;
          const player = players.find(p => p.id === playerId);
          
          if (playerId && newPosition && player?.onPositionChange) {
            player.onPositionChange(playerId, newPosition);
          }
        };
        select.addEventListener('change', handleChange);
        eventHandlers.push({ element: select, event: 'change', handler: handleChange });
      });

      // Cleanup function
      return () => {
        // Remove all event listeners
        eventHandlers.forEach(({ element, event, handler }) => {
          element.removeEventListener(event, handler);
        });
      };
  }, [
    // Only include truly necessary dependencies
    filteredPlayers, 
    searchQuery, 
    internalSelected, 
    variant, 
    borderColor, 
    dotSize, 
    searchable, 
    selectable, 
    multiSelect, 
    useRadio,
    handlePlayerSelection,
    getRatingColor,
    getStatusColor,
    onPlayerClick,
    onActionClick, // This is already here, good
    players, // Add players to access in event handlers
    title,
    showPositionManagement,
    updatingPosition,
    isEditMode
  ]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{ display: 'block', width: '100%' }}
    />
  );
};

export default ShadowPlayersList;