// ABOUTME: Horizontal scrolling container with Shadow DOM encapsulation
// Supports configurable gaps, scroll snapping, and custom scrollbar styling

import React, { useRef, useEffect } from 'react';

export interface ShadowHorizontalScrollerProps {
  children: React.ReactNode;
  gap?: 'small' | 'medium' | 'large';
  snap?: boolean;
  showScrollbar?: boolean;
  className?: string;
}

export const ShadowHorizontalScroller: React.FC<ShadowHorizontalScrollerProps> = ({
  children,
  gap = 'medium',
  snap = false,
  showScrollbar = true,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Create shadow DOM


    

    const shadowRoot = container.attachShadow({ mode: 'open' });
    shadowRootRef.current = shadowRoot;

    // Gap mappings
    const gapMap = {
      small: '8px',
      medium: '16px',
      large: '24px'
    };

    // Create styles
    const style = document.createElement('style');
    style.textContent = `
      :host {
        display: block;
        width: 100%;
      }

      .scroller {
        display: flex;
        gap: ${gapMap[gap]};
        overflow-x: auto;
        overflow-y: hidden;
        padding: 0;
        margin: 0;
        width: 100%;
        ${snap ? 'scroll-snap-type: x mandatory;' : ''}
        -webkit-overflow-scrolling: touch;
        scrollbar-width: ${showScrollbar ? 'thin' : 'none'};
        -ms-overflow-style: ${showScrollbar ? 'auto' : 'none'};
      }

      .scroller::-webkit-scrollbar {
        height: ${showScrollbar ? '6px' : '0'};
        width: ${showScrollbar ? '6px' : '0'};
      }

      .scroller::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
      }

      .scroller::-webkit-scrollbar-thumb {
        background: #6B00DB;
        border-radius: 3px;
      }

      .scroller::-webkit-scrollbar-thumb:hover {
        background: #8B5CF6;
      }

      .content {
        display: contents;
      }

      ::slotted(*) {
        flex-shrink: 0;
        ${snap ? 'scroll-snap-align: center;' : ''}
      }

      /* Mobile optimizations */
      @media (max-width: 768px) {
        .scroller {
          scroll-behavior: smooth;
        }
      }
    `;

    // Create container
    const scrollContainer = document.createElement('div');
    scrollContainer.className = 'scroller';

    // Create slot for content
    const slot = document.createElement('slot');
    scrollContainer.appendChild(slot);

    // Append to shadow DOM
    shadowRoot.appendChild(style);
    shadowRoot.appendChild(scrollContainer);

    // Cleanup function
    return () => {
      if (shadowRootRef.current) {
        shadowRootRef.current.innerHTML = '';
      }
    };
  }, [gap, snap, showScrollbar]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{ display: 'block', width: '100%' }}
    >
      {children}
    </div>
  );
};

export default ShadowHorizontalScroller;