// ABOUTME: Coaches list component with add/remove functionality for team management
// Displays coaches in two modes: assigned coaches with remove buttons, available coaches with add buttons

import React, { useRef, useEffect, useState } from 'react';

export interface CoachData {
  id: string;
  name: string;
  nickname?: string;
  avatar?: string;
  role?: string;
  isPrimary?: boolean;
  secondLine?: string;
  joinedDate?: string;
  actions?: Array<{
    id: string;
    label: string;
    color?: string;
    onClick?: (coachId: string) => void;
  }>;
  // For role management
  roleOptions?: Array<{ value: string; label: string }>;
  onRoleChange?: (coachId: string, newRole: string) => void;
  onPrimaryChange?: (coachId: string) => void;
}

export type CoachListMode = 'assigned' | 'available';

export interface ShadowCoachesListProps {
  coaches: CoachData[];
  mode: CoachListMode;
  searchable?: boolean;
  onCoachAction?: (coachId: string, action: 'add' | 'remove') => void;
  onCoachClick?: (coachId: string, coachData: CoachData) => void;
  className?: string;
  title?: string;
  borderColor?: 'purple' | 'green' | 'teal' | 'none';
  showRoleManagement?: boolean;
  updatingRole?: boolean;
}

export const ShadowCoachesList: React.FC<ShadowCoachesListProps> = ({
  coaches = [],
  mode = 'assigned',
  searchable = true,
  onCoachAction,
  onCoachClick,
  className = '',
  title,
  borderColor = 'purple',
  showRoleManagement = false,
  updatingRole = false
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isEditMode, setIsEditMode] = useState(false);

  // Filter coaches based on search query
  const filteredCoaches = searchQuery
    ? coaches.filter(coach =>
        coach.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (coach.role && coach.role.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (coach.secondLine && coach.secondLine.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    : coaches;

  // Create shadow root only once
  useEffect(() => {
    const container = containerRef.current;
    if (!container || shadowRootRef.current) return;

    try {
      // Check if shadow root already exists
      if (container.shadowRoot) {
        shadowRootRef.current = container.shadowRoot;
        return;
      }

      const shadowRoot = container.attachShadow({ mode: 'open' });
      shadowRootRef.current = shadowRoot;
    } catch (error) {
      console.error('Failed to create shadow DOM for ShadowCoachesList:', error);
    }
  }, []); // Empty dependency array - only run once

  // Update shadow DOM content
  useEffect(() => {
    const shadowRoot = shadowRootRef.current;
    if (!shadowRoot) return;

    const borderColorMap = {
      green: '#10B981',
      purple: '#8B5CF6',
      teal: '#14B8A6',
      none: 'transparent'
    };

    const styles = `
      :host {
        display: block;
        width: 100%;
        font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .coaches-container {
        background: #1F2937;
        border-radius: 12px;
        overflow: hidden;
        border: 2px solid ${borderColorMap[borderColor]};
      }

      .header-section {
        padding: 16px;
        background: #111827;
        border-bottom: 1px solid #374151;
      }

      .header-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
      }

      .title {
        color: #FFFFFF;
        font-size: 18px;
        font-weight: 600;
        margin: 0;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .edit-roles-button {
        background: transparent;
        border: 1px solid #8B5CF6;
        color: #8B5CF6;
        padding: 6px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .edit-roles-button:hover {
        background: #8B5CF6;
        color: white;
      }

      .edit-roles-button.active {
        background: #8B5CF6;
        color: white;
      }

      .search-input {
        width: 100%;
        background: #374151;
        border: 1px solid #4B5563;
        border-radius: 8px;
        padding: 12px 16px;
        color: #FFFFFF;
        font-size: 14px;
        outline: none;
        transition: all 0.2s ease;
      }

      .search-input:focus {
        border-color: ${borderColorMap[borderColor]};
        box-shadow: 0 0 0 2px ${borderColorMap[borderColor]}33;
      }

      .search-input::placeholder {
        color: #9CA3AF;
      }

      .coaches-list {
        max-height: 400px;
        overflow-y: auto;
      }

      .coach-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        border-bottom: 1px solid #374151;
        cursor: ${onCoachClick ? 'pointer' : 'default'};
        transition: all 0.2s ease;
        position: relative;
      }

      .coach-item:hover {
        background: ${onCoachClick ? '#374151' : 'transparent'};
      }

      .coach-item:last-child {
        border-bottom: none;
      }

      .coach-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: linear-gradient(135deg, ${mode === 'assigned' ? '#8B5CF6' : '#10B981'} 0%, ${mode === 'assigned' ? '#764ba2' : '#059669'} 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 18px;
        flex-shrink: 0;
      }

      .coach-avatar img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }

      .coach-info {
        flex: 1;
        min-width: 0;
      }

      .coach-name {
        color: #FFFFFF;
        font-weight: 600;
        font-size: 16px;
        margin: 0 0 4px 0;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .role-management {
        display: flex;
        gap: 12px;
        align-items: center;
        margin-top: 8px;
      }

      .role-select {
        flex: 1;
        background: #374151;
        border: 1px solid #4B5563;
        border-radius: 6px;
        padding: 6px 12px;
        color: #FFFFFF;
        font-size: 14px;
        outline: none;
        cursor: pointer;
      }

      .role-select:focus {
        border-color: #8B5CF6;
        box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
      }

      .role-select:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .primary-toggle {
        display: flex;
        align-items: center;
        gap: 6px;
        cursor: pointer;
      }

      .primary-toggle input[type="radio"] {
        width: 16px;
        height: 16px;
        accent-color: #8B5CF6;
      }

      .primary-toggle input[type="radio"]:disabled {
        cursor: not-allowed;
      }

      .primary-toggle span {
        color: #9CA3AF;
        font-size: 14px;
      }

      .coach-details {
        color: #9CA3AF;
        font-size: 14px;
        margin: 0;
        line-height: 1.4;
      }

      .primary-badge {
        display: inline-block;
        background: rgba(139, 92, 246, 0.2);
        color: #A78BFA;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-left: 8px;
      }

      .action-button {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: white;
        display: flex;
        align-items: center;
        gap: 6px;
      }

      .action-button.remove {
        background-color: #DC2626;
      }

      .action-button.remove:hover {
        background-color: #B91C1C;
        transform: scale(1.05);
      }

      .action-button.add {
        background-color: #10B981;
      }

      .action-button.add:hover {
        background-color: #059669;
        transform: scale(1.05);
      }

      .action-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .action-button:disabled:hover {
        transform: scale(1);
      }

      .no-coaches {
        padding: 40px 20px;
        text-align: center;
        color: #9CA3AF;
        font-style: italic;
      }

      /* Mobile responsive */
      @media (max-width: 768px) {
        .coach-item {
          padding: 12px;
        }

        .coach-avatar {
          width: 40px;
          height: 40px;
          font-size: 16px;
        }

        .coach-name {
          font-size: 15px;
        }

        .coach-details {
          font-size: 13px;
        }

        .action-button {
          padding: 6px 12px;
          font-size: 12px;
        }
      }
    `;

    const containerHTML = `
      <div class="coaches-container">
        ${(title || searchable || (showRoleManagement && mode === 'assigned')) ? `
          <div class="header-section">
            ${title ? `
              <div class="header-row">
                <h2 class="title">${title}</h2>
                ${showRoleManagement && mode === 'assigned' ? `
                  <button 
                    class="edit-roles-button ${isEditMode ? 'active' : ''}"
                    data-edit-roles-toggle
                  >
                    ${isEditMode ? 'Done Editing' : 'Edit Roles'}
                  </button>
                ` : ''}
              </div>
            ` : ''}
            ${searchable ? `
              <input 
                type="text" 
                class="search-input" 
                placeholder="Search coaches..." 
                data-search-input
              />
            ` : ''}
          </div>
        ` : ''}
        
        <div class="coaches-list">
          ${filteredCoaches.length === 0 ? `
            <div class="no-coaches">
              ${searchQuery ? `No coaches found matching "${searchQuery}"` : 'No coaches available'}
            </div>
          ` : filteredCoaches.map(coach => `
            <div 
              class="coach-item" 
              data-coach-id="${coach.id}"
            >
              <div class="coach-avatar">
                ${coach.avatar ? `<img src="${coach.avatar}" alt="${coach.name}">` : coach.name.charAt(0).toUpperCase()}
              </div>
              
              <div class="coach-info">
                <h3 class="coach-name">
                  ${coach.name}
                  ${coach.isPrimary ? '<span class="primary-badge">Primary</span>' : ''}
                </h3>
                ${showRoleManagement && isEditMode && coach.roleOptions ? `
                  <div class="role-management">
                    <select 
                      class="role-select" 
                      data-role-select="${coach.id}"
                      ${updatingRole ? 'disabled' : ''}
                    >
                      ${coach.roleOptions.map(option => `
                        <option value="${option.value}" ${option.value === coach.role ? 'selected' : ''}>
                          ${option.label}
                        </option>
                      `).join('')}
                    </select>
                    <label class="primary-toggle">
                      <input 
                        type="radio" 
                        name="primary-coach" 
                        data-primary-toggle="${coach.id}"
                        ${coach.isPrimary ? 'checked' : ''}
                        ${updatingRole ? 'disabled' : ''}
                      />
                      <span>Primary</span>
                    </label>
                  </div>
                ` : `
                  <p class="coach-details">
                    ${coach.secondLine || (coach.roleOptions ? 
                      coach.roleOptions.find(opt => opt.value === coach.role)?.label || coach.role : 
                      coach.role
                    )}
                    ${coach.isPrimary ? ' • Primary' : ''}
                  </p>
                `}
              </div>
              
              ${!isEditMode && coach.actions && coach.actions.length > 0 ? `
                <div class="actions-container">
                  ${coach.actions.map(action => `
                    <button 
                      class="action-button ${action.color === 'red' ? 'remove' : 'add'}"
                      data-action-id="${action.id}"
                      data-coach-id="${coach.id}"
                    >
                      ${action.color === 'red' ? `
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <polyline points="3 6 5 6 21 6"></polyline>
                          <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                        </svg>
                      ` : `
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                          <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                          <circle cx="8.5" cy="7" r="4"></circle>
                          <line x1="20" y1="8" x2="20" y2="14"></line>
                          <line x1="23" y1="11" x2="17" y2="11"></line>
                        </svg>
                      `}
                      ${action.label}
                    </button>
                  `).join('')}
                </div>
              ` : !isEditMode && onCoachAction && (!coach.isPrimary || mode === 'available') ? `
                <button 
                  class="action-button ${mode === 'assigned' ? 'remove' : 'add'}"
                  data-action-coach="${coach.id}"
                  ${coach.isPrimary && mode === 'assigned' ? 'disabled' : ''}
                >
                  ${mode === 'assigned' ? `
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polyline points="3 6 5 6 21 6"></polyline>
                      <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                    </svg>
                    Remove
                  ` : `
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                      <circle cx="8.5" cy="7" r="4"></circle>
                      <line x1="20" y1="8" x2="20" y2="14"></line>
                      <line x1="23" y1="11" x2="17" y2="11"></line>
                    </svg>
                    Add
                  `}
                </button>
              ` : ''}
            </div>
          `).join('')}
        </div>
      </div>
    `;

    // Apply styles and HTML
    shadowRoot.innerHTML = `<style>${styles}</style>${containerHTML}`;

    // Store all event handlers for cleanup
    const eventHandlers: Array<{ element: Element; event: string; handler: EventListener }> = [];

    // Edit roles toggle handler
    const editRolesButton = shadowRoot.querySelector('[data-edit-roles-toggle]') as HTMLButtonElement;
    if (editRolesButton) {
      const handleEditToggle = () => {
        setIsEditMode(!isEditMode);
      };
      editRolesButton.addEventListener('click', handleEditToggle);
      eventHandlers.push({ element: editRolesButton, event: 'click', handler: handleEditToggle });
    }

    // Search input handler
    const searchInput = shadowRoot.querySelector('[data-search-input]') as HTMLInputElement;
    if (searchInput) {
      // Set the value to match state
      searchInput.value = searchQuery;
      
      // Add event listener
      const handleSearchInput = (e: Event) => {
        const target = e.target as HTMLInputElement;
        setSearchQuery(target.value);
      };
      searchInput.addEventListener('input', handleSearchInput);
      eventHandlers.push({ element: searchInput, event: 'input', handler: handleSearchInput });
    }

    // Coach click listeners
    if (onCoachClick) {
      shadowRoot.querySelectorAll('.coach-item').forEach(item => {
        const handleClick = (e: Event) => {
          const coachId = item.getAttribute('data-coach-id');
          const coach = coaches.find(c => c.id === coachId);
          
          if (coachId && coach) {
            // Don't trigger if clicking on action button
            const target = e.target as HTMLElement;
            if (!target.closest('.action-button')) {
              onCoachClick(coachId, coach);
            }
          }
        };
        item.addEventListener('click', handleClick);
        eventHandlers.push({ element: item, event: 'click', handler: handleClick });
      });
    }

    // Action button listeners for custom actions
    shadowRoot.querySelectorAll('[data-action-id]').forEach(button => {
      const handleClick = (e: Event) => {
        e.stopPropagation();
        const actionId = (e.currentTarget as HTMLButtonElement).getAttribute('data-action-id');
        const coachId = (e.currentTarget as HTMLButtonElement).getAttribute('data-coach-id');
        const coach = coaches.find(c => c.id === coachId);
        const action = coach?.actions?.find(a => a.id === actionId);
        
        if (action && coachId && action.onClick) {
          action.onClick(coachId);
        }
      };
      button.addEventListener('click', handleClick);
      eventHandlers.push({ element: button, event: 'click', handler: handleClick });
    });

    // Action button listeners for default add/remove
    shadowRoot.querySelectorAll('[data-action-coach]').forEach(button => {
      const handleClick = (e: Event) => {
        e.stopPropagation();
        const coachId = (e.currentTarget as HTMLButtonElement).getAttribute('data-action-coach');
        
        if (coachId && onCoachAction) {
          onCoachAction(coachId, mode === 'assigned' ? 'remove' : 'add');
        }
      };
      button.addEventListener('click', handleClick);
      eventHandlers.push({ element: button, event: 'click', handler: handleClick });
    });

    // Role select listeners
    shadowRoot.querySelectorAll('[data-role-select]').forEach(select => {
      const handleChange = (e: Event) => {
        const coachId = (e.currentTarget as HTMLSelectElement).getAttribute('data-role-select');
        const newRole = (e.currentTarget as HTMLSelectElement).value;
        const coach = coaches.find(c => c.id === coachId);
        
        if (coachId && newRole && coach?.onRoleChange) {
          coach.onRoleChange(coachId, newRole);
        }
      };
      select.addEventListener('change', handleChange);
      eventHandlers.push({ element: select, event: 'change', handler: handleChange });
    });

    // Primary toggle listeners
    shadowRoot.querySelectorAll('[data-primary-toggle]').forEach(radio => {
      const handleChange = (e: Event) => {
        const coachId = (e.currentTarget as HTMLInputElement).getAttribute('data-primary-toggle');
        const coach = coaches.find(c => c.id === coachId);
        
        if (coachId && coach?.onPrimaryChange) {
          coach.onPrimaryChange(coachId);
        }
      };
      radio.addEventListener('change', handleChange);
      eventHandlers.push({ element: radio, event: 'change', handler: handleChange });
    });

    // Cleanup function
    return () => {
      // Remove all event listeners
      eventHandlers.forEach(({ element, event, handler }) => {
        element.removeEventListener(event, handler);
      });
    };
  }, [
    filteredCoaches,
    searchQuery,
    mode,
    searchable,
    title,
    borderColor,
    onCoachClick,
    onCoachAction,
    coaches,
    showRoleManagement,
    updatingRole,
    isEditMode
  ]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{ display: 'block', width: '100%' }}
    />
  );
};

export default ShadowCoachesList;