// ABOUTME: Extended version of ShadowPlayersList that allows disabling the scroll limitation
// Displays all players inline without max-height restriction

import React, { useRef, useEffect, useState, useMemo, useCallback } from 'react';
import { 
  PlayerData, 
  PlayerListVariant, 
  BorderColor, 
  DotSize,
  ShadowPlayersListProps 
} from './ShadowPlayersList';

export interface ShadowPlayersListNoScrollProps extends ShadowPlayersListProps {
  noScroll?: boolean;
}

export const ShadowPlayersListNoScroll: React.FC<ShadowPlayersListNoScrollProps> = ({
  players = [],
  variant = 'guardian',
  borderColor = 'none',
  dotSize = 'medium',
  searchable = false,
  selectable = false,
  multiSelect = false,
  useRadio = false,
  selectedPlayers = [],
  onPlayerClick,
  onSelectionChange,
  onActionClick,
  className = '',
  noScroll = false
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [internalSelected, setInternalSelected] = useState<string[]>(selectedPlayers);

  // Filter players based on search query - memoized to prevent re-renders
  const filteredPlayers = useMemo(() => {
    if (!searchQuery) return players;
    
    const query = searchQuery.toLowerCase();
    return players.filter(player =>
      player.name.toLowerCase().includes(query) ||
      (player.position && player.position.toLowerCase().includes(query)) ||
      (player.secondLine && player.secondLine.toLowerCase().includes(query))
    );
  }, [players, searchQuery]);

  // Handle player selection - memoized to prevent re-renders
  const handlePlayerSelection = useCallback((playerId: string) => {
    let newSelection: string[];
    
    if (multiSelect) {
      if (internalSelected.includes(playerId)) {
        newSelection = internalSelected.filter(id => id !== playerId);
      } else {
        newSelection = [...internalSelected, playerId];
      }
    } else {
      newSelection = internalSelected.includes(playerId) ? [] : [playerId];
    }
    
    setInternalSelected(newSelection);
    onSelectionChange?.(newSelection);
  }, [multiSelect, internalSelected, onSelectionChange]);

  // Get rating color
  const getRatingColor = (rating?: number): string => {
    if (!rating) return '#6B7280'; // grey
    if (rating >= 4) return '#10B981'; // green
    if (rating >= 3) return '#F59E0B'; // orange
    return '#EF4444'; // red
  };

  // Get status color
  const getStatusColor = (status?: string): string => {
    switch (status?.toLowerCase()) {
      case 'evaluated': return '#10B981';
      case 'pending': return '#F59E0B';
      case 'not-started': return '#6B7280';
      default: return '#6B7280';
    }
  };

  useEffect(() => {
    setInternalSelected(selectedPlayers);
  }, [selectedPlayers]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    try {
      // Check if shadow root already exists
      let shadowRoot = container.shadowRoot;
      if (!shadowRoot) {
        // Create shadow DOM only if it doesn't exist
        shadowRoot = container.attachShadow({ mode: 'open' });
      }
      shadowRootRef.current = shadowRoot;
      
      // Clear previous content to prevent memory leaks
      shadowRoot.innerHTML = '';

      const borderColorMap = {
        green: '#10B981',
        purple: '#8B5CF6',
        teal: '#14B8A6',
        none: 'transparent'
      };

      const dotSizeMap = {
        small: '6px',
        medium: '8px',
        large: '10px'
      };

      const styles = `
        :host {
          display: block;
          width: 100%;
          font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .players-container {
          background: #1F2937;
          border-radius: 12px;
          overflow: hidden;
          border: 2px solid ${borderColorMap[borderColor]};
        }

        .search-container {
          padding: 16px;
          border-bottom: 1px solid #374151;
          background: #111827;
        }

        .search-input {
          width: 100%;
          background: #374151;
          border: 1px solid #4B5563;
          border-radius: 8px;
          padding: 12px 16px;
          color: #FFFFFF;
          font-size: 14px;
          outline: none;
          transition: all 0.2s ease;
        }

        .search-input:focus {
          border-color: #14B8A6;
          box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.1);
        }

        .search-input::placeholder {
          color: #9CA3AF;
        }

        .players-list {
          ${noScroll ? '' : 'max-height: 400px;'}
          ${noScroll ? '' : 'overflow-y: auto;'}
        }

        .player-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 16px;
          border-bottom: 1px solid #374151;
          cursor: ${selectable || onPlayerClick ? 'pointer' : 'default'};
          transition: all 0.2s ease;
          position: relative;
        }

        .player-item:hover {
          background: ${selectable || onPlayerClick ? '#374151' : 'transparent'};
        }

        .player-item:last-child {
          border-bottom: none;
        }

        .player-item.selected {
          background: #1F2937;
          border-left: 3px solid #14B8A6;
        }

        .selection-control {
          margin-right: 8px;
        }

        .selection-control input[type="checkbox"],
        .selection-control input[type="radio"] {
          width: 16px;
          height: 16px;
          accent-color: #14B8A6;
        }

        .player-avatar {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 600;
          font-size: 18px;
          flex-shrink: 0;
        }

        .player-avatar img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          object-fit: cover;
        }

        .player-info {
          flex: 1;
          min-width: 0;
        }

        .player-name {
          color: #FFFFFF;
          font-weight: 600;
          font-size: 16px;
          margin: 0 0 4px 0;
          font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .player-details {
          color: #9CA3AF;
          font-size: 14px;
          margin: 0;
          line-height: 1.4;
        }

        .teams-list {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-top: 4px;
        }

        .team-badge {
          background: #1F2937;
          color: #14B8A6;
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        }

        .ratings-container {
          display: flex;
          gap: 4px;
          align-items: center;
        }

        .rating-dot {
          width: ${dotSizeMap[dotSize]};
          height: ${dotSizeMap[dotSize]};
          border-radius: 50%;
          flex-shrink: 0;
        }

        .status-badge {
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 11px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .actions-container {
          display: flex;
          gap: 8px;
          margin-left: 12px;
        }

        .action-button {
          padding: 6px 12px;
          border: none;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .action-button:hover {
          opacity: 0.8;
          transform: scale(1.05);
        }

        .no-players {
          padding: 40px 20px;
          text-align: center;
          color: #9CA3AF;
          font-style: italic;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
          .player-item {
            padding: 12px;
          }

          .player-avatar {
            width: 40px;
            height: 40px;
            font-size: 16px;
          }

          .player-name {
            font-size: 15px;
          }

          .player-details {
            font-size: 13px;
          }

          .rating-dot {
            width: ${dotSize === 'large' ? '8px' : dotSize === 'medium' ? '6px' : '4px'};
            height: ${dotSize === 'large' ? '8px' : dotSize === 'medium' ? '6px' : '4px'};
          }
        }
      `;

      const containerHTML = `
        <div class="players-container">
          ${searchable ? `
            <div class="search-container">
              <input 
                type="text" 
                class="search-input" 
                placeholder="Search players..." 
                value="${searchQuery}"
                data-search-input
              />
            </div>
          ` : ''}
          
          <div class="players-list">
            ${filteredPlayers.length === 0 ? `
              <div class="no-players">
                ${searchQuery ? `No players found matching "${searchQuery}"` : 'No players available'}
              </div>
            ` : filteredPlayers.map(player => `
              <div 
                class="player-item ${internalSelected.includes(player.id) ? 'selected' : ''}" 
                data-player-id="${player.id}"
              >
                ${selectable ? `
                  <div class="selection-control">
                    <input 
                      type="${useRadio ? 'radio' : 'checkbox'}" 
                      ${useRadio ? `name="player-selection"` : ''}
                      ${internalSelected.includes(player.id) ? 'checked' : ''}
                      data-select-player="${player.id}"
                    />
                  </div>
                ` : ''}
                
                <div class="player-avatar">
                  ${player.avatar ? `<img src="${player.avatar}" alt="${player.name}">` : player.name.charAt(0).toUpperCase()}
                </div>
                
                <div class="player-info">
                  <h3 class="player-name">
                    ${variant === 'parent' ? (player.nickname || player.name.split(' ')[0]) : player.name}
                  </h3>
                  <p class="player-details">
                    ${variant === 'parent' && player.teams ? '' : 
                      [
                        player.position,
                        player.age ? `Age ${player.age}` : '',
                        player.secondLine
                      ].filter(Boolean).join(' • ')
                    }
                  </p>
                  
                  ${variant === 'parent' && player.teams ? `
                    <div class="teams-list">
                      ${player.teams.map(team => `
                        <span class="team-badge">
                          ${team.name}${team.age_group ? ` (${team.age_group})` : ''}
                        </span>
                      `).join('')}
                    </div>
                  ` : ''}
                </div>
                
                ${player.ratings && variant !== 'parent' ? `
                  <div class="ratings-container">
                    <div class="rating-dot" style="background-color: ${getRatingColor(player.ratings.technical)}" title="Technical"></div>
                    <div class="rating-dot" style="background-color: ${getRatingColor(player.ratings.physical)}" title="Physical"></div>
                    <div class="rating-dot" style="background-color: ${getRatingColor(player.ratings.psychological)}" title="Psychological"></div>
                    <div class="rating-dot" style="background-color: ${getRatingColor(player.ratings.social)}" title="Social"></div>
                    <div class="rating-dot" style="background-color: ${getRatingColor(player.ratings.positional)}" title="Positional"></div>
                  </div>
                ` : ''}
                
                ${player.status && variant !== 'parent' ? `
                  <div class="status-badge" style="background-color: ${getStatusColor(player.status)}22; color: ${getStatusColor(player.status)};">
                    ${player.status}
                  </div>
                ` : ''}
                
                ${player.actions ? `
                  <div class="actions-container">
                    ${player.actions.map(action => `
                      <button 
                        class="action-button" 
                        style="background-color: ${action.color || '#6B7280'}; color: white;"
                        data-action-id="${action.id}"
                        data-player-id="${player.id}"
                      >
                        ${action.label}
                      </button>
                    `).join('')}
                  </div>
                ` : ''}
              </div>
            `).join('')}
          </div>
        </div>
      `;

      // Apply styles and HTML
      shadowRoot.innerHTML = `<style>${styles}</style>${containerHTML}`;

      // Event listeners
      const searchInput = shadowRoot.querySelector('[data-search-input]');
      if (searchInput) {
        searchInput.addEventListener('input', (e) => {
          const target = e.target as HTMLInputElement;
          setSearchQuery(target.value);
        });
      }

      // Player selection listeners
      shadowRoot.querySelectorAll('[data-select-player]').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
          const playerId = (e.target as HTMLInputElement).getAttribute('data-select-player');
          if (playerId) {
            handlePlayerSelection(playerId);
          }
        });
      });

      // Player click listeners
      shadowRoot.querySelectorAll('.player-item').forEach(item => {
        item.addEventListener('click', (e) => {
          const playerId = item.getAttribute('data-player-id');
          const player = players.find(p => p.id === playerId);
          
          if (playerId && player && onPlayerClick) {
            // Don't trigger if clicking on action buttons or selection controls
            const target = e.target as HTMLElement;
            if (!target.closest('.actions-container') && !target.closest('.selection-control')) {
              onPlayerClick(playerId, player);
            }
          }
        });
      });

      // Action button listeners
      shadowRoot.querySelectorAll('[data-action-id]').forEach(button => {
        button.addEventListener('click', (e) => {
          e.stopPropagation();
          const actionId = (e.target as HTMLButtonElement).getAttribute('data-action-id');
          const playerId = (e.target as HTMLButtonElement).getAttribute('data-player-id');
          
          if (actionId && playerId && onActionClick) {
            onActionClick(actionId, playerId);
          }
        });
      });

      // Cleanup function
      return () => {
        // Note: We don't clear innerHTML here as it causes issues with React's reconciliation
        // The shadow root will be cleared on the next update anyway
      };
    } catch (error) {
      console.error('Failed to create shadow DOM for ShadowPlayersListNoScroll:', error);
    }
  }, [
    // Only include truly reactive dependencies
    players, // Source data
    searchQuery, // Search state
    internalSelected, // Selection state
    variant, // Display variant
    borderColor, // Style prop
    dotSize, // Style prop
    searchable, // Feature flag
    selectable, // Feature flag
    multiSelect, // Feature flag
    useRadio, // Feature flag
    noScroll // New feature flag
    // Note: callback functions are excluded as they should be stable
  ]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{ display: 'block', width: '100%' }}
    />
  );
};

export default ShadowPlayersListNoScroll;