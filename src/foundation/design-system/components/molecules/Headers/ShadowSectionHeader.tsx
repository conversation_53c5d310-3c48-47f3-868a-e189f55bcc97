// ABOUTME: Shadow DOM component for section headers with consistent typography
// Provides styled headers for sections with responsive sizing

import React, { useEffect, useRef } from 'react';
export interface ShadowSectionHeaderProps {
  title: string;
  size?: 'sm' | 'md' | 'lg' | 'small' | 'medium' | 'large';
  className?: string;
}

const sizeConfig = {
  sm: {
    desktop: '1.25rem',
    mobile: '1.125rem'
  },
  md: {
    desktop: '1.5rem',
    mobile: '1.25rem'
  },
  lg: {
    desktop: '1.875rem',
    mobile: '1.5rem'
  }
};

export const ShadowSectionHeader: React.FC<ShadowSectionHeaderProps> = ({
  title,
  size = 'md',
  className = ''
}) => {
  const hostRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);

  useEffect(() => {
    if (!hostRef.current || shadowRef.current) return;

    // Create shadow root


    

    shadowRef.current = hostRef.current.attachShadow({ mode: 'open' });

    // Create styles
    // Handle both naming conventions for size
    const normalizedSize = size === 'small' ? 'sm' : size === 'medium' ? 'md' : size === 'large' ? 'lg' : size;
    const config = sizeConfig[normalizedSize] || sizeConfig.md;
    
    const styles = `
      <style>
      :host {
        display: block;
        width: 100%;
      }
      
      .section-header {
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
        font-size: ${config.desktop};
        color: #FFFFFF;
        margin: 0;
        padding: 0;
        line-height: ${1.25};
      }
      
      @media (max-width: 640px) {
        .section-header {
          font-size: ${config.mobile};
        }
      }
      
      ${className ? `.custom { ${className} }` : ''}
    </style>
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Cleanup
    return () => {
      if (shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, [size, className]);

  useEffect(() => {
    if (!shadowRef.current) return;

    // Clear existing content except style
    const style = shadowRef.current.querySelector('style');
    shadowRef.current.innerHTML = '';
    if (style) shadowRef.current.appendChild(style);

    // Create header element
    const headerElement = document.createElement('h2');
    headerElement.className = 'section-header';
    if (className) headerElement.classList.add('custom');
    headerElement.textContent = title;
    
    shadowRef.current.appendChild(headerElement);
  }, [title]);

  return <div ref={hostRef} />;
};