// ABOUTME: Shadow DOM header component with icon and optional view all link
// Provides headers with icon, text, and optional action link

import React, { useEffect, useRef } from 'react';

// Common icon SVG paths for SHOT app
const iconPaths: Record<string, string> = {
  Activity: 'M22 12h-4l-3 9L9 3l-3 9H2',
  Trophy: 'M6 9H4.5a2.5 2.5 0 0 1 0-5H6 M18 9h1.5a2.5 2.5 0 0 0 0-5H18 M4 22h16 M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22 M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22 M18 2H6v7a6 6 0 0 0 12 0V2Z',
  Users: 'M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2 M9 7a4 4 0 1 0 0-8 4 4 0 0 0 0 8z M23 21v-2a4 4 0 0 0-3-3.87 M16 3.13a4 4 0 0 1 0 7.75',
  Target: 'M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0-6 0 M12 12m-8 0a8 8 0 1 0 16 0a8 8 0 1 0-16 0 M12 2v2 M12 20v2 M2 12h2 M20 12h2',
  ChartBar: 'M12 20V10 M6 20V4 M18 20v-4',
  Zap: 'M13 2L3 14h9l-1 8 10-12h-9l1-8z',
  Brain: 'M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z',
  Award: 'M12 15m-7 0a7 7 0 1 0 14 0a7 7 0 1 0-14 0 M8.21 13.89L7 23l5-3 5 3-1.21-9.12',
  CheckCircle: 'M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z M9 12l2 2 4-4',
  Settings: 'M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0-6 0 M12 1v6 M12 17v6 M4.22 4.22l4.24 4.24 M15.54 15.54l4.24 4.24 M1 12h6 M17 12h6 M4.22 19.78l4.24-4.24 M15.54 8.46l4.24-4.24',
  Calendar: 'M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z',
  Clock: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z',
  Star: 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z',
  ShoppingBag: 'M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z M3 6h18 M16 10a4 4 0 0 1-8 0',
  Globe: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z M3.5 8.5h17 M3.5 15.5h17 M12 2a15.3 15.3 0 0 1 4 10a15.3 15.3 0 0 1-4 10a15.3 15.3 0 0 1-4-10a15.3 15.3 0 0 1 4-10z',
  Plus: 'M12 5v14m-7-7h14',
  HelpCircle: 'M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z M9 9a3 3 0 1 0 6 0 3 3 0 0 0-6 0 M12 17h.01'
};

export interface ShadowIconHeaderProps {
  icon?: keyof typeof iconPaths;
  text?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'white' | 'teal' | 'purple' | 'gold' | 'green';
  iconColor?: 'white' | 'teal' | 'purple' | 'gold' | 'green';
  align?: 'left' | 'center' | 'right';
  viewAllLink?: string;
  viewAllText?: string;
  viewAllSize?: 'sm' | 'md' | 'lg';
  onViewAllClick?: () => void;
  className?: string;
}

const colorMap = {
  white: '#FFFFFF',
  teal: '#1ABC9C',
  purple: '#6B00DB',
  gold: '#F7B613',
  green: '#10B981'
};

const sizeConfig = {
  sm: {
    iconSize: '18px',
    fontSize: '1rem',
    fontWeight: '600',
    letterSpacing: '0.025em'
  },
  md: {
    iconSize: '24px',
    fontSize: '1.25rem',
    fontWeight: '600',
    letterSpacing: '0.025em'
  },
  lg: {
    iconSize: '32px',
    fontSize: '1.5rem',
    fontWeight: '700',
    letterSpacing: '0.05em'
  },
  xl: {
    iconSize: '40px',
    fontSize: '1.875rem',
    fontWeight: '700',
    letterSpacing: '0.05em'
  }
};

const viewAllSizeConfig = {
  sm: {
    fontSize: '0.75rem',
    iconSize: '14px'
  },
  md: {
    fontSize: '0.875rem',
    iconSize: '16px'
  },
  lg: {
    fontSize: '1rem',
    iconSize: '18px'
  }
};

export const ShadowIconHeader: React.FC<ShadowIconHeaderProps> = ({ 
  icon = 'Trophy',
  text = 'Header Text',
  size = 'lg',
  color = 'white',
  iconColor,
  align = 'left',
  viewAllLink,
  viewAllText = 'View All',
  viewAllSize = 'md',
  onViewAllClick,
  className = ''
}) => {
  const hostRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);

  const finalIconColor = iconColor || color;

  useEffect(() => {
    if (!hostRef.current || shadowRef.current) return;

    // Create shadow root
    shadowRef.current = hostRef.current.attachShadow({ mode: 'open' });

    // Create styles
    const config = sizeConfig[size] || sizeConfig.lg; // Default to 'lg' if size is invalid
    const viewAllConfig = viewAllSizeConfig[viewAllSize] || viewAllSizeConfig.md; // Default to 'md' if size is invalid
    const textColor = colorMap[color] || colorMap.white; // Default to white if color is invalid
    const iconColorValue = colorMap[finalIconColor] || colorMap.white; // Default to white if color is invalid
    const iconPath = iconPaths[icon] || iconPaths.Trophy;
    
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        :host {
          display: block;
          width: 100%;
        }

        .icon-header {
          display: flex;
          align-items: center;
          justify-content: ${align === 'center' ? 'center' : align === 'right' ? 'flex-end' : 'space-between'};
          padding: 0.5rem 0;
          gap: 0.75rem;
        }

        .header-content {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          ${align === 'right' ? 'order: 2;' : ''}
        }

        .icon-wrapper {
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
        }

        .icon {
          width: ${config.iconSize};
          height: ${config.iconSize};
          stroke: ${iconColorValue};
          fill: none;
          stroke-width: 2;
          stroke-linecap: round;
          stroke-linejoin: round;
        }

        .header-text {
          font-family: 'Poppins', sans-serif;
          font-size: ${config.fontSize};
          font-weight: ${config.fontWeight};
          letter-spacing: ${config.letterSpacing};
          color: ${textColor};
          text-transform: uppercase;
          line-height: 1.2;
          margin: 0;
        }

        .view-all-link {
          font-family: 'Poppins', sans-serif;
          font-size: ${viewAllConfig.fontSize};
          font-weight: 500;
          color: #1ABC9C;
          text-decoration: none;
          display: inline-flex;
          align-items: center;
          gap: 0.25rem;
          transition: all 0.2s ease;
          cursor: pointer;
          text-transform: uppercase;
          letter-spacing: 0.025em;
          border: none;
          background: none;
          padding: 0.25rem 0.5rem;
          margin: -0.25rem -0.5rem;
          border-radius: 0.25rem;
          ${align === 'right' ? 'order: 1;' : ''}
        }

        .view-all-link:hover {
          color: #16A085;
          background: rgba(26, 188, 156, 0.1);
        }

        .view-all-link:active {
          transform: translateY(1px);
        }

        .chevron-icon {
          width: ${viewAllConfig.iconSize};
          height: ${viewAllConfig.iconSize};
          stroke: currentColor;
          transition: transform 0.2s ease;
        }

        .view-all-link:hover .chevron-icon {
          transform: translateX(2px);
        }

        /* Center alignment special case */
        .align-center {
          text-align: center;
        }

        .align-center .icon-header {
          display: inline-flex;
          width: auto;
        }

        /* Mobile adjustments */
        @media (max-width: 640px) {
          .icon-header {
            gap: 0.5rem;
          }

          .header-content {
            gap: 0.5rem;
          }

          ${size === 'xl' ? `
            .icon {
              width: 32px;
              height: 32px;
            }
            .header-text {
              font-size: 1.5rem;
            }
          ` : ''}

          ${size === 'lg' ? `
            .icon {
              width: 24px;
              height: 24px;
            }
            .header-text {
              font-size: 1.25rem;
            }
          ` : ''}
        }
      </style>
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Cleanup
    return () => {
      if (shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, [icon, size, color, finalIconColor, align, viewAllSize]);

  useEffect(() => {
    if (!shadowRef.current) return;

    // Clear existing content except style
    const style = shadowRef.current.querySelector('style');
    shadowRef.current.innerHTML = '';
    if (style) shadowRef.current.appendChild(style);

    // Create wrapper for center alignment
    const wrapper = align === 'center' ? document.createElement('div') : null;
    if (wrapper) wrapper.className = 'align-center';

    // Create the header container
    const container = document.createElement('div');
    container.className = 'icon-header';

    // Create header content
    const content = document.createElement('div');
    content.className = 'header-content';

    // Create icon
    const iconWrapper = document.createElement('div');
    iconWrapper.className = 'icon-wrapper';
    
    const iconPath = iconPaths[icon] || iconPaths.Trophy;
    iconWrapper.innerHTML = `
      <svg class="icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path d="${iconPath}" />
      </svg>
    `;

    // Create text
    const textElement = document.createElement('h2');
    textElement.className = 'header-text';
    textElement.textContent = text;

    // Assemble header content
    content.appendChild(iconWrapper);
    content.appendChild(textElement);
    container.appendChild(content);

    // Add view all link if provided
    if (viewAllLink || onViewAllClick) {
      const linkElement = viewAllLink ? document.createElement('a') : document.createElement('button');
      linkElement.className = 'view-all-link';
      
      if (viewAllLink && linkElement instanceof HTMLAnchorElement) {
        linkElement.href = viewAllLink;
      }

      linkElement.innerHTML = `
        ${viewAllText}
        <svg class="chevron-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      `;

      if (onViewAllClick) {
        linkElement.addEventListener('click', (e) => {
          if (!viewAllLink) {
            e.preventDefault();
          }
          onViewAllClick();
        });
      }

      container.appendChild(linkElement);
    }

    // Add to shadow DOM
    if (wrapper) {
      wrapper.appendChild(container);
      shadowRef.current.appendChild(wrapper);
    } else {
      shadowRef.current.appendChild(container);
    }
  }, [icon, text, align, viewAllLink, viewAllText, onViewAllClick]);

  return <div ref={hostRef} className={className} />;
};