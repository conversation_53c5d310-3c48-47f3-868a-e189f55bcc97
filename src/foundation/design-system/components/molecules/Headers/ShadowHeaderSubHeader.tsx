// ABOUTME: Shadow DOM component for header and subheader with optional image
// Provides a flexible header component with alignment, sizing, and decorative line options

import React, { useEffect, useRef } from 'react';

export interface ShadowHeaderSubHeaderProps {
  header?: string;
  subheader?: string;
  align?: 'left' | 'center' | 'right';
  size?: 'sm' | 'md' | 'lg' | 'small' | 'medium' | 'large';
  withLine?: boolean;
  lineColor?: 'purple' | 'teal' | 'gold' | 'green';
  imageUrl?: string;
  imageAlt?: string;
  className?: string;
}

const lineColorMap = {
  purple: '#6B00DB',
  teal: '#1ABC9C',
  gold: '#F7B613',
  green: '#10B981'
};

const sizeConfig = {
  sm: {
    headerSize: '1.5rem',
    subheaderSize: '1rem',
    imageSize: '48px',
    headerMargin: '0.25rem',
    mobileHeaderSize: '1.25rem',
    mobileSubheaderSize: '0.875rem'
  },
  md: {
    headerSize: '1.875rem',
    subheaderSize: '1.125rem',
    imageSize: '64px',
    headerMargin: '0.25rem',
    mobileHeaderSize: '1.5rem',
    mobileSubheaderSize: '1rem'
  },
  lg: {
    headerSize: '2.25rem',
    subheaderSize: '1.25rem',
    imageSize: '80px',
    headerMargin: '0.5rem',
    mobileHeaderSize: '1.875rem',
    mobileSubheaderSize: '1.125rem'
  }
};

export const ShadowHeaderSubHeader: React.FC<ShadowHeaderSubHeaderProps> = ({ 
  header = 'Header Text',
  subheader = 'Subheader text goes here',
  align = 'center',
  size = 'lg',
  withLine = true,
  lineColor = 'purple',
  imageUrl,
  imageAlt = '',
  className = ''
}) => {
  const hostRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);

  useEffect(() => {
    if (!hostRef.current || shadowRef.current) return;

    // Create shadow root
    shadowRef.current = hostRef.current.attachShadow({ mode: 'open' });

    // Handle both naming conventions for size
    const normalizedSize = size === 'small' ? 'sm' : size === 'medium' ? 'md' : size === 'large' ? 'lg' : size;
    const config = sizeConfig[normalizedSize] || sizeConfig.lg;
    const lineColorValue = lineColorMap[lineColor] || lineColorMap.purple;
    
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700&display=swap');
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        :host {
          display: block;
          width: 100%;
        }

        .header-container {
          text-align: ${align};
          margin-bottom: 2rem;
        }

        .header-content {
          display: inline-flex;
          align-items: center;
          gap: 1.5rem;
          text-align: left;
        }

        .header-image {
          flex-shrink: 0;
          border-radius: 9999px;
          overflow: hidden;
          width: ${config.imageSize};
          height: ${config.imageSize};
        }

        .header-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          display: block;
        }

        .header-text-wrapper {
          flex: 1;
        }
        
        .header {
          font-family: 'Poppins', sans-serif;
          font-size: ${config.headerSize};
          color: #FFFFFF;
          font-weight: 700;
          line-height: 1.2;
          margin: 0 0 ${config.headerMargin} 0;
          letter-spacing: 0.05em;
        }
        
        .subheader {
          font-family: 'Montserrat', sans-serif;
          font-size: ${config.subheaderSize};
          color: #9CA3AF;
          font-weight: 400;
          line-height: 1.5;
          margin: 0;
          max-width: 65ch;
        }
        
        /* Alignment */
        .align-center {
          text-align: center;
        }
        
        .align-center .header-content {
          margin-left: auto;
          margin-right: auto;
        }
        
        .align-center .subheader {
          margin-left: auto;
          margin-right: auto;
        }
        
        .align-right {
          text-align: right;
        }

        .align-right .header-content {
          margin-left: auto;
        }
        
        .align-right .subheader {
          margin-left: auto;
        }
        
        /* Optional decorative line */
        .line-wrapper {
          margin-top: 1.5rem;
          overflow: hidden;
        }

        .decorative-line {
          width: 60px;
          height: 4px;
          border-radius: 9999px;
          transition: width 0.3s ease;
          background: ${lineColorValue};
        }
        
        .align-center .decorative-line {
          margin-left: auto;
          margin-right: auto;
        }
        
        .align-right .decorative-line {
          margin-left: auto;
        }
        
        .header-container:hover .decorative-line {
          width: 100px;
        }

        /* Responsive adjustments */
        @media (max-width: 640px) {
          .header-content {
            gap: 1rem;
          }

          .header-image {
            width: ${parseInt(config.imageSize) * 0.8}px;
            height: ${parseInt(config.imageSize) * 0.8}px;
          }

          .header {
            font-size: ${config.mobileHeaderSize};
          }

          .subheader {
            font-size: ${config.mobileSubheaderSize};
          }
        }
      </style>
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Cleanup
    return () => {
      if (shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, [align, size, lineColor]);

  useEffect(() => {
    if (!shadowRef.current) return;

    // Clear existing content except style
    const style = shadowRef.current.querySelector('style');
    shadowRef.current.innerHTML = '';
    if (style) shadowRef.current.appendChild(style);

    // Create the container
    const container = document.createElement('div');
    container.className = `header-container align-${align}`;

    // Create content wrapper
    const content = document.createElement('div');
    content.className = 'header-content';

    // Add image if provided
    if (imageUrl) {
      const imageWrapper = document.createElement('div');
      imageWrapper.className = 'header-image';
      const img = document.createElement('img');
      img.src = imageUrl;
      img.alt = imageAlt;
      imageWrapper.appendChild(img);
      content.appendChild(imageWrapper);
    }

    // Create text wrapper
    const textWrapper = document.createElement('div');
    textWrapper.className = 'header-text-wrapper';

    // Add header
    const headerElement = document.createElement('h1');
    headerElement.className = 'header';
    headerElement.textContent = header;
    textWrapper.appendChild(headerElement);

    // Add subheader
    const subheaderElement = document.createElement('p');
    subheaderElement.className = 'subheader';
    subheaderElement.textContent = subheader;
    textWrapper.appendChild(subheaderElement);

    content.appendChild(textWrapper);
    container.appendChild(content);

    // Add decorative line if enabled
    if (withLine) {
      const lineWrapper = document.createElement('div');
      lineWrapper.className = 'line-wrapper';
      const line = document.createElement('div');
      line.className = 'decorative-line';
      lineWrapper.appendChild(line);
      container.appendChild(lineWrapper);
    }

    shadowRef.current.appendChild(container);
  }, [header, subheader, align, imageUrl, imageAlt, withLine]);

  return <div ref={hostRef} className={className} />;
};