// ABOUTME: Shadow DOM Modal component for dialogs and confirmations
// Provides a reusable modal dialog with customizable actions

import React, { useEffect, useRef } from 'react';

export interface ShadowModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm?: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'info' | 'warning' | 'danger' | 'success';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const typeConfig = {
  info: {
    confirmBg: '#6B00DB', // purple
    confirmHover: '#5600b3', // darker purple
    confirmText: '#FFFFFF',
  },
  success: {
    confirmBg: '#10B981', // green
    confirmHover: '#059669', // darker green
    confirmText: '#FFFFFF',
  },
  warning: {
    confirmBg: '#F59E0B', // orange
    confirmHover: '#D97706', // darker orange
    confirmText: '#FFFFFF',
  },
  danger: {
    confirmBg: '#EF4444', // red
    confirmHover: '#DC2626', // darker red
    confirmText: '#FFFFFF',
  },
};

const sizeConfig = {
  sm: { maxWidth: '400px', padding: '1rem' },
  md: { maxWidth: '500px', padding: '1.5rem' },
  lg: { maxWidth: '700px', padding: '2rem' },
};

export const ShadowModal: React.FC<ShadowModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  type = 'info',
  size = 'md',
  className = ''
}) => {
  const hostRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);

  useEffect(() => {
    if (!hostRef.current || shadowRef.current) return;

    // Create shadow root


    

    shadowRef.current = hostRef.current.attachShadow({ mode: 'open' });

    // Create styles
    const config = typeConfig[type] || typeConfig.info; // Fallback to 'info' if type is invalid
    const sizeStyles = sizeConfig[size] || sizeConfig.md; // Fallback to 'md' if size is invalid
    
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Montserrat:wght@400;500;600&display=swap');
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        :host {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 9999;
          display: ${isOpen ? 'block' : 'none'};
        }
        
        .modal-backdrop {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.75);
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 1rem;
          animation: fadeIn 150ms ease-out;
        }
        
        .modal-content {
          background: #1A1A1A;
          border: 1px solid #3A3A3A;
          border-radius: 0.75rem;
          padding: ${sizeStyles.padding};
          max-width: ${sizeStyles.maxWidth};
          width: 100%;
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
          animation: slideUp 300ms cubic-bezier(0.34, 1.56, 0.64, 1);
          position: relative;
        }
        
        .modal-close {
          position: absolute;
          top: 1rem;
          right: 1rem;
          background: none;
          border: none;
          color: #9CA3AF;
          cursor: pointer;
          padding: 0.5rem;
          margin: -0.5rem;
          border-radius: 0.25rem;
          transition: all 0.2s ease;
          font-size: 1.5rem;
          line-height: 1;
        }
        
        .modal-close:hover {
          color: #FFFFFF;
          background: rgba(255, 255, 255, 0.1);
        }
        
        .modal-title {
          font-family: 'Poppins', sans-serif;
          font-weight: 600;
          font-size: 1.25rem;
          color: #FFFFFF;
          margin: 0 0 1rem 0;
          padding-right: 2rem;
        }
        
        .modal-message {
          font-family: 'Montserrat', sans-serif;
          font-size: 1rem;
          color: #9CA3AF;
          line-height: 1.75;
          margin-bottom: 1.5rem;
          white-space: pre-wrap;
        }
        
        .modal-buttons {
          display: flex;
          gap: 0.75rem;
          justify-content: flex-end;
        }
        
        .modal-button {
          padding: 0.75rem 1.25rem;
          border-radius: 0.5rem;
          font-family: 'Poppins', sans-serif;
          font-weight: 600;
          font-size: 1rem;
          cursor: pointer;
          transition: all 0.2s ease;
          border: none;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
        
        .modal-button:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        .modal-button:active {
          transform: translateY(0);
        }
        
        .cancel-button {
          background: transparent;
          color: #9CA3AF;
          border: 1px solid #3A3A3A;
        }
        
        .cancel-button:hover {
          background: rgba(255, 255, 255, 0.05);
          border-color: #4B5563;
        }
        
        .confirm-button {
          background: ${config.confirmBg};
          color: ${config.confirmText};
        }
        
        .confirm-button:hover {
          background: ${config.confirmHover};
        }
        
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        
        @keyframes slideUp {
          from {
            transform: translateY(20px) scale(0.95);
            opacity: 0;
          }
          to {
            transform: translateY(0) scale(1);
            opacity: 1;
          }
        }
        
        ${className ? `.custom { ${className} }` : ''}
      </style>
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Handle escape key
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };
    document.addEventListener('keydown', handleEscape);

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleEscape);
      if (shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, [type, size, className, isOpen, onClose]);

  useEffect(() => {
    if (!shadowRef.current || !isOpen) return;

    // Clear existing content except style
    const style = shadowRef.current.querySelector('style');
    const existingContent = shadowRef.current.querySelector('.modal-backdrop');
    if (existingContent) existingContent.remove();

    if (!isOpen) return;

    // Create backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop';
    backdrop.onclick = (e) => {
      if (e.target === backdrop) {
        onClose();
      }
    };

    // Create modal content
    const content = document.createElement('div');
    content.className = 'modal-content';
    if (className) content.classList.add('custom');

    // Close button
    const closeBtn = document.createElement('button');
    closeBtn.className = 'modal-close';
    closeBtn.innerHTML = '×';
    closeBtn.onclick = onClose;
    content.appendChild(closeBtn);

    // Title
    const titleEl = document.createElement('h3');
    titleEl.className = 'modal-title';
    titleEl.textContent = title;
    content.appendChild(titleEl);

    // Message
    const messageEl = document.createElement('p');
    messageEl.className = 'modal-message';
    messageEl.textContent = message;
    content.appendChild(messageEl);

    // Buttons
    const buttonsDiv = document.createElement('div');
    buttonsDiv.className = 'modal-buttons';

    // Cancel button
    const cancelBtn = document.createElement('button');
    cancelBtn.className = 'modal-button cancel-button';
    cancelBtn.textContent = cancelText;
    cancelBtn.onclick = onClose;
    buttonsDiv.appendChild(cancelBtn);

    // Confirm button
    if (onConfirm) {
      const confirmBtn = document.createElement('button');
      confirmBtn.className = 'modal-button confirm-button';
      confirmBtn.textContent = confirmText;
      confirmBtn.onclick = () => {
        onConfirm();
        onClose();
      };
      buttonsDiv.appendChild(confirmBtn);
    }

    content.appendChild(buttonsDiv);
    backdrop.appendChild(content);
    shadowRef.current.appendChild(backdrop);

    // Focus trap
    const focusableElements = content.querySelectorAll('button');
    if (focusableElements.length > 0) {
      (focusableElements[focusableElements.length - 1] as HTMLElement).focus();
    }
  }, [isOpen, title, message, confirmText, cancelText, onClose, onConfirm, className]);

  return <div ref={hostRef} style={{ display: isOpen ? 'block' : 'none' }} />;
};