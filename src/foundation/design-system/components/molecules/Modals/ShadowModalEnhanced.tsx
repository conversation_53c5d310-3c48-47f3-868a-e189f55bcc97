// ABOUTME: Enhanced modal with React children support using Shadow DOM
// Supports complex content rendering within isolated Shadow DOM

import React, { useRef, useEffect, ReactNode } from 'react';
import ReactDOM from 'react-dom';

export type ModalType = 'info' | 'warning' | 'danger' | 'success';

export interface ShadowModalEnhancedProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  type?: ModalType;
  width?: string;
  className?: string;
  children: ReactNode;
}

export const ShadowModalEnhanced: React.FC<ShadowModalEnhancedProps> = ({
  isOpen,
  onClose,
  title,
  type = 'info',
  width = '500px',
  className = '',
  children
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const contentContainerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Create shadow DOM


    

    const shadowRoot = container.attachShadow({ mode: 'open' });
    shadowRootRef.current = shadowRoot;

    // Type color mappings
    const typeColors = {
      info: '#6B00DB',
      warning: '#F59E0B',
      danger: '#EF4444', 
      success: '#10B981'
    };

    // Create styles
    const style = document.createElement('style');
    style.textContent = `
      :host {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9999;
        display: ${isOpen ? 'flex' : 'none'};
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .backdrop {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(4px);
        animation: fadeIn 0.3s ease-out;
        cursor: pointer;
      }

      .modal {
        position: relative;
        background: #1f2937;
        border-radius: 12px;
        width: 100%;
        max-width: ${width};
        max-height: 90vh;
        overflow: hidden;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
        animation: slideIn 0.3s ease-out;
        cursor: default;
      }

      .header {
        padding: 20px;
        border-bottom: 1px solid #374151;
        background: #111827;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .title {
        font-size: 18px;
        font-weight: 600;
        color: #FFFFFF;
        margin: 0;
        padding-right: 40px;
      }

      .close-button {
        background: none;
        border: none;
        color: #9CA3AF;
        font-size: 24px;
        cursor: pointer;
        padding: 0;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: all 0.2s;
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
      }

      .close-button:hover {
        color: #FFFFFF;
        background: rgba(255, 255, 255, 0.1);
      }

      .content {
        padding: 0;
        color: #FFFFFF;
        overflow-y: auto;
        max-height: calc(90vh - 80px);
      }

      .type-indicator {
        height: 3px;
        background: ${typeColors[type]};
        width: 100%;
      }

      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: scale(0.95) translateY(-10px);
        }
        to {
          opacity: 1;
          transform: scale(1) translateY(0);
        }
      }

      /* Mobile responsive */
      @media (max-width: 768px) {
        :host {
          padding: 10px;
        }
        
        .modal {
          max-width: 100%;
          margin: 0;
        }
        
        .header {
          padding: 16px;
        }
        
        .title {
          font-size: 16px;
        }
      }
    `;

    // Create modal structure
    const backdrop = document.createElement('div');
    backdrop.className = 'backdrop';
    backdrop.addEventListener('click', onClose);

    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.addEventListener('click', (e) => e.stopPropagation());

    const typeIndicator = document.createElement('div');
    typeIndicator.className = 'type-indicator';

    const header = document.createElement('div');
    header.className = 'header';

    if (title) {
      const titleElement = document.createElement('h2');
      titleElement.className = 'title';
      titleElement.textContent = title;
      header.appendChild(titleElement);
    }

    const closeButton = document.createElement('button');
    closeButton.className = 'close-button';
    closeButton.innerHTML = '×';
    closeButton.addEventListener('click', onClose);
    header.appendChild(closeButton);

    const content = document.createElement('div');
    content.className = 'content';
    contentContainerRef.current = content;

    // Assemble modal
    modal.appendChild(typeIndicator);
    if (title) {
      modal.appendChild(header);
    }
    modal.appendChild(content);

    // Assemble everything
    shadowRoot.appendChild(style);
    shadowRoot.appendChild(backdrop);
    shadowRoot.appendChild(modal);

    // Handle ESC key
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEsc);

    return () => {
      document.removeEventListener('keydown', handleEsc);
      if (shadowRootRef.current) {
        shadowRootRef.current.innerHTML = '';
      }
    };
  }, [isOpen, onClose, title, type, width]);

  // Render children into the content container
  useEffect(() => {
    if (contentContainerRef.current && isOpen) {
      ReactDOM.render(<>{children}</>, contentContainerRef.current);
    }
  }, [children, isOpen]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, zIndex: 9999, pointerEvents: isOpen ? 'auto' : 'none' }}
    />
  );
};

export default ShadowModalEnhanced;