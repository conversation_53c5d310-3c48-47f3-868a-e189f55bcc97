// ABOUTME: Shadow DOM notification modal component for alerts and confirmations
// Provides a modal with variants for success, error, warning, and default states

import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';

export interface ShadowNotificationModalProps {
  isOpen: boolean;
  title: string;
  message: string;
  buttonText?: string;
  onClose: () => void;
  onConfirm?: () => void;
  variant?: 'default' | 'success' | 'error' | 'warning';
}

const variantConfig = {
  default: {
    iconBg: '#1A1A1A',
    iconColor: '#FFFFFF',
    buttonBg: '#1ABC9C',
    buttonHoverBg: '#16A085',
    icon: ''
  },
  success: {
    iconBg: 'rgba(16, 185, 129, 0.1)', // 10% opacity green
    iconColor: '#10B981',
    buttonBg: '#10B981',
    buttonHoverBg: '#059669',
    icon: '✓'
  },
  error: {
    iconBg: 'rgba(239, 68, 68, 0.1)', // 10% opacity red
    iconColor: '#EF4444',
    buttonBg: '#EF4444',
    buttonHoverBg: '#DC2626',
    icon: '✕'
  },
  warning: {
    iconBg: 'rgba(245, 158, 11, 0.1)', // 10% opacity orange
    iconColor: '#F59E0B',
    buttonBg: '#F59E0B',
    buttonHoverBg: '#D97706',
    icon: '!'
  }
};

export const ShadowNotificationModal: React.FC<ShadowNotificationModalProps> = ({
  isOpen,
  title,
  message,
  buttonText = 'Got it',
  onClose,
  onConfirm,
  variant = 'default'
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);

  useEffect(() => {
    if (!containerRef.current || shadowRef.current) return;

    // Create shadow root


    

    shadowRef.current = containerRef.current.attachShadow({ mode: 'open' });

    // Create styles
    const config = variantConfig[variant];
    
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600&display=swap');
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        :host {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 9999;
        }

        .modal-backdrop {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.85);
          backdrop-filter: blur(4px);
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 1.25rem;
          animation: fadeIn 300ms ease-out;
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }

        .modal-container {
          background: #1A1A1A;
          border-radius: 1rem;
          padding: 2rem;
          max-width: 440px;
          width: 100%;
          box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
          animation: slideUp 500ms ease-out;
          position: relative;
          border: 1px solid #3A3A3A;
        }

        @keyframes slideUp {
          from {
            opacity: 0;
            transform: translateY(20px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        .modal-icon {
          width: 80px;
          height: 80px;
          margin: 0 auto 1.5rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background: ${config.iconBg};
          color: ${config.iconColor};
          font-size: 2.25rem;
          font-weight: 700;
        }

        .modal-title {
          font-family: 'Poppins', sans-serif;
          font-size: 1.875rem;
          font-weight: 700;
          color: #FFFFFF;
          text-align: center;
          margin-bottom: 1rem;
          letter-spacing: -0.025em;
        }

        .modal-message {
          font-family: 'Montserrat', sans-serif;
          font-size: 1rem;
          line-height: 1.625;
          color: #B3B3B3;
          text-align: center;
          margin-bottom: 2rem;
        }

        .modal-button {
          width: 100%;
          padding: 1rem 1.5rem;
          border-radius: 0.75rem;
          font-family: 'Poppins', sans-serif;
          font-size: 1rem;
          font-weight: 600;
          border: none;
          cursor: pointer;
          transition: all 0.2s ease;
          text-transform: none;
          letter-spacing: 0.025em;
          background: ${config.buttonBg};
          color: ${variant === 'error' ? '#FFFFFF' : '#0A0A0A'};
        }

        .modal-button:hover {
          background: ${config.buttonHoverBg};
          transform: translateY(-1px);
          box-shadow: 0 4px 12px ${config.buttonBg}4D;
        }

        .modal-button:active {
          transform: translateY(0);
        }

        /* Mobile responsiveness */
        @media (max-width: 640px) {
          .modal-backdrop {
            padding: 1rem;
          }

          .modal-container {
            padding: 1.5rem;
          }

          .modal-icon {
            width: 64px;
            height: 64px;
            font-size: 1.5rem;
            margin-bottom: 1.25rem;
          }

          .modal-title {
            font-size: 1.5rem;
            margin-bottom: 0.75rem;
          }

          .modal-message {
            font-size: 0.875rem;
            margin-bottom: 1.5rem;
          }

          .modal-button {
            padding: 0.75rem 1.25rem;
            font-size: 0.875rem;
          }
        }
      </style>
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Cleanup
    return () => {
      if (shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, [variant]);

  useEffect(() => {
    if (!shadowRef.current || !isOpen) {
      if (shadowRef.current) {
        const style = shadowRef.current.querySelector('style');
        shadowRef.current.innerHTML = '';
        if (style) shadowRef.current.appendChild(style);
      }
      return;
    }

    // Clear existing content except style
    const style = shadowRef.current.querySelector('style');
    shadowRef.current.innerHTML = '';
    if (style) shadowRef.current.appendChild(style);

    // Create backdrop
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop';
    backdrop.addEventListener('click', (e) => {
      if (e.target === backdrop) {
        onClose();
      }
    });

    // Create container
    const container = document.createElement('div');
    container.className = 'modal-container';

    // Create icon if not default variant
    if (variant !== 'default' && variantConfig[variant].icon) {
      const iconDiv = document.createElement('div');
      iconDiv.className = 'modal-icon';
      iconDiv.textContent = variantConfig[variant].icon;
      container.appendChild(iconDiv);
    }

    // Create title
    const titleEl = document.createElement('h2');
    titleEl.className = 'modal-title';
    titleEl.textContent = title;
    container.appendChild(titleEl);

    // Create message
    const messageEl = document.createElement('p');
    messageEl.className = 'modal-message';
    messageEl.textContent = message;
    container.appendChild(messageEl);

    // Create button
    const button = document.createElement('button');
    button.className = 'modal-button';
    button.textContent = buttonText;
    button.addEventListener('click', () => {
      if (onConfirm) {
        onConfirm();
      }
      onClose();
    });
    container.appendChild(button);

    backdrop.appendChild(container);
    shadowRef.current.appendChild(backdrop);
  }, [isOpen, title, message, buttonText, onClose, onConfirm, variant]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return createPortal(
    <div ref={containerRef} />,
    document.body
  );
};