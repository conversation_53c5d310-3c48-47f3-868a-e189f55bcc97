// ABOUTME: Shadow DOM modal component specialized for forms
// Provides a modal container with proper form styling and scroll behavior

import React, { useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import { createBaseStyles, injectStyles } from '../../../utils/shadowDom';
import { theme } from '../../../tokens';
export interface ShadowFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  maxWidth?: string;
  className?: string;
}

export const ShadowFormModal: React.FC<ShadowFormModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  maxWidth = '500px',
  className = ''
}) => {
  const shadowHostRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!shadowHostRef.current) return;

    let shadowRoot = shadowHostRef.current.shadowRoot;
    if (!shadowRoot) {
      shadowRoot = shadowHostRef.current.attachShadow({ mode: 'open' });
    }

    // Clear shadow DOM
    shadowRoot.innerHTML = '';

    const styles = `
      <style>
      ${createBaseStyles()}
      
      :host {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9999;
        display: ${isOpen ? 'block' : 'none'};
      }
      
      .modal-backdrop {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(8px);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16px;
        animation: fadeIn 0.2s ease;
      }
      
      .modal-container {
        background: ${theme.colors.dark.background};
        border: 1px solid ${theme.colors.dark.border};
        border-radius: ${theme.borderRadius.xl};
        max-width: ${maxWidth};
        width: 100%;
        max-height: 90vh;
        overflow: hidden;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        animation: slideUp 0.3s ease;
        display: flex;
        flex-direction: column;
      }
      
      .modal-header {
        padding: 24px;
        border-bottom: 1px solid ${theme.colors.border.subtle};
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
      }
      
      .modal-title {
        font-family: ${theme.typography.fontFamily.heading};
        font-weight: 700;
        font-size: 24px;
        color: #FFFFFF;
        margin: 0;
      }
      
      .close-button {
        background: none;
        border: none;
        color: #B3B3B3;
        cursor: pointer;
        padding: 8px;
        border-radius: ${theme.borderRadius.md};
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .close-button:hover {
        background: ${theme.colors.dark.surfaceLight};
        color: #FFFFFF;
      }
      
      .close-button svg {
        width: 24px;
        height: 24px;
      }
      
      .modal-body {
        flex: 1;
        overflow-y: auto;
        padding: 24px;
        -webkit-overflow-scrolling: touch;
      }
      
      .content-container {
        min-height: 0;
      }
      
      /* Form-specific styles */
      .modal-body form {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
      
      .modal-body label {
        font-family: ${theme.typography.fontFamily.body};
        font-size: 14px;
        color: #B3B3B3;
        margin-bottom: 8px;
        display: block;
      }
      
      .modal-body input,
      .modal-body textarea,
      .modal-body select {
        width: 100%;
        padding: 12px 16px;
        background: ${theme.colors.dark.surfaceLight};
        border: 1px solid ${theme.colors.dark.border};
        border-radius: ${theme.borderRadius.md};
        color: #FFFFFF;
        font-family: ${theme.typography.fontFamily.body};
        font-size: 16px;
        transition: all 0.2s ease;
      }
      
      .modal-body input:focus,
      .modal-body textarea:focus,
      .modal-body select:focus {
        outline: none;
        border-color: ${theme.colors.primary.teal};
        background: ${theme.colors.dark.surface};
      }
      
      /* Animations */
      @keyframes fadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }
      
      @keyframes slideUp {
        from {
          transform: translateY(20px);
          opacity: 0;
        }
        to {
          transform: translateY(0);
          opacity: 1;
        }
      }
      
      /* Mobile responsiveness */
      @media (max-width: 640px) {
        .modal-backdrop {
          padding: 0;
        }
        
        .modal-container {
          max-width: 100%;
          max-height: 100vh;
          height: 100vh;
          border-radius: 0;
        }
        
        .modal-header {
          padding: 20px;
        }
        
        .modal-title {
          font-size: 20px;
        }
        
        .modal-body {
          padding: 20px;
        }
      }
    `;

    injectStyles(shadowRoot, styles);

    // Create modal structure
    const backdrop = document.createElement('div');
    backdrop.className = 'modal-backdrop';
    backdrop.onclick = (e) => {
      if (e.target === backdrop) {
        onClose();
      }
    };

    const container = document.createElement('div');
    container.className = 'modal-container';

    const header = document.createElement('div');
    header.className = 'modal-header';

    const titleElement = document.createElement('h2');
    titleElement.className = 'modal-title';
    titleElement.textContent = title;

    const closeButton = document.createElement('button');
    closeButton.className = 'close-button';
    closeButton.innerHTML = `
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    `;
    closeButton.onclick = onClose;

    header.appendChild(titleElement);
    header.appendChild(closeButton);

    const body = document.createElement('div');
    body.className = 'modal-body';

    const contentContainer = document.createElement('div');
    contentContainer.className = 'content-container';
    contentRef.current = contentContainer;

    body.appendChild(contentContainer);
    container.appendChild(header);
    container.appendChild(body);
    backdrop.appendChild(container);
    shadowRoot.appendChild(backdrop);

    // Handle escape key
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose, title, maxWidth]);

  // Render children into the content container
  useEffect(() => {
    if (contentRef.current && isOpen) {
      ReactDOM.render(<>{children}</>, contentRef.current);
    }
    return () => {
      if (contentRef.current) {
        ReactDOM.unmountComponentAtNode(contentRef.current);
      }
    };
  }, [children, isOpen]);

  // Portal render
  return ReactDOM.createPortal(
    <div ref={shadowHostRef} className={className} />,
    document.body
  );
};