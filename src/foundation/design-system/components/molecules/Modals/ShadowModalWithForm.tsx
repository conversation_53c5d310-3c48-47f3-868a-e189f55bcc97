// ABOUTME: Shadow DOM modal component using Web Components for advanced form handling
// Custom element implementation for better encapsulation and reusability

import React, { useEffect, useRef } from 'react';
import ReactDOM from 'react-dom';
import { createRoot, Root } from 'react-dom/client';
export interface ShadowModalWithFormProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  maxWidth?: string;
  className?: string;
}

// Define custom element if not already defined
if (!customElements.get('shadow-modal-form')) {
  class ShadowModalFormElement extends HTMLElement {
    private root: ShadowRoot;
    private contentContainer: HTMLDivElement | null = null;
    private isOpenValue: boolean = false;
    private titleValue: string = '';
    private maxWidthValue: string = '500px';
    private onCloseCallback: (() => void) | null = null;

    constructor() {
      super();
      this.root = this.attachShadow({ mode: 'open' });
      this.render();
    }

    static get observedAttributes() {
      return ['is-open', 'title', 'max-width'];
    }

    attributeChangedCallback(name: string, oldValue: string | null, newValue: string | null) {
      switch (name) {
        case 'is-open':
          this.isOpenValue = newValue === 'true';
          this.updateVisibility();
          break;
        case 'title':
          this.titleValue = newValue || '';
          this.updateTitle();
          break;
        case 'max-width':
          this.maxWidthValue = newValue || '500px';
          this.updateMaxWidth();
          break;
      }
    }

    setOnClose(callback: () => void) {
      this.onCloseCallback = callback;
    }

    getContentContainer() {
      return this.contentContainer;
    }

    private updateVisibility() {
      const backdrop = this.root.querySelector('.modal-backdrop') as HTMLElement;
      if (backdrop) {
        backdrop.style.display = this.isOpenValue ? 'flex' : 'none';
      }
    }

    private updateTitle() {
      const titleEl = this.root.querySelector('.modal-title');
      if (titleEl) {
        titleEl.textContent = this.titleValue;
      }
    }

    private updateMaxWidth() {
      const container = this.root.querySelector('.modal-container') as HTMLElement;
      if (container) {
        container.style.maxWidth = this.maxWidthValue;
      }
    }

    private render() {
      this.root.innerHTML = `
        <style>
          @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Montserrat:wght@400;500;600&display=swap');
          
          :host {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 99999;
            pointer-events: none;
          }
          
          * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
          }
          
          .modal-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(8px);
            display: none;
            align-items: center;
            justify-content: center;
            padding: 16px;
            animation: fadeIn 0.2s ease;
            pointer-events: auto;
          }
          
          .modal-container {
            background: #1E1E1E;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            max-width: 500px;
            width: 100%;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            animation: slideUp 0.3s ease;
            display: flex;
            flex-direction: column;
            position: relative;
          }
          
          .modal-header {
            padding: 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
          }
          
          .modal-title {
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
            font-size: 24px;
            color: #FFFFFF;
            margin: 0;
            letter-spacing: -0.02em;
          }
          
          .close-button {
            background: none;
            border: none;
            color: #B3B3B3;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          
          .close-button:hover {
            background: rgba(255, 255, 255, 0.05);
            color: #FFFFFF;
          }
          
          .close-button svg {
            width: 24px;
            height: 24px;
          }
          
          .modal-body {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
            -webkit-overflow-scrolling: touch;
            /* Hide scrollbar for Chrome, Safari and Opera */
            &::-webkit-scrollbar {
              width: 6px;
            }
            &::-webkit-scrollbar-track {
              background: transparent;
            }
            &::-webkit-scrollbar-thumb {
              background: rgba(255, 255, 255, 0.2);
              border-radius: 3px;
            }
            &::-webkit-scrollbar-thumb:hover {
              background: rgba(255, 255, 255, 0.3);
            }
          }
          
          .content-container {
            min-height: 0;
          }
          
          /* Form styles within modal */
          .content-container form {
            display: flex;
            flex-direction: column;
            gap: 20px;
          }
          
          .content-container label {
            display: block;
            font-family: 'Montserrat', sans-serif;
            font-size: 14px;
            font-weight: 500;
            color: #B3B3B3;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
          }
          
          .content-container input,
          .content-container textarea,
          .content-container select {
            width: 100%;
            padding: 12px 16px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            color: #FFFFFF;
            font-family: 'Poppins', sans-serif;
            font-size: 16px;
            transition: all 0.2s ease;
          }
          
          .content-container input:focus,
          .content-container textarea:focus,
          .content-container select:focus {
            outline: none;
            border-color: #1ABC9C;
            background: #2A2A2A;
            box-shadow: 0 0 0 3px #1ABC9C20;
          }
          
          /* Animations */
          @keyframes fadeIn {
            from {
              opacity: 0;
            }
            to {
              opacity: 1;
            }
          }
          
          @keyframes slideUp {
            from {
              transform: translateY(20px);
              opacity: 0;
            }
            to {
              transform: translateY(0);
              opacity: 1;
            }
          }
          
          /* Mobile responsiveness */
          @media (max-width: 640px) {
            .modal-backdrop {
              padding: 0;
            }
            
            .modal-container {
              max-width: 100%;
              max-height: 100vh;
              height: 100vh;
              border-radius: 0;
              border: none;
            }
            
            .modal-header {
              padding: 20px;
              border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }
            
            .modal-title {
              font-size: 20px;
            }
            
            .modal-body {
              padding: 20px;
            }
          }
        </style>
        <div class="modal-backdrop">
          <div class="modal-container">
            <div class="modal-header">
              <h2 class="modal-title"></h2>
              <button class="close-button">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            <div class="modal-body">
              <div class="content-container"></div>
            </div>
          </div>
        </div>
      `;

      // Set up event listeners
      const backdrop = this.root.querySelector('.modal-backdrop');
      if (backdrop) {
        backdrop.addEventListener('click', (e) => {
          if (e.target === backdrop && this.onCloseCallback) {
            this.onCloseCallback();
          }
        });
      }

      const closeButton = this.root.querySelector('.close-button');
      if (closeButton) {
        closeButton.addEventListener('click', () => {
          if (this.onCloseCallback) {
            this.onCloseCallback();
          }
        });
      }

      this.contentContainer = this.root.querySelector('.content-container') as HTMLDivElement;
    }

    connectedCallback() {
      // Handle escape key
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape' && this.isOpenValue && this.onCloseCallback) {
          this.onCloseCallback();
        }
      };
      document.addEventListener('keydown', handleEscape);
      this.setAttribute('data-escape-listener', 'true');
    }

    disconnectedCallback() {
      if (this.getAttribute('data-escape-listener') === 'true') {
        document.removeEventListener('keydown', this.handleEscape);
      }
    }

    private handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && this.isOpenValue && this.onCloseCallback) {
        this.onCloseCallback();
      }
    };
  }

  customElements.define('shadow-modal-form', ShadowModalFormElement);
}

export const ShadowModalWithForm: React.FC<ShadowModalWithFormProps> = ({
  isOpen,
  onClose,
  title,
  children,
  maxWidth = '500px',
  className = ''
}) => {
  const elementRef = useRef<HTMLElement | null>(null);
  const reactRootRef = useRef<Root | null>(null);

  useEffect(() => {
    const modalElement = document.createElement('shadow-modal-form') as any;
    modalElement.setAttribute('is-open', isOpen.toString());
    modalElement.setAttribute('title', title);
    modalElement.setAttribute('max-width', maxWidth);
    modalElement.setOnClose(onClose);
    
    if (className) {
      modalElement.className = className;
    }

    document.body.appendChild(modalElement);
    elementRef.current = modalElement;

    // Render React children into the shadow DOM content container
    const contentContainer = modalElement.getContentContainer();
    if (contentContainer) {
      reactRootRef.current = createRoot(contentContainer);
      reactRootRef.current.render(<>{children}</>);
    }

    return () => {
      if (reactRootRef.current) {
        reactRootRef.current.unmount();
      }
      if (elementRef.current && elementRef.current.parentNode) {
        elementRef.current.parentNode.removeChild(elementRef.current);
      }
    };
  }, []);

  // Update attributes when props change
  useEffect(() => {
    if (elementRef.current) {
      elementRef.current.setAttribute('is-open', isOpen.toString());
    }
  }, [isOpen]);

  useEffect(() => {
    if (elementRef.current) {
      elementRef.current.setAttribute('title', title);
    }
  }, [title]);

  useEffect(() => {
    if (elementRef.current) {
      elementRef.current.setAttribute('max-width', maxWidth);
    }
  }, [maxWidth]);

  // Update children when they change
  useEffect(() => {
    if (reactRootRef.current) {
      reactRootRef.current.render(<>{children}</>);
    }
  }, [children]);

  return null;
};