import React, { useEffect, useRef, useState } from 'react';
import ShadowEvaluationSlider from './ShadowEvaluationSlider';
import { isDebugMode } from '../../../../../utils/debugMode';
import { ShadowDebugModal } from '../../../../../components/shadow/ShadowDebugModal';

interface PlayerRatings {
  technical: number;
  physical: number;
  psychological: number;
  social: number;
  positional: number;
}

interface ShadowPlayerEvaluationProps {
  playerId: string;
  playerName: string;
  playerAvatar?: string;
  position?: string;
  ratings: PlayerRatings;
  preEvalRatings?: Partial<PlayerRatings>;
  onRatingChange: (category: string, value: number) => void;
  onResetAll?: () => void;
  onSave?: () => void;
  hasPreEvaluation?: boolean;
  isSaved?: boolean;
  debugData?: any; // Additional debug data
}

const ShadowPlayerEvaluation: React.FC<ShadowPlayerEvaluationProps> = ({
  playerId,
  playerName,
  playerAvatar,
  position,
  ratings,
  preEvalRatings,
  onRatingChange,
  onResetAll,
  onSave,
  hasPreEvaluation,
  isSaved,
  debugData
}) => {
  const shadowHostRef = useRef<HTMLDivElement>(null);
  const [showDebugModal, setShowDebugModal] = useState(false);

  useEffect(() => {
    if (!shadowHostRef.current) return;

    let shadowRoot = shadowHostRef.current.shadowRoot;
    if (!shadowRoot) {
      shadowRoot = shadowHostRef.current.attachShadow({ mode: 'open' });
    } else {
      shadowRoot.innerHTML = '';
    }

    const style = document.createElement('style');
    style.textContent = `
      * {
        box-sizing: border-box;
      }
      
      .player-card {
        background-color: #1a1a1a;
        border: 1px solid #333;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
      }
      
      .player-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
      }
      
      .player-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: #333;
        margin-right: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
      }
      
      .player-avatar img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }
      
      .player-info {
        flex: 1;
      }
      
      .player-name {
        font-size: 18px;
        font-weight: 600;
        color: #fff;
        margin-bottom: 5px;
      }
      
      .player-position {
        color: #999;
        font-size: 14px;
      }
      
      .pre-eval-badge {
        background-color: #FFA500;
        color: #000;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        margin-left: 10px;
      }
      
      .reset-button {
        padding: 6px 12px;
        background: transparent;
        color: #FFA500;
        border: 1px solid #FFA500;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
      }
      
      .reset-button:hover {
        background-color: #FFA500;
        color: #000;
      }
      
      .save-button {
        padding: 8px 16px;
        background: #1ABC9C;
        color: #000;
        border: 1px solid #1ABC9C;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.2s;
      }
      
      .save-button:hover {
        background-color: #16A085;
        border-color: #16A085;
      }
      
      .save-button.saved {
        background: transparent;
        color: #1ABC9C;
        border: 1px solid #1ABC9C;
      }
      
      .save-button.saved::before {
        content: '✓ ';
      }
      
      .rating-section {
        margin-top: 15px;
      }
      
      .card-footer {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #333;
      }
      
      /* Debug button styles */
      .debug-button {
        position: absolute;
        bottom: 8px;
        right: 8px;
        background: rgba(220, 38, 38, 0.9);
        border: 1px solid #DC2626;
        border-radius: 6px;
        padding: 4px 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 14px;
        color: #FFFFFF;
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
        z-index: 10;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
      
      .debug-button:hover {
        background: #DC2626;
        transform: scale(1.05);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }
      
      .player-card {
        position: relative;
      }
    `;

    const container = document.createElement('div');
    container.className = 'player-card';

    const hasAnyPreEval = hasPreEvaluation && preEvalRatings && Object.keys(preEvalRatings).length > 0;

    container.innerHTML = `
      <div class="player-header">
        <div class="player-avatar">
          ${playerAvatar ? `<img src="${playerAvatar}" alt="${playerName}">` : '👤'}
        </div>
        <div class="player-info">
          <div class="player-name">
            ${playerName}
            ${hasAnyPreEval ? '<span class="pre-eval-badge">Pre-eval</span>' : ''}
          </div>
          <div class="player-position">${position || 'No position'}</div>
        </div>
        ${hasAnyPreEval && onResetAll ? `
          <button class="reset-button">Reset All</button>
        ` : ''}
      </div>
      <div class="rating-section" id="rating-section"></div>
      ${onSave ? `
        <div class="card-footer">
          <button class="save-button ${isSaved ? 'saved' : ''}">
            ${isSaved ? 'Saved' : 'Save'}
          </button>
        </div>
      ` : ''}
      ${isDebugMode() && (debugData || playerId) ? `
        <button class="debug-button" data-action="debug" title="Show debug information">
          🐛
        </button>
      ` : ''}
    `;

    shadowRoot.appendChild(style);
    shadowRoot.appendChild(container);

    // Add rating sliders
    const ratingSection = shadowRoot.getElementById('rating-section');
    if (ratingSection) {
      const categories: (keyof PlayerRatings)[] = ['technical', 'physical', 'psychological', 'social', 'positional'];
      
      categories.forEach(category => {
        const sliderContainer = document.createElement('div');
        shadowRoot.appendChild(sliderContainer);
        
        // Mount the slider component
        const sliderHost = document.createElement('div');
        ratingSection.appendChild(sliderHost);
        
        // Determine color based on evaluation state
        const getEvaluationColor = () => {
          if (!preEvalRatings?.[category]) return '#1ABC9C'; // Green if no pre-eval
          if (ratings[category] === 0 && preEvalRatings?.[category]) return '#FFA500'; // Orange if untouched with pre-eval
          if (ratings[category] === preEvalRatings[category] && ratings[category] > 0) return '#9B59B6'; // Purple if agreed (and not 0)
          return '#1ABC9C'; // Green if changed
        };
        
        // We'll render the slider directly in the shadow DOM
        const sliderShadow = sliderHost.attachShadow({ mode: 'open' });
        const sliderStyle = document.createElement('style');
        sliderStyle.textContent = `
          .rating-row {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background-color: ${preEvalRatings?.[category] ? 'rgba(255, 165, 0, 0.1)' : '#0a0a0a'};
            border-radius: 8px;
            border: 1px solid ${preEvalRatings?.[category] ? 'rgba(255, 165, 0, 0.3)' : 'transparent'};
          }
          
          .rating-label {
            flex: 0 0 120px;
            font-size: 14px;
            text-transform: capitalize;
            color: #fff;
          }
          
          .dots-container {
            display: flex;
            gap: 4px;
            margin-right: 15px;
          }
          
          .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #555;
            cursor: pointer;
            transition: all 0.2s;
          }
          
          .dot:hover {
            border-color: #888;
            transform: scale(1.1);
          }
          
          .dot.filled {
            background-color: ${getEvaluationColor()};
            border-color: ${getEvaluationColor()};
          }
          
          .pre-eval-indicator {
            font-weight: bold;
            color: #FFA500;
            margin-right: 10px;
            width: 40px;
            text-align: center;
          }
          
          .rating-slider {
            display: none; /* Hide the range slider - using dots only */
          }
          
          .rating-value {
            width: 40px;
            text-align: center;
            font-weight: bold;
            color: ${getEvaluationColor()};
          }
        `;
        
        const sliderContainer2 = document.createElement('div');
        sliderContainer2.className = 'rating-row';
        sliderContainer2.innerHTML = `
          <span class="rating-label">${category}</span>
          ${preEvalRatings?.[category] ? `<span class="pre-eval-indicator">${preEvalRatings[category]}</span>` : ''}
          <div class="dots-container">
            ${[1, 2, 3, 4, 5].map(i => `
              <div class="dot ${i <= (ratings[category] || preEvalRatings?.[category] || 0) ? 'filled' : ''}" data-value="${i}"></div>
            `).join('')}
          </div>
          <input 
            type="range" 
            class="rating-slider" 
            min="0" 
            max="5" 
            value="${ratings[category]}"
            data-category="${category}"
          >
          <span class="rating-value">${ratings[category] || preEvalRatings?.[category] || 0}/5</span>
        `;
        
        sliderShadow.appendChild(sliderStyle);
        sliderShadow.appendChild(sliderContainer2);
        
        // Add event listeners
        const slider = sliderShadow.querySelector('input[type="range"]');
        if (slider) {
          slider.addEventListener('input', (e) => {
            const target = e.target as HTMLInputElement;
            onRatingChange(category, parseInt(target.value));
          });
        }
        
        // Add click handlers to dots
        const dots = sliderShadow.querySelectorAll('.dot');
        dots.forEach(dot => {
          dot.addEventListener('click', (e) => {
            const target = e.target as HTMLElement;
            const dotValue = parseInt(target.getAttribute('data-value') || '0');
            onRatingChange(category, dotValue);
          });
        });
      });
    }

    // Add reset button listener
    if (hasAnyPreEval && onResetAll) {
      const resetBtn = shadowRoot.querySelector('.reset-button');
      if (resetBtn) {
        resetBtn.addEventListener('click', () => {
          onResetAll();
        });
      }
    }

    // Add save button listener
    if (onSave) {
      const saveBtn = shadowRoot.querySelector('.save-button');
      if (saveBtn) {
        saveBtn.addEventListener('click', () => {
          onSave();
        });
      }
    }

    // Add debug button listener
    if (isDebugMode() && (debugData || playerId)) {
      const debugBtn = shadowRoot.querySelector('.debug-button');
      if (debugBtn) {
        debugBtn.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          setShowDebugModal(true);
        });
      }
    }

    return () => {
      if (shadowHostRef.current && shadowHostRef.current.shadowRoot) {
        shadowHostRef.current.shadowRoot.innerHTML = '';
      }
    };
  }, [playerId, playerName, playerAvatar, position, ratings, preEvalRatings, onRatingChange, onResetAll, onSave, hasPreEvaluation, isSaved, debugData]);

  return (
    <>
      <div ref={shadowHostRef} />
      <ShadowDebugModal
        isOpen={showDebugModal}
        onClose={() => setShowDebugModal(false)}
        title={`🐛 Player Evaluation Debug - ${playerName}`}
        data={{
          '🎯 Component Props (ShadowPlayerEvaluation)': {
            playerId,
            playerName,
            playerAvatar,
            position,
            ratings,
            preEvalRatings,
            hasPreEvaluation,
            isSaved
          },
          '📊 Additional Debug Data (from parent)': debugData || {},
          '🔧 Component State': {
            showDebugModal
          }
        }}
      />
    </>
  );
};

export default ShadowPlayerEvaluation;