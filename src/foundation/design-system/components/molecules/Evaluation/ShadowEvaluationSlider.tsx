import React, { useEffect, useRef } from 'react';

interface ShadowEvaluationSliderProps {
  category: string;
  value: number;
  preEvalValue?: number;
  onChange: (value: number) => void;
  disabled?: boolean;
}

const ShadowEvaluationSlider: React.FC<ShadowEvaluationSliderProps> = ({
  category,
  value,
  preEvalValue,
  onChange,
  disabled = false
}) => {
  const shadowHostRef = useRef<HTMLDivElement>(null);
  
  // Determine color based on evaluation state
  const getEvaluationColor = () => {
    if (!preEvalValue) return '#1ABC9C'; // Green if no pre-eval
    if (value === 0 && preEvalValue) return '#FFA500'; // Orange if untouched with pre-eval
    if (value === preEvalValue && value > 0) return '#9B59B6'; // Purple if agreed (and not 0)
    return '#1ABC9C'; // Green if changed
  };

  useEffect(() => {
    if (!shadowHostRef.current) return;

    // Check if shadow root already exists
    let shadowRoot = shadowHostRef.current.shadowRoot;
    
    // If not, create shadow root
    if (!shadowRoot) {
      shadowRoot = shadowHostRef.current.attachShadow({ mode: 'open' });
    } else {
      // Clear existing content
      shadowRoot.innerHTML = '';
    }

    // Create styles
    const style = document.createElement('style');
    style.textContent = `
      * {
        box-sizing: border-box;
      }
      
      .rating-row {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px;
        background-color: ${preEvalValue ? 'rgba(255, 165, 0, 0.1)' : '#0a0a0a'};
        border-radius: 8px;
        border: 1px solid ${preEvalValue ? 'rgba(255, 165, 0, 0.3)' : 'transparent'};
        transition: all 0.2s;
      }
      
      .rating-label {
        flex: 0 0 120px;
        font-size: 14px;
        text-transform: capitalize;
        color: #fff;
      }
      
      .dots-container {
        display: flex;
        gap: 4px;
        margin-right: 15px;
      }
      
      .dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid #555;
        transition: all 0.2s;
        cursor: pointer;
      }
      
      .dot:hover {
        border-color: #888;
        transform: scale(1.1);
      }
      
      .dot.filled {
        background-color: ${getEvaluationColor()};
        border-color: ${getEvaluationColor()};
      }
      
      .pre-eval-indicator {
        font-weight: bold;
        color: #FFA500;
        margin-right: 10px;
        width: 40px;
        text-align: center;
      }
      
      .rating-slider {
        display: none; /* Hide the range slider - using dots only */
      }
      
      .rating-value {
        width: 40px;
        text-align: center;
        font-weight: bold;
        color: ${getEvaluationColor()};
      }
    `;

    // Create content
    const container = document.createElement('div');
    container.className = 'rating-row';

    container.innerHTML = `
      <span class="rating-label">${category}</span>
      ${preEvalValue ? `<span class="pre-eval-indicator">${preEvalValue}</span>` : ''}
      <div class="dots-container">
        ${[1, 2, 3, 4, 5].map(i => `
          <div class="dot ${i <= (value || preEvalValue || 0) ? 'filled' : ''}" data-value="${i}"></div>
        `).join('')}
      </div>
      <input 
        type="range" 
        class="rating-slider" 
        min="0" 
        max="5" 
        value="${value}"
        ${disabled ? 'disabled' : ''}
      >
      <span class="rating-value">${value || preEvalValue || 0}/5</span>
    `;

    // Append to shadow root
    shadowRoot.appendChild(style);
    shadowRoot.appendChild(container);

    // Add event listeners
    const slider = shadowRoot.querySelector('input[type="range"]');
    if (slider && !disabled) {
      slider.addEventListener('input', (e) => {
        const target = e.target as HTMLInputElement;
        const newValue = parseInt(target.value);
        onChange(newValue);
      });
    }
    
    // Add click handlers to dots
    const dots = shadowRoot.querySelectorAll('.dot');
    dots.forEach(dot => {
      dot.addEventListener('click', (e) => {
        if (disabled) return;
        const target = e.target as HTMLElement;
        const dotValue = parseInt(target.getAttribute('data-value') || '0');
        onChange(dotValue);
      });
    });

    // Cleanup
    return () => {
      if (shadowHostRef.current && shadowHostRef.current.shadowRoot) {
        shadowHostRef.current.shadowRoot.innerHTML = '';
      }
    };
  }, [category, value, preEvalValue, onChange, disabled, getEvaluationColor]);

  return <div ref={shadowHostRef} />;
};

export default ShadowEvaluationSlider;