// ABOUTME: Shadow DOM component for displaying statistics with value and label
// A versatile card component for showing metrics with customizable colors and sizes

import React, { useEffect, useRef } from 'react';

export interface ShadowStatCardProps {
  value: number | string;
  label: string;
  color?: 'teal' | 'yellow' | 'green' | 'purple' | 'red' | 'blue';
  size?: 'sm' | 'md' | 'lg' | 'small' | 'medium' | 'large';
  labelSize?: 'xs' | 'sm' | 'md' | 'lg';
  onClick?: () => void;
  fullCardLink?: boolean;
  className?: string;
}

const colorMap = {
  teal: '#1ABC9C',
  yellow: '#F59E0B',
  green: '#10B981',
  purple: '#6B00DB',
  red: '#EF4444',
  blue: '#296DFF'
};

const sizeConfig = {
  sm: {
    value: '1.875rem', // 3xl
    minHeight: '100px',
    padding: '1.25rem' // spacing[5]
  },
  md: {
    value: '3rem', // 5xl
    minHeight: '120px',
    padding: '1.5rem' // spacing[6]
  },
  lg: {
    value: '56px',
    minHeight: '140px',
    padding: '2rem' // spacing[8]
  }
};

const labelSizeConfig = {
  xs: '0.75rem',  // xs
  sm: '0.875rem', // sm
  md: '1rem',     // base
  lg: '1.125rem'  // lg
};

export const ShadowStatCard: React.FC<ShadowStatCardProps> = ({
  value,
  label,
  color = 'teal',
  size = 'md',
  labelSize = 'sm',
  onClick,
  fullCardLink = true,
  className = ''
}) => {
  const hostRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);

  useEffect(() => {
    if (!hostRef.current || shadowRef.current) return;

    // Create shadow root


    

    shadowRef.current = hostRef.current.attachShadow({ mode: 'open' });

    // Create styles
    const valueColor = colorMap[color] || colorMap.teal;
    // Handle both naming conventions for size
    const normalizedSize = size === 'small' ? 'sm' : size === 'medium' ? 'md' : size === 'large' ? 'lg' : size;
    const config = sizeConfig[normalizedSize] || sizeConfig.md;
    const labelFontSize = labelSizeConfig[labelSize] || labelSizeConfig.sm;
    
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600&display=swap');
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        :host {
          display: block;
          width: 100%;
        }

        .stat-card {
          background: #0A0A0A;
          border: 1px solid #3A3A3A;
          border-radius: 1rem;
          padding: ${config.padding};
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          transition: all 0.2s ease;
          position: relative;
          overflow: hidden;
          min-height: ${config.minHeight};
          text-align: center;
        }
        
        .stat-card.clickable {
          cursor: pointer;
        }
        
        .stat-card.clickable:hover {
          background: #1A1A1A;
          border-color: #4A4A4A;
          transform: translateY(-2px);
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        
        .stat-card.clickable:active {
          transform: translateY(0);
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        .stat-value {
          font-family: 'Poppins', sans-serif;
          font-weight: 700;
          font-size: ${config.value};
          line-height: 1;
          margin: 0;
          color: ${valueColor};
          transition: color 0.2s ease, filter 0.2s ease;
        }
        
        .stat-label {
          font-family: 'Montserrat', sans-serif;
          font-weight: 500;
          font-size: ${labelFontSize};
          color: #B3B3B3;
          margin: 0;
          text-transform: capitalize;
          line-height: 1.3;
        }
        
        /* Subtle gradient overlay */
        .stat-card::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, 
            rgba(255, 255, 255, 0.02) 0%, 
            rgba(255, 255, 255, 0) 100%);
          pointer-events: none;
        }
        
        /* Glow effect on hover for value */
        .stat-card.clickable:hover .stat-value {
          filter: brightness(1.2);
          text-shadow: 0 0 20px currentColor;
        }
        
        /* Focus styles for accessibility */
        .stat-card.clickable:focus-visible {
          outline: 2px solid rgba(26, 188, 156, 0.4);
          outline-offset: 2px;
        }
        
        @media (max-width: 640px) {
          .stat-card {
            padding: 1.25rem 1rem;
            min-height: ${normalizedSize === 'sm' ? '80px' : normalizedSize === 'md' ? '100px' : '120px'};
          }
          
          .stat-value {
            font-size: ${normalizedSize === 'sm' ? '1.5rem' : 
                         normalizedSize === 'md' ? '2.25rem' : 
                         '48px'};
          }
          
          .stat-label {
            font-size: ${labelSize === 'xs' ? '10px' : 
                         labelSize === 'sm' ? '0.75rem' : 
                         labelSize === 'md' ? '0.875rem' : 
                         '1rem'};
          }
        }
        
        ${className ? `.custom { ${className} }` : ''}
      </style>
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Cleanup
    return () => {
      if (shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, [color, size, labelSize, className]);

  useEffect(() => {
    if (!shadowRef.current) return;

    // Clear existing content except style
    const style = shadowRef.current.querySelector('style');
    shadowRef.current.innerHTML = '';
    if (style) shadowRef.current.appendChild(style);

    // Create card container
    const cardDiv = document.createElement('div');
    cardDiv.className = `stat-card ${onClick ? 'clickable' : ''}`;
    if (className) cardDiv.classList.add('custom');
    
    if (onClick) {
      cardDiv.setAttribute('role', 'button');
      cardDiv.setAttribute('tabindex', '0');
      if (fullCardLink) {
        cardDiv.addEventListener('click', onClick);
        cardDiv.addEventListener('keydown', (e: KeyboardEvent) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onClick();
          }
        });
      }
    }
    
    // Create inner content wrapper for non-full-card-link mode
    const contentWrapper = fullCardLink ? cardDiv : document.createElement('div');
    if (!fullCardLink) {
      contentWrapper.className = 'stat-content';
    }
    
    // Create value element
    const valueElement = document.createElement('p');
    valueElement.className = 'stat-value';
    valueElement.textContent = value.toString();
    contentWrapper.appendChild(valueElement);
    
    // Create label element
    const labelElement = document.createElement('p');
    labelElement.className = 'stat-label';
    labelElement.textContent = label;
    contentWrapper.appendChild(labelElement);
    
    // If not full card link and onClick exists, make only value clickable
    if (!fullCardLink && onClick) {
      valueElement.style.cursor = 'pointer';
      valueElement.addEventListener('click', onClick);
      valueElement.addEventListener('keydown', (e: KeyboardEvent) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick();
        }
      });
      valueElement.setAttribute('role', 'button');
      valueElement.setAttribute('tabindex', '0');
    }
    
    if (!fullCardLink) {
      cardDiv.appendChild(contentWrapper);
    }
    
    shadowRef.current.appendChild(cardDiv);
  }, [value, label, onClick, fullCardLink]);

  return <div ref={hostRef} />;
};