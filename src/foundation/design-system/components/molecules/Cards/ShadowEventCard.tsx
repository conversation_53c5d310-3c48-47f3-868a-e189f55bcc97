// ABOUTME: Event card component with Shadow DOM encapsulation
// Displays event information with status tracking and evaluation progress

import React, { useRef, useEffect, useState } from 'react';
import { isDebugMode } from '../../../../../utils/debugMode';
import { ShadowDebugModal } from '../../../../../components/shadow/ShadowDebugModal';

export type EventType = 'training' | 'match' | 'session' | 'event';
export type EventStatus = 'upcoming' | 'in-progress' | 'complete';
export type EventSize = 'small' | 'medium' | 'large';
export type BadgePosition = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'inline';
export type BadgeColor = 'yellow' | 'red' | 'green' | 'blue' | 'purple' | 'gray' | 'orange';

export interface ShadowEventCardProps {
  id?: string;
  type: EventType;
  status: EventStatus;
  title: string;
  subtitle?: string;
  date: string;
  time?: string;
  location?: string;
  published?: boolean;
  // Badge customization
  draftBadgePosition?: BadgePosition;
  draftBadgeText?: string;
  draftBadgeColor?: BadgeColor;
  // Pre-evaluation props
  preEvaluationCount?: number;
  totalPlayers?: number;
  preEvaluationTotal?: number;
  preEvaluationCompleted?: number;
  isPreEvaluationEnabled?: boolean;
  // Coach evaluation props
  coachEvaluationCount?: number;
  totalCoachEvaluations?: number;
  evaluationsCompleted?: number;
  evaluationsTotal?: number;
  coachEvaluationDraftCount?: number;
  // Attendance props
  invitedCount?: number;
  attendedCount?: number;
  attendanceCompleted?: boolean;
  skipAttendanceCheck?: boolean;
  size?: EventSize;
  onClick?: () => void;
  onSMSClick?: () => void;
  onSmsReminder?: () => void;
  debugData?: any; // Full event data for debug mode
  onDelete?: (id: string) => void; // Delete handler for debug mode
}

export const ShadowEventCard: React.FC<ShadowEventCardProps> = ({
  id,
  type,
  status,
  title,
  subtitle,
  date,
  time,
  location,
  published = true,
  draftBadgePosition = 'bottom-right',
  draftBadgeText = 'DRAFT',
  draftBadgeColor = 'yellow',
  // Pre-evaluation props
  preEvaluationCount = 0,
  totalPlayers = 0,
  preEvaluationTotal,
  preEvaluationCompleted,
  isPreEvaluationEnabled = false,
  // Coach evaluation props
  coachEvaluationCount = 0,
  totalCoachEvaluations = 0,
  evaluationsCompleted,
  evaluationsTotal,
  coachEvaluationDraftCount = 0,
  // Attendance props
  invitedCount = 0,
  attendedCount = 0,
  attendanceCompleted = false,
  skipAttendanceCheck = false,
  size = 'medium',
  onClick,
  onSMSClick,
  onSmsReminder,
  debugData,
  onDelete
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const [showDebugModal, setShowDebugModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Event type configurations
  const eventTypeConfig = {
    training: { emoji: '🏃', name: 'Training' },
    match: { emoji: '⚽', name: 'Match' },
    session: { emoji: '📋', name: 'Session' },
    event: { emoji: '📅', name: 'Event' }
  };

  // Size configurations
  const sizeConfig = {
    small: { padding: '12px', titleSize: '14px', subtitleSize: '12px' },
    medium: { padding: '16px', titleSize: '16px', subtitleSize: '14px' },
    large: { padding: '20px', titleSize: '18px', subtitleSize: '16px' }
  };

  // Badge position styles
  const getBadgePositionStyles = (position: BadgePosition) => {
    switch (position) {
      case 'top-left':
        return 'top: 12px; left: 12px;';
      case 'top-right':
        return 'top: 12px; right: 12px;';
      case 'bottom-left':
        return 'bottom: 12px; left: 12px;';
      case 'bottom-right':
        return 'bottom: 12px; right: 12px;';
      case 'inline':
        return 'position: static; display: inline-block; margin-left: 12px;';
      default:
        return 'bottom: 12px; right: 12px;';
    }
  };

  // Badge color styles
  const getBadgeColorStyles = (color: BadgeColor) => {
    const colorMap = {
      yellow: { bg: 'rgba(247, 182, 19, 0.2)', border: 'rgba(247, 182, 19, 0.3)', text: '#F7B613' },
      red: { bg: 'rgba(239, 68, 68, 0.2)', border: 'rgba(239, 68, 68, 0.3)', text: '#EF4444' },
      green: { bg: 'rgba(34, 197, 94, 0.2)', border: 'rgba(34, 197, 94, 0.3)', text: '#22C55E' },
      blue: { bg: 'rgba(59, 130, 246, 0.2)', border: 'rgba(59, 130, 246, 0.3)', text: '#3B82F6' },
      purple: { bg: 'rgba(147, 51, 234, 0.2)', border: 'rgba(147, 51, 234, 0.3)', text: '#9333EA' },
      gray: { bg: 'rgba(107, 114, 128, 0.2)', border: 'rgba(107, 114, 128, 0.3)', text: '#6B7280' },
      orange: { bg: 'rgba(251, 146, 60, 0.2)', border: 'rgba(251, 146, 60, 0.3)', text: '#FB923C' }
    };
    return colorMap[color] || colorMap.yellow;
  };

  // Use the actual counts with proper fallbacks
  const actualPreEvalCompleted = preEvaluationCompleted || preEvaluationCount || 0;
  const actualPreEvalTotal = preEvaluationTotal || totalPlayers || 0;
  const actualCoachEvalCompleted = evaluationsCompleted || coachEvaluationCount || 0;
  const actualCoachEvalTotal = evaluationsTotal || totalCoachEvaluations || attendedCount || 0;
  
  // Debug logging
  console.log('🔍 ShadowEventCard Debug:', {
    status,
    skipAttendanceCheck,
    attendanceCompleted,
    isPreEvaluationEnabled,
    actualPreEvalTotal,
    actualPreEvalCompleted,
    actualCoachEvalTotal,
    actualCoachEvalCompleted,
    evaluationsCompleted,
    evaluationsTotal,
    attendedCount,
    coachEvaluationDraftCount,
    invitedCount
  });

  // Calculate progress percentages from raw counts
  // For pre-evaluations, always calculate from counts
  const preEvalProgress = actualPreEvalTotal > 0 
    ? Math.round((actualPreEvalCompleted / actualPreEvalTotal) * 100) 
    : 0;
  
  // For coach evaluations, use evaluations of those who attended
  const coachEvalProgress = actualCoachEvalTotal > 0 
    ? Math.round((actualCoachEvalCompleted / actualCoachEvalTotal) * 100) 
    : 0;

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    try {
      // Create shadow DOM


      

      // Check if shadowRoot already exists
      let shadowRoot = container.shadowRoot;
      
      if (!shadowRoot) {
        shadowRoot = container.attachShadow({ mode: 'open' });
      }
      
      shadowRootRef.current = shadowRoot;

      const config = sizeConfig[size];
      const eventConfig = eventTypeConfig[type];

      // Status-specific content logic
      let statusContent = '';
      let progressContent = '';

      // Status content removed - no longer showing status labels
      statusContent = '';
      
      // Build stat rings HTML
      let statRingsHTML = '';
      
      // Check if attendance needs to be set for past events
      // Skip attendance check if explicitly told to (e.g., in evaluation mode)
      const needsAttendance = !skipAttendanceCheck && 
                             !attendanceCompleted &&
                             (status === 'in-progress' || status === 'complete') && 
                             invitedCount > 0;
      
      // Show rings based on status and available data
      console.log('🔍 Ring decision logic:', {
        needsAttendance,
        status,
        published,
        isPreEvaluationEnabled,
        actualPreEvalTotal,
        actualCoachEvalTotal,
        condition1: status === 'upcoming' && published && isPreEvaluationEnabled && actualPreEvalTotal > 0,
        condition2: (status === 'in-progress' || status === 'complete') && (actualCoachEvalTotal > 0 || (isPreEvaluationEnabled && actualPreEvalTotal > 0))
      });
      
      // Show different rings based on published status and event type
      if (status === 'upcoming') {
        if (!published && invitedCount > 0) {
          // For unpublished events, show invited player count
          statRingsHTML = `
            <div class="stat-rings-container single">
              <div class="stat-ring-wrapper">
                <div class="stat-ring draft">
                  <svg viewBox="0 0 36 36" class="circular-chart">
                    <path class="circle-bg"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                    <text x="18" y="18" class="players-count">${invitedCount}</text>
                    <text x="18" y="24" class="players-label">players</text>
                  </svg>
                </div>
                <div class="stat-ring-label">Invited</div>
              </div>
            </div>
          `;
        } else if (published && isPreEvaluationEnabled && actualPreEvalTotal > 0) {
          // Single yellow ring for pre-evaluations only (published events)
          statRingsHTML = `
            <div class="stat-rings-container single">
              <div class="stat-ring-wrapper">
                <div class="stat-ring">
                  <svg viewBox="0 0 36 36" class="circular-chart">
                    <path class="circle-bg"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                    <path class="circle pre-eval-yellow"
                      stroke-dasharray="${preEvalProgress}, 100"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                    <text x="18" y="20.35" class="percentage">${preEvalProgress}%</text>
                  </svg>
                </div>
                <div class="stat-ring-label">${actualPreEvalCompleted}/${actualPreEvalTotal}</div>
              </div>
            </div>
          `;
        }
      } else if (needsAttendance) {
        // Show gray ring with "Set Attendance" message
        statRingsHTML = `
          <div class="stat-rings-container single">
            <div class="stat-ring-wrapper">
              <div class="stat-ring attendance-needed">
                <svg viewBox="0 0 36 36" class="circular-chart">
                  <path class="circle-bg gray"
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  />
                  <text x="18" y="20.35" class="set-attendance-text">SET</text>
                </svg>
              </div>
              <div class="stat-ring-label attendance-label">Set Attendance</div>
              <div class="stat-ring-sublabel">${attendedCount}/${invitedCount} marked</div>
            </div>
          </div>
        `;
      } else if ((status === 'in-progress' || status === 'complete')) {
        // Show rings for in-progress or complete events
        // For evaluation page, we always want to show the rings even if no attendees marked yet
        const showPreEval = isPreEvaluationEnabled && actualPreEvalTotal > 0;
        const showCoachEval = skipAttendanceCheck || actualCoachEvalTotal > 0;
        
        // Special case: On evaluation page but no attendees marked
        if (skipAttendanceCheck && actualCoachEvalTotal === 0 && evaluationsTotal === 0) {
          statRingsHTML = `
            <div class="stat-rings-container single">
              <div class="stat-ring-wrapper">
                <div class="stat-ring attendance-needed">
                  <svg viewBox="0 0 36 36" class="circular-chart">
                    <path class="circle-bg gray"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                    <text x="18" y="20.35" class="set-attendance-text">0</text>
                  </svg>
                </div>
                <div class="stat-ring-label">No players attended</div>
                <div class="stat-ring-sublabel">Set attendance first</div>
              </div>
            </div>
          `;
        } else if (showPreEval && showCoachEval) {
          // Both rings
          statRingsHTML = `
            <div class="stat-rings-container dual">
              <div class="stat-ring-wrapper">
                <div class="stat-ring">
                  <svg viewBox="0 0 36 36" class="circular-chart">
                    <path class="circle-bg"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                    <path class="circle pre-eval-yellow"
                      stroke-dasharray="${preEvalProgress}, 100"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                    <text x="18" y="20.35" class="percentage">${preEvalProgress}%</text>
                  </svg>
                </div>
                <div class="stat-ring-label">${actualPreEvalCompleted}/${actualPreEvalTotal}</div>
              </div>
              <div class="stat-ring-wrapper">
                <div class="stat-ring">
                  <svg viewBox="0 0 36 36" class="circular-chart">
                    <path class="circle-bg"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                    <path class="circle coach-eval-green"
                      stroke-dasharray="${coachEvalProgress}, 100"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                    <text x="18" y="20.35" class="percentage">${coachEvalProgress}%</text>
                  </svg>
                </div>
                <div class="stat-ring-label">${actualCoachEvalCompleted}/${actualCoachEvalTotal}</div>
              </div>
            </div>
          `;
        } else if (showCoachEval) {
          // Single green ring for coach eval only
          const hasDrafts = coachEvaluationDraftCount > 0;
          statRingsHTML = `
            <div class="stat-rings-container single">
              <div class="stat-ring-wrapper">
                <div class="stat-ring${hasDrafts ? ' has-drafts' : ''}">
                  <svg viewBox="0 0 36 36" class="circular-chart">
                    <path class="circle-bg"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                    <path class="circle coach-eval-green${hasDrafts ? ' with-drafts' : ''}"
                      stroke-dasharray="${coachEvalProgress}, 100"
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    />
                    <text x="18" y="20.35" class="percentage">${coachEvalProgress}%</text>
                  </svg>
                </div>
                <div class="stat-ring-label">${actualCoachEvalCompleted}/${actualCoachEvalTotal}</div>
                ${hasDrafts ? `<div class="stat-ring-sublabel">${coachEvaluationDraftCount} draft${coachEvaluationDraftCount > 1 ? 's' : ''}</div>` : ''}
              </div>
            </div>
          `;
        }
      }
      
      console.log('🔍 statRingsHTML generated:', statRingsHTML);

      // SMS button for upcoming published events
      const smsButton = (status === 'upcoming' && published && (onSMSClick || onSmsReminder)) ? `
        <button class="sms-button" data-action="sms" title="Send SMS reminder">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
          </svg>
          <span>SMS</span>
        </button>
      ` : '';
      
      // Draft status indicator with configurable position, text, and color
      const isInline = draftBadgePosition === 'inline';
      const badgeColors = getBadgeColorStyles(draftBadgeColor);
      const draftIndicator = (!published) ? `
        <div class="draft-indicator ${isInline ? 'inline' : ''}" 
             style="${isInline ? '' : getBadgePositionStyles(draftBadgePosition)}; 
                    background: ${badgeColors.bg}; 
                    border-color: ${badgeColors.border}; 
                    color: ${badgeColors.text};">
          <span>${draftBadgeText}</span>
        </div>
      ` : '';

      const styles = `
        :host {
          display: block;
          width: 100%;
          font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .event-card {
          background: #1f1f1f;
          border: 1px solid rgba(75, 75, 75, 0.3);
          border-radius: 12px;
          padding: ${config.padding};
          cursor: ${onClick ? 'pointer' : 'default'};
          transition: all 0.3s ease;
          position: relative;
          width: 100%;
          box-sizing: border-box;
        }

        .event-card:hover {
          ${onClick ? `
            border-color: rgba(107, 0, 219, 0.5);
            box-shadow: 0 4px 20px rgba(107, 0, 219, 0.1);
            transform: translateY(-1px);
          ` : ''}
        }

        .event-card:focus {
          outline: none;
          border-color: #6B00DB;
          box-shadow: 0 0 0 2px rgba(107, 0, 219, 0.2);
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 12px;
        }

        .event-type {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #ffffff;
          font-weight: 600;
          font-size: ${config.subtitleSize};
        }

        .event-emoji {
          font-size: 18px;
        }

        .event-title {
          color: #ffffff;
          font-size: ${config.titleSize};
          font-weight: 700;
          margin: 0 0 4px 0;
          line-height: 1.2;
        }

        .event-subtitle {
          color: #B0B0B0;
          font-size: ${config.subtitleSize};
          margin: 0 0 8px 0;
          font-weight: 400;
        }

        .event-details {
          display: flex;
          flex-direction: column;
          gap: 4px;
          margin-bottom: 16px;
        }

        .detail-row {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #B0B0B0;
          font-size: ${config.subtitleSize};
        }

        .detail-icon {
          width: 16px;
          opacity: 0.7;
        }



        .sms-button {
          position: absolute;
          bottom: 12px;
          right: 12px;
          background: rgba(179, 102, 255, 0.15);
          border: 1px solid rgba(179, 102, 255, 0.3);
          border-radius: 20px;
          padding: 6px 14px;
          color: #B366FF;
          font-size: 11px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          display: flex;
          align-items: center;
          gap: 6px;
          z-index: 5;
        }

        .sms-button svg {
          width: 14px;
          height: 14px;
        }

        .sms-button:hover {
          background: rgba(179, 102, 255, 0.25);
          border-color: rgba(179, 102, 255, 0.5);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(179, 102, 255, 0.3);
        }
        
        .draft-indicator {
          position: absolute;
          border: 1px solid;
          border-radius: 12px;
          padding: 4px 10px;
          font-size: 10px;
          font-weight: 700;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          z-index: 15;
        }

        .draft-indicator.inline {
          position: static;
          display: inline-block;
          margin-left: 12px;
          vertical-align: middle;
        }

        /* Stat Rings Styles */
        .stat-rings-container {
          position: absolute;
          top: 12px;
          right: 12px;
          z-index: 10;
          display: flex;
          gap: 4px;
        }

        .stat-rings-container.single {
          gap: 0;
        }

        .stat-ring-wrapper {
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .stat-ring {
          width: 40px;
          height: 40px;
          position: relative;
        }

        .circular-chart {
          display: block;
          margin: 0;
          max-width: 100%;
          max-height: 100%;
        }

        .circle-bg {
          fill: none;
          stroke: rgba(156, 163, 175, 0.2);
          stroke-width: 3;
        }

        .circle {
          fill: none;
          stroke-width: 3;
          stroke-linecap: round;
          animation: progress 1s ease-out forwards;
        }

        .circle.pre-eval-yellow {
          stroke: #FBB024;
        }

        .circle.coach-eval-green {
          stroke: #10B981;
        }

        .percentage {
          fill: #fff;
          font-size: 0.4em;
          text-anchor: middle;
          font-weight: 700;
        }

        .stat-ring-label {
          text-align: center;
          font-size: 9px;
          color: #9CA3AF;
          margin-top: 2px;
          font-weight: 600;
          white-space: nowrap;
        }

        @keyframes progress {
          0% {
            stroke-dasharray: 0 100;
          }
        }
        
        /* Attendance needed state */
        .stat-ring.attendance-needed .circle-bg.gray {
          stroke: rgba(156, 163, 175, 0.4);
        }
        
        .stat-ring-label.attendance-label {
          color: #F59E0B;
          font-weight: 700;
        }
        
        .stat-ring-sublabel {
          text-align: center;
          font-size: 8px;
          color: #6B7280;
          margin-top: 1px;
        }
        
        .set-attendance-text {
          fill: #F59E0B;
          font-size: 0.35em;
          text-anchor: middle;
          font-weight: 700;
        }
        
        /* Draft indicator for coach evaluations */
        .stat-ring.has-drafts .circle.coach-eval-green.with-drafts {
          stroke-dasharray: 5 2;
          animation: none;
        }

        /* Draft event invited players ring */
        .stat-ring.draft .circle-bg {
          stroke: rgba(247, 182, 19, 0.3);
        }

        .stat-ring.draft .players-count {
          fill: #F7B613;
          font-size: 0.5em;
          text-anchor: middle;
          font-weight: 700;
        }

        .stat-ring.draft .players-label {
          fill: #F7B613;
          font-size: 0.25em;
          text-anchor: middle;
          font-weight: 500;
          opacity: 0.8;
        }

        /* Debug button styles */
        .debug-button {
          position: absolute;
          bottom: 8px;
          right: 8px;
          background: rgba(220, 38, 38, 0.9);
          border: 1px solid #DC2626;
          border-radius: 6px;
          padding: 4px 8px;
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: 14px;
          color: #FFFFFF;
          font-family: 'Poppins', sans-serif;
          font-weight: 600;
          z-index: 10;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .debug-button:hover {
          background: #DC2626;
          transform: scale(1.05);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

      `;

      // Debug button for debug mode
      const debugButton = isDebugMode() && (debugData || id) ? `
        <button class="debug-button" data-action="debug" title="Show debug information">
          🐛
        </button>
      ` : '';

      const cardHTML = `
        <div class="event-card" tabindex="${onClick ? '0' : '-1'}" role="${onClick ? 'button' : 'article'}">
          ${!isInline ? draftIndicator : ''}
          ${smsButton}
          ${debugButton}
          ${statRingsHTML}
          
          <div class="card-header">
            <div class="event-type">
              <span class="event-emoji">${eventConfig.emoji}</span>
              <span>${eventConfig.name}</span>
            </div>
          </div>

          <div class="event-info">
            <h3 class="event-title">
              ${title}
              ${isInline ? draftIndicator : ''}
            </h3>
            ${subtitle ? `<p class="event-subtitle">${subtitle}</p>` : ''}
            
            <div class="event-details">
              <div class="detail-row">
                <span class="detail-icon">📅</span>
                <span>${date}</span>
              </div>
              ${time ? `
                <div class="detail-row">
                  <span class="detail-icon">🕐</span>
                  <span>${time}</span>
                </div>
              ` : ''}
              ${location ? `
                <div class="detail-row">
                  <span class="detail-icon">📍</span>
                  <span>${location}</span>
                </div>
              ` : ''}
            </div>
          </div>

          ${statusContent}
        </div>
      `;

      // Apply styles and HTML
      shadowRoot.innerHTML = `<style>${styles}</style>${cardHTML}`;

      // Event listeners
      const card = shadowRoot.querySelector('.event-card');
      const smsBtn = shadowRoot.querySelector('[data-action="sms"]');
      const debugBtn = shadowRoot.querySelector('[data-action="debug"]');

      if (card && onClick) {
        const handleClick = (e: Event) => {
          e.preventDefault();
          onClick();
        };

        const handleKeyDown = (e: KeyboardEvent) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onClick();
          }
        };

        card.addEventListener('click', handleClick);
        card.addEventListener('keydown', handleKeyDown);
      }

      if (smsBtn && (onSMSClick || onSmsReminder)) {
        smsBtn.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          if (onSmsReminder) {
            onSmsReminder();
          } else if (onSMSClick) {
            onSMSClick();
          }
        });
      }

      if (debugBtn) {
        debugBtn.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          setShowDebugModal(true);
        });
      }

      return () => {
        if (shadowRootRef.current) {
          shadowRootRef.current.innerHTML = '';
        }
      };
    } catch (error) {
      console.error('Failed to create shadow DOM for ShadowEventCard:', error);
      
      // Fallback content
      container.innerHTML = `
        <div style="
          background: #1f1f1f; 
          border: 1px solid rgba(75, 75, 75, 0.3); 
          border-radius: 12px; 
          padding: 16px; 
          color: white;
        ">
          <h3>${title}</h3>
          <p>Event details unavailable</p>
        </div>
      `;
    }
  }, [
    id, type, status, title, subtitle, date, time, location, 
    published, preEvaluationTotal, preEvaluationCompleted,
    isPreEvaluationEnabled, evaluationsCompleted, evaluationsTotal,
    size, onClick, onSMSClick, onSmsReminder,
    preEvalProgress, coachEvalProgress, actualPreEvalCompleted, actualPreEvalTotal,
    actualCoachEvalCompleted, actualCoachEvalTotal, invitedCount, attendedCount,
    coachEvaluationDraftCount, draftBadgePosition, draftBadgeText, draftBadgeColor
  ]);

  return (
    <>
      <div
        ref={containerRef}
        style={{ display: 'block', width: '100%' }}
      />
      <ShadowDebugModal
        isOpen={showDebugModal}
        onClose={() => setShowDebugModal(false)}
        title={`🐛 Event Debug - ${title}`}
        data={{
          '🎯 Component Props (ShadowEventCard)': {
            id,
            type,
            title,
            subtitle,
            date,
            time,
            location,
            status,
            published,
            size,
            draftBadgePosition,
            draftBadgeText,
            draftBadgeColor,
            onClick: onClick ? 'function provided' : 'not provided',
            onSMSClick: onSMSClick ? 'function provided' : 'not provided',
            onSmsReminder: onSmsReminder ? 'function provided' : 'not provided',
            onDelete: onDelete ? 'function provided' : 'not provided'
          },
          '📊 Pre-Evaluation Data': {
            isPreEvaluationEnabled,
            preEvaluationCount,
            totalPlayers,
            preEvaluationTotal,
            preEvaluationCompleted,
            actualPreEvalTotal,
            actualPreEvalCompleted,
            preEvalProgress
          },
          '👨‍🏫 Coach Evaluation Data': {
            coachEvaluationCount,
            totalCoachEvaluations,
            evaluationsCompleted,
            evaluationsTotal,
            coachEvaluationDraftCount,
            actualCoachEvalTotal,
            actualCoachEvalCompleted,
            coachEvalProgress
          },
          '✅ Attendance Data': {
            invitedCount,
            attendedCount,
            attendanceCompleted,
            skipAttendanceCheck
          },
          '📋 Additional Debug Data (from parent)': debugData || {},
          '🔧 Component State': {
            showDebugModal
          },
          '🔍 Delete Button Debug': {
            onDeleteProvided: !!onDelete,
            idProvided: !!id,
            willShowDeleteButton: !!(onDelete && id)
          }
        }}
        onDelete={onDelete && id ? () => {
          console.log('🗑️ DELETE BUTTON CLICKED in ShadowEventCard');
          setShowDeleteConfirm(true);
        } : undefined}
      />
      {showDeleteConfirm && onDelete && id && (
        <div 
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 10000,
            padding: '20px'
          }}
          onClick={() => setShowDeleteConfirm(false)}
        >
          <div 
            style={{
              backgroundColor: '#1f1f1f',
              border: '2px solid #DC2626',
              borderRadius: '12px',
              padding: '24px',
              maxWidth: '500px',
              width: '100%',
              position: 'relative'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <h3 style={{ 
              color: '#DC2626', 
              marginBottom: '16px', 
              fontFamily: 'Poppins, sans-serif',
              fontSize: '20px',
              fontWeight: '600'
            }}>
              ⚠️ Confirm Event Deletion
            </h3>
            <p style={{
              color: '#fff',
              marginBottom: '12px',
              fontSize: '16px',
              fontWeight: '500'
            }}>
              Are you sure you want to delete the event "{title}"?
            </p>
            <div style={{
              backgroundColor: 'rgba(220, 38, 38, 0.1)',
              border: '1px solid rgba(220, 38, 38, 0.3)',
              borderRadius: '8px',
              padding: '12px',
              marginBottom: '20px'
            }}>
              <p style={{
                color: '#DC2626',
                margin: '0 0 8px 0',
                fontWeight: '600'
              }}>
                This will permanently delete:
              </p>
              <ul style={{
                color: '#DC2626',
                margin: '0',
                paddingLeft: '20px',
                fontSize: '14px'
              }}>
                <li>The event record</li>
                <li>All participant associations</li>
                <li>All evaluations for this event</li>
                <li>All pre-evaluations</li>
                <li>All attendance records</li>
              </ul>
              <p style={{
                color: '#DC2626',
                margin: '12px 0 0 0',
                fontWeight: '600',
                fontSize: '14px'
              }}>
                This action cannot be undone!
              </p>
            </div>
            <div style={{ display: 'flex', gap: '12px' }}>
              <button
                onClick={() => setShowDeleteConfirm(false)}
                style={{
                  flex: 1,
                  padding: '12px 20px',
                  backgroundColor: '#2a2a2a',
                  border: '1px solid #444',
                  borderRadius: '8px',
                  color: '#fff',
                  fontFamily: 'Poppins, sans-serif',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#333';
                  e.currentTarget.style.borderColor = '#555';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#2a2a2a';
                  e.currentTarget.style.borderColor = '#444';
                }}
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  setShowDeleteConfirm(false);
                  onDelete(id);
                }}
                style={{
                  flex: 1,
                  padding: '12px 20px',
                  backgroundColor: '#DC2626',
                  border: '1px solid #DC2626',
                  borderRadius: '8px',
                  color: '#fff',
                  fontFamily: 'Poppins, sans-serif',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '8px'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = '#B91C1C';
                  e.currentTarget.style.transform = 'translateY(-1px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = '#DC2626';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                <span style={{ fontSize: '16px' }}>🗑️</span>
                Delete Permanently
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ShadowEventCard;