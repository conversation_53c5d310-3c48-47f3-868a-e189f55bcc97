// ABOUTME: Storybook stories for the enhanced event card component
// Demonstrates all badge positioning options and component variations

import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { ShadowEventCardEnhanced } from './ShadowEventCardEnhanced';

const meta: Meta<typeof ShadowEventCardEnhanced> = {
  title: 'Design System/Molecules/Cards/ShadowEventCardEnhanced',
  component: ShadowEventCardEnhanced,
  parameters: {
    layout: 'padded',
    backgrounds: {
      default: 'dark',
      values: [
        { name: 'dark', value: '#000000' },
        { name: 'light', value: '#f5f5f5' }
      ]
    }
  },
  argTypes: {
    draftBadgePosition: {
      control: { type: 'select' },
      options: ['top-left', 'top-right', 'bottom-left', 'bottom-right', 'inline'],
      description: 'Position of the draft badge'
    },
    type: {
      control: { type: 'select' },
      options: ['training', 'match', 'session', 'event']
    },
    status: {
      control: { type: 'select' },
      options: ['upcoming', 'in-progress', 'complete']
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large']
    }
  }
};

export default meta;
type Story = StoryObj<typeof ShadowEventCardEnhanced>;

// Base draft event data
const draftEventData = {
  type: 'training' as const,
  status: 'upcoming' as const,
  title: 'Team Training Session',
  subtitle: 'Weekly practice',
  date: 'Tomorrow, March 15',
  time: '4:00 PM',
  location: 'Main Field',
  published: false,
  invitedCount: 6,
  size: 'medium' as const
};

// Draft Badge Positions
export const DraftBadgeBottomRight: Story = {
  name: '📐 Draft Badge - Bottom Right',
  args: {
    ...draftEventData,
    draftBadgePosition: 'bottom-right'
  }
};

export const DraftBadgeBottomLeft: Story = {
  name: '📐 Draft Badge - Bottom Left',
  args: {
    ...draftEventData,
    draftBadgePosition: 'bottom-left'
  }
};

export const DraftBadgeTopRight: Story = {
  name: '📐 Draft Badge - Top Right',
  args: {
    ...draftEventData,
    draftBadgePosition: 'top-right'
  }
};

export const DraftBadgeTopLeft: Story = {
  name: '📐 Draft Badge - Top Left',
  args: {
    ...draftEventData,
    draftBadgePosition: 'top-left'
  }
};

export const DraftBadgeInline: Story = {
  name: '📐 Draft Badge - Inline with Title',
  args: {
    ...draftEventData,
    draftBadgePosition: 'inline'
  }
};

// Side by side comparison
export const AllPositionsComparison: Story = {
  name: '🎯 All Badge Positions',
  render: () => (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))', gap: '24px', padding: '20px' }}>
      <div>
        <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Bottom Right (Default)</h3>
        <ShadowEventCardEnhanced {...draftEventData} draftBadgePosition="bottom-right" />
      </div>
      <div>
        <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Bottom Left</h3>
        <ShadowEventCardEnhanced {...draftEventData} draftBadgePosition="bottom-left" />
      </div>
      <div>
        <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Top Right</h3>
        <ShadowEventCardEnhanced {...draftEventData} draftBadgePosition="top-right" />
      </div>
      <div>
        <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Top Left</h3>
        <ShadowEventCardEnhanced {...draftEventData} draftBadgePosition="top-left" />
      </div>
      <div>
        <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Inline with Title</h3>
        <ShadowEventCardEnhanced {...draftEventData} draftBadgePosition="inline" />
      </div>
    </div>
  )
};

// Real world scenarios
export const DraftWithPlayerCircle: Story = {
  name: '💼 Draft with Player Circle (Bottom Right)',
  args: {
    type: 'match',
    status: 'upcoming',
    title: 'Championship Final',
    date: 'Saturday, March 18',
    time: '2:00 PM',
    location: 'Stadium A',
    published: false,
    invitedCount: 11,
    draftBadgePosition: 'bottom-right',
    size: 'large'
  }
};

export const PublishedWithPreEval: Story = {
  name: '✅ Published with Pre-Evaluations',
  args: {
    type: 'training',
    status: 'upcoming',
    title: 'Pre-Season Training',
    date: 'Monday, March 20',
    time: '5:00 PM',
    location: 'Training Ground',
    published: true,
    isPreEvaluationEnabled: true,
    preEvaluationCompleted: 3,
    preEvaluationTotal: 10,
    totalPlayers: 10,
    onSmsReminder: () => console.log('SMS reminder sent'),
    size: 'medium'
  }
};

export const InProgressWithEvaluations: Story = {
  name: '🏃 In Progress with Evaluations',
  args: {
    type: 'match',
    status: 'in-progress',
    title: 'League Match vs Tigers',
    date: 'Today',
    time: '3:00 PM',
    location: 'Home Field',
    published: true,
    evaluationsCompleted: 7,
    evaluationsTotal: 11,
    attendedCount: 11,
    attendanceCompleted: true,
    size: 'medium'
  }
};

export const CompleteWithDrafts: Story = {
  name: '✨ Complete with Draft Evaluations',
  args: {
    type: 'session',
    status: 'complete',
    title: 'Technical Skills Workshop',
    date: 'Yesterday',
    time: '4:30 PM',
    location: 'Indoor Court',
    published: true,
    evaluationsCompleted: 8,
    evaluationsTotal: 12,
    coachEvaluationDraftCount: 4,
    attendedCount: 12,
    attendanceCompleted: true,
    size: 'medium'
  }
};

// Interactive playground
export const Playground: Story = {
  name: '🎮 Interactive Playground',
  args: {
    type: 'training',
    status: 'upcoming',
    title: 'Team Practice',
    subtitle: 'Focus on defensive drills',
    date: 'Tomorrow',
    time: '6:00 PM',
    location: 'Training Field B',
    published: false,
    invitedCount: 15,
    draftBadgePosition: 'bottom-right',
    size: 'medium',
    onClick: () => console.log('Card clicked'),
    onSmsReminder: () => console.log('SMS clicked')
  }
};