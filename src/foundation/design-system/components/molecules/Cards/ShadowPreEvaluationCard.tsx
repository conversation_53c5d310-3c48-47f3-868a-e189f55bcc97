// ABOUTME: Shadow DOM pre-evaluation card component for player/coach evaluation prompts
// Displays urgent call-to-action for completing pre-training evaluations

import React, { useEffect, useRef } from 'react';
import { createBaseStyles, injectStyles } from '../../../utils/shadowDom';
import { theme } from '../../../tokens';
export interface ShadowPreEvaluationCardProps {
  eventId: string;
  eventName: string;
  eventStartTime: string;
  teamName?: string;
  expiresAt: string;
  className?: string;
  onStartEvaluation: () => void;
  urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
  completedCount?: number;
  totalCount?: number;
}

export const ShadowPreEvaluationCard: React.FC<ShadowPreEvaluationCardProps> = ({
  eventId,
  eventName,
  eventStartTime,
  teamName,
  expiresAt,
  className = '',
  onStartEvaluation,
  urgencyLevel = 'low',
  completedCount = 0,
  totalCount = 0
}) => {
  const shadowHostRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!shadowHostRef.current || shadowHostRef.current.shadowRoot) return;

    const shadowRoot = shadowHostRef.current.attachShadow({ mode: 'open' });
    
    const urgencyColors = {
      low: theme.colors.primary.teal,
      medium: '#F7B613',
      high: '#FF9500',
      critical: theme.colors.semantic.error
    };

    const urgencyAnimation = {
      low: 'none',
      medium: 'pulse-border 3s ease-in-out infinite',
      high: 'pulse-border 2s ease-in-out infinite',
      critical: 'pulse-border 1s ease-in-out infinite'
    };
    
    const styles = `
      <style>
      ${createBaseStyles()}
      
      :host {
        display: block;
        width: 100%;
      }

      .pre-eval-card {
        background: ${theme.colors.dark.surface};
        border-radius: ${theme.borderRadius.xl};
        overflow: hidden;
        transition: all 0.3s ease;
        font-family: ${theme.typography.fontFamily.body};
        border: 2px solid ${urgencyColors[urgencyLevel]};
        position: relative;
        animation: ${urgencyAnimation[urgencyLevel]};
      }

      @keyframes pulse-border {
        0%, 100% {
          opacity: 0.8;
          transform: scale(1);
        }
        50% {
          opacity: 1;
          transform: scale(1.005);
        }
      }

      .card-header {
        padding: 1.25rem 1.25rem 0.75rem;
        display: flex;
        align-items: flex-start;
        gap: 1rem;
      }

      .icon-wrapper {
        width: 48px;
        height: 48px;
        background: ${theme.colors.primary.teal};
        border-radius: ${theme.borderRadius.md};
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        ${urgencyLevel === 'critical' ? 'animation: icon-pulse 1s ease-in-out infinite;' : ''}
      }

      @keyframes icon-pulse {
        0%, 100% {
          transform: scale(1);
          box-shadow: 0 0 0 0 rgba(107, 0, 219, 0.5);
        }
        50% {
          transform: scale(1.05);
          box-shadow: 0 0 0 8px rgba(107, 0, 219, 0);
        }
      }

      .icon-wrapper svg {
        width: 24px;
        height: 24px;
        color: white;
      }

      .header-content {
        flex: 1;
        min-width: 0;
      }

      .badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        background: rgba(107, 0, 219, 0.2);
        color: #B794F6;
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.25rem 0.625rem;
        border-radius: 9999px;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 0.5rem;
        font-family: ${theme.typography.fontFamily.heading};
      }

      .title {
        font-size: 1.125rem;
        font-weight: 700;
        color: #FFFFFF;
        margin-bottom: 0.25rem;
        line-height: 1.2;
        font-family: ${theme.typography.fontFamily.heading};
      }

      .subtitle {
        font-size: 0.875rem;
        color: #B3B3B3;
      }

      .description {
        padding: 0 1.25rem;
        font-size: 0.938rem;
        color: #FFFFFF;
        line-height: 1.4;
        margin-bottom: 1rem;
      }

      .completion-bar {
        padding: 0 1.25rem;
        margin-bottom: 1rem;
      }

      .completion-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
      }

      .completion-label {
        font-size: 0.875rem;
        color: #B3B3B3;
      }

      .completion-count {
        font-size: 0.875rem;
        font-weight: 600;
        color: #FFFFFF;
      }

      .progress-bar {
        width: 100%;
        height: 8px;
        background: ${theme.colors.dark.surfaceLight};
        border-radius: 4px;
        overflow: hidden;
      }

      .progress-fill {
        height: 100%;
        background: ${theme.colors.primary.teal};
        transition: width 0.3s ease;
      }

      .button-wrapper {
        padding: 0 1.25rem 1.25rem;
      }

      .button {
        width: 100%;
        padding: 0.875rem 1.5rem;
        border-radius: ${theme.borderRadius.md};
        font-weight: 600;
        font-size: 1rem;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        font-family: ${theme.typography.fontFamily.heading};
        color: white;
        background: ${theme.colors.primary.teal};
        animation: button-flash 1.5s ease-in-out infinite;
        position: relative;
        overflow: hidden;
      }

      @keyframes button-flash {
        0%, 100% {
          opacity: 1;
          transform: scale(1);
        }
        50% {
          opacity: 0.85;
          transform: scale(1.02);
        }
      }

      .button::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
        transform: translate(-50%, -50%) scale(0);
        transition: transform 0.5s ease;
      }

      .button:hover::before {
        transform: translate(-50%, -50%) scale(2);
      }

      .button:hover {
        background: ${theme.colors.primary.purple};
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }

      .button:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      }

      @media (max-width: 640px) {
        .card-header {
          padding: 1rem 1rem 0.625rem;
        }

        .icon-wrapper {
          width: 40px;
          height: 40px;
        }

        .icon-wrapper svg {
          width: 20px;
          height: 20px;
        }

        .title {
          font-size: 1rem;
        }

        .description {
          font-size: 0.875rem;
        }
      }
    `;
    
    injectStyles(shadowRoot, styles);
    
    const card = document.createElement('div');
    card.className = 'pre-eval-card';
    
    // Build the card structure
    card.innerHTML = `
      <div class="card-header">
        <div class="icon-wrapper">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
        </div>
        <div class="header-content">
          <div class="badge">
            <svg width="12" height="12" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
            </svg>
            <span>Pre-Training Required</span>
          </div>
          <h3 class="title">Complete Your Pre-Evaluation</h3>
          <p class="subtitle">${teamName || 'Team'} • ${eventName}</p>
        </div>
      </div>
      
      <div class="description">
        Quick check-in before training starts. Rate your confidence and readiness - takes less than 2 minutes!
      </div>
      
      ${totalCount > 0 ? `
        <div class="completion-bar">
          <div class="completion-header">
            <span class="completion-label">Team Completion</span>
            <span class="completion-count">${completedCount}/${totalCount} completed</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" style="width: ${(completedCount / totalCount) * 100}%"></div>
          </div>
        </div>
      ` : ''}
      
      <div class="button-wrapper">
        <button class="button">Complete Evaluation →</button>
      </div>
    `;
    
    // Add event listener
    const button = card.querySelector('.button');
    if (button) {
      button.addEventListener('click', onStartEvaluation);
    }
    
    shadowRoot.appendChild(card);
  }, [eventName, teamName, onStartEvaluation, urgencyLevel, completedCount, totalCount]);

  return <div ref={shadowHostRef} className={className} />;
};