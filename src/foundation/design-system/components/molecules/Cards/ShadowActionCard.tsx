// ABOUTME: Shadow DOM action card component with icon, content and action button
// Provides a card with icon, title, description and actionable button

import React, { useEffect, useRef } from 'react';
type ButtonColor = 'teal' | 'purple' | 'gold' | 'green' | 'blue' | 'red' | 'secondary';

export interface ShadowActionCardProps {
  icon: string; // Icon name as string instead of Lucide component
  iconColor: 'teal' | 'purple' | 'gold' | 'green' | 'blue' | 'red';
  title: string;
  description: string;
  buttonText: string;
  buttonColor?: ButtonColor;
  onButtonClick?: () => void;
  buttonDisabled?: boolean;
  buttonFlash?: boolean;
  className?: string;
  label?: string;
}

// Common icon paths
const iconPaths: Record<string, string> = {
  Target: 'M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0-6 0 M12 12m-8 0a8 8 0 1 0 16 0a8 8 0 1 0-16 0 M12 2v2 M12 20v2 M2 12h2 M20 12h2',
  Users: 'M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2 M9 7a4 4 0 1 0 0-8 4 4 0 0 0 0 8z M23 21v-2a4 4 0 0 0-3-3.87 M16 3.13a4 4 0 0 1 0 7.75',
  Award: 'M12 15m-7 0a7 7 0 1 0 14 0a7 7 0 1 0-14 0 M8.21 13.89L7 23l5-3 5 3-1.21-9.12',
  ClipboardCheck: 'M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2 M8 2h8a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1z M9 14l2 2 4-4',
  Circle: 'M12 12m-10 0a10 10 0 1 0 20 0a10 10 0 1 0-20 0',
  ChevronRight: 'M9 18l6-6-6-6',
  Zap: 'M13 2L3 14h9l-1 8 10-12h-9l1-8z',
  Activity: 'M22 12h-4l-3 9L9 3l-3 9H2',
  Settings: 'M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0-6 0 M12 1v6 M12 17v6 M4.22 4.22l4.24 4.24 M15.54 15.54l4.24 4.24 M1 12h6 M17 12h6 M4.22 19.78l4.24-4.24 M15.54 8.46l4.24-4.24',
  Plus: 'M12 5v14m-7-7h14'
};

const colorMap: Record<string, { bg: string; color: string; hover: string }> = {
  teal: {
    bg: `#1ABC9C33`, // 20% opacity
    color: '#1ABC9C',
    hover: '#15A085'
  },
  purple: {
    bg: `#6B00DB33`,
    color: '#6B00DB',
    hover: '#5900B8'
  },
  gold: {
    bg: `#F59E0B33`,
    color: '#F59E0B',
    hover: '#E5A200'
  },
  green: {
    bg: `#10B98133`,
    color: '#10B981',
    hover: '#009900'
  },
  blue: {
    bg: '#296DFF33',
    color: '#296DFF',
    hover: '#1555E5'
  },
  red: {
    bg: `#EF444433`,
    color: '#EF4444',
    hover: '#E54560'
  },
  secondary: {
    bg: '#1A1A1A',
    color: '#B3B3B3',
    hover: '#2A2A2A'
  }
};

export const ShadowActionCard: React.FC<ShadowActionCardProps> = ({ 
  icon,
  iconColor,
  title,
  description,
  buttonText,
  buttonColor = 'teal',
  onButtonClick,
  buttonDisabled = false,
  buttonFlash = false,
  className = '',
  label
}) => {
  const hostRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);

  useEffect(() => {
    if (!hostRef.current || shadowRef.current) return;

    // Create shadow root


    

    shadowRef.current = hostRef.current.attachShadow({ mode: 'open' });

    // Create styles
    const iconConfig = colorMap[iconColor];
    const buttonConfig = colorMap[buttonColor];
    
    const styles = `
      <style>
      :host {
        display: block;
        width: 100%;
      }

      .card {
        background: #0A0A0A;
        border: 1px solid #3A3A3A;
        border-radius: 1rem;
        padding: 1.5rem;
        transition: all 0.2s ease;
        font-family: 'Poppins', sans-serif;
        text-align: center;
        cursor: pointer;
        position: relative;
      }

      .label {
        position: absolute;
        top: 0.75rem;
        left: 1.5rem;
        font-size: 0.75rem;
        font-weight: 500;
        color: #B3B3B3;
        text-transform: uppercase;
        letter-spacing: 0.025em;
      }

      .card.has-label {
        padding-top: 2.5rem;
      }

      .card:hover {
        border-color: ${iconConfig.color}80;
        transform: translateY(-2px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      }

      .card.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .card.disabled:hover {
        transform: none;
        border-color: #3A3A3A;
      }

      /* Icon styles */
      .icon-wrapper {
        display: inline-flex;
        width: 64px;
        height: 64px;
        border-radius: 50%;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        background: ${iconConfig.bg};
      }

      .icon {
        width: 32px;
        height: 32px;
        color: ${iconConfig.color};
      }

      /* Title */
      .title {
        font-size: 1.25rem;
        font-weight: 700;
        color: #FFFFFF;
        margin-bottom: 0.5rem;
        line-height: 1.25;
      }

      /* Description */
      .description {
        font-size: 0.875rem;
        color: #B3B3B3;
        line-height: 1.625;
        margin-bottom: 1.25rem;
      }

      /* Button styles */
      .button {
        display: block;
        width: 100%;
        padding: 0.75rem 2rem;
        border-radius: 0.5rem;
        font-weight: 600;
        font-size: 1rem;
        text-transform: uppercase;
        letter-spacing: 0.025em;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        font-family: 'Poppins', sans-serif;
        background: ${buttonConfig.color};
        color: ${buttonColor === 'secondary' ? '#FFFFFF' : '#FFFFFF'};
      }

      /* Flash animation */
      @keyframes pulse {
        0%, 100% {
          opacity: 1;
        }
        50% {
          opacity: 0.75;
        }
      }

      .button-flash {
        animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
      }

      .button:hover:not(:disabled) {
        background: ${buttonConfig.hover};
        transform: translateY(-1px);
      }
      
      .button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none !important;
      }

      /* Responsive */
      @media (max-width: 640px) {
        .card {
          padding: 1.25rem;
        }

        .button {
          padding: 0.75rem 1.5rem;
          font-size: 0.875rem;
        }
      }
      
      ${className ? `.custom { ${className} }` : ''}
    </style>
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Cleanup
    return () => {
      if (shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, [iconColor, buttonColor, className]);

  useEffect(() => {
    if (!shadowRef.current) return;

    // Clear existing content except style
    const style = shadowRef.current.querySelector('style');
    shadowRef.current.innerHTML = '';
    if (style) shadowRef.current.appendChild(style);

    // Create card
    const card = document.createElement('div');
    card.className = `card ${buttonDisabled ? 'disabled' : ''} ${label ? 'has-label' : ''}`;
    if (className) card.classList.add('custom');

    // Add label if provided
    if (label) {
      const labelEl = document.createElement('div');
      labelEl.className = 'label';
      labelEl.textContent = label;
      card.appendChild(labelEl);
    }

    // Create icon
    const iconWrapper = document.createElement('div');
    iconWrapper.className = 'icon-wrapper';
    
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('class', 'icon');
    svg.setAttribute('fill', 'none');
    svg.setAttribute('stroke', 'currentColor');
    svg.setAttribute('stroke-width', '2');
    svg.setAttribute('stroke-linecap', 'round');
    svg.setAttribute('stroke-linejoin', 'round');
    svg.setAttribute('viewBox', '0 0 24 24');
    
    const pathData = iconPaths[icon] || iconPaths.Circle;
    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path.setAttribute('d', pathData);
    svg.appendChild(path);
    
    iconWrapper.appendChild(svg);
    card.appendChild(iconWrapper);

    // Create title
    const titleEl = document.createElement('h3');
    titleEl.className = 'title';
    titleEl.textContent = title;
    card.appendChild(titleEl);

    // Create description
    const descEl = document.createElement('p');
    descEl.className = 'description';
    descEl.textContent = description;
    card.appendChild(descEl);

    // Create button
    const button = document.createElement('button');
    button.className = `button ${buttonFlash ? 'button-flash' : ''}`;
    button.textContent = buttonText;
    button.disabled = buttonDisabled;
    card.appendChild(button);

    // Add event listeners
    if (onButtonClick && !buttonDisabled) {
      button.addEventListener('click', (e) => {
        e.stopPropagation();
        onButtonClick();
      });
      
      card.addEventListener('click', (e) => {
        if (e.target !== button) {
          onButtonClick();
        }
      });
    }

    shadowRef.current.appendChild(card);
  }, [icon, title, description, buttonText, buttonFlash, buttonDisabled, onButtonClick, label]);

  return <div ref={hostRef} />;
};