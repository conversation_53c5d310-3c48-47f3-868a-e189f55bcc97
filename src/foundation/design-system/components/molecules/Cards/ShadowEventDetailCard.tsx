// ABOUTME: Shadow DOM event detail card component for displaying comprehensive event information
// Shows event details with evaluation progress and statistics

import React, { useEffect, useRef } from 'react';
import { createBaseStyles, injectStyles } from '../../../utils/shadowDom';
import { theme } from '../../../tokens';
export interface EventDetailData {
  preEvaluationProgress?: number;
  coachEvaluationProgress?: number;
  attendeeCount?: number;
  playerCount?: number;
  isPast?: boolean;
}

export interface ShadowEventDetailCardProps {
  eventId: string;
  eventTitle: string;
  eventDate: string;
  eventData?: EventDetailData;
  onBack?: () => void;
  className?: string;
}

export const ShadowEventDetailCard: React.FC<ShadowEventDetailCardProps> = ({
  eventId,
  eventTitle,
  eventDate,
  eventData = {},
  onBack,
  className = ''
}) => {
  const shadowHostRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!shadowHostRef.current || shadowHostRef.current.shadowRoot) return;

    const shadowRoot = shadowHostRef.current.attachShadow({ mode: 'open' });
    
    const {
      preEvaluationProgress = 0,
      coachEvaluationProgress = 0,
      attendeeCount = 0,
      playerCount = 0,
      isPast = false
    } = eventData;

    const styles = `
      <style>
      ${createBaseStyles()}
      
      :host {
        display: block;
        width: 100%;
      }

      .event-detail-container {
        background: ${theme.colors.dark.background};
        border-radius: ${theme.borderRadius.xl};
        padding: 24px;
        border: 1px solid ${theme.colors.dark.border};
        transition: all 0.3s ease;
      }

      .header {
        margin-bottom: 24px;
      }

      .back-button {
        background: none;
        border: none;
        color: ${theme.colors.primary.teal};
        font-family: ${theme.typography.fontFamily.heading};
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        padding: 8px 16px 8px 0;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s ease;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .back-button:hover {
        color: ${theme.colors.primary.teal};
        transform: translateX(-2px);
      }

      .event-info {
        display: flex;
        align-items: flex-start;
        gap: 20px;
        margin-bottom: 32px;
      }

      .event-icon {
        width: 64px;
        height: 64px;
        background: ${theme.colors.primary.teal};
        border-radius: ${theme.borderRadius.lg};
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }

      .event-icon svg {
        width: 32px;
        height: 32px;
        color: white;
      }

      .event-details {
        flex: 1;
      }

      .event-title {
        font-family: ${theme.typography.fontFamily.heading};
        font-size: 24px;
        font-weight: 700;
        color: #FFFFFF;
        margin-bottom: 8px;
        line-height: 1.2;
      }

      .event-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        font-family: ${theme.typography.fontFamily.body};
        font-size: 14px;
        color: #B3B3B3;
      }

      .meta-item {
        display: flex;
        align-items: center;
        gap: 6px;
      }

      .meta-item svg {
        width: 16px;
        height: 16px;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
      }

      .stat-card {
        background: ${theme.colors.dark.surface};
        border: 1px solid ${theme.colors.dark.border};
        border-radius: ${theme.borderRadius.lg};
        padding: 20px;
        text-align: center;
      }

      .stat-value {
        font-family: ${theme.typography.fontFamily.heading};
        font-size: 32px;
        font-weight: 700;
        color: #FFFFFF;
        margin-bottom: 4px;
      }

      .stat-label {
        font-family: ${theme.typography.fontFamily.body};
        font-size: 14px;
        color: #B3B3B3;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .progress-section {
        margin-top: 24px;
      }

      .progress-item {
        margin-bottom: 20px;
      }

      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
      }

      .progress-label {
        font-family: ${theme.typography.fontFamily.body};
        font-size: 14px;
        font-weight: 600;
        color: #FFFFFF;
      }

      .progress-percentage {
        font-family: ${theme.typography.fontFamily.heading};
        font-size: 14px;
        font-weight: 700;
        color: ${theme.colors.primary.teal};
      }

      .progress-bar {
        width: 100%;
        height: 8px;
        background: ${theme.colors.dark.surfaceLight};
        border-radius: 4px;
        overflow: hidden;
      }

      .progress-fill {
        height: 100%;
        background: ${theme.colors.primary.teal};
        transition: width 0.3s ease;
        border-radius: 4px;
      }

      .badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 12px;
        background: ${theme.colors.primary.teal}20;
        color: ${theme.colors.primary.teal};
        border-radius: 9999px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .badge.past {
        background: #B3B3B320;
        color: #B3B3B3;
      }

      @media (max-width: 640px) {
        .event-detail-container {
          padding: 20px;
        }

        .event-info {
          flex-direction: column;
          gap: 16px;
        }

        .event-title {
          font-size: 20px;
        }

        .stats-grid {
          grid-template-columns: 1fr;
        }

        .stat-value {
          font-size: 28px;
        }
      }
    `;
    
    injectStyles(shadowRoot, styles);
    
    const container = document.createElement('div');
    container.className = 'event-detail-container';
    
    // Build the card structure
    container.innerHTML = `
      ${onBack ? `
        <div class="header">
          <button class="back-button">← BACK TO TEAM</button>
        </div>
      ` : ''}
      
      <div class="event-info">
        <div class="event-icon">
          <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
        </div>
        <div class="event-details">
          <h2 class="event-title">${eventTitle}</h2>
          <div class="event-meta">
            <div class="meta-item">
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <span>${new Date(eventDate).toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}</span>
            </div>
            <span class="badge ${isPast ? 'past' : ''}">${isPast ? 'Completed' : 'Upcoming'}</span>
          </div>
        </div>
      </div>

      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-value">${attendeeCount}</div>
          <div class="stat-label">Attendees</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">${playerCount}</div>
          <div class="stat-label">Players</div>
        </div>
      </div>

      <div class="progress-section">
        ${!isPast ? `
          <div class="progress-item">
            <div class="progress-header">
              <span class="progress-label">Pre-Evaluation Progress</span>
              <span class="progress-percentage">${Math.round(preEvaluationProgress)}%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: ${preEvaluationProgress}%"></div>
            </div>
          </div>
        ` : `
          <div class="progress-item">
            <div class="progress-header">
              <span class="progress-label">Coach Evaluation Progress</span>
              <span class="progress-percentage">${Math.round(coachEvaluationProgress)}%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: ${coachEvaluationProgress}%"></div>
            </div>
          </div>
        `}
      </div>
    `;
    
    // Add event listeners
    if (onBack) {
      const backButton = container.querySelector('.back-button');
      if (backButton) {
        backButton.addEventListener('click', onBack);
      }
    }
    
    shadowRoot.appendChild(container);
  }, [eventTitle, eventDate, eventData, onBack]);

  return <div ref={shadowHostRef} className={className} />;
};