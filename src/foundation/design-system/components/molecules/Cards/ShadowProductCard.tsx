// ABOUTME: Product card component for e-commerce with cart integration and Shadow DOM encapsulation
// Supports pricing, stock status, discounts, and add-to-cart functionality

import React, { useRef, useEffect, useState } from 'react';

export interface ProductData {
  id: string;
  name: string;
  brand?: string;
  price: number;
  sale_price?: number;
  images?: Array<{ url_standard: string }>;
  inventory_level?: number;
  inventory_tracking?: string;
  availability?: string;
  custom_url?: { url: string };
}

export interface ShadowProductCardProps {
  product: ProductData;
  onClick?: (product: ProductData) => void;
  onAddToCart?: (product: ProductData) => void;
  showQuickView?: boolean;
  className?: string;
  currency?: string;
  isInCart?: boolean;
  isLoading?: boolean;
}

export const ShadowProductCard: React.FC<ShadowProductCardProps> = ({
  product,
  onClick,
  onAddToCart,
  showQuickView = false,
  className = '',
  currency = '$',
  isInCart = false,
  isLoading = false
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const [internalLoading, setInternalLoading] = useState(false);
  const [feedbackMessage, setFeedbackMessage] = useState('');

  // Calculate pricing
  const salePrice = product.sale_price || 0;
  const regularPrice = product.price || 0;
  const hasDiscount = salePrice > 0 && salePrice < regularPrice;
  const displayPrice = hasDiscount ? salePrice : regularPrice;
  const discountPercentage = hasDiscount 
    ? Math.round(((regularPrice - salePrice) / regularPrice) * 100)
    : 0;

  // Stock status
  const isOutOfStock = product.availability === 'disabled' || 
    (product.inventory_tracking === 'product' && (product.inventory_level || 0) <= 0);
  const isLowStock = product.inventory_tracking === 'product' && 
    (product.inventory_level || 0) > 0 && (product.inventory_level || 0) <= 5;

  // Handle add to cart
  const handleAddToCart = async (e: Event) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isOutOfStock || internalLoading || isLoading) return;
    
    setInternalLoading(true);
    setFeedbackMessage('Adding to cart...');
    
    try {
      if (onAddToCart) {
        await onAddToCart(product);
        setFeedbackMessage('Added to cart!');
      }
    } catch (error) {
      setFeedbackMessage('Failed to add to cart');
      console.error('Add to cart error:', error);
    } finally {
      setInternalLoading(false);
      setTimeout(() => setFeedbackMessage(''), 2000);
    }
  };

  // Handle product click
  const handleProductClick = () => {
    if (onClick) {
      onClick(product);
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    try {

      // Create shadow DOM
      const shadowRoot = container.attachShadow({ mode: 'open' });
      shadowRootRef.current = shadowRoot;

      const styles = `
        :host {
          display: block;
          width: 100%;
          font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .product-card {
          background: #1F2937;
          border: 1px solid #374151;
          border-radius: 12px;
          overflow: hidden;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .product-card:hover {
          border-color: #14B8A6;
          box-shadow: 0 8px 25px rgba(20, 184, 166, 0.15);
          transform: translateY(-2px);
        }

        .product-card.out-of-stock {
          opacity: 0.7;
          cursor: not-allowed;
        }

        .product-card.out-of-stock:hover {
          transform: none;
          box-shadow: none;
          border-color: #374151;
        }

        .image-container {
          position: relative;
          width: 100%;
          aspect-ratio: 1 / 1;
          overflow: hidden;
          background: #111827;
        }

        .product-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        .product-card:hover .product-image {
          transform: scale(1.05);
        }

        .image-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #374151 0%, #1F2937 100%);
          color: #9CA3AF;
          font-size: 48px;
        }

        .badge {
          position: absolute;
          top: 12px;
          right: 12px;
          padding: 4px 8px;
          border-radius: 16px;
          font-size: 11px;
          font-weight: 700;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          z-index: 2;
        }

        .badge.sale {
          background: #EF4444;
          color: white;
        }

        .badge.low-stock {
          background: #F59E0B;
          color: white;
        }

        .badge.out-of-stock {
          background: #6B7280;
          color: white;
        }

        .content {
          padding: 16px;
          flex: 1;
          display: flex;
          flex-direction: column;
        }

        .brand {
          color: #9CA3AF;
          font-size: 12px;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-bottom: 4px;
        }

        .product-name {
          color: #FFFFFF;
          font-size: 16px;
          font-weight: 600;
          line-height: 1.4;
          margin: 0 0 12px 0;
          flex: 1;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .pricing {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 16px;
          flex-wrap: wrap;
        }

        .current-price {
          color: #14B8A6;
          font-size: 18px;
          font-weight: 700;
        }

        .original-price {
          color: #9CA3AF;
          font-size: 14px;
          text-decoration: line-through;
        }

        .discount-percentage {
          background: rgba(239, 68, 68, 0.1);
          color: #EF4444;
          padding: 2px 6px;
          border-radius: 4px;
          font-size: 11px;
          font-weight: 600;
        }

        .actions {
          display: flex;
          gap: 8px;
        }

        .add-to-cart-btn {
          flex: 1;
          background: ${isInCart ? '#059669' : '#14B8A6'};
          color: white;
          border: none;
          border-radius: 8px;
          padding: 12px 16px;
          font-size: 14px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          position: relative;
          overflow: hidden;
        }

        .add-to-cart-btn:hover:not(:disabled) {
          background: ${isInCart ? '#047857' : '#0F766E'};
          transform: translateY(-1px);
        }

        .add-to-cart-btn:disabled {
          background: #4B5563;
          cursor: not-allowed;
          transform: none;
        }

        .quick-view-btn {
          background: transparent;
          color: #9CA3AF;
          border: 1px solid #4B5563;
          border-radius: 8px;
          padding: 12px;
          cursor: pointer;
          transition: all 0.2s ease;
          width: 44px;
          height: 44px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .quick-view-btn:hover {
          color: #FFFFFF;
          border-color: #6B7280;
          background: rgba(107, 114, 128, 0.1);
        }

        .feedback-message {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: rgba(0, 0, 0, 0.8);
          color: white;
          padding: 8px 16px;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 500;
          white-space: nowrap;
          z-index: 10;
          backdrop-filter: blur(4px);
          opacity: ${feedbackMessage ? '1' : '0'};
          pointer-events: none;
          transition: opacity 0.3s ease;
        }

        .loading-spinner {
          display: inline-block;
          width: 14px;
          height: 14px;
          border: 2px solid transparent;
          border-top: 2px solid currentColor;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
          .content {
            padding: 12px;
          }

          .product-name {
            font-size: 14px;
          }

          .current-price {
            font-size: 16px;
          }

          .add-to-cart-btn {
            padding: 10px 12px;
            font-size: 12px;
          }
        }
      `;

      const productImage = product.images && product.images.length > 0 
        ? product.images[0].url_standard 
        : null;

      const containerHTML = `
        <div class="product-card ${isOutOfStock ? 'out-of-stock' : ''}" data-product-click>
          <div class="image-container">
            ${productImage ? `
              <img src="${productImage}" alt="${product.name}" class="product-image" />
            ` : `
              <div class="image-placeholder">📦</div>
            `}
            
            ${hasDiscount ? `
              <div class="badge sale">-${discountPercentage}%</div>
            ` : isOutOfStock ? `
              <div class="badge out-of-stock">Out of Stock</div>
            ` : isLowStock ? `
              <div class="badge low-stock">Low Stock</div>
            ` : ''}
          </div>

          <div class="content">
            ${product.brand ? `<div class="brand">${product.brand}</div>` : ''}
            <h3 class="product-name">${product.name}</h3>

            <div class="pricing">
              <span class="current-price">${currency}${displayPrice.toFixed(2)}</span>
              ${hasDiscount ? `
                <span class="original-price">${currency}${regularPrice.toFixed(2)}</span>
                <span class="discount-percentage">${discountPercentage}% OFF</span>
              ` : ''}
            </div>

            <div class="actions">
              <button 
                class="add-to-cart-btn" 
                data-add-to-cart
                ${isOutOfStock || internalLoading || isLoading ? 'disabled' : ''}
              >
                ${internalLoading || isLoading ? `
                  <span class="loading-spinner"></span>
                ` : isInCart ? 'IN CART' : isOutOfStock ? 'OUT OF STOCK' : 'ADD TO CART'}
              </button>
              
              ${showQuickView ? `
                <button class="quick-view-btn" data-quick-view title="Quick View">
                  👁️
                </button>
              ` : ''}
            </div>
          </div>

          <div class="feedback-message">${feedbackMessage}</div>
        </div>
      `;

      // Apply styles and HTML
      shadowRoot.innerHTML = `<style>${styles}</style>${containerHTML}`;

      // Event listeners
      const productElement = shadowRoot.querySelector('[data-product-click]');
      const addToCartBtn = shadowRoot.querySelector('[data-add-to-cart]');
      const quickViewBtn = shadowRoot.querySelector('[data-quick-view]');

      if (productElement && onClick && !isOutOfStock) {
        productElement.addEventListener('click', (e) => {
          // Don't trigger if clicking on buttons
          const target = e.target as HTMLElement;
          if (!target.closest('[data-add-to-cart]') && !target.closest('[data-quick-view]')) {
            handleProductClick();
          }
        });
      }

      if (addToCartBtn) {
        addToCartBtn.addEventListener('click', handleAddToCart);
      }

      if (quickViewBtn) {
        quickViewBtn.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          // Handle quick view - could emit custom event or call prop function
          console.log('Quick view:', product.name);
        });
      }

      return () => {
        if (shadowRootRef.current) {
          shadowRootRef.current.innerHTML = '';
        }
      };
    } catch (error) {
      console.error('Failed to create shadow DOM for ShadowProductCard:', error);
    }
  }, [
    product, onClick, onAddToCart, showQuickView, currency, isInCart, 
    isLoading, internalLoading, feedbackMessage, isOutOfStock, isLowStock, 
    hasDiscount, displayPrice, regularPrice, discountPercentage
  ]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{ display: 'block', width: '100%' }}
    />
  );
};

export default ShadowProductCard;