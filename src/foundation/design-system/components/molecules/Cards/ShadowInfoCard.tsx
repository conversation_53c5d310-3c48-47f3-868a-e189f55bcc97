// ABOUTME: Shadow DOM Info Card component for displaying information or actions
// Versatile card component with optional icons and click actions

import React, { useEffect, useRef } from 'react';
export interface ShadowInfoCardProps {
  variant?: 'info' | 'action';
  title: string;
  description?: string;
  onClick?: () => void;
  className?: string;
  icon?: 'chevronRight' | 'arrow' | 'none';
  size?: 'sm' | 'md' | 'lg' | 'small' | 'medium' | 'large';
  sportIcon?: 'football' | 'basketball' | 'tennis' | 'boxing' | 'cricket' | 'rugby' | 'hockey' | 'baseball' | 'swimming' | 'none';
}

const sizeConfig = {
  sm: {
    padding: `1rem 1.25rem`,
    borderRadius: '0.5rem',
    titleSize: '1rem',
    descSize: '0.875rem',
    iconSize: '16px',
  },
  md: {
    padding: `1.25rem 1.5rem`,
    borderRadius: '0.75rem',
    titleSize: '1.125rem',
    descSize: '1rem',
    iconSize: '20px',
  },
  lg: {
    padding: `1.5rem 2rem`,
    borderRadius: '1rem',
    titleSize: '1.25rem',
    descSize: '1.125rem',
    iconSize: '24px',
  },
};

export const ShadowInfoCard: React.FC<ShadowInfoCardProps> = ({
  variant = 'info',
  title,
  description,
  onClick,
  className = '',
  icon = 'none',
  size = 'md',
  sportIcon = 'none',
}) => {
  const hostRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);

  useEffect(() => {
    if (!hostRef.current || shadowRef.current) return;

    // Create shadow root


    

    shadowRef.current = hostRef.current.attachShadow({ mode: 'open' });

    // Create styles
    // Handle both naming conventions for size
    const normalizedSize = size === 'small' ? 'sm' : size === 'medium' ? 'md' : size === 'large' ? 'lg' : size;
    const sizeStyles = sizeConfig[normalizedSize] || sizeConfig.md;
    
    const styles = `
      <style>
      :host {
        display: block;
        width: 100%;
      }
      
      .info-card {
        background: ${variant === 'action' ? 'rgba(42, 42, 42, 0.95)' : 'rgba(31, 31, 31, 0.95)'};
        border: 1px solid #3A3A3A;
        border-radius: ${sizeStyles.borderRadius};
        padding: ${sizeStyles.padding};
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
        font-family: 'Poppins', sans-serif;
        ${variant === 'action' ? 'cursor: pointer;' : ''}
      }
      
      .info-card.action:hover {
        background: rgba(52, 52, 52, 0.95);
        border-color: #10B98166;
        transform: translateY(-1px);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }
      
      .info-card.action:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      }
      
      .card-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 1rem;
      }
      
      .text-content {
        flex: 1;
      }
      
      .title {
        font-weight: 600;
        font-size: ${sizeStyles.titleSize};
        color: #FFFFFF;
        margin: 0;
        line-height: ${1.25};
      }
      
      .description {
        font-size: ${sizeStyles.descSize};
        color: #B3B3B3;
        margin: 0.5rem 0 0 0;
        line-height: ${1.625};
      }
      
      .sport-icon, .action-icon {
        flex-shrink: 0;
        width: ${sizeStyles.iconSize};
        height: ${sizeStyles.iconSize};
        color: #B3B3B3;
        transition: color 0.2s ease, background-color 0.2s ease;
      }
      
      .info-card.action:hover .action-icon {
        color: #10B981;
        transform: translateX(2px);
      }
      
      .glow-effect {
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(26, 188, 156, 0.1),
          transparent
        );
        transition: left 0.5s ease;
      }
      
      .info-card.action:hover .glow-effect {
        left: 100%;
      }
      
      ${className ? `.custom { ${className} }` : ''}
    </style>
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Cleanup
    return () => {
      if (shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, [variant, size, className]);

  useEffect(() => {
    if (!shadowRef.current) return;

    // Clear existing content except style
    const style = shadowRef.current.querySelector('style');
    const existingContent = shadowRef.current.querySelector('.info-card');
    if (existingContent) existingContent.remove();

    // Create card
    const card = document.createElement('div');
    card.className = `info-card ${variant}`;
    if (className) card.classList.add('custom');
    if (onClick) {
      card.onclick = onClick;
    }

    // Glow effect for action cards
    if (variant === 'action') {
      const glow = document.createElement('div');
      glow.className = 'glow-effect';
      card.appendChild(glow);
    }

    // Card content
    const content = document.createElement('div');
    content.className = 'card-content';

    // Text content
    const textContent = document.createElement('div');
    textContent.className = 'text-content';

    const titleEl = document.createElement('h3');
    titleEl.className = 'title';
    titleEl.textContent = title;
    textContent.appendChild(titleEl);

    if (description) {
      const descEl = document.createElement('p');
      descEl.className = 'description';
      descEl.textContent = description;
      textContent.appendChild(descEl);
    }

    content.appendChild(textContent);

    // Icons
    if (sportIcon !== 'none') {
      const iconEl = createSportIcon(sportIcon);
      iconEl.setAttribute('class', 'sport-icon');
      content.appendChild(iconEl);
    }

    if (icon !== 'none' && variant === 'action') {
      const actionIcon = createActionIcon(icon);
      actionIcon.setAttribute('class', 'action-icon');
      content.appendChild(actionIcon);
    }

    card.appendChild(content);
    shadowRef.current.appendChild(card);
  }, [title, description, onClick, icon, size, sportIcon, variant, className]);

  return <div ref={hostRef} />;
};

// Helper function to create sport icons
function createSportIcon(sport: string): SVGElement {
  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  svg.setAttribute('viewBox', '0 0 24 24');
  svg.setAttribute('fill', 'currentColor');

  // Simple sport icon representations
  const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
  
  switch (sport) {
    case 'football':
      path.setAttribute('d', 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 13v1c0 1.1.9 2 2 2v3.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z');
      break;
    case 'basketball':
      path.setAttribute('d', 'M17.09 11h4.86c-.16-1.61-.71-3.11-1.54-4.4-1.73.83-2.99 2.45-3.32 4.4zM6.91 11c-.33-1.95-1.59-3.57-3.32-4.4-.83 1.29-1.38 2.79-1.54 4.4h4.86zm8.09 0c.33-2.29 1.56-4.18 3.22-5.17-1.15-1.27-2.62-2.23-4.22-2.78V11h1zm-6 0h1V3.05c-1.6.55-3.07 1.51-4.22 2.78C7.44 6.82 8.67 8.71 9 11zm8.09 2H12v8.95c1.6-.55 3.07-1.51 4.22-2.78-1.66-.99-2.89-2.88-3.22-5.17zm-11.04 0H2.05c.16 1.61.71 3.11 1.54 4.4 1.73-.83 2.99-2.45 3.32-4.4zm4.95 0c-.33 2.29-1.56 4.18-3.22 5.17 1.15 1.27 2.62 2.23 4.22 2.78V13zm6.09 0c.33 1.95 1.59 3.57 3.32 4.4.83-1.29 1.38-2.79 1.54-4.4h-4.86z');
      break;
    default:
      // Generic sport icon
      path.setAttribute('d', 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z');
  }

  svg.appendChild(path);
  return svg;
}

// Helper function to create action icons
function createActionIcon(type: string): SVGElement {
  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  svg.setAttribute('viewBox', '0 0 24 24');
  svg.setAttribute('fill', 'none');
  svg.setAttribute('stroke', 'currentColor');
  svg.setAttribute('stroke-width', '2');
  svg.setAttribute('stroke-linecap', 'round');
  svg.setAttribute('stroke-linejoin', 'round');

  const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
  
  switch (type) {
    case 'chevronRight':
      path.setAttribute('d', 'M9 18l6-6-6-6');
      break;
    case 'arrow':
      path.setAttribute('d', 'M5 12h14M12 5l7 7-7 7');
      break;
  }

  svg.appendChild(path);
  return svg;
}