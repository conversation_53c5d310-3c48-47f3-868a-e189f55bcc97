// ABOUTME: Shadow DOM category card component for displaying product categories
// Used in shop and locker sections to show category navigation

import React, { useEffect, useRef } from 'react';
import { createBaseStyles, injectStyles } from '../../../utils/shadowDom';
import { theme } from '../../../tokens';
export interface ShadowCategoryCardProps {
  icon: string;
  title: string;
  description?: string;
  onClick?: () => void;
  className?: string;
}

export const ShadowCategoryCard: React.FC<ShadowCategoryCardProps> = ({
  icon,
  title,
  description,
  onClick,
  className = ''
}) => {
  const shadowHostRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!shadowHostRef.current || shadowHostRef.current.shadowRoot) return;

    const shadowRoot = shadowHostRef.current.attachShadow({ mode: 'open' });
    
    const styles = `
      <style>
      ${createBaseStyles()}
      
      :host {
        display: block;
        width: 200px;
        box-sizing: border-box;
      }
      
      .category-card {
        min-height: 140px;
        padding: 24px;
        background: ${theme.colors.dark.surface};
        border: 1px solid ${theme.colors.dark.border};
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 12px;
        width: 100%;
        box-sizing: border-box;
      }
      
      .category-card:hover {
        background: ${theme.colors.dark.surfaceLight};
        transform: translateY(-2px);
        border-color: ${theme.colors.primary.teal};
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }
      
      .category-card:active {
        transform: translateY(0);
      }
      
      .icon {
        font-size: 36px;
        line-height: 1;
        margin-bottom: 8px;
        filter: grayscale(0%);
        transition: filter 0.3s ease;
      }
      
      .category-card:hover .icon {
        filter: grayscale(0%) brightness(1.2);
      }
      
      .title {
        font-size: 18px;
        font-weight: 600;
        color: #FFFFFF;
        margin: 0;
        font-family: 'Poppins', sans-serif;
        letter-spacing: -0.02em;
      }
      
      .description {
        font-size: 14px;
        color: #B3B3B3;
        margin: 0;
        font-family: 'Montserrat', sans-serif;
        line-height: 1.4;
      }
      
      @media (max-width: 640px) {
        :host {
          width: 100%;
          max-width: 200px;
        }
        
        .category-card {
          min-height: 120px;
          padding: 20px;
        }
        
        .icon {
          font-size: 32px;
        }
        
        .title {
          font-size: 16px;
        }
        
        .description {
          font-size: 13px;
        }
      }
    `;
    
    injectStyles(shadowRoot, styles);
    
    const card = document.createElement('div');
    card.className = 'category-card';
    if (onClick) {
      card.onclick = onClick;
      card.setAttribute('role', 'button');
      card.setAttribute('tabindex', '0');
      card.onkeydown = (e: KeyboardEvent) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick();
        }
      };
    }
    
    const iconElement = document.createElement('div');
    iconElement.className = 'icon';
    iconElement.textContent = icon;
    
    const titleElement = document.createElement('h3');
    titleElement.className = 'title';
    titleElement.textContent = title;
    
    card.appendChild(iconElement);
    card.appendChild(titleElement);
    
    if (description) {
      const descElement = document.createElement('p');
      descElement.className = 'description';
      descElement.textContent = description;
      card.appendChild(descElement);
    }
    
    shadowRoot.appendChild(card);
  }, [icon, title, description, onClick]);

  return <div ref={shadowHostRef} className={className} />;
};