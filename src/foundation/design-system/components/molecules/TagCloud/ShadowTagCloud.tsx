// ABOUTME: Tag cloud component for displaying collections of tags with various interaction patterns
// Used for trending tags, followed tags, and filter displays throughout the application

import React from 'react';
import { TrendingUp, Filter, Tag as TagIcon, ChevronDown, ChevronUp, Heart, X, Hash } from 'lucide-react';

interface BaseTagCloudProps {
  title?: string;
  subtitle?: string;
  className?: string;
}

interface BasicTagCloudProps extends BaseTagCloudProps {
  tags: string[];
}

interface InteractiveTagCloudProps extends BaseTagCloudProps {
  tags: string[];
  followedTags: string[];
  onFollowTag?: (tag: string) => void;
  onUnfollowTag?: (tag: string) => void;
  onTagClick?: (tag: string) => void;
  showMoreEnabled?: boolean;
}

interface ActiveFilterProps {
  activeTag: string | null;
  onClearFilter: () => void;
}

interface FollowingTagsProps {
  followedTags: string[];
  onUnfollowTag: (tag: string) => void;
  onFollowTag?: (tag: string) => void;
  showManagement: boolean;
  onToggleManagement: () => void;
}

interface YourFollowedTagsProps {
  followedTags: string[];
  onUnfollowTag: (tag: string) => void;
  onTagClick: (tag: string) => void;
}

// Basic Tag Cloud - Display only
export const ShadowTagCloudBasic: React.FC<BasicTagCloudProps> = ({ 
  tags, 
  title = "Trending Tags", 
  subtitle = "Popular topics" 
}) => {
  const shadowRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (!shadowRef.current || shadowRef.current.shadowRoot) return;

    const shadowRoot = shadowRef.current.attachShadow({ mode: 'open' });
    
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        :host {
          display: block;
          font-family: 'Poppins', sans-serif;
        }

        .tag-cloud-container {
          background: #1a1a1a;
          border-radius: 8px;
          padding: 16px;
        }

        .header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;
        }

        .title-wrapper {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .title {
          font-size: 14px;
          font-weight: 600;
          color: #ffffff;
        }

        .subtitle {
          font-size: 12px;
          color: #999999;
        }

        .icon {
          width: 16px;
          height: 16px;
          color: #40E0D0;
        }

        .tags-grid {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        .tag {
          display: inline-flex;
          align-items: center;
          gap: 4px;
          padding: 4px 12px;
          background: #2a2a2a;
          border-radius: 9999px;
          font-size: 14px;
          color: #cccccc;
          transition: all 0.2s ease;
          cursor: default;
        }

        .tag:hover {
          background: #3a3a3a;
          color: #ffffff;
        }

        .tag-icon {
          width: 12px;
          height: 12px;
        }
      </style>
    `;

    const html = `
      <div class="tag-cloud-container">
        <div class="header">
          <div class="title-wrapper">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
              <polyline points="17 6 23 6 23 12"></polyline>
            </svg>
            <h3 class="title">${title}</h3>
          </div>
          <p class="subtitle">${subtitle}</p>
        </div>
        <div class="tags-grid">
          ${tags.map(tag => `
            <div class="tag">
              <svg class="tag-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="4" y1="9" x2="20" y2="9"></line>
                <line x1="4" y1="15" x2="20" y2="15"></line>
                <line x1="10" y1="3" x2="8" y2="21"></line>
                <line x1="16" y1="3" x2="14" y2="21"></line>
              </svg>
              <span>${tag}</span>
            </div>
          `).join('')}
        </div>
      </div>
    `;

    shadowRoot.innerHTML = styles + html;
  }, [tags, title, subtitle]);

  return <div ref={shadowRef} />;
};

// Interactive Tag Cloud with Follow/Unfollow
export const ShadowTagCloudInteractive: React.FC<InteractiveTagCloudProps> = ({ 
  tags, 
  followedTags, 
  onFollowTag, 
  onUnfollowTag, 
  onTagClick,
  title = "Trending Tags",
  showMoreEnabled = true
}) => {
  const shadowRef = React.useRef<HTMLDivElement>(null);
  const [showAll, setShowAll] = React.useState(false);

  React.useEffect(() => {
    if (!shadowRef.current) return;

    let shadowRoot = shadowRef.current.shadowRoot;
    if (!shadowRoot) {
      shadowRoot = shadowRef.current.attachShadow({ mode: 'open' });
    }

    const displayedTags = showAll ? tags : tags.slice(0, 8);
    
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        :host {
          display: block;
          font-family: 'Poppins', sans-serif;
        }

        .tag-cloud-container {
          background: #1a1a1a;
          border-radius: 8px;
          padding: 16px;
        }

        .header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12px;
        }

        .title-wrapper {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .title {
          font-size: 14px;
          font-weight: 600;
          color: #ffffff;
        }

        .subtitle {
          font-size: 12px;
          color: #999999;
        }

        .icon {
          width: 16px;
          height: 16px;
          color: #40E0D0;
        }

        .tags-grid {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        .tag {
          display: inline-flex;
          align-items: center;
          gap: 4px;
          padding: 6px 12px;
          border-radius: 9999px;
          font-size: 14px;
          transition: all 0.2s ease;
          cursor: pointer;
          border: none;
          font-family: inherit;
        }

        .tag:hover {
          transform: scale(1.05);
        }

        .tag:active {
          transform: scale(0.95);
        }

        .tag.followed {
          background: #40E0D0;
          color: #000000;
          font-weight: 500;
        }

        .tag.unfollowed {
          background: #2a2a2a;
          color: #cccccc;
        }

        .tag.unfollowed:hover {
          background: #3a3a3a;
          color: #ffffff;
        }

        .tag-icon {
          width: 12px;
          height: 12px;
        }

        .check-icon {
          width: 12px;
          height: 12px;
          margin-left: 2px;
        }

        .show-more-btn {
          padding: 6px 12px;
          background: none;
          border: none;
          color: #40E0D0;
          font-size: 14px;
          cursor: pointer;
          font-family: inherit;
          transition: color 0.2s ease;
        }

        .show-more-btn:hover {
          color: #60F0E0;
        }
      </style>
    `;

    const html = `
      <div class="tag-cloud-container">
        <div class="header">
          <div class="title-wrapper">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="23 6 13.5 15.5 8.5 10.5 1 18"></polyline>
              <polyline points="17 6 23 6 23 12"></polyline>
            </svg>
            <h3 class="title">${title}</h3>
          </div>
          <p class="subtitle">Click to filter articles</p>
        </div>
        <div class="tags-grid" id="tags-grid">
          ${displayedTags.map(tag => {
            const isFollowed = followedTags.includes(tag);
            return `
              <button class="tag ${isFollowed ? 'followed' : 'unfollowed'}" data-tag="${tag}">
                <svg class="tag-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <line x1="4" y1="9" x2="20" y2="9"></line>
                  <line x1="4" y1="15" x2="20" y2="15"></line>
                  <line x1="10" y1="3" x2="8" y2="21"></line>
                  <line x1="16" y1="3" x2="14" y2="21"></line>
                </svg>
                <span>${tag}</span>
                ${isFollowed ? `
                  <svg class="check-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                ` : ''}
              </button>
            `;
          }).join('')}
          ${showMoreEnabled && tags.length > 8 ? `
            <button class="show-more-btn" id="show-more-btn">
              ${showAll ? 'Show less' : `+${tags.length - 8} more`}
            </button>
          ` : ''}
        </div>
      </div>
    `;

    shadowRoot.innerHTML = styles + html;

    // Add event listeners
    const tagButtons = shadowRoot.querySelectorAll('.tag');
    tagButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const tag = (e.currentTarget as HTMLElement).getAttribute('data-tag');
        if (!tag) return;

        if (onTagClick) {
          onTagClick(tag);
        } else {
          const isFollowed = followedTags.includes(tag);
          if (isFollowed && onUnfollowTag) {
            onUnfollowTag(tag);
          } else if (!isFollowed && onFollowTag) {
            onFollowTag(tag);
          }
        }
      });
    });

    const showMoreBtn = shadowRoot.getElementById('show-more-btn');
    if (showMoreBtn) {
      showMoreBtn.addEventListener('click', () => {
        setShowAll(!showAll);
      });
    }
  }, [tags, followedTags, showAll, onFollowTag, onUnfollowTag, onTagClick, title, showMoreEnabled]);

  return <div ref={shadowRef} />;
};

// Active Filter Display
export const ShadowTagCloudActiveFilter: React.FC<ActiveFilterProps> = ({ 
  activeTag, 
  onClearFilter 
}) => {
  const shadowRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (!shadowRef.current || !activeTag) return;

    let shadowRoot = shadowRef.current.shadowRoot;
    if (!shadowRoot) {
      shadowRoot = shadowRef.current.attachShadow({ mode: 'open' });
    }
    
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        :host {
          display: block;
          font-family: 'Poppins', sans-serif;
        }

        .filter-container {
          background: linear-gradient(135deg, #1a3a3a 0%, #1a1a2a 100%);
          border: 1px solid #40E0D0;
          border-radius: 8px;
          padding: 12px 16px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;
          animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .filter-content {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .filter-icon {
          width: 20px;
          height: 20px;
          color: #40E0D0;
        }

        .filter-text {
          font-size: 14px;
          color: #ffffff;
        }

        .tag-name {
          font-weight: 600;
          color: #40E0D0;
        }

        .clear-btn {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 6px 12px;
          background: rgba(255, 255, 255, 0.1);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 4px;
          color: #ffffff;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s ease;
          font-family: inherit;
        }

        .clear-btn:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.3);
        }

        .clear-icon {
          width: 14px;
          height: 14px;
        }
      </style>
    `;

    const html = `
      <div class="filter-container">
        <div class="filter-content">
          <svg class="filter-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
          </svg>
          <p class="filter-text">
            Filtering by: <span class="tag-name">#${activeTag}</span>
          </p>
        </div>
        <button class="clear-btn" id="clear-btn">
          <svg class="clear-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
          Clear filter
        </button>
      </div>
    `;

    shadowRoot.innerHTML = styles + html;

    const clearBtn = shadowRoot.getElementById('clear-btn');
    if (clearBtn) {
      clearBtn.addEventListener('click', onClearFilter);
    }
  }, [activeTag, onClearFilter]);

  if (!activeTag) return null;
  return <div ref={shadowRef} />;
};

// Export all variants
export const ShadowTagCloud = {
  Basic: ShadowTagCloudBasic,
  Interactive: ShadowTagCloudInteractive,
  ActiveFilter: ShadowTagCloudActiveFilter
};