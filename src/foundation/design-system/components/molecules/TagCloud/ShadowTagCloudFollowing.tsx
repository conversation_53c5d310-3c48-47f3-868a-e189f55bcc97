// ABOUTME: Tag cloud components for managing followed tags
// Includes expandable management view and compact display variants

import React from 'react';

interface FollowingTagsProps {
  followedTags: string[];
  onUnfollowTag: (tag: string) => void;
  onFollowTag?: (tag: string) => void;
  showManagement: boolean;
  onToggleManagement: () => void;
}

interface YourFollowedTagsProps {
  followedTags: string[];
  onUnfollowTag: (tag: string) => void;
  onTagClick: (tag: string) => void;
}

// Following Tags Management View
export const ShadowTagCloudFollowing: React.FC<FollowingTagsProps> = ({ 
  followedTags, 
  onUnfollowTag, 
  showManagement, 
  onToggleManagement 
}) => {
  const shadowRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (!shadowRef.current || followedTags.length === 0) return;

    let shadowRoot = shadowRef.current.shadowRoot;
    if (!shadowRoot) {
      shadowRoot = shadowRef.current.attachShadow({ mode: 'open' });
    }
    
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        :host {
          display: block;
          font-family: 'Poppins', sans-serif;
        }

        .following-container {
          background: #1a1a1a;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 16px;
        }

        .header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: ${showManagement ? '16px' : '0'};
        }

        .title-wrapper {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .title {
          font-size: 14px;
          font-weight: 600;
          color: #ffffff;
        }

        .tag-icon {
          width: 16px;
          height: 16px;
          color: #40E0D0;
        }

        .toggle-btn {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          background: none;
          border: none;
          color: #999999;
          font-size: 12px;
          cursor: pointer;
          font-family: inherit;
          transition: color 0.2s ease;
        }

        .toggle-btn:hover {
          color: #ffffff;
        }

        .chevron {
          width: 14px;
          height: 14px;
          transition: transform 0.2s ease;
        }

        .chevron.open {
          transform: rotate(180deg);
        }

        .tags-list {
          display: flex;
          flex-direction: column;
          gap: 8px;
          max-height: ${showManagement ? '300px' : '0'};
          overflow-y: auto;
          transition: max-height 0.3s ease;
        }

        .tag-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 8px 12px;
          background: #2a2a2a;
          border-radius: 6px;
          transition: background 0.2s ease;
        }

        .tag-item:hover {
          background: #3a3a3a;
        }

        .tag-name {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 14px;
          color: #cccccc;
        }

        .hash-icon {
          width: 12px;
          height: 12px;
          color: #40E0D0;
        }

        .unfollow-btn {
          padding: 4px 8px;
          background: rgba(255, 59, 48, 0.2);
          border: none;
          border-radius: 4px;
          color: #ff3b30;
          font-size: 12px;
          cursor: pointer;
          font-family: inherit;
          transition: all 0.2s ease;
        }

        .unfollow-btn:hover {
          background: rgba(255, 59, 48, 0.3);
          transform: scale(1.05);
        }
      </style>
    `;

    const html = `
      <div class="following-container">
        <div class="header">
          <div class="title-wrapper">
            <svg class="tag-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
              <line x1="7" y1="7" x2="7.01" y2="7"></line>
            </svg>
            <h3 class="title">Following Tags (${followedTags.length})</h3>
          </div>
          <button class="toggle-btn" id="toggle-btn">
            <span>${showManagement ? 'Hide' : 'Manage'}</span>
            <svg class="chevron ${showManagement ? 'open' : ''}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="6 9 12 15 18 9"></polyline>
            </svg>
          </button>
        </div>
        <div class="tags-list">
          ${followedTags.map(tag => `
            <div class="tag-item">
              <div class="tag-name">
                <svg class="hash-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <line x1="4" y1="9" x2="20" y2="9"></line>
                  <line x1="4" y1="15" x2="20" y2="15"></line>
                  <line x1="10" y1="3" x2="8" y2="21"></line>
                  <line x1="16" y1="3" x2="14" y2="21"></line>
                </svg>
                <span>${tag}</span>
              </div>
              <button class="unfollow-btn" data-tag="${tag}">Unfollow</button>
            </div>
          `).join('')}
        </div>
      </div>
    `;

    shadowRoot.innerHTML = styles + html;

    const toggleBtn = shadowRoot.getElementById('toggle-btn');
    if (toggleBtn) {
      toggleBtn.addEventListener('click', onToggleManagement);
    }

    const unfollowBtns = shadowRoot.querySelectorAll('.unfollow-btn');
    unfollowBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const tag = (e.currentTarget as HTMLElement).getAttribute('data-tag');
        if (tag) onUnfollowTag(tag);
      });
    });
  }, [followedTags, onUnfollowTag, showManagement, onToggleManagement]);

  if (followedTags.length === 0) return null;
  return <div ref={shadowRef} />;
};

// Your Followed Tags - Compact Display
export const ShadowTagCloudYourFollowed: React.FC<YourFollowedTagsProps> = ({ 
  followedTags, 
  onUnfollowTag, 
  onTagClick 
}) => {
  const shadowRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (!shadowRef.current || followedTags.length === 0) return;

    let shadowRoot = shadowRef.current.shadowRoot;
    if (!shadowRoot) {
      shadowRoot = shadowRef.current.attachShadow({ mode: 'open' });
    }
    
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
        
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        :host {
          display: block;
          font-family: 'Poppins', sans-serif;
        }

        .followed-tags-container {
          background: #1a1a1a;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 24px;
        }

        .header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;
        }

        .title {
          font-size: 14px;
          font-weight: 600;
          color: #ffffff;
        }

        .heart-icon {
          width: 16px;
          height: 16px;
          color: #9333EA;
          fill: #9333EA;
        }

        .tags-wrapper {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-bottom: 8px;
        }

        .tag-item {
          display: inline-flex;
          align-items: center;
          gap: 4px;
          padding: 4px 12px;
          background: rgba(147, 51, 234, 0.2);
          color: #9333EA;
          border-radius: 9999px;
          font-size: 14px;
          transition: all 0.2s ease;
          position: relative;
        }

        .tag-item:hover {
          transform: scale(1.05);
          background: rgba(147, 51, 234, 0.3);
        }

        .tag-item:active {
          transform: scale(0.95);
        }

        .hash-icon {
          width: 12px;
          height: 12px;
        }

        .tag-name {
          cursor: pointer;
        }

        .remove-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 4px;
          padding: 2px;
          background: none;
          border: none;
          color: #9333EA;
          cursor: pointer;
          transition: all 0.2s ease;
          border-radius: 50%;
        }

        .remove-btn:hover {
          color: #ffffff;
          background: rgba(255, 255, 255, 0.1);
        }

        .remove-icon {
          width: 12px;
          height: 12px;
        }

        .hint-text {
          font-size: 12px;
          color: #999999;
          margin-top: 8px;
        }
      </style>
    `;

    const html = `
      <div class="followed-tags-container">
        <div class="header">
          <svg class="heart-icon" viewBox="0 0 24 24" fill="currentColor" stroke="none">
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
          </svg>
          <h3 class="title">Your Followed Tags</h3>
        </div>
        <div class="tags-wrapper" id="tags-wrapper">
          ${followedTags.map(tag => `
            <div class="tag-item" data-tag="${tag}">
              <svg class="hash-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="4" y1="9" x2="20" y2="9"></line>
                <line x1="4" y1="15" x2="20" y2="15"></line>
                <line x1="10" y1="3" x2="8" y2="21"></line>
                <line x1="16" y1="3" x2="14" y2="21"></line>
              </svg>
              <span class="tag-name" data-tag="${tag}">${tag}</span>
              <button class="remove-btn" data-tag="${tag}">
                <svg class="remove-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
          `).join('')}
        </div>
        <p class="hint-text">Click tag to filter • Click × to unfollow</p>
      </div>
    `;

    shadowRoot.innerHTML = styles + html;

    // Add event listeners for tag clicks
    const tagNames = shadowRoot.querySelectorAll('.tag-name');
    tagNames.forEach(element => {
      element.addEventListener('click', (e) => {
        e.stopPropagation();
        const tag = (e.currentTarget as HTMLElement).getAttribute('data-tag');
        if (tag) onTagClick(tag);
      });
    });

    // Add event listeners for remove buttons
    const removeBtns = shadowRoot.querySelectorAll('.remove-btn');
    removeBtns.forEach(btn => {
      btn.addEventListener('click', (e) => {
        e.stopPropagation();
        const tag = (e.currentTarget as HTMLElement).getAttribute('data-tag');
        if (tag) onUnfollowTag(tag);
      });
    });
  }, [followedTags, onUnfollowTag, onTagClick]);

  if (followedTags.length === 0) return null;
  return <div ref={shadowRef} />;
};