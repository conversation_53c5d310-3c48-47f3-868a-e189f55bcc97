// ABOUTME: Pulse feed card component for displaying news and content items
// Primary component for the Pulse news feed feature

import React from 'react';
import { PulseCardProps } from './types';

// Helper functions
const getPillarColor = (pillar: string): string => {
  switch (pillar) {
    case 'TAKE YOUR SHOT':
      return 'teal';
    case 'OWN IT':
      return 'purple';
    case 'MAKE IMPACT':
      return 'gold';
    default:
      return 'red';
  }
};

const formatDate = (dateString?: string): string => {
  const date = dateString ? new Date(dateString) : new Date();
  return date.toLocaleDateString('en-US', { month: 'long', day: 'numeric' });
};

const getEntityIcon = (type: string): string => {
  switch (type) {
    case 'player':
      return `<svg fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" /></svg>`;
    case 'team':
      return `<svg fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" /></svg>`;
    case 'league':
      return `<svg fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" /></svg>`;
    default:
      return `<svg fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" /></svg>`;
  }
};

export const ShadowPulseCard: React.FC<PulseCardProps> = ({ 
  post, 
  index, 
  onClick, 
  onLike, 
  onBookmark, 
  onShare, 
  onTagClick 
}) => {
  const shadowRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (!shadowRef.current || shadowRef.current.shadowRoot) return;

    const shadowRoot = shadowRef.current.attachShadow({ mode: 'open' });
    
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800;900&family=Montserrat:wght@300;400;500;600&display=swap');
        
        * {
          box-sizing: border-box;
          margin: 0;
          padding: 0;
        }
        
        :host {
          display: block;
          width: 100%;
        }
        
        .feed-card {
          background: #1E1E1E;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
          transition: all 0.3s ease;
          cursor: pointer;
          animation: fadeIn 0.3s ease-in-out;
          animation-delay: ${index * 0.1}s;
          animation-fill-mode: both;
          height: 100%;
          display: flex;
          flex-direction: column;
        }
        
        @keyframes fadeIn {
          from { 
            opacity: 0; 
            transform: translateY(10px); 
          }
          to { 
            opacity: 1; 
            transform: translateY(0); 
          }
        }
        
        .feed-card:hover {
          box-shadow: 0 20px 25px -5px rgba(107, 0, 219, 0.2), 0 10px 10px -5px rgba(107, 0, 219, 0.04);
          transform: translateY(-4px);
        }
        
        .card-header {
          padding: 16px;
        }
        
        .header-top {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
        }
        
        .source {
          font-family: 'Montserrat', sans-serif;
          font-size: 11px;
          font-weight: 600;
          color: #B3B3B3;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
        
        .pulse-badge {
          font-family: 'Montserrat', sans-serif;
          font-size: 11px;
          font-weight: 700;
          color: #F7B613;
          border: 1px solid #F7B613;
          border-radius: 24px;
          padding: 2px 8px;
        }
        
        .title-container {
          aspect-ratio: 16 / 9;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 16px;
          background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 100%);
          position: relative;
          overflow: hidden;
        }
        
        .title-container::before {
          content: '';
          position: absolute;
          inset: 0;
          background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.05) 50%, transparent 70%);
          animation: shimmer 3s ease-in-out infinite;
        }
        
        @keyframes shimmer {
          0% {
            transform: translateX(-100%) skewX(-15deg);
          }
          100% {
            transform: translateX(200%) skewX(-15deg);
          }
        }
        
        .title-container.teal {
          background-color: #1ABC9C;
        }
        
        .title-container.purple {
          background-color: #6B00DB;
        }
        
        .title-container.gold {
          background-color: #F7B613;
        }
        
        .title-container.red {
          background-color: #E63946;
        }
        
        .title {
          font-family: 'Poppins', sans-serif;
          font-weight: 800;
          font-size: clamp(20px, 5vw, 28px);
          text-align: center;
          color: #FFFFFF;
          line-height: 1.2;
          text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
          position: relative;
          z-index: 1;
        }
        
        .card-content {
          padding: 0 16px 16px;
          flex: 1;
          display: flex;
          flex-direction: column;
        }
        
        .narrative {
          font-family: 'Montserrat', sans-serif;
          font-size: 16px;
          line-height: 1.75;
          color: #B3B3B3;
          margin-bottom: 16px;
          flex: 1;
        }
        
        .entity-tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-bottom: 16px;
        }
        
        .entity-tag {
          display: inline-flex;
          align-items: center;
          gap: 4px;
          padding: 6px 12px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 20px;
          font-family: 'Montserrat', sans-serif;
          font-size: 12px;
          color: #B3B3B3;
          transition: all 0.2s ease;
          cursor: pointer;
          border: 1px solid transparent;
        }
        
        .entity-tag:hover {
          background: #1ABC9C;
          color: #FFFFFF;
          border-color: #1ABC9C;
        }
        
        .entity-tag svg {
          width: 14px;
          height: 14px;
        }
        
        .read-more {
          font-family: 'Montserrat', sans-serif;
          font-size: 14px;
          font-weight: 600;
          color: #F7B613;
          text-decoration: none;
          display: inline-flex;
          align-items: center;
          gap: 4px;
          transition: all 0.2s ease;
        }
        
        .read-more:hover {
          text-decoration: underline;
          gap: 8px;
        }
        
        .read-more svg {
          width: 14px;
          height: 14px;
        }
        
        .card-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          font-family: 'Montserrat', sans-serif;
          font-size: 12px;
          color: #B3B3B3;
        }
        
        .desk {
          display: flex;
          align-items: center;
          font-weight: 600;
          gap: 6px;
        }
        
        .desk-emoji {
          font-size: 18px;
        }
        
        .date {
          display: flex;
          align-items: center;
          gap: 6px;
        }
        
        .date svg {
          width: 14px;
          height: 14px;
        }
        
        .interactions {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 0 16px 16px;
        }
        
        .interaction-btn {
          display: flex;
          align-items: center;
          gap: 4px;
          background: none;
          border: none;
          padding: 8px 12px;
          border-radius: 20px;
          font-family: 'Montserrat', sans-serif;
          font-size: 13px;
          color: #B3B3B3;
          cursor: pointer;
          transition: all 0.2s ease;
        }
        
        .interaction-btn:hover {
          background: rgba(255, 255, 255, 0.1);
        }
        
        .interaction-btn.liked {
          color: #E63946;
        }
        
        .interaction-btn.bookmarked {
          color: #1ABC9C;
        }
        
        .interaction-btn svg {
          width: 18px;
          height: 18px;
        }
      </style>
    `;

    const content = `
      ${styles}
      <div class="feed-card">
        <div class="card-header">
          <div class="header-top">
            <p class="source">${(post.source || 'SHOT Newsroom').toUpperCase()}</p>
            <span class="pulse-badge">PULSE</span>
          </div>
          <div class="title-container ${getPillarColor(post.pillar)}">
            <h3 class="title">${post.title}</h3>
          </div>
        </div>
        <div class="card-content">
          <p class="narrative">${post.narrative}</p>
          ${post.entities && Array.isArray(post.entities) && post.entities.length > 0 ? `
            <div class="entity-tags">
              ${post.entities.map(entity => `
                <button class="entity-tag" data-tag="${entity.name}">
                  ${getEntityIcon(entity.type)}
                  <span>${entity.name}</span>
                </button>
              `).join('')}
            </div>
          ` : ''}
          <a href="${post.url}" target="_blank" rel="noopener noreferrer" class="read-more">
            Read More at ${post.source || 'the source'}
            <svg fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
              <polyline points="15 3 21 3 21 9"></polyline>
              <line x1="10" y1="14" x2="21" y2="3"></line>
            </svg>
          </a>
        </div>
        <div class="interactions">
          <button class="interaction-btn like-btn">
            <svg fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
            </svg>
            <span>${post.like_count || 0}</span>
          </button>
          <button class="interaction-btn bookmark-btn">
            <svg fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
            </svg>
            <span>${post.bookmark_count || 0}</span>
          </button>
          <button class="interaction-btn share-btn">
            <svg fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <circle cx="18" cy="5" r="3"></circle>
              <circle cx="6" cy="12" r="3"></circle>
              <circle cx="18" cy="19" r="3"></circle>
              <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
              <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
            </svg>
            <span>${post.share_count || 0}</span>
          </button>
        </div>
        <div class="card-footer">
          <span class="desk">
            <span class="desk-emoji">${post.desk_emoji || '🏆'}</span>
            ${post.desk}
          </span>
          <span class="date">
            <svg fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
            ${formatDate(post.pubDate)}
          </span>
        </div>
      </div>
    `;

    shadowRoot.innerHTML = content;

    // Add event listeners
    const card = shadowRoot.querySelector('.feed-card');
    if (card && onClick) {
      card.addEventListener('click', (e) => {
        if (!(e.target as HTMLElement).closest('.interaction-btn, .entity-tag, .read-more')) {
          onClick();
        }
      });
    }

    // Like button
    const likeBtn = shadowRoot.querySelector('.like-btn');
    if (likeBtn && onLike) {
      likeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        onLike();
      });
    }

    // Bookmark button
    const bookmarkBtn = shadowRoot.querySelector('.bookmark-btn');
    if (bookmarkBtn && onBookmark) {
      bookmarkBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        onBookmark();
      });
    }

    // Share button
    const shareBtn = shadowRoot.querySelector('.share-btn');
    if (shareBtn && onShare) {
      shareBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        onShare();
      });
    }

    // Entity tags
    const tags = shadowRoot.querySelectorAll('.entity-tag');
    tags.forEach(tag => {
      tag.addEventListener('click', (e) => {
        e.stopPropagation();
        const tagName = (e.currentTarget as HTMLElement).getAttribute('data-tag');
        if (tagName && onTagClick) {
          onTagClick(tagName);
        }
      });
    });
  }, [post, index, onClick, onLike, onBookmark, onShare, onTagClick]);

  return <div ref={shadowRef} />;
};