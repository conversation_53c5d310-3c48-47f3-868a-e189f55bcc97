// ABOUTME: Type definitions for Pulse feed components
// Shared interfaces for all Pulse variants

export interface PulseEntity {
  id: string;
  name: string;
  type: 'player' | 'team' | 'league' | 'brand' | 'artist' | 'cultural';
}

export interface PulsePost {
  id?: string;
  title: string;
  pillar: 'TAKE YOUR SHOT' | 'OWN IT' | 'MAKE IMPACT' | string;
  desk: string;
  desk_emoji?: string;
  narrative: string;
  url: string;
  source?: string;
  pubDate?: string;
  image_url?: string;
  entities?: PulseEntity[];
  social_post?: string;
  // Interaction stats
  like_count?: number;
  bookmark_count?: number;
  share_count?: number;
}

export interface PulseInteractionProps {
  onClick?: () => void;
  onLike?: () => void;
  onBookmark?: () => void;
  onShare?: () => void;
  onTagClick?: (tag: string) => void;
}

export interface PulseCardProps extends PulseInteractionProps {
  post: PulsePost;
  index: number;
}