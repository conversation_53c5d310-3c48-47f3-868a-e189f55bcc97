// ABOUTME: Collapsible drawer component with Shadow DOM encapsulation and React portal support
// Provides slide-out functionality with complete style isolation

import React, { useEffect, useRef, useState, ReactNode } from 'react';
import ReactDOM from 'react-dom';

export type DrawerSize = 'small' | 'medium' | 'large';

export interface ShadowCollapsibleDrawerProps {
  isOpen: boolean;
  onToggle: () => void;
  title?: string;
  children: ReactNode;
  hideButtonText?: string;
  showButtonText?: string;
  className?: string;
  size?: DrawerSize;
}

export const ShadowCollapsibleDrawer: React.FC<ShadowCollapsibleDrawerProps> = ({
  isOpen,
  onToggle,
  title = 'Manage Options',
  children,
  hideButtonText = 'HIDE OPTIONS',
  showButtonText = 'SHOW OPTIONS',
  className = '',
  size = 'medium'
}) => {
  const shadowHostRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const [childrenContainer, setChildrenContainer] = useState<HTMLDivElement | null>(null);

  // Size configurations
  const sizeConfig = {
    small: { maxHeight: '200px', padding: '12px' },
    medium: { maxHeight: '300px', padding: '16px' },
    large: { maxHeight: '400px', padding: '20px' }
  };

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onToggle();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, onToggle]);

  // Initialize Shadow DOM
  useEffect(() => {
    if (!shadowHostRef.current) return;

    try {
      // Create shadow root if it doesn't exist
      if (!shadowRootRef.current) {
        shadowRootRef.current = shadowHostRef.current.attachShadow({ mode: 'open' });
      }

      const shadowRoot = shadowRootRef.current;
      const config = sizeConfig[size];

      const styles = `
        :host {
          display: block;
          width: 100%;
          font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        * {
          box-sizing: border-box;
        }
        
        .drawer-container {
          width: 100%;
          background: #1A1A1A;
          border-radius: 12px;
          overflow: hidden;
          border: 1px solid #3A3A3A;
          transition: all 0.3s ease;
        }

        .drawer-container:hover {
          border-color: ${isOpen ? '#16A085' : '#1ABC9C'};
        }
        
        .drawer-button {
          width: 100%;
          padding: ${config.padding};
          background: ${isOpen ? '#1ABC9C' : '#2A2A2A'};
          border: none;
          color: ${isOpen ? '#1A1A1A' : '#FFFFFF'};
          font-size: 14px;
          font-weight: 600;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          transition: all 0.3s ease;
          font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          position: relative;
          overflow: hidden;
        }

        .drawer-button::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
          transition: left 0.5s;
        }

        .drawer-button:hover::before {
          left: 100%;
        }
        
        .drawer-button:hover {
          background: ${isOpen ? '#16A085' : '#3A3A3A'};
          transform: translateY(-1px);
          box-shadow: 0 4px 12px ${isOpen ? 'rgba(26, 188, 156, 0.3)' : 'rgba(58, 58, 58, 0.3)'};
        }

        .drawer-button:active {
          transform: translateY(0);
        }
        
        .drawer-content {
          max-height: ${isOpen ? config.maxHeight : '0'};
          overflow: hidden;
          transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
          background: #1A1A1A;
        }

        .drawer-content.open {
          overflow-y: auto;
        }
        
        .drawer-inner {
          padding: ${isOpen ? config.padding : '0'} 0;
          border-top: 1px solid #3A3A3A;
          transition: padding 0.4s ease;
        }
        
        .drawer-title {
          font-size: 16px;
          font-weight: 600;
          color: #FFFFFF;
          margin-bottom: 12px;
          text-align: center;
          padding: 0 ${config.padding};
          font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          opacity: ${isOpen ? '1' : '0'};
          transition: opacity 0.3s ease 0.1s;
        }
        
        .drawer-children {
          opacity: ${isOpen ? '1' : '0'};
          transition: opacity 0.3s ease 0.2s;
        }
        
        .drawer-chevron {
          width: 16px;
          height: 16px;
          transform: ${isOpen ? 'rotate(180deg)' : 'rotate(0deg)'};
          transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          flex-shrink: 0;
        }

        .button-text {
          transition: all 0.3s ease;
        }

        /* Loading animation for content */
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .drawer-content.open .drawer-children > * {
          animation: fadeIn 0.4s ease forwards;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
          .drawer-button {
            padding: 12px 16px;
            font-size: 13px;
          }

          .drawer-title {
            font-size: 15px;
          }

          .drawer-chevron {
            width: 14px;
            height: 14px;
          }
        }

        /* Focus styles for accessibility */
        .drawer-button:focus {
          outline: none;
          box-shadow: 0 0 0 2px rgba(26, 188, 156, 0.5);
        }

        .drawer-button:focus-visible {
          outline: 2px solid #1ABC9C;
          outline-offset: 2px;
        }
      `;

      // Clear shadow root and rebuild
      shadowRoot.innerHTML = `<style>${styles}</style>`;

      // Create DOM structure
      const container = document.createElement('div');
      container.className = 'drawer-container';

      const button = document.createElement('button');
      button.className = 'drawer-button';
      button.type = 'button';
      button.setAttribute('aria-expanded', isOpen.toString());
      button.setAttribute('aria-controls', 'drawer-content');
      button.onclick = onToggle;

      const buttonText = document.createElement('span');
      buttonText.className = 'button-text';
      buttonText.textContent = isOpen ? hideButtonText : showButtonText;
      button.appendChild(buttonText);

      const chevron = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      chevron.setAttribute('class', 'drawer-chevron');
      chevron.setAttribute('fill', 'none');
      chevron.setAttribute('stroke', 'currentColor');
      chevron.setAttribute('viewBox', '0 0 24 24');
      chevron.setAttribute('aria-hidden', 'true');
      
      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      path.setAttribute('stroke-linecap', 'round');
      path.setAttribute('stroke-linejoin', 'round');
      path.setAttribute('stroke-width', '2');
      path.setAttribute('d', 'M19 9l-7 7-7-7');
      chevron.appendChild(path);
      
      button.appendChild(chevron);
      container.appendChild(button);

      const content = document.createElement('div');
      content.className = `drawer-content ${isOpen ? 'open' : ''}`;
      content.id = 'drawer-content';
      content.setAttribute('aria-hidden', (!isOpen).toString());

      const inner = document.createElement('div');
      inner.className = 'drawer-inner';

      if (title && isOpen) {
        const titleEl = document.createElement('div');
        titleEl.className = 'drawer-title';
        titleEl.textContent = title;
        titleEl.setAttribute('role', 'heading');
        titleEl.setAttribute('aria-level', '3');
        inner.appendChild(titleEl);
      }

      const childrenDiv = document.createElement('div');
      childrenDiv.className = 'drawer-children';
      childrenDiv.setAttribute('role', 'region');
      childrenDiv.setAttribute('aria-label', title || 'Drawer content');
      inner.appendChild(childrenDiv);
      content.appendChild(inner);
      container.appendChild(content);

      shadowRoot.appendChild(container);

      // Set the children container for React portal
      setChildrenContainer(childrenDiv);

      return () => {
        if (shadowRootRef.current) {
          shadowRootRef.current.innerHTML = '';
        }
      };
    } catch (error) {
      console.error('Failed to create shadow DOM for ShadowCollapsibleDrawer:', error);
    }
  }, [isOpen, onToggle, title, hideButtonText, showButtonText, size]);

  return (
    <>
      <div 
        ref={shadowHostRef} 
        className={className}
        style={{ display: 'block', width: '100%' }}
      />
      {childrenContainer && ReactDOM.createPortal(children, childrenContainer)}
    </>
  );
};

export default ShadowCollapsibleDrawer;