// ABOUTME: Enhanced navigation drawer with role management integration
// Provides role-aware navigation with dynamic styling and role switching

import React, { useEffect, useRef, useState } from 'react';
import { useCurrentRole } from '../../../../../hooks/useCurrentRole';
import { useHistory } from 'react-router-dom';

export interface ShadowNavigationDrawerEnhancedProps {
  isOpen: boolean;
  onToggle: () => void;
  userName?: string;
  userAvatar?: string;
  userRole?: string;
  profilesFilter?: string | null;
  onProfileClick?: () => void;
  onSettingsClick?: () => void;
  onLogoutClick?: () => void;
}

export const ShadowNavigationDrawerEnhanced: React.FC<ShadowNavigationDrawerEnhancedProps> = ({
  isOpen,
  onToggle,
  userName = 'User',
  userAvatar,
  userRole,
  profilesFilter = null,
  onProfileClick,
  onSettingsClick,
  onLogoutClick
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const history = useHistory();
  const [debugMode] = useState(false); // Can be enabled for debugging

  // Use the current role system
  const { 
    currentRole, 
    availableRoles, 
    switchRole, 
    isLoading,
    roleInfo 
  } = useCurrentRole();

  // Debug logging
  useEffect(() => {
    if (debugMode) {
      console.log('NavigationDrawer - Role state:', {
        currentRole,
        availableRoles,
        roleInfo,
        isLoading,
        hasMultipleRoles: availableRoles.length > 1
      });
    }
  }, [currentRole, availableRoles, roleInfo, isLoading, debugMode]);

  const handleRoleSwitch = async (newRole: string) => {
    if (newRole === currentRole) return;
    
    try {
      await switchRole(newRole);
      // Navigate to the default route for the new role
      if (roleInfo?.defaultRoute) {
        history.replace(roleInfo.defaultRoute);
      }
    } catch (error) {
      console.error('Failed to switch role:', error);
    }
  };

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Check if shadow root already exists
    let shadowRoot = container.shadowRoot;
    if (!shadowRoot) {
      // Create shadow DOM only if it doesn't exist
      shadowRoot = container.attachShadow({ mode: 'open' });
    }
    shadowRootRef.current = shadowRoot;

    // Enhanced styles with role-specific colors
    const styles = `
      :host {
        position: fixed;
        top: 0;
        right: ${isOpen ? '0' : '-320px'};
        height: 100vh;
        width: 320px;
        z-index: 10000;
        transition: right 0.3s ease-in-out;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .drawer {
        height: 100vh;
        background: linear-gradient(180deg, #1a1a2e 0%, #16213e 100%);
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: column;
        overflow-y: auto;
      }

      .header {
        padding: 24px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        background: rgba(0, 0, 0, 0.2);
      }

      .user-info {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 20px;
        color: white;
        border: 3px solid transparent;
        position: relative;
      }

      .avatar.role-superadmin { border-color: #ef4444; }
      .avatar.role-admin { border-color: #f97316; }
      .avatar.role-coach { border-color: #3b82f6; }
      .avatar.role-player { border-color: #10b981; }
      .avatar.role-parent { border-color: #8b5cf6; }

      .avatar img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }

      .user-details h3 {
        color: white;
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
      }

      .user-details p {
        color: #a0a0a0;
        margin: 0;
        font-size: 14px;
        text-transform: capitalize;
      }

      .role-selector {
        margin: 16px 24px;
        padding: 16px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .role-selector h4 {
        color: white;
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .role-buttons {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .role-button {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        padding: 12px;
        color: white;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
        text-transform: capitalize;
        position: relative;
        overflow: hidden;
      }

      .role-button:hover {
        background: rgba(255, 255, 255, 0.15);
        border-color: rgba(255, 255, 255, 0.3);
      }

      .role-button.active {
        background: var(--active-bg);
        border-color: var(--active-border);
        box-shadow: 0 0 20px var(--active-glow);
      }

      .role-button.active.role-superadmin {
        --active-bg: rgba(239, 68, 68, 0.2);
        --active-border: #ef4444;
        --active-glow: rgba(239, 68, 68, 0.3);
      }

      .role-button.active.role-admin {
        --active-bg: rgba(249, 115, 22, 0.2);
        --active-border: #f97316;
        --active-glow: rgba(249, 115, 22, 0.3);
      }

      .role-button.active.role-coach {
        --active-bg: rgba(59, 130, 246, 0.2);
        --active-border: #3b82f6;
        --active-glow: rgba(59, 130, 246, 0.3);
      }

      .role-button.active.role-player {
        --active-bg: rgba(16, 185, 129, 0.2);
        --active-border: #10b981;
        --active-glow: rgba(16, 185, 129, 0.3);
      }

      .role-button.active.role-parent {
        --active-bg: rgba(139, 92, 246, 0.2);
        --active-border: #8b5cf6;
        --active-glow: rgba(139, 92, 246, 0.3);
      }

      .role-indicator {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
        background: var(--active-border);
        opacity: 0;
        transition: opacity 0.2s;
      }

      .role-button.active .role-indicator {
        opacity: 1;
      }

      .checkmark {
        color: var(--active-border);
        font-weight: bold;
        opacity: 0;
        transition: opacity 0.2s;
      }

      .role-button.active .checkmark {
        opacity: 1;
      }

      .navigation-section {
        padding: 24px;
        flex: 1;
      }

      .nav-button {
        width: 100%;
        background: transparent;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 16px;
        color: white;
        cursor: pointer;
        margin-bottom: 12px;
        transition: all 0.2s;
        font-size: 14px;
        text-align: left;
      }

      .nav-button:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
      }

      .footer {
        padding: 24px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        margin-top: auto;
      }

      .close-button {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.1);
        border: none;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        transition: background 0.2s;
      }

      .close-button:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      .debug-info {
        margin: 16px 24px;
        padding: 12px;
        background: rgba(255, 193, 7, 0.1);
        border: 1px solid rgba(255, 193, 7, 0.3);
        border-radius: 8px;
        color: #ffc107;
        font-size: 12px;
        line-height: 1.4;
      }
    `;

    const drawerHTML = `
      <div class="drawer">
        <button class="close-button">×</button>
        
        <div class="header">
          <div class="user-info">
            <div class="avatar ${currentRole ? `role-${currentRole}` : ''}">
              ${userAvatar ? `<img src="${userAvatar}" alt="${userName}">` : userName.charAt(0).toUpperCase()}
            </div>
            <div class="user-details">
              <h3>${userName}</h3>
              <p>${roleInfo?.displayName || currentRole || userRole || 'User'}</p>
            </div>
          </div>
        </div>

        ${availableRoles.length > 1 ? `
          <div class="role-selector">
            <h4>Switch Role</h4>
            <div class="role-buttons">
              ${availableRoles.map(role => `
                <button class="role-button ${role === currentRole ? 'active' : ''} role-${role}" data-role="${role}">
                  <div class="role-indicator"></div>
                  <span>${role.charAt(0).toUpperCase() + role.slice(1)}</span>
                  <span class="checkmark">✓</span>
                </button>
              `).join('')}
            </div>
          </div>
        ` : ''}

        ${debugMode && availableRoles.length <= 1 ? `
          <div class="debug-info">
            Debug: Role selector hidden because user has ${availableRoles.length} role(s).
            Current: ${currentRole}, Available: [${availableRoles.join(', ')}]
          </div>
        ` : ''}

        <div class="navigation-section">
          <button class="nav-button" data-action="profile">👤 Profile</button>
          <button class="nav-button" data-action="settings">⚙️ Settings</button>
        </div>

        <div class="footer">
          <button class="nav-button" data-action="logout">🚪 Sign Out</button>
        </div>
      </div>
    `;

    // Apply styles and HTML
    shadowRoot.innerHTML = `<style>${styles}</style>${drawerHTML}`;

    // Event listeners
    const closeButton = shadowRoot.querySelector('.close-button');
    closeButton?.addEventListener('click', onToggle);

    // Role button event listeners
    const roleButtons = shadowRoot.querySelectorAll('.role-button');
    roleButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const role = (e.currentTarget as HTMLElement).getAttribute('data-role');
        if (role) {
          handleRoleSwitch(role);
        }
      });
    });

    // Navigation button event listeners
    const navButtons = shadowRoot.querySelectorAll('[data-action]');
    navButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const action = (e.currentTarget as HTMLElement).getAttribute('data-action');
        switch (action) {
          case 'profile':
            onProfileClick?.();
            break;
          case 'settings':
            onSettingsClick?.();
            break;
          case 'logout':
            onLogoutClick?.();
            break;
        }
        onToggle(); // Close drawer after action
      });
    });

    return () => {
      if (shadowRootRef.current) {
        shadowRootRef.current.innerHTML = '';
      }
    };
  }, [isOpen, currentRole, availableRoles, roleInfo, userName, userAvatar, userRole, onToggle, onProfileClick, onSettingsClick, onLogoutClick]);

  return (
    <div
      ref={containerRef}
      style={{ position: 'fixed', zIndex: 10000, pointerEvents: isOpen ? 'auto' : 'none' }}
    />
  );
};

export default ShadowNavigationDrawerEnhanced;