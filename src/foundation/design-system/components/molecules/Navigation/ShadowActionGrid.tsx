// ABOUTME: Shadow DOM action grid component for displaying actionable grid items
// Provides a responsive grid of clickable items with icons and labels

import React, { useEffect, useRef } from 'react';
export interface ActionItem {
  id: string;
  icon: string;
  label: string;
  onClick?: () => void;
  dimmed?: boolean;
}

export interface ShadowActionGridProps {
  items: ActionItem[];
  columns?: 2 | 3 | 4;
  className?: string;
}

// Define icon paths - these map to common icon names
const iconPaths: Record<string, string> = {
  calendar: 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z',
  users: 'M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2 M9 7a4 4 0 100-8 4 4 0 000 8z M23 21v-2a4 4 0 00-3-3.87 M16 3.13a4 4 0 010 7.75',
  send: 'M22 2L11 13 M22 2l-7 20-4-9-9-4 20-7z',
  share: 'M18 8a3 3 0 01-3 3 3 3 0 01-3-3 3 3 0 013-3 3 3 0 013 3zM6 15a3 3 0 01-3 3 3 3 0 01-3-3 3 3 0 013-3 3 3 0 013 3z M18 20a3 3 0 01-3 3 3 3 0 01-3-3 3 3 0 013-3 3 3 0 013 3z M8.59 13.51l6.83 3.98 M15.41 6.51l-6.82 3.98',
  edit: 'M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7 M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z',
  cog: 'M12 2a10 10 0 100 20 10 10 0 000-20zm0 0v20m0-10h10m-10 0H2 M16.24 7.76l-8.48 8.48 M7.76 7.76l8.48 8.48',
  settings: 'M12 15a3 3 0 100-6 3 3 0 000 6z M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z',
  'user-plus': 'M16 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2 M8 7a4 4 0 100-8 4 4 0 000 8z M20 8v6 M23 11h-6',
  'message-circle': 'M21 11.5a8.38 8.38 0 01-.9 3.8 8.5 8.5 0 01-7.6 4.7 8.38 8.38 0 01-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 01-.9-3.8 8.5 8.5 0 014.7-7.6 8.38 8.38 0 013.8-.9h.5a8.48 8.48 0 018 8v.5z',
  // Additional common icons
  plus: 'M12 5v14m-7-7h14',
  plusCircle: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z M12 7v10 M7 12h10',
  qrCode: 'M3 7h3v3H3zm0 8h3v3H3zm8-8h3v3h-3zm8 0v3h3V7zm0 8h3v3h-3zM8 12h3v3H8zm0 4h3v3H8zm4-4h3v3h-3zm4 4h3v3h-3zm0-8h3v3h-3z',
  refresh: 'M1 4v6h6 M23 20v-6h-6 M20.49 9A9 9 0 005.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 013.51 15',
  check: 'M20 6L9 17l-5-5',
  x: 'M18 6L6 18 M6 6l12 12',
  target: 'M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0-6 0 M12 12m-8 0a8 8 0 1 0 16 0a8 8 0 1 0-16 0 M12 2v2 M12 20v2 M2 12h2 M20 12h2',
  chart: 'M12 20V10 M6 20V4 M18 20v-4',
  'calendar-days': 'M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z M8 15h.01M12 15h.01M16 15h.01M8 18h.01M12 18h.01M16 18h.01',
  'bar-chart-2': 'M18 20V10m-6 10V4M6 20v-6'
};

export const ShadowActionGrid: React.FC<ShadowActionGridProps> = ({
  items,
  columns = 2,
  className = ''
}) => {
  const hostRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);

  useEffect(() => {
    if (!hostRef.current || shadowRef.current) return;

    // Create shadow root


    

    shadowRef.current = hostRef.current.attachShadow({ mode: 'open' });

    // Create styles
    const styles = `
      <style>
      :host {
        display: block;
        width: 100%;
      }
      
      .action-grid {
        display: grid;
        gap: 1rem;
        width: 100%;
        grid-template-columns: repeat(${columns}, 1fr);
      }
      
      .action-item {
        background: #0A0A0A;
        border: 1px solid #3A3A3A;
        border-radius: 1rem;
        padding: 2rem 1.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 0.75rem;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
        min-height: 140px;
      }
      
      .action-item:hover {
        background: #1A1A1A;
        border-color: #1ABC9C80;
        transform: translateY(-2px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      }
      
      .action-item:active {
        transform: translateY(0);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }
      
      .action-icon {
        width: 32px;
        height: 32px;
        color: #FFFFFF;
        transition: all 0.2s ease;
      }
      
      .action-icon svg {
        width: 100%;
        height: 100%;
      }
      
      .action-item:hover .action-icon {
        color: #1ABC9C;
        transform: scale(1.1);
      }
      
      .action-label {
        font-family: 'Poppins', sans-serif;
        font-weight: 600;
        font-size: 1rem;
        color: #FFFFFF;
        text-align: center;
        margin: 0;
        transition: color 0.2s ease, background-color 0.2s ease;
      }
      
      .action-item:hover .action-label {
        color: #1ABC9C;
      }
      
      /* Dimmed items */
      .action-item.dimmed .action-icon {
        color: #808080;
      }
      
      .action-item.dimmed .action-label {
        color: #808080;
      }
      
      .action-item.dimmed:hover .action-icon,
      .action-item.dimmed:hover .action-label {
        color: #A0A0A0;
      }
      
      /* Subtle gradient overlay */
      .action-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, 
          rgba(255, 255, 255, 0.03) 0%, 
          rgba(255, 255, 255, 0) 100%);
        pointer-events: none;
      }
      
      /* Focus styles for accessibility */
      .action-item:focus-visible {
        outline: 2px solid #1ABC9C60;
        outline-offset: 2px;
      }
      
      @media (max-width: 640px) {
        .action-grid {
          grid-template-columns: repeat(${columns === 4 ? 2 : columns}, 1fr);
        }
        
        .action-item {
          padding: 1.5rem 1rem;
          min-height: 120px;
        }
        
        .action-icon {
          width: 28px;
          height: 28px;
        }
        
        .action-label {
          font-size: 0.875rem;
        }
      }
      
      ${className ? `.custom { ${className} }` : ''}
    </style>
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Cleanup
    return () => {
      if (shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, [columns, className]);

  useEffect(() => {
    if (!shadowRef.current) return;

    // Clear existing content except style
    const style = shadowRef.current.querySelector('style');
    shadowRef.current.innerHTML = '';
    if (style) shadowRef.current.appendChild(style);

    // Create grid container
    const gridDiv = document.createElement('div');
    gridDiv.className = 'action-grid';
    if (className) gridDiv.classList.add('custom');

    // Create action items
    items.forEach((item) => {
      const itemDiv = document.createElement('div');
      itemDiv.className = item.dimmed ? 'action-item dimmed' : 'action-item';
      itemDiv.setAttribute('role', 'button');
      itemDiv.setAttribute('tabindex', '0');
      
      if (item.onClick) {
        itemDiv.addEventListener('click', item.onClick);
        itemDiv.addEventListener('keydown', (e: KeyboardEvent) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            item.onClick!();
          }
        });
      }
      
      // Create icon
      const iconDiv = document.createElement('div');
      iconDiv.className = 'action-icon';
      
      // Create SVG based on icon name
      const svgNS = 'http://www.w3.org/2000/svg';
      const svg = document.createElementNS(svgNS, 'svg');
      svg.setAttribute('viewBox', '0 0 24 24');
      svg.setAttribute('fill', 'none');
      svg.setAttribute('stroke', 'currentColor');
      svg.setAttribute('stroke-width', '2');
      svg.setAttribute('stroke-linecap', 'round');
      svg.setAttribute('stroke-linejoin', 'round');
      
      const pathData = iconPaths[item.icon] || iconPaths.calendar;
      const path = document.createElementNS(svgNS, 'path');
      path.setAttribute('d', pathData);
      svg.appendChild(path);
      
      iconDiv.appendChild(svg);
      itemDiv.appendChild(iconDiv);
      
      // Create label
      const labelElement = document.createElement('p');
      labelElement.className = 'action-label';
      labelElement.textContent = item.label;
      itemDiv.appendChild(labelElement);
      
      gridDiv.appendChild(itemDiv);
    });
    
    shadowRef.current.appendChild(gridDiv);
  }, [items]);

  return <div ref={hostRef} />;
};