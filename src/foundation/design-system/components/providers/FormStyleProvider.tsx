// ABOUTME: Centralized form style provider that injects consistent styling to all child form elements
// Provides the single source of truth for form styling across the application

import React, { createContext, useContext, ReactNode } from 'react';

// Form theme configuration
export interface FormTheme {
  colors: {
    background: string;
    border: string;
    borderFocus: string;
    borderError: string;
    text: string;
    placeholder: string;
    label: string;
    error: string;
    focusShadow: string;
  };
  spacing: {
    inputPadding: string;
    fieldGap: string;
    labelMargin: string;
  };
  typography: {
    fontSize: string;
    labelSize: string;
    errorSize: string;
  };
  borderRadius: string;
}

// Default theme based on International Address Form styling
const defaultTheme: FormTheme = {
  colors: {
    background: '#1f2937',      // Dark gray background
    border: '#374151',          // Gray border
    borderFocus: '#eab308',     // Gold border on focus
    borderError: '#ef4444',     // Red border for errors
    text: '#ffffff',            // White text
    placeholder: '#6b7280',     // Gray placeholder
    label: '#d1d5db',          // Light gray label
    error: '#ef4444',          // Red error text
    focusShadow: 'rgba(234, 179, 8, 0.1)', // Gold shadow
  },
  spacing: {
    inputPadding: '0.625rem 0.75rem', // 10px 12px
    fieldGap: '0.5rem',
    labelMargin: '0.5rem',
  },
  typography: {
    fontSize: '1rem',
    labelSize: '0.875rem',
    errorSize: '0.75rem',
  },
  borderRadius: '0.5rem', // 8px
};

// Context for form styling
const FormStyleContext = createContext<{
  theme: FormTheme;
  getInputStyles: () => string;
  getLabelStyles: () => string;
  getErrorStyles: () => string;
  getFormFieldStyles: () => string;
} | null>(null);

interface FormStyleProviderProps {
  children: ReactNode;
  theme?: Partial<FormTheme>;
  injectStyles?: boolean;
}

export const FormStyleProvider: React.FC<FormStyleProviderProps> = ({
  children,
  theme: customTheme,
  injectStyles = true,
}) => {
  // Merge custom theme with default
  const theme: FormTheme = {
    ...defaultTheme,
    ...customTheme,
    colors: { ...defaultTheme.colors, ...customTheme?.colors },
    spacing: { ...defaultTheme.spacing, ...customTheme?.spacing },
    typography: { ...defaultTheme.typography, ...customTheme?.typography },
  };

  // Generate consistent styles for form elements
  const getInputStyles = () => `
    width: 100%;
    padding: ${theme.spacing.inputPadding};
    background-color: ${theme.colors.background};
    border: 1px solid ${theme.colors.border};
    border-radius: ${theme.borderRadius};
    color: ${theme.colors.text};
    font-size: ${theme.typography.fontSize};
    transition: border-color 0.2s, box-shadow 0.2s;
    outline: none;
    
    &::placeholder {
      color: ${theme.colors.placeholder};
    }
    
    &:focus {
      border-color: ${theme.colors.borderFocus};
      box-shadow: 0 0 0 3px ${theme.colors.focusShadow};
    }
    
    &.error {
      border-color: ${theme.colors.borderError};
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  `;

  const getLabelStyles = () => `
    display: block;
    font-size: ${theme.typography.labelSize};
    font-weight: 500;
    color: ${theme.colors.label};
    margin-bottom: ${theme.spacing.labelMargin};
    
    &.required::after {
      content: ' *';
      color: ${theme.colors.error};
    }
  `;

  const getErrorStyles = () => `
    font-size: ${theme.typography.errorSize};
    color: ${theme.colors.error};
    margin-top: 0.25rem;
  `;

  const getFormFieldStyles = () => `
    display: flex;
    flex-direction: column;
    gap: ${theme.spacing.fieldGap};
    margin-bottom: 1rem;
  `;

  // Global styles to inject
  const globalFormStyles = `
    /* Form Field Container */
    .shot-form-field {
      ${getFormFieldStyles()}
    }
    
    /* Labels */
    .shot-form-label {
      ${getLabelStyles()}
    }
    
    /* Input Elements */
    .shot-form-input,
    .shot-form-select,
    .shot-form-textarea {
      ${getInputStyles()}
    }
    
    /* Error Messages */
    .shot-form-error {
      ${getErrorStyles()}
    }
    
    /* Form Container */
    .shot-form-container {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
    
    /* Form Row Layouts */
    .shot-form-row {
      display: grid;
      gap: 1rem;
    }
    
    .shot-form-row.two-column {
      grid-template-columns: 1fr 1fr;
    }
    
    @media (max-width: 640px) {
      .shot-form-row.two-column {
        grid-template-columns: 1fr;
      }
    }
  `;

  const contextValue = {
    theme,
    getInputStyles,
    getLabelStyles,
    getErrorStyles,
    getFormFieldStyles,
  };

  return (
    <FormStyleContext.Provider value={contextValue}>
      {injectStyles && (
        <style dangerouslySetInnerHTML={{ __html: globalFormStyles }} />
      )}
      {children}
    </FormStyleContext.Provider>
  );
};

// Hook to use form styles in components
export const useFormStyles = () => {
  const context = useContext(FormStyleContext);
  if (!context) {
    throw new Error('useFormStyles must be used within a FormStyleProvider');
  }
  return context;
};

// Helper to get CSS string for Shadow DOM injection
export const getFormStylesForShadowDOM = (theme?: Partial<FormTheme>) => {
  const mergedTheme: FormTheme = {
    ...defaultTheme,
    ...theme,
    colors: { ...defaultTheme.colors, ...theme?.colors },
    spacing: { ...defaultTheme.spacing, ...theme?.spacing },
    typography: { ...defaultTheme.typography, ...theme?.typography },
  };

  return `
    /* Shadow DOM Form Styles */
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="number"],
    input[type="tel"],
    input[type="url"],
    input[type="search"],
    input[type="date"],
    select,
    textarea {
      width: 100%;
      padding: ${mergedTheme.spacing.inputPadding};
      background-color: ${mergedTheme.colors.background};
      border: 1px solid ${mergedTheme.colors.border};
      border-radius: ${mergedTheme.borderRadius};
      color: ${mergedTheme.colors.text};
      font-size: ${mergedTheme.typography.fontSize};
      transition: border-color 0.2s, box-shadow 0.2s;
      outline: none;
    }
    
    input::placeholder,
    textarea::placeholder {
      color: ${mergedTheme.colors.placeholder};
    }
    
    input:focus,
    select:focus,
    textarea:focus {
      border-color: ${mergedTheme.colors.borderFocus};
      box-shadow: 0 0 0 3px ${mergedTheme.colors.focusShadow};
    }
    
    input.error,
    select.error,
    textarea.error {
      border-color: ${mergedTheme.colors.borderError};
    }
    
    input:disabled,
    select:disabled,
    textarea:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    
    label {
      display: block;
      font-size: ${mergedTheme.typography.labelSize};
      font-weight: 500;
      color: ${mergedTheme.colors.label};
      margin-bottom: ${mergedTheme.spacing.labelMargin};
    }
    
    label.required::after {
      content: ' *';
      color: ${mergedTheme.colors.error};
    }
    
    .error-message {
      font-size: ${mergedTheme.typography.errorSize};
      color: ${mergedTheme.colors.error};
      margin-top: 0.25rem;
    }
  `;
};

export default FormStyleProvider;