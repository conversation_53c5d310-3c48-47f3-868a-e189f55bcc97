// ABOUTME: Shadow DOM component for switching between linked accounts (parent/child accounts)
// Provides a compact, horizontal scrollable UI for account selection with avatars and usernames

import React, { useEffect, useRef } from 'react';
export interface Account {
  id: string;
  name: string;
  avatarUrl?: string;
  role?: string;
  isChild?: boolean;
}

export interface ShadowAccountSwitcherProps {
  accounts: Account[];
  currentAccountId: string;
  onAccountSelect: (accountId: string) => void;
  variant?: 'horizontal-bar' | 'dropdown' | 'pills' | 'compact-with-arrows' | 'compact-selected-expanded' | 'compact-with-add';
  size?: 'sm' | 'md';
  className?: string;
  navigationPath?: string;
  showAddButton?: boolean;
  addButtonUrl?: string;
  addButtonTooltip?: string;
}

const sizeConfig = {
  sm: {
    avatar: '32px',
    fontSize: '0.875rem',
    padding: '0.5rem',
    gap: '0.5rem',
  },
  md: {
    avatar: '40px',
    fontSize: '1rem',
    padding: '0.75rem',
    gap: '0.75rem',
  },
};

export const ShadowAccountSwitcher: React.FC<ShadowAccountSwitcherProps> = ({
  accounts = [],
  currentAccountId,
  onAccountSelect,
  variant = 'horizontal-bar',
  size = 'md',
  className = '',
  showAddButton = false,
  addButtonUrl = '/add-profile',
  addButtonTooltip = 'Add new profile'
}) => {
  const hostRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);

  useEffect(() => {
    if (!hostRef.current || shadowRef.current) return;

    // Create shadow root


    

    shadowRef.current = hostRef.current.attachShadow({ mode: 'open' });

    // Create styles
    const sizeStyles = sizeConfig[size];
    
    const styles = `
      <style>
      :host {
        display: block;
        width: 100%;
      }

      .switcher-container {
        background: rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(10px);
        border: 1px solid #3A3A3A;
        border-radius: 0.75rem;
        padding: 0.5rem;
        position: relative;
      }

      /* Horizontal Bar Variant */
      .horizontal-bar {
        display: flex;
        gap: ${sizeStyles.gap};
        overflow-x: auto;
        scrollbar-width: thin;
        scrollbar-color: #3A3A3A transparent;
      }

      .horizontal-bar::-webkit-scrollbar {
        height: 4px;
      }

      .horizontal-bar::-webkit-scrollbar-track {
        background: transparent;
      }

      .horizontal-bar::-webkit-scrollbar-thumb {
        background: #3A3A3A;
        border-radius: 2px;
      }

      /* Account Item */
      .account-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: ${sizeStyles.padding};
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 2px solid transparent;
        background: transparent;
        white-space: nowrap;
        flex-shrink: 0;
      }

      .account-item:hover {
        background: rgba(255, 255, 255, 0.05);
      }

      .account-item.active {
        background: #6B00DB20;
        border-color: #6B00DB;
      }

      .account-avatar {
        width: ${sizeStyles.avatar};
        height: ${sizeStyles.avatar};
        border-radius: 50%;
        background: #6B00DB;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: ${sizeStyles.fontSize};
        overflow: hidden;
        flex-shrink: 0;
      }

      .account-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .account-info {
        display: flex;
        flex-direction: column;
        min-width: 0;
      }

      .account-name {
        font-size: ${sizeStyles.fontSize};
        font-weight: 500;
        color: #FFFFFF;
        line-height: 1.2;
      }

      .account-role {
        font-size: 0.75rem;
        color: #B3B3B3;
        line-height: 1.2;
        margin-top: 0.25rem;
      }

      /* Dropdown Variant */
      .dropdown {
        position: relative;
      }

      .dropdown-trigger {
        display: flex;
        align-items: center;
        gap: ${sizeStyles.gap};
        padding: ${sizeStyles.padding};
        background: transparent;
        border: 1px solid #3A3A3A;
        border-radius: 0.5rem;
        cursor: pointer;
        width: 100%;
        transition: all 0.2s ease;
      }

      .dropdown-trigger:hover {
        border-color: #6B00DB;
      }

      .dropdown-menu {
        position: absolute;
        top: calc(100% + 0.5rem);
        left: 0;
        right: 0;
        background: #1A1A1A;
        border: 1px solid #3A3A3A;
        border-radius: 0.5rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        padding: 0.5rem;
        display: none;
        max-height: 300px;
        overflow-y: auto;
        z-index: 10;
      }

      .dropdown.open .dropdown-menu {
        display: block;
      }

      /* Pills Variant */
      .pills {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
      }

      .pills .account-item {
        border: 1px solid #3A3A3A;
      }

      /* Add Button */
      .add-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: ${sizeStyles.avatar};
        height: ${sizeStyles.avatar};
        border-radius: 50%;
        border: 2px dashed #3A3A3A;
        background: transparent;
        cursor: pointer;
        transition: all 0.2s ease;
        flex-shrink: 0;
      }

      .add-button:hover {
        border-color: #6B00DB;
        background: rgba(255, 255, 255, 0.05);
      }

      .add-icon {
        font-size: 1.25rem;
        color: #B3B3B3;
      }

      /* Compact Variants */
      .compact-with-arrows-container {
        position: relative;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .compact-with-arrows {
        display: flex;
        gap: 0.5rem;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        flex: 1;
      }

      .compact-with-arrows::-webkit-scrollbar {
        display: none;
      }

      .arrow-button {
        background: #1A1A1A;
        border: 1px solid #3A3A3A;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        flex-shrink: 0;
      }

      .arrow-button:hover {
        background: rgba(255, 255, 255, 0.05);
        border-color: #6B00DB;
      }

      .arrow-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      ${className ? `.custom { ${className} }` : ''}
    </style>
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Cleanup
    return () => {
      if (shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, [variant, size, className]);

  useEffect(() => {
    if (!shadowRef.current) return;

    // Clear existing content except style
    const style = shadowRef.current.querySelector('style');
    shadowRef.current.innerHTML = '';
    if (style) shadowRef.current.appendChild(style);

    // Create container
    const container = document.createElement('div');
    container.className = 'switcher-container';
    if (className) container.classList.add('custom');

    // Render based on variant
    switch (variant) {
      case 'dropdown':
        renderDropdown(container);
        break;
      case 'pills':
        renderPills(container);
        break;
      case 'compact-with-arrows':
        renderCompactWithArrows(container);
        break;
      case 'horizontal-bar':
      default:
        renderHorizontalBar(container);
        break;
    }

    shadowRef.current.appendChild(container);
  }, [accounts, currentAccountId, variant, showAddButton]);

  const renderHorizontalBar = (container: HTMLElement) => {
    const bar = document.createElement('div');
    bar.className = 'horizontal-bar';

    accounts.forEach(account => {
      const item = createAccountItem(account);
      bar.appendChild(item);
    });

    if (showAddButton) {
      const addBtn = createAddButton();
      bar.appendChild(addBtn);
    }

    container.appendChild(bar);
  };

  const renderDropdown = (container: HTMLElement) => {
    const dropdown = document.createElement('div');
    dropdown.className = 'dropdown';

    const currentAccount = accounts.find(a => a.id === currentAccountId);
    if (currentAccount) {
      const trigger = document.createElement('button');
      trigger.className = 'dropdown-trigger';
      trigger.innerHTML = `
        ${createAvatarHTML(currentAccount)}
        <div class="account-info">
          <div class="account-name">${currentAccount.name}</div>
          ${currentAccount.role ? `<div class="account-role">${currentAccount.role}</div>` : ''}
        </div>
        <span style="margin-left: auto;">▼</span>
      `;
      trigger.onclick = () => dropdown.classList.toggle('open');
      dropdown.appendChild(trigger);
    }

    const menu = document.createElement('div');
    menu.className = 'dropdown-menu';
    accounts.forEach(account => {
      const item = createAccountItem(account);
      menu.appendChild(item);
    });
    dropdown.appendChild(menu);

    container.appendChild(dropdown);
  };

  const renderPills = (container: HTMLElement) => {
    const pills = document.createElement('div');
    pills.className = 'pills';

    accounts.forEach(account => {
      const item = createAccountItem(account);
      pills.appendChild(item);
    });

    if (showAddButton) {
      const addBtn = createAddButton();
      pills.appendChild(addBtn);
    }

    container.appendChild(pills);
  };

  const renderCompactWithArrows = (container: HTMLElement) => {
    const wrapper = document.createElement('div');
    wrapper.className = 'compact-with-arrows-container';

    const leftArrow = document.createElement('button');
    leftArrow.className = 'arrow-button';
    leftArrow.innerHTML = '←';
    leftArrow.onclick = () => scrollAccounts('left');

    const accountsContainer = document.createElement('div');
    accountsContainer.className = 'compact-with-arrows';
    accounts.forEach(account => {
      const item = createAccountItem(account, true);
      accountsContainer.appendChild(item);
    });

    const rightArrow = document.createElement('button');
    rightArrow.className = 'arrow-button';
    rightArrow.innerHTML = '→';
    rightArrow.onclick = () => scrollAccounts('right');

    wrapper.appendChild(leftArrow);
    wrapper.appendChild(accountsContainer);
    wrapper.appendChild(rightArrow);

    container.appendChild(wrapper);
  };

  const createAccountItem = (account: Account, compactMode = false) => {
    const item = document.createElement('button');
    item.className = `account-item ${account.id === currentAccountId ? 'active' : ''}`;
    item.onclick = () => onAccountSelect(account.id);

    const avatarHTML = createAvatarHTML(account);
    
    if (compactMode || variant === 'pills') {
      item.innerHTML = avatarHTML;
    } else {
      item.innerHTML = `
        ${avatarHTML}
        <div class="account-info">
          <div class="account-name">${account.name}</div>
          ${account.role ? `<div class="account-role">${account.role}</div>` : ''}
        </div>
      `;
    }

    return item;
  };

  const createAvatarHTML = (account: Account) => {
    if (account.avatarUrl) {
      return `<div class="account-avatar"><img src="${account.avatarUrl}" alt="${account.name}" /></div>`;
    }
    return `<div class="account-avatar">${account.name[0].toUpperCase()}</div>`;
  };

  const createAddButton = () => {
    const addBtn = document.createElement('a');
    addBtn.href = addButtonUrl;
    addBtn.className = 'add-button';
    addBtn.title = addButtonTooltip;
    addBtn.innerHTML = '<span class="add-icon">+</span>';
    return addBtn;
  };

  const scrollAccounts = (direction: 'left' | 'right') => {
    const container = shadowRef.current?.querySelector('.compact-with-arrows') as HTMLElement;
    if (container) {
      const scrollAmount = 200;
      container.scrollLeft += direction === 'left' ? -scrollAmount : scrollAmount;
    }
  };

  return <div ref={hostRef} />;
};