// ABOUTME: Shadow DOM Navigation Drawer component for app-wide navigation
// Provides a flexible drawer with profile info, navigation sections, and settings

import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
export interface Achievement {
  icon: string;
  label: string;
}

export interface NavigationItem {
  label: string;
  description?: string;
  icon?: string;
  onClick: () => void;
}

export interface NavigationSection {
  title: string;
  items: NavigationItem[];
}

export interface NavigationDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  // For design system / generic navigation
  sections?: NavigationSection[];
  // For profile navigation (original props)
  user?: {
    name: string;
    role: string;
    sport: string;
    avatar?: string;
  };
  achievements?: Achievement[];
  // Dynamic configuration
  availableRoles?: string[];
  availableSports?: string[];
  currentRole?: string;
  currentSport?: string;
  onRoleChange?: (role: string) => void;
  onSportChange?: (sport: string) => void;
  onTimelineClick?: () => void;
  onRewardsClick?: () => void;
  onSettingsClick?: () => void;
  // Allow hiding sections
  showMyShot?: boolean;
  showAchievements?: boolean;
  showRoleSelector?: boolean;
  showSportSelector?: boolean;
  showSettings?: boolean;
}

export const ShadowNavigationDrawer: React.FC<NavigationDrawerProps> = ({
  isOpen,
  onClose,
  title,
  sections,
  user = {
    name: 'User',
    role: 'Player',
    sport: 'Football'
  },
  achievements = [
    { icon: '⚡', label: 'Power User' },
    { icon: '🏅', label: 'First 10k SP' },
    { icon: '🛡️', label: 'Team Captain' }
  ],
  availableRoles = ['Player', 'Coach', 'Parent', 'Member'],
  availableSports = ['Football', 'Boxing', 'Motorsport', 'Basketball', 'Tennis'],
  currentRole,
  currentSport,
  onRoleChange,
  onSportChange,
  onTimelineClick,
  onRewardsClick,
  onSettingsClick,
  showMyShot = true,
  showAchievements = true,
  showRoleSelector = true,
  showSportSelector = true,
  showSettings = true
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);

  const activeRole = currentRole || user.role;
  const activeSport = currentSport || user.sport;
  const isDesignSystemMode = sections && sections.length > 0;

  useEffect(() => {
    if (!containerRef.current || shadowRef.current) return;

    // Create shadow root


    

    shadowRef.current = containerRef.current.attachShadow({ mode: 'open' });

    // Create styles
    const styles = `
      <style>
      :host {
        --drawer-width: 320px;
      }

      .drawer-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        transition: opacity 500ms ease;
        pointer-events: none;
      }

      .drawer-overlay.open {
        opacity: 1;
        pointer-events: auto;
      }

      .drawer-container {
        position: fixed;
        top: 0;
        right: 0;
        height: 100vh;
        width: var(--drawer-width);
        background: #0A0A0A;
        transform: translateX(100%);
        transition: transform 500ms cubic-bezier(0.34, 1.56, 0.64, 1);
        z-index: 1000;
        overflow-y: auto;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
      }

      .drawer-container.open {
        transform: translateX(0);
      }

      .drawer-header {
        padding: 1.5rem;
        border-bottom: 1px solid #3A3A3A;
      }

      .drawer-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #FFFFFF;
        margin-bottom: 0.25rem;
      }

      .close-button {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: none;
        border: none;
        color: #B3B3B3;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 0.25rem;
        transition: color 0.2s ease, background-color 0.2s ease;
      }

      .close-button:hover {
        background: rgba(255, 255, 255, 0.05);
        color: #FFFFFF;
      }

      .user-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
      }

      .user-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: #6B00DB;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #FFFFFF;
        font-weight: 700;
        font-size: 1.25rem;
      }

      .user-details h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: #FFFFFF;
        margin-bottom: 0.25rem;
      }

      .user-details p {
        font-size: 0.875rem;
        color: #B3B3B3;
      }

      .achievements {
        display: flex;
        gap: 0.5rem;
        margin-top: 0.75rem;
      }

      .achievement {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
        padding: 0.5rem;
        background: #1A1A1A;
        border-radius: 0.5rem;
        border: 1px solid #3A3A3A;
      }

      .achievement-icon {
        font-size: 1.25rem;
      }

      .achievement-label {
        font-size: 0.75rem;
        color: #B3B3B3;
        text-align: center;
      }

      .drawer-content {
        padding: 1.5rem;
      }

      .nav-section {
        margin-bottom: 2rem;
      }

      .nav-section-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: #B3B3B3;
        text-transform: uppercase;
        letter-spacing: 0.025em;
        margin-bottom: 0.75rem;
      }

      .nav-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        margin-bottom: 0.5rem;
        background: transparent;
        border: none;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
        width: 100%;
        text-align: left;
      }

      .nav-item:hover {
        background: rgba(255, 255, 255, 0.05);
      }

      .nav-item-icon {
        font-size: 1.25rem;
        width: 24px;
        text-align: center;
      }

      .nav-item-content {
        flex: 1;
      }

      .nav-item-label {
        font-size: 1rem;
        font-weight: 500;
        color: #FFFFFF;
        margin-bottom: 0.25rem;
      }

      .nav-item-description {
        font-size: 0.875rem;
        color: #B3B3B3;
      }

      .selector {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
      }

      .selector-button {
        padding: 0.5rem 0.75rem;
        background: #1A1A1A;
        border: 1px solid #3A3A3A;
        border-radius: 0.5rem;
        color: #B3B3B3;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 0.875rem;
        font-weight: 500;
      }

      .selector-button:hover {
        border-color: #6B00DB;
        color: #FFFFFF;
      }

      .selector-button.active {
        background: #6B00DB;
        border-color: #6B00DB;
        color: #FFFFFF;
      }

      @media (max-width: 640px) {
        .drawer-container {
          width: 100%;
        }
      }
    </style>
    `;

    // Inject styles
    const styleElement = document.createElement('style');
    styleElement.textContent = styles;
    shadowRef.current.appendChild(styleElement);

    // Handle escape key
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };
    document.addEventListener('keydown', handleEscape);

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  useEffect(() => {
    if (!shadowRef.current) return;

    // Clear existing content except style
    const style = shadowRef.current.querySelector('style');
    shadowRef.current.innerHTML = '';
    if (style) shadowRef.current.appendChild(style);

    // Create drawer HTML
    const drawerHTML = createDrawerHTML();
    shadowRef.current.innerHTML += drawerHTML;

    // Add event listeners
    setupEventListeners();
  }, [isOpen, title, sections, user, achievements, activeRole, activeSport,
      availableRoles, availableSports, showMyShot, showAchievements,
      showRoleSelector, showSportSelector, showSettings, isDesignSystemMode]);

  const createDrawerHTML = () => {
    return `
      <div class="drawer-overlay ${isOpen ? 'open' : ''}" data-overlay></div>
      <div class="drawer-container ${isOpen ? 'open' : ''}" data-drawer>
        ${createHeaderHTML()}
        <div class="drawer-content">
          ${isDesignSystemMode ? createDesignSystemContent() : createProfileContent()}
        </div>
      </div>
    `;
  };

  const createHeaderHTML = () => {
    if (isDesignSystemMode) {
      return `
        <div class="drawer-header">
          <button class="close-button" data-close>✕</button>
          <h2 class="drawer-title">${title || 'Navigation'}</h2>
        </div>
      `;
    }

    return `
      <div class="drawer-header">
        <button class="close-button" data-close>✕</button>
        <div class="user-info">
          <div class="user-avatar">
            ${user.avatar ? `<img src="${user.avatar}" alt="${user.name}" />` : user.name[0]}
          </div>
          <div class="user-details">
            <h3>${user.name}</h3>
            <p>${activeRole} • ${activeSport}</p>
          </div>
        </div>
        ${showAchievements ? createAchievementsHTML() : ''}
      </div>
    `;
  };

  const createAchievementsHTML = () => {
    return `
      <div class="achievements">
        ${achievements.map(a => `
          <div class="achievement">
            <span class="achievement-icon">${a.icon}</span>
            <span class="achievement-label">${a.label}</span>
          </div>
        `).join('')}
      </div>
    `;
  };

  const createDesignSystemContent = () => {
    return sections!.map(section => `
      <div class="nav-section">
        <h3 class="nav-section-title">${section.title}</h3>
        ${section.items.map((item, index) => `
          <button class="nav-item" data-nav-item="${section.title}-${index}">
            ${item.icon ? `<span class="nav-item-icon">${item.icon}</span>` : ''}
            <div class="nav-item-content">
              <div class="nav-item-label">${item.label}</div>
              ${item.description ? `<div class="nav-item-description">${item.description}</div>` : ''}
            </div>
          </button>
        `).join('')}
      </div>
    `).join('');
  };

  const createProfileContent = () => {
    const content = [];

    if (showMyShot) {
      content.push(`
        <div class="nav-section">
          <h3 class="nav-section-title">My SHOT</h3>
          <button class="nav-item" data-timeline>
            <span class="nav-item-icon">📊</span>
            <div class="nav-item-content">
              <div class="nav-item-label">Timeline & Progress</div>
            </div>
          </button>
          <button class="nav-item" data-rewards>
            <span class="nav-item-icon">🎁</span>
            <div class="nav-item-content">
              <div class="nav-item-label">SP & Rewards</div>
            </div>
          </button>
        </div>
      `);
    }

    if (showRoleSelector) {
      content.push(`
        <div class="nav-section">
          <h3 class="nav-section-title">Switch Role</h3>
          <div class="selector">
            ${availableRoles.map(role => `
              <button class="selector-button ${role === activeRole ? 'active' : ''}" 
                      data-role="${role}">${role}</button>
            `).join('')}
          </div>
        </div>
      `);
    }

    if (showSportSelector) {
      content.push(`
        <div class="nav-section">
          <h3 class="nav-section-title">Sport</h3>
          <div class="selector">
            ${availableSports.map(sport => `
              <button class="selector-button ${sport === activeSport ? 'active' : ''}" 
                      data-sport="${sport}">${sport}</button>
            `).join('')}
          </div>
        </div>
      `);
    }

    if (showSettings) {
      content.push(`
        <div class="nav-section">
          <button class="nav-item" data-settings>
            <span class="nav-item-icon">⚙️</span>
            <div class="nav-item-content">
              <div class="nav-item-label">Settings</div>
            </div>
          </button>
        </div>
      `);
    }

    return content.join('');
  };

  const setupEventListeners = () => {
    if (!shadowRef.current) return;

    // Close on overlay click
    const overlay = shadowRef.current.querySelector('[data-overlay]');
    overlay?.addEventListener('click', onClose);

    // Close button
    const closeBtn = shadowRef.current.querySelector('[data-close]');
    closeBtn?.addEventListener('click', onClose);

    // Design system navigation items
    if (isDesignSystemMode && sections) {
      sections.forEach((section, sectionIndex) => {
        section.items.forEach((item, itemIndex) => {
          const navItem = shadowRef.current!.querySelector(`[data-nav-item="${section.title}-${itemIndex}"]`);
          navItem?.addEventListener('click', () => {
            item.onClick();
            onClose();
          });
        });
      });
    }

    // Profile mode handlers
    if (!isDesignSystemMode) {
      // Timeline click
      const timeline = shadowRef.current.querySelector('[data-timeline]');
      timeline?.addEventListener('click', () => {
        onTimelineClick?.();
        onClose();
      });

      // Rewards click
      const rewards = shadowRef.current.querySelector('[data-rewards]');
      rewards?.addEventListener('click', () => {
        onRewardsClick?.();
        onClose();
      });

      // Settings click
      const settings = shadowRef.current.querySelector('[data-settings]');
      settings?.addEventListener('click', () => {
        onSettingsClick?.();
        onClose();
      });

      // Role buttons
      shadowRef.current.querySelectorAll('[data-role]').forEach((btn) => {
        btn.addEventListener('click', (e) => {
          const role = (e.target as HTMLElement).getAttribute('data-role');
          if (role) onRoleChange?.(role);
        });
      });

      // Sport buttons
      shadowRef.current.querySelectorAll('[data-sport]').forEach((btn) => {
        btn.addEventListener('click', (e) => {
          const sport = (e.target as HTMLElement).getAttribute('data-sport');
          if (sport) onSportChange?.(sport);
        });
      });
    }
  };

  // Portal to body
  return createPortal(
    <div ref={containerRef} />,
    document.body
  );
};