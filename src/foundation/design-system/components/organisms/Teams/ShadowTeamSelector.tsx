// ABOUTME: Team selector component with multiple variations for selecting teams
// Provides 3 different visual styles for team selection interfaces

import React, { useState } from 'react';
import { ShadowInfoCard } from '../../molecules/Cards/ShadowInfoCard';

export interface Team {
  id: string;
  name: string;
  sportType?: 'football' | 'basketball' | 'tennis' | 'boxing' | 'cricket' | 'rugby' | 'hockey' | 'baseball' | 'swimming';
  memberCount?: number;
  description?: string;
  [key: string]: any;
}

interface ShadowTeamSelectorProps {
  teams: Team[];
  loading?: boolean;
  onTeamClick: (teamId: string) => void;
  variant?: 'list' | 'grid' | 'compact';
  showHeader?: boolean;
  headerText?: string;
  showMemberCount?: boolean;
  showDescription?: boolean;
  selectedTeamId?: string;
}

export const ShadowTeamSelector: React.FC<ShadowTeamSelectorProps> = ({
  teams,
  loading = false,
  onTeamClick,
  variant = 'list',
  showHeader = true,
  headerText = "Select Team",
  showMemberCount = false,
  showDescription = false,
  selectedTeamId
}) => {
  if (loading) {
    return (
      <div className="px-4 pt-4">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-700 rounded w-32 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-20 bg-gray-800 rounded-xl"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const renderListVariant = () => (
    <div className="px-4 pt-4">
      {teams.length > 0 ? (
        <>
          {showHeader && (
            <h2 className="text-white text-xl font-semibold mb-4">{headerText}</h2>
          )}
          
          <div className="max-w-3xl space-y-3">
            {teams.map(team => (
              <div key={team.id} className={`relative ${selectedTeamId === team.id ? 'ring-2 ring-teal-500 rounded-xl' : ''}`}>
                <ShadowInfoCard
                  variant="action"
                  title={team.name}
                  description={showDescription ? team.description : showMemberCount && team.memberCount ? `${team.memberCount} members` : undefined}
                  icon="chevronRight"
                  size="medium"
                  sportIcon={team.sportType}
                  onClick={() => onTeamClick(team.id)}
                />
              </div>
            ))}
          </div>
        </>
      ) : (
        <div className="text-center py-8 px-4 w-full">
          <p className="text-gray-400 mb-4">No teams available</p>
          <p className="text-gray-500 text-sm">Contact your administrator to get assigned to teams</p>
        </div>
      )}
    </div>
  );

  const renderGridVariant = () => (
    <div className="px-4 pt-4">
      {teams.length > 0 ? (
        <>
          {showHeader && (
            <h2 className="text-white text-xl font-semibold mb-4">{headerText}</h2>
          )}
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 max-w-6xl">
            {teams.map(team => (
              <div 
                key={team.id} 
                className={`transform transition-all duration-200 hover:scale-105 ${
                  selectedTeamId === team.id ? 'ring-2 ring-teal-500 rounded-xl' : ''
                }`}
              >
                <div className="bg-gray-800 rounded-xl p-6 cursor-pointer hover:bg-gray-700 transition-colors" 
                     onClick={() => onTeamClick(team.id)}>
                  <div className="flex items-center gap-3 mb-3">
                    {team.sportType && (
                      <span className="text-3xl">
                        {(() => {
                          switch(team.sportType) {
                            case 'football': return '⚽';
                            case 'basketball': return '🏀';
                            case 'tennis': return '🎾';
                            case 'boxing': return '🥊';
                            case 'cricket': return '🏏';
                            case 'rugby': return '🏉';
                            case 'hockey': return '🏑';
                            case 'baseball': return '⚾';
                            case 'swimming': return '🏊';
                            default: return '';
                          }
                        })()}
                      </span>
                    )}
                    <h3 className="text-white text-lg font-semibold flex-1">{team.name}</h3>
                  </div>
                  {showDescription && team.description && (
                    <p className="text-gray-400 text-sm mb-2 line-clamp-2">{team.description}</p>
                  )}
                  {showMemberCount && team.memberCount !== undefined && (
                    <p className="text-teal-400 text-sm font-medium">{team.memberCount} members</p>
                  )}
                  {selectedTeamId === team.id && (
                    <div className="mt-3 flex items-center gap-2">
                      <div className="w-2 h-2 bg-teal-500 rounded-full animate-pulse"></div>
                      <span className="text-teal-500 text-xs font-medium">Selected</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </>
      ) : (
        <div className="text-center py-8 px-4 w-full">
          <p className="text-gray-400 mb-4">No teams available</p>
          <p className="text-gray-500 text-sm">Contact your administrator to get assigned to teams</p>
        </div>
      )}
    </div>
  );

  const renderCompactVariant = () => (
    <div className="px-4 pt-4">
      {teams.length > 0 ? (
        <>
          {showHeader && (
            <h2 className="text-white text-lg font-semibold mb-3">{headerText}</h2>
          )}
          
          <div className="max-w-2xl">
            <div className="bg-gray-800/50 rounded-lg divide-y divide-gray-700 overflow-hidden border border-gray-700">
              {teams.map((team, index) => (
                <div
                  key={team.id}
                  className={`px-4 py-3 cursor-pointer hover:bg-gray-700/50 transition-all duration-200 flex items-center justify-between group ${
                    selectedTeamId === team.id ? 'bg-gray-700/70 border-l-4 border-teal-500 pl-3' : ''
                  }`}
                  onClick={() => onTeamClick(team.id)}
                >
                  <div className="flex items-center gap-3 flex-1">
                    {team.sportType && (
                      <span className="text-xl opacity-80 group-hover:opacity-100 transition-opacity">
                        {(() => {
                          switch(team.sportType) {
                            case 'football': return '⚽';
                            case 'basketball': return '🏀';
                            case 'tennis': return '🎾';
                            case 'boxing': return '🥊';
                            case 'cricket': return '🏏';
                            case 'rugby': return '🏉';
                            case 'hockey': return '🏑';
                            case 'baseball': return '⚾';
                            case 'swimming': return '🏊';
                            default: return '';
                          }
                        })()}
                      </span>
                    )}
                    <div className="flex-1">
                      <h4 className="text-white text-sm font-medium">{team.name}</h4>
                      {showMemberCount && team.memberCount !== undefined && (
                        <p className="text-gray-500 text-xs mt-0.5">{team.memberCount} members</p>
                      )}
                    </div>
                  </div>
                  <svg 
                    className={`w-5 h-5 transition-all ${
                      selectedTeamId === team.id 
                        ? 'text-teal-500 transform translate-x-1' 
                        : 'text-gray-600 group-hover:text-gray-400 group-hover:translate-x-1'
                    }`}
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              ))}
            </div>
            {showDescription && teams.some(t => t.description) && (
              <div className="mt-3 p-3 bg-gray-800/50 rounded-lg border border-gray-700">
                <p className="text-gray-500 text-xs">
                  Tip: Select a team to view more details and manage team settings
                </p>
              </div>
            )}
          </div>
        </>
      ) : (
        <div className="text-center py-6 px-4 w-full bg-gray-800/50 rounded-lg border border-gray-700">
          <p className="text-gray-400 text-sm mb-2">No teams available</p>
          <p className="text-gray-500 text-xs">Contact your administrator</p>
        </div>
      )}
    </div>
  );

  switch (variant) {
    case 'grid':
      return renderGridVariant();
    case 'compact':
      return renderCompactVariant();
    case 'list':
    default:
      return renderListVariant();
  }
};

// Export individual variants for convenience
export const TeamSelectorList = (props: Omit<ShadowTeamSelectorProps, 'variant'>) => (
  <ShadowTeamSelector {...props} variant="list" />
);

export const TeamSelectorGrid = (props: Omit<ShadowTeamSelectorProps, 'variant'>) => (
  <ShadowTeamSelector {...props} variant="grid" />
);

export const TeamSelectorCompact = (props: Omit<ShadowTeamSelectorProps, 'variant'>) => (
  <ShadowTeamSelector {...props} variant="compact" />
);