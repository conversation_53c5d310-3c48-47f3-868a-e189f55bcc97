// ABOUTME: Responsive design utilities for adaptive layouts
// Provides hooks and helpers for responsive behavior

import { useState, useEffect } from 'react';
import { theme } from '../tokens/theme';

/**
 * Hook to detect current breakpoint
 */
export const useBreakpoint = () => {
  const [breakpoint, setBreakpoint] = useState<keyof typeof theme.breakpoints.values>('xs');

  useEffect(() => {
    const checkBreakpoint = () => {
      const width = window.innerWidth;
      const breakpoints = Object.entries(theme.breakpoints.values).reverse();
      
      for (const [key, value] of breakpoints) {
        if (width >= value) {
          setBreakpoint(key as keyof typeof theme.breakpoints.values);
          break;
        }
      }
    };

    checkBreakpoint();
    window.addEventListener('resize', checkBreakpoint);
    return () => window.removeEventListener('resize', checkBreakpoint);
  }, []);

  return breakpoint;
};

/**
 * Hook to check if screen is above a certain breakpoint
 */
export const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState<boolean>(() => {
    if (typeof window !== 'undefined') {
      return window.matchMedia(query).matches;
    }
    return false;
  });

  useEffect(() => {
    const mediaQuery = window.matchMedia(query);
    
    const handleChange = (e: MediaQueryListEvent) => {
      setMatches(e.matches);
    };

    // Modern browsers
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    } 
    // Legacy browsers
    else {
      mediaQuery.addListener(handleChange);
      return () => mediaQuery.removeListener(handleChange);
    }
  }, [query]);

  return matches;
};

/**
 * Check if current screen is mobile
 */
export const useIsMobile = (): boolean => {
  return !useMediaQuery(theme.breakpoints.media.md);
};

/**
 * Check if current screen is tablet
 */
export const useIsTablet = (): boolean => {
  const isAboveMobile = useMediaQuery(theme.breakpoints.media.md);
  const isBelowDesktop = !useMediaQuery(theme.breakpoints.media.lg);
  return isAboveMobile && isBelowDesktop;
};

/**
 * Check if current screen is desktop
 */
export const useIsDesktop = (): boolean => {
  return useMediaQuery(theme.breakpoints.media.lg);
};

/**
 * Get responsive value based on current breakpoint
 */
export const useResponsiveValue = <T>(
  values: Partial<Record<keyof typeof theme.breakpoints.values, T>> & { base?: T }
): T | undefined => {
  const breakpoint = useBreakpoint();
  
  // Find the value for current breakpoint or fall back to smaller ones
  const breakpointKeys = Object.keys(theme.breakpoints.values) as Array<keyof typeof theme.breakpoints.values>;
  const currentIndex = breakpointKeys.indexOf(breakpoint);
  
  for (let i = currentIndex; i >= 0; i--) {
    const key = breakpointKeys[i];
    if (key in values) {
      return values[key];
    }
  }
  
  return values.base;
};