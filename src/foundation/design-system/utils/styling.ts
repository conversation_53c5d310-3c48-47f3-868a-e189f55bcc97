// ABOUTME: Styling utility functions for consistent component styling
// Provides helpers for creating consistent styles across components

import { theme } from '../tokens/theme';

/**
 * Combine class names conditionally
 */
export const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

/**
 * Create button styles based on variant
 */
export interface ButtonStyleOptions {
  variant?: 'primary' | 'secondary' | 'success' | 'error' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
}

export const createButtonStyles = (options: ButtonStyleOptions = {}): string => {
  const { variant = 'primary', size = 'md', fullWidth = false } = options;
  
  const baseStyles = `
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
    font-weight: ${theme.typography.fontWeight.semibold};
    transition: ${theme.animations.transition.all};
    cursor: pointer;
    border: 2px solid transparent;
    ${fullWidth ? 'width: 100%;' : ''}
  `;
  
  const sizeStyles = {
    sm: `
      padding: ${theme.spacing[2]} ${theme.spacing[3]};
      font-size: ${theme.typography.fontSize.sm};
    `,
    md: `
      padding: ${theme.spacing[3]} ${theme.spacing[4]};
      font-size: ${theme.typography.fontSize.base};
    `,
    lg: `
      padding: ${theme.spacing[4]} ${theme.spacing[6]};
      font-size: ${theme.typography.fontSize.lg};
    `,
  };
  
  const variantStyles = {
    primary: `
      background-color: ${theme.colors.primary.teal};
      color: ${theme.colors.neutral.black};
      
      :hover {
        background-color: ${theme.colors.primary.purple};
        color: ${theme.colors.neutral.white};
      }
      
      :active {
        transform: scale(0.98);
      }
    `,
    secondary: `
      background-color: ${theme.colors.primary.purple};
      color: ${theme.colors.neutral.white};
      
      :hover {
        background-color: ${theme.colors.primary.gold};
        color: ${theme.colors.neutral.black};
      }
    `,
    success: `
      background-color: ${theme.colors.semantic.success};
      color: ${theme.colors.neutral.white};
      
      :hover {
        filter: brightness(1.1);
      }
    `,
    error: `
      background-color: ${theme.colors.semantic.error};
      color: ${theme.colors.neutral.white};
      
      :hover {
        filter: brightness(0.9);
      }
    `,
    ghost: `
      background-color: transparent;
      color: ${theme.colors.dark.text.primary};
      
      :hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    `,
    outline: `
      background-color: transparent;
      color: ${theme.colors.primary.teal};
      border-color: ${theme.colors.primary.teal};
      
      :hover {
        background-color: ${theme.colors.primary.teal};
        color: ${theme.colors.neutral.black};
      }
    `,
  };
  
  return baseStyles + sizeStyles[size] + variantStyles[variant];
};

/**
 * Create card styles
 */
export interface CardStyleOptions {
  variant?: 'elevated' | 'outlined' | 'filled';
  padding?: keyof typeof theme.spacing;
}

export const createCardStyles = (options: CardStyleOptions = {}): string => {
  const { variant = 'elevated', padding = 4 } = options;
  
  const baseStyles = `
    background-color: ${theme.colors.dark.surface};
    border-radius: 0.5rem;
    padding: ${theme.spacing[padding]};
  `;
  
  const variantStyles = {
    elevated: `
      box-shadow: ${theme.shadows.md};
    `,
    outlined: `
      border: 1px solid ${theme.colors.dark.border};
    `,
    filled: `
      background-color: ${theme.colors.dark.background};
    `,
  };
  
  return baseStyles + variantStyles[variant];
};

/**
 * Generate responsive styles
 */
export const responsive = (
  styles: Partial<Record<keyof typeof theme.breakpoints.values, string>>
): string => {
  return Object.entries(styles)
    .map(([breakpoint, style]) => {
      if (breakpoint === 'xs') return style;
      return `
        ${theme.breakpoints.media[breakpoint as keyof typeof theme.breakpoints.media]} {
          ${style}
        }
      `;
    })
    .join('\n');
};