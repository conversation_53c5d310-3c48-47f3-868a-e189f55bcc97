// ABOUTME: Shared styles for player-related components in the design system
// Provides consistent styling for player lists, cards, and attendance tracking

import { theme } from '../tokens/theme';

/**
 * Common player item styles used across player list and attendance tracker
 */
export const playerItemStyles = `
  .player-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 4px;
    cursor: pointer;
    transition: all 0.2s;
    background: rgba(31, 41, 55, 0.5);
    border: 1px solid transparent;
  }

  .player-item:hover {
    background: rgba(31, 41, 55, 0.8);
    border-color: #374151;
  }

  .player-item.selected {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
  }

  .player-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .player-item.disabled:hover {
    background: rgba(31, 41, 55, 0.5);
    border-color: transparent;
  }
`;

/**
 * Avatar styles shared across player components
 */
export const avatarStyles = `
  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    background: #374151;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    flex-shrink: 0;
  }

  .avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .avatar-placeholder {
    color: #9ca3af;
    font-size: 18px;
    font-weight: 600;
  }
`;

/**
 * Player info section styles
 */
export const playerInfoStyles = `
  .player-info {
    flex: 1;
    min-width: 0;
  }

  .player-name {
    color: white;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .player-position {
    color: #9ca3af;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
`;

/**
 * Status badge styles
 */
export const statusBadgeStyles = `
  .player-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
    flex-shrink: 0;
  }

  .status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
  }

  .badge-inactive {
    background: rgba(156, 163, 175, 0.2);
    color: #9ca3af;
  }

  .badge-evaluated {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
  }
`;

/**
 * Checkbox styles for attendance tracking
 */
export const checkboxStyles = `
  .checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid #374151;
    border-radius: 4px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    flex-shrink: 0;
  }

  .checkbox.checked {
    background: #10b981;
    border-color: #10b981;
  }

  .checkbox-icon {
    width: 14px;
    height: 14px;
    color: white;
    display: none;
  }

  .checkbox.checked .checkbox-icon {
    display: block;
  }
`;

/**
 * Search input styles
 */
export const searchInputStyles = `
  .search-container {
    margin-bottom: 16px;
    position: relative;
  }

  .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    color: #6b7280;
  }

  .search-input {
    width: 100%;
    padding: 10px 12px 10px 44px;
    background: #1e2130;
    border: 1px solid #374151;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
  }

  .search-input:focus {
    border-color: #1ABC9C;
  }

  .search-input::placeholder {
    color: #9CA3AF;
  }
`;

/**
 * List container styles
 */
export const listContainerStyles = `
  .player-list {
    background: #111827;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #374151;
    padding: 8px;
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #9ca3af;
  }

  .empty-state p {
    margin-bottom: 16px;
  }
`;

/**
 * Button styles following design system patterns
 */
export const buttonStyles = `
  .button {
    width: 100%;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    outline: none;
  }

  .button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .button-primary {
    background: ${theme.colors.primary.gold};
    color: black;
  }

  .button-primary:hover:not(:disabled) {
    background: #e5a611;
    transform: translateY(-1px);
  }

  .button-secondary {
    background: ${theme.colors.primary.purple};
    color: white;
  }

  .button-secondary:hover:not(:disabled) {
    background: #5a00b8;
    transform: translateY(-1px);
  }

  .button-icon {
    width: 20px;
    height: 20px;
  }
`;

/**
 * Combine all player-related styles
 */
export const getAllPlayerStyles = () => {
  return `
    ${playerItemStyles}
    ${avatarStyles}
    ${playerInfoStyles}
    ${statusBadgeStyles}
    ${checkboxStyles}
    ${searchInputStyles}
    ${listContainerStyles}
    ${buttonStyles}
  `;
};