// ABOUTME: Shadow DOM utility functions for component creation
// Provides helpers for creating and managing Shadow DOM components

import { useEffect, useRef, MutableRefObject } from 'react';
import { theme } from '../tokens';

/**
 * Hook to create and manage a Shadow DOM root
 */
export const useShadowRoot = <T extends HTMLElement = HTMLDivElement>(
  mode: 'open' | 'closed' = 'open'
): [MutableRefObject<T | null>, ShadowRoot | null] => {
  const hostRef = useRef<T>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);

  useEffect(() => {
    if (hostRef.current && !shadowRootRef.current) {
      shadowRootRef.current = hostRef.current.attachShadow({ mode });
    }
  }, [mode]);

  return [hostRef, shadowRootRef.current];
};

/**
 * Inject styles into a Shadow DOM root
 */
export const injectStyles = (shadowRoot: ShadowRoot, styles: string): void => {
  const styleElement = document.createElement('style');
  styleElement.textContent = styles;
  shadowRoot.appendChild(styleElement);
};

/**
 * Create a base style sheet with design tokens
 */
export const createBaseStyles = (): string => {
  return `
    :host {
      /* Reset */
      box-sizing: border-box;
      
      /* Typography */
      font-family: ${theme.typography.fontFamily.primary};
      font-size: ${theme.typography.fontSize.base};
      line-height: ${theme.typography.lineHeight.normal};
      color: ${theme.colors.dark.text.primary};
      
      /* Smooth animations */
      * {
        transition: all ${theme.animations.duration.normal} ${theme.animations.easing.easeInOut};
      }
    }
    
    :host *,
    :host *::before,
    :host *::after {
      box-sizing: inherit;
    }
    
    /* Focus styles */
    :host(:focus-within) {
      outline: 2px solid ${theme.colors.primary.purple};
      outline-offset: 2px;
    }
    
    /* Disabled state */
    :host([disabled]) {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
    }
  `;
};

/**
 * Helper to create a Shadow DOM component wrapper
 */
export interface ShadowComponentOptions {
  styles?: string;
  mode?: 'open' | 'closed';
  delegatesFocus?: boolean;
}

export const createShadowComponent = (
  render: (shadowRoot: ShadowRoot) => void,
  options: ShadowComponentOptions = {}
) => {
  const { styles = '', mode = 'open', delegatesFocus = false } = options;
  
  return class extends HTMLElement {
    private shadow: ShadowRoot;
    
    constructor() {
      super();
      this.shadow = this.attachShadow({ mode, delegatesFocus });
      
      // Inject base styles and custom styles
      const allStyles = createBaseStyles() + styles;
      injectStyles(this.shadow, allStyles);
      
      // Render content
      render(this.shadow);
    }
    
    connectedCallback() {
      // Component connected to DOM
    }
    
    disconnectedCallback() {
      // Cleanup when component is removed
    }
  };
};