// ABOUTME: Typography tokens for the SHOT design system
// Font families, sizes, weights, and line heights

export const typography = {
  // Font families
  fontFamily: {
    primary: "'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
    secondary: "'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
    mono: "'Fira Code', 'Consolas', 'Monaco', monospace",
  },
  
  // Font sizes
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem',    // 48px
    '6xl': '3.75rem', // 60px
    '7xl': '4.5rem',  // 72px
    '8xl': '6rem',    // 96px
    '9xl': '8rem',    // 128px
  },
  
  // Font weights
  fontWeight: {
    thin: 100,
    extralight: 200,
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
    black: 900,
  },
  
  // Line heights
  lineHeight: {
    none: 1,
    tight: 1.25,
    snug: 1.375,
    normal: 1.5,
    relaxed: 1.625,
    loose: 1.75,
    body: 1.75,
  },
  
  // Letter spacing
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0em',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em',
  },
  
  // Text transforms
  textTransform: {
    none: 'none',
    uppercase: 'uppercase',
    lowercase: 'lowercase',
    capitalize: 'capitalize',
  },
  
  // Common text styles
  textStyles: {
    h1: {
      fontFamily: "'Poppins', sans-serif",
      fontSize: '3rem',
      fontWeight: 800,
      lineHeight: 1.25,
      letterSpacing: '-0.025em',
    },
    h2: {
      fontFamily: "'Poppins', sans-serif",
      fontSize: '2.25rem',
      fontWeight: 700,
      lineHeight: 1.25,
      letterSpacing: '-0.025em',
    },
    h3: {
      fontFamily: "'Poppins', sans-serif",
      fontSize: '1.875rem',
      fontWeight: 600,
      lineHeight: 1.375,
    },
    h4: {
      fontFamily: "'Poppins', sans-serif",
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.375,
    },
    h5: {
      fontFamily: "'Poppins', sans-serif",
      fontSize: '1.25rem',
      fontWeight: 600,
      lineHeight: 1.5,
    },
    h6: {
      fontFamily: "'Poppins', sans-serif",
      fontSize: '1.125rem',
      fontWeight: 600,
      lineHeight: 1.5,
    },
    body: {
      fontFamily: "'Montserrat', sans-serif",
      fontSize: '1rem',
      fontWeight: 400,
      lineHeight: 1.75,
    },
    bodySmall: {
      fontFamily: "'Montserrat', sans-serif",
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: 1.625,
    },
    caption: {
      fontFamily: "'Montserrat', sans-serif",
      fontSize: '0.75rem',
      fontWeight: 400,
      lineHeight: 1.5,
    },
    button: {
      fontFamily: "'Poppins', sans-serif",
      fontSize: '0.875rem',
      fontWeight: 600,
      lineHeight: 1,
      letterSpacing: '0.025em',
      textTransform: 'uppercase' as const,
    },
  },
} as const;

// Export type for TypeScript
export type TypographyTokens = typeof typography;