// ABOUTME: Combined theme object that brings together all design tokens
// This is the main export for accessing the complete design system theme

import { colors } from './colors';
import { typography } from './typography';
import { spacing } from './spacing';
import { shadows } from './shadows';
import { animations } from './animations';
import { breakpoints } from './breakpoints';

export const theme = {
  colors,
  typography,
  spacing,
  shadows,
  animations,
  breakpoints,
} as const;

export type Theme = typeof theme;

// CSS variable generator for the theme
export const generateCSSVariables = (): string => {
  let cssVars = ':root {\n';
  
  // Colors
  Object.entries(colors).forEach(([category, values]) => {
    if (typeof values === 'object') {
      Object.entries(values).forEach(([key, value]) => {
        cssVars += `  --color-${category}-${key}: ${value};\n`;
      });
    }
  });
  
  // Typography
  Object.entries(typography.fontSize).forEach(([key, value]) => {
    cssVars += `  --font-size-${key}: ${value};\n`;
  });
  
  Object.entries(typography.fontWeight).forEach(([key, value]) => {
    cssVars += `  --font-weight-${key}: ${value};\n`;
  });
  
  // Spacing
  Object.entries(spacing).forEach(([key, value]) => {
    if (typeof value === 'string') {
      cssVars += `  --spacing-${key}: ${value};\n`;
    }
  });
  
  // Animations
  Object.entries(animations.duration).forEach(([key, value]) => {
    cssVars += `  --duration-${key}: ${value};\n`;
  });
  
  cssVars += '}\n';
  return cssVars;
};