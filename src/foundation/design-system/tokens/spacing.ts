// ABOUTME: Spacing tokens for the SHOT design system
// Consistent spacing scale for margins, padding, and gaps

export const spacing = {
  // Base spacing scale (using rem units)
  0: '0',
  px: '1px',
  0.5: '0.125rem',  // 2px
  1: '0.25rem',     // 4px
  1.5: '0.375rem',  // 6px
  2: '0.5rem',      // 8px
  2.5: '0.625rem',  // 10px
  3: '0.75rem',     // 12px
  3.5: '0.875rem',  // 14px
  4: '1rem',        // 16px
  5: '1.25rem',     // 20px
  6: '1.5rem',      // 24px
  7: '1.75rem',     // 28px
  8: '2rem',        // 32px
  9: '2.25rem',     // 36px
  10: '2.5rem',     // 40px
  11: '2.75rem',    // 44px
  12: '3rem',       // 48px
  14: '3.5rem',     // 56px
  16: '4rem',       // 64px
  20: '5rem',       // 80px
  24: '6rem',       // 96px
  28: '7rem',       // 112px
  32: '8rem',       // 128px
  36: '9rem',       // 144px
  40: '10rem',      // 160px
  44: '11rem',      // 176px
  48: '12rem',      // 192px
  52: '13rem',      // 208px
  56: '14rem',      // 224px
  60: '15rem',      // 240px
  64: '16rem',      // 256px
  72: '18rem',      // 288px
  80: '20rem',      // 320px
  96: '24rem',      // 384px
} as const;

// Layout-specific spacing
export const layoutSpacing = {
  // Container padding
  containerPadding: {
    mobile: spacing[4],    // 16px
    tablet: spacing[6],    // 24px
    desktop: spacing[8],   // 32px
  },
  
  // Section spacing
  sectionSpacing: {
    small: spacing[8],     // 32px
    medium: spacing[16],   // 64px
    large: spacing[24],    // 96px
  },
  
  // Component spacing
  componentSpacing: {
    xs: spacing[2],        // 8px
    sm: spacing[3],        // 12px
    md: spacing[4],        // 16px
    lg: spacing[6],        // 24px
    xl: spacing[8],        // 32px
  },
  
  // Grid gaps
  gridGap: {
    small: spacing[4],     // 16px
    medium: spacing[6],    // 24px
    large: spacing[8],     // 32px
  },
} as const;

// Breakpoints for responsive design
export const breakpoints = {
  xs: '0px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// Z-index scale
export const zIndex = {
  auto: 'auto',
  0: 0,
  10: 10,
  20: 20,
  30: 30,
  40: 40,
  50: 50,
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modalBackdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
} as const;

// Border radius
export const borderRadius = {
  none: '0',
  sm: '0.125rem',    // 2px
  base: '0.25rem',   // 4px
  md: '0.375rem',    // 6px
  lg: '0.5rem',      // 8px
  xl: '0.75rem',     // 12px
  '2xl': '1rem',     // 16px
  '3xl': '1.5rem',   // 24px
  full: '9999px',
} as const;

// Shadows
export const shadows = {
  none: 'none',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  // Brand shadows
  purple: '0 20px 25px -5px rgba(107, 0, 219, 0.2), 0 10px 10px -5px rgba(107, 0, 219, 0.04)',
  teal: '0 20px 25px -5px rgba(26, 188, 156, 0.2), 0 10px 10px -5px rgba(26, 188, 156, 0.04)',
  gold: '0 20px 25px -5px rgba(247, 182, 19, 0.2), 0 10px 10px -5px rgba(247, 182, 19, 0.04)',
} as const;

// Export types for TypeScript
export type SpacingTokens = typeof spacing;
export type LayoutSpacingTokens = typeof layoutSpacing;
export type BreakpointTokens = typeof breakpoints;
export type ZIndexTokens = typeof zIndex;
export type BorderRadiusTokens = typeof borderRadius;
export type ShadowTokens = typeof shadows;