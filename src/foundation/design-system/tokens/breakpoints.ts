// ABOUTME: Breakpoint tokens for responsive design
// Defines screen size breakpoints for consistent responsive behavior

export const breakpoints = {
  // Breakpoint values
  values: {
    xs: 0,
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536,
  },
  
  // Media queries
  media: {
    xs: '@media (min-width: 0px)',
    sm: '@media (min-width: 640px)',
    md: '@media (min-width: 768px)',
    lg: '@media (min-width: 1024px)',
    xl: '@media (min-width: 1280px)',
    '2xl': '@media (min-width: 1536px)',
    
    // Max width queries
    xsMax: '@media (max-width: 639px)',
    smMax: '@media (max-width: 767px)',
    mdMax: '@media (max-width: 1023px)',
    lgMax: '@media (max-width: 1279px)',
    xlMax: '@media (max-width: 1535px)',
    
    // Range queries
    smOnly: '@media (min-width: 640px) and (max-width: 767px)',
    mdOnly: '@media (min-width: 768px) and (max-width: 1023px)',
    lgOnly: '@media (min-width: 1024px) and (max-width: 1279px)',
  },
  
  // Container max widths
  container: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
  },
} as const;

export type BreakpointTokens = typeof breakpoints;

// Helper functions
export const isAbove = (breakpoint: keyof typeof breakpoints.values): string => 
  `@media (min-width: ${breakpoints.values[breakpoint]}px)`;

export const isBelow = (breakpoint: keyof typeof breakpoints.values): string => {
  const values = Object.entries(breakpoints.values);
  const index = values.findIndex(([key]) => key === breakpoint);
  if (index > 0) {
    return `@media (max-width: ${values[index][1] - 1}px)`;
  }
  return '@media (max-width: 0px)';
};

export const isBetween = (
  min: keyof typeof breakpoints.values,
  max: keyof typeof breakpoints.values
): string => 
  `@media (min-width: ${breakpoints.values[min]}px) and (max-width: ${breakpoints.values[max] - 1}px)`;