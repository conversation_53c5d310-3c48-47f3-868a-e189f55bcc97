// ABOUTME: Shadow tokens for consistent elevation and depth
// Provides a standardized elevation system for the design

export const shadows = {
  // Elevation levels
  none: 'none',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
  
  // Special shadows for dark theme
  dark: {
    sm: '0 1px 2px 0 rgba(255, 255, 255, 0.05)',
    base: '0 1px 3px 0 rgba(255, 255, 255, 0.1), 0 1px 2px 0 rgba(255, 255, 255, 0.06)',
    md: '0 4px 6px -1px rgba(255, 255, 255, 0.1), 0 2px 4px -1px rgba(255, 255, 255, 0.06)',
    lg: '0 10px 15px -3px rgba(255, 255, 255, 0.1), 0 4px 6px -2px rgba(255, 255, 255, 0.05)',
  },
  
  // Colored shadows (brand colors)
  colored: {
    purple: '0 4px 14px 0 rgba(107, 0, 219, 0.4)',
    orange: '0 4px 14px 0 rgba(247, 182, 19, 0.4)',
    green: '0 4px 14px 0 rgba(26, 188, 156, 0.4)',
  },
  
  // Inner shadows
  inner: {
    sm: 'inset 0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    base: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
    md: 'inset 0 4px 6px -1px rgba(0, 0, 0, 0.1)',
  },
} as const;

export type ShadowTokens = typeof shadows;