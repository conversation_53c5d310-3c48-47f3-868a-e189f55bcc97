// ABOUTME: Color tokens for the SHOT design system
// Centralized color palette used across all components

export const colors = {
  // Primary brand colors
  primary: {
    teal: '#1ABC9C',      // TAKE YOUR SHOT
    purple: '#6B00DB',    // OWN IT
    gold: '#F7B613',      // MAKE IMPACT
    red: '#E63946',       // Alert/Error states
  },
  
  // Neutral colors
  neutral: {
    black: '#000000',
    white: '#FFFFFF',
    gray: {
      50: '#F9FAFB',
      100: '#F3F4F6',
      200: '#E5E7EB',
      300: '#D1D5DB',
      400: '#9CA3AF',
      500: '#6B7280',
      600: '#4B5563',
      700: '#374151',
      800: '#1F2937',
      900: '#111827',
    },
  },
  
  // Dark theme colors
  dark: {
    background: '#0A0A0A',
    surface: '#1A1A1A',
    surfaceLight: '#2A2A2A',
    border: '#3A3A3A',
    text: {
      primary: '#FFFFFF',
      secondary: '#B3B3B3',
      muted: '#999999',
    },
  },
  
  // Semantic colors
  semantic: {
    success: '#10B981',
    warning: '#F59E0B',
    error: '#EF4444',
    info: '#3B82F6',
  },
  
  // Social/Platform colors
  social: {
    twitter: '#1DA1F2',
    instagram: '#E4405F',
    facebook: '#1877F2',
    youtube: '#FF0000',
    twitch: '#9146FF',
    tiktok: '#000000',
  },
} as const;

// Utility function to get color with opacity
export const withOpacity = (color: string, opacity: number): string => {
  // Convert hex to RGB
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

// Export type for TypeScript
export type ColorTokens = typeof colors;