// ABOUTME: Central export for all design system tokens
// Single source of truth for design values

export * from './colors';
export * from './typography';
export * from './spacing';

// Animation tokens
export const animations = {
  duration: {
    instant: '0ms',
    fast: '150ms',
    normal: '300ms',
    slow: '500ms',
    slower: '700ms',
  },
  easing: {
    linear: 'linear',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
} as const;

// Re-export all tokens as a single theme object
import { colors } from './colors';
import { typography } from './typography';
import { spacing, layoutSpacing, breakpoints, zIndex, borderRadius, shadows } from './spacing';

export const theme = {
  colors,
  typography,
  spacing,
  layoutSpacing,
  breakpoints,
  zIndex,
  borderRadius,
  shadows,
  animations,
} as const;

export type Theme = typeof theme;