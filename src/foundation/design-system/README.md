# Design System

A comprehensive component library built with Shadow DOM for style isolation and consistency across the SHOT application.

## Architecture

### Atomic Design Structure

```
components/
├── atoms/          # Basic building blocks (Button, Input, Icon)
├── molecules/      # Simple combinations (Card, Modal, Dropdown)
├── organisms/      # Complex components (Navigation, DataTable, Forms)
└── templates/      # Page layouts (AuthLayout, DashboardLayout)
```

### Design Tokens

Centralized design decisions:
- **Colors**: Brand colors, semantic colors, gradients
- **Typography**: Font families, sizes, weights, line heights
- **Spacing**: Consistent spacing scale
- **Shadows**: Elevation system
- **Animations**: Transitions and animations

## Shadow DOM Implementation

All components use Shadow DOM for style isolation:

```typescript
export const ShadowButton: FC<ButtonProps> = ({ children, ...props }) => {
  const shadowRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (!shadowRef.current?.shadowRoot) {
      const shadow = shadowRef.current?.attachShadow({ mode: 'open' });
      // Apply styles and render content
    }
  }, []);
  
  return <div ref={shadowRef} />;
};
```

## Usage

```typescript
import { Button, Card, theme } from '@/foundation/design-system';

// Use components
<Button variant="primary" onClick={handleClick}>
  Click me
</Button>

// Access design tokens
const primaryColor = theme.colors.primary;
```

## Component Guidelines

1. **Accessibility First**: All components must be WCAG 2.1 AA compliant
2. **TypeScript**: Full type definitions for all props
3. **Testing**: Unit tests for all components
4. **Documentation**: Storybook stories for visual documentation
5. **Performance**: Lazy loading for complex components

## Migration from Old Components

When migrating from old components:
1. Check if Shadow DOM component exists
2. Update imports to use design system
3. Adjust props if API changed
4. Test thoroughly
5. Move old component to deprecated folder