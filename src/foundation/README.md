# Foundation Modules

Core infrastructure modules that all features depend on. These must be stable and well-tested as they form the foundation of the entire application.

## Modules

### 1. Design System (`/design-system`)
- **Purpose**: Unified component library using Shadow DOM
- **Structure**: Atomic design (atoms → molecules → organisms → templates)
- **Key Features**: 
  - Shadow DOM isolation
  - Design tokens
  - Theme support
  - Accessibility built-in

### 2. Authentication (`/auth`)
- **Purpose**: Centralized authentication and authorization
- **Features**:
  - Supabase integration
  - Session management
  - Role-based access control
  - Permission system

### 3. Communication (`/communication`)
- **Purpose**: Unified notification and messaging system
- **Channels**:
  - SMS notifications
  - Email communications
  - In-app notifications
  - Push notifications

## Usage

All foundation modules export from their index files:

```typescript
// Design System
import { Button, Card, Modal } from '@/foundation/design-system';

// Authentication
import { useAuth, AuthGuard } from '@/foundation/auth';

// Communication
import { sendNotification, NotificationProvider } from '@/foundation/communication';
```

## Development Guidelines

1. **Stability First**: Changes to foundation modules affect the entire app
2. **Backward Compatibility**: Never break existing APIs
3. **Comprehensive Testing**: 100% test coverage target
4. **Documentation**: Every export must be documented
5. **Performance**: These modules are used everywhere - optimize aggressively

## Module Dependencies

```
communication → auth (for user context)
     ↓
design-system (standalone)
```

The design system has no dependencies, auth is mostly standalone, and communication depends on auth for user context.