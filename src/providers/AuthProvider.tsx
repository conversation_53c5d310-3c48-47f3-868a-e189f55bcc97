// AuthProvider.tsx
import { createContext, useContext, useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";
import { useQueryClient } from "@tanstack/react-query";
import { queryKeys } from "@/lib/query-keys";
import type { Session } from "@supabase/supabase-js";

type AuthContextType = { hydrated: boolean };
const AuthContext = createContext<AuthContextType>({ hydrated: false });

export function useAuthHydration() {
  return useContext(AuthContext).hydrated;
}

export default function AuthProvider({ children }: { children: React.ReactNode }) {
  const queryClient = useQueryClient();
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    let mounted = true;

    async function hydrate() {
      let session: Session | null = null;

      const url = new URL(window.location.href);
      const hasCode = url.searchParams.get("code");

      if (hasCode) {
        const { data } = await supabase.auth.exchangeCodeForSession(window.location.href);
        if (data?.session) session = data.session;
      }

      if (!session) {
        const { data: { session: localSession } } = await supabase.auth.getSession();
        session = localSession;
      }

      if (mounted) {
        queryClient.setQueryData(queryKeys.auth.session, {
          user: session?.user ?? null,
          session,
        });
        setHydrated(true);
      }
    }

    hydrate();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      if (mounted) {
        queryClient.setQueryData(queryKeys.auth.session, {
          user: session?.user ?? null,
          session,
        });
      }
    });

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, [queryClient]);

  return (
    <AuthContext.Provider value={{ hydrated }}>
      {children}
    </AuthContext.Provider>
  );
}