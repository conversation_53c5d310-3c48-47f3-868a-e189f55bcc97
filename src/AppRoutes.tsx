import React from "react";
import { useLocation, useHistory } from "react-router-dom";
import { renderRoutes } from "@/routes/RouteRenderer";
import { publicRoutes } from "@/routes/publicRoutes";
import { protectedRoutes } from "@/routes/protectedRoutes";
import { coachRoutes } from "@/routes/coachRoutes";
import { lockerRoutes } from "@/routes/lockerRoutes";
import { evaluationRoutes } from "@/routes/evaluationRoutes";
import { redirects } from "@/routes/redirects";
import { devRoutes } from "@/routes/devRoutes";
import { v2Routes } from "@/routes/v2Routes";
import { useCurrentUser } from "@/hooks/useCurrentUser";
import { PlayerSelfEvaluationForm } from "@/pages/section/Coach/components";

const AppRoutes: React.FC = () => {
  const location = useLocation();
  const history = useHistory();
  const { user, isLoading } = useCurrentUser();
  const isDev = import.meta.env.DEV;

  // Check for pre-evaluation routes
  const isPreEvaluationRoute = 
    location.pathname.startsWith("/evaluation/pre/") ||
    location.pathname === "/pre-evaluation-test.html";
  
  const preEvaluationId = isPreEvaluationRoute
    ? location.pathname.startsWith("/evaluation/pre/")
      ? location.pathname.split("/").pop()
      : new URLSearchParams(location.search).get("id")
    : null;
  
  // Handle pre-evaluation routes (allow anonymous access)
  if (isPreEvaluationRoute && preEvaluationId) {
    return <PlayerSelfEvaluationForm preEvaluationId={preEvaluationId} />;
  }

  // Handle loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-900">
        <div className="text-white text-lg">Loading...</div>
      </div>
    );
  }

  // Render main routes
  return (
    <>
      {/* Public routes */}
      {!user && renderRoutes(publicRoutes, "", history)}

      {/* Authenticated routes (merged with redirects so * works) */}
      {user &&
        renderRoutes([
          ...protectedRoutes,
          ...coachRoutes.map((r) => ({ ...r, path: "/coach" + r.path })),
          ...lockerRoutes,
          ...evaluationRoutes,
          ...v2Routes,
          ...(isDev ? devRoutes : []),
          ...redirects,
        ], "", history)}

      {/* Public fallback for unauthenticated users */}
      {!user &&
        renderRoutes([
          { path: "*", redirectTo: "/login" },
        ], "", history)}
    </>
  );
};

export default AppRoutes;