// ABOUTME: Global debug shortcuts for quick access to debug information
// Provides keyboard shortcuts for debugging components in debug mode

import { isDebugMode } from './debugMode';

let isInitialized = false;
let hoveredDebugElement: HTMLElement | null = null;

/**
 * Initialize global debug shortcuts
 * - Cmd/Ctrl + D: Opens debug modal for hovered element with debug button
 * - Cmd/Ctrl + Shift + D: Copies debug data of hovered element
 */
export function initializeDebugShortcuts() {
  if (isInitialized || !isDebugMode()) return;
  
  isInitialized = true;
  
  // Track hovered elements with debug buttons
  document.addEventListener('mouseover', (e) => {
    const target = e.target as HTMLElement;
    
    // Check if we're hovering over a debug button or its parent card
    const debugButton = target.closest('.debug-button');
    const card = target.closest('[data-shadow-rendered="true"]');
    
    if (debugButton || (card && card.shadowRoot?.querySelector('.debug-button'))) {
      hoveredDebugElement = card as HTMLElement || debugButton as HTMLElement;
    } else {
      hoveredDebugElement = null;
    }
  });
  
  // Global keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    if (!isDebugMode()) return;
    
    // Cmd/Ctrl + D: Open debug modal
    if ((e.metaKey || e.ctrlKey) && e.key === 'd' && !e.shiftKey) {
      e.preventDefault();
      
      if (hoveredDebugElement) {
        const shadowRoot = hoveredDebugElement.shadowRoot;
        const debugButton = shadowRoot?.querySelector('.debug-button') as HTMLElement;
        
        if (debugButton) {
          debugButton.click();
        }
      }
    }
    
    // Cmd/Ctrl + Shift + D: Quick copy debug data
    if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'D') {
      e.preventDefault();
      
      // If a debug modal is open, copy its data
      const openModal = document.querySelector('[data-debug-modal-open="true"]');
      if (openModal) {
        const copyButton = openModal.shadowRoot?.querySelector('.copy-button') as HTMLElement;
        if (copyButton) {
          copyButton.click();
        }
      }
    }
  });
  
  console.log('🐛 Debug shortcuts initialized:', {
    'Cmd/Ctrl + D': 'Open debug modal for hovered element',
    'Cmd/Ctrl + Shift + D': 'Copy debug data from open modal',
    'Cmd/Ctrl + C': 'Copy data when modal is open',
    'Escape': 'Close debug modal'
  });
}

// Auto-initialize if debug mode is enabled
if (typeof window !== 'undefined' && isDebugMode()) {
  window.addEventListener('DOMContentLoaded', initializeDebugShortcuts);
}