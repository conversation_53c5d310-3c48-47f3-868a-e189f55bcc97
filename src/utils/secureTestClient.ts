// Secure test client that uses environment variables
// Use this instead of hardcoded credentials in test files

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_APP_SUPABASE_URL;
const anonKey = import.meta.env.VITE_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !anonKey) {
  throw new Error('Missing Supabase environment variables. Check your .env file.');
}

// Create a clean client without middleware for testing
export const testClient = createClient(supabaseUrl, anonKey, {
  auth: {
    persistSession: false, // Don't persist sessions for tests
  }
});

// Service role client for admin tests (uses service key from environment)
const serviceKey = import.meta.env.VITE_APP_SUPABASE_SERVICE_KEY;

export const adminTestClient = serviceKey 
  ? createClient(supabaseUrl, serviceKey, {
      auth: {
        persistSession: false,
      }
    })
  : null;

// Helper to check if admin client is available
export const hasAdminClient = (): boolean => {
  return adminTestClient !== null;
};