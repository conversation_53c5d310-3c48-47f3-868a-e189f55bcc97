import type { AppRoute } from "./types";
import { PublicComponents } from './components';

export const publicRoutes: AppRoute[] = [
  {
    path: "/",
    exact: true,
    component: PublicComponents.Splash,
  },
  {
    path: "/login",
    component: PublicComponents.Login,
    guard: "public",
  },
  {
    path: "/signup",
    component: PublicComponents.Signup,
    guard: "public",
  },
  {
    path: "/register",
    component: PublicComponents.Register,
    guard: "public",
  },
  {
    path: "/register-invited",
    redirectTo: "/register",
  },
  {
    path: "/reset-password",
    component: PublicComponents.ResetPassword,
    guard: "public",
  },

  // Evaluation routes (anonymous access allowed)
  {
    path: "/evaluation/pre/:preEvaluationId",
    component: PublicComponents.PlayerSelfEvaluation,
  },

  // Design system (publicly accessible)
  {
    path: "/design-system",
    component: PublicComponents.DesignSystem,
  },
  {
    path: "/design-system/buttons",
    component: PublicComponents.ButtonShowcase,
  },
  {
    path: "/design-system/event-cards",
    component: PublicComponents.EventCardShowcase,
  },
];