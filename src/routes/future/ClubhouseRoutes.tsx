// ABOUTME: Clubhouse area routes - Club management, member directories, and club-specific features
// Future state routes for club-level features (distinct from platform admin in Control area)

import React from 'react';
import { Route, Switch } from 'react-router-dom';

// Lazy load all Clubhouse pages
const ClubDashboard = React.lazy(() => import('@/features/clubhouse/pages/ClubDashboard'));
const ClubList = React.lazy(() => import('@/features/clubhouse/pages/clubs/ClubList'));
const ClubDetail = React.lazy(() => import('@/features/clubhouse/pages/clubs/ClubDetail'));
const CreateClub = React.lazy(() => import('@/features/clubhouse/pages/clubs/CreateClub'));
const EditClub = React.lazy(() => import('@/features/clubhouse/pages/clubs/EditClub'));
const ClubSettings = React.lazy(() => import('@/features/clubhouse/pages/clubs/ClubSettings'));
const ClubMembers = React.lazy(() => import('@/features/clubhouse/pages/members/ClubMembers'));
const ClubCoaches = React.lazy(() => import('@/features/clubhouse/pages/coaches/ClubCoaches'));
const ClubAnalytics = React.lazy(() => import('@/features/clubhouse/pages/analytics/ClubAnalytics'));
const ClubAnnouncements = React.lazy(() => import('@/features/clubhouse/pages/announcements/ClubAnnouncements'));
const ClubBranding = React.lazy(() => import('@/features/clubhouse/pages/branding/ClubBranding'));

export const ClubhouseRoutes: React.FC = () => {
  return (
    <Switch>
      {/* ========== STATIC ROUTES (NO PARAMETERS) ========== */}
      <Route exact path="/clubhouse" component={ClubDashboard} />
      <Route exact path="/clubhouse/clubs" component={ClubList} />
      <Route exact path="/clubhouse/clubs/create" component={CreateClub} />
      <Route exact path="/clubhouse/my-clubs" component={MyClubs} />
      <Route exact path="/clubhouse/join" component={JoinClub} />
      
      {/* ========== CLUB-SPECIFIC STATIC ROUTES (BEFORE :clubId) ========== */}
      <Route exact path="/clubhouse/clubs/discover" component={DiscoverClubs} />
      <Route exact path="/clubhouse/clubs/verified" component={VerifiedClubs} />
      <Route exact path="/clubhouse/clubs/pending" component={PendingClubs} />
      
      {/* ========== SINGLE PARAMETER ROUTES ========== */}
      <Route exact path="/clubhouse/clubs/:clubId" component={ClubDetail} />
      <Route exact path="/clubhouse/clubs/:clubId/edit" component={EditClub} />
      <Route exact path="/clubhouse/clubs/:clubId/settings" component={ClubSettings} />
      <Route exact path="/clubhouse/clubs/:clubId/members" component={ClubMembers} />
      <Route exact path="/clubhouse/clubs/:clubId/coaches" component={ClubCoaches} />
      <Route exact path="/clubhouse/clubs/:clubId/analytics" component={ClubAnalytics} />
      <Route exact path="/clubhouse/clubs/:clubId/announcements" component={ClubAnnouncements} />
      <Route exact path="/clubhouse/clubs/:clubId/branding" component={ClubBranding} />
      
      {/* ========== CLUB MEMBER MANAGEMENT ROUTES ========== */}
      <Route exact path="/clubhouse/clubs/:clubId/members/invite" component={InviteMembers} />
      <Route exact path="/clubhouse/clubs/:clubId/members/requests" component={MemberRequests} />
      <Route exact path="/clubhouse/clubs/:clubId/members/:memberId" component={MemberDetail} />
      
      {/* ========== CLUB COACH MANAGEMENT ROUTES ========== */}
      <Route exact path="/clubhouse/clubs/:clubId/coaches/add" component={AddCoach} />
      <Route exact path="/clubhouse/clubs/:clubId/coaches/:coachId" component={CoachDetail} />
      <Route exact path="/clubhouse/clubs/:clubId/coaches/:coachId/permissions" component={CoachPermissions} />
      
      {/* ========== CLUB SETTINGS SUB-ROUTES ========== */}
      <Route exact path="/clubhouse/clubs/:clubId/settings/general" component={GeneralSettings} />
      <Route exact path="/clubhouse/clubs/:clubId/settings/privacy" component={PrivacySettings} />
      <Route exact path="/clubhouse/clubs/:clubId/settings/integrations" component={IntegrationSettings} />
      <Route exact path="/clubhouse/clubs/:clubId/settings/billing" component={BillingSettings} />
      
      {/* ========== CLUB VERIFICATION ROUTES ========== */}
      <Route exact path="/clubhouse/clubs/:clubId/verification" component={ClubVerification} />
      <Route exact path="/clubhouse/clubs/:clubId/verification/submit" component={SubmitVerification} />
      <Route exact path="/clubhouse/clubs/:clubId/verification/status" component={VerificationStatus} />
      
      {/* ========== CLUB ANNOUNCEMENT ROUTES ========== */}
      <Route exact path="/clubhouse/clubs/:clubId/announcements/create" component={CreateAnnouncement} />
      <Route exact path="/clubhouse/clubs/:clubId/announcements/:announcementId" component={AnnouncementDetail} />
      <Route exact path="/clubhouse/clubs/:clubId/announcements/:announcementId/edit" component={EditAnnouncement} />
      
      {/* ========== PUBLIC CLUB ROUTES (NO AUTH REQUIRED) ========== */}
      <Route exact path="/clubhouse/public/:clubSlug" component={PublicClubPage} />
      <Route exact path="/clubhouse/join/:inviteCode" component={JoinByInvite} />
      
      {/* ========== CATCH-ALL (MUST BE LAST) ========== */}
      <Route path="/clubhouse/*" component={ClubhouseNotFound} />
    </Switch>
  );
};

// ROUTE MAPPING FROM CURRENT STRUCTURE:
// =====================================
// CLUB MANAGEMENT:
// - /coach/clubs → /clubhouse/clubs
// - /coach/clubs/management → /clubhouse/clubs
// - /coach/club/:clubId → /clubhouse/clubs/:clubId
// - /coach/club/:clubId/edit → /clubhouse/clubs/:clubId/edit
// - /coach/club/:clubId/settings → /clubhouse/clubs/:clubId/settings
// - /coach/club/:clubId/players → /clubhouse/clubs/:clubId/members
// - /coach/club/:clubId/coaches → /clubhouse/clubs/:clubId/coaches
// - /coach/club/:clubId/administrators → Moved to Control area
// - /coach/club/:clubId/verification → /clubhouse/clubs/:clubId/verification
// - /coach/club/:clubId/analytics → /clubhouse/clubs/:clubId/analytics
// - /coach/club/create → /clubhouse/clubs/create
//
// TO BE DEPRECATED:
// - All club routes under /coach/* structure
// - Mixed admin/club features (admin moves to Control)

// KEY DISTINCTIONS:
// =================
// CLUBHOUSE includes:
// - Club-specific features and settings
// - Member management at club level
// - Club branding and customization
// - Club announcements and communications
// - Club-level analytics and reporting
//
// CONTROL (Admin) includes:
// - Platform administration
// - User role management
// - System-wide settings
// - Super admin features
// - Platform-wide analytics

// FEATURES IN CLUBHOUSE:
// ======================
// 1. Club creation and management
// 2. Member directories and invitations
// 3. Coach assignment and permissions
// 4. Club verification process
// 5. Club-specific announcements
// 6. Branding and customization
// 7. Club analytics and insights
// 8. Public club pages
// 9. Invite-based joining