// ABOUTME: Pulse area routes - Social feed, content, videos, and community features
// Future state routes for social and content features

import React from 'react';
import { Route, Switch } from 'react-router-dom';

// Lazy load all Pulse pages
const PulseFeed = React.lazy(() => import('@/features/pulse/pages/feed/PulseFeed'));
const VideoLibrary = React.lazy(() => import('@/features/pulse/pages/videos/VideoLibrary'));
const VideoPlayer = React.lazy(() => import('@/features/pulse/pages/videos/VideoPlayer'));
const ContentSync = React.lazy(() => import('@/features/pulse/pages/admin/ContentSync'));
const WidgetBuilder = React.lazy(() => import('@/features/pulse/pages/admin/WidgetBuilder'));
const CreatePost = React.lazy(() => import('@/features/pulse/pages/posts/CreatePost'));
const PostDetail = React.lazy(() => import('@/features/pulse/pages/posts/PostDetail'));
const Notifications = React.lazy(() => import('@/features/pulse/pages/notifications/Notifications'));

export const PulseRoutes: React.FC = () => {
  return (
    <Switch>
      {/* ========== STATIC ROUTES (NO PARAMETERS) ========== */}
      <Route exact path="/pulse" component={PulseFeed} />
      <Route exact path="/pulse/feed" component={PulseFeed} />
      <Route exact path="/pulse/videos" component={VideoLibrary} />
      <Route exact path="/pulse/notifications" component={Notifications} />
      <Route exact path="/pulse/create" component={CreatePost} />
      <Route exact path="/pulse/trending" component={TrendingContent} />
      <Route exact path="/pulse/following" component={FollowingFeed} />
      
      {/* ========== VIDEO ROUTES ========== */}
      <Route exact path="/pulse/videos/upload" component={UploadVideo} />
      <Route exact path="/pulse/videos/library" component={VideoLibrary} />
      <Route exact path="/pulse/videos/categories" component={VideoCategories} />
      <Route exact path="/pulse/videos/:videoId" component={VideoPlayer} />
      <Route exact path="/pulse/videos/:videoId/edit" component={EditVideo} />
      
      {/* ========== POST ROUTES ========== */}
      <Route exact path="/pulse/posts/:postId" component={PostDetail} />
      <Route exact path="/pulse/posts/:postId/edit" component={EditPost} />
      
      {/* ========== USER CONTENT ROUTES ========== */}
      <Route exact path="/pulse/users/:userId" component={UserProfile} />
      <Route exact path="/pulse/users/:userId/posts" component={UserPosts} />
      <Route exact path="/pulse/users/:userId/videos" component={UserVideos} />
      
      {/* ========== TEAM/CLUB CONTENT ROUTES ========== */}
      <Route exact path="/pulse/teams/:teamId" component={TeamFeed} />
      <Route exact path="/pulse/teams/:teamId/posts" component={TeamPosts} />
      <Route exact path="/pulse/clubs/:clubId" component={ClubFeed} />
      <Route exact path="/pulse/clubs/:clubId/announcements" component={ClubAnnouncements} />
      
      {/* ========== NOTIFICATION ROUTES ========== */}
      <Route exact path="/pulse/notifications/settings" component={NotificationSettings} />
      <Route exact path="/pulse/notifications/:notificationId" component={NotificationDetail} />
      
      {/* ========== ADMIN ROUTES ========== */}
      <Route exact path="/pulse/admin" component={PulseAdmin} />
      <Route exact path="/pulse/admin/content-sync" component={ContentSync} />
      <Route exact path="/pulse/admin/widget-builder" component={WidgetBuilder} />
      <Route exact path="/pulse/admin/moderation" component={ContentModeration} />
      <Route exact path="/pulse/admin/analytics" component={ContentAnalytics} />
      
      {/* ========== SP POINTS ROUTES ========== */}
      <Route exact path="/pulse/points" component={SPPointsDashboard} />
      <Route exact path="/pulse/points/history" component={PointsHistory} />
      <Route exact path="/pulse/points/leaderboard" component={Leaderboard} />
      
      {/* ========== CATCH-ALL (MUST BE LAST) ========== */}
      <Route path="/pulse/*" component={PulseNotFound} />
    </Switch>
  );
};

// ROUTE MAPPING FROM CURRENT STRUCTURE:
// =====================================
// MAIN ROUTES:
// - /pulse → /pulse (keep as is)
// - /videos → /pulse/videos
// - /videoplayer/:id → /pulse/videos/:videoId
//
// V2 ROUTES:
// - /v2/pulse → /pulse (consolidate)
// - /v2/admin/content-sync → /pulse/admin/content-sync
// - /v2/admin/widget-builder → /pulse/admin/widget-builder
//
// TO BE DEPRECATED:
// - Legacy Pulse.tsx implementation
// - Scattered notification components
// - Old video player implementation

// KEY FEATURES IN PULSE:
// ======================
// 1. SOCIAL FEED
//    - User posts and updates
//    - Team/club announcements
//    - Following/follower system
//    - Trending content
//
// 2. VIDEO PLATFORM
//    - Video library and categories
//    - Video upload and management
//    - Video player with analytics
//    - Training video collections
//
// 3. NOTIFICATIONS
//    - Real-time notifications
//    - Notification preferences
//    - Team/club notifications
//    - System announcements
//
// 4. SP POINTS SYSTEM
//    - Points tracking
//    - Leaderboards
//    - Rewards and achievements
//    - Points history
//
// 5. CONTENT MANAGEMENT
//    - Content moderation
//    - Admin tools
//    - Widget builder
//    - Content analytics
//
// INTEGRATION POINTS:
// ==================
// - Schedule: Event announcements
// - Perform: Training videos
// - Assess: Achievement notifications
// - Clubhouse: Club announcements
// - Identity: User profiles