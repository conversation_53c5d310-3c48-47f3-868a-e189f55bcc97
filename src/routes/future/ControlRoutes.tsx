// ABOUTME: Control area routes - Platform administration, system settings, and super admin features
// Future state routes for platform-wide administrative functions

import React from 'react';
import { Route, Switch } from 'react-router-dom';

// Lazy load all Control pages
const AdminDashboard = React.lazy(() => import('@/features/control/pages/AdminDashboard'));
const UserManagement = React.lazy(() => import('@/features/control/pages/users/UserManagement'));
const UserDetail = React.lazy(() => import('@/features/control/pages/users/UserDetail'));
const RoleManagement = React.lazy(() => import('@/features/control/pages/roles/RoleManagement'));
const SystemSettings = React.lazy(() => import('@/features/control/pages/settings/SystemSettings'));
const PlatformAnalytics = React.lazy(() => import('@/features/control/pages/analytics/PlatformAnalytics'));
const AuditLogs = React.lazy(() => import('@/features/control/pages/audit/AuditLogs'));
const SMSDashboard = React.lazy(() => import('@/features/control/pages/communications/SMSDashboard'));
const EmailDashboard = React.lazy(() => import('@/features/control/pages/communications/EmailDashboard'));
const ClubAdministration = React.lazy(() => import('@/features/control/pages/clubs/ClubAdministration'));

export const ControlRoutes: React.FC = () => {
  return (
    <Switch>
      {/* ========== STATIC ROUTES (NO PARAMETERS) ========== */}
      <Route exact path="/control" component={AdminDashboard} />
      <Route exact path="/control/users" component={UserManagement} />
      <Route exact path="/control/roles" component={RoleManagement} />
      <Route exact path="/control/settings" component={SystemSettings} />
      <Route exact path="/control/analytics" component={PlatformAnalytics} />
      <Route exact path="/control/audit" component={AuditLogs} />
      <Route exact path="/control/clubs" component={ClubAdministration} />
      
      {/* ========== COMMUNICATION MANAGEMENT ========== */}
      <Route exact path="/control/communications" component={CommunicationsDashboard} />
      <Route exact path="/control/communications/sms" component={SMSDashboard} />
      <Route exact path="/control/communications/email" component={EmailDashboard} />
      <Route exact path="/control/communications/notifications" component={NotificationCenter} />
      <Route exact path="/control/communications/templates" component={MessageTemplates} />
      
      {/* ========== USER MANAGEMENT ROUTES ========== */}
      <Route exact path="/control/users/create" component={CreateUser} />
      <Route exact path="/control/users/import" component={ImportUsers} />
      <Route exact path="/control/users/export" component={ExportUsers} />
      <Route exact path="/control/users/:userId" component={UserDetail} />
      <Route exact path="/control/users/:userId/edit" component={EditUser} />
      <Route exact path="/control/users/:userId/permissions" component={UserPermissions} />
      <Route exact path="/control/users/:userId/activity" component={UserActivity} />
      
      {/* ========== ROLE MANAGEMENT ROUTES ========== */}
      <Route exact path="/control/roles/create" component={CreateRole} />
      <Route exact path="/control/roles/:roleId" component={RoleDetail} />
      <Route exact path="/control/roles/:roleId/edit" component={EditRole} />
      <Route exact path="/control/roles/:roleId/permissions" component={RolePermissions} />
      
      {/* ========== SYSTEM SETTINGS ROUTES ========== */}
      <Route exact path="/control/settings/general" component={GeneralSettings} />
      <Route exact path="/control/settings/security" component={SecuritySettings} />
      <Route exact path="/control/settings/api" component={APISettings} />
      <Route exact path="/control/settings/integrations" component={IntegrationSettings} />
      <Route exact path="/control/settings/billing" component={BillingSettings} />
      <Route exact path="/control/settings/features" component={FeatureFlags} />
      
      {/* ========== CLUB ADMINISTRATION ROUTES ========== */}
      <Route exact path="/control/clubs/:clubId" component={ClubAdminDetail} />
      <Route exact path="/control/clubs/:clubId/administrators" component={ClubAdministrators} />
      <Route exact path="/control/clubs/:clubId/suspend" component={SuspendClub} />
      <Route exact path="/control/clubs/:clubId/delete" component={DeleteClub} />
      
      {/* ========== ANALYTICS ROUTES ========== */}
      <Route exact path="/control/analytics/users" component={UserAnalytics} />
      <Route exact path="/control/analytics/clubs" component={ClubAnalytics} />
      <Route exact path="/control/analytics/usage" component={UsageAnalytics} />
      <Route exact path="/control/analytics/revenue" component={RevenueAnalytics} />
      <Route exact path="/control/analytics/performance" component={PerformanceMetrics} />
      
      {/* ========== AUDIT & COMPLIANCE ROUTES ========== */}
      <Route exact path="/control/audit/logs" component={AuditLogs} />
      <Route exact path="/control/audit/security" component={SecurityAudit} />
      <Route exact path="/control/audit/compliance" component={ComplianceReports} />
      <Route exact path="/control/audit/exports" component={DataExports} />
      
      {/* ========== SUPPORT & MAINTENANCE ROUTES ========== */}
      <Route exact path="/control/support" component={SupportDashboard} />
      <Route exact path="/control/support/tickets" component={SupportTickets} />
      <Route exact path="/control/maintenance" component={MaintenanceMode} />
      <Route exact path="/control/debug" component={DebugTools} />
      
      {/* ========== CATCH-ALL (MUST BE LAST) ========== */}
      <Route path="/control/*" component={ControlNotFound} />
    </Switch>
  );
};

// ROUTE MAPPING FROM CURRENT STRUCTURE:
// =====================================
// ADMIN ROUTES:
// - /superadmin → /control
// - /admin/sms-notification-dashboard → /control/communications/sms
// - /coach/club/:clubId/administrators → /control/clubs/:clubId/administrators
// - /coach/club/:clubId/debug-administrators → /control/clubs/:clubId/administrators/debug
// - /coach/clubs/debug/:clubId → /control/clubs/:clubId/debug
//
// USER MANAGEMENT:
// - /coach/user-management → /control/users
// - /coach/communication-management → /control/communications
//
// TO BE DEPRECATED:
// - Admin features mixed with coach routes
// - Scattered admin components
// - Legacy super admin page

// KEY FEATURES IN CONTROL:
// ========================
// 1. PLATFORM ADMINISTRATION
//    - System-wide settings
//    - Feature flags
//    - API management
//
// 2. USER & ROLE MANAGEMENT
//    - User creation and management
//    - Role-based permissions
//    - User activity tracking
//
// 3. CLUB ADMINISTRATION
//    - Club approval/suspension
//    - Administrator assignment
//    - Club deletion (with audit)
//
// 4. COMMUNICATIONS HUB
//    - SMS notification center
//    - Email campaign management
//    - System notifications
//    - Message templates
//
// 5. ANALYTICS & INSIGHTS
//    - Platform-wide metrics
//    - Usage analytics
//    - Revenue tracking
//    - Performance monitoring
//
// 6. AUDIT & COMPLIANCE
//    - Activity logs
//    - Security audits
//    - Compliance reports
//    - Data export tools
//
// ACCESS CONTROL:
// ==============
// All Control routes require:
// - Authenticated user
// - Admin or SuperAdmin role
// - Additional permission checks per feature