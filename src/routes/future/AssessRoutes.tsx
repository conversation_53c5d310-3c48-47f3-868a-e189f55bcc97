// ABOUTME: Assess area routes - Evaluations, IDPs, and player development tracking
// Future state routes for all assessment and evaluation features

import React from 'react';
import { Route, Switch } from 'react-router-dom';

// Lazy load all Assess pages
const EvaluationDashboard = React.lazy(() => import('@/features/assess/pages/EvaluationDashboard'));
const EvaluationList = React.lazy(() => import('@/features/assess/pages/evaluations/EvaluationList'));
const CreateEvaluation = React.lazy(() => import('@/features/assess/pages/evaluations/CreateEvaluation'));
const EvaluationDetail = React.lazy(() => import('@/features/assess/pages/evaluations/EvaluationDetail'));
const PlayerEvaluation = React.lazy(() => import('@/features/assess/pages/evaluations/PlayerEvaluation'));
const TeamEvaluation = React.lazy(() => import('@/features/assess/pages/evaluations/TeamEvaluation'));
const SelfEvaluation = React.lazy(() => import('@/features/assess/pages/evaluations/SelfEvaluation'));
const PreEvaluation = React.lazy(() => import('@/features/assess/pages/evaluations/PreEvaluation'));
const IDPDashboard = React.lazy(() => import('@/features/assess/pages/idp/IDPDashboard'));
const IDPDetail = React.lazy(() => import('@/features/assess/pages/idp/IDPDetail'));
const CreateIDP = React.lazy(() => import('@/features/assess/pages/idp/CreateIDP'));
const ProgressTracking = React.lazy(() => import('@/features/assess/pages/progress/ProgressTracking'));
const CriteriaManagement = React.lazy(() => import('@/features/assess/pages/criteria/CriteriaManagement'));

export const AssessRoutes: React.FC = () => {
  return (
    <Switch>
      {/* ========== STATIC ROUTES (NO PARAMETERS) ========== */}
      <Route exact path="/assess" component={EvaluationDashboard} />
      <Route exact path="/assess/evaluations" component={EvaluationList} />
      <Route exact path="/assess/evaluations/create" component={CreateEvaluation} />
      <Route exact path="/assess/idp" component={IDPDashboard} />
      <Route exact path="/assess/idp/create" component={CreateIDP} />
      <Route exact path="/assess/progress" component={ProgressTracking} />
      <Route exact path="/assess/criteria" component={CriteriaManagement} />
      
      {/* ========== EVALUATION TYPE ROUTES (BEFORE PARAMS) ========== */}
      <Route exact path="/assess/evaluations/weekly" component={WeeklyEvaluations} />
      <Route exact path="/assess/evaluations/event" component={EventEvaluations} />
      <Route exact path="/assess/evaluations/self" component={SelfEvaluationList} />
      <Route exact path="/assess/evaluations/templates" component={EvaluationTemplates} />
      
      {/* ========== SINGLE PARAMETER ROUTES ========== */}
      <Route exact path="/assess/evaluations/:evaluationId" component={EvaluationDetail} />
      <Route exact path="/assess/idp/:idpId" component={IDPDetail} />
      
      {/* ========== PLAYER-SPECIFIC ROUTES ========== */}
      <Route exact path="/assess/players/:playerId/evaluations" component={PlayerEvaluationHistory} />
      <Route exact path="/assess/players/:playerId/progress" component={PlayerProgress} />
      <Route exact path="/assess/players/:playerId/idp" component={PlayerIDP} />
      
      {/* ========== TEAM-SPECIFIC ROUTES ========== */}
      <Route exact path="/assess/teams/:teamId/evaluations" component={TeamEvaluationList} />
      <Route exact path="/assess/teams/:teamId/evaluate" component={TeamEvaluation} />
      <Route exact path="/assess/teams/:teamId/progress" component={TeamProgress} />
      
      {/* ========== NESTED EVALUATION ROUTES ========== */}
      <Route exact path="/assess/evaluations/:evaluationId/players" component={EvaluationPlayerList} />
      <Route exact path="/assess/evaluations/:evaluationId/players/:playerId" component={PlayerEvaluation} />
      <Route exact path="/assess/evaluations/:evaluationId/summary" component={EvaluationSummary} />
      <Route exact path="/assess/evaluations/:evaluationId/compare" component={EvaluationComparison} />
      
      {/* ========== PRE-EVALUATION ROUTES (PUBLIC ACCESS) ========== */}
      {/* These might not require authentication */}
      <Route exact path="/assess/pre/:preEvaluationId" component={PreEvaluation} />
      <Route exact path="/assess/self/:selfEvaluationId" component={SelfEvaluation} />
      
      {/* ========== EVENT-BASED EVALUATION ROUTES ========== */}
      <Route exact path="/assess/events/:eventId/evaluations" component={EventEvaluationList} />
      <Route exact path="/assess/events/:eventId/evaluate" component={EventBatchEvaluation} />
      <Route exact path="/assess/events/:eventId/pre-evaluations" component={EventPreEvaluations} />
      
      {/* ========== CRITERIA MANAGEMENT ROUTES ========== */}
      <Route exact path="/assess/criteria/:frameworkId" component={CriteriaFramework} />
      <Route exact path="/assess/criteria/:frameworkId/edit" component={EditCriteriaFramework} />
      
      {/* ========== CATCH-ALL (MUST BE LAST) ========== */}
      <Route path="/assess/*" component={AssessNotFound} />
    </Switch>
  );
};

// ROUTE MAPPING FROM CURRENT STRUCTURE:
// =====================================
// EVALUATION ROUTES:
// - /coach/club/:clubId/team/:teamId/evaluations → /assess/teams/:teamId/evaluations
// - /coach/club/:clubId/team/:teamId/event/:eventId/evaluate → /assess/events/:eventId/evaluate
// - /coach/club/:clubId/team/:teamId/event/:eventId/pre-evaluations → /assess/events/:eventId/pre-evaluations
// - /evaluation/pre/:preEvaluationId → /assess/pre/:preEvaluationId
// - /player/:playerId/evaluation-history → /assess/players/:playerId/evaluations
// - /coach/weekly-evaluation → /assess/evaluations/weekly
//
// IDP ROUTES:
// - /view-idp → /assess/idp
// - /improveIDP → /assess/idp/improve
//
// PROGRESS ROUTES:
// - /player/:playerId/progress → /assess/players/:playerId/progress
//
// CRITERIA ROUTES:
// - /coach/perform-framework-management/:frameworkVersion/criteria → /assess/criteria/:frameworkId
//
// TO BE DEPRECATED:
// - All evaluation routes under /coach/* structure
// - Legacy IDP pages (ViewIDP, ImproveIDP)
// - Scattered evaluation components

// KEY FEATURES IN ASSESS:
// =======================
// 1. Weekly evaluations for ongoing player development
// 2. Event-based evaluations (post-match, training)
// 3. Self-evaluations and pre-evaluations
// 4. Individual Development Plans (IDP)
// 5. Progress tracking and reporting
// 6. Evaluation criteria management
// 7. Comparison and analytics tools
// 8. Public links for player self-evaluations