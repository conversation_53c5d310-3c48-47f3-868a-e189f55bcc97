// ABOUTME: Locker area routes - E-commerce, merchandise, memberships, and payments
// Future state routes for shopping and commerce features

import React from 'react';
import { Route, Switch } from 'react-router-dom';

// Lazy load all Locker pages
const LockerHome = React.lazy(() => import('@/features/locker/pages/shop/LockerHome'));
const ProductListing = React.lazy(() => import('@/features/locker/pages/shop/ProductListing'));
const ProductDetail = React.lazy(() => import('@/features/locker/pages/products/ProductDetail'));
const Cart = React.lazy(() => import('@/features/locker/pages/cart/Cart'));
const Checkout = React.lazy(() => import('@/features/locker/pages/checkout/Checkout'));
const OrderConfirmation = React.lazy(() => import('@/features/locker/pages/checkout/OrderConfirmation'));
const OrderHistory = React.lazy(() => import('@/features/locker/pages/orders/OrderHistory'));
const Wishlist = React.lazy(() => import('@/features/locker/pages/wishlist/Wishlist'));
const DropsPage = React.lazy(() => import('@/features/locker/pages/drops/DropsPage'));
const DropsAdmin = React.lazy(() => import('@/features/locker/admin/DropsAdmin'));
const MembershipPage = React.lazy(() => import('@/features/locker/pages/membership/MembershipPage'));

export const LockerRoutes: React.FC = () => {
  return (
    <Switch>
      {/* ========== STATIC ROUTES (NO PARAMETERS) ========== */}
      <Route exact path="/locker" component={LockerHome} />
      <Route exact path="/locker/shop" component={ProductListing} />
      <Route exact path="/locker/cart" component={Cart} />
      <Route exact path="/locker/checkout" component={Checkout} />
      <Route exact path="/locker/order-confirmation" component={OrderConfirmation} />
      <Route exact path="/locker/orders" component={OrderHistory} />
      <Route exact path="/locker/wishlist" component={Wishlist} />
      <Route exact path="/locker/drops" component={DropsPage} />
      <Route exact path="/locker/membership" component={MembershipPage} />
      <Route exact path="/locker/size-guide" component={SizeGuide} />
      
      {/* ========== CATEGORY ROUTES (BEFORE PRODUCT ID) ========== */}
      <Route exact path="/locker/shop/apparel" component={ApparelListing} />
      <Route exact path="/locker/shop/equipment" component={EquipmentListing} />
      <Route exact path="/locker/shop/accessories" component={AccessoriesListing} />
      <Route exact path="/locker/shop/training" component={TrainingGearListing} />
      <Route exact path="/locker/shop/team-gear" component={TeamGearListing} />
      <Route exact path="/locker/shop/sale" component={SaleItems} />
      
      {/* ========== PRODUCT ROUTES ========== */}
      <Route exact path="/locker/product/:productId" component={ProductDetail} />
      <Route exact path="/locker/shop/:category" component={ProductListing} />
      
      {/* ========== ORDER MANAGEMENT ROUTES ========== */}
      <Route exact path="/locker/orders/:orderId" component={OrderDetail} />
      <Route exact path="/locker/orders/:orderId/track" component={OrderTracking} />
      <Route exact path="/locker/orders/:orderId/return" component={ReturnRequest} />
      
      {/* ========== MEMBERSHIP ROUTES ========== */}
      <Route exact path="/locker/membership/plans" component={MembershipPlans} />
      <Route exact path="/locker/membership/upgrade" component={UpgradeMembership} />
      <Route exact path="/locker/membership/benefits" component={MembershipBenefits} />
      <Route exact path="/locker/membership/manage" component={ManageMembership} />
      
      {/* ========== PAYMENT ROUTES ========== */}
      <Route exact path="/locker/payment/methods" component={PaymentMethods} />
      <Route exact path="/locker/payment/add" component={AddPaymentMethod} />
      <Route exact path="/locker/payment/history" component={PaymentHistory} />
      
      {/* ========== TEAM STORE ROUTES ========== */}
      <Route exact path="/locker/teams/:teamId/store" component={TeamStore} />
      <Route exact path="/locker/teams/:teamId/products" component={TeamProducts} />
      <Route exact path="/locker/clubs/:clubId/store" component={ClubStore} />
      
      {/* ========== ADMIN ROUTES ========== */}
      <Route exact path="/locker/admin" component={LockerAdmin} />
      <Route exact path="/locker/admin/drops" component={DropsAdmin} />
      <Route exact path="/locker/admin/products" component={ProductAdmin} />
      <Route exact path="/locker/admin/orders" component={OrderAdmin} />
      <Route exact path="/locker/admin/inventory" component={InventoryManagement} />
      <Route exact path="/locker/admin/analytics" component={SalesAnalytics} />
      
      {/* ========== CATCH-ALL (MUST BE LAST) ========== */}
      <Route path="/locker/*" component={LockerNotFound} />
    </Switch>
  );
};

// ROUTE MAPPING FROM CURRENT STRUCTURE:
// =====================================
// SHOP ROUTES:
// - /shop → /locker/shop
// - /shop/cart → /locker/cart
// - /shop/product/:productId → /locker/product/:productId
// - /shop/checkout → /locker/checkout
// - /shop/order-confirmation → /locker/order-confirmation
//
// MEMBERSHIP:
// - /membership → /locker/membership
// - /checkout → /locker/checkout (unified)
//
// TO BE DEPRECATED:
// - Legacy shop routes under /shop/*
// - Scattered e-commerce components
// - Old membership page

// KEY FEATURES IN LOCKER:
// =======================
// 1. PRODUCT CATALOG
//    - Browse by category
//    - Product search and filters
//    - Product details and variants
//    - Size guides
//
// 2. SHOPPING CART
//    - Add/remove items
//    - Update quantities
//    - Apply discount codes
//    - Save for later
//
// 3. CHECKOUT PROCESS
//    - Shipping information
//    - Payment processing (Stripe)
//    - Order review
//    - Confirmation
//
// 4. ORDER MANAGEMENT
//    - Order history
//    - Order tracking
//    - Returns and exchanges
//    - Digital receipts
//
// 5. MEMBERSHIP SYSTEM
//    - Membership tiers
//    - Benefits and perks
//    - Auto-renewal
//    - Member discounts
//
// 6. TEAM/CLUB STORES
//    - Custom team merchandise
//    - Bulk ordering
//    - Team discounts
//    - Fundraising options
//
// 7. LIMITED DROPS
//    - Exclusive releases
//    - Countdown timers
//    - Early access
//    - Drop notifications
//
// INTEGRATION POINTS:
// ==================
// - Identity: User profiles for orders
// - Pulse: Product announcements
// - Clubhouse: Team/club stores
// - Control: Order administration