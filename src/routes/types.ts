import { ComponentType, LazyExoticComponent } from "react";

export type Guard =
  | "protected"
  | "public"
  | "email-verified"
  | "role:coach"
  | "role:club"
  | "role:admin"
  | "role:dev";

export type AppRoute = {
  path: string;
  exact?: boolean;
  redirectTo?: string;
  guard?: Guard;
  component?: ComponentType<any> | LazyExoticComponent<any>;
  children?: AppRoute[];

  // optional metadata
  label?: string;
  showInNav?: boolean;
};