import React from "react";
import type { AppRoute } from "./types";
import { clubEventRoutes } from "./clubEventRoutes";
import { teamRoutes } from "./teamRoutes";
import { ClubComponents } from "./components";

export const clubRoutes: AppRoute[] = [
  { path: "/", exact: true, component: ClubComponents.ClubsList, guard: "role:coach" },
  { path: "/settings", component: ClubComponents.ClubSettings, guard: "role:coach" },
  { path: "/players", component: ClubComponents.ClubPlayers, guard: "role:coach" },
  { path: "/verification", component: ClubComponents.ClubVerification, guard: "role:coach" },
  { path: "/edit", component: ClubComponents.EditClubPage, guard: "role:coach" },

  // Admin routes
  { path: "/administrators", component: ClubComponents.ClubAdministrators, guard: "role:admin" },
  { path: "/new-administrators", redirectTo: "/administrators" },
  { path: "/debug-administrators", component: ClubComponents.NewClubAdministrators, guard: "role:admin" },

  // Nest team routes
  { path: "/team", children: teamRoutes, guard: "role:coach" },

  // Nest club event routes
  ...clubEventRoutes,

  // Analytics
  { path: "/analytics", component: ClubComponents.ClubAnalytics, guard: "role:club" },
];