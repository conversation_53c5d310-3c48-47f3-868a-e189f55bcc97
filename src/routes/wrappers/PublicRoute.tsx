import React from "react";
import { Redirect } from "react-router-dom";
import { useCurrentUser } from "@/hooks/useCurrentUser";

const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { hydrated, isAuthenticated } = useCurrentUser();

  if (!hydrated) {
    return (
      <div className="flex items-center justify-center h-screen bg-slate-900 text-white">
        Loading...
      </div>
    );
  }

  if (isAuthenticated) {
    // already logged in → send them to home/account
    return <Redirect to="/home" />;
  }

  return <>{children}</>;
};

export default PublicRoute;