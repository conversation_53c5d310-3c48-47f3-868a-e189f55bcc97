import React, { useEffect, useState } from "react";
import { Redirect, useParams } from "react-router-dom";
import { supabase } from "@/lib/supabase";
import LoadingPage from "@/pages/Loading";
import { useCurrentUser } from "@/hooks/useCurrentUser";

type Role = "coach" | "club" | "admin";

interface RoleProtectedRouteProps {
  children: React.ReactNode;
  role: Role;
}

const RoleProtectedRoute: React.FC<RoleProtectedRouteProps> = ({ children, role }) => {
  const { teamId, clubId } = useParams<{ teamId?: string; clubId?: string }>();
  const [loading, setLoading] = useState(true);
  const [allowed, setAllowed] = useState(false);
  const [authenticated, setAuthenticated] = useState(false);

  useEffect(() => {
    const checkAccess = async () => {
      setLoading(true);

      const {profile: user} = useCurrentUser();

      if (!user) {
        setAuthenticated(false);
        setAllowed(false);
        setLoading(false);
        return;
      }

      // User is authenticated
      setAuthenticated(true);
      let isAllowed = false;

      if (role === "coach" && teamId) {
        const { data: teamCoaches } = await supabase
          .from("team_coaches")
          .select("user_id")
          .eq("team_id", teamId)
          .eq("status", "active");

        isAllowed = teamCoaches?.some((c) => c.user_id === user.id) ?? false;
      }

      if (role === "club" && clubId) {
        const { data: clubCoaches } = await supabase
          .from("club_coaches")
          .select("user_id")
          .eq("club_id", clubId)
          .eq("status", "active");

        const { data: clubAdmins } = await supabase
          .from("club_administrators")
          .select("user_id")
          .eq("club_id", clubId)
          .eq("is_active", true);

        const isCoach = clubCoaches?.some((c) => c.user_id === user.id) ?? false;
        const isAdmin = clubAdmins?.some((a) => a.user_id === user.id) ?? false;

        isAllowed = isCoach || isAdmin;
      }

      if (role === "admin") {
        // Check for superadmin privileges (matches AdminProtectedRoute logic)
        isAllowed = Array.isArray(user.privileges) && user.privileges.includes("superadmin");
        
        // If not superadmin but clubId is provided, check if they're a club admin
        if (!isAllowed && clubId) {
          const { data: clubAdmins } = await supabase
            .from("club_administrators")
            .select("user_id")
            .eq("club_id", clubId)
            .eq("user_id", user.id)
            .eq("is_active", true)
            .maybeSingle();
          
          isAllowed = !!clubAdmins;
        }
      }

      setAllowed(isAllowed);
      setLoading(false);
    };

    checkAccess();
  }, [role, teamId, clubId]);

  if (loading) return <LoadingPage />;

  if (!authenticated) return <Redirect to="/login" />;

  if (!allowed) return <Redirect to="/home" />;

  return <>{children}</>;
};

export default RoleProtectedRoute;