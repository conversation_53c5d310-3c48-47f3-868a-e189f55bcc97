import React from "react";
import { Redirect, useLocation } from "react-router-dom";
import { useCurrentUser } from "@/hooks/useCurrentUser";

const EmailVerificationGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { hydrated, isAuthenticated, isEmailVerified } = useCurrentUser();
  const location = useLocation();

  if (!hydrated) {
    return (
      <div className="flex items-center justify-center h-screen bg-slate-900 text-white">
        Loading...
      </div>
    );
  }

  if (!isAuthenticated) {
    // redirect to login, but remember where they came from
    return <Redirect to={{ pathname: "/login", state: { from: location } }} />;
  }

  if (!isEmailVerified) {
    // redirect to email verification page
    return <Redirect to={{ pathname: "/email-verification", state: { from: location } }} />;
  }

  return <>{children}</>;
};

export default EmailVerificationGuard;