import React from "react";
import type { AppRoute } from "./types";
import { TeamComponents } from "./components";

export const teamRoutes: AppRoute[] = [
  { path: "/:teamId", exact: true, component: TeamComponents.TeamFlat, guard: "role:coach" },
  { path: "/:teamId/edit", component: TeamComponents.EditTeamShadow, guard: "role:coach" },
  { path: "/:teamId/add-player", component: TeamComponents.TeamAddPlayer, guard: "role:coach" },
  { path: "/:teamId/comms", component: TeamComponents.TeamComms, guard: "role:coach" },
  { path: "/:teamId/calendar", component: TeamComponents.SessionCalendar, guard: "role:coach" },
  { path: "/:teamId/stats", component: TeamComponents.TeamStats, guard: "role:coach" },
  { path: "/:teamId/objectives", component: TeamComponents.TeamObjectives, guard: "role:coach" },
  { path: "/:teamId/evaluation", component: TeamComponents.TeamEvaluation, guard: "role:coach" },
  { path: "/:teamId/evaluations", component: TeamComponents.EvaluationsPage, guard: "role:coach" },
  { path: "/:teamId/player/:playerId/objectives", component: TeamComponents.PlayerObjectives, guard: "role:coach" },

  // Team events
  { path: "/:teamId/events", component: TeamComponents.EventsList, guard: "role:coach" },
  { path: "/:teamId/event/:eventId", component: TeamComponents.EventPage, guard: "role:coach" },
  { path: "/:teamId/event/:eventId/evaluate", component: TeamComponents.EventEvaluation, guard: "role:coach" },
  { path: "/:teamId/event/:eventId/pre-evaluations", component: TeamComponents.SelfEvaluationSummary, guard: "role:coach" },
];