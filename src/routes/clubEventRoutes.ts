import type { AppRoute } from "./types";
import { ClubEventComponents } from "./components";

export const clubEventRoutes: AppRoute[] = [
  { path: "/events", component: ClubEventComponents.EventsList, guard: "role:club" },
  { path: "/event/:eventId", component: ClubEventComponents.EventPage, guard: "role:club" },
  { path: "/event/:eventId/evaluate", component: ClubEventComponents.EventEvaluation, guard: "role:club" },
  { path: "/event/:eventId/participants", component: ClubEventComponents.EventParticipantsList, guard: "role:club" },
  { path: "/event/:eventId/sms-status", component: ClubEventComponents.SmsStatusPage, guard: "role:club" },
  { path: "/event/:eventId/notifications", component: ClubEventComponents.EventPreEvaluationNotifications, guard: "role:club" },
];