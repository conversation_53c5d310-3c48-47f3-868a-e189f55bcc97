import type { AppRoute } from "./types";
import { LockerComponents } from "./components";

export const lockerRoutes: AppRoute[] = [
  {
    path: "/locker",
    exact: true,
    component: LockerComponents.LockerHome,
    guard: "protected",
    label: "Locker",
    showInNav: true,
  },
  {
    path: "/locker/shop/:category?",
    component: LockerComponents.ProductListing,
    guard: "protected",
    label: "Shop",
    showInNav: true,
  },
  {
    path: "/locker/product/:productId",
    component: LockerComponents.ProductDetail,
    guard: "protected",
  },
  {
    path: "/locker/cart",
    component: LockerComponents.Cart,
    guard: "protected",
    label: "Cart",
    showInNav: true,
  },
  {
    path: "/locker/checkout",
    component: LockerComponents.Checkout,
    guard: "protected",
  },
  {
    path: "/locker/order-confirmation",
    component: LockerComponents.OrderConfirmation,
    guard: "protected",
  },
  {
    path: "/locker/orders",
    component: LockerComponents.ComingSoonWithNav,
    guard: "protected",
    label: "Orders",
    showInNav: true,
  },
  {
    path: "/locker/wishlist",
    component: LockerComponents.ComingSoonWithNav,
    guard: "protected",
    label: "Wishlist",
    showInNav: true,
  },
  {
    path: "/locker/size-guide",
    component: LockerComponents.ComingSoonWithNav,
    guard: "protected",
  },
  {
    path: "/locker/drops",
    component: LockerComponents.DropsPage,
    guard: "protected",
    label: "Drops",
    showInNav: true,
  },
  {
    path: "/locker/admin/drops",
    component: LockerComponents.DropsAdmin,
    guard: "role:admin",
  },
];