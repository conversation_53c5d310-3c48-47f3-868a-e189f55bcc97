import React from "react";
import type { AppRoute } from "./types";
import { EvaluationComponents } from "./components";

export const evaluationRoutes: AppRoute[] = [
  // Public evaluation routes
  {
    path: "/evaluation/pre/:preEvaluationId",
    component: EvaluationComponents.PlayerSelfEvaluationForm,
    guard: "public",
  },
  {
    path: "/pre-evaluation-test.html",
    component: EvaluationComponents.PlayerSelfEvaluationForm,
    guard: "public",
  },

  // Authenticated evaluation routes
  {
    path: "/evaluation/demo",
    component: EvaluationComponents.EvaluationSystemDemo,
    guard: "protected",
  },
  {
    path: "/player/:playerId/evaluation-history",
    component: EvaluationComponents.PlayerEvaluationHistory,
    guard: "protected",
  },
  {
    path: "/player/:playerId/progress",
    component: EvaluationComponents.PlayerProgressView,
    guard: "protected",
  },
  {
    path: "/player/:playerId/training-history",
    component: EvaluationComponents.TrainingHistoryPage,
    guard: "protected",
  },
];