import type { AppRoute } from "./types";
import { DevComponents } from "./components";

export const devRoutes: AppRoute[] = [
  { path: "/dev", component: DevComponents.DesignSystem, guard: "role:dev" },
  { path: "/dev/buttons", component: DevComponents.ButtonShowcase, guard: "role:dev" },
  { path: "/dev/event-cards", component: DevComponents.EventCardShowcase, guard: "role:dev" },
  { path: "/dev/currency", component: DevComponents.CurrencyTest, guard: "role:dev" },
  { path: "/dev/perform-debug", component: DevComponents.PerformDebugModeDemo, guard: "role:dev" },
  { path: "/dev/evaluation-system", component: DevComponents.EvaluationSystemDemo, guard: "role:dev" },
  { path: "/dev/background-debug", component: DevComponents.BackgroundDebugReport, guard: "role:dev" },
  { path: "/dev/test-backgrounds", component: DevComponents.TestBackgroundColors, guard: "role:dev" },
  { path: "/dev/database-tables", component: DevComponents.CheckDatabaseTables, guard: "role:dev" },
  { path: "/dev/sms-status", component: DevComponents.SimpleSmsStatusPage, guard: "role:dev" },
];