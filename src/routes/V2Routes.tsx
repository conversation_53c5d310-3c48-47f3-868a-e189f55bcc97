// src/routes/V2Routes.tsx
import React, { Suspense } from 'react';
import { Switch, Route, Redirect } from 'react-router-dom';
import type { AppRoute } from './types';
import { V2Components } from './components';

// Design system components removed - now using main /design-system route

const LoadingFallback = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '100vh',
    backgroundColor: '#0a0a0a',
    color: '#666',
    fontSize: '14px',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
  }}>
    Loading...
  </div>
);

export const v2Routes: AppRoute[] = [
  {
    path: '/v2/perform',
    component: V2Components.Perform,
    guard: 'protected',
    label: 'Perform',
    showInNav: true,
  },
  {
    path: '/v2/perform/coach',
    component: V2Components.CoachPerform,
    guard: 'role:coach',
    label: 'Coach Perform',
  },
  {
    path: '/v2/perform/player',
    component: V2Components.PlayerPerform,
    guard: 'protected',
    label: 'Player Perform',
  },
  {
    path: '/v2/perform/parent',
    component: V2Components.ParentPerform,
    guard: 'protected',
    label: 'Parent Perform',
  },
  {
    path: '/v2/perform/member',
    component: V2Components.MemberPerform,
    guard: 'protected',
    label: 'Member Perform',
  },
  {
    path: '/v2/pulse',
    component: V2Components.PulseFeed,
    guard: 'protected',
    label: 'Pulse',
    showInNav: true,
  },
  {
    path: '/v2/admin/content-sync',
    component: V2Components.ContentSync,
    guard: 'role:admin',
    label: 'Content Sync',
  },
  {
    path: '/v2/admin/widget-builder',
    component: V2Components.WidgetBuilder,
    guard: 'role:admin',
    label: 'Widget Builder',
  },
];

export const V2Routes: React.FC = () => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <Switch>
        {/* Perform Routes - More specific routes first */}
        {/* <Route exact path="/v2/perform/debug" component={PerformDebug} /> - Disabled for production */}
        <Route exact path="/v2/perform/coach" component={V2Components.CoachPerform} />
        <Route exact path="/v2/perform/player" component={V2Components.PlayerPerform} />
        <Route exact path="/v2/perform/parent" component={V2Components.ParentPerform} />
        <Route exact path="/v2/perform/member" component={V2Components.MemberPerform} />
        <Route exact path="/v2/perform" component={V2Components.Perform} />
        
        {/* Evaluation Routes */}
        {/* <Route exact path="/v2/evaluation/showcase" component={EvaluationShowcase} /> - Disabled for production */}
        
        {/* Pulse Routes for Testing */}
        <Route exact path="/v2/pulse" component={V2Components.PulseFeed} />
        {/* <Route exact path="/v2/pulse-comparison" component={PulseComparison} /> */}
        {/* <Route exact path="/v2/pulse-feed" component={PulseFeedV2} /> */}
        
        {/* Admin Routes */}
        <Route exact path="/v2/admin/content-sync" component={V2Components.ContentSync} />
        <Route exact path="/v2/admin/widget-builder" component={V2Components.WidgetBuilder} />
        
        {/* Redirect v2 design system routes to main design-system route */}
        <Route exact path="/v2/design-system/minimal" render={() => <Redirect to="/design-system" />} />
        <Route exact path="/v2/design-system" render={() => <Redirect to="/design-system" />} />
        <Route exact path="/v2" render={() => <Redirect to="/design-system" />} />
        
        {/* Catch-all for debugging */}
        <Route path="/v2/*" render={({ location }) => {
          return <div className="p-8 text-white">404 - Route not found: {location.pathname}</div>;
        }} />
      </Switch>
    </Suspense>
  );
};