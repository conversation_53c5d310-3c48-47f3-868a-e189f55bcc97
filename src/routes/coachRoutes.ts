import React from "react";
import type { AppRoute } from "./types";
import { clubRoutes } from "./clubRoutes";
import { CoachComponents } from "./components";

export const coachRoutes: AppRoute[] = [
  { path: "/dashboard", component: CoachComponents.CoachDashboard, guard: "role:coach" },
  { path: "/home", component: CoachComponents.CoachHome, guard: "role:coach" },
  { path: "/player-management", component: CoachComponents.PlayerManagement, guard: "role:coach" },
  { path: "/user-management", component: CoachComponents.UserManagement, guard: "role:coach" },
  { path: "/event-management", component: CoachComponents.EventManagement, guard: "role:coach" },
  { path: "/communication-management", component: CoachComponents.CommunicationManagement, guard: "role:coach" },
  { path: "/session-planner", component: Coach<PERSON>omponents.SessionPlanner, guard: "role:coach" },
  { path: "/match-setup", component: CoachComponents.MatchSetup, guard: "role:coach" },
  { path: "/perform-framework-management", component: CoachComponents.PerformFramework, guard: "role:coach" },
  { path: "/perform/criteria-manager", component: CoachComponents.CriteriaManager, guard: "role:coach" },

  // Club-scoped routes
  { path: "/club/:clubId", children: clubRoutes, guard: "role:coach" },
];