// Centralized component constants for all lazy-loaded route components
import React from 'react';

// Public Components
export const PublicComponents = {
  Splash: React.lazy(() => import('@/pages/Splash')),
  Login: React.lazy(() => import('@/pages/UpdatedLoginV2')),
  Signup: React.lazy(() => import('@/pages/Signup')),
  Register: React.lazy(() => import('@/pages/registration/OpenRegistration')),
  ResetPassword: React.lazy(() => import('@/pages/ResetPassword')),
  PlayerSelfEvaluation: React.lazy(() => import('@/pages/section/Coach/components/PlayerSelfEvaluation/PlayerSelfEvaluationForm')),
  DesignSystem: React.lazy(() => import('@/pages/v2/DesignSystem')),
  ButtonShowcase: React.lazy(() => import('@/pages/section/ButtonShowcase')),
  EventCardShowcase: React.lazy(() => import('@/pages/design-system/EventCardShowcase')),
};

// Protected Components
export const ProtectedComponents = {
  Home: React.lazy(() => import('@/pages/Home')),
  Account: React.lazy(() => import('@/pages/Account')),
  Membership: React.lazy(() => import('@/pages/Membership')),
  Checkout: React.lazy(() => import('@/pages/CheckoutPage')),
  Activities: React.lazy(() => import('@/pages/Activities')),
  Family: React.lazy(() => import('@/pages/Family')),
  Events: React.lazy(() => import('@/pages/Events')),
  ViewIDP: React.lazy(() => import('@/pages/ViewIDP')),
  ImproveIDP: React.lazy(() => import('@/pages/ImproveIDP')),
  WelcomeFollow: React.lazy(() => import('@/pages/section/Follow/WelcomeFollow')),
  GenerateCode: React.lazy(() => import('@/pages/GenerateCode')),
  AddFamily: React.lazy(() => import('@/pages/AddFamily')),
  VideoLibrary: React.lazy(() => import('@/features/pulse/pages/videos/VideoLibrary')),
  InstantSession: React.lazy(() => import('@/components/placeholders/InstantSessionPlaceholder')),
  DomainsIndex: React.lazy(() => import('@/components/domains/DomainsIndex')),
  IdentityProfile: React.lazy(() => import('@/components/domains/DomainMockupPages').then(module => ({ default: module.IdentityProfileMockup }))),
  Organization: React.lazy(() => import('@/components/domains/DomainMockupPages').then(module => ({ default: module.OrganizationMockup }))),
  PeopleRoles: React.lazy(() => import('@/components/domains/DomainMockupPages').then(module => ({ default: module.PeopleRolesMockup }))),
  Communication: React.lazy(() => import('@/components/domains/DomainMockupPages').then(module => ({ default: module.CommunicationMockup }))),
  Evaluation: React.lazy(() => import('@/components/domains/DomainMockupPages').then(module => ({ default: module.EvaluationMockup }))),
};

// V2 Components
export const V2Components = {
  Perform: React.lazy(() => import('@/pages/v2/perform/Perform')),
  CoachPerform: React.lazy(() => import('@/pages/v2/perform/CoachPerform')),
  MemberPerform: React.lazy(() => import('@/pages/v2/perform/MemberPerform')),
  ParentPerform: React.lazy(() => import('@/pages/v2/perform/ParentPerform')),
  PlayerPerform: React.lazy(() => import('@/pages/v2/perform/PlayerPerform')),
  PulsePage: React.lazy(() => import('@/pages/v2/pulse/PulsePage')),
  DesignSystem: React.lazy(() => import('@/pages/v2/DesignSystem')),
  PulseFeed: React.lazy(() => import('@/features/pulse/pages/feed/PulseFeed')),
  ContentSync: React.lazy(() => import('@/features/pulse/pages/admin/ContentSync')),
  WidgetBuilder: React.lazy(() => import('@/features/pulse/pages/admin/WidgetBuilder')),
} as const;

// Club Components
export const ClubComponents = {
  ClubsList: React.lazy(() => import('@/pages/section/Coach/supporting/ClubManagement/ClubsList')),
  ClubSettings: React.lazy(() => import('@/pages/section/Coach/supporting/ClubManagement/ClubSettings')),
  ClubPlayers: React.lazy(() => import('@/pages/section/Coach/supporting/ClubManagement/ClubPlayers')),
  ClubVerification: React.lazy(() => import('@/pages/section/Coach/supporting/ClubManagement/ClubVerification')),
  EditClubPage: React.lazy(() => import('@/pages/section/Coach/supporting/ClubManagement/EditClubPage')),
  ClubAdministrators: React.lazy(() => import('@/pages/section/Coach/supporting/ClubManagement/ClubAdministrators')),
  NewClubAdministrators: React.lazy(() => import('@/pages/section/Coach/supporting/ClubManagement/NewClubAdministrators')),
  ClubAnalytics: React.lazy(() => import('@/pages/section/Coach/supporting/ClubManagement/analytics/ClubAnalytics')),
} as const;

// Coach Components
export const CoachComponents = {
  CoachDashboard: React.lazy(() => import('@/pages/section/Coach/CoachDashboard')),
  CoachHome: React.lazy(() => import('@/pages/section/Coach/CoachHome')),
  PlayerManagement: React.lazy(() => import('@/pages/section/Coach/PlayerManagement')),
  UserManagement: React.lazy(() => import('@/pages/section/Coach/UserManagement')),
  EventManagement: React.lazy(() => import('@/pages/section/Coach/EventManagement')),
  CommunicationManagement: React.lazy(() => import('@/pages/section/Coach/CommunicationManagement')),
  SessionPlanner: React.lazy(() => import('@/pages/section/Coach/SessionPlanner')),
  MatchSetup: React.lazy(() => import('@/pages/section/Coach/MatchSetup')),
  PerformFramework: React.lazy(() => import('@/pages/section/Coach/supporting/PerformFramework')),
  CriteriaManager: React.lazy(() => import('@/pages/section/Coach/supporting/PerformFramework/components/CriteriaManager').then(module => ({ default: module.CriteriaManager }))),
} as const;

// Club Event Components
export const ClubEventComponents = {
  EventsList: React.lazy(() => import("@/pages/section/Coach/events/EventsList")),
  EventPage: React.lazy(() => import("@/pages/section/Coach/events/EventPage")),
  EventEvaluation: React.lazy(() => import("@/pages/section/Coach/events/EventEvaluation")),
  SelfEvaluationSummary: React.lazy(() => import("@/pages/section/Coach/events/SelfEvaluationSummary")),
  EventParticipantsList: React.lazy(() => import("@/pages/section/Coach/events/EventParticipantsList")),
  SmsStatusPage: React.lazy(() => import("@/pages/section/Coach/events/SmsStatusPage")),
  EventPreEvaluationNotifications: React.lazy(() => import("@/pages/section/Coach/events/EventPreEvaluationNotifications")),
} as const;

// Team Components
export const TeamComponents = {
  TeamFlat: React.lazy(() => import('@/pages/section/Coach/TeamFlat')),
  EditTeamShadow: React.lazy(() => import('@/pages/section/Coach/EditTeamShadow')),
  TeamAddPlayer: React.lazy(() => import('@/pages/section/Coach/supporting/ClubManagement/TeamAddPlayer')),
  TeamComms: React.lazy(() => import('@/pages/section/Coach/TeamComms/TeamComms')),
  SessionCalendar: React.lazy(() => import('@/pages/section/Coach/SessionCalendar/SessionCalendar')),
  TeamStats: React.lazy(() => import('@/pages/section/Coach/TeamStats/TeamStats')),
  TeamObjectives: React.lazy(() => import('@/pages/section/Coach/TeamObjectives')),
  TeamEvaluation: React.lazy(() => import('@/pages/section/Coach/TeamEvaluation')),
  EvaluationsPage: React.lazy(() => import('@/pages/section/Coach/evaluations/EvaluationsPage')),
  PlayerObjectives: React.lazy(() => import('@/pages/section/Coach/PlayerObjectives')),
  EventsList: React.lazy(() => import('@/pages/section/Coach/events/EventsList')),
  EventPage: React.lazy(() => import('@/pages/section/Coach/events/EventPage')),
  EventEvaluation: React.lazy(() => import('@/pages/section/Coach/events/EventEvaluation')),
  SelfEvaluationSummary: React.lazy(() => import('@/pages/section/Coach/events/SelfEvaluationSummary')),
} as const;

// Evaluation Components
export const EvaluationComponents = {
  PlayerSelfEvaluationForm: React.lazy(() => import('@/pages/section/Coach/components/PlayerSelfEvaluation/PlayerSelfEvaluationForm')),
  EvaluationSystemDemo: React.lazy(() => import('@/components/v2/EvaluationSystemDemo')),
  PlayerEvaluationHistory: React.lazy(() => import('@/pages/section/Player/PlayerEvaluationHistory')),
  PlayerProgressView: React.lazy(() => import('@/pages/section/Player/PlayerProgressView')),
  TrainingHistoryPage: React.lazy(() => import('@/pages/section/Player/TrainingHistoryPage')),
} as const;

// Locker Components
export const LockerComponents = {
  LockerHome: React.lazy(() => import('@/features/locker/pages/shop/LockerHome')),
  ProductListing: React.lazy(() => import('@/features/locker/pages/shop/ProductListing')),
  ProductDetail: React.lazy(() => import('@/features/locker/pages/products/ProductDetail')),
  Cart: React.lazy(() => import('@/features/locker/pages/cart/Cart')),
  Checkout: React.lazy(() => import('@/features/locker/pages/checkout/Checkout')),
  OrderConfirmation: React.lazy(() => import('@/features/locker/pages/checkout/OrderConfirmation')),
  ComingSoonWithNav: React.lazy(() => import('@/components/ComingSoonWithNav')),
  DropsPage: React.lazy(() => import('@/features/locker/pages/drops/DropsPage')),
  DropsAdmin: React.lazy(() => import('@/features/locker/admin/DropsAdmin')),
} as const;

// Dev Components
export const DevComponents = {
  DesignSystem: React.lazy(() => import('@/pages/v2/DesignSystem')),
  ButtonShowcase: React.lazy(() => import('@/pages/section/ButtonShowcase')),
  EventCardShowcase: React.lazy(() => import('@/pages/design-system/EventCardShowcase')),
  CurrencyTest: React.lazy(() => import('@/pages/CurrencyTest')),
  PerformDebugModeDemo: React.lazy(() => import('@/pages/section/Coach/dashboards/PerformDebugModeDemo')),
  EvaluationSystemDemo: React.lazy(() => import('@/components/v2/EvaluationSystemDemo')),
  BackgroundDebugReport: React.lazy(() => import('@/components/debug/BackgroundDebugReport')),
  TestBackgroundColors: React.lazy(() => import('@/components/test/TestBackgroundColors')),
  CheckDatabaseTables: React.lazy(() => import('@/pages/CheckDatabaseTables')),
  SimpleSmsStatusPage: React.lazy(() => import('@/pages/section/Coach/events/SimpleSmsStatusPage')),
} as const;

// Component Registry
export const ComponentRegistry = {
  PublicComponents,
  ProtectedComponents,
  V2Components,
  ClubComponents,
  CoachComponents,
  ClubEventComponents,
  TeamComponents,
  EvaluationComponents,
  LockerComponents,
  DevComponents,
};