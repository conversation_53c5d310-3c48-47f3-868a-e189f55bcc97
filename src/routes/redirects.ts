import type { AppRoute } from "./types";

export const redirects: AppRoute[] = [
  {path: "/ai", redirectTo: "/home"},

  // Profile → Account
  { path: "/profile", redirectTo: "/account" },

  // Shop → Locker
  { path: "/shop", redirectTo: "/locker" },
  { path: "/shop/cart", redirectTo: "/locker/cart" },
  { path: "/shop/product/:productId", redirectTo: "/locker/product/:productId" },
  { path: "/shop/checkout", redirectTo: "/locker/checkout" },
  { path: "/shop/order-confirmation", redirectTo: "/locker/order-confirmation" },

  // Coach legacy → new
  { path: "/coach/add-club", redirectTo: "/coach/club/create" },
  { path: "/coach/clubs", redirectTo: "/coach/clubs/management" },
  { path: "/coach/edit-club/:id", redirectTo: "/coach/club/:id/edit" },

  // Club legacy → new
  { path: "/coach/club/:clubId/teams", redirectTo: "/coach/club/:clubId" },
  { path: "/coach/club/:clubId/team/:teamId/db", redirectTo: "/coach/club/:clubId/team/:teamId" },
  { path: "/coach/club/:clubId/team/:teamId/squad", redirectTo: "/coach/club/:clubId/team/:teamId" },

  // Club event legacy → new
  { path: "/coach/club/:clubId/event/:eventId/attendance", redirectTo: "/coach/club/:clubId/event/:eventId" },
  { path: "/coach/club/:clubId/event/:eventId/invite", redirectTo: "/coach/club/:clubId/event/:eventId" },

  // Root → Home
  { path: "/", exact: true, redirectTo: "/home" },

  // Catch-all → Home
  { path: "*", redirectTo: "/home" },
];