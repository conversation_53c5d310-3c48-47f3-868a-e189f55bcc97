import { lazy } from "react";
import { AppRoute } from "./types";

// Lazy load V2 Perform pages
const Perform = lazy(() => import("@/pages/v2/perform/Perform"));
const CoachPerform = lazy(() => import("@/pages/v2/perform/CoachPerform"));
const PlayerPerform = lazy(() => import("@/pages/v2/perform/PlayerPerform"));
const ParentPerform = lazy(() => import("@/pages/v2/perform/ParentPerform"));
const MemberPerform = lazy(() => import("@/pages/v2/perform/MemberPerform"));

// Lazy load V2 Pulse pages
const PulseV2 = lazy(() => import("@/features/pulse/pages/feed/PulseFeed"));

// Lazy load V2 Admin pages
const ContentSyncDashboard = lazy(() => import("@/features/pulse/pages/admin/ContentSync"));
const WidgetBuilder = lazy(() => import("@/features/pulse/pages/admin/WidgetBuilder"));

export const v2Routes: AppRoute[] = [
  {
    path: "/perform/coach",
    exact: true,
    component: CoachPerform,
    guard: "role:coach",
    label: "Coach Perform V2",
    showInNav: false,
  },
  {
    path: "/perform/player",
    exact: true,
    component: PlayerPerform,
    guard: "protected",
    label: "Player Perform V2",
    showInNav: false,
  },
  {
    path: "/perform/parent",
    exact: true,
    component: ParentPerform,
    guard: "protected",
    label: "Parent Perform V2",
    showInNav: false,
  },
  {
    path: "/perform/member",
    exact: true,
    component: MemberPerform,
    guard: "protected",
    label: "Member Perform V2",
    showInNav: false,
  },
  {
    path: "/perform",
    exact: true,
    component: Perform,
    guard: "protected",
    label: "Perform V2",
    showInNav: false,
  },
  {
    path: "/pulse",
    exact: true,
    component: PulseV2,
    guard: "protected",
    label: "Pulse V2",
    showInNav: false,
  },
  {
    path: "/admin/content-sync",
    exact: true,
    component: ContentSyncDashboard,
    guard: "role:admin",
    label: "Content Sync Dashboard",
    showInNav: false,
  },
  {
    path: "/admin/widget-builder",
    exact: true,
    component: WidgetBuilder,
    guard: "role:admin",
    label: "Widget Builder",
    showInNav: false,
  },
  // Redirects for V2 design system routes
  {
    path: "/design-system/minimal",
    exact: true,
    redirectTo: "/design-system",
    guard: "public",
  },
  {
    path: "/design-system",
    exact: true,
    redirectTo: "/design-system",
    guard: "public",
  },
];