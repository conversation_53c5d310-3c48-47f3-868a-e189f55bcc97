import type { AppRoute } from "./types";
import { ProtectedComponents } from './components';

export const protectedRoutes: AppRoute[] = [
  {
    path: "/home",
    component: ProtectedComponents.Home,
    guard: "protected",
    label: "Home",
    showInNav: true,
  },
  {
    path: "/account",
    component: ProtectedComponents.Account,
    guard: "protected",
    label: "Account",
    showInNav: true,
  },
  {
    path: "/membership",
    component: ProtectedComponents.Membership,
    guard: "protected",
    label: "Membership",
    showInNav: true,
  },
  {
    path: "/checkout",
    component: ProtectedComponents.Checkout,
    guard: "protected",
    label: "Checkout",
    showInNav: false,
  },
  {
    path: "/activities",
    component: ProtectedComponents.Activities,
    guard: "protected",
    label: "Activities",
    showInNav: true,
  },
  {
    path: "/family",
    component: ProtectedComponents.Family,
    guard: "protected",
    label: "Family",
    showInNav: true,
  },
  {
    path: "/events",
    component: ProtectedComponents.Events,
    guard: "protected",
    label: "Events",
    showInNav: true,
  },
  {
    path: "/view-idp",
    component: ProtectedComponents.ViewIDP,
    guard: "protected",
    label: "View IDP",
    showInNav: false,
  },
  {
    path: "/improveIDP",
    component: ProtectedComponents.ImproveIDP,
    guard: "protected",
    label: "Improve IDP",
    showInNav: false,
  },
  {
    path: "/welcome-follow",
    component: ProtectedComponents.WelcomeFollow,
    guard: "protected",
    label: "Welcome Follow",
    showInNav: false,
  },
  {
    path: "/generate-code",
    component: ProtectedComponents.GenerateCode,
    guard: "protected",
    label: "Generate Code",
    showInNav: false,
  },
  {
    path: "/add-family",
    component: ProtectedComponents.AddFamily,
    guard: "protected",
    label: "Add Family",
    showInNav: false,
  },
  {
    path: "/videos",
    component: ProtectedComponents.VideoLibrary,
    guard: "protected",
    label: "Videos",
    showInNav: true,
  },
  {
    path: "/perform/instant-session/:sessionId",
    component: ProtectedComponents.InstantSession,
    guard: "protected",
    label: "Instant Session",
    showInNav: false,
  },

  // Domain mockup routes
  {
    path: "/domains",
    component: ProtectedComponents.DomainsIndex,
    guard: "protected",
    label: "Domains",
    showInNav: false,
  },
  {
    path: "/domains/identity-profile",
    component: ProtectedComponents.IdentityProfile,
    guard: "protected",
    label: "Identity Profile",
    showInNav: false,
  },
  {
    path: "/domains/organization",
    component: ProtectedComponents.Organization,
    guard: "protected",
    label: "Organization",
    showInNav: false,
  },
  {
    path: "/domains/people-roles",
    component: ProtectedComponents.PeopleRoles,
    guard: "protected",
    label: "People Roles",
    showInNav: false,
  },
  {
    path: "/domains/communication",
    component: ProtectedComponents.Communication,
    guard: "protected",
    label: "Communication",
    showInNav: false,
  },
  {
    path: "/domains/evaluation",
    component: ProtectedComponents.Evaluation,
    guard: "protected",
    label: "Evaluation",
    showInNav: false,
  },
];