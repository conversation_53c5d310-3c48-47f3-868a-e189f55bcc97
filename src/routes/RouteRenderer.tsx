import React, { Suspense } from "react";
import { Route, Redirect, useHistory } from "react-router-dom";
import { AppRoute } from "./types";
import ProtectedRoute from "./wrappers/ProtectedRoute";
import PublicRoute from "./wrappers/PublicRoute";
import RoleProtectedRoute from "./wrappers/RoleProtectedRoute";
import EmailVerificationGuard from "./wrappers/EmailVerificationGuard";
import * as H from 'history';

function wrapWithGuard(route: AppRoute, element: JSX.Element) {
  switch (route.guard) {
    case "protected":
      return <ProtectedRoute>{element}</ProtectedRoute>;
    case "public":
      return <PublicRoute>{element}</PublicRoute>;
    case "email-verified":
      return <EmailVerificationGuard>{element}</EmailVerificationGuard>;
    case "role:coach":
      return <RoleProtectedRoute role="coach">{element}</RoleProtectedRoute>;
    case "role:club":
      return <RoleProtectedRoute role="club">{element}</RoleProtectedRoute>;
    case "role:admin":
      return <RoleProtectedRoute role="admin">{element}</RoleProtectedRoute>;
    default:
      return element;
  }
}

function wrapWithSuspense(element: JSX.Element) {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center h-screen bg-slate-900 text-white">
          Loading...
        </div>
      }
    >
      {element}
    </Suspense>
  );
}

export function renderRoutes(routes: AppRoute[], basePath = "", history?: H.History<H.LocationState>) {
  return routes.map((route, index) => {
    const fullPath = `${basePath}${route.path}`.replace(/\/+$/, "");
    const key = `${fullPath}-${index}`;

    if (route.redirectTo) {
      return (
        <Route path={fullPath} exact={route.exact} key={key}>
          <Redirect to={route.redirectTo} />
        </Route>
      );
    }

    if (route.children) {
      return (
        <Route
          key={key}
          path={fullPath}
          render={({ match }) => renderRoutes(route.children!, match.url, history)}
        />
      );
    }

    if (route.component) {
      const Element = route.component;
      return (
        <Route
          key={key}
          exact={route.exact}
          path={fullPath}
          render={(props) =>
            wrapWithGuard(route, wrapWithSuspense(<Element {...props} />))
          }
        />
      );
    }

    return null;
  });
}