import { useState } from 'react';
import {
  IonButton,
  IonContent,
  IonInput,
  IonItem,
  IonLabel,
  IonList,
  IonPage,
  useIonToast,
  useIonLoading,
  IonIcon,
} from '@ionic/react';
import { eye, eyeOff } from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import { StandardHeader } from '../components/StandardHeader';

export function SignupPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [inviteCode, setInviteCode] = useState('');
  const [isCodeVerified, setIsCodeVerified] = useState(false);
  const [verifiedCodeData, setVerifiedCodeData] = useState<any>(null);
  const [showPassword, setShowPassword] = useState(false);

  const [showLoading, hideLoading] = useIonLoading();
  const [showToast] = useIonToast();

  const verifyInviteCode = async (e: React.FormEvent) => {
    e.preventDefault();
    await showLoading();
    
    try {
      const { data: inviteData, error: inviteError } = await supabase
        .from('invite_codes')
        .select('*')
        .eq('code', inviteCode)
        .is('used_by', null)
        .eq('is_valid', true)
        .single();

      if (inviteError || !inviteData) {
        await showToast({ 
          message: 'Invalid or expired invite code', 
          duration: 3000,
          color: 'danger'
        });
        return;
      }

      setIsCodeVerified(true);
      setVerifiedCodeData(inviteData);
      await showToast({ 
        message: 'Code verified successfully!', 
        duration: 2000,
        color: 'success'
      });
    } catch (error) {
      await showToast({ 
        message: 'Error verifying code', 
        duration: 3000,
        color: 'danger'
      });
    } finally {
      await hideLoading();
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    await showLoading();
    
    try {
      const { data: {user}, error } = await supabase.auth.signUp({ 
        email, 
        password 
      });

      if (error) throw error;

      if (user) {
        await supabase
          .from('invite_codes')
          .update({
            used_by: user.id,
            used_at: new Date().toISOString(),
            is_valid: false
          })
          .eq('code', verifiedCodeData.code);

        await showToast({ 
          message: 'Check your email for the confirmation link!',
          duration: 5000,
          color: 'success'
        });
      }
    } catch (error: any) {
      await showToast({ 
        message: error.error_description || error.message,
        duration: 5000,
        color: 'danger'
      });
    } finally {
      await hideLoading();
    }
  };

  return (
    <IonPage>
      <StandardHeader 
        showBackButton={true}
        backUrl="/login"
      />
      
      <IonContent className="ion-padding">
        {!isCodeVerified ? (
          // Invite Code Form
          <form onSubmit={verifyInviteCode}>
            <h2 className="text-xl font-bold mb-4">Enter Invite Code</h2>
            <IonList>
              <IonItem>
                <IonLabel position="stacked">Invite Code</IonLabel>
                <IonInput
                  value={inviteCode}
                  onIonChange={e => setInviteCode(e.detail.value!)}
                  placeholder="Enter your invite code"
                  required
                />
              </IonItem>
            </IonList>
            
            <div className="ion-padding">
              <IonButton expand="block" type="submit">
                Verify Code
              </IonButton>
            </div>
          </form>
        ) : (
          // Signup Form
          <form onSubmit={handleSignUp}>
            <h2 className="text-xl font-bold mb-4">Create Account</h2>
            <IonList>
              <IonItem>
                <IonLabel position="stacked">Email</IonLabel>
                <IonInput
                  type="email"
                  value={email}
                  onIonChange={e => setEmail(e.detail.value!)}
                  placeholder="Enter your email"
                  required
                />
              </IonItem>

              <IonItem>
                <IonLabel position="stacked">Password</IonLabel>
                <div className="relative w-full">
                  <IonInput
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onIonChange={e => setPassword(e.detail.value!)}
                    placeholder="Enter your password"
                    required
                  />
                  <button 
                    type="button"
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 z-10 text-gray-500 hover:text-gray-700 focus:outline-none"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    <IonIcon icon={showPassword ? eyeOff : eye} className="h-5 w-5" />
                  </button>
                </div>
              </IonItem>
            </IonList>
            
            <div className="ion-padding">
              <IonButton expand="block" type="submit">
                Sign Up
              </IonButton>
            </div>
          </form>
        )}
      </IonContent>
    </IonPage>
  );
}

// Add default export
export default SignupPage;