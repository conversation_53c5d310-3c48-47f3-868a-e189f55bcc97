import React from 'react';
import { 
  IonPage, 
  IonContent, 
  IonHeader, 
  IonToolbar, 
  IonTitle, 
  IonGrid, 
  IonRow, 
  IonCol,
  IonCard,
  IonCardHeader,
  IonCardTitle,
  IonCardContent,
  IonItemDivider,
  IonLabel,
  IonText,
  IonButton,
  IonIcon,
  IonSpinner
} from '@ionic/react';
import { 
  add, 
  arrowForward, 
  checkmark, 
  closeCircle, 
  create, 
  trash, 
  star, 
  search,
  location,
  heart,
  calendar,
  person,
  barbell,
  football,
  trophy,
  clipboard,
  analytics,
  cog
} from 'ionicons/icons';

// Import our enhanced button components and styles
import EnhancedShotButton from '../../../components/common/EnhancedShotButton';
import { SHOT_COLORS } from '../../../styles/updatedShotColors';
import { buttonStyles, getOutlineButtonStyle } from '../../../styles/updatedButtonStyles';

const ButtonShowcasePage: React.FC = () => {
  // Array of core colors
  const coreColors: (keyof typeof SHOT_COLORS)[] = [
    'teal',
    'purple', 
    'gold',
    'black',
    'white'
  ];
  
  // Array of PERFORM system colors
  const performColors: (keyof typeof SHOT_COLORS)[] = [
    'electricBlue',
    'forestGreen',
    'burntOrange',
    'warmCoral',
    'charcoalGrey',
    'crimsonRed'
  ];
  
  // Array of common icons
  const icons = [
    { name: 'add', icon: add },
    { name: 'checkmark', icon: checkmark },
    { name: 'arrow-forward', icon: arrowForward },
    { name: 'close-circle', icon: closeCircle },
    { name: 'create', icon: create },
    { name: 'trash', icon: trash },
    { name: 'star', icon: star },
    { name: 'search', icon: search },
    { name: 'location', icon: location },
    { name: 'heart', icon: heart },
    { name: 'calendar', icon: calendar },
    { name: 'person', icon: person },
    { name: 'barbell', icon: barbell },
    { name: 'football', icon: football },
    { name: 'trophy', icon: trophy },
    { name: 'clipboard', icon: clipboard },
    { name: 'analytics', icon: analytics },
    { name: 'cog', icon: cog }
  ];
  
  // Helper function to capitalize first letter
  const capitalize = (str: string) => {
    return str.charAt(0).toUpperCase() + str.slice(1);
  };
  
  // For demo - show fallback to using the raw buttonStyles when EnhancedShotButton is not yet fully available
  const fallbackButtons = () => (
    <div style={{ margin: '16px 0' }}>
      <IonButton style={buttonStyles.teal} className="m-1">
        Teal (Direct Style)
      </IonButton>
      <IonButton style={buttonStyles.purple} className="m-1">
        Purple (Direct Style)
      </IonButton>
      <IonButton style={buttonStyles.gold} className="m-1">
        Gold (Direct Style)
      </IonButton>
      <IonButton fill="outline" style={getOutlineButtonStyle('electricBlue')} className="m-1">
        Electric Blue (Outline)
      </IonButton>
    </div>
  );
  
  return (
    <IonPage className="button-showcase-page">
      <IonHeader>
        <IonToolbar style={{ '--background': SHOT_COLORS.black, '--color': SHOT_COLORS.white }}>
          <IonTitle>SHOT Button Showcase</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent style={{ '--background': '#111111' }}>
        <IonGrid>
          <IonRow>
            <IonCol size="12">
              <IonCard style={{ margin: '16px' }}>
                <IonCardHeader>
                  <IonCardTitle>SHOT Color System & Button Guide</IonCardTitle>
                </IonCardHeader>
                <IonCardContent>
                  <p>
                    This showcase demonstrates the complete SHOT color system implemented as buttons
                    with various styles and configurations. Use this as a reference when implementing
                    UI components across the app.
                  </p>
                </IonCardContent>
              </IonCard>
            </IonCol>
          </IonRow>
          
          {/* Core Colors Section */}
          <IonRow>
            <IonCol size="12">
              <IonCard>
                <IonCardHeader>
                  <IonCardTitle>Core Colors</IonCardTitle>
                </IonCardHeader>
                <IonCardContent>
                  <IonText>
                    <p>The main SHOT brand colors that form the foundation of our design system:</p>
                    <ul>
                      <li><strong style={{ color: SHOT_COLORS.teal }}>Teal (#1ABC9C)</strong>: Entry point energy, new journeys, early momentum</li>
                      <li><strong style={{ color: SHOT_COLORS.purple }}>Purple (#6B00DB)</strong>: Ownership, mastery, core identity</li>
                      <li><strong style={{ color: SHOT_COLORS.gold }}>Gold (#F7B613)</strong>: Impact, rewards, standout moments</li>
                      <li><strong>Black & White</strong>: Structure, contrast, navigation</li>
                    </ul>
                  </IonText>
                  
                  {/* Solid Buttons - Core Colors */}
                  <IonItemDivider>
                    <IonLabel>Solid Buttons</IonLabel>
                  </IonItemDivider>
                  <div style={{ margin: '16px 0' }}>
                    {coreColors.map((color) => (
                      <IonButton 
                        key={`solid-${color}`} 
                        style={buttonStyles[color]} 
                        className={`m-1 shot-button-${color.toLowerCase()}`}
                      >
                        {capitalize(color)}
                      </IonButton>
                    ))}
                  </div>
                  
                  {/* Outline Buttons - Core Colors */}
                  <IonItemDivider>
                    <IonLabel>Outline Buttons</IonLabel>
                  </IonItemDivider>
                  <div style={{ margin: '16px 0' }}>
                    {coreColors.map((color) => (
                      <IonButton key={`outline-${color}`} fill="outline" style={getOutlineButtonStyle(color)} className="m-1">
                        {capitalize(color)}
                      </IonButton>
                    ))}
                  </div>
                  
                  {/* Full Width Buttons - Core Colors */}
                  <IonItemDivider>
                    <IonLabel>Full Width Buttons</IonLabel>
                  </IonItemDivider>
                  <div style={{ margin: '16px 0' }}>
                    {coreColors.map((color) => (
                      <IonButton 
                        key={`full-${color}`} 
                        expand="block" 
                        style={buttonStyles[color]}
                        className="my-1"
                      >
                        {capitalize(color)} - Full Width
                      </IonButton>
                    ))}
                  </div>
                </IonCardContent>
              </IonCard>
            </IonCol>
          </IonRow>
          
          {/* PERFORM Colors Section */}
          <IonRow>
            <IonCol size="12">
              <IonCard>
                <IonCardHeader>
                  <IonCardTitle>PERFORM System Colors</IonCardTitle>
                </IonCardHeader>
                <IonCardContent>
                  <IonText>
                    <p>Colors supporting the PERFORM system and Five Corners:</p>
                    <ul>
                      <li><strong style={{ color: SHOT_COLORS.electricBlue }}>Electric Blue (#296DFF)</strong>: Technical – precision, skill, structure</li>
                      <li><strong style={{ color: SHOT_COLORS.forestGreen }}>Forest Green (#2E8B57)</strong>: Physical – energy, health, movement</li>
                      <li><strong style={{ color: SHOT_COLORS.burntOrange }}>Burnt Orange (#FF6F3C)</strong>: Psychological – mindset, focus, resilience</li>
                      <li><strong style={{ color: SHOT_COLORS.warmCoral }}>Warm Coral (#FF5D73)</strong>: Social – communication, team play, connection</li>
                      <li><strong style={{ color: SHOT_COLORS.charcoalGrey }}>Charcoal Grey (#5C5C5C)</strong>: Positional – depth, discipline, role-specific growth</li>
                      <li><strong style={{ color: SHOT_COLORS.crimsonRed }}>Crimson Red (#E63946)</strong>: Functional only – for remove, cancel, warning</li>
                    </ul>
                  </IonText>
                  
                  {/* Solid Buttons - PERFORM Colors */}
                  <IonItemDivider>
                    <IonLabel>Solid Buttons</IonLabel>
                  </IonItemDivider>
                  <div style={{ margin: '16px 0' }}>
                    {performColors.map((color) => (
                      <IonButton key={`solid-${color}`} style={buttonStyles[color]} className="m-1">
                        {capitalize(color.replace(/([A-Z])/g, ' $1').trim())}
                      </IonButton>
                    ))}
                  </div>
                  
                  {/* Outline Buttons - PERFORM Colors */}
                  <IonItemDivider>
                    <IonLabel>Outline Buttons</IonLabel>
                  </IonItemDivider>
                  <div style={{ margin: '16px 0' }}>
                    {performColors.map((color) => (
                      <IonButton key={`outline-${color}`} fill="outline" style={getOutlineButtonStyle(color)} className="m-1">
                        {capitalize(color.replace(/([A-Z])/g, ' $1').trim())}
                      </IonButton>
                    ))}
                  </div>
                  
                  {/* Full Width Buttons - PERFORM Colors */}
                  <IonItemDivider>
                    <IonLabel>Full Width Buttons</IonLabel>
                  </IonItemDivider>
                  <div style={{ margin: '16px 0' }}>
                    {performColors.map((color) => (
                      <IonButton 
                        key={`full-${color}`} 
                        expand="block" 
                        style={buttonStyles[color]}
                        className="my-1"
                      >
                        {capitalize(color.replace(/([A-Z])/g, ' $1').trim())} - Full Width
                      </IonButton>
                    ))}
                  </div>
                </IonCardContent>
              </IonCard>
            </IonCol>
          </IonRow>
          
          {/* Buttons with Icons */}
          <IonRow>
            <IonCol size="12">
              <IonCard>
                <IonCardHeader>
                  <IonCardTitle>Buttons with Icons</IonCardTitle>
                </IonCardHeader>
                <IonCardContent>
                  {/* Core Color Buttons with Icons */}
                  <IonItemDivider>
                    <IonLabel>Core Colors with Icons</IonLabel>
                  </IonItemDivider>
                  <div style={{ margin: '16px 0', display: 'flex', flexWrap: 'wrap' }}>
                    {coreColors.map((color, i) => (
                      <IonButton 
                        key={`icon-${color}`} 
                        style={buttonStyles[color]}
                        className="m-1"
                      >
                        <IonIcon slot="start" icon={icons[i % icons.length].icon} />
                        {capitalize(color)}
                      </IonButton>
                    ))}
                  </div>
                  
                  {/* Icon Grid - Show a grid of different icons with different colors */}
                  <IonItemDivider>
                    <IonLabel>Icon Grid</IonLabel>
                  </IonItemDivider>
                  <IonGrid>
                    <IonRow>
                      {icons.map((iconObj, index) => {
                        // Cycle through both core and perform colors
                        const allColors = [...coreColors, ...performColors];
                        const color = allColors[index % allColors.length];
                        
                        return (
                          <IonCol size="6" size-md="4" size-lg="3" key={`grid-${iconObj.name}`}>
                            <IonButton 
                              expand="block"
                              style={buttonStyles[color]}
                              className="text-nowrap"
                            >
                              <IonIcon slot="start" icon={iconObj.icon} />
                              {iconObj.name}
                            </IonButton>
                          </IonCol>
                        );
                      })}
                    </IonRow>
                  </IonGrid>
                  
                  {/* Icons with different positions */}
                  <IonItemDivider>
                    <IonLabel>Icon Positions</IonLabel>
                  </IonItemDivider>
                  <div style={{ margin: '16px 0' }}>
                    <IonButton style={buttonStyles.purple} className="m-1">
                      <IonIcon slot="start" icon={add} />
                      Icon Start
                    </IonButton>
                    
                    <IonButton style={buttonStyles.purple} className="m-1">
                      Icon End
                      <IonIcon slot="end" icon={arrowForward} />
                    </IonButton>
                    
                    <IonButton style={buttonStyles.teal} className="m-1">
                      <IonIcon slot="start" icon={checkmark} />
                      Confirm
                    </IonButton>
                    
                    <IonButton style={buttonStyles.crimsonRed} className="m-1">
                      <IonIcon slot="start" icon={trash} />
                      Delete
                    </IonButton>
                  </div>
                </IonCardContent>
              </IonCard>
            </IonCol>
          </IonRow>
          
          {/* Size Variations */}
          <IonRow>
            <IonCol size="12">
              <IonCard>
                <IonCardHeader>
                  <IonCardTitle>Size Variations</IonCardTitle>
                </IonCardHeader>
                <IonCardContent>
                  <div style={{ margin: '16px 0', display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
                    <IonButton style={buttonStyles.purple} size="small" className="m-1">
                      Small
                    </IonButton>
                    
                    <IonButton style={buttonStyles.purple} className="m-1">
                      Default
                    </IonButton>
                    
                    <IonButton style={buttonStyles.purple} size="large" className="m-1">
                      Large
                    </IonButton>
                  </div>
                </IonCardContent>
              </IonCard>
            </IonCol>
          </IonRow>
          
          {/* Grid Layout Example */}
          <IonRow>
            <IonCol size="12">
              <IonCard>
                <IonCardHeader>
                  <IonCardTitle>Grid Layout Example</IonCardTitle>
                </IonCardHeader>
                <IonCardContent>
                  <p>Buttons arranged in a responsive grid (2 columns on small screens, 3 on medium, 4 on large):</p>
                  
                  <IonGrid>
                    <IonRow>
                      <IonCol size="6" size-md="4" size-lg="3">
                        <IonButton expand="block" style={buttonStyles.teal}>
                          <IonIcon slot="start" icon={barbell} />
                          Training
                        </IonButton>
                      </IonCol>
                      <IonCol size="6" size-md="4" size-lg="3">
                        <IonButton expand="block" style={buttonStyles.purple}>
                          <IonIcon slot="start" icon={trophy} />
                          Compete
                        </IonButton>
                      </IonCol>
                      <IonCol size="6" size-md="4" size-lg="3">
                        <IonButton expand="block" style={buttonStyles.gold}>
                          <IonIcon slot="start" icon={star} />
                          Achievements
                        </IonButton>
                      </IonCol>
                      <IonCol size="6" size-md="4" size-lg="3">
                        <IonButton expand="block" style={buttonStyles.electricBlue}>
                          <IonIcon slot="start" icon={analytics} />
                          Analysis
                        </IonButton>
                      </IonCol>
                      <IonCol size="6" size-md="4" size-lg="3">
                        <IonButton expand="block" style={buttonStyles.forestGreen}>
                          <IonIcon slot="start" icon={football} />
                          Performance
                        </IonButton>
                      </IonCol>
                      <IonCol size="6" size-md="4" size-lg="3">
                        <IonButton expand="block" style={buttonStyles.burntOrange}>
                          <IonIcon slot="start" icon={clipboard} />
                          Psychology
                        </IonButton>
                      </IonCol>
                      <IonCol size="6" size-md="4" size-lg="3">
                        <IonButton expand="block" style={buttonStyles.warmCoral}>
                          <IonIcon slot="start" icon={person} />
                          Community
                        </IonButton>
                      </IonCol>
                      <IonCol size="6" size-md="4" size-lg="3">
                        <IonButton expand="block" style={buttonStyles.charcoalGrey}>
                          <IonIcon slot="start" icon={cog} />
                          Settings
                        </IonButton>
                      </IonCol>
                    </IonRow>
                  </IonGrid>
                </IonCardContent>
              </IonCard>
            </IonCol>
          </IonRow>
          
          {/* Full Width Use Cases */}
          <IonRow>
            <IonCol size="12">
              <IonCard>
                <IonCardHeader>
                  <IonCardTitle>Full Width Use Cases</IonCardTitle>
                </IonCardHeader>
                <IonCardContent>
                  <p>Examples of full-width buttons in context:</p>
                  
                  <div style={{ 
                    background: '#111', 
                    padding: '20px', 
                    borderRadius: '8px',
                    marginBottom: '20px'
                  }}>
                    <h3 style={{ color: SHOT_COLORS.white, marginBottom: '20px' }}>Training Session Setup</h3>
                    
                    <div style={{ marginBottom: '20px' }}>
                      <IonButton expand="block" style={buttonStyles.electricBlue}>
                        <IonIcon slot="start" icon={barbell} />
                        Technical Skills
                      </IonButton>
                    </div>
                    
                    <div style={{ marginBottom: '20px' }}>
                      <IonButton expand="block" style={buttonStyles.forestGreen}>
                        <IonIcon slot="start" icon={barbell} />
                        Physical Conditioning
                      </IonButton>
                    </div>
                    
                    <div style={{ marginBottom: '20px' }}>
                      <IonButton expand="block" style={buttonStyles.burntOrange}>
                        <IonIcon slot="start" icon={barbell} />
                        Mental Training
                      </IonButton>
                    </div>
                    
                    <div style={{ marginBottom: '20px' }}>
                      <IonButton expand="block" style={buttonStyles.purple}>
                        <IonIcon slot="start" icon={add} />
                        Create Custom Session
                      </IonButton>
                    </div>
                  </div>
                  
                  <div style={{ 
                    background: '#111', 
                    padding: '20px', 
                    borderRadius: '8px' 
                  }}>
                    <h3 style={{ color: SHOT_COLORS.white, marginBottom: '20px' }}>Post-Match Actions</h3>
                    
                    <div style={{ marginBottom: '20px' }}>
                      <IonButton expand="block" style={buttonStyles.teal}>
                        <IonIcon slot="start" icon={analytics} />
                        Review Performance
                      </IonButton>
                    </div>
                    
                    <div style={{ marginBottom: '20px' }}>
                      <IonButton expand="block" style={buttonStyles.gold}>
                        <IonIcon slot="start" icon={star} />
                        Highlight Achievements
                      </IonButton>
                    </div>
                    
                    <div style={{ marginBottom: '20px' }}>
                      <IonButton expand="block" style={buttonStyles.warmCoral}>
                        <IonIcon slot="start" icon={heart} />
                        Team Feedback
                      </IonButton>
                    </div>
                    
                    <div style={{ marginBottom: '0' }}>
                      <IonButton expand="block" style={buttonStyles.charcoalGrey}>
                        <IonIcon slot="start" icon={calendar} />
                        Schedule Next Session
                      </IonButton>
                    </div>
                  </div>
                </IonCardContent>
              </IonCard>
            </IonCol>
          </IonRow>
        </IonGrid>
      </IonContent>
    </IonPage>
  );
};

export default ButtonShowcasePage;
