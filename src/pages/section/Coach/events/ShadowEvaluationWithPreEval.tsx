import React, { useEffect, useRef } from 'react';

interface Player {
  id: string;
  full_name: string;
  position?: string;
  avatar_url?: string;
  ratings: {
    technical: number;
    physical: number;
    psychological: number;
    social: number;
    positional: number;
  };
  preEvalRatings?: {
    technical?: number;
    physical?: number;
    psychological?: number;
    social?: number;
    positional?: number;
  };
  hasPreEvaluation?: boolean;
}

interface ShadowEvaluationWithPreEvalProps {
  players: Player[];
  eventName: string;
  onRatingChange: (playerId: string, category: string, value: number) => void;
  onSave: () => void;
  onBack: () => void;
  onResetToPreEval?: (playerId: string, category: string) => void;
  isSaving: boolean;
}

const ShadowEvaluationWithPreEval: React.FC<ShadowEvaluationWithPreEvalProps> = ({
  players,
  eventName,
  onRatingChange,
  onSave,
  onBack,
  onResetToPreEval,
  isSaving
}) => {
  const shadowHostRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!shadowHostRef.current) return;

    // Check if shadow root already exists
    let shadowRoot = shadowHostRef.current.shadowRoot;
    
    // If not, create shadow root
    if (!shadowRoot) {
      shadowRoot = shadowHostRef.current.attachShadow({ mode: 'open' });
    } else {
      // Clear existing content
      shadowRoot.innerHTML = '';
    }

    // Create styles
    const style = document.createElement('style');
    style.textContent = `
      * {
        box-sizing: border-box;
      }
      
      .container {
        background-color: #000;
        color: #fff;
        min-height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .content {
        padding: 20px 20px 20px;
      }
      
      .event-card {
        background-color: #1a1a1a;
        border: 1px solid #333;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
      }
      
      .event-name {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #1ABC9C;
      }
      
      .stats {
        display: flex;
        gap: 20px;
        color: #999;
        font-size: 14px;
      }
      
      .legend {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #333;
        display: flex;
        gap: 20px;
        font-size: 13px;
      }
      
      .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      .legend-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
      }
      
      .legend-dot.green {
        background-color: #1ABC9C;
      }
      
      .legend-dot.orange {
        background-color: #FFA500;
      }
      
      .player-list {
        margin-top: 20px;
      }
      
      .player-card {
        background-color: #1a1a1a;
        border: 1px solid #333;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
      }
      
      .player-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
      }
      
      .player-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: #333;
        margin-right: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
      }
      
      .player-info {
        flex: 1;
      }
      
      .player-name {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 5px;
      }
      
      .player-position {
        color: #999;
        font-size: 14px;
      }
      
      .pre-eval-badge {
        background-color: #FFA500;
        color: #000;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
      }
      
      .rating-section {
        margin-top: 15px;
      }
      
      .rating-row {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px;
        background-color: #0a0a0a;
        border-radius: 8px;
        position: relative;
      }
      
      .rating-row.has-pre-eval {
        background-color: rgba(255, 165, 0, 0.1);
        border: 1px solid rgba(255, 165, 0, 0.3);
      }
      
      .rating-label {
        flex: 1;
        font-size: 14px;
        text-transform: capitalize;
      }
      
      .rating-value {
        width: 40px;
        text-align: center;
        font-weight: bold;
      }
      
      .rating-value.coach-eval {
        color: #1ABC9C;
      }
      
      .rating-value.pre-eval {
        color: #FFA500;
      }
      
      .pre-eval-indicator {
        font-size: 11px;
        color: #FFA500;
        margin-left: 8px;
        white-space: nowrap;
      }
      
      .rating-slider {
        width: 200px;
        margin: 0 15px;
      }
      
      input[type="range"] {
        -webkit-appearance: none;
        width: 100%;
        height: 8px;
        border-radius: 4px;
        background: #333;
        outline: none;
      }
      
      input[type="range"]::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #1ABC9C;
        cursor: pointer;
      }
      
      input[type="range"]::-moz-range-thumb {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #1ABC9C;
        cursor: pointer;
      }
      
      .reset-btn {
        padding: 4px 8px;
        background: transparent;
        color: #FFA500;
        border: 1px solid #FFA500;
        border-radius: 4px;
        cursor: pointer;
        font-size: 11px;
        margin-left: 10px;
      }
      
      .reset-btn:hover {
        background-color: #FFA500;
        color: #000;
      }
      
      .save-section {
        position: fixed;
        bottom: 60px; /* Account for bottom navigation bar */
        left: 0;
        right: 0;
        background-color: #1a1a1a;
        padding: 20px;
        border-top: 1px solid #333;
        z-index: 999; /* Below nav but above content */
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
      }
      
      .save-btn {
        width: 100%;
        padding: 15px;
        background-color: #1ABC9C;
        color: #000;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        text-transform: uppercase;
      }
      
      .save-btn:hover {
        background-color: #16A085;
      }
      
      .save-btn:disabled {
        background-color: #666;
        cursor: not-allowed;
      }
      
      .no-players {
        text-align: center;
        padding: 40px;
        background-color: #1a1a1a;
        border-radius: 12px;
        color: #999;
      }
    `;

    // Create content
    const container = document.createElement('div');
    container.className = 'container';

    // Build HTML
    let playersHtml = '';
    if (players.length === 0) {
      playersHtml = `
        <div class="no-players">
          <p>No players marked as attended for this event.</p>
          <p>Please update attendance first.</p>
        </div>
      `;
    } else {
      // Debug logging
      console.log('ShadowEvaluationWithPreEval - Players data:', players);
      players.forEach(player => {
        console.log(`Player ${player.full_name}:`, {
          hasPreEvaluation: player.hasPreEvaluation,
          preEvalRatings: player.preEvalRatings,
          ratings: player.ratings
        });
      });
      
      playersHtml = players.map(player => {
        const hasPreEval = player.hasPreEvaluation && player.preEvalRatings && Object.keys(player.preEvalRatings).length > 0;
        
        return `
          <div class="player-card">
            <div class="player-header">
              <div class="player-avatar">
                ${player.avatar_url ? `<img src="${player.avatar_url}" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">` : '👤'}
              </div>
              <div class="player-info">
                <div class="player-name">
                  ${player.full_name}
                  ${hasPreEval ? '<span class="pre-eval-badge">Pre-eval</span>' : ''}
                </div>
                <div class="player-position">${player.position || 'No position'}</div>
              </div>
            </div>
            <div class="rating-section">
              ${['technical', 'physical', 'psychological', 'social', 'positional'].map(category => {
                const currentRating = player.ratings[category as keyof typeof player.ratings];
                const preEvalRating = player.preEvalRatings?.[category as keyof typeof player.preEvalRatings];
                const hasPreEvalForCategory = preEvalRating !== undefined && preEvalRating > 0;
                const isUsingPreEval = hasPreEvalForCategory && currentRating === preEvalRating;
                
                // Create pre-eval dots display
                const preEvalDotsHtml = hasPreEvalForCategory ? `
                  <div class="pre-eval-dots" style="display: flex; gap: 3px; margin-left: 10px; align-items: center;">
                    ${[1, 2, 3, 4, 5].map(i => `
                      <div style="
                        width: 10px; 
                        height: 10px; 
                        border-radius: 50%; 
                        background-color: ${i <= preEvalRating ? '#FFA500' : 'transparent'};
                        border: 2px solid ${i <= preEvalRating ? '#FFA500' : '#555'};
                        transition: all 0.2s;
                      "></div>
                    `).join('')}
                  </div>
                ` : '';
                
                return `
                  <div class="rating-row ${hasPreEvalForCategory ? 'has-pre-eval' : ''}">
                    <span class="rating-label">${category}</span>
                    ${hasPreEvalForCategory ? `<span class="pre-eval-indicator" style="font-size: 10px; color: #FFA500;">Pre: ${preEvalRating}</span>` : ''}
                    ${preEvalDotsHtml}
                    <input 
                      type="range" 
                      class="rating-slider" 
                      min="0" 
                      max="5" 
                      value="${currentRating}"
                      data-player-id="${player.id}"
                      data-category="${category}"
                    >
                    <span class="rating-value ${isUsingPreEval ? 'pre-eval' : 'coach-eval'}">${currentRating}/5</span>
                    ${hasPreEvalForCategory && onResetToPreEval ? `
                      <button class="reset-btn" 
                        data-player-id="${player.id}"
                        data-category="${category}">
                        Reset
                      </button>
                    ` : ''}
                  </div>
                `;
              }).join('')}
            </div>
          </div>
        `;
      }).join('');
    }

    // Count stats
    const playersWithPreEval = players.filter(p => p.hasPreEvaluation).length;
    const evaluatedPlayers = players.filter(p => Object.values(p.ratings).some(r => r > 0)).length;

    container.innerHTML = `
      <div class="content">
        <div class="event-card">
          <div class="legend">
            <div class="legend-item">
              <div class="legend-dot green"></div>
              <span>Coach evaluation</span>
            </div>
            <div class="legend-item">
              <div class="legend-dot orange"></div>
              <span>Player pre-evaluation</span>
            </div>
          </div>
        </div>
        
        <div class="player-list">
          ${playersHtml}
        </div>
        
        <div style="height: 180px;"></div> <!-- Extra space for save button + bottom nav -->
      </div>
      
      <div class="save-section">
        <button class="save-btn" id="saveBtn" ${isSaving ? 'disabled' : ''}>
          ${isSaving ? 'Saving...' : 'Save All Evaluations'}
        </button>
      </div>
    `;

    // Append to shadow root
    shadowRoot.appendChild(style);
    shadowRoot.appendChild(container);

    // Event listeners removed for back button since it's now in PageWithNavigation

    const saveBtn = shadowRoot.getElementById('saveBtn');
    if (saveBtn) {
      saveBtn.addEventListener('click', onSave);
    }

    // Add listeners for all sliders
    const sliders = shadowRoot.querySelectorAll('input[type="range"]');
    sliders.forEach(slider => {
      slider.addEventListener('input', (e) => {
        const target = e.target as HTMLInputElement;
        const playerId = target.getAttribute('data-player-id');
        const category = target.getAttribute('data-category');
        const value = parseInt(target.value);
        
        // Update the display value
        const valueSpan = target.nextElementSibling as HTMLElement;
        if (valueSpan) {
          valueSpan.textContent = `${value}/5`;
          // Update color based on whether it matches pre-eval
          const player = players.find(p => p.id === playerId);
          const preEvalRating = player?.preEvalRatings?.[category as keyof typeof player.preEvalRatings];
          if (preEvalRating !== undefined && value === preEvalRating) {
            valueSpan.className = 'rating-value pre-eval';
          } else {
            valueSpan.className = 'rating-value coach-eval';
          }
        }
        
        if (playerId && category) {
          onRatingChange(playerId, category, value);
        }
      });
    });

    // Add listeners for reset buttons
    if (onResetToPreEval) {
      const resetBtns = shadowRoot.querySelectorAll('.reset-btn');
      resetBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
          const target = e.target as HTMLElement;
          const playerId = target.getAttribute('data-player-id');
          const category = target.getAttribute('data-category');
          
          if (playerId && category) {
            onResetToPreEval(playerId, category);
          }
        });
      });
    }

    // Cleanup
    return () => {
      if (shadowHostRef.current && shadowHostRef.current.shadowRoot) {
        shadowHostRef.current.shadowRoot.innerHTML = '';
      }
    };
  }, [players, eventName, onRatingChange, onSave, onBack, onResetToPreEval, isSaving]);

  return <div ref={shadowHostRef} style={{ minHeight: '100vh' }} />;
};

export default ShadowEvaluationWithPreEval;