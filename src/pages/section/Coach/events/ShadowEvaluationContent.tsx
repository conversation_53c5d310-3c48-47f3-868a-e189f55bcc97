import React, { useEffect, useRef } from 'react';

interface Player {
  id: string;
  full_name: string;
  position?: string;
  avatar_url?: string;
  ratings: {
    technical: number;
    physical: number;
    psychological: number;
    social: number;
    positional: number;
  };
}

interface ShadowEvaluationContentProps {
  players: Player[];
  eventName: string;
  onRatingChange: (playerId: string, category: string, value: number) => void;
  onSave: () => void;
  onBack: () => void;
  isSaving: boolean;
}

const ShadowEvaluationContent: React.FC<ShadowEvaluationContentProps> = ({
  players,
  eventName,
  onRatingChange,
  onSave,
  onBack,
  isSaving
}) => {
  const shadowHostRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!shadowHostRef.current) return;

    // Check if shadow root already exists
    let shadowRoot = shadowHostRef.current.shadowRoot;
    
    // If not, create shadow root
    if (!shadowRoot) {
      shadowRoot = shadowHostRef.current.attachShadow({ mode: 'open' });
    } else {
      // Clear existing content
      shadowRoot.innerHTML = '';
    }

    // Create styles
    const style = document.createElement('style');
    style.textContent = `
      * {
        box-sizing: border-box;
      }
      
      .container {
        background-color: #000;
        color: #fff;
        min-height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 60px;
        background-color: #1a1a1a;
        display: flex;
        align-items: center;
        padding: 0 20px;
        border-bottom: 1px solid #333;
        z-index: 1000;
      }
      
      .back-btn {
        padding: 8px 16px;
        background: transparent;
        color: #1ABC9C;
        border: 1px solid #1ABC9C;
        border-radius: 5px;
        cursor: pointer;
        margin-right: 20px;
        font-size: 14px;
      }
      
      .back-btn:hover {
        background-color: #1ABC9C;
        color: #000;
      }
      
      .header-title {
        flex: 1;
        margin: 0;
        font-size: 18px;
        font-weight: 600;
      }
      
      .content {
        padding: 80px 20px 20px;
      }
      
      .event-card {
        background-color: #1a1a1a;
        border: 1px solid #333;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
      }
      
      .event-name {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #1ABC9C;
      }
      
      .stats {
        display: flex;
        gap: 20px;
        color: #999;
        font-size: 14px;
      }
      
      .player-list {
        margin-top: 20px;
      }
      
      .player-card {
        background-color: #1a1a1a;
        border: 1px solid #333;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
      }
      
      .player-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
      }
      
      .player-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: #333;
        margin-right: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
      }
      
      .player-info {
        flex: 1;
      }
      
      .player-name {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 5px;
      }
      
      .player-position {
        color: #999;
        font-size: 14px;
      }
      
      .rating-section {
        margin-top: 15px;
      }
      
      .rating-row {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px;
        background-color: #0a0a0a;
        border-radius: 8px;
      }
      
      .rating-label {
        flex: 1;
        font-size: 14px;
        text-transform: capitalize;
      }
      
      .rating-value {
        width: 40px;
        text-align: center;
        font-weight: bold;
        color: #1ABC9C;
      }
      
      .rating-slider {
        width: 200px;
        margin: 0 15px;
      }
      
      input[type="range"] {
        -webkit-appearance: none;
        width: 100%;
        height: 8px;
        border-radius: 4px;
        background: #333;
        outline: none;
      }
      
      input[type="range"]::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #1ABC9C;
        cursor: pointer;
      }
      
      input[type="range"]::-moz-range-thumb {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #1ABC9C;
        cursor: pointer;
      }
      
      .save-section {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: #1a1a1a;
        padding: 20px;
        border-top: 1px solid #333;
      }
      
      .save-btn {
        width: 100%;
        padding: 15px;
        background-color: #1ABC9C;
        color: #000;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        text-transform: uppercase;
      }
      
      .save-btn:hover {
        background-color: #16A085;
      }
      
      .save-btn:disabled {
        background-color: #666;
        cursor: not-allowed;
      }
      
      .no-players {
        text-align: center;
        padding: 40px;
        background-color: #1a1a1a;
        border-radius: 12px;
        color: #999;
      }
    `;

    // Create content
    const container = document.createElement('div');
    container.className = 'container';

    // Build HTML
    let playersHtml = '';
    if (players.length === 0) {
      playersHtml = `
        <div class="no-players">
          <p>No players marked as attended for this event.</p>
          <p>Please update attendance first.</p>
        </div>
      `;
    } else {
      playersHtml = players.map(player => `
        <div class="player-card">
          <div class="player-header">
            <div class="player-avatar">
              ${player.avatar_url ? `<img src="${player.avatar_url}" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">` : '👤'}
            </div>
            <div class="player-info">
              <div class="player-name">${player.full_name}</div>
              <div class="player-position">${player.position || 'No position'}</div>
            </div>
          </div>
          <div class="rating-section">
            ${['technical', 'physical', 'psychological', 'social', 'positional'].map(category => `
              <div class="rating-row">
                <span class="rating-label">${category}</span>
                <input 
                  type="range" 
                  class="rating-slider" 
                  min="0" 
                  max="5" 
                  value="${player.ratings[category as keyof typeof player.ratings]}"
                  data-player-id="${player.id}"
                  data-category="${category}"
                >
                <span class="rating-value">${player.ratings[category as keyof typeof player.ratings]}/5</span>
              </div>
            `).join('')}
          </div>
        </div>
      `).join('');
    }

    container.innerHTML = `
      <div class="header">
        <button class="back-btn" id="backBtn">← Back</button>
        <h1 class="header-title">Team Event Evaluation</h1>
      </div>
      
      <div class="content">
        <div class="event-card">
          <div class="event-name">${eventName}</div>
          <div class="stats">
            <span>Players: ${players.length}</span>
            <span>Evaluated: ${players.filter(p => Object.values(p.ratings).some(r => r > 0)).length}</span>
          </div>
        </div>
        
        <div class="player-list">
          ${playersHtml}
        </div>
        
        <div style="height: 100px;"></div>
      </div>
      
      <div class="save-section">
        <button class="save-btn" id="saveBtn" ${isSaving ? 'disabled' : ''}>
          ${isSaving ? 'Saving...' : 'Save All Evaluations'}
        </button>
      </div>
    `;

    // Append to shadow root
    shadowRoot.appendChild(style);
    shadowRoot.appendChild(container);

    // Add event listeners
    const backBtn = shadowRoot.getElementById('backBtn');
    if (backBtn) {
      backBtn.addEventListener('click', onBack);
    }

    const saveBtn = shadowRoot.getElementById('saveBtn');
    if (saveBtn) {
      saveBtn.addEventListener('click', onSave);
    }

    // Add listeners for all sliders
    const sliders = shadowRoot.querySelectorAll('input[type="range"]');
    sliders.forEach(slider => {
      slider.addEventListener('input', (e) => {
        const target = e.target as HTMLInputElement;
        const playerId = target.getAttribute('data-player-id');
        const category = target.getAttribute('data-category');
        const value = parseInt(target.value);
        
        // Update the display value
        const valueSpan = target.nextElementSibling as HTMLElement;
        if (valueSpan) {
          valueSpan.textContent = `${value}/5`;
        }
        
        if (playerId && category) {
          onRatingChange(playerId, category, value);
        }
      });
    });

    // Cleanup
    return () => {
      if (shadowHostRef.current && shadowHostRef.current.shadowRoot) {
        shadowHostRef.current.shadowRoot.innerHTML = '';
      }
    };
  }, [players, eventName, onRatingChange, onSave, onBack, isSaving]);

  return <div ref={shadowHostRef} style={{ minHeight: '100vh' }} />;
};

export default ShadowEvaluationContent;