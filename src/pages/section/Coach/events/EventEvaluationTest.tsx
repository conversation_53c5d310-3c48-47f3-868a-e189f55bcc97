import React, { useEffect, useRef } from 'react';
import { useParams, useHistory } from 'react-router-dom';

interface EventEvaluationParams {
  clubId: string;
  teamId: string;
  eventId: string;
}

const EventEvaluationTest: React.FC = () => {
  const { clubId, teamId, eventId } = useParams<EventEvaluationParams>();
  const history = useHistory();
  const shadowHostRef = useRef<HTMLDivElement>(null);
  
  console.log('🎯 EventEvaluationTest mounted with:', { clubId, teamId, eventId });
  
  useEffect(() => {
    if (!shadowHostRef.current) return;
    
    // Create shadow root
    const shadowRoot = shadowHostRef.current.attachShadow({ mode: 'open' });
    
    // Create styles
    const style = document.createElement('style');
    style.textContent = `
      .container {
        background-color: #000;
        color: #fff;
        min-height: 100vh;
        padding: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      h1 {
        color: #fff;
        font-size: 24px;
        margin-bottom: 20px;
      }
      
      p {
        color: #fff;
        margin-bottom: 10px;
      }
      
      .go-back-btn {
        margin-top: 20px;
        padding: 10px 20px;
        background-color: #1ABC9C;
        color: #fff;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 16px;
        font-weight: bold;
      }
      
      .go-back-btn:hover {
        background-color: #16A085;
      }
      
      .info-box {
        margin-top: 20px;
        padding: 20px;
        background-color: #333;
        border: 2px solid #666;
        border-radius: 8px;
      }
      
      .success-text {
        color: #00ff00;
      }
      
      .red-box {
        margin-top: 20px;
        padding: 20px;
        background-color: #ff0000;
        color: #fff;
        border-radius: 8px;
      }
      
      .player-card {
        margin-top: 20px;
        padding: 20px;
        background-color: #1a1a1a;
        border: 1px solid #444;
        border-radius: 8px;
      }
    `;
    
    // Create content
    const container = document.createElement('div');
    container.className = 'container';
    container.innerHTML = `
      <h1>Event Evaluation (Shadow DOM)</h1>
      <p>Club ID: ${clubId}</p>
      <p>Team ID: ${teamId}</p>
      <p>Event ID: ${eventId}</p>
      
      <button class="go-back-btn" id="goBackBtn">Go Back (Green Button)</button>
      
      <div class="info-box">
        <p>This is using Shadow DOM for isolated styling</p>
        <p class="success-text">✅ Routing is working correctly!</p>
      </div>
      
      <div class="red-box">
        <p>🔴 This should be a RED box with white text</p>
      </div>
      
      <div class="player-card">
        <h2 style="color: #1ABC9C; margin-bottom: 10px;">Mock Player Evaluation</h2>
        <p>Player: John Doe</p>
        <p>Position: Forward</p>
        <div style="margin-top: 10px;">
          <label style="display: block; margin-bottom: 5px;">Technical (1-5):</label>
          <input type="range" min="1" max="5" value="3" style="width: 100%;">
        </div>
      </div>
    `;
    
    // Append to shadow root
    shadowRoot.appendChild(style);
    shadowRoot.appendChild(container);
    
    // Add event listener for go back button
    const goBackBtn = shadowRoot.getElementById('goBackBtn');
    if (goBackBtn) {
      goBackBtn.addEventListener('click', () => {
        history.goBack();
      });
    }
    
    // Cleanup
    return () => {
      if (shadowHostRef.current && shadowHostRef.current.shadowRoot) {
        shadowHostRef.current.shadowRoot.innerHTML = '';
      }
    };
  }, [clubId, teamId, eventId, history]);
  
  return <div ref={shadowHostRef} style={{ minHeight: '100vh' }} />;
};

export default EventEvaluationTest;