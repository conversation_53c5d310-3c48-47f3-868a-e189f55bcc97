// ABOUTME: Design system showcase for the AttendanceTracker configuration of ShadowPlayersList
// Shows how to use ShadowPlayersList for event attendance management

import React, { useState } from 'react';
import { SectionWrapper, SectionTitle, ExampleGrid } from '../components/SectionComponents';
import { useSectionMetadata } from '../utils/sectionHelpers';
import { ShadowPlayersList, PlayerData } from '@/foundation/design-system/components/molecules/Lists';

// Sample player data for demonstration with attendance-specific fields
const samplePlayers: PlayerData[] = [
  {
    id: '1',
    name: '<PERSON>',
    position: 'Striker',
    secondLine: 'Striker',
    avatarUrl: '/avatar/SHOT avatar1.png',
    status: 'ACTIVE',
    evaluated: true // Has been evaluated
  },
  {
    id: '2',
    name: '<PERSON>',
    position: 'Midfielder',
    secondLine: 'Midfielder',
    avatarUrl: '/avatar/SHOT avatar2.png',
    status: 'ACTIVE',
    evaluated: false
  },
  {
    id: '3',
    name: '<PERSON>',
    position: 'Defender',
    secondLine: 'Defender',
    avatarUrl: '/avatar/SHOT avatar3.png',
    status: 'ACTIVE',
    evaluated: false
  },
  {
    id: '4',
    name: '<PERSON>',
    position: 'Goalkeeper',
    secondLine: 'Goalkeeper',
    avatarUrl: '/avatar/SHOT avatar4.png',
    status: 'ACTIVE',
    evaluated: true
  },
  {
    id: '5',
    name: 'Sophie Anderson',
    position: 'Midfielder',
    secondLine: 'Midfielder',
    avatarUrl: '/avatar/SHOT avatar5.png',
    status: 'INACTIVE',
    evaluated: false
  },
  {
    id: '6',
    name: 'Liam O\'Connor',
    position: 'Defender',
    secondLine: 'Defender',
    avatarUrl: '/avatar/SHOT avatar6.png',
    status: 'ACTIVE',
    evaluated: false
  }
];

export const AttendanceTrackerSection: React.FC = () => {
  const metadata = useSectionMetadata('attendance-tracker');
  
  // State for attendance tracking
  const [attendanceList, setAttendanceList] = useState<string[]>(
    // Default: all active players are marked as attending
    samplePlayers.filter(p => p.status === 'ACTIVE').map(p => p.id)
  );
  
  // State for evaluation mode
  const [isEvaluationMode, setIsEvaluationMode] = useState(false);

  const handleAttendanceChange = (selectedIds: string[]) => {
    setAttendanceList(selectedIds);
  };

  const handlePlayerClick = (playerId: string) => {
    const player = samplePlayers.find(p => p.id === playerId);
    if (player && isEvaluationMode) {
      // Only allow clicking on attended players in evaluation mode
      if (attendanceList.includes(playerId)) {
        console.log('Opening evaluation for:', player.name);
        alert(`Opening evaluation for ${player.name}`);
      }
    }
  };

  return (
    <SectionWrapper id="attendance-tracker">
      <SectionTitle
        title="Attendance Tracker"
        description="Event attendance tracking using ShadowPlayersList with multi-select configuration"
        {...metadata}
      />

      <div className="bg-blue-500/10 border border-blue-500/50 rounded-lg p-4 mb-6">
        <p className="text-blue-300 text-sm font-semibold mb-2">📋 Component Features:</p>
        <ul className="text-blue-200 text-xs space-y-1 list-disc list-inside">
          <li>Uses ShadowPlayersList with multi-select enabled</li>
          <li>Default state: All active players pre-selected as attending</li>
          <li>Coaches deselect players who didn't attend</li>
          <li>Two modes: Attendance tracking and Evaluation mode</li>
          <li>Consistent with coach selection and active player patterns</li>
          <li>Shows attendance statistics</li>
        </ul>
      </div>

      <ExampleGrid title="Attendance Mode - Default State">
        <div className="p-6 bg-gray-900 rounded-lg">
          <div className="mb-4">
            <div style={{ 
              backgroundColor: 'rgba(6, 78, 59, 0.4)', 
              padding: '15px', 
              borderRadius: '8px', 
              marginBottom: '15px',
              display: 'flex',
              alignItems: 'center',
              border: '1px solid rgba(16, 185, 129, 0.3)'
            }}>
              <div style={{ marginRight: '12px', fontSize: '24px' }}>✅</div>
              <div>
                <h3 style={{ margin: '0', color: 'white', fontSize: '18px' }}>
                  Attendance Tracking
                </h3>
                <p style={{ margin: '5px 0 0 0', color: '#aaaaaa', fontSize: '14px' }}>
                  Remove players from the list who did not attend before continuing
                </p>
              </div>
            </div>
            
            <div style={{ color: '#F7B613', fontSize: '14px', marginBottom: '12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span>ℹ️</span>
              <span>All invited players are marked as attending by default</span>
            </div>
          </div>
          
          <ShadowPlayersList
            players={samplePlayers}
            variant="coach"
            searchable={true}
            selectable={true}
            multiSelect={true}
            selectedPlayers={attendanceList}
            onSelectionChange={handleAttendanceChange}
            borderColor="green"
            title="Event Attendance"
          />
          
          <div style={{
            padding: '12px',
            marginTop: '8px',
            background: 'rgba(26, 188, 156, 0.1)',
            borderRadius: '6px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}>
            <span style={{ color: '#10b981', fontSize: '14px', fontWeight: '500' }}>
              {attendanceList.length} attended of {samplePlayers.length} invited players
            </span>
          </div>
          
          <button 
            onClick={() => {
              console.log('Saving attendance:', attendanceList);
              alert(`Saved attendance: ${attendanceList.length} players attended`);
            }}
            style={{
              width: '100%',
              marginTop: '16px',
              padding: '12px 20px',
              background: '#F7B613',
              color: 'black',
              border: 'none',
              borderRadius: '8px',
              fontSize: '14px',
              fontWeight: '600',
              cursor: 'pointer'
            }}
          >
            Save Attendance
          </button>
        </div>
      </ExampleGrid>

      <ExampleGrid title="Evaluation Mode">
        <div className="p-6 bg-gray-900 rounded-lg">
          <div className="mb-4">
            <div style={{ 
              backgroundColor: 'rgba(26, 54, 93, 0.4)', 
              padding: '15px', 
              borderRadius: '8px', 
              marginBottom: '15px',
              display: 'flex',
              alignItems: 'center',
              border: '1px solid rgba(59, 130, 246, 0.3)'
            }}>
              <div style={{ marginRight: '12px', fontSize: '24px' }}>📊</div>
              <div>
                <h3 style={{ margin: '0', color: 'white', fontSize: '18px' }}>
                  Player Evaluation
                </h3>
                <p style={{ margin: '5px 0 0 0', color: '#aaaaaa', fontSize: '14px' }}>
                  Select a player who attended to evaluate their performance
                </p>
              </div>
            </div>
          </div>
          
          <ShadowPlayersList
            players={samplePlayers.map(p => ({
              ...p,
              // Only enable players who attended
              status: attendanceList.includes(p.id) ? p.status : 'INACTIVE',
              statusText: !attendanceList.includes(p.id) ? 'Not Attended' : undefined
            }))}
            variant="coach"
            searchable={true}
            selectable={false}
            onPlayerClick={(playerId) => handlePlayerClick(playerId)}
            borderColor="teal"
            title="Select Player to Evaluate"
          />
          
          <div style={{ marginTop: '16px', display: 'flex', gap: '8px', flexDirection: 'column' }}>
            <button 
              onClick={() => {
                console.log('Starting batch evaluation');
                alert('Opening batch evaluation interface...');
              }}
              style={{
                width: '100%',
                padding: '12px 20px',
                background: '#F7B613',
                color: 'black',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px'
              }}
            >
              📊 Evaluate All Players
            </button>
            
            <button 
              onClick={() => {
                setIsEvaluationMode(false);
                console.log('Switching back to attendance mode');
              }}
              style={{
                width: '100%',
                padding: '12px 20px',
                background: '#6B00DB',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '14px',
                fontWeight: '600',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px'
              }}
            >
              ✅ Edit Attendance
            </button>
          </div>
        </div>
      </ExampleGrid>

      <div className="mt-8 p-4 bg-gray-800 rounded-lg">
        <h3 className="text-white font-semibold mb-3">Usage Example:</h3>
        <pre className="text-sm text-gray-300 overflow-x-auto">
{`import { ShadowPlayersList } from '@/foundation/design-system/components/molecules/Lists';

function EventAttendance() {
  const [attendanceList, setAttendanceList] = useState<string[]>(
    // Default: all active players attending
    players.filter(p => p.status === 'ACTIVE').map(p => p.id)
  );
  
  return (
    <>
      <ShadowPlayersList
        players={players}
        variant="coach"
        searchable={true}
        selectable={true}
        multiSelect={true}
        selectedPlayers={attendanceList}
        onSelectionChange={setAttendanceList}
        borderColor="green"
        title="Event Attendance"
      />
      
      <div className="stats-bar">
        {attendanceList.length} attended of {players.length} invited
      </div>
    </>
  );
}`}
        </pre>
      </div>

      <div className="mt-6 p-4 bg-yellow-900/20 border border-yellow-600/50 rounded-lg">
        <h4 className="text-yellow-400 font-semibold mb-2">⚠️ Implementation Notes:</h4>
        <ul className="text-sm text-yellow-200 space-y-1 list-disc list-inside">
          <li>This uses the same ShadowPlayersList component as coach and player selection</li>
          <li>Default behavior: All active invited players are pre-selected</li>
          <li>Coaches deselect players who didn't attend (opposite of typical selection)</li>
          <li>In evaluation mode, use the status prop to disable non-attended players</li>
          <li>The component already handles all the styling and interactions</li>
        </ul>
      </div>
    </SectionWrapper>
  );
};