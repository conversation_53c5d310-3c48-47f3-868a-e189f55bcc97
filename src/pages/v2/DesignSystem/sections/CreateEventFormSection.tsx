// ABOUTME: Design system showcase for the redesigned create event form with improved visibility
// This component demonstrates the improved form layout and styling for the create event page

import React, { useState } from 'react';
import { IonIcon } from '@ionic/react';
import { calendarOutline, timeOutline, locationOutline, peopleOutline, clipboard, checkmarkCircle } from 'ionicons/icons';
import { format } from 'date-fns';
import { ShadowTextInput, ShadowSelect, ShadowCheckbox } from '@/foundation/design-system/components/atoms/Input';
import { SectionWrapper, SectionTitle, ExampleGrid, SectionSpacer } from '../components/SectionComponents';
import { useSectionMetadata } from '../utils/sectionHelpers';
import { EventSelector, MatchTitleSelector } from '../../../../pages/section/Coach/supporting/EventManagement/components/index';
import { PlayerAttendanceSelector } from '../../../../pages/section/Coach/supporting/EventManagement/components';

export const CreateEventFormSection: React.FC = () => {
  const metadata = useSectionMetadata('create-event-form');
  
  // Set default values for today
  const today = new Date();
  const formattedDate = format(today, 'yyyy-MM-dd');
  const nextHour = new Date();
  nextHour.setHours(nextHour.getHours() + 1);
  const defaultTime = format(nextHour, 'HH:mm');
  
  // Form state
  const [title, setTitle] = useState('');
  const [eventType, setEventType] = useState<'training' | 'match' | 'assessment'>('training');
  const [date, setDate] = useState(formattedDate);
  const [duration, setDuration] = useState<'60' | '90' | '120'>('90');
  const [locationName, setLocationName] = useState('');
  const [time, setTime] = useState(defaultTime);
  const [meetDatetime, setMeetDatetime] = useState('');
  const [selectedAttendees, setSelectedAttendees] = useState<string[]>([]);
  const [isPreSessionEvaluation, setIsPreSessionEvaluation] = useState(true);
  const [selectedFramework, setSelectedFramework] = useState<string>('shot-perform-v1');
  
  const durationOptions = [
    { value: '60', label: '60 minutes' },
    { value: '90', label: '90 minutes' },
    { value: '120', label: '120 minutes' }
  ];
  
  const frameworkOptions = [
    { value: 'shot-perform-v1', label: 'SHOT PERFORM Framework v1' },
    { value: 'shot-perform-v2', label: 'SHOT PERFORM Framework v2 (Beta)' }
  ];

  // Inline styles for the redesigned form
  const styles = `
    .create-event-redesign {
      background: #0A0A0A;
      color: #FFFFFF;
      padding: 20px;
      border-radius: 8px;
    }
    
    .form-section {
      background: #1A1A1A;
      border: 1px solid #2A2A2A;
      border-radius: 12px;
      padding: 24px;
      margin-bottom: 20px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    }
    
    .form-section-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid #2A2A2A;
    }
    
    .form-section-title {
      font-size: 18px;
      font-weight: 600;
      color: #FFFFFF;
      margin: 0;
      margin-left: 12px;
    }
    
    .form-section-icon {
      color: #00D9FF;
      font-size: 24px;
    }
    
    .form-intro {
      background: #1A1A1A;
      border: 1px solid #2A2A2A;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 24px;
      color: #B0B0B0;
      font-size: 14px;
      line-height: 1.6;
    }
    
    .form-field-wrapper {
      margin-bottom: 20px;
    }
    
    .help-box {
      background: #0D1117;
      border: 1px solid #30363D;
      border-radius: 8px;
      padding: 16px;
      margin-top: 16px;
      font-size: 13px;
      color: #8B949E;
    }
    
    .help-box-title {
      color: #00D9FF;
      font-weight: 600;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
    }
    
    .help-box ul {
      margin: 8px 0 0 20px;
      padding: 0;
      color: #8B949E;
    }
    
    .help-box li {
      margin-bottom: 4px;
      line-height: 1.5;
    }
    
    .action-buttons {
      display: flex;
      gap: 16px;
      margin-top: 32px;
      padding: 20px;
      background: #0A0A0A;
      border-top: 1px solid #2A2A2A;
      border-radius: 0 0 8px 8px;
      margin-left: -20px;
      margin-right: -20px;
      margin-bottom: -20px;
    }
    
    .btn-cancel {
      flex: 1;
      padding: 14px 24px;
      background: #1A1A1A;
      border: 1px solid #3A3A3A;
      color: #FFFFFF;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
    }
    
    .btn-cancel:hover {
      background: #2A2A2A;
      border-color: #4A4A4A;
    }
    
    .btn-submit {
      flex: 1.5;
      padding: 14px 24px;
      background: #00D9FF;
      border: none;
      color: #000000;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
    }
    
    .btn-submit:hover:not(:disabled) {
      background: #00B8E6;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 217, 255, 0.3);
    }
    
    .btn-submit:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    
    /* Override shadow component backgrounds for better visibility */
    .create-event-redesign shadow-form-group {
      --background-color: #1A1A1A !important;
      --border-color: #2A2A2A !important;
    }
    
    .create-event-redesign shadow-text-input {
      --input-background: #0D1117 !important;
      --input-border: #30363D !important;
      --input-text: #FFFFFF !important;
      --label-color: #B0B0B0 !important;
      --input-focus-border: #00D9FF !important;
    }
    
    .create-event-redesign shadow-select {
      --select-background: #0D1117 !important;
      --select-border: #30363D !important;
      --select-text: #FFFFFF !important;
      --label-color: #B0B0B0 !important;
      --select-focus-border: #00D9FF !important;
    }
    
    .create-event-redesign shadow-checkbox {
      --checkbox-background: #0D1117 !important;
      --checkbox-border: #30363D !important;
      --checkbox-checked: #00D9FF !important;
      --label-color: #FFFFFF !important;
    }
    
    /* Comparison styles */
    .comparison-container {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 40px;
    }
    
    .comparison-box {
      background: #1A1A1A;
      border: 1px solid #2A2A2A;
      border-radius: 8px;
      padding: 20px;
    }
    
    .comparison-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #FFFFFF;
    }
    
    .issue-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    
    .issue-list li {
      display: flex;
      align-items: flex-start;
      margin-bottom: 12px;
      color: #B0B0B0;
      font-size: 14px;
    }
    
    .issue-list li::before {
      content: "❌";
      margin-right: 8px;
      flex-shrink: 0;
    }
    
    .improvement-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    
    .improvement-list li {
      display: flex;
      align-items: flex-start;
      margin-bottom: 12px;
      color: #B0B0B0;
      font-size: 14px;
    }
    
    .improvement-list li::before {
      content: "✅";
      margin-right: 8px;
      flex-shrink: 0;
    }

    @media (max-width: 768px) {
      .comparison-container {
        grid-template-columns: 1fr;
      }
    }
  `;

  return (
    <SectionWrapper id="create-event-form">
      <style>{styles}</style>
      <SectionTitle 
        title="Create Event Form Redesign" 
        description="Improved visibility and contrast for the create event form"
        {...metadata}
      />

      {/* Comparison Section */}
      <ExampleGrid title="Current Issues vs. Improvements">
        <div className="comparison-container">
          <div className="comparison-box">
            <h3 className="comparison-title">🔴 Current Issues</h3>
            <ul className="issue-list">
              <li>Dark gray (#1f2937) inputs on dark background</li>
              <li>Low contrast between form sections</li>
              <li>Insufficient spacing between elements</li>
              <li>Shadow DOM styles not properly applied</li>
              <li>Action buttons blend into background</li>
              <li>Form sections lack clear visual hierarchy</li>
            </ul>
          </div>
          
          <div className="comparison-box">
            <h3 className="comparison-title">🟢 Improvements Made</h3>
            <ul className="improvement-list">
              <li>Darker inputs (#0D1117) with lighter borders</li>
              <li>Clear section separation with borders</li>
              <li>Increased padding and margins</li>
              <li>Proper CSS variable overrides for Shadow DOM</li>
              <li>High contrast action buttons with hover states</li>
              <li>Visual hierarchy with icons and sections</li>
            </ul>
          </div>
        </div>
      </ExampleGrid>

      {/* Redesigned Form */}
      <ExampleGrid title="Redesigned Create Event Form">
        <div className="create-event-redesign">
          <div className="form-intro">
            Create a new event for <strong>U14 Eagles</strong>. Choose the event type, select players, and set the schedule details below.
          </div>
          
          {/* Event Details Section */}
          <div className="form-section">
            <div className="form-section-header">
              <IonIcon icon={calendarOutline} className="form-section-icon" />
              <h2 className="form-section-title">Event Details</h2>
            </div>
            
            <div className="form-field-wrapper">
              <EventSelector
                label="Event Type"
                value={eventType}
                onSelect={(value) => setEventType(value as 'training' | 'match' | 'assessment')}
                required={false}
                events={[
                  { value: 'training', label: 'Training', icon: 'barbell' },
                  { value: 'match', label: 'Match', icon: 'trophy' },
                  { value: 'assessment', label: 'Assessment', icon: 'clipboard' }
                ]}
              />
            </div>
            
            <div className="form-field-wrapper">
              <MatchTitleSelector 
                eventType={eventType} 
                teamName="U14 Eagles"
                value={title}
                onChange={setTitle}
                required
                date={date}
                placeholder={eventType === 'training' ? "Training Session" : "Player Assessment"}
                selectedPlayers={selectedAttendees}
                playerNames={{}}
              />
            </div>
          </div>
          
          {/* Player Selection Section */}
          <div className="form-section">
            <div className="form-section-header">
              <IonIcon icon={peopleOutline} className="form-section-icon" />
              <h2 className="form-section-title">Select Players</h2>
            </div>
            
            <div className="bg-gray-800 p-4 rounded-lg text-gray-400 text-center">
              <p className="text-sm">Player selector would appear here</p>
              <p className="text-xs mt-2">(Component requires live data)</p>
            </div>
          </div>
          
          {/* Schedule Section */}
          <div className="form-section">
            <div className="form-section-header">
              <IonIcon icon={timeOutline} className="form-section-icon" />
              <h2 className="form-section-title">Schedule & Location</h2>
            </div>
            
            <div className="form-field-wrapper">
              <ShadowTextInput
                label="Date"
                type="date"
                name="date"
                value={date}
                onChange={(e) => setDate(e.detail.value)}
                required
              />
            </div>
            
            <div className="form-field-wrapper">
              <ShadowTextInput
                label="Start Time"
                type="time"
                name="time"
                value={time}
                onChange={(e) => setTime(e.detail.value)}
                required
              />
            </div>
            
            <div className="form-field-wrapper">
              <ShadowTextInput
                label="Meet Time (optional)"
                type="time"
                name="meetDatetime"
                value={meetDatetime}
                onChange={(e) => setMeetDatetime(e.detail.value)}
                helpText="Time for players to arrive before the event"
              />
            </div>
            
            <div className="form-field-wrapper">
              <ShadowSelect
                label="Duration"
                value={duration}
                onChange={(value) => setDuration(value as '60' | '90' | '120')}
                options={durationOptions}
              />
            </div>
            
            <div className="form-field-wrapper">
              <ShadowTextInput
                label="Location (optional)"
                name="location"
                value={locationName}
                onChange={(e) => setLocationName(e.detail.value)}
                placeholder="Enter event location"
              />
            </div>
          </div>
          
          {/* Pre-Session Evaluation Section */}
          <div className="form-section">
            <div className="form-section-header">
              <IonIcon icon={clipboard} className="form-section-icon" />
              <h2 className="form-section-title">Pre-Session Evaluation</h2>
            </div>
            
            <ShadowCheckbox
              label="Enable Pre-Session Self-Evaluation"
              helpText="Players will assess themselves before the session using SHOT PERFORM framework"
              checked={isPreSessionEvaluation}
              onChange={(e) => setIsPreSessionEvaluation(e.detail.checked)}
            />
            
            {isPreSessionEvaluation && (
              <>
                <div className="form-field-wrapper" style={{ marginTop: '20px' }}>
                  <ShadowSelect
                    label="Evaluation Framework"
                    value={selectedFramework}
                    onChange={(value) => setSelectedFramework(value)}
                    options={frameworkOptions}
                    helpText="Select which evaluation framework to use"
                  />
                </div>
                
                <div className="help-box">
                  <div className="help-box-title">
                    ℹ️ How Pre-Session Evaluation Works
                  </div>
                  <ul>
                    <li>Players receive self-assessment questions before the session</li>
                    <li>Questions are based on the SHOT PERFORM framework</li>
                    <li>Framework covers Technical, Physical, Psychological, and Social areas</li>
                    <li>Self-assessments help players reflect on their abilities</li>
                    <li>Coaches can compare self-assessments with their evaluations</li>
                  </ul>
                </div>
              </>
            )}
          </div>
          
          {/* Action Buttons */}
          <div className="action-buttons">
            <button className="btn-cancel">
              Cancel
            </button>
            
            <button className="btn-submit">
              <IonIcon icon={checkmarkCircle} />
              Create Event
            </button>
          </div>
        </div>
      </ExampleGrid>

      {/* Implementation Guide */}
      <ExampleGrid title="Implementation Guide">
        <div className="bg-gray-800 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-4">Key Changes to Apply:</h3>
          
          <div className="space-y-4 text-sm">
            <div>
              <h4 className="text-shot-teal font-medium mb-2">1. Background Colors</h4>
              <ul className="list-disc list-inside text-gray-400 space-y-1">
                <li>Page background: #0A0A0A (pure black)</li>
                <li>Form sections: #1A1A1A with #2A2A2A borders</li>
                <li>Input fields: #0D1117 with #30363D borders</li>
                <li>Help boxes: #0D1117 background</li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-shot-teal font-medium mb-2">2. Text Colors</h4>
              <ul className="list-disc list-inside text-gray-400 space-y-1">
                <li>Primary text: #FFFFFF (white)</li>
                <li>Secondary text: #B0B0B0 (light gray)</li>
                <li>Help text: #8B949E (medium gray)</li>
                <li>Accent color: #00D9FF (cyan)</li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-shot-teal font-medium mb-2">3. Spacing & Layout</h4>
              <ul className="list-disc list-inside text-gray-400 space-y-1">
                <li>Section padding: 24px</li>
                <li>Field spacing: 20px between elements</li>
                <li>Section headers with icons for visual hierarchy</li>
                <li>Sticky action buttons at bottom</li>
              </ul>
            </div>
            
            <div>
              <h4 className="text-shot-teal font-medium mb-2">4. Shadow DOM Overrides</h4>
              <pre className="bg-gray-900 p-3 rounded text-xs overflow-x-auto">
{`shadow-text-input {
  --input-background: #0D1117 !important;
  --input-border: #30363D !important;
  --input-text: #FFFFFF !important;
  --label-color: #B0B0B0 !important;
}`}</pre>
            </div>
          </div>
        </div>
      </ExampleGrid>

      <SectionSpacer />
    </SectionWrapper>
  );
};