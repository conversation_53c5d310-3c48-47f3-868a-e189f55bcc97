import { useState } from 'react';
import {
  IonButton,
  IonContent,
  IonHeader,
  IonInput,
  IonItem,
  IonLabel,
  IonList,
  IonPage,
  IonTitle,
  IonToolbar,
  useIonToast,
  useIonLoading,
} from '@ionic/react';
import { supabase } from '@/lib/supabase';
import Splash from '../components/Splash';

function SplashPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const [showLoading, hideLoading] = useIonLoading();
  const [showToast ] = useIonToast();
  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    console.log()
    e.preventDefault();
    await showLoading();
    try {
      const x = await supabase.auth.signUp({ email, password });
      console.log(x);
      
      await showToast({ message: 'Check your email for the login link!' });
    } catch (e: any) {
      await showToast({ message: e.error_description || e.message , duration: 5000});
    } finally {
      await hideLoading();
    }
  };
  return (
    <IonPage>
      
      
      <IonContent>
       
       
      <Splash />


      
      </IonContent>



    </IonPage>
  );
}

// Add default export
export default SplashPage;
