import React, { useState, useEffect, useMemo } from 'react';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButton,
  IonIcon,
  IonRange,
  IonTextarea,
  IonProgressBar,
  IonBadge,
  IonSpinner,
  IonAlert
} from '@ionic/react';
import {
  closeOutline,
  chevronBackOutline,
  chevronForwardOutline,
  checkmarkCircleOutline,
  saveOutline,
  personOutline
} from 'ionicons/icons';
import { simpleEnhancedPlayerEvaluationService } from '../../services/SimpleEnhancedPlayerEvaluationService';

interface PlayerWithEvaluation {
  id: string;
  full_name: string;
  position?: string;
  avatar_url?: string;
  ratings: {
    technical: number;
    physical: number;
    psychological: number;
    social: number;
    positional: number;
  };
  evaluationCriteria?: { [category: string]: any[] };
  syncStatus: 'synced' | 'pending' | 'error';
  hasExistingEvaluation: boolean;
}

interface QuickEvaluationQuestion {
  id: string;
  playerId: string;
  playerName: string;
  category: string;
  area: string;
  question: string;
  rating: number;
  notes: string;
}

interface QuickEvaluationModalProps {
  isOpen: boolean;
  onClose: () => void;
  players: PlayerWithEvaluation[];
  eventId: string;
  teamId: string;
  startingPlayerId?: string;
  onEvaluationSaved: (playerId: string, ratings: any) => void;
}

const QuickEvaluationModal: React.FC<QuickEvaluationModalProps> = ({
  isOpen,
  onClose,
  players,
  eventId,
  teamId,
  startingPlayerId,
  onEvaluationSaved
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [questions, setQuestions] = useState<QuickEvaluationQuestion[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [showConfirmClose, setShowConfirmClose] = useState(false);
  const [completedPlayers, setCompletedPlayers] = useState<Set<string>>(new Set());

  // Generate all questions for all players who need evaluation
  const generateQuestions = useMemo(() => {
    const questionsList: QuickEvaluationQuestion[] = [];
    
    // Filter players who need evaluation (not fully evaluated or starting player)
    const playersNeedingEval = players.filter(player => {
      const hasAllRatings = Object.values(player.ratings).every(rating => rating > 0);
      return !hasAllRatings || player.id === startingPlayerId;
    });

    // Generate questions for each player
    playersNeedingEval.forEach(player => {
      const categories = ['technical', 'physical', 'psychological', 'social'];
      
      // Add positional category if player has a position set
      if (player.position && player.position !== 'Position not set') {
        categories.push('positional');
      }

      categories.forEach(category => {
        const categoryUpper = category.toUpperCase();
        const criteria = player.evaluationCriteria?.[categoryUpper] || [];
        const criterion = criteria.length > 0 ? criteria[0] : null;
        
        // Skip if player already has a rating for this category and we're not starting with them
        if (player.id !== startingPlayerId && player.ratings[category as keyof typeof player.ratings] > 0) {
          return;
        }

        questionsList.push({
          id: `${player.id}_${category}`,
          playerId: player.id,
          playerName: player.full_name,
          category: categoryUpper,
          area: criterion?.area || `${category} evaluation`,
          question: criterion?.question || `How would you rate ${player.full_name}'s ${category} performance?`,
          rating: player.ratings[category as keyof typeof player.ratings] || 0,
          notes: ''
        });
      });
    });

    return questionsList;
  }, [players, startingPlayerId]);

  // Initialize questions when modal opens
  useEffect(() => {
    if (isOpen && generateQuestions.length > 0) {
      setQuestions(generateQuestions);
      setCurrentQuestionIndex(0);
      setCompletedPlayers(new Set());
    }
  }, [isOpen, generateQuestions]);

  // Current question
  const currentQuestion = questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === questions.length - 1;
  const isFirstQuestion = currentQuestionIndex === 0;
  
  // Progress calculations
  const totalQuestions = questions.length;
  const progressPercentage = totalQuestions > 0 ? ((currentQuestionIndex + 1) / totalQuestions) * 100 : 0;
  
  // Current player info
  const currentPlayer = players.find(p => p.id === currentQuestion?.playerId);
  
  // Get category icon and color
  const getCategoryIcon = (category: string): { icon: string; color: string } => {
    switch (category) {
      case 'TECHNICAL':
        return { icon: '⚽', color: 'text-blue-500' };
      case 'PHYSICAL':
        return { icon: '💪', color: 'text-red-500' };
      case 'PSYCHOLOGICAL':
        return { icon: '🧠', color: 'text-yellow-500' };
      case 'SOCIAL':
        return { icon: '👥', color: 'text-green-500' };
      case 'POSITIONAL':
        return { icon: '🎯', color: 'text-purple-500' };
      default:
        return { icon: '❓', color: 'text-gray-500' };
    }
  };

  const getRatingColor = (rating: number): string => {
    if (rating === 0) return 'bg-gray-600';
    if (rating <= 2) return 'bg-red-500';
    if (rating <= 3) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getRatingLabel = (rating: number): string => {
    if (rating === 0) return 'Not Rated';
    if (rating === 1) return 'Poor';
    if (rating === 2) return 'Below Average';
    if (rating === 3) return 'Average';
    if (rating === 4) return 'Good';
    if (rating === 5) return 'Excellent';
    return 'Unknown';
  };

  // Handle rating change
  const handleRatingChange = (rating: number) => {
    if (!currentQuestion) return;
    
    setQuestions(prev => 
      prev.map(q => 
        q.id === currentQuestion.id 
          ? { ...q, rating }
          : q
      )
    );
  };

  // Handle notes change
  const handleNotesChange = (notes: string) => {
    if (!currentQuestion) return;
    
    setQuestions(prev => 
      prev.map(q => 
        q.id === currentQuestion.id 
          ? { ...q, notes }
          : q
      )
    );
  };

  // Navigate to next question
  const handleNext = () => {
    if (isLastQuestion) {
      handleFinish();
    } else {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  // Navigate to previous question
  const handlePrevious = () => {
    if (!isFirstQuestion) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  // Save current player's evaluations
  const savePlayerEvaluations = async (playerId: string) => {
    try {
      const playerQuestions = questions.filter(q => q.playerId === playerId && q.rating > 0);
      
      if (playerQuestions.length === 0) return;

      const player = players.find(p => p.id === playerId);
      if (!player) return;

      const evaluations = playerQuestions.map(q => ({
        category: q.category,
        area: q.area,
        question: q.question,
        rating: q.rating,
        notes: q.notes || undefined
      }));

      await simpleEnhancedPlayerEvaluationService.upsertEventEvaluations(
        playerId,
        eventId,
        teamId,
        simpleEnhancedPlayerEvaluationService.mapUIPositionToDatabase(player.position || 'Forward'),
        evaluations
      );

      // Update main page state
      const newRatings = {
        technical: 0,
        physical: 0,
        psychological: 0,
        social: 0,
        positional: 0
      };

      playerQuestions.forEach(q => {
        const category = q.category.toLowerCase() as keyof typeof newRatings;
        if (category in newRatings) {
          newRatings[category] = q.rating;
        }
      });

      onEvaluationSaved(playerId, newRatings);
      
    } catch (error) {
      console.error('Error saving player evaluations:', error);
      throw error;
    }
  };

  // Finish evaluation process
  const handleFinish = async () => {
    try {
      setIsSaving(true);

      // Group questions by player and save each player's evaluations
      const playerIds = [...new Set(questions.map(q => q.playerId))];
      
      for (const playerId of playerIds) {
        const playerQuestions = questions.filter(q => q.playerId === playerId && q.rating > 0);
        if (playerQuestions.length > 0) {
          await savePlayerEvaluations(playerId);
          setCompletedPlayers(prev => new Set([...prev, playerId]));
        }
      }

      // Close modal after short delay
      setTimeout(() => {
        onClose();
      }, 1000);

    } catch (error) {
      console.error('Error finishing evaluation:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle modal close with unsaved changes check
  const handleClose = () => {
    const hasUnsavedChanges = questions.some(q => q.rating > 0);
    
    if (hasUnsavedChanges && !isSaving) {
      setShowConfirmClose(true);
    } else {
      onClose();
    }
  };

  // Skip current question
  const handleSkip = () => {
    handleNext();
  };

  if (!currentQuestion) {
    return (
      <IonModal isOpen={isOpen} onDidDismiss={onClose}>
        <IonHeader>
          <IonToolbar>
            <IonTitle>Quick Evaluation</IonTitle>
            <IonButton slot="end" fill="clear" onClick={onClose}>
              <IonIcon icon={closeOutline} />
            </IonButton>
          </IonToolbar>
        </IonHeader>
        <IonContent className="ion-padding">
          <div className="flex flex-col items-center justify-center min-h-[50vh]">
            <p className="text-gray-400 text-center">No evaluation questions available.</p>
            <IonButton onClick={onClose} className="mt-4">Close</IonButton>
          </div>
        </IonContent>
      </IonModal>
    );
  }

  return (
    <>
      <IonModal isOpen={isOpen} onDidDismiss={onClose}>
        {/* Header */}
        <IonHeader>
          <IonToolbar style={{ '--background': '#1f2937' }}>
            <IonTitle style={{ color: 'white' }}>
              Quick Evaluation
            </IonTitle>
            <IonButton slot="end" fill="clear" onClick={handleClose}>
              <IonIcon icon={closeOutline} style={{ color: 'white' }} />
            </IonButton>
          </IonToolbar>
        </IonHeader>

        <IonContent style={{ '--background': '#111827' }}>
          {/* Progress Header */}
          <div style={{ 
            background: 'linear-gradient(135deg, #6f42c1 0%, #3b82f6 100%)',
            padding: '20px',
            color: 'white'
          }}>
            <div className="flex items-center justify-between mb-3">
              <div className="text-sm opacity-90">
                Question {currentQuestionIndex + 1} of {totalQuestions}
              </div>
              <IonBadge color="light" style={{ '--background': 'rgba(255,255,255,0.2)' }}>
                {Math.round(progressPercentage)}%
              </IonBadge>
            </div>
            <IonProgressBar 
              value={progressPercentage / 100} 
              style={{ 
                '--background': 'rgba(255,255,255,0.2)',
                '--progress-background': 'white'
              }}
            />
          </div>

          {/* Player Info */}
          <div style={{ padding: '20px', borderBottom: '1px solid #374151' }}>
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-700 flex items-center justify-center">
                {currentPlayer?.avatar_url ? (
                  <img 
                    src={currentPlayer.avatar_url} 
                    alt={currentPlayer.full_name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <IonIcon icon={personOutline} style={{ color: 'white', fontSize: '24px' }} />
                )}
              </div>
              <div>
                <h3 style={{ color: 'white', margin: 0, fontSize: '18px', fontWeight: 'bold' }}>
                  {currentQuestion.playerName}
                </h3>
                <p style={{ color: '#9ca3af', margin: 0, fontSize: '14px' }}>
                  {currentPlayer?.position || 'Player'}
                </p>
              </div>
            </div>
          </div>

          {/* Question Content */}
          <div style={{ padding: '24px' }}>
            {/* Category Header */}
            <div className="flex items-center mb-6">
              <span style={{ fontSize: '32px', marginRight: '12px' }}>
                {getCategoryIcon(currentQuestion.category).icon}
              </span>
              <div>
                <h3 style={{ 
                  color: 'white', 
                  margin: 0, 
                  fontSize: '20px', 
                  fontWeight: '600' 
                }}>
                  {currentQuestion.area}
                </h3>
                <p style={{ 
                  color: '#9ca3af', 
                  margin: 0, 
                  fontSize: '14px' 
                }}>
                  {currentQuestion.category} Assessment
                </p>
              </div>
            </div>

            {/* Question */}
            <div style={{ 
              background: '#1f2937', 
              padding: '20px', 
              borderRadius: '12px', 
              marginBottom: '24px',
              border: '1px solid #374151'
            }}>
              <p style={{ 
                color: 'white', 
                margin: 0, 
                fontSize: '16px', 
                lineHeight: '1.5' 
              }}>
                {currentQuestion.question}
              </p>
            </div>

            {/* Rating */}
            <div style={{ marginBottom: '24px' }}>
              <div className="flex justify-between items-center mb-4">
                <label style={{ color: 'white', fontSize: '16px', fontWeight: '500' }}>
                  Rating
                </label>
                <div style={{
                  padding: '6px 12px',
                  borderRadius: '20px',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: 'white'
                }} className={getRatingColor(currentQuestion.rating)}>
                  {getRatingLabel(currentQuestion.rating)}
                </div>
              </div>

              <IonRange
                min={0}
                max={5}
                step={1}
                value={currentQuestion.rating}
                onIonChange={e => handleRatingChange(e.detail.value as number)}
                style={{
                  '--bar-background': 'rgba(255, 255, 255, 0.2)',
                  '--bar-background-active': '#6f42c1',
                  '--knob-background': '#6f42c1',
                  '--pin-background': '#6f42c1',
                  '--pin-color': 'white'
                }}
                ticks
                snaps
                pin
              />

              <div className="flex justify-between text-xs mt-2" style={{ color: '#9ca3af', paddingLeft: '8px', paddingRight: '8px' }}>
                <span>Not Rated</span>
                <span>Poor</span>
                <span>Below Avg</span>
                <span>Average</span>
                <span>Good</span>
                <span>Excellent</span>
              </div>
            </div>

            {/* Notes */}
            <div style={{ marginBottom: '32px' }}>
              <label style={{ 
                color: 'white', 
                fontSize: '14px', 
                fontWeight: '500',
                display: 'block',
                marginBottom: '8px'
              }}>
                Quick Notes (Optional)
              </label>
              <IonTextarea
                placeholder="Add specific observations..."
                value={currentQuestion.notes}
                onIonChange={e => handleNotesChange(e.detail.value!)}
                style={{
                  '--background': '#1f2937',
                  '--color': 'white',
                  '--placeholder-color': '#9ca3af',
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  '--padding-start': '12px',
                  '--padding-end': '12px',
                  '--padding-top': '12px',
                  '--padding-bottom': '12px'
                }}
                rows={2}
              />
            </div>
          </div>
        </IonContent>

        {/* Footer Navigation */}
        <div style={{ 
          background: '#1f2937', 
          padding: '16px', 
          borderTop: '1px solid #374151' 
        }}>
          <div className="flex justify-between items-center">
            {/* Previous Button */}
            <IonButton 
              fill="outline" 
              disabled={isFirstQuestion || isSaving}
              onClick={handlePrevious}
              style={{
                '--border-color': '#6b7280',
                '--color': '#6b7280',
                minWidth: '100px'
              }}
            >
              <IonIcon slot="start" icon={chevronBackOutline} />
              Previous
            </IonButton>

            {/* Skip Button */}
            <IonButton 
              fill="clear" 
              onClick={handleSkip}
              disabled={isSaving}
              style={{
                '--color': '#9ca3af'
              }}
            >
              Skip
            </IonButton>

            {/* Next/Finish Button */}
            <IonButton 
              fill="solid"
              onClick={handleNext}
              disabled={isSaving}
              style={{
                '--background': isLastQuestion ? '#10b981' : '#6f42c1',
                '--color': 'white',
                minWidth: '100px'
              }}
            >
              {isSaving ? (
                <IonSpinner name="crescent" style={{ width: '20px', height: '20px' }} />
              ) : (
                <>
                  {isLastQuestion ? (
                    <>
                      <IonIcon slot="start" icon={checkmarkCircleOutline} />
                      Finish
                    </>
                  ) : (
                    <>
                      Next
                      <IonIcon slot="end" icon={chevronForwardOutline} />
                    </>
                  )}
                </>
              )}
            </IonButton>
          </div>
        </div>
      </IonModal>

      {/* Confirm Close Alert */}
      <IonAlert
        isOpen={showConfirmClose}
        onDidDismiss={() => setShowConfirmClose(false)}
        header="Unsaved Progress"
        message="You have unsaved evaluations. Are you sure you want to close?"
        buttons={[
          {
            text: 'Cancel',
            role: 'cancel'
          },
          {
            text: 'Close Anyway',
            role: 'destructive',
            handler: () => onClose()
          }
        ]}
      />
    </>
  );
};

export default QuickEvaluationModal;
