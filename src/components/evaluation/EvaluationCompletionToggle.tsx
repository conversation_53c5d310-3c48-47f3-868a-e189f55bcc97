/**
 * EvaluationCompletionToggle Component
 * 
 * A toggle component that allows coaches to manually mark events as evaluation complete
 * or let the system automatically manage completion based on actual evaluation status.
 */

import React, { useState, useEffect } from 'react';
import {
  IonButton,
  IonIcon,
  IonToggle,
  IonLabel,
  IonItem,
  IonSpinner,
  useIonToast
} from '@ionic/react';
import {
  checkboxOutline,
  refreshOutline,
  warningOutline,
  checkmarkCircle
} from 'ionicons/icons';
import { EvaluationCompletionService } from '../../services/EvaluationCompletionService';

interface EvaluationCompletionToggleProps {
  eventId: string;
  eventName: string;
  className?: string;
  onToggle?: (completed: boolean, manual: boolean) => void;
}

const EvaluationCompletionToggle: React.FC<EvaluationCompletionToggleProps> = ({
  eventId,
  eventName,
  className = '',
  onToggle
}) => {
  const [showToast] = useIonToast();
  const [loading, setLoading] = useState(false);
  const [evaluationCompleted, setEvaluationCompleted] = useState(false);
  const [manuallyCompleted, setManuallyCompleted] = useState(false);
  const [completedAt, setCompletedAt] = useState<string | null>(null);
  const [completedBy, setCompletedBy] = useState<string | null>(null);

  // Load current evaluation status
  useEffect(() => {
    loadEvaluationStatus();
  }, [eventId]);

  const loadEvaluationStatus = async () => {
    try {
      setLoading(true);
      const status = await EvaluationCompletionService.getEventEvaluationStatus(eventId);
      if (status) {
        setEvaluationCompleted(status.evaluation_completed);
        setManuallyCompleted(status.evaluation_completed_manually);
        setCompletedAt(status.evaluation_completed_at);
        setCompletedBy(status.evaluation_completed_by);
      }
    } catch (error) {
      console.error('Error loading evaluation status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleManualToggle = async (completed: boolean) => {
    try {
      setLoading(true);
      
      const success = await EvaluationCompletionService.setEventEvaluationCompletionManual(
        eventId,
        completed
      );
      
      if (success) {
        setEvaluationCompleted(completed);
        setManuallyCompleted(true);
        setCompletedAt(completed ? new Date().toISOString() : null);
        
        showToast({
          message: `${eventName} evaluations marked as ${completed ? 'complete' : 'incomplete'}`,
          duration: 2000,
          color: completed ? 'success' : 'warning'
        });
        
        if (onToggle) {
          onToggle(completed, true);
        }
      }
    } catch (error) {
      console.error('Error updating manual completion:', error);
      showToast({
        message: 'Failed to update evaluation completion',
        duration: 2000,
        color: 'danger'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResetToAutomatic = async () => {
    try {
      setLoading(true);
      
      const success = await EvaluationCompletionService.resetEventEvaluationCompletionManual(eventId);
      
      if (success) {
        // Reload status to get the new automatic calculation
        await loadEvaluationStatus();
        
        showToast({
          message: `${eventName} reset to automatic evaluation tracking`,
          duration: 2000,
          color: 'primary'
        });
        
        if (onToggle) {
          onToggle(evaluationCompleted, false);
        }
      }
    } catch (error) {
      console.error('Error resetting to automatic:', error);
      showToast({
        message: 'Failed to reset evaluation completion',
        duration: 2000,
        color: 'danger'
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={`evaluation-completion-toggle loading ${className}`}>
        <IonSpinner name="dots" />
        <span>Loading evaluation status...</span>
      </div>
    );
  }

  return (
    <div className={`evaluation-completion-toggle ${className}`}>
      <div className="evaluation-status">
        <div className="status-header">
          <h4>Evaluation Completion</h4>
          <div className={`status-badge ${evaluationCompleted ? 'complete' : 'incomplete'}`}>
            <IonIcon icon={evaluationCompleted ? checkmarkCircle : warningOutline} />
            {evaluationCompleted ? 'Complete' : 'Incomplete'}
          </div>
        </div>
        
        {manuallyCompleted && (
          <div className="manual-override-info">
            <IonIcon icon={checkboxOutline} style={{ color: '#10b981' }} />
            <span>Manually overridden by coach</span>
            {completedAt && (
              <span className="completion-time">
                on {new Date(completedAt).toLocaleDateString()}
              </span>
            )}
          </div>
        )}
      </div>
      
      <div className="evaluation-controls">
        <IonItem lines="none">
          <IonLabel>
            <h3>Mark as Complete</h3>
            <p>Override automatic tracking</p>
          </IonLabel>
          <IonToggle
            checked={evaluationCompleted}
            onIonToggle={(e) => handleManualToggle(e.detail.checked)}
            disabled={loading}
          />
        </IonItem>
        
        {manuallyCompleted && (
          <IonButton
            fill="outline"
            size="small"
            onClick={handleResetToAutomatic}
            disabled={loading}
            style={{
              '--color': '#6b7280',
              '--border-color': '#6b7280',
              marginTop: '8px'
            }}
          >
            <IonIcon slot="start" icon={refreshOutline} />
            Reset to Automatic
          </IonButton>
        )}
      </div>
      
      <style jsx>{`
        .evaluation-completion-toggle {
          background: rgba(0, 0, 0, 0.4);
          border-radius: 12px;
          padding: 16px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .evaluation-completion-toggle.loading {
          display: flex;
          align-items: center;
          gap: 12px;
          justify-content: center;
          color: #9ca3af;
        }
        
        .status-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
        }
        
        .status-header h4 {
          margin: 0;
          color: #ffffff;
          font-size: 16px;
          font-weight: 600;
        }
        
        .status-badge {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
        }
        
        .status-badge.complete {
          background: rgba(16, 185, 129, 0.15);
          color: #10b981;
        }
        
        .status-badge.incomplete {
          background: rgba(239, 68, 68, 0.15);
          color: #ef4444;
        }
        
        .manual-override-info {
          display: flex;
          align-items: center;
          gap: 8px;
          color: #10b981;
          font-size: 14px;
          margin-bottom: 16px;
        }
        
        .completion-time {
          color: #9ca3af;
          font-size: 12px;
        }
        
        .evaluation-controls {
          margin-top: 12px;
        }
      `}</style>
    </div>
  );
};

export default EvaluationCompletionToggle;