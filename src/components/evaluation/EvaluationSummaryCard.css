/* ============================================================================
   EVALUATION SUMMARY CARD STYLES
   ============================================================================ */

.evaluation-summary-card {
  margin: 16px 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
}

.evaluation-summary-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Progress bar styling */
.evaluation-summary-card ion-progress-bar {
  border-radius: 4px;
  overflow: hidden;
}

/* Badge styling for status */
.evaluation-summary-card .status-badge {
  font-weight: 600;
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 12px;
}

/* Stats grid styling */
.evaluation-summary-card .stats-grid {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 8px;
}

/* Chip container */
.evaluation-summary-card .chip-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  margin: 12px 0;
}

.evaluation-summary-card .chip-container ion-chip {
  font-size: 11px;
  height: 28px;
}

/* Button styling */
.evaluation-summary-card .action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
}

.evaluation-summary-card .primary-button {
  height: 48px;
  font-weight: 600;
  border-radius: 8px;
}

.evaluation-summary-card .secondary-button {
  height: 40px;
  border-radius: 8px;
}

/* Loading state */
.evaluation-summary-card .loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.evaluation-summary-card .loading-container ion-spinner {
  margin-bottom: 12px;
}

/* Error state */
.evaluation-summary-card .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  text-align: center;
}

.evaluation-summary-card .error-container ion-icon {
  font-size: 28px;
  margin-bottom: 12px;
}

/* No data state */
.evaluation-summary-card .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.evaluation-summary-card .empty-state ion-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .evaluation-summary-card {
    margin: 12px 0;
  }
  
  .evaluation-summary-card .chip-container {
    justify-content: flex-start;
  }
  
  .evaluation-summary-card .stats-grid {
    padding: 12px 8px;
  }
}

/* Animation for progress bar */
@keyframes progressFill {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

.evaluation-summary-card ion-progress-bar {
  animation: progressFill 1s ease-out;
  transform-origin: left;
}

/* Status icon styling */
.evaluation-summary-card .status-icon-container {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  transition: all 0.2s ease;
}

/* Hover effects for interactive elements */
.evaluation-summary-card .clickable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.evaluation-summary-card .clickable:hover {
  opacity: 0.8;
  transform: scale(1.02);
}

/* Time estimate styling */
.evaluation-summary-card .time-estimate {
  margin-top: 16px;
  padding: 12px;
  border-radius: 8px;
  text-align: center;
  font-size: 13px;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

/* Completion percentage badge */
.evaluation-summary-card .completion-badge {
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
}

/* Header section */
.evaluation-summary-card .header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.evaluation-summary-card .header-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.evaluation-summary-card .header-title {
  color: white;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.evaluation-summary-card .header-subtitle {
  color: #9ca3af;
  margin: 2px 0 0 0;
  font-size: 14px;
}
