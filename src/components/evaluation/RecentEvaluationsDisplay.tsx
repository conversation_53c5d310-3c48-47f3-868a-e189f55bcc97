import React, { useState, useEffect } from 'react';
import {
  IonSpinner,
  IonButton,
  IonIcon,
  IonGrid,
  IonRow,
  IonCol
} from '@ionic/react';
import {
  analyticsOutline,
  chevronForward,
  alertCircleOutline,
  refreshOutline
} from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import { EventService, Event } from '../../services/EventService';
import { eventEvaluationService, EventEvaluationSummary } from '../../services/EventEvaluationService';
import EvaluationProgressCard from '../../pages/section/Coach/components/EvaluationProgressCard';
import { supabase } from '@/lib/supabase';

interface RecentEvaluationsDisplayProps {
  teamId: string;
  clubId: string;
  limit?: number;
  className?: string;
}

interface EventWithEvaluation {
  event: Event;
  evaluationSummary: EventEvaluationSummary;
}

const RecentEvaluationsDisplay: React.FC<RecentEvaluationsDisplayProps> = ({
  teamId,
  clubId,
  limit = 3,
  className = ''
}) => {
  const history = useHistory();
  const [eventsWithEvaluations, setEventsWithEvaluations] = useState<EventWithEvaluation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Function to get the actual last evaluation time from the database
  const getActualLastEvaluationTime = async (eventId: string): Promise<string | null> => {
    try {
      console.log('🕐 Getting actual last evaluation time for event:', eventId);
      
      const { data, error } = await supabase
        .from('player_evaluations')
        .select('updated_at, created_at')
        .eq('event_id', eventId)
        .order('updated_at', { ascending: false })
        .limit(1);

      if (error) {
        console.warn('Error fetching last evaluation time:', error);
        return null;
      }

      if (data && data.length > 0) {
        const lastEval = data[0];
        // Use updated_at if available, otherwise created_at
        const timestamp = lastEval.updated_at || lastEval.created_at;
        console.log('✅ Found actual last evaluation time:', timestamp);
        return timestamp;
      }

      console.log('⚠️  No evaluations found for event:', eventId);
      return null;
    } catch (error) {
      console.warn('Error in getActualLastEvaluationTime:', error);
      return null;
    }
  };

  useEffect(() => {
    loadRecentEvaluations();
  }, [teamId, limit]);

  const loadRecentEvaluations = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('🔄 Loading recent evaluations for team:', teamId);
      
      // Get all team events, sorted by most recent first
      const allEvents = await EventService.getTeamEvents(teamId);
      console.log('📅 Found', allEvents.length, 'total events for team');
      
      // Filter to only past events (events that have already started)
      const now = new Date();
      const pastEvents = allEvents.filter(event => {
        const eventStart = new Date(event.start_datetime);
        return eventStart <= now;
      }).sort((a, b) => {
        // Sort by most recent first
        return new Date(b.start_datetime).getTime() - new Date(a.start_datetime).getTime();
      });
      
      console.log('📊 Found', pastEvents.length, 'past events');
      
      // Check each past event for evaluation data
      const eventsWithEvalData: EventWithEvaluation[] = [];
      
      for (const event of pastEvents) {
        if (eventsWithEvalData.length >= limit) {
          break; // We have enough events
        }
        
        try {
          console.log('🔍 Checking evaluations for event:', event.name, event.id);
          const evaluationSummary = await eventEvaluationService.getEventEvaluationSummary(event.id!);
          
          if (evaluationSummary) {
            // Get the actual last evaluation timestamp directly from the database
            const actualLastSaveTime = await getActualLastEvaluationTime(event.id!);
            if (actualLastSaveTime) {
              evaluationSummary.last_evaluation_date = actualLastSaveTime;
            }
            // Validate data consistency
            const isDataValid = evaluationSummary.evaluated_participants <= evaluationSummary.attended_participants;
            
            if (!isDataValid) {
              console.warn('⚠️  Data inconsistency detected for event:', event.name, {
                evaluated: evaluationSummary.evaluated_participants,
                attended: evaluationSummary.attended_participants,
                event_id: event.id
              });
              
              // Fix the data by capping evaluated participants
              evaluationSummary.evaluated_participants = Math.min(
                evaluationSummary.evaluated_participants, 
                evaluationSummary.attended_participants
              );
              
              // Recalculate completion percentage
              evaluationSummary.completion_percentage = evaluationSummary.attended_participants > 0 
                ? (evaluationSummary.evaluated_participants / evaluationSummary.attended_participants) * 100
                : 0;
              
              // Update status if needed
              if (evaluationSummary.attended_participants === 0) {
                evaluationSummary.evaluation_status = 'not_started';
              } else if (evaluationSummary.evaluated_participants === evaluationSummary.attended_participants) {
                evaluationSummary.evaluation_status = 'completed';
              } else if (evaluationSummary.evaluated_participants > 0) {
                evaluationSummary.evaluation_status = 'in_progress';
              } else {
                evaluationSummary.evaluation_status = 'not_started';
              }
              
              console.log('✅ Data corrected for event:', event.name, {
                corrected_evaluated: evaluationSummary.evaluated_participants,
                attended: evaluationSummary.attended_participants,
                new_percentage: evaluationSummary.completion_percentage,
                new_status: evaluationSummary.evaluation_status
              });
            }
            
            // Only include events that have meaningful evaluation data:
            // 1. Events with attended participants, OR
            // 2. Events with evaluation activity (even if no current participants)
            const hasAttendedParticipants = evaluationSummary.attended_participants > 0;
            const hasEvaluationActivity = evaluationSummary.total_individual_evaluations > 0 || 
                                        evaluationSummary.evaluated_participants > 0;
            
            if (hasAttendedParticipants || hasEvaluationActivity) {
              console.log('✅ Found evaluation data for event:', event.name, {
                attended: evaluationSummary.attended_participants,
                evaluated: evaluationSummary.evaluated_participants,
                status: evaluationSummary.evaluation_status
              });
              eventsWithEvalData.push({
                event,
                evaluationSummary
              });
            } else {
              console.log('⚠️  Skipping event with no meaningful evaluation data:', event.name, {
                attended: evaluationSummary.attended_participants,
                total_evaluations: evaluationSummary.total_individual_evaluations
              });
            }
          } else {
            console.log('⚠️  No evaluation data for event:', event.name);
          }
        } catch (evalError) {
          console.warn('⚠️  Error checking evaluation for event:', event.name, evalError);
          // Continue to next event instead of failing completely
        }
      }
      
      console.log('📈 Final result:', eventsWithEvalData.length, 'events with evaluation data');
      setEventsWithEvaluations(eventsWithEvalData);
      
    } catch (err) {
      console.error('❌ Error loading recent evaluations:', err);
      setError('Failed to load recent evaluations');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    loadRecentEvaluations();
  };

  // Navigate to all evaluations page
  const handleViewAllEvaluations = () => {
    history.push(`/coach/club/${clubId}/team/${teamId}/evaluations`);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={`recent-evaluations-display ${className}`}>
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column',
          alignItems: 'center', 
          justifyContent: 'center',
          padding: '40px 20px',
          textAlign: 'center'
        }}>
          <IonSpinner name="dots" style={{ marginBottom: '16px' }} />
          <p style={{ color: '#9ca3af', margin: 0 }}>Loading recent evaluations...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`recent-evaluations-display ${className}`}>
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column',
          alignItems: 'center', 
          justifyContent: 'center',
          padding: '40px 20px',
          textAlign: 'center'
        }}>
          <IonIcon 
            icon={alertCircleOutline} 
            style={{ fontSize: '32px', color: '#ef4444', marginBottom: '12px' }} 
          />
          <p style={{ color: '#ef4444', margin: '0 0 16px 0' }}>{error}</p>
          <IonButton 
            size="small" 
            fill="outline" 
            onClick={handleRefresh}
            style={{ '--border-color': '#ef4444', '--color': '#ef4444' }}
          >
            Try Again
          </IonButton>
        </div>
      </div>
    );
  }

  // No evaluations state
  if (eventsWithEvaluations.length === 0) {
    return (
      <div className={`recent-evaluations-display ${className}`}>
        <div style={{ 
          display: 'flex', 
          flexDirection: 'column',
          alignItems: 'center', 
          justifyContent: 'center',
          padding: '40px 20px',
          textAlign: 'center'
        }}>
          <IonIcon 
            icon={analyticsOutline} 
            style={{ fontSize: '32px', color: '#6b7280', marginBottom: '12px' }} 
          />
          <h3 style={{ color: 'white', margin: '0 0 8px 0' }}>No Recent Evaluations</h3>
          <p style={{ color: '#9ca3af', margin: '0 0 16px 0' }}>
            No evaluations found for recent events.
          </p>
          <IonButton 
            expand="block"
            onClick={handleViewAllEvaluations}
            style={{
              '--background': '#6f42c1',
              '--color': 'white',
              maxWidth: '200px'
            }}
          >
            <IonIcon slot="start" icon={analyticsOutline} />
            View All Evaluations
          </IonButton>
        </div>
      </div>
    );
  }

  // Display evaluations
  return (
    <div className={`recent-evaluations-display ${className}`}>
      <IonGrid style={{ padding: '0' }}>
        {eventsWithEvaluations.map((item, index) => (
          <IonRow key={item.event.id} style={{ marginBottom: index < eventsWithEvaluations.length - 1 ? '16px' : '0' }}>
            <IonCol size="12">
              {/* Event Header and Progress */}
              <EvaluationProgressCard
                eventData={{
                  name: item.event.name,
                  start_datetime: item.event.start_datetime,
                  end_datetime: item.event.end_datetime,
                  location_name: item.event.location_name,
                  location_address: item.event.location_address,
                  event_type: item.event.event_type
                }}
                participantCount={item.evaluationSummary.attended_participants}
                completionPercentage={item.evaluationSummary.completion_percentage}
                progressText={`Progress: ${item.evaluationSummary.evaluated_participants} of ${item.evaluationSummary.attended_participants} players evaluated`}
                statusText={(() => {
                  switch (item.evaluationSummary.evaluation_status) {
                    case 'completed':
                      return 'All players evaluated!';
                    case 'in_progress':
                      return 'Evaluation in progress...';
                    case 'not_started':
                      return 'Evaluation not started';
                    default:
                      return 'Evaluation status unknown';
                  }
                })()}
                lastSavedTime={(() => {
                  // Handle the database timestamp safely with enhanced debugging
                  if (item.evaluationSummary.last_evaluation_date) {
                    try {
                      console.log('🕐 Processing timestamp for event:', item.event.name, 'Raw timestamp:', item.evaluationSummary.last_evaluation_date);
                      
                      const dbDate = new Date(item.evaluationSummary.last_evaluation_date);
                      console.log('🕐 Parsed date object:', dbDate, 'Time:', dbDate.getTime(), 'Local string:', dbDate.toLocaleTimeString());
                      
                      // Check if the date is valid and not in a weird timezone
                      if (dbDate.getTime() > 0 && dbDate.getFullYear() > 2020) {
                        console.log('✅ Using valid timestamp:', dbDate.toLocaleTimeString());
                        return dbDate;
                      } else {
                        console.warn('⚠️  Invalid timestamp detected:', {
                          time: dbDate.getTime(),
                          year: dbDate.getFullYear(),
                          original: item.evaluationSummary.last_evaluation_date
                        });
                      }
                    } catch (error) {
                      console.warn('❌ Error parsing timestamp:', item.evaluationSummary.last_evaluation_date, error);
                    }
                  } else {
                    console.log('⚠️  No last_evaluation_date for event:', item.event.name);
                  }
                  return undefined;
                })()}
                buttonText="Evaluation >"
                onButtonClick={() => history.push(`/coach/club/${clubId}/team/${teamId}/event/${item.event.id}/evaluate`)}
                onTitleClick={() => history.push(`/coach/club/${clubId}/team/${teamId}/event/${item.event.id}/evaluate`)}
                className="mb-0"
              />
            </IonCol>
          </IonRow>
        ))}
        
        {/* View All Button */}
        <IonRow style={{ marginTop: '20px' }}>
          <IonCol size="6">
            <IonButton 
              expand="block"
              fill="outline"
              onClick={handleRefresh}
              style={{
                '--border-color': '#10b981',
                '--color': '#10b981',
                '--border-radius': '8px',
                height: '48px',
                fontWeight: '600'
              }}
            >
              <IonIcon slot="start" icon={refreshOutline} />
              Refresh Data
            </IonButton>
          </IonCol>
          <IonCol size="6">
            <IonButton 
              expand="block"
              fill="outline"
              onClick={handleViewAllEvaluations}
              style={{
                '--border-color': '#6f42c1',
                '--color': '#6f42c1',
                '--border-radius': '8px',
                height: '48px',
                fontWeight: '600'
              }}
            >
              <IonIcon slot="start" icon={analyticsOutline} />
              View All Evaluations
            </IonButton>
          </IonCol>
        </IonRow>
      </IonGrid>
    </div>
  );
};

export default RecentEvaluationsDisplay;
