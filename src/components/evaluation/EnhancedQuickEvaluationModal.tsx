import React, { useState, useEffect, useMemo } from 'react';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButton,
  IonIcon,
  IonRange,
  IonTextarea,
  IonProgressBar,
  IonBadge,
  IonSpinner,
  Ion<PERSON>lert,
  IonCard,
  IonCardContent
} from '@ionic/react';
import {
  closeOutline,
  chevronBackOutline,
  chevronForwardOutline,
  checkmarkCircleOutline,
  saveOutline,
  personOutline,
  star,
  starOutline,
  people,
  trophy,
  peopleOutline,
  playSkipForwardOutline
} from 'ionicons/icons';
import { simpleEnhancedPlayerEvaluationService } from '../../services/SimpleEnhancedPlayerEvaluationService';

interface PlayerWithEvaluation {
  id: string;
  full_name: string;
  position?: string;
  avatar_url?: string;
  ratings: {
    technical: number;
    physical: number;
    psychological: number;
    social: number;
    positional: number;
  };
  evaluationCriteria?: { [category: string]: any[] };
  syncStatus: 'synced' | 'pending' | 'error';
  hasExistingEvaluation: boolean;
}

interface EvaluationQuestion {
  id: string;
  category: string;
  area: string;
  question: string;
  rating: number;
  notes: string;
}

interface PlayerEvaluation {
  player: PlayerWithEvaluation;
  questions: EvaluationQuestion[];
  isComplete: boolean;
  isSaved: boolean;
}

interface EnhancedQuickEvaluationModalProps {
  isOpen: boolean;
  onClose: () => void;
  players: PlayerWithEvaluation[];
  eventId: string;
  teamId: string;
  startingPlayerId?: string;
  onEvaluationSaved: (playerId: string, ratings: any) => void;
}

const EnhancedQuickEvaluationModal: React.FC<EnhancedQuickEvaluationModalProps> = ({
  isOpen,
  onClose,
  players,
  eventId,
  teamId,
  startingPlayerId,
  onEvaluationSaved
}) => {
  const [currentPlayerIndex, setCurrentPlayerIndex] = useState(0);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [playerEvaluations, setPlayerEvaluations] = useState<PlayerEvaluation[]>([]);
  const [isSaving, setIsSaving] = useState(false);
  const [showConfirmClose, setShowConfirmClose] = useState(false);
  const [showAllComplete, setShowAllComplete] = useState(false);
  const [justCompletedPlayer, setJustCompletedPlayer] = useState<string>('');
  const [skippedPlayerIds, setSkippedPlayerIds] = useState<Set<string>>(new Set());

  // Generate player evaluations data structure
  const generatePlayerEvaluations = useMemo(() => {
    const evaluations: PlayerEvaluation[] = [];
    
    // Use ALL players, don't filter - let the user decide what to do
    players.forEach(player => {
      const questions: EvaluationQuestion[] = [];
      const categories = ['technical', 'physical', 'psychological', 'social'];
      
      // Add positional category if player has a position set
      if (player.position && player.position !== 'Position not set') {
        categories.push('positional');
      }

      categories.forEach(category => {
        const categoryUpper = category.toUpperCase();
        const criteria = player.evaluationCriteria?.[categoryUpper] || [];
        const criterion = criteria.length > 0 ? criteria[0] : null;
        
        questions.push({
          id: `${player.id}_${category}`,
          category: categoryUpper,
          area: criterion?.area || `${category} evaluation`,
          question: criterion?.question || `How would you rate ${player.full_name}'s ${category} performance?`,
          rating: player.ratings[category as keyof typeof player.ratings] || 0,
          notes: ''
        });
      });

      evaluations.push({
        player,
        questions,
        isComplete: false,
        isSaved: false
      });
    });

    return evaluations;
  }, [players]);

  // Initialize data when modal opens
  useEffect(() => {
    if (isOpen && generatePlayerEvaluations.length > 0) {
      setPlayerEvaluations(generatePlayerEvaluations);
      setCurrentPlayerIndex(0);
      setCurrentQuestionIndex(0);
      setShowAllComplete(false);
      setJustCompletedPlayer('');
      setSkippedPlayerIds(new Set()); // Reset skipped players
      
      // Safety check: if somehow all players are already complete, show completion immediately
      const allComplete = generatePlayerEvaluations.every(pe => pe.isComplete);
      if (allComplete) {
        setShowAllComplete(true);
      }
    }
  }, [isOpen, generatePlayerEvaluations]);

  // Current player and question
  const currentPlayerEval = playerEvaluations[currentPlayerIndex];
  const currentQuestion = currentPlayerEval?.questions[currentQuestionIndex];
  const currentPlayer = currentPlayerEval?.player;
  
  // Progress calculations
  const totalPlayers = playerEvaluations.length;
  const completedPlayers = playerEvaluations.filter(pe => pe.isComplete).length;
  const skippedPlayers = skippedPlayerIds.size;
  const processedPlayers = completedPlayers + skippedPlayers;
  const allPlayersProcessed = processedPlayers >= totalPlayers;
  
  const playerProgressPercentage = totalPlayers > 0 ? (processedPlayers / totalPlayers) * 100 : 0;
  
  const totalQuestionsForPlayer = currentPlayerEval?.questions.length || 0;
  const completedQuestionsForPlayer = currentPlayerEval?.questions.filter(q => q.rating > 0).length || 0;
  const questionProgressPercentage = totalQuestionsForPlayer > 0 ? ((currentQuestionIndex + 1) / totalQuestionsForPlayer) * 100 : 0;
  
  // Navigation state
  const isFirstPlayer = currentPlayerIndex === 0;
  const isLastPlayer = currentPlayerIndex === totalPlayers - 1;
  const isFirstQuestion = currentQuestionIndex === 0;
  const isLastQuestion = currentQuestionIndex === totalQuestionsForPlayer - 1;
  const isLastQuestionOfLastPlayer = isLastPlayer && isLastQuestion;

  // Get category icon and color
  const getCategoryIcon = (category: string): { icon: string; color: string } => {
    switch (category) {
      case 'TECHNICAL':
        return { icon: '⚽', color: 'text-blue-500' };
      case 'PHYSICAL':
        return { icon: '💪', color: 'text-red-500' };
      case 'PSYCHOLOGICAL':
        return { icon: '🧠', color: 'text-yellow-500' };
      case 'SOCIAL':
        return { icon: '👥', color: 'text-green-500' };
      case 'POSITIONAL':
        return { icon: '🎯', color: 'text-purple-500' };
      default:
        return { icon: '❓', color: 'text-gray-500' };
    }
  };

  const getRatingColor = (rating: number): string => {
    if (rating === 0) return 'bg-gray-600';
    if (rating <= 2) return 'bg-red-500';
    if (rating <= 3) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getRatingLabel = (rating: number): string => {
    if (rating === 0) return 'Not Rated';
    if (rating === 1) return 'Poor';
    if (rating === 2) return 'Below Average';
    if (rating === 3) return 'Average';
    if (rating === 4) return 'Good';
    if (rating === 5) return 'Excellent';
    return 'Unknown';
  };

  // Handle rating change
  const handleRatingChange = (rating: number) => {
    if (!currentQuestion || !currentPlayerEval) return;
    
    setPlayerEvaluations(prev => 
      prev.map((pe, pIndex) => {
        if (pIndex === currentPlayerIndex) {
          return {
            ...pe,
            questions: pe.questions.map(q => 
              q.id === currentQuestion.id 
                ? { ...q, rating }
                : q
            )
          };
        }
        return pe;
      })
    );
  };

  // Handle notes change
  const handleNotesChange = (notes: string) => {
    if (!currentQuestion || !currentPlayerEval) return;
    
    setPlayerEvaluations(prev => 
      prev.map((pe, pIndex) => {
        if (pIndex === currentPlayerIndex) {
          return {
            ...pe,
            questions: pe.questions.map(q => 
              q.id === currentQuestion.id 
                ? { ...q, notes }
                : q
            )
          };
        }
        return pe;
      })
    );
  };

  // Save current player's evaluations
  const saveCurrentPlayerEvaluations = async () => {
    if (!currentPlayerEval) return;
    
    try {
      setIsSaving(true);
      
      const questionsWithRatings = currentPlayerEval.questions.filter(q => q.rating > 0);
      
      if (questionsWithRatings.length === 0) return;

      const evaluations = questionsWithRatings.map(q => ({
        category: q.category,
        area: q.area,
        question: q.question,
        rating: q.rating,
        notes: q.notes || undefined
      }));

      await simpleEnhancedPlayerEvaluationService.upsertEventEvaluations(
        currentPlayerEval.player.id,
        eventId,
        teamId,
        simpleEnhancedPlayerEvaluationService.mapUIPositionToDatabase(currentPlayerEval.player.position || 'Forward'),
        evaluations
      );

      // Update main page state
      const newRatings = {
        technical: 0,
        physical: 0,
        psychological: 0,
        social: 0,
        positional: 0
      };

      questionsWithRatings.forEach(q => {
        const category = q.category.toLowerCase() as keyof typeof newRatings;
        if (category in newRatings) {
          newRatings[category] = q.rating;
        }
      });

      onEvaluationSaved(currentPlayerEval.player.id, newRatings);

      // Mark player as saved and complete
      setPlayerEvaluations(prev => 
        prev.map((pe, pIndex) => 
          pIndex === currentPlayerIndex 
            ? { ...pe, isComplete: true, isSaved: true }
            : pe
        )
      );

      console.log(`Saved evaluations for ${currentPlayerEval.player.full_name}`);
      
    } catch (error) {
      console.error('Error saving player evaluations:', error);
      throw error;
    } finally {
      setIsSaving(false);
    }
  };

  // Navigate to next question
  const handleNext = async () => {
    if (!currentPlayerEval) return;

    if (isLastQuestion) {
      // Complete current player
      await saveCurrentPlayerEvaluations();
      setJustCompletedPlayer(currentPlayerEval.player.full_name);
      
      // Check if all players are now processed (completed + skipped)
      const updatedCompletedPlayers = completedPlayers + 1; // +1 for the player we just completed
      const totalProcessedAfterSave = updatedCompletedPlayers + skippedPlayers;
      
      if (totalProcessedAfterSave >= totalPlayers) {
        // All players complete or skipped
        setShowAllComplete(true);
      } else {
        // Find next unprocessed player
        let nextPlayerIndex = currentPlayerIndex + 1;
        while (nextPlayerIndex < totalPlayers && 
               (playerEvaluations[nextPlayerIndex]?.isComplete || 
                skippedPlayerIds.has(playerEvaluations[nextPlayerIndex]?.player.id))) {
          nextPlayerIndex++;
        }
        
        if (nextPlayerIndex < totalPlayers) {
          // Move to next unprocessed player
          setCurrentPlayerIndex(nextPlayerIndex);
          setCurrentQuestionIndex(0);
        } else {
          // All players processed
          setShowAllComplete(true);
        }
      }
    } else {
      // Move to next question for current player
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  // Navigate to previous question/player
  const handlePrevious = () => {
    if (isFirstQuestion && !isFirstPlayer) {
      // Go to previous player's last question
      setCurrentPlayerIndex(prev => prev - 1);
      setCurrentQuestionIndex(playerEvaluations[currentPlayerIndex - 1]?.questions.length - 1 || 0);
    } else if (!isFirstQuestion) {
      // Go to previous question for current player
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  // Handle all complete confirmation
  const handleAllCompleteClose = () => {
    setShowAllComplete(false);
    onClose();
  };

  // Skip current question only
  const handleSkipQuestion = () => {
    handleNext();
  };

  // Skip entire current player and move to next player
  const handleSkipPlayer = () => {
    if (currentPlayerEval) {
      // Track this player as skipped
      setSkippedPlayerIds(prev => new Set(prev).add(currentPlayerEval.player.id));
      console.log(`⏭️ Skipped player: ${currentPlayerEval.player.full_name}`);
    }
    
    // Check if all players are now processed (completed + skipped)
    const totalProcessedAfterSkip = completedPlayers + (skippedPlayers + 1); // +1 for the player we just skipped
    
    if (totalProcessedAfterSkip >= totalPlayers) {
      // All players complete or skipped
      setShowAllComplete(true);
    } else {
      // Find next unprocessed player
      let nextPlayerIndex = currentPlayerIndex + 1;
      const updatedSkippedIds = new Set(skippedPlayerIds).add(currentPlayerEval?.player.id || '');
      
      while (nextPlayerIndex < totalPlayers && 
             (playerEvaluations[nextPlayerIndex]?.isComplete || 
              updatedSkippedIds.has(playerEvaluations[nextPlayerIndex]?.player.id))) {
        nextPlayerIndex++;
      }
      
      if (nextPlayerIndex < totalPlayers) {
        // Move to next unprocessed player
        setCurrentPlayerIndex(nextPlayerIndex);
        setCurrentQuestionIndex(0);
      } else {
        // All players processed
        setShowAllComplete(true);
      }
    }
  };

  // Handle modal close with unsaved changes check
  const handleClose = () => {
    const hasUnsavedChanges = playerEvaluations.some(pe => 
      pe.questions.some(q => q.rating > 0) && !pe.isComplete
    );
    
    if (hasUnsavedChanges && !isSaving) {
      setShowConfirmClose(true);
    } else {
      onClose();
    }
  };

  if (!currentPlayerEval || !currentQuestion) {
    return (
      <IonModal isOpen={isOpen} onDidDismiss={onClose}>
        <IonHeader>
          <IonToolbar>
            <IonTitle>Team Evaluation</IonTitle>
            <IonButton slot="end" fill="clear" onClick={onClose}>
              <IonIcon icon={closeOutline} />
            </IonButton>
          </IonToolbar>
        </IonHeader>
        <IonContent className="ion-padding">
          <div className="flex flex-col items-center justify-center min-h-[50vh]">
            <p className="text-gray-400 text-center">No evaluation questions available.</p>
            <IonButton onClick={onClose} className="mt-4">Close</IonButton>
          </div>
        </IonContent>
      </IonModal>
    );
  }

  return (
    <>
      <IonModal isOpen={isOpen} onDidDismiss={onClose}>
        {/* Header */}
        <IonHeader>
          <IonToolbar style={{ '--background': '#1f2937' }}>
            <IonTitle style={{ color: 'white' }}>
              Team Evaluation
            </IonTitle>
            <IonButton slot="end" fill="clear" onClick={handleClose}>
              <IonIcon icon={closeOutline} style={{ color: 'white' }} />
            </IonButton>
          </IonToolbar>
        </IonHeader>

        <IonContent style={{ '--background': '#111827' }}>
          {/* Progress Header */}
          <div style={{ 
            background: 'linear-gradient(135deg, #6f42c1 0%, #3b82f6 100%)',
            padding: '20px',
            color: 'white'
          }}>
            {/* Team Progress */}
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <IonIcon icon={people} style={{ marginRight: '8px', fontSize: '18px' }} />
                <span className="text-sm opacity-90">
                  Player {currentPlayerIndex + 1} of {totalPlayers}
                  {skippedPlayerIds.size > 0 && (
                    <span className="ml-2 text-yellow-300 text-xs">
                      ({skippedPlayerIds.size} skipped)
                    </span>
                  )}
                </span>
              </div>
              <IonBadge color="light" style={{ '--background': 'rgba(255,255,255,0.2)' }}>
                {Math.round(playerProgressPercentage)}%
              </IonBadge>
            </div>
            <IonProgressBar 
              value={playerProgressPercentage / 100} 
              style={{ 
                '--background': 'rgba(255,255,255,0.2)',
                '--progress-background': 'white',
                marginBottom: '16px'
              }}
            />

            {/* Current Player Question Progress */}
            <div className="flex items-center justify-between mb-3">
              <div className="text-sm opacity-90">
                Question {currentQuestionIndex + 1} of {totalQuestionsForPlayer}
              </div>
              <IonBadge color="light" style={{ '--background': 'rgba(255,255,255,0.3)' }}>
                {Math.round(questionProgressPercentage)}%
              </IonBadge>
            </div>
            <IonProgressBar 
              value={questionProgressPercentage / 100} 
              style={{ 
                '--background': 'rgba(255,255,255,0.3)',
                '--progress-background': 'rgba(255,255,255,0.8)'
              }}
            />
          </div>

          {/* Current Player Info */}
          <div style={{ padding: '20px', borderBottom: '1px solid #374151' }}>
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 rounded-full overflow-hidden bg-gray-700 flex items-center justify-center">
                {currentPlayer?.avatar_url ? (
                  <img 
                    src={currentPlayer.avatar_url} 
                    alt={currentPlayer.full_name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <IonIcon icon={personOutline} style={{ color: 'white', fontSize: '32px' }} />
                )}
              </div>
              <div className="flex-1">
                <h3 style={{ color: 'white', margin: 0, fontSize: '20px', fontWeight: 'bold' }}>
                  {currentPlayer?.full_name}
                </h3>
                <p style={{ color: '#9ca3af', margin: 0, fontSize: '16px' }}>
                  {currentPlayer?.position || 'Player'}
                </p>
                
                {/* Player progress indicators */}
                <div className="flex items-center mt-2 space-x-1">
                  {currentPlayerEval.questions.map((q, idx) => (
                    <div
                      key={q.id}
                      style={{
                        width: '8px',
                        height: '8px',
                        borderRadius: '50%',
                        backgroundColor: q.rating > 0 ? '#10b981' : idx === currentQuestionIndex ? '#6f42c1' : '#374151'
                      }}
                    />
                  ))}
                </div>
              </div>
              
              {/* Completion badge */}
              {currentPlayerEval.isComplete && (
                <div className="flex items-center text-green-400">
                  <IonIcon icon={checkmarkCircleOutline} style={{ fontSize: '24px' }} />
                </div>
              )}
            </div>
          </div>

          {/* Question Content */}
          <div style={{ padding: '24px' }}>
            {/* Category Header */}
            <div className="flex items-center mb-6">
              <span style={{ fontSize: '32px', marginRight: '12px' }}>
                {getCategoryIcon(currentQuestion.category).icon}
              </span>
              <div>
                <h3 style={{ 
                  color: 'white', 
                  margin: 0, 
                  fontSize: '20px', 
                  fontWeight: '600' 
                }}>
                  {currentQuestion.area}
                </h3>
                <p style={{ 
                  color: '#9ca3af', 
                  margin: 0, 
                  fontSize: '14px' 
                }}>
                  {currentQuestion.category} Assessment
                </p>
              </div>
            </div>

            {/* Question */}
            <div style={{ 
              background: '#1f2937', 
              padding: '20px', 
              borderRadius: '12px', 
              marginBottom: '24px',
              border: '1px solid #374151'
            }}>
              <p style={{ 
                color: 'white', 
                margin: 0, 
                fontSize: '16px', 
                lineHeight: '1.5' 
              }}>
                {currentQuestion.question}
              </p>
            </div>

            {/* Rating */}
            <div style={{ marginBottom: '24px' }}>
              <div className="flex justify-between items-center mb-4">
                <label style={{ color: 'white', fontSize: '16px', fontWeight: '500' }}>
                  Rating
                </label>
                <div style={{
                  padding: '6px 12px',
                  borderRadius: '20px',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: 'white'
                }} className={getRatingColor(currentQuestion.rating)}>
                  {getRatingLabel(currentQuestion.rating)}
                </div>
              </div>

              <IonRange
                min={0}
                max={5}
                step={1}
                value={currentQuestion.rating}
                onIonChange={e => handleRatingChange(e.detail.value as number)}
                style={{
                  '--bar-background': 'rgba(255, 255, 255, 0.2)',
                  '--bar-background-active': '#6f42c1',
                  '--knob-background': '#6f42c1',
                  '--pin-background': '#6f42c1',
                  '--pin-color': 'white'
                }}
                ticks
                snaps
                pin
              />

              <div className="flex justify-between text-xs mt-2" style={{ color: '#9ca3af', paddingLeft: '8px', paddingRight: '8px' }}>
                <span>Not Rated</span>
                <span>Poor</span>
                <span>Below Avg</span>
                <span>Average</span>
                <span>Good</span>
                <span>Excellent</span>
              </div>
            </div>

            {/* Notes */}
            <div style={{ marginBottom: '32px' }}>
              <label style={{ 
                color: 'white', 
                fontSize: '14px', 
                fontWeight: '500',
                display: 'block',
                marginBottom: '8px'
              }}>
                Quick Notes (Optional)
              </label>
              <IonTextarea
                placeholder="Add specific observations..."
                value={currentQuestion.notes}
                onIonChange={e => handleNotesChange(e.detail.value!)}
                style={{
                  '--background': '#1f2937',
                  '--color': 'white',
                  '--placeholder-color': '#9ca3af',
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  '--padding-start': '12px',
                  '--padding-end': '12px',
                  '--padding-top': '12px',
                  '--padding-bottom': '12px'
                }}
                rows={2}
              />
            </div>
          </div>
        </IonContent>

        {/* Footer Navigation */}
        <div style={{ 
          background: '#1f2937', 
          padding: '16px', 
          borderTop: '1px solid #374151' 
        }}>
          <div className="flex justify-between items-center">
            {/* Previous Button */}
            <IonButton 
              fill="outline" 
              disabled={(isFirstPlayer && isFirstQuestion) || isSaving}
              onClick={handlePrevious}
              style={{
                '--border-color': '#6b7280',
                '--color': '#6b7280',
                minWidth: '90px'
              }}
            >
              <IonIcon slot="start" icon={chevronBackOutline} />
              Previous
            </IonButton>

            {/* Skip Options */}
            <div className="flex flex-col space-y-1">
              <IonButton 
                fill="clear" 
                size="small"
                onClick={handleSkipQuestion}
                disabled={isSaving}
                style={{
                  '--color': '#9ca3af',
                  fontSize: '12px',
                  height: '28px',
                  '--padding-start': '8px',
                  '--padding-end': '8px'
                }}
              >
                <IonIcon slot="start" icon={playSkipForwardOutline} style={{ fontSize: '14px', marginRight: '4px' }} />
                Skip Question
              </IonButton>
              
              <IonButton 
                fill="clear" 
                size="small"
                onClick={handleSkipPlayer}
                disabled={isSaving}
                style={{
                  '--color': '#f59e0b',
                  fontSize: '12px',
                  fontWeight: '600',
                  height: '28px',
                  '--padding-start': '8px',
                  '--padding-end': '8px'
                }}
              >
                <IonIcon slot="start" icon={peopleOutline} style={{ fontSize: '14px', marginRight: '4px' }} />
                Skip Player
              </IonButton>
            </div>

            {/* Next Button */}
            <IonButton 
              fill="solid"
              onClick={handleNext}
              disabled={isSaving}
              style={{
                '--background': isLastQuestion ? '#10b981' : '#6f42c1',
                '--color': 'white',
                minWidth: '120px'
              }}
            >
              {isSaving ? (
                <IonSpinner name="crescent" style={{ width: '20px', height: '20px' }} />
              ) : (
                <>
                  {isLastQuestion ? (
                    isLastPlayer ? (
                      <>
                        <IonIcon slot="start" icon={checkmarkCircleOutline} />
                        Finish
                      </>
                    ) : (
                      <>
                        <IonIcon slot="start" icon={saveOutline} />
                        Save & Next Player
                      </>
                    )
                  ) : (
                    <>
                      Next
                      <IonIcon slot="end" icon={chevronForwardOutline} />
                    </>
                  )}
                </>
              )}
            </IonButton>
          </div>
        </div>
      </IonModal>

      {/* All Complete Alert */}
      <IonAlert
        isOpen={showAllComplete}
        onDidDismiss={() => setShowAllComplete(false)}
        header="Team Evaluation Complete! 🏆"
        message={`Excellent work! You've completed the evaluation process for all ${totalPlayers} players. ${completedPlayers > 0 ? `${completedPlayers} players evaluated` : ''}${completedPlayers > 0 && skippedPlayerIds.size > 0 ? ' and ' : ''}${skippedPlayerIds.size > 0 ? `${skippedPlayerIds.size} players skipped` : ''}. All evaluations have been saved to the database.`}
        buttons={[
          {
            text: 'Done',
            handler: handleAllCompleteClose
          }
        ]}
      />

      {/* Confirm Close Alert */}
      <IonAlert
        isOpen={showConfirmClose}
        onDidDismiss={() => setShowConfirmClose(false)}
        header="Unsaved Progress"
        message="You have unsaved evaluations. Are you sure you want to close?"
        buttons={[
          {
            text: 'Cancel',
            role: 'cancel'
          },
          {
            text: 'Close Anyway',
            role: 'destructive',
            handler: () => onClose()
          }
        ]}
      />
    </>
  );
};

export default EnhancedQuickEvaluationModal;
