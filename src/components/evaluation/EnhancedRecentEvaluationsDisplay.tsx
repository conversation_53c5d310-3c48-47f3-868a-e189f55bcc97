import React, { useEffect, useState } from 'react';
import { IonIcon, IonSpinner } from '@ionic/react';
import { analyticsOutline, chevronForward } from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { eventEvaluationService, EventEvaluationSummary } from '../../services/EventEvaluationService';
import TeamEvaluationCard from '../../pages/section/Coach/evaluations/EnhancedEvaluationCard';
import '../../pages/section/Coach/TeamFlat.css';

interface EnhancedRecentEvaluationsDisplayProps {
  teamId: string;
  clubId: string;
  limit?: number;
}

interface EventWithEvaluation {
  id: string;
  name: string;
  start_datetime: string;
  end_datetime?: string;
  location_name?: string;
  event_type: string;
  evaluationSummary: EventEvaluationSummary;
}

const EnhancedRecentEvaluationsDisplay: React.FC<EnhancedRecentEvaluationsDisplayProps> = ({
  teamId,
  clubId,
  limit = 3
}) => {
  const history = useHistory();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [recentEvaluations, setRecentEvaluations] = useState<EventWithEvaluation[]>([]);

  useEffect(() => {
    const fetchRecentEvaluations = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Fetch recent events with evaluations using RPC
        const { data: eventsWithEvals, error } = await supabase
          .rpc('get_team_events_minimal', {
            team_id_param: teamId,
            limit_param: limit,
            offset_param: 0
          });

        if (error) {
          throw new Error(`Error fetching recent evaluations: ${error.message}`);
        }

        if (!eventsWithEvals || eventsWithEvals.length === 0) {
          setRecentEvaluations([]);
          setIsLoading(false);
          return;
        }

        // Process each event to get evaluation details
        const evaluationsPromises = eventsWithEvals.map(async (eventData) => {
          try {
            const evaluationSummary = await eventEvaluationService.getEventEvaluationSummary(eventData.id);
            
            if (evaluationSummary) {
              // Get actual last evaluation time
              const lastEvalTime = await getActualLastEvaluationTime(eventData.id);
              if (lastEvalTime) {
                evaluationSummary.last_evaluation_date = lastEvalTime;
              }

              // Create event with evaluation object
              return {
                id: eventData.id,
                name: eventData.name,
                start_datetime: eventData.start_datetime,
                end_datetime: eventData.start_datetime, // Use same as start since we don't have end
                location_name: 'Training Ground', // Default location
                event_type: eventData.event_type || 'Training',
                evaluationSummary
              };
            }
            return null;
          } catch (evalError) {
            console.warn('Error processing evaluation for event:', eventData.name, evalError);
            return null;
          }
        });

        // Wait for all promises to resolve
        const evaluationsResults = await Promise.all(evaluationsPromises);
        
        // Filter out nulls and set state
        const validEvaluations = evaluationsResults.filter(item => item !== null) as EventWithEvaluation[];
        setRecentEvaluations(validEvaluations);

      } catch (err) {
        console.error('Error in fetching recent evaluations:', err);
        setError('Failed to load recent evaluations');
      } finally {
        setIsLoading(false);
      }
    };

    fetchRecentEvaluations();
  }, [teamId, limit]);

  // Helper function to get actual last evaluation time
  const getActualLastEvaluationTime = async (eventId: string): Promise<string | null> => {
    try {
      const { data, error } = await supabase
        .from('player_evaluations')
        .select('updated_at, created_at')
        .eq('event_id', eventId)
        .order('updated_at', { ascending: false })
        .limit(1);

      if (error || !data || data.length === 0) return null;
      
      const lastEval = data[0];
      return lastEval.updated_at || lastEval.created_at;
    } catch (error) {
      console.warn('Error getting last evaluation time:', error);
      return null;
    }
  };

  // Navigate to all evaluations page
  const navigateToAllEvaluations = () => {
    history.push(`/coach/club/${clubId}/team/${teamId}/evaluations`);
  };

  return (
    <div className="recent-evaluations-display">
      {/* Header with title and "View All" link */}
      <div className="recent-evaluations-header">
        <div className="recent-evaluations-title">
          Recent Evaluations
        </div>
        <div 
          className="view-all-link"
          onClick={navigateToAllEvaluations}
        >
          View All
          <IonIcon icon={chevronForward} />
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="loading-container">
          <IonSpinner name="dots" className="loading-icon" />
          <div className="loading-text">Loading recent evaluations...</div>
        </div>
      )}

      {/* Error State */}
      {!isLoading && error && (
        <div className="no-evaluations-message">
          <div className="no-evaluations-text">{error}</div>
        </div>
      )}

      {/* No Evaluations State */}
      {!isLoading && !error && recentEvaluations.length === 0 && (
        <div className="no-evaluations-message">
          <IonIcon icon={analyticsOutline} className="no-evaluations-icon" />
          <div className="no-evaluations-text">No evaluations found for this team.</div>
        </div>
      )}

      {/* Evaluations List */}
      {!isLoading && !error && recentEvaluations.length > 0 && (
        <div className="evaluations-list">
          {recentEvaluations.map(evaluation => (
            <TeamEvaluationCard
              key={evaluation.id}
              id={evaluation.id}
              name={evaluation.name}
              eventType={evaluation.event_type}
              date={evaluation.start_datetime}
              location={evaluation.location_name}
              evaluatedPlayers={evaluation.evaluationSummary.evaluated_participants}
              totalPlayers={evaluation.evaluationSummary.attended_participants}
              completionPercentage={evaluation.evaluationSummary.completion_percentage}
              lastSaved={evaluation.evaluationSummary.last_evaluation_date}
              clubId={clubId}
              teamId={teamId}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default EnhancedRecentEvaluationsDisplay;