import React, { useState, useEffect, useMemo } from 'react';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonContent,
  IonButton,
  IonIcon,
  IonRange,
  IonTextarea,
  IonProgressBar,
  IonBadge,
  IonSpinner,
  IonAlert
} from '@ionic/react';
import {
  closeOutline,
  chevronBackOutline,
  chevronForwardOutline,
  checkmarkCircleOutline,
  saveOutline,
  personOutline,
  people,
  peopleOutline,
  playSkipForwardOutline
} from 'ionicons/icons';
// import { simpleEnhancedPlayerEvaluationService } from '../../services/SimpleEnhancedPlayerEvaluationService'; // Moved to outbox
import playerEvaluationService from '../../services/PlayerEvaluationService';

interface PlayerWithEvaluation {
  id: string;
  full_name: string;
  position?: string;
  avatar_url?: string;
  ratings: {
    technical: number;
    physical: number;
    psychological: number;
    social: number;
    positional: number;
  };
  evaluationCriteria?: { [category: string]: any[] };
  syncStatus: 'synced' | 'pending' | 'error';
  hasExistingEvaluation: boolean;
}

interface EvaluationQuestion {
  id: string;
  category: string;
  area: string;
  question: string;
  rating: number;
  notes: string;
}

interface SimpleQuickEvaluationModalProps {
  isOpen: boolean;
  onClose: () => void;
  players: PlayerWithEvaluation[];
  eventId: string;
  teamId: string;
  onEvaluationSaved: (playerId: string, ratings: any) => void;
  startingPlayerId?: string; // Optional: which player to start with
}

const SimpleQuickEvaluationModal: React.FC<SimpleQuickEvaluationModalProps> = ({
  isOpen,
  onClose,
  players,
  eventId,
  teamId,
  onEvaluationSaved,
  startingPlayerId
}) => {
  const [currentPlayerIndex, setCurrentPlayerIndex] = useState(0);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isSaving, setIsSaving] = useState(false);
  const [showConfirmClose, setShowConfirmClose] = useState(false);
  const [showAllComplete, setShowAllComplete] = useState(false);
  const [questionsForAllPlayers, setQuestionsForAllPlayers] = useState<{ [playerId: string]: EvaluationQuestion[] }>({});

  // Generate questions for all players (only when modal first opens)
  useEffect(() => {
    if (isOpen && players.length > 0 && Object.keys(questionsForAllPlayers).length === 0) {
      console.log('=== MODAL INITIALIZING FOR FIRST TIME ===');
      const allQuestions: { [playerId: string]: EvaluationQuestion[] } = {};
      
      players.forEach(player => {
        const questions: EvaluationQuestion[] = [];
        const categories = ['technical', 'physical', 'psychological', 'social'];
        
        // Add positional if player has position
        if (player.position && player.position !== 'Position not set') {
          categories.push('positional');
        }

        categories.forEach(category => {
          const categoryUpper = category.toUpperCase();
          const criteria = player.evaluationCriteria?.[categoryUpper] || [];
          const criterion = criteria.length > 0 ? criteria[0] : null;
          
          questions.push({
            id: `${player.id}_${category}`,
            category: categoryUpper,
            area: criterion?.area || `${category} evaluation`,
            question: criterion?.question || `How would you rate ${player.full_name}'s ${category} performance?`,
            rating: player.ratings[category as keyof typeof player.ratings] || 0,
            notes: ''
          });
        });

        allQuestions[player.id] = questions;
      });

      setQuestionsForAllPlayers(allQuestions);
      
      // Set starting player index based on startingPlayerId
      let initialPlayerIndex = 0;
      if (startingPlayerId) {
        const playerIndex = players.findIndex(p => p.id === startingPlayerId);
        if (playerIndex >= 0) {
          initialPlayerIndex = playerIndex;
          console.log(`=== STARTING WITH PLAYER: ${players[playerIndex].full_name} (index ${playerIndex}) ===`);
        } else {
          console.log(`=== STARTING PLAYER ID ${startingPlayerId} NOT FOUND, USING FIRST PLAYER ===`);
        }
      }
      
      setCurrentPlayerIndex(initialPlayerIndex);
      setCurrentQuestionIndex(0);
      setShowAllComplete(false);
      
      console.log('=== MODAL INITIALIZED ===');
      console.log('Total players:', players.length);
      console.log('Starting player index:', initialPlayerIndex);
      console.log('Questions generated for:', Object.keys(allQuestions).length, 'players');
    } else if (isOpen && Object.keys(questionsForAllPlayers).length > 0) {
      console.log('=== MODAL REOPENED - KEEPING EXISTING PROGRESS ===');
    }
  }, [isOpen, startingPlayerId]); // Added startingPlayerId dependency

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      console.log('=== MODAL CLOSED - RESETTING STATE ===');
      setQuestionsForAllPlayers({});
      setCurrentPlayerIndex(0);
      setCurrentQuestionIndex(0);
      setShowAllComplete(false);
      setIsSaving(false);
    }
  }, [isOpen]);

  // Monitor completion state changes
  useEffect(() => {
    console.log('=== SHOW ALL COMPLETE STATE CHANGED ===', showAllComplete);
    if (showAllComplete) {
      console.log('>>> Completion alert should be visible now!');
    }
  }, [showAllComplete]);

  // Calculate meaningful progress based on evaluation completion
  const calculateEvaluationProgress = () => {
    // Count players who have NO evaluations (completely blank)
    const playersNeedingEvaluation = players.filter(player => {
      const hasAnyRating = Object.values(player.ratings).some(rating => rating > 0);
      return !hasAnyRating;
    });
    
    // Count how many blank players we've completed in this session
    let completedInSession = 0;
    playersNeedingEvaluation.forEach(player => {
      const currentQuestions = questionsForAllPlayers[player.id] || [];
      const hasCompletedAnyQuestion = currentQuestions.some(q => q.rating > 0);
      if (hasCompletedAnyQuestion) {
        completedInSession++;
      }
    });
    
    return {
      totalNeedingEvaluation: playersNeedingEvaluation.length,
      completedInSession,
      remaining: playersNeedingEvaluation.length - completedInSession,
      allPlayersHaveEvaluations: playersNeedingEvaluation.length === 0
    };
  };
  
  const evaluationProgress = calculateEvaluationProgress();
  
  // Current player and question
  const currentPlayer = players[currentPlayerIndex];
  const currentQuestions = currentPlayer ? questionsForAllPlayers[currentPlayer.id] || [] : [];
  const currentQuestion = currentQuestions[currentQuestionIndex];
  
  // Simple progress calculations for current player's questions
  const totalPlayers = players.length;
  const questionProgress = currentQuestions.length > 0 ? ((currentQuestionIndex + 1) / currentQuestions.length) * 100 : 0;
  
  // Overall progress based on evaluation completion
  const overallProgress = evaluationProgress.totalNeedingEvaluation > 0 
    ? (evaluationProgress.completedInSession / evaluationProgress.totalNeedingEvaluation) * 100 
    : 100;
  
  // Navigation states
  const isLastPlayer = currentPlayerIndex === totalPlayers - 1;
  const isLastQuestion = currentQuestionIndex === currentQuestions.length - 1;
  const isVeryEnd = isLastPlayer && isLastQuestion;

  // Get category icon and color
  const getCategoryIcon = (category: string): { icon: string; color: string } => {
    switch (category) {
      case 'TECHNICAL':
        return { icon: '⚽', color: 'text-blue-500' };
      case 'PHYSICAL':
        return { icon: '💪', color: 'text-red-500' };
      case 'PSYCHOLOGICAL':
        return { icon: '🧠', color: 'text-yellow-500' };
      case 'SOCIAL':
        return { icon: '👥', color: 'text-green-500' };
      case 'POSITIONAL':
        return { icon: '🎯', color: 'text-purple-500' };
      default:
        return { icon: '❓', color: 'text-gray-500' };
    }
  };

  const getRatingColor = (rating: number): string => {
    if (rating === 0) return 'bg-gray-600';
    if (rating <= 1) return 'bg-red-600';
    if (rating <= 3) return 'bg-yellow-600';
    return 'bg-green-600';
  };

  const getRatingLabel = (rating: number): string => {
    if (rating === 0) return 'Not Rated';
    if (rating === 1) return 'Poor';
    if (rating === 2) return 'Below Average';
    if (rating === 3) return 'Average';
    if (rating === 4) return 'Good';
    if (rating === 5) return 'Excellent';
    return 'Unknown';
  };

  // Handle rating change
  const handleRatingChange = (rating: number) => {
    if (!currentQuestion || !currentPlayer) return;
    
    setQuestionsForAllPlayers(prev => ({
      ...prev,
      [currentPlayer.id]: prev[currentPlayer.id].map(q => 
        q.id === currentQuestion.id ? { ...q, rating } : q
      )
    }));
  };

  // Handle notes change
  const handleNotesChange = (notes: string) => {
    if (!currentQuestion || !currentPlayer) return;
    
    setQuestionsForAllPlayers(prev => ({
      ...prev,
      [currentPlayer.id]: prev[currentPlayer.id].map(q => 
        q.id === currentQuestion.id ? { ...q, notes } : q
      )
    }));
  };

  // Save current player's evaluations
  const saveCurrentPlayerEvaluations = async () => {
    if (!currentPlayer) return;
    
    try {
      setIsSaving(true);
      
      const playerQuestions = questionsForAllPlayers[currentPlayer.id] || [];
      const questionsWithRatings = playerQuestions.filter(q => q.rating > 0);
      
      if (questionsWithRatings.length === 0) return;

      const evaluations = questionsWithRatings.map(q => ({
        category: q.category,
        area: q.area,
        question: q.question,
        rating: q.rating,
        notes: q.notes || undefined
      }));

      // TODO: Replace with playerEvaluationService equivalent
      console.log('TODO: Save evaluations for player', currentPlayer.id);
      // await simpleEnhancedPlayerEvaluationService.upsertEventEvaluations(
      //   currentPlayer.id,
      //   eventId,
      //   teamId,
      //   playerEvaluationService.mapUIPositionToDatabase(currentPlayer.position || 'Forward'),
      //   evaluations
      // );

      // Update main page state immediately
      const newRatings = {
        technical: 0,
        physical: 0,
        psychological: 0,
        social: 0,
        positional: 0
      };

      questionsWithRatings.forEach(q => {
        const category = q.category.toLowerCase() as keyof typeof newRatings;
        if (category in newRatings) {
          newRatings[category] = q.rating;
        }
      });

      // This will trigger a re-render with updated progress calculations
      onEvaluationSaved(currentPlayer.id, newRatings);
      
      console.log('>>> Player evaluation saved and main page state updated');
      
    } catch (error) {
      console.error('Error saving player evaluations:', error);
      throw error;
    } finally {
      setIsSaving(false);
    }
  };

  // Simple navigation: Next
  const handleNext = async () => {
    console.log('=== HANDLE NEXT CALLED ===');
    console.log('Current player index:', currentPlayerIndex);
    console.log('Current question index:', currentQuestionIndex);
    console.log('Total players:', totalPlayers);
    console.log('Current questions length:', currentQuestions.length);
    console.log('Is last question:', isLastQuestion);
    console.log('Is last player:', isLastPlayer);
    console.log('Evaluation progress before save:', evaluationProgress);
    
    if (isLastQuestion) {
      console.log('>>> On last question, saving player...');
      // Save current player
      await saveCurrentPlayerEvaluations();
      
      // Small delay to let state updates propagate, then recalculate
      setTimeout(() => {
        const updatedProgress = calculateEvaluationProgress();
        console.log('>>> Updated progress after save:', updatedProgress);
        
        if (updatedProgress.remaining === 0 || isLastPlayer) {
          console.log('>>> SHOULD COMPLETE - either no remaining evaluations or reached end of list!');
          setShowAllComplete(true);
        } else {
          console.log('>>> Moving to next player:', currentPlayerIndex + 1);
          // Move to next player, first question
          setCurrentPlayerIndex(currentPlayerIndex + 1);
          setCurrentQuestionIndex(0);
        }
      }, 100); // Small delay to let parent state update
    } else {
      console.log('>>> Moving to next question:', currentQuestionIndex + 1);
      // Move to next question
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
    console.log('=== HANDLE NEXT COMPLETED ===');
  };

  // Simple navigation: Previous
  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      // Go to previous question
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    } else if (currentPlayerIndex > 0) {
      // Go to previous player, last question
      setCurrentPlayerIndex(currentPlayerIndex - 1);
      const prevPlayerQuestions = questionsForAllPlayers[players[currentPlayerIndex - 1].id] || [];
      setCurrentQuestionIndex(Math.max(0, prevPlayerQuestions.length - 1));
    }
  };

  // Skip current question
  const handleSkipQuestion = () => {
    handleNext();
  };

  // Skip entire player
  const handleSkipPlayer = () => {
    console.log('=== SKIP PLAYER CALLED ===');
    console.log('Current player index:', currentPlayerIndex);
    console.log('Total players:', totalPlayers);
    console.log('Is last player:', isLastPlayer);
    console.log('Evaluation progress:', evaluationProgress);
    
    // Check if we should complete based on meaningful progress or reaching end
    if (evaluationProgress.remaining <= 1 || isLastPlayer) {
      console.log('>>> Skipping - SHOULD COMPLETE!');
      setShowAllComplete(true);
      return;
    } else {
      console.log('>>> Skipping to next player:', currentPlayerIndex + 1);
      // Move to next player, first question
      setCurrentPlayerIndex(currentPlayerIndex + 1);
      setCurrentQuestionIndex(0);
    }
    console.log('=== SKIP PLAYER COMPLETED ===');
  };

  // Handle save and close
  const handleSaveAndClose = async () => {
    try {
      await saveCurrentPlayerEvaluations();
      onClose();
    } catch (error) {
      console.error('Error saving evaluation:', error);
      // Don't close modal if save failed
    }
  };

  // Handle completion
  const handleAllCompleteClose = () => {
    console.log('=== COMPLETION ALERT CLOSED ===');
    setShowAllComplete(false);
    onClose();
  };

  // Handle close with unsaved changes check
  const handleClose = () => {
    const hasUnsavedChanges = Object.values(questionsForAllPlayers).some(questions =>
      questions.some(q => q.rating > 0)
    );
    
    if (hasUnsavedChanges && !isSaving) {
      setShowConfirmClose(true);
    } else {
      onClose();
    }
  };

  if (!currentPlayer || !currentQuestion) {
    return (
      <IonModal isOpen={isOpen} onDidDismiss={onClose}>
        <IonHeader>
          <IonToolbar>
            <IonTitle>Team Evaluation</IonTitle>
            <IonButton slot="end" fill="clear" onClick={onClose}>
              <IonIcon icon={closeOutline} />
            </IonButton>
          </IonToolbar>
        </IonHeader>
        <IonContent className="ion-padding">
          <div className="flex flex-col items-center justify-center min-h-[50vh]">
            <p className="text-gray-400 text-center">Loading evaluation questions...</p>
          </div>
        </IonContent>
      </IonModal>
    );
  }

  return (
    <>
      <IonModal isOpen={isOpen} onDidDismiss={onClose}>
        {/* Header with minimal progress */}
        <IonHeader>
          <IonToolbar style={{ '--background': '#1f2937' }}>
            <IonTitle style={{ color: 'white', fontSize: '18px' }}>
              {currentPlayer?.full_name} - Question {currentQuestionIndex + 1} of {currentQuestions.length}
            </IonTitle>
            <IonButton slot="end" fill="clear" onClick={handleClose}>
              <IonIcon icon={closeOutline} style={{ color: 'white' }} />
            </IonButton>
          </IonToolbar>
          {/* Minimal progress bar in header */}
          <IonProgressBar 
            value={questionProgress / 100} 
            style={{ 
              '--progress-background': '#6f42c1',
              height: '3px'
            }}
          />
        </IonHeader>

        <IonContent style={{ '--background': '#111827' }}>
          {/* Current Player Info - Compact */}
          <div style={{ padding: '16px', borderBottom: '1px solid #374151' }}>
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-700 flex items-center justify-center">
                {currentPlayer?.avatar_url ? (
                  <img 
                    src={currentPlayer.avatar_url} 
                    alt={currentPlayer.full_name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <IonIcon icon={personOutline} style={{ color: 'white', fontSize: '24px' }} />
                )}
              </div>
              <div className="flex-1">
                <h3 style={{ color: 'white', margin: 0, fontSize: '18px', fontWeight: 'bold' }}>
                  {currentPlayer?.full_name}
                </h3>
                <p style={{ color: '#9ca3af', margin: 0, fontSize: '14px' }}>
                  {currentPlayer?.position || 'Player'} • {currentQuestion.category}
                </p>
              </div>
            </div>
          </div>

          {/* Question Content */}
          <div style={{ padding: '20px' }}>
            {/* Category Header */}
            <div className="flex items-center mb-4">
              <span style={{ fontSize: '28px', marginRight: '10px' }}>
                {getCategoryIcon(currentQuestion.category).icon}
              </span>
              <div>
                <h3 style={{ 
                  color: 'white', 
                  margin: 0, 
                  fontSize: '18px', 
                  fontWeight: '600' 
                }}>
                  {currentQuestion.area}
                </h3>
              </div>
            </div>

            {/* Question */}
            <div style={{ 
              background: '#1f2937', 
              padding: '16px', 
              borderRadius: '10px', 
              marginBottom: '20px',
              border: '1px solid #374151'
            }}>
              <p style={{ 
                color: 'white', 
                margin: 0, 
                fontSize: '15px', 
                lineHeight: '1.4' 
              }}>
                {currentQuestion.question}
              </p>
            </div>

            {/* Rating */}
            <div style={{ marginBottom: '20px' }}>
              <div className="flex justify-between items-center mb-3">
                <label style={{ color: 'white', fontSize: '15px', fontWeight: '500' }}>
                  Rating
                </label>
                <div style={{
                  padding: '4px 10px',
                  borderRadius: '16px',
                  fontSize: '13px',
                  fontWeight: '500',
                  color: 'white'
                }} className={getRatingColor(currentQuestion.rating)}>
                  {getRatingLabel(currentQuestion.rating)}
                </div>
              </div>

              <IonRange
                min={0}
                max={5}
                step={1}
                value={currentQuestion.rating}
                onIonChange={e => handleRatingChange(e.detail.value as number)}
                style={{
                  '--bar-background': 'rgba(255, 255, 255, 0.2)',
                  '--bar-background-active': currentQuestion.rating === 0 ? '#6b7280' :
                                           currentQuestion.rating <= 1 ? '#dc2626' :
                                           currentQuestion.rating <= 3 ? '#ca8a04' : '#16a34a',
                  '--knob-background': currentQuestion.rating === 0 ? '#6b7280' :
                                     currentQuestion.rating <= 1 ? '#dc2626' :
                                     currentQuestion.rating <= 3 ? '#ca8a04' : '#16a34a',
                  '--pin-background': currentQuestion.rating === 0 ? '#6b7280' :
                                    currentQuestion.rating <= 1 ? '#dc2626' :
                                    currentQuestion.rating <= 3 ? '#ca8a04' : '#16a34a',
                  '--pin-color': 'white'
                }}
                ticks
                snaps
                pin
              />

              <div className="flex justify-between text-xs mt-1" style={{ color: '#9ca3af', paddingLeft: '6px', paddingRight: '6px' }}>
                <span>Not Rated</span>
                <span>Poor</span>
                <span>Below Avg</span>
                <span>Average</span>
                <span>Good</span>
                <span>Excellent</span>
              </div>
            </div>

            {/* Navigation Buttons - RIGHT AFTER RATING */}
            <div style={{ marginBottom: '24px' }}>
              <div className="flex justify-between items-center mb-2">
                {/* Previous Button */}
                <IonButton 
                  fill="outline" 
                  size="default"
                  disabled={(currentPlayerIndex === 0 && currentQuestionIndex === 0) || isSaving}
                  onClick={handlePrevious}
                  style={{
                    '--border-color': '#6b7280',
                    '--color': '#6b7280',
                    width: '80px',
                    height: '40px'
                  }}
                >
                  <IonIcon slot="start" icon={chevronBackOutline} />
                  Prev
                </IonButton>

                {/* Next Button - PROMINENT */}
                <IonButton 
                  fill="solid"
                  size="default"
                  onClick={handleNext}
                  disabled={isSaving}
                  style={{
                    '--background': isVeryEnd ? '#10b981' : '#6f42c1',
                    '--color': 'white',
                    width: '140px',
                    height: '40px',
                    fontWeight: '600'
                  }}
                >
                  {isSaving ? (
                    <IonSpinner name="crescent" style={{ width: '18px', height: '18px' }} />
                  ) : (
                    <>
                      {isVeryEnd ? (
                        <>
                          <IonIcon slot="start" icon={checkmarkCircleOutline} />
                          Finish
                        </>
                      ) : isLastQuestion ? (
                        <>
                          <IonIcon slot="start" icon={saveOutline} />
                          Next Player
                        </>
                      ) : (
                        <>
                          Next
                          <IonIcon slot="end" icon={chevronForwardOutline} />
                        </>
                      )}
                    </>
                  )}
                </IonButton>
              </div>

              {/* Skip Options - Small and Secondary */}
              <div className="flex justify-center space-x-4">
                <IonButton 
                  fill="clear" 
                  size="small"
                  onClick={handleSkipQuestion}
                  disabled={isSaving}
                  style={{
                    '--color': '#9ca3af',
                    fontSize: '11px',
                    height: '24px',
                    '--padding-start': '6px',
                    '--padding-end': '6px'
                  }}
                >
                  Skip Question
                </IonButton>
                
                <IonButton 
                  fill="clear" 
                  size="small"
                  onClick={handleSkipPlayer}
                  disabled={isSaving}
                  style={{
                    '--color': '#f59e0b',
                    fontSize: '11px',
                    height: '24px',
                    '--padding-start': '6px',
                    '--padding-end': '6px'
                  }}
                >
                  Skip Player
                </IonButton>
              </div>
            </div>

            {/* Notes - Moved to bottom, optional */}
            <div style={{ marginBottom: '80px' }}>
              <label style={{ 
                color: 'white', 
                fontSize: '13px', 
                fontWeight: '500',
                display: 'block',
                marginBottom: '6px'
              }}>
                Quick Notes (Optional)
              </label>
              <IonTextarea
                placeholder="Add specific observations..."
                value={currentQuestion.notes}
                onIonChange={e => handleNotesChange(e.detail.value!)}
                style={{
                  '--background': '#1f2937',
                  '--color': 'white',
                  '--placeholder-color': '#9ca3af',
                  border: '1px solid #374151',
                  borderRadius: '6px',
                  '--padding-start': '10px',
                  '--padding-end': '10px',
                  '--padding-top': '8px',
                  '--padding-bottom': '8px'
                }}
                rows={2}
              />
            </div>
          </div>
        </IonContent>

        {/* Bottom Save Button - Safety Net */}
        <div style={{ 
          background: '#1f2937', 
          padding: '12px', 
          borderTop: '1px solid #374151' 
        }}>
          <IonButton 
            expand="block"
            fill="solid"
            onClick={handleSaveAndClose}
            disabled={isSaving}
            style={{
              '--background': '#10b981',
              '--color': 'white',
              height: '36px',
              fontSize: '14px'
            }}
          >
            {isSaving ? (
              <>
                <IonSpinner name="crescent" style={{ width: '16px', height: '16px', marginRight: '6px' }} />
                Saving...
              </>
            ) : (
              <>
                <IonIcon slot="start" icon={saveOutline} />
                Save & Close
              </>
            )}
          </IonButton>
        </div>
      </IonModal>

      {/* All Complete Alert */}
      <IonAlert
        isOpen={showAllComplete}
        onDidDismiss={() => setShowAllComplete(false)}
        header="Evaluation Session Complete! 🏆"
        message={`Great work! ${evaluationProgress.allPlayersHaveEvaluations ? 'All players already had evaluations.' : evaluationProgress.completedInSession > 0 ? `You completed ${evaluationProgress.completedInSession} new evaluation${evaluationProgress.completedInSession === 1 ? '' : 's'}.` : 'Session completed.'} All evaluations have been saved to the database.`}
        buttons={[
          {
            text: 'Done',
            handler: handleAllCompleteClose
          }
        ]}
      />

      {/* Confirm Close Alert */}
      <IonAlert
        isOpen={showConfirmClose}
        onDidDismiss={() => setShowConfirmClose(false)}
        header="Unsaved Progress"
        message="You have unsaved evaluations. Are you sure you want to close?"
        buttons={[
          {
            text: 'Cancel',
            role: 'cancel'
          },
          {
            text: 'Close Anyway',
            role: 'destructive',
            handler: () => onClose()
          }
        ]}
      />
    </>
  );
};

export default SimpleQuickEvaluationModal;
