import React, { useState, useEffect } from 'react';
import {
  IonCard,
  IonCardContent,
  IonButton,
  IonIcon,
  IonSpinner,
  IonBadge,
  IonChip,
  IonProgressBar,
  IonGrid,
  IonRow,
  IonCol
} from '@ionic/react';
import {
  analyticsOutline,
  checkmarkCircle,
  timeOutline,
  peopleOutline,
  starOutline,
  trendingUpOutline,
  listOutline,
  alertCircleOutline
} from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import { eventEvaluationService, EventEvaluationSummary } from '../../services/EventEvaluationService';
import { SHOT_COLORS } from '../../styles/shotColors';
import './EvaluationSummaryCard.css';

interface EvaluationSummaryCardProps {
  eventId: string;
  clubId: string;
  teamId: string;
  onRefresh?: () => void;
  className?: string;
}

const EvaluationSummaryCard: React.FC<EvaluationSummaryCardProps> = ({
  eventId,
  clubId,
  teamId,
  onRefresh,
  className = ''
}) => {
  const history = useHistory();
  const [summary, setSummary] = useState<EventEvaluationSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load evaluation summary
  useEffect(() => {
    loadEvaluationSummary();
  }, [eventId]);

  const loadEvaluationSummary = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      console.log('🔄 Loading evaluation summary for event:', eventId);
      const summaryData = await eventEvaluationService.getEventEvaluationSummary(eventId);
      
      if (summaryData) {
        setSummary(summaryData);
        console.log('✅ Evaluation summary loaded successfully');
      } else {
        console.log('⚠️  No summary data available');
        setSummary(null);
      }
    } catch (err) {
      console.error('❌ Error loading evaluation summary:', err);
      setError('Failed to load evaluation summary');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle navigation to evaluation page
  const handleEvaluateAllPlayers = () => {
    const returnUrl = encodeURIComponent(window.location.pathname + window.location.search);
    const targetUrl = `/coach/club/${clubId}/team/${teamId}/event/${eventId}/evaluate?returnTo=${returnUrl}`;
    console.log('🎯 Navigating to evaluation page:', targetUrl);
    history.push(targetUrl);
  };

  // Handle navigation to detailed view
  const handleViewDetails = () => {
    const returnUrl = encodeURIComponent(window.location.pathname);
    const targetUrl = `/coach/club/${clubId}/team/${teamId}/event/${eventId}/evaluation-details?returnTo=${returnUrl}`;
    history.push(targetUrl);
  };

  // Refresh handler
  const handleRefresh = async () => {
    await loadEvaluationSummary();
    if (onRefresh) {
      onRefresh();
    }
  };

  // Get status-based styling
  const getStatusStyling = (status: string) => {
    switch (status) {
      case 'completed':
        return {
          color: '#10b981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          icon: checkmarkCircle
        };
      case 'in_progress':
        return {
          color: '#f59e0b',
          backgroundColor: 'rgba(245, 158, 11, 0.1)',
          icon: analyticsOutline
        };
      case 'not_started':
        return {
          color: '#6b7280',
          backgroundColor: 'rgba(107, 114, 128, 0.1)',
          icon: alertCircleOutline
        };
      default:
        return {
          color: '#6b7280',
          backgroundColor: 'rgba(107, 114, 128, 0.1)',
          icon: alertCircleOutline
        };
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <IonCard className={`evaluation-summary-card ${className}`}>
        <IonCardContent>
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center', 
            justifyContent: 'center',
            padding: '20px',
            textAlign: 'center'
          }}>
            <IonSpinner name="dots" style={{ marginBottom: '12px' }} />
            <p style={{ color: '#9ca3af', margin: 0 }}>Loading evaluation summary...</p>
          </div>
        </IonCardContent>
      </IonCard>
    );
  }

  // Error state
  if (error) {
    return (
      <IonCard className={`evaluation-summary-card ${className}`}>
        <IonCardContent>
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center', 
            justifyContent: 'center',
            padding: '20px',
            textAlign: 'center'
          }}>
            <IonIcon 
              icon={alertCircleOutline} 
              style={{ fontSize: '24px', color: '#ef4444', marginBottom: '8px' }} 
            />
            <p style={{ color: '#ef4444', margin: '0 0 12px 0' }}>{error}</p>
            <IonButton 
              size="small" 
              fill="outline" 
              onClick={handleRefresh}
              style={{ '--border-color': '#ef4444', '--color': '#ef4444' }}
            >
              Try Again
            </IonButton>
          </div>
        </IonCardContent>
      </IonCard>
    );
  }

  // No data state
  if (!summary) {
    return (
      <IonCard className={`evaluation-summary-card ${className}`}>
        <IonCardContent>
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column',
            alignItems: 'center', 
            justifyContent: 'center',
            padding: '20px',
            textAlign: 'center'
          }}>
            <IonIcon 
              icon={analyticsOutline} 
              style={{ fontSize: '32px', color: '#6b7280', marginBottom: '12px' }} 
            />
            <h3 style={{ color: 'white', margin: '0 0 8px 0' }}>Player Evaluations</h3>
            <p style={{ color: '#9ca3af', margin: '0 0 16px 0' }}>
              No evaluation data available for this event.
            </p>
            <IonButton 
              expand="block"
              onClick={handleEvaluateAllPlayers}
              style={{
                '--background': SHOT_COLORS.orange,
                '--color': 'black',
                maxWidth: '200px'
              }}
            >
              <IonIcon slot="start" icon={analyticsOutline} />
              Start Evaluations
            </IonButton>
          </div>
        </IonCardContent>
      </IonCard>
    );
  }

  const statusStyling = getStatusStyling(summary.evaluation_status);
  const completionProgress = summary.attended_participants > 0 
    ? summary.completion_percentage / 100 
    : 0;

  return (
    <IonCard className={`evaluation-summary-card ${className}`} style={{
      backgroundColor: '#1a1e2a',
      border: '1px solid #2d3348',
      borderRadius: '12px',
      overflow: 'hidden'
    }}>
      <IonCardContent style={{ padding: '20px' }}>
        {/* Header Section */}
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '20px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '10px',
              backgroundColor: statusStyling.backgroundColor,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginRight: '12px'
            }}>
              <IonIcon 
                icon={statusStyling.icon} 
                style={{ fontSize: '24px', color: statusStyling.color }} 
              />
            </div>
            <div>
              <h3 style={{ color: 'white', margin: '0', fontSize: '18px', fontWeight: '600' }}>
                Player Evaluations
              </h3>
              <p style={{ color: '#9ca3af', margin: '2px 0 0 0', fontSize: '14px' }}>
                {eventEvaluationService.getEvaluationStatusText(summary.evaluation_status)}
              </p>
            </div>
          </div>
          
          <IonBadge 
            style={{
              backgroundColor: statusStyling.color,
              color: summary.evaluation_status === 'not_started' ? 'white' : 'black',
              padding: '6px 12px',
              borderRadius: '16px',
              fontSize: '14px',
              fontWeight: '600'
            }}
          >
            {eventEvaluationService.formatCompletionPercentage(summary.completion_percentage)}
          </IonBadge>
        </div>

        {/* Progress Bar */}
        {summary.attended_participants > 0 && (
          <div style={{ marginBottom: '20px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '8px'
            }}>
              <span style={{ color: '#d1d5db', fontSize: '14px' }}>
                Progress: {summary.evaluated_participants} of {summary.attended_participants} players
              </span>
              <span style={{ color: statusStyling.color, fontSize: '14px', fontWeight: '600' }}>
                {Math.round(completionProgress * 100)}%
              </span>
            </div>
            <IonProgressBar 
              value={completionProgress}
              style={{
                '--progress-background': statusStyling.color,
                '--background': 'rgba(75, 85, 99, 0.3)',
                height: '8px',
                borderRadius: '4px'
              }}
            />
          </div>
        )}

        {/* Stats Grid */}
        <IonGrid style={{ padding: '0' }}>
          <IonRow>
            <IonCol size="6">
              <div style={{ textAlign: 'center', padding: '16px 8px' }}>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  marginBottom: '8px'
                }}>
                  <IonIcon 
                    icon={peopleOutline} 
                    style={{ fontSize: '20px', color: SHOT_COLORS.purple, marginRight: '6px' }} 
                  />
                  <span style={{ color: 'white', fontSize: '24px', fontWeight: '700' }}>
                    {summary.attended_participants}
                  </span>
                </div>
                <p style={{ color: '#9ca3af', margin: 0, fontSize: '12px' }}>
                  Players Attended
                </p>
              </div>
            </IonCol>
            
            <IonCol size="6">
              <div style={{ textAlign: 'center', padding: '16px 8px' }}>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  marginBottom: '8px'
                }}>
                  <IonIcon 
                    icon={starOutline} 
                    style={{ fontSize: '20px', color: SHOT_COLORS.orange, marginRight: '6px' }} 
                  />
                  <span style={{ color: 'white', fontSize: '24px', fontWeight: '700' }}>
                    {summary.avg_overall_rating ? eventEvaluationService.formatRating(summary.avg_overall_rating) : 'N/A'}
                  </span>
                </div>
                <p style={{ color: '#9ca3af', margin: 0, fontSize: '12px' }}>
                  Average Rating
                </p>
              </div>
            </IonCol>
          </IonRow>
        </IonGrid>

        {/* Quick Stats Chips */}
        {summary.total_individual_evaluations > 0 && (
          <div style={{ 
            display: 'flex', 
            flexWrap: 'wrap', 
            gap: '8px',
            marginBottom: '20px',
            justifyContent: 'center'
          }}>
            <IonChip style={{ 
              '--background': 'rgba(139, 92, 246, 0.1)', 
              '--color': '#8b5cf6',
              fontSize: '12px'
            }}>
              <IonIcon icon={listOutline} />
              <span>{summary.total_individual_evaluations} total evaluations</span>
            </IonChip>
            
            {summary.last_evaluation_date && (
              <IonChip style={{ 
                '--background': 'rgba(16, 185, 129, 0.1)', 
                '--color': '#10b981',
                fontSize: '12px'
              }}>
                <IonIcon icon={timeOutline} />
                <span>Last: {new Date(summary.last_evaluation_date).toLocaleDateString()}</span>
              </IonChip>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <IonButton 
            expand="block"
            onClick={handleEvaluateAllPlayers}
            style={{
              '--background': SHOT_COLORS.orange,
              '--color': 'black',
              '--border-radius': '8px',
              height: '48px',
              fontWeight: '600'
            }}
          >
            <IonIcon slot="start" icon={analyticsOutline} />
            {summary.evaluation_status === 'not_started' ? 'Start Evaluations' : 'Continue Evaluations'}
          </IonButton>
          
          {summary.total_individual_evaluations > 0 && (
            <IonButton 
              expand="block"
              fill="outline"
              onClick={handleViewDetails}
              style={{
                '--border-color': SHOT_COLORS.purple,
                '--color': SHOT_COLORS.purple,
                '--border-radius': '8px',
                height: '40px'
              }}
            >
              <IonIcon slot="start" icon={trendingUpOutline} />
              View Detailed Results
            </IonButton>
          )}
        </div>

        {/* Time Estimate */}
        {summary.evaluation_status === 'in_progress' && summary.attended_participants > summary.evaluated_participants && (
          <div style={{
            marginTop: '16px',
            padding: '12px',
            backgroundColor: 'rgba(245, 158, 11, 0.1)',
            borderRadius: '8px',
            textAlign: 'center'
          }}>
            <p style={{ color: '#f59e0b', margin: 0, fontSize: '13px' }}>
              ⏱️ Estimated {eventEvaluationService.estimateTimeToComplete(
                summary.attended_participants, 
                summary.evaluated_participants
              )} minutes to complete remaining evaluations
            </p>
          </div>
        )}
      </IonCardContent>
    </IonCard>
  );
};

export default EvaluationSummaryCard;
