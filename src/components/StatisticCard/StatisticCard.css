/* StatisticCard Component Styling */
/* These styles are specific to StatisticCard functionality, 
   base card styling comes from ContentCard */

.stat-box.clickable {
  cursor: pointer;
}

.stat-box.clickable:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

/* Stats grid styling */
.stats-grid {
  display: grid;
  gap: 12px;
  margin-bottom: 16px;
}

.stats-grid.columns-2 {
  grid-template-columns: repeat(2, 1fr);
}

.stats-grid.columns-3 {
  grid-template-columns: repeat(3, 1fr);
}

.stat-box {
  text-align: center;
  padding: 16px 8px;
  background-color: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.stat-box:hover {
  background-color: rgba(255, 255, 255, 0.12);
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #f1f5f9;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #94a3b8;
}

/* Progress bar styling */
.attendance-stat {
  margin-top: 16px;
}

.attendance-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #f1f5f9;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .stats-grid.columns-3 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .stat-label {
    font-size: 12px;
  }
}