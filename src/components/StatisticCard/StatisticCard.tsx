import React, { ReactNode } from 'react';
import { IonIcon, IonProgressBar } from '@ionic/react';
import ContentCard from '../ContentCard/ContentCard';
import './StatisticCard.css';

interface StatItem {
  value: string | number;
  label: string;
  onClick?: () => void;
}

interface StatisticCardProps {
  title: string;
  icon?: string;
  items: StatItem[];
  columns?: 2 | 3;
  progressBar?: {
    value: number;
    label: string;
    color?: string;
  };
  className?: string;
  headerAction?: ReactNode;
  actions?: ReactNode;
  fullWidthActions?: boolean;
}

const StatisticCard: React.FC<StatisticCardProps> = ({
  title,
  icon,
  items = [],
  columns = 3,
  progressBar,
  className = '',
  headerAction,
  actions,
  fullWidthActions = false
}) => {
  return (
    <ContentCard
      title={title}
      icon={icon}
      className={`statistic-card ${className}`}
      headerAction={headerAction}
      actions={actions}
      fullWidthActions={fullWidthActions}
    >
      <div className={`stats-grid columns-${columns}`}>
        {items.map((item, index) => (
          <div key={index} className={`stat-box ${item.onClick ? 'clickable' : ''}`} onClick={item.onClick}>
            <div className="stat-value">{item.value}</div>
            <div className="stat-label">{item.label}</div>
          </div>
        ))}
      </div>
      
      {progressBar && (
        <div className="attendance-stat">
          <div className="attendance-label">
            {progressBar.label}
          </div>
          <IonProgressBar
            value={progressBar.value}
            color={progressBar.color || 'success'}
          />
        </div>
      )}
    </ContentCard>
  );
};

export default StatisticCard;