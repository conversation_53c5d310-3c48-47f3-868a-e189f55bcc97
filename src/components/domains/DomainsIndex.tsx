import React from 'react';
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonCard, IonCardContent, IonIcon, IonBackButton, IonButtons } from '@ionic/react';
import { useHistory } from 'react-router-dom';
import { 
  personCircle, 
  people, 
  shieldCheckmark, 
  chatbubbles, 
  analytics,
  checkmarkCircle 
} from 'ionicons/icons';

interface DomainInfo {
  path: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  features: string[];
}

const domains: DomainInfo[] = [
  {
    path: 'identity-profile',
    title: 'Identity & Profile',
    description: 'Central user management, SportHead progression, and SHOT ID system.',
    icon: personCircle,
    color: 'var(--shot-purple)',
    features: ['SportHead XP & Levels', 'Profile Management', 'Privacy Controls']
  },
  {
    path: 'organization',
    title: 'Organization',
    description: 'Club and team management with roster tracking and team statistics.',
    icon: people,
    color: 'var(--shot-teal)',
    features: ['Team Roster', 'Invite System', 'Player Status']
  },
  {
    path: 'people-roles',
    title: 'People & Roles',
    description: 'Role-based access control, permissions, and team hierarchy management.',
    icon: shieldCheckmark,
    color: 'var(--shot-gold)',
    features: ['Role Assignment', 'Permission Matrix', 'Approval Workflow']
  },
  {
    path: 'communication',
    title: 'Communication',
    description: 'Centralized messaging, notifications, and team communication channels.',
    icon: chatbubbles,
    color: 'var(--shot-burnt-orange)',
    features: ['Multi-channel Messaging', 'Message Templates', 'Offline Queue']
  },
  {
    path: 'evaluation',
    title: 'Evaluation',
    description: 'Player assessments with multi-framework support and offline capabilities.',
    icon: analytics,
    color: 'var(--shot-forest-green)',
    features: ['Multi-framework Support', 'Offline Evaluations', 'Team Insights']
  }
];

const DomainsIndex: React.FC = () => {
  const history = useHistory();

  const navigateToDomain = (path: string) => {
    history.push(`/domains/${path}`);
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar style={{ '--background': 'var(--shot-black)' }}>
          <IonButtons slot="start">
            <IonBackButton defaultHref="/home" />
          </IonButtons>
          <IonTitle>SHOT V3 Domain Mockups</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent fullscreen style={{ '--background': 'var(--shot-black)' }}>
        <div style={{
          background: 'linear-gradient(135deg, rgba(107, 0, 219, 0.2) 0%, rgba(26, 188, 156, 0.1) 100%)',
          padding: '40px 24px',
          textAlign: 'center',
          borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
        }}>
          <h1 className="shot-h1" style={{ color: 'var(--shot-white)' }}>SHOT V3 DOMAIN MOCKUPS</h1>
          <p style={{ color: 'rgba(255, 255, 255, 0.8)', maxWidth: '600px', margin: '16px auto' }}>
            Domain-Driven Design implementation mockups showcasing the new architecture 
            with offline-first capabilities and improved user experience.
          </p>
          <div style={{ display: 'flex', justifyContent: 'center', gap: '12px', marginTop: '24px' }}>
            <span className="shot-badge shot-badge-teal">Offline Ready</span>
            <span className="shot-badge shot-badge-purple">DDD Architecture</span>
            <span className="shot-badge shot-badge-gold">SHOT Brand</span>
          </div>
        </div>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '24px',
          padding: '24px'
        }}>
          {domains.map((domain) => (
            <IonCard
              key={domain.path}
              button
              onClick={() => navigateToDomain(domain.path)}
              style={{
                '--background': 'var(--shot-black)',
                border: '2px solid rgba(255, 255, 255, 0.1)',
                borderRadius: 'var(--shot-button-radius)',
                transition: 'all 0.3s ease',
                cursor: 'pointer',
                margin: 0
              }}
              onMouseEnter={(e) => {
                const card = e.currentTarget;
                card.style.transform = 'translateY(-4px)';
                card.style.borderColor = domain.color;
                card.style.boxShadow = `0 8px 24px ${domain.color}33`;
              }}
              onMouseLeave={(e) => {
                const card = e.currentTarget;
                card.style.transform = 'translateY(0)';
                card.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                card.style.boxShadow = 'none';
              }}
            >
              <IonCardContent>
                <div style={{
                  width: '64px',
                  height: '64px',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '32px',
                  marginBottom: '16px',
                  background: `${domain.color}33`
                }}>
                  <IonIcon icon={domain.icon} style={{ color: domain.color }} />
                </div>
                <h2 style={{
                  fontFamily: 'var(--shot-font-heading)',
                  fontSize: '20px',
                  fontWeight: 700,
                  color: 'var(--shot-white)',
                  marginBottom: '8px'
                }}>
                  {domain.title}
                </h2>
                <p style={{
                  fontSize: '14px',
                  color: 'rgba(255, 255, 255, 0.7)',
                  lineHeight: 1.4,
                  marginBottom: '16px'
                }}>
                  {domain.description}
                </p>
                <div style={{
                  marginTop: '16px',
                  paddingTop: '16px',
                  borderTop: '1px solid rgba(255, 255, 255, 0.1)'
                }}>
                  {domain.features.map((feature, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      marginBottom: '8px',
                      fontSize: '12px',
                      color: 'rgba(255, 255, 255, 0.6)'
                    }}>
                      <IonIcon icon={checkmarkCircle} style={{ color: 'var(--shot-teal)', fontSize: '16px' }} />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
              </IonCardContent>
            </IonCard>
          ))}
        </div>

        <div style={{ textAlign: 'center', padding: '40px 24px', color: 'rgba(255, 255, 255, 0.6)' }}>
          <p>These mockups demonstrate the new domain-driven architecture for SHOT V3.</p>
          <p>Each domain is designed to be independently deployable with offline-first capabilities.</p>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default DomainsIndex;
