import React from 'react';
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonBackButton, IonButtons } from '@ionic/react';

interface DomainMockupPageProps {
  title: string;
  htmlPath: string;
}

const DomainMockupPage: React.FC<DomainMockupPageProps> = ({ title, htmlPath }) => {
  return (
    <IonPage>
      <IonHeader>
        <IonToolbar style={{ '--background': 'var(--shot-black)' }}>
          <IonButtons slot="start">
            <IonBackButton defaultHref="/domains" />
          </IonButtons>
          <IonTitle>{title}</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent fullscreen style={{ '--background': 'var(--shot-black)' }}>
        <iframe
          src={htmlPath}
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            background: 'var(--shot-black)'
          }}
          title={title}
        />
      </IonContent>
    </IonPage>
  );
};

// Individual domain mockup components
export const IdentityProfileMockup: React.FC = () => (
  <DomainMockupPage 
      title="Identity & Profile Domain" 
      htmlPath="/domains/identity-profile/profile-management.html" 
    />
);

export const OrganizationMockup: React.FC = () => (
  <DomainMockupPage 
    title="Organization Domain" 
    htmlPath="/domains/organization/team-management.html" 
  />
);

export const PeopleRolesMockup: React.FC = () => (
  <DomainMockupPage 
    title="People & Roles Domain" 
    htmlPath="/domains/people-roles/test.html" 
  />
);

export const CommunicationMockup: React.FC = () => (
  <DomainMockupPage 
    title="Communication Domain" 
    htmlPath="/domains/communication/communication-hub.html" 
  />
);

export const EvaluationMockup: React.FC = () => (
  <DomainMockupPage 
    title="Evaluation Domain" 
    htmlPath="/domains/evaluation/test.html" 
  />
);

export default DomainMockupPage;
