import React from 'react';

interface RegistrationLayoutProps {
  currentStep: number;
  totalSteps: number;
  title: string;
  subtitle?: string;
  showBackToLogin?: boolean;
  onBackToLogin?: () => void;
  children: React.ReactNode;
}

const RegistrationLayout: React.FC<RegistrationLayoutProps> = ({
  currentStep,
  totalSteps,
  title,
  subtitle,
  showBackToLogin = false,
  onBackToLogin,
  children
}) => {
  return (
    <div className="min-h-screen shot-dashboard">
      <div className="max-w-md mx-auto px-4 py-8">
        <h1 className="shot-dashboard-title">{title}</h1>
        
        {/* Progress indicator */}
        <div className="mb-8">
          <div className="flex justify-between mb-2">
            {Array.from({ length: totalSteps }, (_, i) => i + 1).map((step) => (
              <div
                key={step}
                className={`flex-1 h-1 shot-rounded mx-1 transition-colors ${
                  step <= currentStep ? 'bg-[color:var(--shot-teal)]' : 'bg-gray-700'
                }`}
              />
            ))}
          </div>
          <p className="text-center shot-caption">
            Step {currentStep} of {totalSteps}
          </p>
        </div>
        
        {/* Back to login button */}
        {showBackToLogin && onBackToLogin && (
          <div className="mb-6">
            <button 
              className="shot-body text-gray-400 hover:text-white transition-colors"
              onClick={onBackToLogin}
            >
              ← Back to Login
            </button>
          </div>
        )}
        
        {/* Step content */}
        <div className="space-y-6">
          {subtitle && (
            <div className="text-center">
              <h2 className="shot-h2">{subtitle}</h2>
            </div>
          )}
          {children}
        </div>
      </div>
    </div>
  );
};

export default RegistrationLayout;