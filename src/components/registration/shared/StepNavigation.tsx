import React from 'react';
import { ShadowButton } from '../../shadow/ShadowButton';

interface StepNavigationProps {
  onBack?: () => void;
  onNext?: () => void;
  onSubmit?: () => void;
  backLabel?: string;
  nextLabel?: string;
  submitLabel?: string;
  isLoading?: boolean;
  isNextDisabled?: boolean;
  isSubmitDisabled?: boolean;
  showBack?: boolean;
  showNext?: boolean;
  showSubmit?: boolean;
}

const StepNavigation: React.FC<StepNavigationProps> = ({
  onBack,
  onNext,
  onSubmit,
  backLabel = 'Back',
  nextLabel = 'Continue',
  submitLabel = 'Complete Registration',
  isLoading = false,
  isNextDisabled = false,
  isSubmitDisabled = false,
  showBack = true,
  showNext = false,
  showSubmit = false
}) => {
  return (
    <div className="flex gap-4 pt-4">
      {/* Back Button */}
      {showBack && onBack && (
        <ShadowButton
          text={backLabel}
          variant="outline"
          size="medium"
          onClick={onBack}
          disabled={isLoading}
          className="flex-1"
        />
      )}
      
      {/* Next Button */}
      {showNext && onNext && (
        <ShadowButton
          text={isLoading ? 'Loading...' : nextLabel}
          variant="primary"
          size="medium"
          onClick={onNext}
          disabled={isNextDisabled || isLoading}
          className="flex-1"
        />
      )}
      
      {/* Submit Button */}
      {showSubmit && onSubmit && (
        <ShadowButton
          text={isLoading ? 'Creating Account...' : submitLabel}
          variant="secondary"
          size="medium"
          onClick={onSubmit}
          disabled={isSubmitDisabled || isLoading}
          className="flex-1"
        />
      )}
    </div>
  );
};

export default StepNavigation;