import React from 'react';

interface AvatarModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (avatarPath: string) => void;
  selectedAvatar: string;
  avatarOptions: string[];
}

const AvatarModal: React.FC<AvatarModalProps> = ({
  isOpen,
  onClose,
  onSelect,
  selectedAvatar,
  avatarOptions
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
      <div className="shot-card max-w-md w-full max-h-[80vh] overflow-y-auto">
        <div className="shot-card-header">
          <div className="flex justify-between items-center">
            <h2 className="shot-card-title">Choose Your Sport Head</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
        
        <div className="shot-card-content">
          <div className="grid grid-cols-3 gap-4">
            {avatarOptions.map((avatar, index) => (
              <button
                key={index}
                onClick={() => onSelect(avatar)}
                className={`border-2 shot-rounded p-2 transition-all ${
                  selectedAvatar === avatar
                    ? 'border-[color:var(--shot-teal)] bg-[color:var(--shot-teal)]/10'
                    : 'border-gray-700 hover:border-gray-500'
                }`}
              >
                <img
                  src={avatar}
                  alt={`Avatar option ${index + 1}`}
                  className="w-full shot-rounded"
                />
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AvatarModal;