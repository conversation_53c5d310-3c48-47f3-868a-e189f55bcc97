import React from 'react';

interface ErrorDisplayProps {
  message: string;
  className?: string;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ message, className = '' }) => {
  if (!message) return null;

  return (
    <div className={`bg-[color:var(--shot-warm-coral)]/20 border border-[color:var(--shot-warm-coral)]/50 rounded-lg p-3 text-[color:var(--shot-warm-coral)] text-sm shot-body ${className}`}>
      {message}
    </div>
  );
};

export default ErrorDisplay;