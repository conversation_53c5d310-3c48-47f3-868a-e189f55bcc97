import React from 'react';
import { ShadowButton } from '../../shadow/ShadowButton';

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  isInvitedFlow: boolean;
  teamInfo?: {
    name: string;
    club?: { name: string };
  };
}

const SuccessModal: React.FC<SuccessModalProps> = ({
  isOpen,
  onClose,
  isInvitedFlow,
  teamInfo,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 z-50 flex items-center justify-center p-4">
      <div className="shot-card max-w-sm w-full">
        <div className="shot-card-content text-center">
          <div className="w-16 h-16 bg-[color:var(--shot-teal)]/20 shot-rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-[color:var(--shot-teal)]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h3 className="shot-h3 mb-2">Account Created!</h3>
          <p className="shot-body text-gray-400 mb-6">
            {isInvitedFlow && teamInfo
              ? `Welcome to SHOT! You're joining ${teamInfo.name}. Please check your email to verify your account.`
              : 'Welcome to SHOT! Please check your email to verify your account, then explore the app or head to the Perform tab to get started.'}
          </p>

          <ShadowButton
            text="Got it"
            variant="secondary"
            size="medium"
            onClick={onClose}
            className="flex-1"
          />
        </div>
      </div>
    </div>
  );
};

export default SuccessModal;
