import React, { useState } from 'react';
import ShadowTextInput, { ShadowInputChangeEvent } from '../../shadow/form/ShadowTextInput';
import StepNavigation from '../shared/StepNavigation';
import ErrorDisplay from '../shared/ErrorDisplay';

interface AccountData {
  email: string;
  password: string;
  confirmPassword: string;
  agreedToTerms: boolean;
}

interface AccountCreationStepProps {
  accountData: AccountData;
  onAccountDataChange: (data: Partial<AccountData>) => void;
  onNext: () => void;
  onBack: () => void;
}

const AccountCreationStep: React.FC<AccountCreationStepProps> = ({
  accountData,
  onAccountDataChange,
  onNext,
  onBack
}) => {
  const [errorMessage, setErrorMessage] = useState('');

  const handleInputChange = (field: keyof AccountData) => (e: ShadowInputChangeEvent) => {
    onAccountDataChange({ [field]: e.detail.value });
    setErrorMessage('');
  };

  const handleTermsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onAccountDataChange({ agreedToTerms: e.target.checked });
    setErrorMessage('');
  };

  const validateAndNext = () => {
    // Account validation
    if (!accountData.email || !accountData.email.includes('@')) {
      setErrorMessage('Please enter a valid email address');
      return;
    }
    if (accountData.password.length < 6) {
      setErrorMessage('Password must be at least 6 characters');
      return;
    }
    if (accountData.password !== accountData.confirmPassword) {
      setErrorMessage('Passwords do not match');
      return;
    }
    if (!accountData.agreedToTerms) {
      setErrorMessage('Please agree to the Terms of Service');
      return;
    }

    setErrorMessage('');
    onNext();
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <p className="shot-body text-gray-400 mt-2">
          Join SHOT and start your sports journey
        </p>
      </div>
      
      <div className="space-y-4">
        <ShadowTextInput
          type="email"
          name="email"
          value={accountData.email}
          label="Email Address"
          placeholder="<EMAIL>"
          icon="mail"
          required
          onChange={handleInputChange('email')}
          size="medium"
        />
        
        <ShadowTextInput
          type="password"
          name="password"
          value={accountData.password}
          label="Password"
          placeholder="Create a password"
          icon="lock"
          required
          onChange={handleInputChange('password')}
          size="medium"
          showPasswordToggle={true}
        />
        
        <ShadowTextInput
          type="password"
          name="confirmPassword"
          value={accountData.confirmPassword}
          label="Confirm Password"
          placeholder="Confirm your password"
          icon="lock"
          required
          onChange={handleInputChange('confirmPassword')}
          size="medium"
          showPasswordToggle={true}
        />
        
        <div className="space-y-3 mt-6">
          <label className="flex items-center shot-body">
            <input
              type="checkbox"
              checked={accountData.agreedToTerms}
              onChange={handleTermsChange}
              className="mr-3 w-4 h-4 text-[color:var(--shot-teal)] bg-gray-800 border-gray-600 rounded focus:ring-[color:var(--shot-teal)] focus:ring-2"
            />
            <span className="text-sm text-gray-300">
              I agree to the Terms of Service and Privacy Policy
            </span>
          </label>
        </div>
        
        <ErrorDisplay message={errorMessage} />
        
        <StepNavigation
          onBack={onBack}
          onNext={validateAndNext}
          backLabel="Back"
          nextLabel="Continue"
          showBack={true}
          showNext={true}
        />
      </div>
    </div>
  );
};

export default AccountCreationStep;