import React, { useState } from 'react';
import StepNavigation from '../shared/StepNavigation';
import ErrorDisplay from '../shared/ErrorDisplay';
import AvatarModal from '../shared/AvatarModal';

interface AvatarSelectionStepProps {
  selectedAvatar: string;
  onAvatarChange: (avatar: string) => void;
  onSubmit: () => void;
  onBack: () => void;
  isSubmitting: boolean;
  avatarOptions: string[];
  errorMessage?: string;
}

const AvatarSelectionStep: React.FC<AvatarSelectionStepProps> = ({
  selectedAvatar,
  onAvatarChange,
  onSubmit,
  onBack,
  isSubmitting,
  avatarOptions,
  errorMessage = ''
}) => {
  const [showAvatarModal, setShowAvatarModal] = useState(false);

  const handleAvatarSelect = (avatarPath: string) => {
    onAvatarChange(avatarPath);
    setShowAvatarModal(false);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <p className="shot-body text-gray-400 mt-2">
          Select an avatar that represents you
        </p>
      </div>
      
      <div className="text-center space-y-6">
        <div className="inline-block">
          <img 
            src={selectedAvatar} 
            alt="Selected Avatar"
            className="w-32 h-32 shot-rounded border-2 border-[color:var(--shot-teal)] object-cover cursor-pointer hover:opacity-80 transition-opacity"
            onClick={() => setShowAvatarModal(true)}
          />
          <p className="shot-caption mt-2">Click to change</p>
        </div>
        
        <ErrorDisplay message={errorMessage} />
        
        <StepNavigation
          onBack={onBack}
          onSubmit={onSubmit}
          backLabel="Back"
          submitLabel={isSubmitting ? 'Creating Account...' : 'Complete Registration'}
          isLoading={isSubmitting}
          isSubmitDisabled={isSubmitting}
          showBack={true}
          showSubmit={true}
        />
      </div>

      <AvatarModal
        isOpen={showAvatarModal}
        onClose={() => setShowAvatarModal(false)}
        onSelect={handleAvatarSelect}
        selectedAvatar={selectedAvatar}
        avatarOptions={avatarOptions}
      />
    </div>
  );
};

export default AvatarSelectionStep;