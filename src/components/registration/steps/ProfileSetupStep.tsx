import React, { useState } from 'react';
import ShadowTextInput, { ShadowInputChangeEvent } from '../../shadow/form/ShadowTextInput';
import StepNavigation from '../shared/StepNavigation';
import ErrorDisplay from '../shared/ErrorDisplay';

interface ProfileData {
  fullName: string;
  nickname: string;
  phoneNumber: string;
  dateOfBirth: string;
  userLocation: string;
  marketingEmail: boolean;
  marketingNotifications: boolean;
}

interface ProfileSetupStepProps {
  profileData: ProfileData;
  onProfileDataChange: (data: Partial<ProfileData>) => void;
  onNext: () => void;
  onBack: () => void;
}

const ProfileSetupStep: React.FC<ProfileSetupStepProps> = ({
  profileData,
  onProfileDataChange,
  onNext,
  onBack
}) => {
  const [errorMessage, setErrorMessage] = useState('');

  const handleInputChange = (field: keyof ProfileData) => (e: ShadowInputChangeEvent) => {
    onProfileDataChange({ [field]: e.detail.value });
    setErrorMessage('');
  };

  const handleCheckboxChange = (field: keyof ProfileData) => (e: React.ChangeEvent<HTMLInputElement>) => {
    onProfileDataChange({ [field]: e.target.checked });
  };

  const validateAndNext = () => {
    // Profile validation
    if (!profileData.fullName.trim()) {
      setErrorMessage('Please enter your full name');
      return;
    }
    if (!profileData.dateOfBirth) {
      setErrorMessage('Please enter your date of birth');
      return;
    }
    
    // Validate age (optional minimum age check)
    const birthDate = new Date(profileData.dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    if (age < 13) {
      setErrorMessage('You must be at least 13 years old to register');
      return;
    }

    setErrorMessage('');
    onNext();
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <p className="shot-body text-gray-400 mt-2">
          Tell us a bit about yourself
        </p>
      </div>
      
      <div className="space-y-4">
        <ShadowTextInput
          type="text"
          name="fullName"
          value={profileData.fullName}
          label="Full Name"
          placeholder="Enter your full name"
          icon="user"
          required
          onChange={handleInputChange('fullName')}
          size="medium"
        />
        
        <ShadowTextInput
          type="text"
          name="nickname"
          value={profileData.nickname}
          label="Nickname (Optional)"
          placeholder="What should we call you?"
          icon="edit"
          onChange={handleInputChange('nickname')}
          size="medium"
        />
        
        <ShadowTextInput
          type="date"
          name="dateOfBirth"
          value={profileData.dateOfBirth}
          label="Date of Birth"
          icon="calendar"
          required
          onChange={handleInputChange('dateOfBirth')}
          size="medium"
        />
        
        <ShadowTextInput
          type="tel"
          name="phoneNumber"
          value={profileData.phoneNumber}
          label="Phone Number (Optional)"
          placeholder="****** 567 8900"
          icon="phone"
          onChange={handleInputChange('phoneNumber')}
          size="medium"
        />
        
        <ShadowTextInput
          type="text"
          name="userLocation"
          value={profileData.userLocation}
          label="Location (Optional)"
          placeholder="City, Country"
          icon="globe"
          onChange={handleInputChange('userLocation')}
          size="medium"
        />
        
        <div className="space-y-3 mt-6">
          <h3 className="shot-h3 text-sm">Marketing Preferences</h3>
          <label className="flex items-center shot-body">
            <input
              type="checkbox"
              checked={profileData.marketingEmail}
              onChange={handleCheckboxChange('marketingEmail')}
              className="mr-3 w-4 h-4 text-[color:var(--shot-teal)] bg-gray-800 border-gray-600 rounded focus:ring-[color:var(--shot-teal)] focus:ring-2"
            />
            <span className="text-sm text-gray-300">Receive email updates about SHOT</span>
          </label>
          <label className="flex items-center shot-body">
            <input
              type="checkbox"
              checked={profileData.marketingNotifications}
              onChange={handleCheckboxChange('marketingNotifications')}
              className="mr-3 w-4 h-4 text-[color:var(--shot-teal)] bg-gray-800 border-gray-600 rounded focus:ring-[color:var(--shot-teal)] focus:ring-2"
            />
            <span className="text-sm text-gray-300">Receive push notifications</span>
          </label>
        </div>
        
        <ErrorDisplay message={errorMessage} />
        
        <StepNavigation
          onBack={onBack}
          onNext={validateAndNext}
          backLabel="Back"
          nextLabel="Continue"
          showBack={true}
          showNext={true}
        />
      </div>
    </div>
  );
};

export default ProfileSetupStep;