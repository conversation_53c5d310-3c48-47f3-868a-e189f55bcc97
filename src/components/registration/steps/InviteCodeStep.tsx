import React, { useState } from 'react';
import ShadowTextInput from '../../shadow/form/ShadowTextInput';
import StepNavigation from '../shared/StepNavigation';
import ErrorDisplay from '../shared/ErrorDisplay';

interface TeamInfo {
  team_id: string;
  name: string;
  club?: { name: string };
}

interface InviteCodeStepProps {
  inviteCode: string;
  onInviteCodeChange: (code: string) => void;
  onNext: (teamInfo: TeamInfo) => void;
  onBack: () => void;
  validateInviteCode: (code: string) => Promise<TeamInfo | null>;
}

const InviteCodeStep: React.FC<InviteCodeStepProps> = ({
  inviteCode,
  onInviteCodeChange,
  onNext,
  onBack,
  validateInviteCode
}) => {
  const [isValidating, setIsValidating] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [teamInfo, setTeamInfo] = useState<TeamInfo | null>(null);

  const handleCodeChange = (e: any) => {
    const code = e.detail.value.toUpperCase();
    onInviteCodeChange(code);
    setErrorMessage('');
    setTeamInfo(null);
  };

  const handleNext = async () => {
    if (!inviteCode.trim()) {
      setErrorMessage('Please enter an invite code');
      return;
    }

    setIsValidating(true);
    setErrorMessage('');

    try {
      const validatedTeamInfo = await validateInviteCode(inviteCode);
      if (validatedTeamInfo) {
        setTeamInfo(validatedTeamInfo);
        onNext(validatedTeamInfo);
      }
    } catch (error: any) {
      setErrorMessage(error.message || 'Invalid invite code. Please check and try again.');
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <p className="shot-body text-gray-400 mt-2">
          Enter the invite code shared by your coach to join your team
        </p>
      </div>
      
      <div className="space-y-4">
        <ShadowTextInput
          type="text"
          name="inviteCode"
          value={inviteCode}
          label="Invite Code"
          placeholder="Enter invite code"
          required
          onChange={handleCodeChange}
          size="large"
          className="text-center text-lg font-mono tracking-wider"
        />
        
        {teamInfo && (
          <div className="shot-card shot-card-content">
            <p className="shot-caption">You'll be joining:</p>
            <p className="shot-body font-semibold">{teamInfo.name}</p>
            {teamInfo.club && (
              <p className="shot-caption">{teamInfo.club.name}</p>
            )}
          </div>
        )}
        
        <ErrorDisplay message={errorMessage} />
        
        <StepNavigation
          onBack={onBack}
          onNext={handleNext}
          backLabel="Back"
          nextLabel={isValidating ? 'Verifying...' : 'Verify Code'}
          isLoading={isValidating}
          isNextDisabled={!inviteCode.trim()}
          showBack={true}
          showNext={true}
        />
      </div>
    </div>
  );
};

export default InviteCodeStep;