# Registration Components

This directory contains the modular registration components for the SHOT application.

## Structure

### Shared Components (`/shared`)
- **RegistrationLayout.tsx** - Common layout with progress indicator and navigation
- **StepNavigation.tsx** - Reusable back/next/submit buttons with SHOT styling
- **ErrorDisplay.tsx** - Standardized error message component
- **AvatarModal.tsx** - Reusable avatar selection modal
- **SuccessModal.tsx** - Registration success confirmation modal

### Step Components (`/steps`)
- **InviteCodeStep.tsx** - Invite code validation with team info display
- **AccountCreationStep.tsx** - Email, password, and terms agreement
- **ProfileSetupStep.tsx** - Personal information and preferences
- **AvatarSelectionStep.tsx** - Avatar picker and final registration

## Usage

```typescript
import {
  RegistrationLayout,
  InviteCodeStep,
  AccountCreationStep,
  ProfileSetupStep,
  AvatarSelectionStep
} from '../../components/registration';
```

## Benefits

1. **Single Responsibility** - Each component has one clear purpose
2. **Reusability** - Step components can be reused in other flows
3. **Maintainability** - Easier to modify individual steps
4. **Testing** - Each component can be tested independently
5. **SHOT Brand Compliance** - All components use SHOT design system
6. **Consistent Styling** - Uses refactored ShadowTextInput with Tailwind + SHOT CSS variables

## Component Props

Each step component follows a consistent pattern:
- Receives data as props
- Has onChange handlers for data updates
- Has onNext/onBack navigation handlers
- Handles its own validation and error states
- Uses SHOT brand styling throughout

This modular approach makes the registration flow much more maintainable and follows React best practices.