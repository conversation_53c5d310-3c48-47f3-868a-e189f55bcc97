# AddEntityModal Component

A reusable modal component for adding various entities (Teams, Coaches, Clubs, Events, etc.) throughout the SHOT application.

## Features

- Consistent UX pattern across all "Add Entity" flows
- Built-in loading states and error handling
- Validation support
- Customizable header and description
- Responsive design
- Optional full-height mode for complex forms
- Automatic form reset after successful save

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `isOpen` | `boolean` | Yes | Controls modal visibility |
| `onDismiss` | `() => void` | Yes | Callback when modal is dismissed |
| `onSave` | `(data: any) => Promise<void>` | Yes | Async callback for saving entity |
| `title` | `string` | Yes | Modal title |
| `description` | `string` | No | Optional description text |
| `ctaText` | `string` | Yes | Text for the save button |
| `children` | `ReactNode` | Yes | Form content |
| `resetOnSave` | `boolean` | No | Reset form after save (default: true) |
| `validate` | `() => boolean \| string` | No | Validation function |
| `showCompactHeader` | `boolean` | No | Use compact header style |
| `fullHeight` | `boolean` | No | Enable full-height mode |

## Usage Examples

### Basic Usage (Add Team Coach)

```tsx
import AddEntityModal from '@/components/AddEntity';
import AddCoachForm from './components/AddCoachForm';

const AddTeamCoachModal = ({ isOpen, onDismiss, onSave, teamId }) => {
  const [formData, setFormData] = useState({
    searchQuery: '',
    selectedUserId: '',
    role: 'assistant_coach',
    isPrimary: false
  });

  const handleSave = async () => {
    const input = {
      team_id: teamId,
      user_id: formData.selectedUserId,
      role: formData.role,
      is_primary: formData.isPrimary
    };
    await CoachService.createTeamCoach(input);
    onSave();
  };

  const validate = () => {
    if (!formData.selectedUserId) {
      return 'Please select a user to assign as coach';
    }
    return true;
  };

  return (
    <AddEntityModal
      isOpen={isOpen}
      onDismiss={onDismiss}
      onSave={handleSave}
      title="Add Team Coach"
      description="Assign a new coach to this team."
      ctaText="Add Coach"
      validate={validate}
    >
      <AddCoachForm
        value={formData}
        onChange={updates => setFormData(prev => ({ ...prev, ...updates }))}
      />
    </AddEntityModal>
  );
};
```

### Complex Form (Add Event)

```tsx
const AddEventModal = ({ isOpen, onDismiss, onSave, teamId }) => {
  // ... form state setup

  return (
    <AddEntityModal
      isOpen={isOpen}
      onDismiss={onDismiss}
      onSave={handleSave}
      title="Create Event"
      description="Schedule a new event for your team."
      ctaText="Create Event"
      validate={validate}
      fullHeight={true} // Enable for complex forms
    >
      {/* Complex form with date pickers, etc. */}
    </AddEntityModal>
  );
};
```

## Styling

The component uses CSS modules located in `styles.css`. Key classes include:

- `.add-entity-modal` - Main modal container
- `.entity-description` - Description area styling
- `.error-message` - Error display styling
- `.entity-form-content` - Form content wrapper
- `.entity-footer` - Footer with action buttons

## Best Practices

1. **Validation**: Always provide a validation function for better UX
2. **Error Messages**: Return descriptive error messages from validation
3. **Loading States**: The component handles loading automatically
4. **Form Reset**: Default behavior resets form on save, disable if needed
5. **Responsive**: Works well on both mobile and desktop screens

## Accessibility

- Proper focus management
- Keyboard navigation support
- ARIA labels for screen readers
- Semantic HTML structure

## Creating New Entity Forms

1. Create a new form component in the appropriate directory
2. Implement form state management in the parent component
3. Use AddEntityModal as wrapper with appropriate props
4. Handle entity-specific logic in the onSave callback

Example directory structure:
```
components/
  AddTeamForm/
    index.tsx
  AddEventForm/
    index.tsx
  AddPlayerForm/
    index.tsx
```

## Related Components

- `CoachCard` - Display coach information
- `FormStyles` - Shared form styling components
- `PageWithNavigation` - Page wrapper component

## Changelog

- v1.0.0 - Initial implementation
- v1.1.0 - Added fullHeight prop for complex forms
- v1.2.0 - Improved validation and error handling
