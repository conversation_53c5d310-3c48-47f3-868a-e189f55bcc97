.add-entity-modal {
  --width: 90%;
  --max-width: 600px;
  --border-radius: 16px;
  --box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.add-entity-modal.full-height {
  --height: 90%;
  --max-height: 900px;
}

.add-entity-modal ion-modal {
  border-radius: var(--border-radius);
}

.add-entity-modal .compact-header {
  --min-height: 44px;
}

.add-entity-modal .compact-header ion-toolbar {
  --min-height: 44px;
  --padding-top: 0;
  --padding-bottom: 0;
}

.add-entity-modal .compact-header ion-title {
  font-size: 18px;
  font-weight: 600;
}

.add-entity-content {
  --background: var(--ion-background-color);
}

.entity-description {
  padding: 16px;
  background: var(--ion-color-light);
  border-bottom: 1px solid var(--ion-color-light-shade);
}

.entity-description p {
  margin: 0;
  font-size: 14px;
  color: var(--ion-color-medium-shade);
  line-height: 1.5;
}

.error-message {
  padding: 12px 16px;
  background: var(--ion-color-danger-tint);
  border-left: 4px solid var(--ion-color-danger);
  margin: 16px;
  border-radius: 8px;
}

.error-message p {
  margin: 0;
  font-size: 14px;
}

.entity-form-content {
  padding: 16px;
}

.entity-footer {
  border-top: 1px solid var(--ion-color-light-shade);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.entity-footer ion-toolbar {
  --padding-top: 12px;
  --padding-bottom: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
}

.footer-buttons {
  display: flex;
  gap: 12px;
  width: 100%;
}

.save-button {
  --border-radius: 12px;
  --box-shadow: none;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* Form styling within the modal */
.entity-form-content ion-list {
  background: transparent;
  padding: 0;
}

.entity-form-content ion-item {
  --background: var(--ion-color-light);
  --padding-start: 16px;
  --padding-end: 16px;
  --border-radius: 12px;
  margin-bottom: 12px;
  --border-width: 0;
  --inner-border-width: 0;
}

.entity-form-content ion-item:last-child {
  margin-bottom: 0;
}

.entity-form-content ion-label {
  font-weight: 500;
  font-size: 14px;
  color: var(--ion-color-dark);
  margin-bottom: 6px !important;
}

.entity-form-content ion-input,
.entity-form-content ion-textarea,
.entity-form-content ion-select {
  font-size: 16px;
  --padding-top: 8px;
  --padding-bottom: 8px;
}

.entity-form-content ion-toggle {
  --handle-height: 24px;
  --handle-width: 24px;
  --handle-spacing: 3px;
}

/* Search results list */
.search-results-list {
  margin-top: 8px;
  border: 1px solid var(--ion-color-light-shade);
  border-radius: 12px;
  overflow: hidden;
}

.search-results-list ion-item {
  margin-bottom: 0;
  --border-radius: 0;
  --background: var(--ion-card-background);
}

.search-results-list ion-item:not(:last-child) {
  border-bottom: 1px solid var(--ion-color-light-shade);
}

/* Card styling within modal */
.entity-form-content ion-card {
  margin: 16px 0;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.entity-form-content ion-card-header {
  padding-bottom: 8px;
}

.entity-form-content ion-card-title {
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.entity-form-content ion-card-content {
  padding-top: 0;
}

.entity-form-content ion-card h3 {
  font-size: 15px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--ion-color-dark);
}

.entity-form-content ion-card p {
  font-size: 14px;
  line-height: 1.5;
  color: var(--ion-color-medium-shade);
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 576px) {
  .add-entity-modal {
    --width: 100%;
    --height: 100%;
    --border-radius: 0;
  }
  
  .add-entity-modal.full-height {
    --height: 100%;
  }
}
