import React, { useState } from 'react';
import {
  IonModal,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButtons,
  IonButton,
  IonContent,
  IonFooter,
  IonIcon,
  IonText
} from '@ionic/react';
import { close as closeIcon } from 'ionicons/icons';
import './styles.css';

interface AddEntityModalProps {
  isOpen: boolean;
  onDismiss: () => void;
  onSave: (data: any) => Promise<void>;
  title: string;
  description?: string;
  ctaText: string;
  children: React.ReactNode;
  resetOnSave?: boolean;
  validate?: () => boolean | string;
  showCompactHeader?: boolean;
  fullHeight?: boolean;
}

const AddEntityModal: React.FC<AddEntityModalProps> = ({
  isOpen,
  onDismiss,
  onSave,
  title,
  description,
  ctaText,
  children,
  resetOnSave = true,
  validate,
  showCompactHeader = false,
  fullHeight = false
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleDismiss = () => {
    if (!loading) {
      setError(null);
      onDismiss();
    }
  };

  const handleSave = async () => {
    if (validate) {
      const validationResult = validate();
      if (validationResult !== true) {
        setError(typeof validationResult === 'string' ? validationResult : 'Please fill in all required fields');
        return;
      }
    }

    setLoading(true);
    setError(null);
    
    try {
      await onSave(null);
      if (resetOnSave) {
        setError(null);
      }
      handleDismiss();
    } catch (err) {
      console.error('Error saving entity:', err);
      setError(err instanceof Error ? err.message : 'Failed to save');
    } finally {
      setLoading(false);
    }
  };

  return (
    <IonModal 
      isOpen={isOpen} 
      onDidDismiss={handleDismiss}
      className={`add-entity-modal ${fullHeight ? 'full-height' : ''}`}
    >
      <IonHeader className={showCompactHeader ? 'compact-header' : ''}>
        <IonToolbar>
          <IonTitle>{title}</IonTitle>
          <IonButtons slot="end">
            <IonButton onClick={handleDismiss} fill="clear">
              <IonIcon icon={closeIcon} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>
      
      <IonContent className="add-entity-content">
        {description && (
          <div className="entity-description">
            <IonText>
              <p>{description}</p>
            </IonText>
          </div>
        )}
        
        {error && (
          <div className="error-message">
            <IonText color="danger">
              <p>{error}</p>
            </IonText>
          </div>
        )}
        
        <div className="entity-form-content">
          {children}
        </div>
      </IonContent>
      
      <IonFooter className="entity-footer">
        <IonToolbar>
          <div className="footer-buttons">
            <IonButton
              expand="block"
              onClick={handleSave}
              disabled={loading}
              className="save-button"
            >
              {loading ? 'Saving...' : ctaText}
            </IonButton>
          </div>
        </IonToolbar>
      </IonFooter>
    </IonModal>
  );
};

export default AddEntityModal;
