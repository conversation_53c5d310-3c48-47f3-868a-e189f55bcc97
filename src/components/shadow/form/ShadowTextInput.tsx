// ABOUTME: Shadow DOM text input component matching delivery form styling
// Provides consistent input styling across the SHOT application

import React, { useState, useEffect } from 'react';
import {
  Search,
  Mail,
  User,
  Lock,
  Phone,
  Globe,
  Calendar,
  Info,
  Code,
  Edit,
  Eye,
  EyeOff,
  LucideIcon
} from 'lucide-react';
import { getFormStylesForShadowDOM } from '../../../foundation/design-system/components/providers/FormStyleProvider';

export interface ShadowInputChangeEvent extends CustomEvent {
  detail: {
    name: string;
    value: string;
    originalEvent: Event;
  };
}

interface ShadowTextInputProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'search' | 'date';
  name?: string;
  value?: string;
  placeholder?: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  size?: 'small' | 'medium' | 'large';
  className?: string;
  onChange?: (e: ShadowInputChangeEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  helpText?: string;
  icon?: string;
  iconPosition?: 'left' | 'right';
  showPasswordToggle?: boolean;
  autoComplete?: string;
}

// Icon mapping for lucide-react components
const iconMap: Record<string, LucideIcon> = {
  search: Search,
  mail: Mail,
  user: User,
  lock: Lock,
  phone: Phone,
  globe: Globe,
  calendar: Calendar,
  info: Info,
  code: Code,
  edit: Edit,
  eye: Eye,
  eyeoff: EyeOff,
};

// Helper function to get icon component
const getIconComponent = (iconName: string): LucideIcon | null => {
  if (!iconName) return null;
  return iconMap[iconName.toLowerCase()] || null;
};

// Standard size configuration (matching delivery form)
const sizeStyles = {
  small: {
    container: 'text-sm',
    label: 'text-xs',
    input: 'px-3 py-2 text-sm',
    icon: 'w-4 h-4',
    message: 'text-xs mt-1'
  },
  medium: {
    container: 'text-base',
    label: 'text-sm',
    input: 'px-3 py-2.5 text-base',
    icon: 'w-5 h-5',
    message: 'text-xs mt-1'
  },
  large: {
    container: 'text-lg',
    label: 'text-base',
    input: 'px-4 py-3 text-lg',
    icon: 'w-6 h-6',
    message: 'text-sm mt-1'
  }
};

const ShadowTextInput: React.FC<ShadowTextInputProps> = ({
  type = 'text',
  name = '',
  value = '',
  placeholder = '',
  label = '',
  required = false,
  disabled = false,
  error = '',
  size = 'medium',
  className = '',
  onChange,
  onFocus,
  onBlur,
  helpText = '',
  icon = '',
  iconPosition = 'left',
  showPasswordToggle = true,
  autoComplete
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  
  const inputId = name || `input-${Math.random().toString(36).substring(2, 9)}`;
  const styles = sizeStyles[size];
  
  // Update internal value when prop changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);
  
  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    
    // Create custom event matching the original interface
    if (onChange) {
      const customEvent = new CustomEvent('change', {
        detail: {
          name,
          value: newValue,
          originalEvent: e.nativeEvent
        }
      }) as ShadowInputChangeEvent;
      onChange(customEvent);
    }
  };
  
  // Handle password toggle
  const handlePasswordToggle = () => {
    setShowPassword(!showPassword);
  };
  
  // Determine input type
  const inputType = type === 'password' ? (showPassword ? 'text' : 'password') : type;
  
  // Get centralized form styles
  const formStyles = getFormStylesForShadowDOM();
  
  return (
    <div className="form-field">
      <style>{`
        /* Import centralized form styles */
        ${formStyles}
        
        .form-field {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
          margin-bottom: 0;
        }
        
        .input-wrapper {
          position: relative;
        }
        
        .password-toggle {
          position: absolute;
          right: 0.75rem;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: #6b7280;
          cursor: pointer;
          padding: 0.25rem;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: color 0.2s;
        }
        
        .password-toggle:hover {
          color: #d1d5db;
        }
        
        .password-toggle svg {
          width: 1.25rem;
          height: 1.25rem;
        }
        
        /* Icon styling */
        input.with-icon-left {
          padding-left: 2.5rem;
        }
        
        input.with-icon-right {
          padding-right: 2.5rem;
        }
        
        .input-icon {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          color: #6b7280;
          pointer-events: none;
        }
        
        .input-icon.left {
          left: 0.75rem;
        }
        
        .input-icon.right {
          right: 0.75rem;
        }
        
        .input-icon svg {
          width: 1.25rem;
          height: 1.25rem;
        }
      `}</style>
      
      {/* Label */}
      {label && (
        <label htmlFor={inputId} className={required ? 'required' : ''}>
          {label}
        </label>
      )}
      
      {/* Input Container */}
      <div className="input-wrapper">
        {/* Left Icon */}
        {icon && iconPosition === 'left' && (
          <div className="input-icon left">
            {React.createElement(getIconComponent(icon) || 'div', {})}
          </div>
        )}
        
        {/* Input Field */}
        <input
          id={inputId}
          name={name}
          type={inputType}
          value={inputValue}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          className={`${error ? 'error' : ''} ${icon && iconPosition === 'left' ? 'with-icon-left' : ''} ${(icon && iconPosition === 'right' && !showPasswordToggle) || (type === 'password' && showPasswordToggle) ? 'with-icon-right' : ''} ${className || ''}`}
          onChange={handleInputChange}
          onFocus={onFocus}
          onBlur={onBlur}
          autoComplete={autoComplete}
        />
        
        {/* Right Icon (not password) */}
        {icon && iconPosition === 'right' && type !== 'password' && (
          <div className="input-icon right">
            {React.createElement(getIconComponent(icon) || 'div', {})}
          </div>
        )}
        
        {/* Password Toggle */}
        {type === 'password' && showPasswordToggle && (
          <button
            type="button"
            className="password-toggle"
            onClick={handlePasswordToggle}
            tabIndex={-1}
            aria-label={showPassword ? "Hide password" : "Show password"}
          >
            {showPassword ? <EyeOff /> : <Eye />}
          </button>
        )}
      </div>
      
      {/* Error Message or Help Text */}
      {error && <div className="error-message">{error}</div>}
      {!error && helpText && <div className="help-text">{helpText}</div>}
    </div>
  );
};

export default ShadowTextInput;