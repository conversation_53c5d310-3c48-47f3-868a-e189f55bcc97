// ABOUTME: Team selector component with full and compact variants
// Provides consistent team selection interface for coaches across different contexts

import React, { useRef, useEffect } from 'react';
import { ChevronDown, Users, Home } from 'lucide-react';

export interface TeamData {
  id: string;
  name: string;
  ageGroup?: string;
  playerCount?: number;
  sport?: string;
  logoUrl?: string;
  clubName?: string;
  isActive?: boolean;
}

export interface ShadowTeamSelectorProps {
  teams: TeamData[];
  selectedTeamId: string | null;
  onTeamChange: (teamId: string) => void;
  variant?: 'full' | 'compact';
  placeholder?: string;
  label?: string;
  className?: string;
  disabled?: boolean;
  loading?: boolean;
}

const ShadowTeamSelector: React.FC<ShadowTeamSelectorProps> = ({
  teams = [],
  selectedTeamId,
  onTeamChange,
  variant = 'full',
  placeholder = 'Select a team',
  label,
  className = '',
  disabled = false,
  loading = false
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);

  // Get selected team data
  const selectedTeam = teams.find(team => team.id === selectedTeamId);

  // Create shadow root only once
  useEffect(() => {
    const container = containerRef.current;
    if (!container || shadowRootRef.current) return;

    try {
      // Check if shadow root already exists
      if (container.shadowRoot) {
        shadowRootRef.current = container.shadowRoot;
        return;
      }

      const shadowRoot = container.attachShadow({ mode: 'open' });
      shadowRootRef.current = shadowRoot;
    } catch (error) {
      console.error('Failed to create shadow DOM for ShadowTeamSelector:', error);
    }
  }, []);

  // Update shadow DOM content
  useEffect(() => {
    const shadowRoot = shadowRootRef.current;
    if (!shadowRoot) return;

    const styles = `
      @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Montserrat:wght@400;500;600&display=swap');
      
      :host {
        display: block;
        width: 100%;
        font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .selector-container {
        width: 100%;
      }

      .selector-label {
        display: block;
        font-family: 'Poppins', sans-serif;
        font-weight: 500;
        color: #FFFFFF;
        margin-bottom: 8px;
        font-size: 14px;
      }

      /* Full variant styles */
      .selector-full {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;
      }

      .selector-full:hover:not(.disabled) {
        background: rgba(255, 255, 255, 0.08);
        border-color: #14B8A6;
        box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.1);
      }

      .selector-button-full {
        width: 100%;
        padding: 16px;
        display: flex;
        align-items: center;
        gap: 12px;
        background: none;
        border: none;
        color: #FFFFFF;
        cursor: pointer;
        text-align: left;
        outline: none;
        transition: all 0.2s ease;
      }

      .team-logo {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        overflow: hidden;
      }

      .team-logo img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .team-logo-text {
        color: white;
        font-weight: 600;
        font-size: 16px;
        font-family: 'Poppins', sans-serif;
      }

      .team-info {
        flex: 1;
        min-width: 0;
      }

      .team-name {
        font-weight: 600;
        font-size: 16px;
        color: #FFFFFF;
        margin: 0;
        font-family: 'Poppins', sans-serif;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .team-details {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-top: 4px;
        font-size: 14px;
        color: #9CA3AF;
      }

      .team-detail-item {
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .detail-icon {
        width: 14px;
        height: 14px;
        opacity: 0.7;
      }

      .chevron-icon {
        width: 20px;
        height: 20px;
        color: #9CA3AF;
        transition: transform 0.3s ease;
        flex-shrink: 0;
      }

      /* Compact variant styles */
      .selector-compact {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
      }

      .selector-compact:hover:not(.disabled) {
        background: rgba(255, 255, 255, 0.08);
        border-color: #14B8A6;
      }

      .selector-button-compact {
        width: 100%;
        padding: 8px 12px;
        display: flex;
        align-items: center;
        gap: 8px;
        background: none;
        border: none;
        color: #FFFFFF;
        cursor: pointer;
        text-align: left;
        outline: none;
        font-size: 14px;
      }

      .team-name-compact {
        flex: 1;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .team-badge {
        background: rgba(20, 184, 166, 0.1);
        color: #14B8A6;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: 600;
        white-space: nowrap;
      }

      /* Dropdown styles */
      .dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        margin-top: 4px;
        background: #1F2937;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
        overflow: hidden;
        z-index: 1000;
        max-height: 320px;
        overflow-y: auto;
        opacity: 0;
        transform: translateY(-10px);
        visibility: hidden;
        transition: all 0.2s ease;
      }

      .dropdown.open {
        opacity: 1;
        transform: translateY(0);
        visibility: visible;
      }

      .dropdown-item {
        padding: 12px 16px;
        display: flex;
        align-items: center;
        gap: 12px;
        cursor: pointer;
        transition: background 0.2s ease;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
      }

      .dropdown-item:last-child {
        border-bottom: none;
      }

      .dropdown-item:hover {
        background: rgba(255, 255, 255, 0.05);
      }

      .dropdown-item.selected {
        background: rgba(20, 184, 166, 0.1);
      }

      .dropdown-item.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .dropdown-team-logo {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        font-size: 14px;
        color: white;
        font-weight: 600;
      }

      .dropdown-team-info {
        flex: 1;
        min-width: 0;
      }

      .dropdown-team-name {
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .dropdown-team-details {
        font-size: 12px;
        color: #9CA3AF;
        margin-top: 2px;
      }

      /* Placeholder state */
      .placeholder-text {
        color: #6B7280;
        font-style: italic;
      }

      /* Disabled state */
      .disabled {
        opacity: 0.6;
        cursor: not-allowed !important;
      }

      .disabled .selector-button-full,
      .disabled .selector-button-compact {
        cursor: not-allowed !important;
      }

      /* Loading state */
      .loading-spinner {
        width: 16px;
        height: 16px;
        border: 2px solid rgba(255, 255, 255, 0.1);
        border-top-color: #14B8A6;
        border-radius: 50%;
        animation: spin 0.8s linear infinite;
      }

      @keyframes spin {
        to { transform: rotate(360deg); }
      }

      /* Mobile responsive */
      @media (max-width: 768px) {
        .team-details {
          font-size: 12px;
          gap: 8px;
        }

        .dropdown {
          max-height: 280px;
        }
      }

      /* Custom scrollbar */
      .dropdown::-webkit-scrollbar {
        width: 6px;
      }

      .dropdown::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.05);
      }

      .dropdown::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
      }

      .dropdown::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    `;

    const renderFullVariant = () => `
      <div class="selector-button-full ${disabled ? 'disabled' : ''}" data-selector-button>
        ${selectedTeam ? `
          <div class="team-logo">
            ${selectedTeam.logoUrl 
              ? `<img src="${selectedTeam.logoUrl}" alt="${selectedTeam.name}" />`
              : `<span class="team-logo-text">${selectedTeam.name.charAt(0)}</span>`
            }
          </div>
          <div class="team-info">
            <h3 class="team-name">${selectedTeam.name}</h3>
            <div class="team-details">
              ${selectedTeam.ageGroup ? `
                <span class="team-detail-item">
                  <svg class="detail-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" />
                  </svg>
                  ${selectedTeam.ageGroup}
                </span>
              ` : ''}
              ${selectedTeam.playerCount !== undefined ? `
                <span class="team-detail-item">
                  <svg class="detail-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                  </svg>
                  ${selectedTeam.playerCount} players
                </span>
              ` : ''}
              ${selectedTeam.clubName ? `
                <span class="team-detail-item">
                  <svg class="detail-icon" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                  </svg>
                  ${selectedTeam.clubName}
                </span>
              ` : ''}
            </div>
          </div>
        ` : `
          <div class="team-info">
            <p class="team-name placeholder-text">${placeholder}</p>
          </div>
        `}
        ${loading ? `
          <div class="loading-spinner"></div>
        ` : `
          <svg class="chevron-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        `}
      </div>
    `;

    const renderCompactVariant = () => `
      <div class="selector-button-compact ${disabled ? 'disabled' : ''}" data-selector-button>
        ${selectedTeam ? `
          <span class="team-name-compact">${selectedTeam.name}</span>
          ${selectedTeam.playerCount !== undefined ? `
            <span class="team-badge">${selectedTeam.playerCount}</span>
          ` : ''}
        ` : `
          <span class="team-name-compact placeholder-text">${placeholder}</span>
        `}
        ${loading ? `
          <div class="loading-spinner"></div>
        ` : `
          <svg class="chevron-icon" style="width: 16px; height: 16px;" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        `}
      </div>
    `;

    const renderDropdown = () => `
      <div class="dropdown" data-dropdown>
        ${teams.length === 0 ? `
          <div class="dropdown-item disabled">
            <p class="dropdown-team-name placeholder-text">No teams available</p>
          </div>
        ` : teams.map(team => `
          <div 
            class="dropdown-item ${team.id === selectedTeamId ? 'selected' : ''} ${team.isActive === false ? 'disabled' : ''}" 
            data-team-id="${team.id}"
          >
            <div class="dropdown-team-logo">
              ${team.logoUrl 
                ? `<img src="${team.logoUrl}" alt="${team.name}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 6px;" />`
                : team.name.charAt(0)
              }
            </div>
            <div class="dropdown-team-info">
              <p class="dropdown-team-name">${team.name}</p>
              <p class="dropdown-team-details">
                ${[
                  team.ageGroup,
                  team.playerCount !== undefined ? `${team.playerCount} players` : '',
                  team.sport
                ].filter(Boolean).join(' • ')}
              </p>
            </div>
          </div>
        `).join('')}
      </div>
    `;

    const containerHTML = `
      <div class="selector-container">
        ${label ? `<label class="selector-label">${label}</label>` : ''}
        <div class="selector-${variant} ${disabled ? 'disabled' : ''}" style="position: relative;">
          ${variant === 'full' ? renderFullVariant() : renderCompactVariant()}
          ${renderDropdown()}
        </div>
      </div>
    `;

    // Apply styles and HTML
    shadowRoot.innerHTML = `<style>${styles}</style>${containerHTML}`;

    // Event handlers
    const selectorButton = shadowRoot.querySelector('[data-selector-button]');
    const dropdown = shadowRoot.querySelector('[data-dropdown]');
    let isOpen = false;

    const toggleDropdown = () => {
      if (disabled || loading) return;
      
      isOpen = !isOpen;
      if (dropdown) {
        if (isOpen) {
          dropdown.classList.add('open');
        } else {
          dropdown.classList.remove('open');
        }
      }

      // Update chevron rotation
      const chevron = shadowRoot.querySelector('.chevron-icon') as SVGElement;
      if (chevron) {
        chevron.style.transform = isOpen ? 'rotate(180deg)' : 'rotate(0deg)';
      }
    };

    const closeDropdown = () => {
      isOpen = false;
      if (dropdown) {
        dropdown.classList.remove('open');
      }
      const chevron = shadowRoot.querySelector('.chevron-icon') as SVGElement;
      if (chevron) {
        chevron.style.transform = 'rotate(0deg)';
      }
    };

    // Button click handler
    if (selectorButton) {
      selectorButton.addEventListener('click', toggleDropdown);
    }

    // Team selection handlers
    shadowRoot.querySelectorAll('[data-team-id]').forEach(item => {
      item.addEventListener('click', () => {
        const teamId = item.getAttribute('data-team-id');
        const team = teams.find(t => t.id === teamId);
        
        if (teamId && team && team.isActive !== false) {
          onTeamChange(teamId);
          closeDropdown();
        }
      });
    });

    // Click outside to close
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as Node;
      if (!containerRef.current?.contains(target)) {
        closeDropdown();
      }
    };

    document.addEventListener('click', handleClickOutside);

    // Cleanup
    return () => {
      document.removeEventListener('click', handleClickOutside);
      if (selectorButton) {
        selectorButton.removeEventListener('click', toggleDropdown);
      }
    };
  }, [teams, selectedTeamId, variant, placeholder, label, disabled, loading, onTeamChange]);

  return (
    <div
      ref={containerRef}
      className={className}
      style={{ display: 'block', width: '100%' }}
    />
  );
};

export default ShadowTeamSelector;