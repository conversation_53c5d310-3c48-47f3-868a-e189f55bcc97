// ShadowSelect.tsx - Select dropdown field with Shadow DOM
import React, { useEffect, useRef } from 'react';

interface SelectOption {
  value: string;
  label: string;
}

interface ShadowSelectProps {
  name?: string;
  value?: string;
  options: SelectOption[];
  placeholder?: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  size?: 'small' | 'medium' | 'large';
  className?: string;
  onChange?: (e: CustomEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  helpText?: string;
}

const ShadowSelect: React.FC<ShadowSelectProps> = ({ 
  name = '',
  value = '', 
  options = [],
  placeholder = 'Select an option',
  label = '',
  required = false,
  disabled = false,
  error = '',
  size = 'medium',
  className = '',
  onChange,
  onFocus,
  onBlur,
  helpText = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const selectId = name || `select-${Math.random().toString(36).substring(2, 9)}`;

  useEffect(() => {
    if (!containerRef.current) return;

    // Create a div that will host our shadow DOM
    const host = document.createElement('div');
    host.className = className; // Apply any external classes to the host
    const shadow = host.attachShadow({ mode: 'open' });

    // Define styles
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700&display=swap');
        
        :host {
          display: block;
          width: 100%;
          margin-bottom: 16px;
        }

        .select-container {
          position: relative;
          width: 100%;
        }

        label {
          display: block;
          font-family: 'Poppins', sans-serif;
          font-weight: 500;
          margin-bottom: 8px;
          color: white;
        }

        .required::after {
          content: " *";
          color: #ff5d73;
        }

        select {
          width: 100%;
          font-family: 'Poppins', sans-serif;
          background-color: #333;
          color: white;
          border: 2px solid #555;
          border-radius: 8px;
          box-sizing: border-box;
          appearance: none;
          -webkit-appearance: none;
          -moz-appearance: none;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        /* Size variations */
        .small select {
          padding: 8px 32px 8px 12px;
          font-size: 0.875rem;
        }
        
        .small label {
          font-size: 0.875rem;
        }

        .medium select {
          padding: 10px 32px 10px 14px;
          font-size: 1rem;
        }
        
        .medium label {
          font-size: 1rem;
        }

        .large select {
          padding: 12px 32px 12px 16px;
          font-size: 1.125rem;
        }
        
        .large label {
          font-size: 1.125rem;
        }

        /* Add the dropdown arrow */
        .select-container::after {
          content: '';
          position: absolute;
          right: 12px;
          top: 50%;
          transform: translateY(-50%);
          width: 0; 
          height: 0; 
          border-left: 6px solid transparent;
          border-right: 6px solid transparent;
          border-top: 6px solid white;
          pointer-events: none;
        }

        .placeholder {
          color: #888;
        }

        select:focus {
          outline: none;
          border-color: #6B00DB; /* shot-purple */
          box-shadow: 0 0 0 2px rgba(107, 0, 219, 0.2);
        }

        select:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        /* Error state */
        .error select {
          border-color: #ff5d73;
        }

        .error-message {
          font-family: 'Poppins', sans-serif;
          color: #ff5d73;
          font-size: 0.8125rem;
          margin-top: 4px;
        }

        /* Help text */
        .help-text {
          font-family: 'Poppins', sans-serif;
          color: #888;
          font-size: 0.8125rem;
          margin-top: 4px;
        }
      </style>
    `;

    // Create options HTML
    const optionsHtml = options.map(option => 
      `<option value="${option.value}" ${value === option.value ? 'selected' : ''}>${option.label}</option>`
    ).join('');

    // Create the HTML
    shadow.innerHTML = `
      ${styles}
      <div class="${size} ${error ? 'error' : ''}">
        ${label ? `<label for="${selectId}" class="${required ? 'required' : ''}">${label}</label>` : ''}
        <div class="select-container">
          <select 
            id="${selectId}"
            name="${name}"
            ${required ? 'required' : ''}
            ${disabled ? 'disabled' : ''}
          >
            <option value="" disabled ${!value ? 'selected' : ''} class="placeholder">${placeholder}</option>
            ${optionsHtml}
          </select>
        </div>
        ${error ? `<div class="error-message">${error}</div>` : ''}
        ${!error && helpText ? `<div class="help-text">${helpText}</div>` : ''}
      </div>
    `;

    // Add event handlers
    const select = shadow.querySelector('select');
    if (select) {
      // Set the value (outside of HTML to avoid XSS issues with complex values)
      select.value = value;
      
      if (onChange) {
        select.addEventListener('change', (e: Event) => {
          const target = e.target as HTMLSelectElement;
          const customEvent = new CustomEvent('change', {
            detail: {
              name,
              value: target.value,
              originalEvent: e
            }
          });
          onChange(customEvent);
        });
      }

      if (onFocus) {
        select.addEventListener('focus', () => {
          onFocus();
        });
      }

      if (onBlur) {
        select.addEventListener('blur', () => {
          onBlur();
        });
      }
    }

    containerRef.current.appendChild(host);

    // Cleanup
    return () => {
      if (containerRef.current && host.parentNode) {
        containerRef.current.removeChild(host);
      }
    };
  }, [name, value, options, placeholder, label, required, disabled, error, size, className, onChange, onFocus, onBlur, helpText]);

  return <div ref={containerRef} />;
};

export default ShadowSelect;