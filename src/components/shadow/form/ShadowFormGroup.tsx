// ShadowFormGroup.tsx - Form group with Shadow DOM
import React, { useEffect, useRef } from 'react';

interface ShadowFormGroupProps {
  className?: string;
  style?: React.CSSProperties;
  legend?: string;
  children: React.ReactNode;
  layout?: 'vertical' | 'horizontal' | 'grid';
  columns?: 1 | 2 | 3;
  gap?: 'small' | 'medium' | 'large';
}

const ShadowFormGroup: React.FC<ShadowFormGroupProps> = ({
  className = '',
  style = {},
  legend = '',
  children,
  layout = 'vertical',
  columns = 1,
  gap = 'medium'
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (!containerRef.current) return;
    
    // Create a container for children to be rendered normally
    const childContainer = document.createElement('div');
    childContainer.className = 'children-container';
    childContainer.style.width = '100%';
    childContainer.style.margin = '0';
    childContainer.style.padding = '0';
    
    // Create a div that will host our shadow DOM for the form group styling
    const host = document.createElement('div');
    host.className = className; // Apply any external classes to the host
    
    // Apply any custom styles
    Object.keys(style).forEach(key => {
      (host.style as any)[key] = (style as any)[key];
    });
    
    const shadow = host.attachShadow({ mode: 'open' });
    
    // Calculate gap size
    let gapSize;
    switch (gap) {
      case 'small':
        gapSize = '12px';
        break;
      case 'large':
        gapSize = '24px';
        break;
      case 'medium':
      default:
        gapSize = '16px';
    }
    
    // Define styles
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700&display=swap');
        
        :host {
          display: block;
          width: 100%;
          margin-bottom: 24px;
        }
        
        .form-group {
          background-color: #181818;
          border-radius: 8px;
          padding: 20px;
          width: 100%;
        }
        
        .form-group-content {
          display: ${layout === 'grid' ? 'grid' : 'flex'};
          flex-direction: ${layout === 'horizontal' ? 'row' : 'column'};
          ${layout === 'horizontal' ? `align-items: flex-end;` : ''}
          ${layout === 'horizontal' ? `flex-wrap: wrap;` : ''}
          ${layout === 'grid' ? `grid-template-columns: repeat(${columns}, 1fr);` : ''}
          gap: ${gapSize};
        }
        
        .form-group-content > ::slotted(*) {
          margin-bottom: 0 !important;
        }
        
        legend {
          font-family: 'Poppins', sans-serif;
          font-weight: 600;
          color: white;
          font-size: 1.1rem;
          margin-bottom: 16px;
          padding: 0;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
          .form-group-content {
            display: flex;
            flex-direction: column;
          }
        }
      </style>
    `;
    
    // Create the HTML
    shadow.innerHTML = `
      ${styles}
      <div class="form-group">
        ${legend ? `<legend>${legend}</legend>` : ''}
        <div class="form-group-content">
          <slot></slot>
        </div>
      </div>
    `;
    
    // Append the child container where the React children will be rendered
    host.appendChild(childContainer);
    containerRef.current.appendChild(host);
    
    // Cleanup
    return () => {
      if (containerRef.current && host.parentNode) {
        containerRef.current.removeChild(host);
      }
    };
  }, [className, style, legend, layout, columns, gap]);
  
  return (
    <div ref={containerRef}>
      {children}
    </div>
  );
};

export default ShadowFormGroup;