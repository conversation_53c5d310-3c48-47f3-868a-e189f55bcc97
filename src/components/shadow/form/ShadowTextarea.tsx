// ShadowTextarea.tsx - Textarea field with Shadow DOM
import React, { useEffect, useRef } from 'react';

interface ShadowTextareaProps {
  name?: string;
  value?: string;
  placeholder?: string;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  size?: 'small' | 'medium' | 'large';
  className?: string;
  onChange?: (e: CustomEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  helpText?: string;
  rows?: number;
}

const ShadowTextarea: React.FC<ShadowTextareaProps> = ({ 
  name = '',
  value = '', 
  placeholder = '',
  label = '',
  required = false,
  disabled = false,
  error = '',
  size = 'medium',
  className = '',
  onChange,
  onFocus,
  onBlur,
  helpText = '',
  rows = 4
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const textareaId = name || `textarea-${Math.random().toString(36).substring(2, 9)}`;

  useEffect(() => {
    if (!containerRef.current) return;

    // Create a div that will host our shadow DOM
    const host = document.createElement('div');
    host.className = className; // Apply any external classes to the host
    const shadow = host.attachShadow({ mode: 'open' });

    // Define styles
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700&display=swap');
        
        :host {
          display: block;
          width: 100%;
          margin-bottom: 16px;
        }

        .textarea-container {
          position: relative;
          width: 100%;
        }

        label {
          display: block;
          font-family: 'Poppins', sans-serif;
          font-weight: 500;
          margin-bottom: 8px;
          color: white;
        }

        .required::after {
          content: " *";
          color: #ff5d73;
        }

        textarea {
          width: 100%;
          font-family: 'Poppins', sans-serif;
          background-color: #333;
          color: white;
          border: 2px solid #555;
          border-radius: 8px;
          box-sizing: border-box;
          resize: vertical;
          min-height: 100px;
          transition: all 0.2s ease;
        }

        textarea:focus {
          outline: none;
          border-color: #6B00DB; /* shot-purple */
          box-shadow: 0 0 0 2px rgba(107, 0, 219, 0.2);
        }

        textarea:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        textarea::placeholder {
          color: #888;
        }

        /* Size variations */
        .small textarea {
          padding: 8px 12px;
          font-size: 0.875rem;
        }
        
        .small label {
          font-size: 0.875rem;
        }

        .medium textarea {
          padding: 10px 14px;
          font-size: 1rem;
        }
        
        .medium label {
          font-size: 1rem;
        }

        .large textarea {
          padding: 12px 16px;
          font-size: 1.125rem;
        }
        
        .large label {
          font-size: 1.125rem;
        }

        /* Error state */
        .error textarea {
          border-color: #ff5d73;
        }

        .error-message {
          font-family: 'Poppins', sans-serif;
          color: #ff5d73;
          font-size: 0.8125rem;
          margin-top: 4px;
        }

        /* Help text */
        .help-text {
          font-family: 'Poppins', sans-serif;
          color: #888;
          font-size: 0.8125rem;
          margin-top: 4px;
        }
      </style>
    `;

    // Create the HTML
    shadow.innerHTML = `
      ${styles}
      <div class="${size} ${error ? 'error' : ''}">
        ${label ? `<label for="${textareaId}" class="${required ? 'required' : ''}">${label}</label>` : ''}
        <div class="textarea-container">
          <textarea 
            id="${textareaId}"
            name="${name}"
            placeholder="${placeholder}"
            rows="${rows}"
            ${required ? 'required' : ''}
            ${disabled ? 'disabled' : ''}
          ></textarea>
        </div>
        ${error ? `<div class="error-message">${error}</div>` : ''}
        ${!error && helpText ? `<div class="help-text">${helpText}</div>` : ''}
      </div>
    `;

    // Add event handlers
    const textarea = shadow.querySelector('textarea');
    if (textarea) {
      // Set the value directly
      textarea.value = value;
      
      if (onChange) {
        textarea.addEventListener('input', (e: Event) => {
          const target = e.target as HTMLTextAreaElement;
          const customEvent = new CustomEvent('change', {
            detail: {
              name,
              value: target.value,
              originalEvent: e
            }
          });
          onChange(customEvent);
        });
      }

      if (onFocus) {
        textarea.addEventListener('focus', () => {
          onFocus();
        });
      }

      if (onBlur) {
        textarea.addEventListener('blur', () => {
          onBlur();
        });
      }
    }

    containerRef.current.appendChild(host);

    // Cleanup
    return () => {
      if (containerRef.current && host.parentNode) {
        containerRef.current.removeChild(host);
      }
    };
  }, [name, value, placeholder, label, required, disabled, error, size, className, onChange, onFocus, onBlur, helpText, rows]);

  return <div ref={containerRef} />;
};

export default ShadowTextarea;