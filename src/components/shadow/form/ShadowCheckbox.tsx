// ShadowCheckbox.tsx - Checkbox field with Shadow DOM
import React, { useEffect, useRef } from 'react';

interface ShadowCheckboxProps {
  name?: string;
  checked?: boolean;
  label: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  className?: string;
  onChange?: (e: CustomEvent) => void;
  helpText?: string;
}

const ShadowCheckbox: React.FC<ShadowCheckboxProps> = ({
  name = '',
  checked = false,
  label,
  required = false,
  disabled = false,
  error = '',
  className = '',
  onChange,
  helpText = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const checkboxRef = useRef<HTMLInputElement | null>(null);
  const checkboxId = useRef(name || `checkbox-${Math.random().toString(36).substring(2, 9)}`).current;

  // Create shadow DOM only once
  useEffect(() => {
    if (!containerRef.current || shadowRootRef.current) return;

    // Create a div that will host our shadow DOM
    const host = document.createElement('div');
    host.className = className; // Apply any external classes to the host
    const shadow = host.attachShadow({ mode: 'open' });
    shadowRootRef.current = shadow;

    // Define styles
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700&display=swap');
        
        :host {
          display: block;
          width: 100%;
          margin-bottom: 16px;
        }

        .checkbox-container {
          display: flex;
          align-items: flex-start;
          position: relative;
        }

        .input-wrapper {
          position: relative;
          display: inline-block;
          width: 20px;
          height: 20px;
          flex-shrink: 0;
          margin-right: 10px;
          margin-top: 1px;
        }

        /* Hide the default checkbox but keep it clickable */
        input[type="checkbox"] {
          position: absolute;
          opacity: 0;
          width: 20px;
          height: 20px;
          cursor: pointer;
          z-index: 1;
        }

        /* Custom checkbox style */
        .custom-checkbox {
          position: absolute;
          top: 0;
          left: 0;
          width: 20px;
          height: 20px;
          background-color: #333;
          border: 2px solid #555;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.2s;
        }

        /* Checked state */
        input[type="checkbox"]:checked + .custom-checkbox {
          background-color: #6B00DB; /* shot-purple */
          border-color: #6B00DB; /* shot-purple */
        }

        /* Checkmark */
        input[type="checkbox"]:checked + .custom-checkbox::after {
          content: '';
          position: absolute;
          left: 6px;
          top: 2px;
          width: 5px;
          height: 10px;
          border: solid white;
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
        }

        /* Focus state */
        input[type="checkbox"]:focus + .custom-checkbox {
          box-shadow: 0 0 0 2px rgba(107, 0, 219, 0.2);
        }

        /* Disabled state */
        input[type="checkbox"]:disabled + .custom-checkbox {
          background-color: #444;
          border-color: #555;
          cursor: not-allowed;
        }

        input[type="checkbox"]:disabled:checked + .custom-checkbox {
          background-color: #555;
        }

        input[type="checkbox"]:disabled ~ label {
          color: #888;
          cursor: not-allowed;
        }

        label {
          font-family: 'Poppins', sans-serif;
          color: white;
          font-size: 1rem;
          cursor: pointer;
          user-select: none;
        }

        .required::after {
          content: " *";
          color: #ff5d73;
          vertical-align: middle;
        }

        /* Error state */
        .error .custom-checkbox {
          border-color: #ff5d73;
        }

        .error-message {
          font-family: 'Poppins', sans-serif;
          color: #ff5d73;
          font-size: 0.8125rem;
          margin-top: 4px;
          margin-left: 30px;
        }

        /* Help text */
        .help-text {
          font-family: 'Poppins', sans-serif;
          color: #888;
          font-size: 0.8125rem;
          margin-top: 4px;
          margin-left: 30px;
        }
      </style>
    `;

    // Create initial HTML structure
    shadow.innerHTML = `
      ${styles}
      <div class="${error ? 'error' : ''}">
        <div class="checkbox-container">
          <span class="input-wrapper">
            <input 
              type="checkbox" 
              id="${checkboxId}"
              name="${name}"
              ${checked ? 'checked' : ''}
              ${required ? 'required' : ''}
              ${disabled ? 'disabled' : ''}
            />
            <span class="custom-checkbox"></span>
          </span>
          <label for="${checkboxId}" class="${required ? 'required' : ''}">${label}</label>
        </div>
        ${error ? `<div class="error-message">${error}</div>` : ''}
        ${!error && helpText ? `<div class="help-text">${helpText}</div>` : ''}
      </div>
    `;

    // Store checkbox reference
    checkboxRef.current = shadow.querySelector('input[type="checkbox"]') as HTMLInputElement;

    containerRef.current.appendChild(host);

    // Cleanup
    return () => {
      if (containerRef.current && host.parentNode) {
        containerRef.current.removeChild(host);
      }
    };
  }, []); // Only run once on mount

  // Update checkbox props without recreating shadow DOM
  useEffect(() => {
    if (!shadowRootRef.current || !checkboxRef.current) return;

    // Update checkbox properties
    checkboxRef.current.checked = checked;
    checkboxRef.current.disabled = disabled;
    checkboxRef.current.required = required;
    checkboxRef.current.name = name;

    // Update label
    const labelElement = shadowRootRef.current.querySelector('label');
    if (labelElement) {
      labelElement.textContent = label;
      labelElement.className = required ? 'required' : '';
    }

    // Update error state
    const containerDiv = shadowRootRef.current.querySelector('div');
    if (containerDiv) {
      containerDiv.className = error ? 'error' : '';
    }

    // Update error message
    const errorElement = shadowRootRef.current.querySelector('.error-message');
    const helpElement = shadowRootRef.current.querySelector('.help-text');
    
    if (error) {
      if (errorElement) {
        errorElement.textContent = error;
      } else {
        // Add error message
        const container = shadowRootRef.current.querySelector('.checkbox-container')?.parentElement;
        if (container) {
          const errorDiv = document.createElement('div');
          errorDiv.className = 'error-message';
          errorDiv.textContent = error;
          container.appendChild(errorDiv);
        }
      }
      // Remove help text if error exists
      if (helpElement) {
        helpElement.remove();
      }
    } else {
      // Remove error message
      if (errorElement) {
        errorElement.remove();
      }
      // Handle help text
      if (helpText) {
        if (helpElement) {
          helpElement.textContent = helpText;
        } else {
          // Add help text
          const container = shadowRootRef.current.querySelector('.checkbox-container')?.parentElement;
          if (container) {
            const helpDiv = document.createElement('div');
            helpDiv.className = 'help-text';
            helpDiv.textContent = helpText;
            container.appendChild(helpDiv);
          }
        }
      } else if (helpElement) {
        helpElement.remove();
      }
    }
  }, [checked, disabled, required, name, label, error, helpText]);

  // Handle change events
  useEffect(() => {
    if (!checkboxRef.current) return;

    const handleChange = (e: Event) => {
      const target = e.target as HTMLInputElement;
      const customEvent = new CustomEvent('change', {
        detail: {
          name,
          checked: target.checked,
          value: target.checked,
          originalEvent: e
        }
      });
      if (onChange) {
        onChange(customEvent);
      }
    };

    checkboxRef.current.addEventListener('change', handleChange);

    return () => {
      if (checkboxRef.current) {
        checkboxRef.current.removeEventListener('change', handleChange);
      }
    };
  }, [onChange, name]);

  return <div ref={containerRef} />;
};

export default ShadowCheckbox;