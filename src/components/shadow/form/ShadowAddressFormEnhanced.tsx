// ABOUTME: Enhanced Shadow DOM address form with dynamic country-specific fields
// Adapts form layout and validation based on selected country configuration

import React, { useEffect, useRef, useState } from 'react';
import { AddressData } from '../../../types/addressConfig';
import { getAddressConfig, validateAddressField } from '../../../config/addressFormats';
import ShadowCountrySelector from './ShadowCountrySelector';

interface ShadowAddressFormEnhancedProps {
  formData: AddressData;
  onChange: (data: AddressData) => void;
  errors?: Partial<Record<keyof AddressData, string>>;
  title?: string;
  showEmailPhone?: boolean;
  defaultCountry?: string;
  className?: string;
}

const ShadowAddressFormEnhanced: React.FC<ShadowAddressFormEnhancedProps> = ({
  formData,
  onChange,
  errors = {},
  title = 'Shipping Address',
  showEmailPhone = true,
  defaultCountry = 'GB',
  className
}) => {
  const [country, setCountry] = useState(formData.country || defaultCountry);
  const [localErrors, setLocalErrors] = useState<Partial<Record<keyof AddressData, string>>>({});
  const formRef = useRef<HTMLDivElement>(null);

  // Get country configuration
  const config = getAddressConfig(country);

  useEffect(() => {
    // Update form data when country changes
    onChange({ ...formData, country });
  }, [country]);

  const handleFieldChange = (fieldName: keyof AddressData, value: string) => {
    // Validate field
    const validation = validateAddressField(country, fieldName, value);
    
    if (!validation.isValid) {
      setLocalErrors(prev => ({ ...prev, [fieldName]: validation.error }));
    } else {
      setLocalErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }

    // Update form data
    onChange({ ...formData, [fieldName]: value });
  };

  const renderField = (fieldName: keyof AddressData) => {
    const field = config.fields.find(f => f.name === fieldName);
    if (!field || field.hidden) return null;

    // Skip email/phone if not showing them
    if (!showEmailPhone && (fieldName === 'email' || fieldName === 'phone')) {
      return null;
    }

    const error = errors[fieldName] || localErrors[fieldName];
    const value = formData[fieldName] || '';

    // Handle special labels
    let label = field.label;
    if (fieldName === 'postalCode') label = config.postalCodeLabel;
    if (fieldName === 'state') label = config.stateLabel;

    return (
      <div key={fieldName} className={`form-field ${field.type === 'select' ? '' : 'text-field'}`}>
        <label htmlFor={fieldName} className={field.required ? 'required' : ''}>
          {label}
        </label>
        
        {field.type === 'select' && field.options ? (
          <select
            id={fieldName}
            name={fieldName}
            value={value}
            onChange={(e) => handleFieldChange(fieldName, e.target.value)}
            className={`form-select ${error ? 'error' : ''}`}
            required={field.required}
          >
            <option value="">{field.placeholder}</option>
            {field.options.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        ) : (
          <input
            type={field.type || 'text'}
            id={fieldName}
            name={fieldName}
            value={value}
            onChange={(e) => handleFieldChange(fieldName, e.target.value)}
            placeholder={field.placeholder}
            className={`form-input ${error ? 'error' : ''}`}
            autoComplete={field.autocomplete}
            required={field.required}
            maxLength={field.validation?.maxLength}
          />
        )}
        
        {error && <span className="error-message">{error}</span>}
      </div>
    );
  };

  // Group fields for layout
  const renderFieldGroup = () => {
    const fieldGroups = [
      ['firstName', 'lastName'],
      ['email'],
      ['phone'],
      ['address1'],
      ['address2'],
      ['city'],
      ['state', 'postalCode']
    ];

    return fieldGroups.map((group, index) => {
      const fields = group.map(fieldName => renderField(fieldName as keyof AddressData)).filter(Boolean);
      if (fields.length === 0) return null;

      return (
        <div key={index} className={`form-row ${group.length > 1 ? 'two-column' : 'single-column'}`}>
          {fields}
        </div>
      );
    });
  };

  return (
    <div ref={formRef} className={`shadow-address-form-enhanced ${className || ''}`}>
      <style>{`
        .shadow-address-form-enhanced {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .form-title {
          font-size: 1.25rem;
          font-weight: 600;
          color: white;
          margin-bottom: 0.5rem;
        }

        .country-section {
          margin-bottom: 1rem;
          padding-bottom: 1rem;
          border-bottom: 1px solid #374151;
        }

        .country-label {
          display: block;
          font-size: 0.875rem;
          font-weight: 500;
          color: #d1d5db;
          margin-bottom: 0.5rem;
        }

        .form-row {
          display: grid;
          gap: 1rem;
        }

        .form-row.two-column {
          grid-template-columns: 1fr 1fr;
        }

        .form-row.single-column {
          grid-template-columns: 1fr;
        }

        .form-field {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        label {
          display: block;
          font-size: 0.875rem;
          font-weight: 500;
          color: #d1d5db;
        }

        label.required::after {
          content: ' *';
          color: #ef4444;
        }

        .form-input,
        .form-select {
          width: 100%;
          padding: 0.625rem 0.75rem;
          background-color: #1f2937;
          border: 1px solid #374151;
          border-radius: 0.5rem;
          color: white;
          font-size: 1rem;
          transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-input::placeholder {
          color: #6b7280;
        }

        .form-input:focus,
        .form-select:focus {
          outline: none;
          border-color: #eab308;
          box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
        }

        .form-input.error,
        .form-select.error {
          border-color: #ef4444;
        }

        .error-message {
          font-size: 0.75rem;
          color: #ef4444;
          margin-top: 0.25rem;
        }

        @media (max-width: 640px) {
          .form-row.two-column {
            grid-template-columns: 1fr;
          }
        }
      `}</style>

      {title && <h3 className="form-title">{title}</h3>}
      
      <div className="country-section">
        <label className="country-label required">Country/Region</label>
        <ShadowCountrySelector
          value={country}
          onChange={setCountry}
        />
      </div>

      <div className="form-fields">
        {renderFieldGroup()}
      </div>
    </div>
  );
};

export default ShadowAddressFormEnhanced;