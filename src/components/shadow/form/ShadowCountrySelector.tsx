// ABOUTME: Shadow DOM country selector with flags for international address forms
// Provides visual country selection with immediate form updates

import React, { useEffect, useRef } from 'react';
import { SUPPORTED_COUNTRIES } from '../../../config/addressFormats';

interface ShadowCountrySelectorProps {
  value: string;
  onChange: (countryCode: string) => void;
  className?: string;
}

class ShadowCountrySelectorElement extends HTMLElement {
  private shadow: ShadowRoot;
  private value: string = 'GB';
  private isOpen: boolean = false;

  constructor() {
    super();
    this.shadow = this.attachShadow({ mode: 'open' });
  }

  connectedCallback() {
    this.render();
  }

  static get observedAttributes() {
    return ['value'];
  }

  attributeChangedCallback(name: string, oldValue: string, newValue: string) {
    if (oldValue !== newValue) {
      if (name === 'value') {
        this.value = newValue;
        this.render();
      }
    }
  }

  setValue(value: string) {
    this.value = value;
    this.render();
  }

  private toggleDropdown() {
    this.isOpen = !this.isOpen;
    this.render();
  }

  private selectCountry(countryCode: string) {
    this.value = countryCode;
    this.isOpen = false;
    this.dispatchEvent(new CustomEvent('countrychange', {
      detail: { countryCode },
      bubbles: true,
      composed: true
    }));
    this.render();
  }

  render() {
    const selectedCountry = SUPPORTED_COUNTRIES.find(c => c.code === this.value) || SUPPORTED_COUNTRIES[0];

    this.shadow.innerHTML = `
      <style>
        :host {
          display: block;
          width: 100%;
        }

        .country-selector {
          position: relative;
          width: 100%;
        }

        .selector-button {
          width: 100%;
          padding: 0.625rem 0.75rem;
          background-color: #1f2937;
          border: 1px solid #374151;
          border-radius: 0.5rem;
          color: white;
          font-size: 1rem;
          text-align: left;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: space-between;
          transition: border-color 0.2s, box-shadow 0.2s;
        }

        .selector-button:hover {
          border-color: #4b5563;
        }

        .selector-button:focus {
          outline: none;
          border-color: #eab308;
          box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
        }

        .selector-button.open {
          border-color: #eab308;
          box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
        }

        .country-display {
          display: flex;
          align-items: center;
          gap: 0.5rem;
        }

        .flag {
          font-size: 1.25rem;
          line-height: 1;
        }

        .country-name {
          font-weight: 500;
        }

        .chevron {
          width: 1rem;
          height: 1rem;
          transition: transform 0.2s;
        }

        .chevron.open {
          transform: rotate(180deg);
        }

        .dropdown {
          position: absolute;
          top: 100%;
          left: 0;
          right: 0;
          margin-top: 0.25rem;
          background-color: #1f2937;
          border: 1px solid #374151;
          border-radius: 0.5rem;
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
          z-index: 10;
          opacity: 0;
          transform: translateY(-0.5rem);
          pointer-events: none;
          transition: opacity 0.2s, transform 0.2s;
        }

        .dropdown.open {
          opacity: 1;
          transform: translateY(0);
          pointer-events: auto;
        }

        .country-option {
          padding: 0.75rem 1rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          cursor: pointer;
          transition: background-color 0.15s;
        }

        .country-option:hover {
          background-color: #374151;
        }

        .country-option.selected {
          background-color: #374151;
        }

        .country-option:first-child {
          border-radius: 0.5rem 0.5rem 0 0;
        }

        .country-option:last-child {
          border-radius: 0 0 0.5rem 0.5rem;
        }

        .country-code {
          font-size: 0.875rem;
          color: #9ca3af;
          margin-left: auto;
        }

        /* Click outside overlay */
        .overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 5;
          display: none;
        }

        .overlay.open {
          display: block;
        }
      </style>

      <div class="country-selector">
        <div class="overlay ${this.isOpen ? 'open' : ''}"></div>
        
        <button class="selector-button ${this.isOpen ? 'open' : ''}" type="button">
          <div class="country-display">
            <span class="flag">${selectedCountry.flag}</span>
            <span class="country-name">${selectedCountry.name}</span>
          </div>
          <svg class="chevron ${this.isOpen ? 'open' : ''}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </button>

        <div class="dropdown ${this.isOpen ? 'open' : ''}">
          ${SUPPORTED_COUNTRIES.map(country => `
            <div class="country-option ${country.code === this.value ? 'selected' : ''}" data-country="${country.code}">
              <span class="flag">${country.flag}</span>
              <span class="country-name">${country.name}</span>
              <span class="country-code">${country.code}</span>
            </div>
          `).join('')}
        </div>
      </div>
    `;

    // Add event listeners
    const button = this.shadow.querySelector('.selector-button');
    const overlay = this.shadow.querySelector('.overlay');
    const options = this.shadow.querySelectorAll('.country-option');

    button?.addEventListener('click', () => this.toggleDropdown());
    overlay?.addEventListener('click', () => {
      this.isOpen = false;
      this.render();
    });

    options.forEach(option => {
      option.addEventListener('click', () => {
        const countryCode = option.getAttribute('data-country');
        if (countryCode) {
          this.selectCountry(countryCode);
        }
      });
    });
  }
}

// Register the custom element
if (!customElements.get('shadow-country-selector')) {
  customElements.define('shadow-country-selector', ShadowCountrySelectorElement);
}

// React wrapper component
const ShadowCountrySelector: React.FC<ShadowCountrySelectorProps> = ({
  value,
  onChange,
  className
}) => {
  const elementRef = useRef<ShadowCountrySelectorElement>(null);

  useEffect(() => {
    if (elementRef.current) {
      elementRef.current.setValue(value);
    }
  }, [value]);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const handleChange = (e: Event) => {
      const customEvent = e as CustomEvent<{ countryCode: string }>;
      onChange(customEvent.detail.countryCode);
    };

    element.addEventListener('countrychange', handleChange);
    return () => {
      element.removeEventListener('countrychange', handleChange);
    };
  }, [onChange]);

  return (
    <shadow-country-selector
      ref={elementRef}
      value={value}
      class={className}
    />
  );
};

export default ShadowCountrySelector;