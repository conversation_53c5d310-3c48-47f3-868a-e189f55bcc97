// ShadowSportSelector.tsx - Sports selection component with Shadow DOM
import React, { useEffect, useRef } from 'react';
import { CheckCircle } from 'lucide-react';
import ReactDOM from 'react-dom/client';

interface Sport {
  value: string;
  label: string;
  image: string;
}

interface ShadowSportSelectorProps {
  value?: string;
  onChange?: (e: CustomEvent) => void;
  label?: string;
  error?: string;
  required?: boolean;
  sports?: Sport[];
  disabled?: boolean;
  className?: string;
  helpText?: string;
}

const defaultSports: Sport[] = [
  { value: 'football', label: 'Football', image: '⚽' },
  { value: 'boxing', label: 'Boxing', image: '🥊' },
  { value: 'basketball', label: 'Basketball', image: '🏀' },
  { value: 'tennis', label: 'Tennis', image: '🎾' },
  { value: 'cricket', label: 'Cricket', image: '🏏' },
  { value: 'rugby', label: 'Rugby', image: '🏉' }
];

const ShadowSportSelector: React.FC<ShadowSportSelectorProps> = ({ 
  value = '', 
  onChange,
  label = '',
  error = '',
  required = false,
  sports = defaultSports,
  disabled = false,
  className = '',
  helpText = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRootRef = useRef<ShadowRoot | null>(null);
  const reactRootRef = useRef<ReactDOM.Root | null>(null);
  const cleanupTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Create a div that will host our shadow DOM
    const host = document.createElement('div');
    host.className = className; // Apply any external classes to the host
    const shadow = host.attachShadow({ mode: 'open' });
    shadowRootRef.current = shadow;

    // Define styles
    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700&display=swap');
        
        :host {
          display: block;
          width: 100%;
          margin-bottom: 16px;
        }

        .sport-selector-container {
          width: 100%;
        }

        label {
          display: block;
          font-family: 'Poppins', sans-serif;
          font-weight: 500;
          margin-bottom: 8px;
          color: white;
          font-size: 0.875rem;
        }

        .required::after {
          content: " *";
          color: #ff5d73;
        }

        .sports-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 16px;
          padding: 4px;
          border-radius: 12px;
        }

        @media (max-width: 640px) {
          .sports-grid {
            grid-template-columns: repeat(2, 1fr);
          }
        }

        .sport-button {
          position: relative;
          padding: 16px;
          border-radius: 12px;
          border: 2px solid;
          background-color: #1a1a1a; /* shot-black */
          cursor: pointer;
          transition: all 0.2s ease;
          text-align: center;
          font-family: 'Poppins', sans-serif;
        }

        .sport-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .sport-button:not(.selected) {
          border-color: #2a2a2a; /* gray-700 */
        }

        .sport-button:not(.selected):not(:disabled):hover {
          border-color: #555; /* gray-600 */
          background-color: #232323; /* gray-800 */
          transform: scale(1.02);
        }

        .sport-button.selected {
          border-color: #1ABC9C; /* shot-teal */
          background-color: rgba(26, 188, 156, 0.1);
          transform: scale(1.05);
          box-shadow: 0 10px 20px rgba(26, 188, 156, 0.2);
        }

        .sport-emoji {
          font-size: 2rem;
          margin-bottom: 8px;
          display: block;
        }

        .sport-label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #9CA3AF; /* gray-300 */
        }

        .sport-button.selected .sport-label {
          color: #1ABC9C; /* shot-teal */
        }

        .check-icon {
          position: absolute;
          top: 4px;
          right: 4px;
          width: 20px;
          height: 20px;
          background-color: #1ABC9C; /* shot-teal */
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: black;
        }

        /* Error state */
        .error .sports-grid {
          box-shadow: 0 0 0 2px #ff5d73;
          border-radius: 12px;
        }

        .error-message {
          font-family: 'Poppins', sans-serif;
          color: #ff5d73;
          font-size: 0.8125rem;
          margin-top: 8px;
        }

        /* Help text */
        .help-text {
          font-family: 'Poppins', sans-serif;
          color: #888;
          font-size: 0.8125rem;
          margin-top: 8px;
        }

        /* React icon container */
        .icon-container {
          width: 12px;
          height: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      </style>
    `;

    // Create the HTML structure
    shadow.innerHTML = `
      ${styles}
      <div class="sport-selector-container ${error ? 'error' : ''}">
        ${label ? `<label class="${required ? 'required' : ''}">${label}</label>` : ''}
        <div class="sports-grid" id="sports-grid"></div>
        ${error ? `<div class="error-message">${error}</div>` : ''}
        ${!error && helpText ? `<div class="help-text">${helpText}</div>` : ''}
      </div>
    `;

    // Create React root for the sports grid
    const sportsGrid = shadow.getElementById('sports-grid');
    if (sportsGrid) {
      const reactRoot = ReactDOM.createRoot(sportsGrid);
      reactRootRef.current = reactRoot;
      
      // Render the sports buttons
      const renderSports = () => {
        const SportButtons = () => (
          <>
            {sports.map((sport) => (
              <button
                key={sport.value}
                type="button"
                className={`sport-button ${value === sport.value ? 'selected' : ''}`}
                onClick={() => {
                  if (!disabled && onChange) {
                    const customEvent = new CustomEvent('change', {
                      detail: { value: sport.value }
                    });
                    onChange(customEvent);
                  }
                }}
                disabled={disabled}
              >
                <span className="sport-emoji">{sport.image}</span>
                <span className="sport-label">{sport.label}</span>
                {value === sport.value && (
                  <div className="check-icon">
                    <div className="icon-container">
                      <CheckCircle size={12} />
                    </div>
                  </div>
                )}
              </button>
            ))}
          </>
        );

        reactRoot.render(<SportButtons />);
      };

      renderSports();
    }

    containerRef.current.appendChild(host);

    // Cleanup
    return () => {
      // Cancel any pending cleanup timeout
      if (cleanupTimeoutRef.current) {
        clearTimeout(cleanupTimeoutRef.current);
      }

      // Defer unmounting to avoid React warning
      cleanupTimeoutRef.current = setTimeout(() => {
        if (reactRootRef.current) {
          try {
            reactRootRef.current.unmount();
          } catch (error) {
            // Ignore unmount errors during cleanup
          }
          reactRootRef.current = null;
        }
        if (containerRef.current && host.parentNode) {
          containerRef.current.removeChild(host);
        }
      }, 0);
    };
  }, [className]);

  // Update the React content when props change
  useEffect(() => {
    if (!reactRootRef.current) return;

    const SportButtons = () => (
      <>
        {sports.map((sport) => (
          <button
            key={sport.value}
            type="button"
            className={`sport-button ${value === sport.value ? 'selected' : ''}`}
            onClick={() => {
              if (!disabled && onChange) {
                const customEvent = new CustomEvent('change', {
                  detail: { value: sport.value }
                });
                onChange(customEvent);
              }
            }}
            disabled={disabled}
          >
            <span className="sport-emoji">{sport.image}</span>
            <span className="sport-label">{sport.label}</span>
            {value === sport.value && (
              <div className="check-icon">
                <div className="icon-container">
                  <CheckCircle size={12} />
                </div>
              </div>
            )}
          </button>
        ))}
      </>
    );

    reactRootRef.current.render(<SportButtons />);
  }, [value, onChange, sports, disabled]);

  // Update error and help text
  useEffect(() => {
    if (!shadowRootRef.current) return;

    const container = shadowRootRef.current.querySelector('.sport-selector-container');
    const errorElement = shadowRootRef.current.querySelector('.error-message');
    const helpElement = shadowRootRef.current.querySelector('.help-text');

    if (container) {
      if (error) {
        container.classList.add('error');
      } else {
        container.classList.remove('error');
      }
    }

    if (errorElement) {
      errorElement.textContent = error;
    }

    if (helpElement && helpText) {
      helpElement.textContent = helpText;
    }
  }, [error, helpText]);

  return <div ref={containerRef} />;
};

export default ShadowSportSelector;