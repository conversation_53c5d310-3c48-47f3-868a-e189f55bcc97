// ABOUTME: Shadow DOM address form component for shipping and billing addresses
// Provides structured address input with validation and autocomplete support

import React, { useEffect, useRef } from 'react';

export interface AddressData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

interface ShadowAddressFormProps {
  formData: AddressData;
  onChange: (data: AddressData) => void;
  errors?: Partial<Record<keyof AddressData, string>>;
  title?: string;
  showEmailPhone?: boolean;
  className?: string;
}

class ShadowAddressFormElement extends HTMLElement {
  private shadow: ShadowRoot;
  private formData: AddressData = {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address1: '',
    address2: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'GB' // UK default
  };
  private errors: Partial<Record<keyof AddressData, string>> = {};
  private title = 'Address';
  private showEmailPhone = true;

  constructor() {
    super();
    this.shadow = this.attachShadow({ mode: 'open' });
  }

  connectedCallback() {
    this.render();
  }

  static get observedAttributes() {
    return ['title', 'show-email-phone'];
  }

  attributeChangedCallback(name: string, oldValue: string, newValue: string) {
    if (oldValue !== newValue) {
      if (name === 'title') this.title = newValue;
      if (name === 'show-email-phone') this.showEmailPhone = newValue === 'true';
      this.render();
    }
  }

  setFormData(data: AddressData) {
    this.formData = data;
    this.render();
  }

  setErrors(errors: Partial<Record<keyof AddressData, string>>) {
    this.errors = errors;
    this.render();
  }

  private handleInputChange(field: keyof AddressData, value: string) {
    this.formData = { ...this.formData, [field]: value };
    this.dispatchEvent(new CustomEvent('addresschange', {
      detail: this.formData,
      bubbles: true,
      composed: true
    }));
  }

  render() {
    this.shadow.innerHTML = `
      <style>
        :host {
          display: block;
          width: 100%;
        }

        .address-form {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .form-title {
          font-size: 1.25rem;
          font-weight: 600;
          color: white;
          margin-bottom: 0.5rem;
        }

        .form-row {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1rem;
        }

        .form-field {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .form-field.full-width {
          grid-column: 1 / -1;
        }

        label {
          display: block;
          font-size: 0.875rem;
          font-weight: 500;
          color: #d1d5db;
        }

        .required::after {
          content: ' *';
          color: #ef4444;
        }

        input {
          width: 100%;
          padding: 0.625rem 0.75rem;
          background-color: #1f2937;
          border: 1px solid #374151;
          border-radius: 0.5rem;
          color: white;
          font-size: 1rem;
          transition: border-color 0.2s, box-shadow 0.2s;
        }

        input::placeholder {
          color: #6b7280;
        }

        input:focus {
          outline: none;
          border-color: #eab308;
          box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.1);
        }

        input.error {
          border-color: #ef4444;
        }

        .error-message {
          font-size: 0.75rem;
          color: #ef4444;
          margin-top: 0.25rem;
        }

        .state-zip-row {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 1rem;
        }

        @media (max-width: 640px) {
          .form-row {
            grid-template-columns: 1fr;
          }

          .state-zip-row {
            grid-template-columns: 1fr;
          }
        }
      </style>

      <div class="address-form">
        ${this.title ? `<h3 class="form-title">${this.title}</h3>` : ''}
        
        <div class="form-row">
          <div class="form-field">
            <label for="firstName" class="required">First Name</label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value="${this.formData.firstName}"
              placeholder="John"
              class="${this.errors.firstName ? 'error' : ''}"
              autocomplete="given-name"
              required
            />
            ${this.errors.firstName ? `<span class="error-message">${this.errors.firstName}</span>` : ''}
          </div>
          
          <div class="form-field">
            <label for="lastName" class="required">Last Name</label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value="${this.formData.lastName}"
              placeholder="Doe"
              class="${this.errors.lastName ? 'error' : ''}"
              autocomplete="family-name"
              required
            />
            ${this.errors.lastName ? `<span class="error-message">${this.errors.lastName}</span>` : ''}
          </div>
        </div>

        ${this.showEmailPhone ? `
          <div class="form-field full-width">
            <label for="email" class="required">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              value="${this.formData.email}"
              placeholder="<EMAIL>"
              class="${this.errors.email ? 'error' : ''}"
              autocomplete="email"
              required
            />
            ${this.errors.email ? `<span class="error-message">${this.errors.email}</span>` : ''}
          </div>

          <div class="form-field full-width">
            <label for="phone" class="required">Phone</label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value="${this.formData.phone}"
              placeholder="+44 20 7123 4567"
              class="${this.errors.phone ? 'error' : ''}"
              autocomplete="tel"
              required
            />
            ${this.errors.phone ? `<span class="error-message">${this.errors.phone}</span>` : ''}
          </div>
        ` : ''}

        <div class="form-field full-width">
          <label for="address1" class="required">Address Line 1</label>
          <input
            type="text"
            id="address1"
            name="address1"
            value="${this.formData.address1}"
            placeholder="123 High Street"
            class="${this.errors.address1 ? 'error' : ''}"
            autocomplete="address-line1"
            required
          />
          ${this.errors.address1 ? `<span class="error-message">${this.errors.address1}</span>` : ''}
        </div>

        <div class="form-field full-width">
          <label for="address2">Address Line 2</label>
          <input
            type="text"
            id="address2"
            name="address2"
            value="${this.formData.address2 || ''}"
            placeholder="Apartment, suite, etc. (optional)"
            autocomplete="address-line2"
          />
        </div>

        <div class="form-field full-width">
          <label for="city" class="required">City</label>
          <input
            type="text"
            id="city"
            name="city"
            value="${this.formData.city}"
            placeholder="London"
            class="${this.errors.city ? 'error' : ''}"
            autocomplete="address-level2"
            required
          />
          ${this.errors.city ? `<span class="error-message">${this.errors.city}</span>` : ''}
        </div>

        <div class="state-zip-row">
          <div class="form-field">
            <label for="state" class="required">County</label>
            <input
              type="text"
              id="state"
              name="state"
              value="${this.formData.state}"
              placeholder="Greater London"
              class="${this.errors.state ? 'error' : ''}"
              autocomplete="address-level1"
              required
            />
            ${this.errors.state ? `<span class="error-message">${this.errors.state}</span>` : ''}
          </div>

          <div class="form-field">
            <label for="postalCode" class="required">Postcode</label>
            <input
              type="text"
              id="postalCode"
              name="postalCode"
              value="${this.formData.postalCode}"
              placeholder="SW1A 1AA"
              class="${this.errors.postalCode ? 'error' : ''}"
              autocomplete="postal-code"
              required
            />
            ${this.errors.postalCode ? `<span class="error-message">${this.errors.postalCode}</span>` : ''}
          </div>
        </div>
      </div>
    `;

    // Add event listeners
    const inputs = this.shadow.querySelectorAll('input');
    inputs.forEach(input => {
      input.addEventListener('input', (e) => {
        const target = e.target as HTMLInputElement;
        const field = target.name as keyof AddressData;
        this.handleInputChange(field, target.value);
      });
    });
    
  }
}

// Register the custom element
if (!customElements.get('shadow-address-form')) {
  customElements.define('shadow-address-form', ShadowAddressFormElement);
}

// React wrapper component
const ShadowAddressForm: React.FC<ShadowAddressFormProps> = ({
  formData,
  onChange,
  errors,
  title,
  showEmailPhone = true,
  className
}) => {
  const elementRef = useRef<ShadowAddressFormElement>(null);
  

  useEffect(() => {
    if (elementRef.current) {
      elementRef.current.setFormData(formData);
    }
  }, [formData]);

  useEffect(() => {
    if (elementRef.current && errors) {
      elementRef.current.setErrors(errors);
    }
  }, [errors]);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const handleChange = (e: Event) => {
      const customEvent = e as CustomEvent<AddressData>;
      onChange(customEvent.detail);
    };

    element.addEventListener('addresschange', handleChange);
    return () => {
      element.removeEventListener('addresschange', handleChange);
    };
  }, [onChange]);


  return (
    <shadow-address-form
      ref={elementRef}
      title={title}
      show-email-phone={showEmailPhone.toString()}
      class={className}
    />
  );
};

export default ShadowAddressForm;