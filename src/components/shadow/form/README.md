# Shadow DOM Form Components

This directory contains Shadow DOM form components for the SHOT design system. These components provide consistent, styled form elements that match the SHOT brand and design language.

## Available Components

### ShadowTextInput
A text input field with various types (text, email, password, etc.), customizable size, and support for icons, error states, and help text.

```jsx
<ShadowTextInput 
  label="Email Address"
  type="email"
  placeholder="<EMAIL>"
  required={true}
  error="Please enter a valid email"
  icon="mail"
  iconPosition="left"
  onChange={(e) => console.log('Value:', e.detail.value)}
/>
```

### ShadowSelect
A dropdown select field with customizable options, size, error states, and help text.

```jsx
<ShadowSelect 
  label="Sport Type"
  options={[
    { value: 'football', label: 'Football' },
    { value: 'basketball', label: 'Basketball' }
  ]}
  value="football"
  required={true}
  onChange={(e) => console.log('Selected:', e.detail.value)}
/>
```

### ShadowTextarea
A multi-line text input field with customizable rows, size, error states, and help text.

```jsx
<ShadowTextarea 
  label="Team Description"
  placeholder="Enter details about the team"
  rows={4}
  value="Current description text"
  onChange={(e) => console.log('Text:', e.detail.value)}
/>
```

### ShadowCheckbox
A styled checkbox with support for required, disabled, and error states.

```jsx
<ShadowCheckbox 
  label="I agree to terms and conditions"
  checked={true}
  required={true}
  onChange={(e) => console.log('Checked:', e.detail.checked)}
/>
```

### ShadowRadioGroup
A group of radio buttons with vertical or horizontal layout options.

```jsx
<ShadowRadioGroup
  name="availability"
  label="Match Availability"
  options={[
    { value: 'available', label: 'Available' },
    { value: 'unavailable', label: 'Unavailable' }
  ]}
  value="available"
  layout="vertical"
  onChange={(e) => console.log('Selected:', e.detail.value)}
/>
```

### ShadowFormGroup
A container for form fields with optional legend and layout controls (vertical, horizontal, or grid).

```jsx
<ShadowFormGroup legend="Personal Information" layout="grid" columns={2}>
  <ShadowTextInput label="First Name" />
  <ShadowTextInput label="Last Name" />
  <ShadowTextInput label="Email" type="email" />
  <ShadowTextInput label="Phone" type="tel" />
</ShadowFormGroup>
```

## Usage Notes

1. All form components use Shadow DOM for encapsulation
2. Components use SHOT brand colors and fonts from Tailwind config and shot-components.css
3. Consistent styling and behavior across all form elements
4. All components support event handling with custom events
5. Consistent error handling and validation support

## Event Handling

Form components emit custom events with a `detail` object containing:
- `name`: The field name
- `value` or `checked`: The current value
- `originalEvent`: The original DOM event

Example:
```jsx
<ShadowTextInput
  name="firstName"
  onChange={(e) => {
    console.log(e.detail.name); // "firstName"
    console.log(e.detail.value); // Current input value
  }}
/>
```