// ABOUTME: Team performance insights component for coach analytics with completion rates, Four Corners averages, and form guide

import React from 'react';

export interface CategoryScore {
  category: string;
  score: number;
  completionRate: number;
}

export interface FormGuideData {
  playerId: string;
  playerName: string;
  trend: 'up' | 'down' | 'stable';
  improvement: number;
}

export interface TeamInsightsProps {
  teamName: string;
  categoryScores: CategoryScore[];
  formGuide: FormGuideData[];
  onCategoryClick?: (category: string) => void;
  onPlayerClick?: (playerId: string) => void;
  className?: string;
}

export const ShadowTeamInsights: React.FC<TeamInsightsProps> = ({
  teamName,
  categoryScores,
  formGuide,
  onCategoryClick,
  onPlayerClick,
  className = ''
}) => {
  const getCompletionColor = (rate: number): string => {
    if (rate >= 80) return '#2ECC71'; // Green
    if (rate >= 60) return '#F39C12'; // Orange
    return '#E74C3C'; // Red
  };

  const getTrendIcon = (trend: string): string => {
    switch (trend) {
      case 'up': return '↗️';
      case 'down': return '↘️';
      case 'stable': return '➡️';
      default: return '➡️';
    }
  };

  const getTrendColor = (trend: string): string => {
    switch (trend) {
      case 'up': return '#2ECC71';
      case 'down': return '#E74C3C';
      case 'stable': return '#95A5A6';
      default: return '#95A5A6';
    }
  };

  const getCategoryColor = (category: string): string => {
    const lowerCategory = category.toLowerCase();
    switch (lowerCategory) {
      case 'physical': return '#1ABC9C';
      case 'technical': return '#F7B613';
      case 'tactical': return '#6B00DB';
      case 'mental': return '#E74C3C';
      default: return '#95A5A6';
    }
  };

  const getScoreColor = (score: number): string => {
    if (score >= 80) return '#1ABC9C'; // Teal - excellent
    if (score >= 60) return '#F7B613'; // Gold - good
    return '#E74C3C'; // Red - needs work
  };

  // Calculate overall completion rate
  const overallCompletionRate = categoryScores.length > 0
    ? categoryScores.reduce((acc, cat) => acc + cat.completionRate, 0) / categoryScores.length
    : 0;

  // Calculate form guide summary
  const formGuideSummary = {
    improving: formGuide.filter(p => p.trend === 'up').length,
    declining: formGuide.filter(p => p.trend === 'down').length,
    stable: formGuide.filter(p => p.trend === 'stable').length
  };

  return (
    <div className={`shadow-team-insights ${className}`}>
      <style>
        {`
          .shadow-team-insights {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 12px;
            padding: 24px;
            font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, sans-serif;
          }
          
          .insights-header {
            margin-bottom: 24px;
          }
          
          .team-name {
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
            font-size: 20px;
            color: white;
            margin: 0 0 8px 0;
          }
          
          .insights-subtitle {
            font-size: 14px;
            color: #ccc;
            margin: 0;
          }
          
          .completion-overview {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
            text-align: center;
          }
          
          .completion-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 16px;
            margin-bottom: 12px;
          }
          
          .completion-title {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 16px;
            color: white;
            margin: 0;
          }
          
          .completion-percentage {
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
            font-size: 32px;
            color: var(--completion-color);
          }
          
          .completion-bar {
            height: 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            overflow: hidden;
          }
          
          .completion-fill {
            height: 100%;
            background: var(--completion-color);
            border-radius: 6px;
            transition: width 0.6s ease;
          }
          
          .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
            margin-bottom: 24px;
          }
          
          .category-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--category-color, #333);
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
          }
          
          .category-card:hover {
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
          }
          
          .category-name {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 12px;
            color: var(--category-color);
            text-transform: uppercase;
            margin: 0 0 8px 0;
          }
          
          .category-score {
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
            font-size: 24px;
            color: white;
            display: block;
            margin-bottom: 4px;
          }
          
          .category-completion {
            font-size: 11px;
            color: #999;
          }
          
          .form-guide-section {
            border-top: 1px solid #333;
            padding-top: 20px;
          }
          
          .section-title {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 16px;
            color: white;
            margin: 0 0 16px 0;
          }
          
          .form-guide-summary {
            display: flex;
            justify-content: space-around;
            margin-bottom: 16px;
          }
          
          .summary-item {
            text-align: center;
          }
          
          .summary-count {
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
            font-size: 20px;
            color: var(--trend-color);
            display: block;
          }
          
          .summary-label {
            font-size: 12px;
            color: #999;
          }
          
          .player-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
          }
          
          .player-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
          }
          
          .player-item:hover {
            background: rgba(255, 255, 255, 0.06);
          }
          
          .player-info {
            display: flex;
            align-items: center;
            gap: 12px;
          }
          
          .player-name {
            font-family: 'Montserrat', sans-serif;
            font-weight: 500;
            font-size: 14px;
            color: white;
          }
          
          .player-trend {
            display: flex;
            align-items: center;
            gap: 6px;
          }
          
          .trend-icon {
            font-size: 14px;
            color: var(--trend-color);
          }
          
          .improvement-value {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 12px;
            color: var(--trend-color);
          }
          
          /* Mobile responsive */
          @media (max-width: 768px) {
            .shadow-team-insights {
              padding: 16px;
            }
            
            .categories-grid {
              grid-template-columns: repeat(2, 1fr);
            }
            
            .team-name {
              font-size: 18px;
            }
            
            .completion-percentage {
              font-size: 28px;
            }
            
            .category-score {
              font-size: 20px;
            }
          }
          
          /* Print styles */
          @media print {
            .shadow-team-insights {
              border: 1px solid #000;
              background: white;
            }
            
            .category-card,
            .player-item {
              border: 1px solid #000;
              background: white;
            }
            
            .team-name,
            .category-score,
            .player-name {
              color: #000 !important;
            }
          }
        `}
      </style>
      
      <div className="insights-header">
        <h3 className="team-name">{teamName}</h3>
        <p className="insights-subtitle">Team Performance Analytics</p>
      </div>
      
      <div className="completion-overview">
        <div className="completion-header">
          <h4 className="completion-title">Completion Rate</h4>
          <span 
            className="completion-percentage"
            style={{ '--completion-color': getCompletionColor(overallCompletionRate) } as React.CSSProperties}
          >
            {overallCompletionRate.toFixed(0)}%
          </span>
        </div>
        <div className="completion-bar">
          <div 
            className="completion-fill"
            style={{ 
              width: `${overallCompletionRate}%`,
              '--completion-color': getCompletionColor(overallCompletionRate) 
            } as React.CSSProperties}
          />
        </div>
      </div>
      
      <div className="categories-grid">
        {categoryScores.map((category) => (
          <div
            key={category.category}
            className="category-card"
            style={{ '--category-color': getCategoryColor(category.category) } as React.CSSProperties}
            onClick={() => onCategoryClick?.(category.category)}
          >
            <h5 className="category-name">{category.category}</h5>
            <span className="category-score">{category.score}</span>
            <span className="category-completion">{category.completionRate}% complete</span>
          </div>
        ))}
      </div>
      
      <div className="form-guide-section">
        <h4 className="section-title">Form Guide</h4>
        
        <div className="form-guide-summary">
          <div className="summary-item">
            <span 
              className="summary-count"
              style={{ '--trend-color': '#2ECC71' } as React.CSSProperties}
            >
              {formGuideSummary.improving}
            </span>
            <span className="summary-label">Improving</span>
          </div>
          <div className="summary-item">
            <span 
              className="summary-count"
              style={{ '--trend-color': '#95A5A6' } as React.CSSProperties}
            >
              {formGuideSummary.stable}
            </span>
            <span className="summary-label">Stable</span>
          </div>
          <div className="summary-item">
            <span 
              className="summary-count"
              style={{ '--trend-color': '#E74C3C' } as React.CSSProperties}
            >
              {formGuideSummary.declining}
            </span>
            <span className="summary-label">Declining</span>
          </div>
        </div>
        
        <div className="player-list">
          {formGuide.slice(0, 5).map((player) => (
            <div
              key={player.playerId}
              className="player-item"
              onClick={() => onPlayerClick?.(player.playerId)}
            >
              <div className="player-info">
                <span className="player-name">{player.playerName}</span>
              </div>
              <div 
                className="player-trend"
                style={{ '--trend-color': getTrendColor(player.trend) } as React.CSSProperties}
              >
                <span className="trend-icon">{getTrendIcon(player.trend)}</span>
                <span className="improvement-value">
                  {player.improvement > 0 ? '+' : ''}{player.improvement}%
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};