// ABOUTME: Path selection card component for member onboarding with theme-based styling and feature lists

import React from 'react';

export interface PathCardProps {
  icon: string;           // 🎯, 🛡️, 🏆
  title: string;          // "JUST FOR ME", "JOIN A CLUB", "COACH"
  tagline: string;        // "Solo training, instant improve"
  features: string[];     // ["Start now", "AI drills", ...]
  metric: string;         // "23% avg improvement"
  cta: string;           // "Start Now"
  theme: 'teal' | 'purple' | 'gold';
  onClick: () => void;
  disabled?: boolean;
  className?: string;
}

export const ShadowPathCard: React.FC<PathCardProps> = ({
  icon,
  title,
  tagline,
  features,
  metric,
  cta,
  theme,
  onClick,
  disabled = false,
  className = ''
}) => {
  return (
    <div className={`shadow-path-card ${className}`}>
      <style>
        {`
          .shadow-path-card {
            background: #1a1a1a;
            border-radius: 12px;
            border: 2px solid #333;
            padding: 24px;
            display: flex;
            flex-direction: column;
            height: 100%;
            min-height: 400px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
          }
          
          .shadow-path-card:hover:not(.disabled) {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0,0,0,0.3);
          }
          
          .shadow-path-card.disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
          
          /* Theme-based borders and accents */
          .shadow-path-card.theme-teal {
            border-color: #1ABC9C;
          }
          
          .shadow-path-card.theme-teal:hover:not(.disabled) {
            border-color: #16a085;
            box-shadow: 0 8px 24px rgba(26, 188, 156, 0.2);
          }
          
          .shadow-path-card.theme-purple {
            border-color: #6B00DB;
          }
          
          .shadow-path-card.theme-purple:hover:not(.disabled) {
            border-color: #5a00b8;
            box-shadow: 0 8px 24px rgba(107, 0, 219, 0.2);
          }
          
          .shadow-path-card.theme-gold {
            border-color: #F7B613;
          }
          
          .shadow-path-card.theme-gold:hover:not(.disabled) {
            border-color: #d49c0f;
            box-shadow: 0 8px 24px rgba(247, 182, 19, 0.2);
          }
          
          .path-card-header {
            text-align: center;
            margin-bottom: 20px;
          }
          
          .path-card-icon {
            font-size: 48px;
            margin-bottom: 12px;
            display: block;
          }
          
          .path-card-title {
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
            font-size: 20px;
            color: white;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
          }
          
          .path-card-tagline {
            font-family: 'Montserrat', sans-serif;
            font-size: 14px;
            color: #ccc;
            font-weight: 500;
          }
          
          .path-card-features {
            flex: 1;
            margin: 20px 0;
          }
          
          .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-family: 'Montserrat', sans-serif;
            font-size: 14px;
            color: white;
          }
          
          .feature-checkmark {
            color: #2ECC71;
            margin-right: 8px;
            font-weight: bold;
            min-width: 16px;
          }
          
          .path-card-metric {
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            margin-bottom: 20px;
            border-left: 4px solid var(--theme-color);
          }
          
          .metric-icon {
            font-size: 16px;
            margin-right: 8px;
          }
          
          .metric-text {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 14px;
            color: white;
          }
          
          .path-card-cta {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 8px;
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
          }
          
          .path-card-cta.theme-teal {
            background: #1ABC9C;
          }
          
          .path-card-cta.theme-teal:hover:not(:disabled) {
            background: #16a085;
          }
          
          .path-card-cta.theme-purple {
            background: #6B00DB;
          }
          
          .path-card-cta.theme-purple:hover:not(:disabled) {
            background: #5a00b8;
          }
          
          .path-card-cta.theme-gold {
            background: #F7B613;
            color: #000;
          }
          
          .path-card-cta.theme-gold:hover:not(:disabled) {
            background: #d49c0f;
          }
          
          .path-card-cta:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
          
          /* Mobile responsive */
          @media (max-width: 768px) {
            .shadow-path-card {
              min-height: 350px;
              padding: 20px;
            }
            
            .path-card-icon {
              font-size: 40px;
            }
            
            .path-card-title {
              font-size: 18px;
            }
            
            .path-card-tagline {
              font-size: 13px;
            }
            
            .feature-item {
              font-size: 13px;
              margin-bottom: 10px;
            }
            
            .path-card-cta {
              padding: 14px;
              font-size: 15px;
            }
          }
          
          @media (max-width: 480px) {
            .shadow-path-card {
              min-height: 320px;
              padding: 16px;
            }
            
            .path-card-icon {
              font-size: 36px;
            }
            
            .path-card-title {
              font-size: 16px;
            }
          }
        `}
      </style>
      
      <div 
        className={`shadow-path-card theme-${theme} ${disabled ? 'disabled' : ''}`}
        onClick={!disabled ? onClick : undefined}
        style={{'--theme-color': theme === 'teal' ? '#1ABC9C' : theme === 'purple' ? '#6B00DB' : '#F7B613'} as React.CSSProperties}
      >
        <div className="path-card-header">
          <span className="path-card-icon">{icon}</span>
          <h3 className="path-card-title">{title}</h3>
          <p className="path-card-tagline">{tagline}</p>
        </div>
        
        <div className="path-card-features">
          {features.map((feature, index) => (
            <div key={index} className="feature-item">
              <span className="feature-checkmark">✓</span>
              <span>{feature}</span>
            </div>
          ))}
        </div>
        
        <div className="path-card-metric">
          <span className="metric-icon">📈</span>
          <span className="metric-text">{metric}</span>
        </div>
        
        <button 
          className={`path-card-cta theme-${theme}`}
          disabled={disabled}
          onClick={(e) => {
            e.stopPropagation();
            if (!disabled) onClick();
          }}
        >
          {cta}
        </button>
      </div>
    </div>
  );
};