// ABOUTME: Urgent alert component for time-sensitive player actions with countdown and priority styling

import React, { useState, useEffect } from 'react';

export interface UrgentAlertProps {
  type: 'urgent' | 'warning' | 'info';
  title: string;
  message: string;
  countdown?: string;          // "4hrs remaining"
  ctaText: string;            // "Complete Now"
  onAction: () => void;
  onDismiss?: () => void;
  isDismissible?: boolean;
  className?: string;
}

export const ShadowUrgentAlert: React.FC<UrgentAlertProps> = ({
  type,
  title,
  message,
  countdown,
  ctaText,
  onAction,
  onDismiss,
  isDismissible = false,
  className = ''
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isPulsing, setIsPulsing] = useState(type === 'urgent');

  useEffect(() => {
    if (type === 'urgent') {
      const pulseInterval = setInterval(() => {
        setIsPulsing(prev => !prev);
      }, 1500);
      
      return () => clearInterval(pulseInterval);
    }
  }, [type]);

  const handleDismiss = () => {
    if (isDismissible && onDismiss) {
      setIsVisible(false);
      setTimeout(() => onDismiss(), 300);
    }
  };

  if (!isVisible) return null;

  return (
    <div className={`shadow-urgent-alert ${className}`}>
      <style>
        {`
          .shadow-urgent-alert {
            width: 100%;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            position: relative;
            overflow: hidden;
            animation: slideInDown 0.3s ease-out;
          }
          
          @keyframes slideInDown {
            from {
              opacity: 0;
              transform: translateY(-20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
          
          @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(231, 76, 60, 0); }
            100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
          }
          
          /* Type-based styling */
          .alert-urgent {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            border-left: 6px solid #ffffff;
            color: white;
          }
          
          .alert-urgent.pulsing {
            animation: pulse 2s infinite;
          }
          
          .alert-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            border-left: 6px solid #ffffff;
            color: white;
          }
          
          .alert-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border-left: 6px solid #ffffff;
            color: white;
          }
          
          .alert-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 16px;
          }
          
          .alert-text {
            flex: 1;
            min-width: 200px;
          }
          
          .alert-icon {
            font-size: 24px;
            margin-right: 12px;
            vertical-align: middle;
          }
          
          .alert-title {
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
            font-size: 18px;
            margin: 0 0 6px 0;
            display: flex;
            align-items: center;
          }
          
          .alert-message {
            font-family: 'Montserrat', sans-serif;
            font-size: 14px;
            margin: 0 0 8px 0;
            opacity: 0.95;
            line-height: 1.4;
          }
          
          .alert-countdown {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 13px;
            background: rgba(255,255,255,0.2);
            padding: 4px 8px;
            border-radius: 12px;
            display: inline-block;
            margin-left: 36px;
          }
          
          .alert-actions {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-shrink: 0;
          }
          
          .alert-cta {
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
          }
          
          .alert-cta:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-1px);
          }
          
          .alert-cta:active {
            transform: translateY(0);
          }
          
          .dismiss-button {
            background: none;
            border: none;
            color: rgba(255,255,255,0.7);
            font-size: 20px;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            transition: all 0.3s ease;
            line-height: 1;
          }
          
          .dismiss-button:hover {
            color: white;
            background: rgba(255,255,255,0.1);
          }
          
          /* Mobile responsive */
          @media (max-width: 768px) {
            .shadow-urgent-alert {
              padding: 16px;
            }
            
            .alert-content {
              flex-direction: column;
              align-items: stretch;
              gap: 12px;
            }
            
            .alert-text {
              min-width: unset;
            }
            
            .alert-title {
              font-size: 16px;
            }
            
            .alert-message {
              font-size: 13px;
            }
            
            .alert-countdown {
              margin-left: 28px;
              font-size: 12px;
            }
            
            .alert-actions {
              justify-content: stretch;
            }
            
            .alert-cta {
              flex: 1;
              text-align: center;
              padding: 14px;
            }
          }
          
          @media (max-width: 480px) {
            .alert-icon {
              font-size: 20px;
              margin-right: 8px;
            }
            
            .alert-title {
              font-size: 15px;
            }
            
            .alert-countdown {
              margin-left: 24px;
            }
          }
        `}
      </style>
      
      <div className={`shadow-urgent-alert alert-${type} ${isPulsing ? 'pulsing' : ''}`}>
        <div className="alert-content">
          <div className="alert-text">
            <h4 className="alert-title">
              <span className="alert-icon">
                {type === 'urgent' ? '⚡' : type === 'warning' ? '⚠️' : 'ℹ️'}
              </span>
              {title}
            </h4>
            <p className="alert-message">{message}</p>
            {countdown && (
              <span className="alert-countdown">{countdown}</span>
            )}
          </div>
          
          <div className="alert-actions">
            <button 
              className="alert-cta"
              onClick={onAction}
            >
              {ctaText}
            </button>
            
            {isDismissible && (
              <button 
                className="dismiss-button"
                onClick={handleDismiss}
                aria-label="Dismiss alert"
              >
                ×
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};