// ABOUTME: Four Corners performance visualization component with color-coded scores, trends, and interactive drill-down

import React from 'react';

export interface QuadrantData {
  category: string;
  score: number;           // 0-100
  trend: 'up' | 'down' | 'stable';
  color: string;
}

export interface PerformanceQuadrantProps {
  quadrants: QuadrantData[];
  playerName?: string;
  lastUpdated?: string;
  showTrends?: boolean;
  onCategoryClick?: (category: string) => void;
  className?: string;
}

export const ShadowPerformanceQuadrant: React.FC<PerformanceQuadrantProps> = ({
  quadrants,
  playerName,
  lastUpdated,
  showTrends = true,
  onCategoryClick,
  className = ''
}) => {
  const getScoreColor = (score: number): string => {
    if (score >= 0 && score <= 40) return '#E74C3C'; // Red - needs work
    if (score >= 41 && score <= 70) return '#F7B613'; // Gold - developing
    if (score >= 71 && score <= 100) return '#1ABC9C'; // Teal - excellent
    return '#666'; // Default
  };

  const getProgressPercentage = (score: number): number => {
    return Math.min(100, Math.max(0, score));
  };

  const getTrendIcon = (trend: string): string => {
    switch (trend) {
      case 'up': return '↗️';
      case 'down': return '↘️';
      case 'stable': return '➡️';
      default: return '➡️';
    }
  };

  const getTrendColor = (trend: string): string => {
    switch (trend) {
      case 'up': return '#2ECC71';
      case 'down': return '#E74C3C';
      case 'stable': return '#95A5A6';
      default: return '#95A5A6';
    }
  };

  const formatCategoryName = (category: string): string => {
    return category.charAt(0).toUpperCase() + category.slice(1).toLowerCase();
  };

  const getCategoryIcon = (category: string): string => {
    const lowerCategory = category.toLowerCase();
    switch (lowerCategory) {
      case 'physical': return '💪';
      case 'technical': return '⚽';
      case 'tactical': return '🧠';
      case 'mental': return '🎯';
      default: return '📊';
    }
  };

  return (
    <div className={`shadow-performance-quadrant ${className}`}>
      <style>
        {`
          .shadow-performance-quadrant {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 12px;
            padding: 24px;
            font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, sans-serif;
          }
          
          .quadrant-header {
            text-align: center;
            margin-bottom: 24px;
          }
          
          .quadrant-title {
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
            font-size: 20px;
            color: white;
            margin: 0 0 4px 0;
          }
          
          .quadrant-subtitle {
            font-size: 14px;
            color: #ccc;
            margin: 0;
          }
          
          .player-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
          }
          
          .player-name {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 16px;
            color: #1ABC9C;
            margin: 0;
          }
          
          .last-updated {
            font-size: 12px;
            color: #999;
          }
          
          .quadrant-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
          }
          
          .category-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--category-color, #333);
            border-radius: 8px;
            padding: 16px;
            transition: all 0.3s ease;
          }
          
          .category-card.clickable {
            cursor: pointer;
          }
          
          .category-card.clickable:hover {
            transform: translateY(-2px);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
          }
          
          .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
          }
          
          .category-info {
            display: flex;
            align-items: center;
            gap: 8px;
          }
          
          .category-icon {
            font-size: 20px;
          }
          
          .category-name {
            font-family: 'Poppins', sans-serif;
            font-weight: 600;
            font-size: 14px;
            color: white;
            margin: 0;
          }
          
          .category-score {
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
            font-size: 24px;
            color: var(--category-color);
          }
          
          .progress-bar {
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
          }
          
          .progress-fill {
            height: 100%;
            background: var(--category-color);
            border-radius: 4px;
            transition: width 0.6s ease;
          }
          
          .trend-indicator {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-top: 8px;
          }
          
          .trend-icon {
            font-size: 14px;
            color: var(--trend-color);
          }
          
          .trend-text {
            font-size: 12px;
            color: var(--trend-color);
            font-weight: 500;
          }
          
          /* Mobile responsive */
          @media (max-width: 768px) {
            .shadow-performance-quadrant {
              padding: 16px;
            }
            
            .quadrant-grid {
              grid-template-columns: 1fr;
            }
            
            .quadrant-title {
              font-size: 18px;
            }
            
            .category-score {
              font-size: 20px;
            }
          }
          
          /* Animations */
          @keyframes scoreUpdate {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
          }
          
          .category-score {
            animation: scoreUpdate 0.6s ease-out;
          }
          
          /* Print styles */
          @media print {
            .shadow-performance-quadrant {
              border: 1px solid #000;
              background: white;
            }
            
            .category-card {
              border-color: #000 !important;
              background: white;
            }
            
            .category-name,
            .category-score {
              color: #000 !important;
            }
            
            .progress-bar {
              background: #f0f0f0;
            }
          }
        `}
      </style>
      
      <div className="quadrant-header">
        <h3 className="quadrant-title">Performance Overview</h3>
        <p className="quadrant-subtitle">Four Corners Development Tracking</p>
      </div>
      
      {(playerName || lastUpdated) && (
        <div className="player-info">
          {playerName && <h4 className="player-name">{playerName}</h4>}
          {lastUpdated && <span className="last-updated">{lastUpdated}</span>}
        </div>
      )}
      
      <div className="quadrant-grid">
        {(quadrants || []).map((quad) => (
          <div
            key={quad.category}
            className={`category-card ${onCategoryClick ? 'clickable' : ''}`}
            style={{
              '--category-color': quad.color || getScoreColor(quad.score),
              '--trend-color': getTrendColor(quad.trend)
            } as React.CSSProperties}
            onClick={() => onCategoryClick?.(quad.category)}
          >
            <div className="category-header">
              <div className="category-info">
                <span className="category-icon">{getCategoryIcon(quad.category)}</span>
                <h4 className="category-name">{formatCategoryName(quad.category)}</h4>
              </div>
              <span className="category-score">{quad.score}</span>
            </div>
            
            <div className="progress-bar">
              <div 
                className="progress-fill"
                style={{ width: `${getProgressPercentage(quad.score)}%` }}
              />
            </div>
            
            {showTrends && (
              <div className="trend-indicator">
                <span className="trend-icon">{getTrendIcon(quad.trend)}</span>
                <span className="trend-text">
                  {quad.trend === 'up' ? 'Improving' : 
                   quad.trend === 'down' ? 'Declining' : 'Stable'}
                </span>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};