// ABOUTME: Shadow DOM debug modal component for displaying debug information
// Provides isolated styling to avoid global CSS conflicts

import React, { useEffect, useRef } from 'react';

export interface ShadowDebugModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  data: any;
  onDelete?: () => void;
  deleteLabel?: string;
}

export const ShadowDebugModal: React.FC<ShadowDebugModalProps> = ({
  isOpen,
  onClose,
  title = '🐛 Debug Information',
  data,
  onDelete,
  deleteLabel = 'Delete Event and All Related Data'
}) => {
  const shadowHostRef = useRef<HTMLDivElement>(null);
  const [copied, setCopied] = React.useState(false);

  const copyToClipboard = () => {
    const jsonData = JSON.stringify(data, null, 2);
    navigator.clipboard.writeText(jsonData).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }).catch(err => {
      console.error('Failed to copy:', err);
    });
  };

  useEffect(() => {
    if (!isOpen) return;

    // Keyboard shortcut handler
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + C to copy when modal is open
      if ((e.metaKey || e.ctrlKey) && e.key === 'c') {
        e.preventDefault();
        copyToClipboard();
      }
      // Escape to close
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, data, onClose]);

  useEffect(() => {
    if (!shadowHostRef.current || !isOpen) return;
    
    console.log('🐛 DEBUG MODAL: Rendering with onDelete =', !!onDelete, 'deleteLabel =', deleteLabel);
    if (onDelete) {
      console.log('🗑️ DELETE BUTTON: Should be visible in modal');
    } else {
      console.log('❌ DELETE BUTTON: Not rendered - onDelete prop not provided');
    }

    // Create or get shadow root
    let shadowRoot = shadowHostRef.current.shadowRoot;
    if (!shadowRoot) {
      shadowRoot = shadowHostRef.current.attachShadow({ mode: 'open' });
    }

    // Clear and render content
    shadowRoot.innerHTML = `
      <style>
        :host {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 999999;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .modal-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.9);
          backdrop-filter: blur(5px);
        }

        .modal-content {
          position: relative;
          background: #1a1a1a;
          border: 2px solid #DC2626;
          border-radius: 12px;
          padding: 24px;
          max-width: 90vw;
          max-height: 80vh;
          width: 600px;
          overflow: visible;
          display: flex;
          flex-direction: column;
          box-shadow: 
            0 0 40px rgba(220, 38, 38, 0.3),
            0 20px 60px rgba(0, 0, 0, 0.8);
          margin: 20px;
        }

        .modal-header {
          display: flex;
          align-items: center;
          margin-bottom: 20px;
          padding-bottom: 16px;
          border-bottom: 1px solid #333;
          gap: 12px;
        }
        
        .header-content {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 12px;
        }
        
        .header-actions {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .modal-title {
          color: #DC2626;
          font-size: 20px;
          font-weight: 600;
          font-family: 'Poppins', system-ui, sans-serif;
          margin: 0;
        }

        .close-button {
          background: #DC2626;
          color: white;
          border: none;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          font-size: 20px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;
          font-weight: bold;
        }

        .close-button:hover {
          background: #B91C1C;
          transform: scale(1.1);
        }
        
        .copy-button {
          background: #1a1a1a;
          color: #DC2626;
          border: 1px solid #DC2626;
          padding: 6px 16px;
          border-radius: 6px;
          font-size: 13px;
          cursor: pointer;
          font-family: 'Poppins', system-ui, sans-serif;
          font-weight: 600;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 6px;
        }
        
        .copy-button:hover {
          background: #DC2626;
          color: white;
        }
        
        .copy-button.copied {
          background: #1ABC9C;
          border-color: #1ABC9C;
          color: white;
        }
        
        .shortcut-hint {
          font-size: 11px;
          color: #666;
          margin-left: 4px;
        }

        .modal-body {
          flex: 1;
          overflow-y: auto;
          overflow-x: auto;
          background: #0f0f0f;
          border: 1px solid #333;
          border-radius: 8px;
          padding: 16px;
          min-height: 0;
          max-height: calc(60vh - 200px);
        }

        .debug-content {
          color: #E5E5E5;
          font-size: 13px;
          font-family: 'Monaco', 'Consolas', 'Courier New', monospace;
          line-height: 1.5;
          white-space: pre-wrap;
          word-break: break-word;
          margin: 0;
        }

        /* Scrollbar styling */
        .modal-body::-webkit-scrollbar {
          width: 10px;
        }

        .modal-body::-webkit-scrollbar-track {
          background: #1a1a1a;
          border-radius: 5px;
        }

        .modal-body::-webkit-scrollbar-thumb {
          background: #DC2626;
          border-radius: 5px;
        }

        .modal-body::-webkit-scrollbar-thumb:hover {
          background: #B91C1C;
        }
        
        .delete-button {
          background: #1a1a1a;
          color: #DC2626;
          border: 1px solid #DC2626;
          padding: 6px 16px;
          border-radius: 6px;
          font-size: 13px;
          cursor: pointer;
          font-family: 'Poppins', system-ui, sans-serif;
          font-weight: 600;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 6px;
        }
        
        .delete-button:hover {
          background: #DC2626;
          color: white;
        }
        
        .delete-text {
          font-size: 13px;
        }
        
        @media (max-width: 480px) {
          .delete-text {
            display: none;
          }
        }
      </style>
      
      <div class="modal-overlay" onclick="this.getRootNode().host.dispatchEvent(new Event('close'))"></div>
      
      <div class="modal-content">
        <div class="modal-header">
          <div class="header-content">
            <h3 class="modal-title">${title}</h3>
          </div>
          <div class="header-actions">
            <button class="copy-button ${copied ? 'copied' : ''}" onclick="this.getRootNode().host.dispatchEvent(new Event('copy'))">
              ${copied ? '✓ Copied!' : '📋 Copy'}
              <span class="shortcut-hint">${navigator.platform.includes('Mac') ? '⌘C' : 'Ctrl+C'}</span>
            </button>
            ${onDelete ? `
              <button class="delete-button" onclick="this.getRootNode().host.dispatchEvent(new Event('delete'))" title="${deleteLabel}">
                <span style="font-size: 16px">🗑️</span>
                <span class="delete-text">Delete</span>
              </button>
            ` : ''}
            <button class="close-button" onclick="this.getRootNode().host.dispatchEvent(new Event('close'))">×</button>
          </div>
        </div>
        <div class="modal-body">
          <pre class="debug-content">${JSON.stringify(data, null, 2)}</pre>
        </div>
      </div>
    `;

    // Add event listeners
    const handleClose = () => onClose();
    const handleCopy = () => copyToClipboard();
    const handleDelete = () => {
      if (onDelete) {
        onClose(); // Close the modal first
        onDelete(); // Then trigger the delete
      }
    };
    
    shadowHostRef.current.addEventListener('close', handleClose);
    shadowHostRef.current.addEventListener('copy', handleCopy);
    shadowHostRef.current.addEventListener('delete', handleDelete);

    return () => {
      shadowHostRef.current?.removeEventListener('close', handleClose);
      shadowHostRef.current?.removeEventListener('copy', handleCopy);
      shadowHostRef.current?.removeEventListener('delete', handleDelete);
    };
  }, [isOpen, onClose, title, data, copied, onDelete, deleteLabel]);

  if (!isOpen) return null;

  return <div ref={shadowHostRef} data-debug-modal-open="true" />;
};