// ABOUTME: Enhanced product card component with Shadow DOM for e-commerce
// Displays product information with add to cart functionality and variant support

import React, { useEffect, useRef, useState } from 'react';
import { BigCommerceProduct } from '../../../services/BigCommerceService';
import { useEnhancedShoppingCart } from '../../../contexts/EnhancedShoppingCartContext';
import { useCurrency } from '../../../contexts/CurrencyContext';

interface ShadowProductCardProps {
  product: BigCommerceProduct;
  onClick?: (product: BigCommerceProduct) => void;
  showQuickView?: boolean;
  className?: string;
}

export const ShadowProductCard: React.FC<ShadowProductCardProps> = ({ 
  product,
  onClick,
  showQuickView = true,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { addToCart, isInCart } = useEnhancedShoppingCart();
  const { currency, formatPrice } = useCurrency();
  const [isAdding, setIsAdding] = useState(false);
  const [addedMessage, setAddedMessage] = useState(false);

  useEffect(() => {
    if (!containerRef.current) return;

    const host = document.createElement('div');
    const shadow = host.attachShadow({ mode: 'open' });

    // Calculate price display
    const currentPrice = product.sale_price || product.price;
    const hasDiscount = product.sale_price && product.sale_price < product.price;
    const discountPercent = hasDiscount 
      ? Math.round((1 - product.sale_price! / product.price) * 100)
      : 0;

    // Get primary image
    const primaryImage = product.images?.find(img => img.is_thumbnail)?.url_standard 
      || product.images?.[0]?.url_standard 
      || '/placeholder-product.png';

    // Check stock status
    const isOutOfStock = product.inventory_level === 0;
    const isLowStock = product.inventory_level && product.inventory_level < 10;

    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
        
        :host {
          display: block;
          width: 100%;
        }

        .product-card {
          background: #0F0F0F;
          border: 1px solid #1a1a1a;
          border-radius: 12px;
          overflow: hidden;
          transition: all 0.3s ease;
          position: relative;
          cursor: pointer;
        }

        .product-card:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 24px rgba(0, 0, 0, 0.4);
          border-color: #2a2a2a;
        }

        .image-container {
          position: relative;
          width: 100%;
          padding-bottom: 100%; /* 1:1 aspect ratio */
          overflow: hidden;
          background: #0A0A0A;
        }

        .product-image {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        .product-card:hover .product-image {
          transform: scale(1.05);
        }

        .badges {
          position: absolute;
          top: 12px;
          left: 12px;
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .badge {
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 11px;
          font-weight: 600;
          letter-spacing: 0.5px;
          font-family: 'Poppins', sans-serif;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .badge.sale {
          background: rgba(220, 38, 38, 0.9);
          color: white;
        }

        .badge.low-stock {
          background: rgba(251, 146, 60, 0.9);
          color: white;
        }

        .badge.out-of-stock {
          background: rgba(107, 114, 128, 0.9);
          color: white;
        }

        .quick-actions {
          position: absolute;
          top: 12px;
          right: 12px;
          display: flex;
          flex-direction: column;
          gap: 8px;
          opacity: 0;
          transform: translateY(-10px);
          transition: all 0.3s ease;
        }

        .product-card:hover .quick-actions {
          opacity: 1;
          transform: translateY(0);
        }

        .action-button {
          width: 36px;
          height: 36px;
          background: rgba(0, 0, 0, 0.8);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;
          backdrop-filter: blur(10px);
        }

        .action-button:hover {
          background: rgba(26, 188, 156, 0.9);
          border-color: #1ABC9C;
          transform: scale(1.1);
        }

        .action-button svg {
          width: 18px;
          height: 18px;
          stroke: white;
          stroke-width: 2;
          fill: none;
        }

        .content {
          padding: 16px;
        }

        .brand {
          font-size: 11px;
          color: #6B7280;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-bottom: 4px;
          font-family: 'Poppins', sans-serif;
        }

        .title {
          font-size: 14px;
          font-weight: 500;
          color: #ffffff;
          margin-bottom: 8px;
          line-height: 1.4;
          font-family: 'Poppins', sans-serif;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          min-height: 40px;
        }

        .price-row {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 12px;
        }

        .price {
          font-size: 18px;
          font-weight: 600;
          color: #1ABC9C;
          font-family: 'Poppins', sans-serif;
        }

        .original-price {
          font-size: 14px;
          color: #6B7280;
          text-decoration: line-through;
        }

        .add-to-cart {
          width: 100%;
          padding: 10px;
          background: #1ABC9C;
          color: #0A0A0A;
          border: none;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 600;
          font-family: 'Poppins', sans-serif;
          cursor: pointer;
          transition: all 0.2s ease;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .add-to-cart:hover:not(:disabled) {
          background: #15A085;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(26, 188, 156, 0.3);
        }

        .add-to-cart:active:not(:disabled) {
          transform: translateY(0);
        }

        .add-to-cart:disabled {
          background: #374151;
          color: #9CA3AF;
          cursor: not-allowed;
        }
        
        .add-to-cart.adding {
          opacity: 0.7;
          cursor: wait;
        }
        
        .add-to-cart.added {
          background: #10B981;
          color: white;
        }

        .rating {
          display: flex;
          align-items: center;
          gap: 4px;
          margin-bottom: 8px;
        }

        .stars {
          display: flex;
          gap: 2px;
        }

        .star {
          width: 12px;
          height: 12px;
          fill: #FCD34D;
        }

        .star.empty {
          fill: #374151;
        }

        .rating-count {
          font-size: 12px;
          color: #6B7280;
          font-family: 'Poppins', sans-serif;
        }
      </style>
    `;

    // Use formatPrice from currency context
    const formatPriceInShadow = (price: number) => {
      return formatPrice(price);
    };

    shadow.innerHTML = `
      ${styles}
      <div class="product-card">
        <div class="image-container">
          <img src="${primaryImage}" alt="${product.name}" class="product-image" />
          
          <div class="badges">
            ${hasDiscount ? `<span class="badge sale">-${discountPercent}%</span>` : ''}
            ${isOutOfStock ? '<span class="badge out-of-stock">OUT OF STOCK</span>' : ''}
            ${isLowStock && !isOutOfStock ? `<span class="badge low-stock">${product.inventory_level} LEFT</span>` : ''}
          </div>

          ${showQuickView ? `
            <div class="quick-actions">
              <button class="action-button quick-view" title="Quick View">
                <svg viewBox="0 0 24 24">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
              </button>
            </div>
          ` : ''}
        </div>

        <div class="content">
          ${product.brand_name ? `<div class="brand">${product.brand_name}</div>` : ''}
          
          <h3 class="title">${product.name}</h3>

          <div class="price-row">
            <span class="price">${formatPriceInShadow(currentPrice)}</span>
            ${hasDiscount ? `<span class="original-price">${formatPriceInShadow(product.price)}</span>` : ''}
          </div>

          <button class="add-to-cart ${isAdding ? 'adding' : ''} ${addedMessage ? 'added' : ''}" ${isOutOfStock || isAdding ? 'disabled' : ''}>
            ${isOutOfStock ? 'OUT OF STOCK' : addedMessage ? 'ADDED TO CART' : isAdding ? 'ADDING...' : isInCart(product.id) ? 'IN CART' : 'ADD TO CART'}
          </button>
        </div>
      </div>
    `;

    // Add event listeners
    const card = shadow.querySelector('.product-card');
    const addButton = shadow.querySelector('.add-to-cart');
    const quickViewButton = shadow.querySelector('.quick-view');

    if (card && onClick) {
      card.addEventListener('click', (e) => {
        // Don't trigger if clicking on buttons
        if ((e.target as HTMLElement).closest('button')) return;
        onClick(product);
      });
    }

    if (addButton && !isOutOfStock) {
      addButton.addEventListener('click', async (e) => {
        e.stopPropagation();
        
        if (isInCart(product.id)) {
          // If already in cart, clicking takes user to product page
          if (onClick) {
            onClick(product);
          }
          return;
        }
        
        setIsAdding(true);
        try {
          await addToCart(product);
          setAddedMessage(true);
          // Reset the message after 2 seconds
          setTimeout(() => {
            setAddedMessage(false);
          }, 2000);
        } catch (error) {
          console.error('Failed to add to cart:', error);
        } finally {
          setIsAdding(false);
        }
      });
    }

    if (quickViewButton && onClick) {
      quickViewButton.addEventListener('click', (e) => {
        e.stopPropagation();
        onClick(product);
      });
    }

    containerRef.current.appendChild(host);

    return () => {
      if (containerRef.current && host.parentNode) {
        containerRef.current.removeChild(host);
      }
    };
  }, [product, onClick, showQuickView, isAdding, addedMessage, isInCart, addToCart]);

  return <div ref={containerRef} className={className} />;
};