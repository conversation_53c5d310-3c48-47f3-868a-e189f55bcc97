// ABOUTME: Shadow DOM component for displaying countdown timer for product drops
// Shows time remaining until drop goes live with dynamic updates

import React, { useEffect, useRef, useState } from 'react';

interface ShadowDropCountdownProps {
  dropDate: Date;
  onDropLive?: () => void;
  variant?: 'large' | 'compact';
  className?: string;
}

interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

const ShadowDropCountdown: React.FC<ShadowDropCountdownProps> = ({
  dropDate,
  onDropLive,
  variant = 'large',
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining | null>(null);
  const [isLive, setIsLive] = useState(false);

  useEffect(() => {
    const calculateTimeRemaining = (): TimeRemaining | null => {
      const now = new Date().getTime();
      const dropTime = dropDate.getTime();
      const distance = dropTime - now;

      if (distance < 0) {
        return null;
      }

      return {
        days: Math.floor(distance / (1000 * 60 * 60 * 24)),
        hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
        minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
        seconds: Math.floor((distance % (1000 * 60)) / 1000)
      };
    };

    // Initial calculation
    const initial = calculateTimeRemaining();
    setTimeRemaining(initial);
    setIsLive(initial === null);

    // Update every second
    const timer = setInterval(() => {
      const remaining = calculateTimeRemaining();
      setTimeRemaining(remaining);
      
      if (remaining === null && !isLive) {
        setIsLive(true);
        onDropLive?.();
        clearInterval(timer);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [dropDate, onDropLive, isLive]);

  useEffect(() => {
    if (!containerRef.current) return;

    const host = document.createElement('div');
    host.className = className;
    const shadow = host.attachShadow({ mode: 'open' });

    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap');
        
        :host {
          display: block;
          width: 100%;
        }

        .countdown-container {
          background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(0, 0, 0, 0.7));
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 16px;
          padding: 24px;
          text-align: center;
          position: relative;
          overflow: hidden;
        }

        .countdown-container.compact {
          padding: 16px;
          border-radius: 12px;
        }

        /* Animated background effect */
        .countdown-container::before {
          content: '';
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: linear-gradient(
            45deg,
            transparent,
            rgba(28, 224, 232, 0.1),
            transparent,
            rgba(155, 93, 229, 0.1),
            transparent
          );
          animation: shimmer 3s linear infinite;
          pointer-events: none;
        }

        @keyframes shimmer {
          0% {
            transform: translateX(-100%) translateY(-100%) rotate(45deg);
          }
          100% {
            transform: translateX(100%) translateY(100%) rotate(45deg);
          }
        }

        .countdown-label {
          font-family: 'Poppins', sans-serif;
          font-size: 14px;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.7);
          text-transform: uppercase;
          letter-spacing: 2px;
          margin-bottom: 16px;
          position: relative;
          z-index: 1;
        }

        .compact .countdown-label {
          font-size: 12px;
          margin-bottom: 12px;
        }

        .time-blocks {
          display: flex;
          justify-content: center;
          gap: 16px;
          position: relative;
          z-index: 1;
        }

        .compact .time-blocks {
          gap: 12px;
        }

        .time-block {
          display: flex;
          flex-direction: column;
          align-items: center;
        }

        .time-value {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          padding: 16px 20px;
          min-width: 80px;
          position: relative;
          overflow: hidden;
        }

        .compact .time-value {
          padding: 12px 16px;
          min-width: 60px;
          border-radius: 8px;
        }

        .time-value::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            180deg,
            rgba(255, 255, 255, 0.1),
            transparent
          );
          pointer-events: none;
        }

        .time-number {
          font-family: 'Poppins', sans-serif;
          font-size: 36px;
          font-weight: 700;
          color: white;
          line-height: 1;
          text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .compact .time-number {
          font-size: 24px;
        }

        .time-unit {
          font-family: 'Poppins', sans-serif;
          font-size: 12px;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.5);
          text-transform: uppercase;
          margin-top: 8px;
          letter-spacing: 1px;
        }

        .compact .time-unit {
          font-size: 10px;
          margin-top: 4px;
        }

        .time-separator {
          font-family: 'Poppins', sans-serif;
          font-size: 24px;
          font-weight: 700;
          color: rgba(255, 255, 255, 0.3);
          align-self: center;
          margin-bottom: 24px;
          animation: pulse 1s ease-in-out infinite;
        }

        .compact .time-separator {
          font-size: 18px;
          margin-bottom: 16px;
        }

        @keyframes pulse {
          0%, 100% {
            opacity: 0.3;
          }
          50% {
            opacity: 0.7;
          }
        }

        /* Live state */
        .drop-live {
          font-family: 'Poppins', sans-serif;
          font-size: 32px;
          font-weight: 700;
          background: linear-gradient(135deg, #1CE0E8, #9B5DE5);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          animation: glow 2s ease-in-out infinite alternate;
          position: relative;
          z-index: 1;
        }

        .compact .drop-live {
          font-size: 24px;
        }

        @keyframes glow {
          from {
            text-shadow: 0 0 10px rgba(28, 224, 232, 0.5);
          }
          to {
            text-shadow: 0 0 20px rgba(155, 93, 229, 0.8);
          }
        }

        .live-indicator {
          display: inline-block;
          width: 12px;
          height: 12px;
          background: #1CE0E8;
          border-radius: 50%;
          margin-left: 12px;
          animation: live-pulse 1s ease-in-out infinite;
        }

        @keyframes live-pulse {
          0% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(28, 224, 232, 0.7);
          }
          70% {
            transform: scale(1.1);
            box-shadow: 0 0 0 10px rgba(28, 224, 232, 0);
          }
          100% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(28, 224, 232, 0);
          }
        }

        /* Mobile responsiveness */
        @media (max-width: 480px) {
          .time-blocks {
            gap: 8px;
          }
          
          .time-value {
            padding: 12px 14px;
            min-width: 60px;
          }
          
          .time-number {
            font-size: 28px;
          }
          
          .time-separator {
            font-size: 20px;
          }
        }
      </style>
    `;

    const renderTimeBlock = (value: number, unit: string) => {
      const displayValue = value.toString().padStart(2, '0');
      return `
        <div class="time-block">
          <div class="time-value">
            <div class="time-number">${displayValue}</div>
          </div>
          <div class="time-unit">${unit}</div>
        </div>
      `;
    };

    const renderContent = () => {
      if (isLive) {
        return `
          <div class="drop-live">
            DROP IS LIVE!
            <span class="live-indicator"></span>
          </div>
        `;
      }

      if (!timeRemaining) {
        return '<div class="countdown-label">Loading...</div>';
      }

      return `
        <div class="countdown-label">Drop Starts In</div>
        <div class="time-blocks">
          ${renderTimeBlock(timeRemaining.days, 'Days')}
          <div class="time-separator">:</div>
          ${renderTimeBlock(timeRemaining.hours, 'Hours')}
          <div class="time-separator">:</div>
          ${renderTimeBlock(timeRemaining.minutes, 'Min')}
          <div class="time-separator">:</div>
          ${renderTimeBlock(timeRemaining.seconds, 'Sec')}
        </div>
      `;
    };

    shadow.innerHTML = `
      ${styles}
      <div class="countdown-container ${variant}">
        ${renderContent()}
      </div>
    `;

    containerRef.current.innerHTML = '';
    containerRef.current.appendChild(host);

    return () => {
      containerRef.current?.removeChild(host);
    };
  }, [timeRemaining, isLive, variant, className]);

  return <div ref={containerRef} />;
};

export default ShadowDropCountdown;