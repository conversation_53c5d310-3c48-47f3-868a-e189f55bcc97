// ABOUTME: Full shopping cart page component with Shadow DOM
// Displays cart items, totals, and checkout button in a full page layout

import React, { useEffect, useRef } from 'react';
import { CartItem } from '../../../contexts/EnhancedShoppingCartContext';

interface ShadowCartPageProps {
  items: CartItem[];
  subtotal: number;
  taxTotal: number;
  shippingTotal: number;
  discountTotal: number;
  grandTotal: number;
  onUpdateQuantity?: (itemId: string, quantity: number) => void;
  onRemoveItem?: (itemId: string) => void;
  onCheckout?: () => void;
  onContinueShopping?: () => void;
  className?: string;
}

export const ShadowCartPage: React.FC<ShadowCartPageProps> = ({ 
  items,
  subtotal,
  taxTotal,
  shippingTotal,
  discountTotal,
  grandTotal,
  onUpdateQuantity,
  onRemoveItem,
  onCheckout,
  onContinueShopping,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const host = document.createElement('div');
    const shadow = host.attachShadow({ mode: 'open' });

    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
        
        :host {
          display: block;
          width: 100%;
        }

        .cart-page {
          background: #0A0A0A;
          min-height: 100vh;
          color: white;
          font-family: 'Poppins', sans-serif;
        }

        .header {
          padding: 24px;
          border-bottom: 1px solid #1a1a1a;
        }

        .header-content {
          max-width: 1200px;
          margin: 0 auto;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .page-title {
          font-size: 28px;
          font-weight: 600;
        }

        .item-count {
          font-size: 16px;
          color: #6B7280;
        }

        .content {
          max-width: 1200px;
          margin: 0 auto;
          padding: 24px;
          display: grid;
          grid-template-columns: 1fr 400px;
          gap: 32px;
        }

        @media (max-width: 768px) {
          .content {
            grid-template-columns: 1fr;
          }
        }

        .empty-cart {
          grid-column: 1 / -1;
          text-align: center;
          padding: 80px 24px;
        }

        .empty-icon {
          width: 120px;
          height: 120px;
          margin: 0 auto 24px;
          background: #1a1a1a;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .empty-icon svg {
          width: 60px;
          height: 60px;
          stroke: #6B7280;
          stroke-width: 1.5;
          fill: none;
        }

        .empty-title {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 12px;
        }

        .empty-message {
          font-size: 16px;
          color: #6B7280;
          margin-bottom: 32px;
        }

        .continue-shopping {
          padding: 14px 28px;
          background: #1ABC9C;
          color: #0A0A0A;
          border: none;
          border-radius: 8px;
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .continue-shopping:hover {
          background: #15A085;
          transform: translateY(-2px);
          box-shadow: 0 8px 16px rgba(26, 188, 156, 0.3);
        }

        .cart-items {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .cart-item {
          background: #0F0F0F;
          border: 1px solid #1a1a1a;
          border-radius: 12px;
          padding: 20px;
          display: grid;
          grid-template-columns: 120px 1fr;
          gap: 20px;
          transition: all 0.2s ease;
        }

        .cart-item:hover {
          border-color: #2a2a2a;
        }

        .item-image {
          width: 120px;
          height: 120px;
          object-fit: cover;
          border-radius: 8px;
          background: #1a1a1a;
        }

        .item-details {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: start;
        }

        .item-title {
          font-size: 16px;
          font-weight: 500;
          line-height: 1.4;
          flex: 1;
          padding-right: 16px;
        }

        .remove-button {
          width: 32px;
          height: 32px;
          background: transparent;
          border: 1px solid #374151;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .remove-button:hover {
          background: #DC2626;
          border-color: #DC2626;
        }

        .remove-button svg {
          width: 16px;
          height: 16px;
          stroke: #9CA3AF;
          stroke-width: 2;
        }

        .remove-button:hover svg {
          stroke: white;
        }

        .item-variant {
          font-size: 14px;
          color: #6B7280;
        }

        .item-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .quantity-controls {
          display: flex;
          align-items: center;
          background: #1a1a1a;
          border-radius: 8px;
          overflow: hidden;
        }

        .quantity-button {
          width: 36px;
          height: 36px;
          background: transparent;
          border: none;
          color: white;
          font-size: 18px;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .quantity-button:hover {
          background: #2a2a2a;
        }

        .quantity-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .quantity-value {
          width: 50px;
          text-align: center;
          font-size: 14px;
          font-weight: 500;
        }

        .item-price {
          text-align: right;
        }

        .current-price {
          font-size: 18px;
          font-weight: 600;
          color: #1ABC9C;
        }

        .original-price {
          font-size: 14px;
          color: #6B7280;
          text-decoration: line-through;
        }

        .order-summary {
          background: #0F0F0F;
          border: 1px solid #1a1a1a;
          border-radius: 12px;
          padding: 24px;
          height: fit-content;
          position: sticky;
          top: 24px;
        }

        .summary-title {
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 24px;
          padding-bottom: 16px;
          border-bottom: 1px solid #1a1a1a;
        }

        .summary-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          font-size: 14px;
        }

        .summary-label {
          color: #9CA3AF;
        }

        .summary-value {
          font-weight: 500;
        }

        .summary-discount {
          color: #10B981;
        }

        .summary-divider {
          height: 1px;
          background: #1a1a1a;
          margin: 20px 0;
        }

        .summary-total {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 20px;
          font-weight: 600;
          margin-bottom: 24px;
        }

        .checkout-button {
          width: 100%;
          padding: 16px;
          background: #1ABC9C;
          color: #0A0A0A;
          border: none;
          border-radius: 8px;
          font-size: 16px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .checkout-button:hover {
          background: #15A085;
          transform: translateY(-2px);
          box-shadow: 0 8px 16px rgba(26, 188, 156, 0.3);
        }

        .checkout-button:disabled {
          background: #374151;
          color: #9CA3AF;
          cursor: not-allowed;
          transform: none;
          box-shadow: none;
        }

        .shipping-note {
          font-size: 12px;
          color: #6B7280;
          text-align: center;
          margin-top: 12px;
        }

        .promo-section {
          margin-top: 24px;
          padding-top: 24px;
          border-top: 1px solid #1a1a1a;
        }

        .promo-label {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 8px;
        }

        .promo-input-group {
          display: flex;
          gap: 8px;
        }

        .promo-input {
          flex: 1;
          padding: 10px 12px;
          background: #1a1a1a;
          border: 1px solid #2a2a2a;
          border-radius: 6px;
          color: white;
          font-size: 14px;
          font-family: 'Poppins', sans-serif;
        }

        .promo-input::placeholder {
          color: #6B7280;
        }

        .promo-input:focus {
          outline: none;
          border-color: #1ABC9C;
        }

        .apply-button {
          padding: 10px 16px;
          background: transparent;
          border: 1px solid #1ABC9C;
          border-radius: 6px;
          color: #1ABC9C;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .apply-button:hover {
          background: #1ABC9C;
          color: #0A0A0A;
        }
      </style>
    `;

    const formatPrice = (price: number) => {
      return new Intl.NumberFormat('en-GB', {
        style: 'currency',
        currency: 'GBP'
      }).format(price);
    };

    shadow.innerHTML = `
      ${styles}
      <div class="cart-page">
        <div class="header">
          <div class="header-content">
            <h1 class="page-title">Shopping Cart</h1>
            <span class="item-count">${items.length} ${items.length === 1 ? 'item' : 'items'}</span>
          </div>
        </div>

        <div class="content">
          ${items.length === 0 ? `
            <div class="empty-cart">
              <div class="empty-icon">
                <svg viewBox="0 0 24 24">
                  <circle cx="9" cy="21" r="1"></circle>
                  <circle cx="20" cy="21" r="1"></circle>
                  <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                </svg>
              </div>
              <h2 class="empty-title">Your cart is empty</h2>
              <p class="empty-message">Add some products to get started!</p>
              <button class="continue-shopping">Continue Shopping</button>
            </div>
          ` : `
            <div class="cart-items">
              ${items.map(item => {
                const hasDiscount = item.originalPrice && item.originalPrice > item.price;
                return `
                  <div class="cart-item" data-item-id="${item.id}">
                    <img 
                      src="${item.product.images?.[0]?.url_thumbnail || '/placeholder-product.png'}" 
                      alt="${item.product.name}" 
                      class="item-image"
                    />
                    <div class="item-details">
                      <div class="item-header">
                        <h3 class="item-title">${item.product.name}</h3>
                        <button class="remove-button" data-item-id="${item.id}">
                          <svg viewBox="0 0 24 24" fill="none">
                            <path d="M6 6L18 18M6 18L18 6" />
                          </svg>
                        </button>
                      </div>
                      
                      ${item.variant ? `
                        <div class="item-variant">
                          ${item.variant.option_values?.map(v => v.label).join(' / ')}
                        </div>
                      ` : ''}
                      
                      <div class="item-footer">
                        <div class="quantity-controls">
                          <button class="quantity-button decrease" data-item-id="${item.id}" ${item.quantity <= 1 ? 'disabled' : ''}>−</button>
                          <span class="quantity-value">${item.quantity}</span>
                          <button class="quantity-button increase" data-item-id="${item.id}">+</button>
                        </div>
                        
                        <div class="item-price">
                          <div class="current-price">${formatPrice(item.price * item.quantity)}</div>
                          ${hasDiscount ? `
                            <div class="original-price">${formatPrice(item.originalPrice! * item.quantity)}</div>
                          ` : ''}
                        </div>
                      </div>
                    </div>
                  </div>
                `;
              }).join('')}
            </div>

            <div class="order-summary">
              <h2 class="summary-title">Order Summary</h2>
              
              <div class="summary-row">
                <span class="summary-label">Subtotal</span>
                <span class="summary-value">${formatPrice(subtotal)}</span>
              </div>
              
              ${discountTotal > 0 ? `
                <div class="summary-row">
                  <span class="summary-label">Discount</span>
                  <span class="summary-value summary-discount">-${formatPrice(discountTotal)}</span>
                </div>
              ` : ''}
              
              <div class="summary-row">
                <span class="summary-label">Shipping</span>
                <span class="summary-value">${shippingTotal === 0 ? 'FREE' : formatPrice(shippingTotal)}</span>
              </div>
              
              <div class="summary-row">
                <span class="summary-label">Tax</span>
                <span class="summary-value">${formatPrice(taxTotal)}</span>
              </div>
              
              <div class="summary-divider"></div>
              
              <div class="summary-total">
                <span>Total</span>
                <span>${formatPrice(grandTotal)}</span>
              </div>
              
              <button class="checkout-button" ${items.length === 0 ? 'disabled' : ''}>
                Proceed to Checkout
              </button>
              
              ${shippingTotal === 0 && subtotal > 50 ? `
                <p class="shipping-note">🚚 Congratulations! You qualify for free shipping.</p>
              ` : subtotal < 50 ? `
                <p class="shipping-note">Add ${formatPrice(50 - subtotal)} more for free shipping</p>
              ` : ''}
              
              <div class="promo-section">
                <div class="promo-label">Promo Code</div>
                <div class="promo-input-group">
                  <input type="text" class="promo-input" placeholder="Enter code" />
                  <button class="apply-button">Apply</button>
                </div>
              </div>
            </div>
          `}
        </div>
      </div>
    `;

    // Add event listeners
    const continueBtn = shadow.querySelector('.continue-shopping');
    if (continueBtn && onContinueShopping) {
      continueBtn.addEventListener('click', onContinueShopping);
    }

    // Remove item buttons
    const removeButtons = shadow.querySelectorAll('.remove-button');
    removeButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const itemId = (e.currentTarget as HTMLElement).dataset.itemId;
        if (itemId && onRemoveItem) {
          onRemoveItem(itemId);
        }
      });
    });

    // Quantity controls
    const decreaseButtons = shadow.querySelectorAll('.quantity-button.decrease');
    const increaseButtons = shadow.querySelectorAll('.quantity-button.increase');

    decreaseButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const itemId = (e.currentTarget as HTMLElement).dataset.itemId;
        const item = items.find(i => i.id === itemId);
        if (itemId && item && onUpdateQuantity) {
          onUpdateQuantity(itemId, Math.max(1, item.quantity - 1));
        }
      });
    });

    increaseButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const itemId = (e.currentTarget as HTMLElement).dataset.itemId;
        const item = items.find(i => i.id === itemId);
        if (itemId && item && onUpdateQuantity) {
          onUpdateQuantity(itemId, item.quantity + 1);
        }
      });
    });

    // Checkout button
    const checkoutBtn = shadow.querySelector('.checkout-button');
    if (checkoutBtn && onCheckout && items.length > 0) {
      checkoutBtn.addEventListener('click', onCheckout);
    }

    containerRef.current.appendChild(host);

    return () => {
      if (containerRef.current && host.parentNode) {
        containerRef.current.removeChild(host);
      }
    };
  }, [items, subtotal, taxTotal, shippingTotal, discountTotal, grandTotal, onUpdateQuantity, onRemoveItem, onCheckout, onContinueShopping]);

  return <div ref={containerRef} className={className} />;
};