// ABOUTME: Shadow DOM component for displaying limited edition drop products
// Shows product info, countdown timer, stock status, and purchase limits

import React, { useEffect, useRef } from 'react';
import { formatCurrency } from '../../../utils/formatters';

interface DropProductCardProps {
  product: {
    id: number;
    name: string;
    price: number;
    image?: string;
    description?: string;
  };
  drop: {
    id: string;
    dropDate: Date;
    totalQuantity: number;
    soldCount: number;
    maxPerCustomer: number;
    isActive: boolean;
  };
  userPurchaseCount?: number;
  onAddToCart?: () => void;
  className?: string;
}

const ShadowDropProductCard: React.FC<DropProductCardProps> = ({
  product,
  drop,
  userPurchaseCount = 0,
  onAddToCart,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const host = document.createElement('div');
    host.className = className;
    const shadow = host.attachShadow({ mode: 'open' });

    const remainingStock = drop.totalQuantity - drop.soldCount;
    const stockPercentage = (remainingStock / drop.totalQuantity) * 100;
    const canPurchase = drop.isActive && remainingStock > 0 && userPurchaseCount < drop.maxPerCustomer;
    const isComingSoon = new Date(drop.dropDate) > new Date();
    const isSoldOut = remainingStock === 0;
    const reachedLimit = userPurchaseCount >= drop.maxPerCustomer;

    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
        
        :host {
          display: block;
          width: 100%;
        }

        .card-container {
          background: rgba(0, 0, 0, 0.8);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 16px;
          overflow: hidden;
          transition: all 0.3s ease;
          position: relative;
        }

        .card-container:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
          border-color: rgba(28, 224, 232, 0.3);
        }

        /* Limited Edition Badge */
        .limited-badge {
          position: absolute;
          top: 16px;
          left: 16px;
          background: linear-gradient(135deg, #1CE0E8, #9B5DE5);
          padding: 6px 12px;
          border-radius: 20px;
          font-family: 'Poppins', sans-serif;
          font-size: 11px;
          font-weight: 600;
          color: white;
          text-transform: uppercase;
          letter-spacing: 1px;
          z-index: 10;
          box-shadow: 0 4px 12px rgba(28, 224, 232, 0.4);
        }

        /* Product Image */
        .product-image {
          width: 100%;
          height: 300px;
          position: relative;
          overflow: hidden;
          background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
        }

        .product-image img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        .card-container:hover .product-image img {
          transform: scale(1.05);
        }

        /* Sold Out Overlay */
        .sold-out-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 5;
        }

        .sold-out-text {
          font-family: 'Poppins', sans-serif;
          font-size: 32px;
          font-weight: 700;
          color: rgba(255, 255, 255, 0.3);
          text-transform: uppercase;
          letter-spacing: 4px;
          transform: rotate(-15deg);
        }

        /* Content Section */
        .card-content {
          padding: 24px;
        }

        .product-name {
          font-family: 'Poppins', sans-serif;
          font-size: 20px;
          font-weight: 700;
          color: white;
          margin-bottom: 8px;
          line-height: 1.2;
        }

        .product-price {
          font-family: 'Poppins', sans-serif;
          font-size: 24px;
          font-weight: 600;
          color: #1CE0E8;
          margin-bottom: 16px;
        }

        /* Stock Status */
        .stock-section {
          margin-bottom: 20px;
        }

        .stock-label {
          font-family: 'Poppins', sans-serif;
          font-size: 12px;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.6);
          text-transform: uppercase;
          letter-spacing: 1px;
          margin-bottom: 8px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .stock-count {
          color: ${stockPercentage < 20 ? '#FF4747' : '#1CE0E8'};
          font-weight: 700;
        }

        .stock-bar {
          width: 100%;
          height: 8px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          overflow: hidden;
          position: relative;
        }

        .stock-fill {
          height: 100%;
          background: ${stockPercentage < 20 ? 'linear-gradient(90deg, #FF4747, #FF6B6B)' : 'linear-gradient(90deg, #1CE0E8, #9B5DE5)'};
          width: ${stockPercentage}%;
          transition: width 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .stock-fill::after {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.3),
            transparent
          );
          animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
          100% {
            left: 100%;
          }
        }

        /* Purchase Limit */
        .limit-info {
          font-family: 'Poppins', sans-serif;
          font-size: 13px;
          color: rgba(255, 255, 255, 0.6);
          margin-bottom: 20px;
          padding: 12px;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .limit-reached {
          color: #FF4747;
          font-weight: 600;
        }

        /* Action Button */
        .action-button {
          width: 100%;
          padding: 16px;
          border: none;
          border-radius: 12px;
          font-family: 'Poppins', sans-serif;
          font-size: 16px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 1px;
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;
        }

        .action-button.active {
          background: linear-gradient(135deg, #1CE0E8, #9B5DE5);
          color: white;
        }

        .action-button.active:hover {
          transform: scale(1.02);
          box-shadow: 0 6px 20px rgba(28, 224, 232, 0.4);
        }

        .action-button.disabled {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.3);
          cursor: not-allowed;
        }

        .action-button.coming-soon {
          background: rgba(155, 93, 229, 0.2);
          color: #9B5DE5;
          cursor: default;
        }

        /* Low Stock Warning */
        .low-stock-warning {
          position: absolute;
          bottom: 100%;
          left: 50%;
          transform: translateX(-50%);
          background: #FF4747;
          color: white;
          padding: 6px 12px;
          border-radius: 6px;
          font-family: 'Poppins', sans-serif;
          font-size: 12px;
          font-weight: 600;
          margin-bottom: 8px;
          white-space: nowrap;
          opacity: 0;
          animation: bounce 1s ease-in-out infinite;
        }

        @keyframes bounce {
          0%, 100% {
            transform: translateX(-50%) translateY(0);
          }
          50% {
            transform: translateX(-50%) translateY(-4px);
          }
        }

        .show-warning .low-stock-warning {
          opacity: 1;
        }
      </style>
    `;

    const getButtonContent = () => {
      if (isComingSoon) {
        return 'Coming Soon';
      }
      if (isSoldOut) {
        return 'Sold Out';
      }
      if (reachedLimit) {
        return 'Purchase Limit Reached';
      }
      return 'Add to Cart';
    };

    const getButtonClass = () => {
      if (isComingSoon) return 'coming-soon';
      if (isSoldOut || reachedLimit || !canPurchase) return 'disabled';
      return 'active';
    };

    shadow.innerHTML = `
      ${styles}
      <div class="card-container">
        <div class="limited-badge">Limited Edition</div>
        
        <div class="product-image">
          ${product.image ? `<img src="${product.image}" alt="${product.name}" />` : ''}
          ${isSoldOut ? `
            <div class="sold-out-overlay">
              <div class="sold-out-text">Sold Out</div>
            </div>
          ` : ''}
        </div>
        
        <div class="card-content">
          <h3 class="product-name">${product.name}</h3>
          <div class="product-price">${formatCurrency(product.price)}</div>
          
          ${drop.isActive ? `
            <div class="stock-section">
              <div class="stock-label">
                <span>Stock Level</span>
                <span class="stock-count">${remainingStock} / ${drop.totalQuantity}</span>
              </div>
              <div class="stock-bar">
                <div class="stock-fill"></div>
              </div>
            </div>
          ` : ''}
          
          <div class="limit-info">
            ${reachedLimit 
              ? `<span class="limit-reached">You've reached the purchase limit (${drop.maxPerCustomer} per customer)</span>`
              : `Limit: ${drop.maxPerCustomer} per customer ${userPurchaseCount > 0 ? `(${userPurchaseCount} purchased)` : ''}`
            }
          </div>
          
          <button 
            class="action-button ${getButtonClass()}" 
            ${canPurchase ? '' : 'disabled'}
            onclick="${canPurchase ? 'this.getRootNode().host.dispatchEvent(new CustomEvent("add-to-cart"))' : ''}"
          >
            ${remainingStock <= 10 && remainingStock > 0 && drop.isActive ? `
              <span class="low-stock-warning">Only ${remainingStock} left!</span>
            ` : ''}
            ${getButtonContent()}
          </button>
        </div>
      </div>
    `;

    // Handle add to cart event
    const handleAddToCart = () => {
      if (canPurchase && onAddToCart) {
        onAddToCart();
      }
    };

    host.addEventListener('add-to-cart', handleAddToCart);

    containerRef.current.innerHTML = '';
    containerRef.current.appendChild(host);

    return () => {
      host.removeEventListener('add-to-cart', handleAddToCart);
      containerRef.current?.removeChild(host);
    };
  }, [product, drop, userPurchaseCount, onAddToCart, className]);

  return <div ref={containerRef} />;
};

export default ShadowDropProductCard;