// ABOUTME: Product detail component with Shadow DOM for full product view
// Displays comprehensive product information with images, variants, and add to cart

import React, { useEffect, useRef, useState } from 'react';
import { BigCommerceProduct, ProductVariant } from '../../../services/BigCommerceService';
import { useCurrency } from '../../../contexts/CurrencyContext';

interface ShadowProductDetailProps {
  product: BigCommerceProduct;
  onAddToCart?: (product: BigCommerceProduct, variant?: ProductVariant, quantity?: number) => void;
  onBack?: () => void;
  className?: string;
}

export const ShadowProductDetail: React.FC<ShadowProductDetailProps> = ({ 
  product,
  onAddToCart,
  onBack,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { currency, formatPrice } = useCurrency();
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | undefined>();
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    if (!containerRef.current) return;

    const host = document.createElement('div');
    const shadow = host.attachShadow({ mode: 'open' });

    // Calculate price display
    const currentPrice = selectedVariant?.sale_price || selectedVariant?.price || product.sale_price || product.price;
    const originalPrice = selectedVariant?.price || product.price;
    const hasDiscount = product.sale_price && product.sale_price < product.price;
    const discountPercent = hasDiscount 
      ? Math.round((1 - product.sale_price! / product.price) * 100)
      : 0;

    // Get images
    const images = product.images || [];
    const currentImage = images[selectedImageIndex]?.url_zoom || images[selectedImageIndex]?.url_standard || '/placeholder-product.png';

    // Check stock status
    const stockLevel = selectedVariant?.inventory_level ?? product.inventory_level ?? 0;
    const isOutOfStock = stockLevel === 0;
    const isLowStock = stockLevel > 0 && stockLevel < 10;

    const styles = `
      <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');
        
        :host {
          display: block;
          width: 100%;
        }

        .product-detail {
          background: #0A0A0A;
          min-height: 100vh;
          color: white;
          font-family: 'Poppins', sans-serif;
        }

        .header {
          padding: 16px;
          border-bottom: 1px solid #1a1a1a;
          display: flex;
          align-items: center;
          gap: 16px;
        }

        .back-button {
          width: 40px;
          height: 40px;
          background: #1a1a1a;
          border: none;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .back-button:hover {
          background: #2a2a2a;
          transform: scale(1.05);
        }

        .back-button svg {
          width: 20px;
          height: 20px;
          stroke: white;
          stroke-width: 2;
          fill: none;
        }

        .header-title {
          font-size: 18px;
          font-weight: 600;
          flex: 1;
        }

        .content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 32px;
          padding: 24px;
          max-width: 1200px;
          margin: 0 auto;
        }

        @media (max-width: 768px) {
          .content {
            grid-template-columns: 1fr;
            gap: 24px;
          }
        }

        .image-section {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .main-image-container {
          position: relative;
          width: 100%;
          padding-bottom: 100%;
          background: #0F0F0F;
          border-radius: 12px;
          overflow: hidden;
        }

        .main-image {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: contain;
        }

        .image-thumbnails {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
          gap: 8px;
        }

        .thumbnail {
          width: 100%;
          padding-bottom: 100%;
          position: relative;
          background: #0F0F0F;
          border: 2px solid transparent;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .thumbnail.active {
          border-color: #1ABC9C;
        }

        .thumbnail:hover {
          border-color: #2a2a2a;
        }

        .thumbnail img {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .info-section {
          display: flex;
          flex-direction: column;
          gap: 24px;
        }

        .brand {
          font-size: 14px;
          color: #6B7280;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .title {
          font-size: 28px;
          font-weight: 600;
          line-height: 1.3;
          margin-bottom: 8px;
        }

        .price-section {
          display: flex;
          align-items: baseline;
          gap: 12px;
          margin-bottom: 8px;
        }

        .price {
          font-size: 32px;
          font-weight: 700;
          color: #1ABC9C;
        }

        .original-price {
          font-size: 24px;
          color: #6B7280;
          text-decoration: line-through;
        }

        .discount-badge {
          background: #DC2626;
          color: white;
          padding: 4px 12px;
          border-radius: 20px;
          font-size: 14px;
          font-weight: 600;
        }

        .stock-info {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 16px;
        }

        .stock-indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #10B981;
        }

        .stock-indicator.low {
          background: #F59E0B;
        }

        .stock-indicator.out {
          background: #EF4444;
        }

        .stock-text {
          font-size: 14px;
          color: #9CA3AF;
        }

        .variants {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .variant-group {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .variant-label {
          font-size: 14px;
          font-weight: 500;
          color: #D1D5DB;
        }

        .variant-options {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        .variant-option {
          padding: 8px 16px;
          background: #1a1a1a;
          border: 2px solid transparent;
          border-radius: 8px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .variant-option:hover {
          border-color: #2a2a2a;
        }

        .variant-option.selected {
          background: #1ABC9C;
          color: #0A0A0A;
          border-color: #1ABC9C;
        }

        .variant-option:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .quantity-section {
          display: flex;
          align-items: center;
          gap: 16px;
        }

        .quantity-label {
          font-size: 14px;
          font-weight: 500;
          color: #D1D5DB;
        }

        .quantity-controls {
          display: flex;
          align-items: center;
          background: #1a1a1a;
          border-radius: 8px;
          overflow: hidden;
        }

        .quantity-button {
          width: 40px;
          height: 40px;
          background: transparent;
          border: none;
          color: white;
          font-size: 20px;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .quantity-button:hover {
          background: #2a2a2a;
        }

        .quantity-button:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        .quantity-value {
          width: 60px;
          text-align: center;
          font-size: 16px;
          font-weight: 500;
        }

        .add-to-cart {
          width: 100%;
          padding: 16px;
          background: #1ABC9C;
          color: #0A0A0A;
          border: none;
          border-radius: 12px;
          font-size: 16px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .add-to-cart:hover:not(:disabled) {
          background: #15A085;
          transform: translateY(-2px);
          box-shadow: 0 8px 16px rgba(26, 188, 156, 0.3);
        }

        .add-to-cart:active:not(:disabled) {
          transform: translateY(0);
        }

        .add-to-cart:disabled {
          background: #374151;
          color: #9CA3AF;
          cursor: not-allowed;
        }

        .description {
          padding-top: 24px;
          border-top: 1px solid #1a1a1a;
        }

        .description-title {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 12px;
        }

        .description-content {
          font-size: 14px;
          line-height: 1.6;
          color: #D1D5DB;
        }

        .specifications {
          display: grid;
          grid-template-columns: 1fr 2fr;
          gap: 12px;
          padding: 16px;
          background: #0F0F0F;
          border-radius: 8px;
        }

        .spec-label {
          font-size: 14px;
          color: #6B7280;
        }

        .spec-value {
          font-size: 14px;
          color: #D1D5DB;
        }
      </style>
    `;

    // Use formatPrice from currency context
    const formatPriceInShadow = (price: number) => {
      return formatPrice(price);
    };

    // Group variants by option
    const variantOptions: Record<string, Set<string>> = {};
    product.variants?.forEach(variant => {
      variant.option_values?.forEach(optionValue => {
        if (!variantOptions[optionValue.option_display_name]) {
          variantOptions[optionValue.option_display_name] = new Set();
        }
        variantOptions[optionValue.option_display_name].add(optionValue.label);
      });
    });

    shadow.innerHTML = `
      ${styles}
      <div class="product-detail">
        <div class="header">
          ${onBack ? `
            <button class="back-button">
              <svg viewBox="0 0 24 24">
                <path d="M19 12H5M12 19l-7-7 7-7"/>
              </svg>
            </button>
          ` : ''}
          <h1 class="header-title">Product Details</h1>
        </div>

        <div class="content">
          <div class="image-section">
            <div class="main-image-container">
              <img src="${currentImage}" alt="${product.name}" class="main-image" />
            </div>
            
            ${images.length > 1 ? `
              <div class="image-thumbnails">
                ${images.map((img, index) => `
                  <div class="thumbnail ${index === selectedImageIndex ? 'active' : ''}" data-index="${index}">
                    <img src="${img.url_thumbnail}" alt="${product.name} ${index + 1}" />
                  </div>
                `).join('')}
              </div>
            ` : ''}
          </div>

          <div class="info-section">
            ${product.brand_name ? `<div class="brand">${product.brand_name}</div>` : ''}
            
            <h2 class="title">${product.name}</h2>

            <div class="price-section">
              <span class="price">${formatPriceInShadow(currentPrice)}</span>
              ${hasDiscount ? `
                <span class="original-price">${formatPriceInShadow(originalPrice)}</span>
                <span class="discount-badge">-${discountPercent}%</span>
              ` : ''}
            </div>

            <div class="stock-info">
              <span class="stock-indicator ${isOutOfStock ? 'out' : isLowStock ? 'low' : ''}"></span>
              <span class="stock-text">
                ${isOutOfStock ? 'Out of Stock' : isLowStock ? `Only ${stockLevel} left in stock` : 'In Stock'}
              </span>
            </div>

            ${Object.keys(variantOptions).length > 0 ? `
              <div class="variants">
                ${Object.entries(variantOptions).map(([optionName, values]) => `
                  <div class="variant-group">
                    <div class="variant-label">${optionName}:</div>
                    <div class="variant-options">
                      ${Array.from(values).map(value => `
                        <button class="variant-option" data-option="${optionName}" data-value="${value}">
                          ${value}
                        </button>
                      `).join('')}
                    </div>
                  </div>
                `).join('')}
              </div>
            ` : ''}

            <div class="quantity-section">
              <span class="quantity-label">Quantity:</span>
              <div class="quantity-controls">
                <button class="quantity-button decrease" ${quantity <= 1 ? 'disabled' : ''}>−</button>
                <span class="quantity-value">${quantity}</span>
                <button class="quantity-button increase" ${quantity >= (stockLevel || 99) ? 'disabled' : ''}>+</button>
              </div>
            </div>

            <button class="add-to-cart" ${isOutOfStock ? 'disabled' : ''}>
              ${isOutOfStock ? 'OUT OF STOCK' : 'ADD TO CART'}
            </button>

            ${product.description ? `
              <div class="description">
                <h3 class="description-title">Description</h3>
                <div class="description-content">${product.description}</div>
              </div>
            ` : ''}

            <div class="specifications">
              <span class="spec-label">SKU:</span>
              <span class="spec-value">${product.sku}</span>
              
              ${product.weight ? `
                <span class="spec-label">Weight:</span>
                <span class="spec-value">${product.weight} kg</span>
              ` : ''}
              
              <span class="spec-label">Availability:</span>
              <span class="spec-value">${product.availability}</span>
              
              <span class="spec-label">Condition:</span>
              <span class="spec-value">${product.condition}</span>
            </div>
          </div>
        </div>
      </div>
    `;

    // Add event listeners
    const backButton = shadow.querySelector('.back-button');
    if (backButton && onBack) {
      backButton.addEventListener('click', onBack);
    }

    // Thumbnail clicks
    const thumbnails = shadow.querySelectorAll('.thumbnail');
    thumbnails.forEach(thumb => {
      thumb.addEventListener('click', (e) => {
        const index = parseInt((e.currentTarget as HTMLElement).dataset.index || '0');
        setSelectedImageIndex(index);
      });
    });

    // Quantity controls
    const decreaseBtn = shadow.querySelector('.quantity-button.decrease');
    const increaseBtn = shadow.querySelector('.quantity-button.increase');
    
    if (decreaseBtn) {
      decreaseBtn.addEventListener('click', () => {
        setQuantity(prev => Math.max(1, prev - 1));
      });
    }

    if (increaseBtn) {
      increaseBtn.addEventListener('click', () => {
        setQuantity(prev => Math.min(stockLevel || 99, prev + 1));
      });
    }

    // Variant selection
    const variantButtons = shadow.querySelectorAll('.variant-option');
    variantButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const option = (e.currentTarget as HTMLElement).dataset.option;
        const value = (e.currentTarget as HTMLElement).dataset.value;
        
        // Find matching variant
        const matchingVariant = product.variants?.find(v => 
          v.option_values?.some(ov => 
            ov.option_display_name === option && ov.label === value
          )
        );
        
        if (matchingVariant) {
          setSelectedVariant(matchingVariant);
        }
      });
    });

    // Add to cart
    const addButton = shadow.querySelector('.add-to-cart');
    if (addButton && onAddToCart && !isOutOfStock) {
      addButton.addEventListener('click', () => {
        onAddToCart(product, selectedVariant, quantity);
      });
    }

    containerRef.current.appendChild(host);

    return () => {
      if (containerRef.current && host.parentNode) {
        containerRef.current.removeChild(host);
      }
    };
  }, [product, selectedImageIndex, selectedVariant, quantity, onAddToCart, onBack]);

  return <div ref={containerRef} className={className} />;
};