// ABOUTME: Mini cart component for header display
// Shows cart icon with item count badge and opens cart drawer on click

import React from 'react';
import { ShoppingCart } from 'lucide-react';
import { useEnhancedShoppingCart } from '../../../contexts/EnhancedShoppingCartContext';
import './ShadowMiniCart.css';

interface ShadowMiniCartProps {
  onClick: () => void;
  className?: string;
}

export const ShadowMiniCart: React.FC<ShadowMiniCartProps> = ({
  onClick,
  className = '',
}) => {
  const { getCartItemCount, cart } = useEnhancedShoppingCart();
  const itemCount = getCartItemCount();
  
  // console.log('[ShadowMiniCart] Render - Cart count:', itemCount, 'Cart:', cart);

  return (
    <button
      className={`shadow-mini-cart ${className}`}
      onClick={onClick}
      aria-label={`Shopping cart with ${itemCount} items`}
    >
      <ShoppingCart size={24} />
      {itemCount > 0 && (
        <span style={{ 
          color: '#FFD700', 
          marginLeft: '4px', 
          fontWeight: 'bold',
          fontSize: '14px'
        }}>
          {itemCount}
        </span>
      )}
    </button>
  );
};