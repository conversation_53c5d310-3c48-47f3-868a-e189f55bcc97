// ABOUTME: Reusable cart item component for displaying product with quantity controls
// Used in cart drawer, cart page, and checkout review

import React from 'react';
import { Plus, Minus, Trash2 } from 'lucide-react';
import { CartItem } from '../../../contexts/EnhancedShoppingCartContext';
import { useCurrency } from '../../../contexts/CurrencyContext';
import './ShadowCartItem.css';

interface ShadowCartItemProps {
  item: CartItem;
  onQuantityChange: (itemId: string, quantity: number) => void;
  onRemove: (itemId: string) => void;
  showControls?: boolean;
  compact?: boolean;
}

export const ShadowCartItem: React.FC<ShadowCartItemProps> = ({
  item,
  onQuantityChange,
  onRemove,
  showControls = true,
  compact = false,
}) => {
  const { formatPrice } = useCurrency();
  const imageUrl = item.product.images?.[0]?.url_thumbnail || '/images/ecommerce/fan-jersey.jpg';
  const variantText = item.variant ? 
    item.variant.option_values.map(v => v.label).join(' / ') : '';

  const handleQuantityChange = (delta: number) => {
    const newQuantity = item.quantity + delta;
    if (newQuantity < 1) {
      onRemove(item.id);
    } else {
      onQuantityChange(item.id, newQuantity);
    }
  };

  return (
    <div className={`shadow-cart-item ${compact ? 'compact' : ''}`}>
      <div className="cart-item-image">
        <img src={imageUrl} alt={item.product.name} />
      </div>
      
      <div className="cart-item-details">
        <h4 className="cart-item-name">{item.product.name}</h4>
        {variantText && (
          <p className="cart-item-variant">{variantText}</p>
        )}
        
        <div className="cart-item-price">
          {item.discountAmount && item.discountAmount > 0 ? (
            <>
              <span className="sale-price">{formatPrice(item.price)}</span>
              <span className="original-price">{formatPrice(item.originalPrice || item.price)}</span>
              <span className="discount-badge">
                -{Math.round((item.discountAmount / (item.originalPrice || item.price)) * 100)}%
              </span>
            </>
          ) : (
            <span>{formatPrice(item.price)}</span>
          )}
        </div>
        
        {item.customFields && Object.keys(item.customFields).length > 0 && (
          <div className="cart-item-custom-fields">
            {Object.entries(item.customFields).map(([key, value]) => (
              <p key={key}>
                <span className="field-label">{key}:</span> {value}
              </p>
            ))}
          </div>
        )}
      </div>
      
      {showControls && (
        <div className="cart-item-controls">
          <div className="quantity-controls">
            <button
              onClick={() => handleQuantityChange(-1)}
              className="quantity-button"
              aria-label="Decrease quantity"
              disabled={item.quantity <= 1}
            >
              <Minus size={16} />
            </button>
            
            <input
              type="number"
              value={item.quantity}
              onChange={(e) => {
                const value = parseInt(e.target.value) || 1;
                onQuantityChange(item.id, Math.max(1, value));
              }}
              className="quantity-input"
              min="1"
              max="999"
              aria-label="Item quantity"
            />
            
            <button
              onClick={() => handleQuantityChange(1)}
              className="quantity-button"
              aria-label="Increase quantity"
            >
              <Plus size={16} />
            </button>
          </div>
          
          <button
            onClick={() => onRemove(item.id)}
            className="remove-button"
            aria-label="Remove item from cart"
          >
            <Trash2 size={18} />
          </button>
        </div>
      )}
      
      <div className="cart-item-subtotal">
        <span className="subtotal-label">Subtotal:</span>
        <span className="subtotal-amount">
          {formatPrice(item.price * item.quantity)}
        </span>
      </div>
    </div>
  );
};