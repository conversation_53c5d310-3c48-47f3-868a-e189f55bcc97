/* ABOUTME: Styles for the mini cart component aligned with SHOT design system
   Dark theme cart icon with badge and SHOT brand colors */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600&display=swap');

.shadow-mini-cart {
  position: relative;
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s ease;
  border-radius: 8px;
}

.shadow-mini-cart:hover {
  color: #1ABC9C; /* shot-teal */
  background-color: rgba(26, 188, 156, 0.1);
  transform: translateY(-1px);
}

.shadow-mini-cart:focus-visible {
  outline: 2px solid #1ABC9C;
  outline-offset: 2px;
}

/* Item count badge - Bright yellow for visibility */
.shadow-mini-cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #FFD700; /* Gold/Yellow */
  color: #000000;
  font-size: 11px;
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  z-index: 10;
}

/* Total amount display */
.shadow-mini-cart-total {
  font-size: 0.875rem;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  color: rgba(156, 163, 175, 0.9);
  margin-left: 0.25rem;
}

/* Animation for badge appearing */
@keyframes badge-pop {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.shadow-mini-cart-badge {
  animation: badge-pop 0.3s ease-out;
}

/* Mobile adjustments */
@media (max-width: 640px) {
  .shadow-mini-cart-total {
    display: none;
  }
}