/* ABOUTME: Styles for the cart item component aligned with SHOT design system
   Dark theme with SHOT brand colors and consistent card styling */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600&display=swap');

.shadow-cart-item {
  display: grid;
  grid-template-columns: 100px 1fr auto;
  grid-template-rows: auto auto;
  gap: 1rem;
  padding: 1rem;
  background-color: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(75, 75, 75, 0.3);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.shadow-cart-item:hover {
  background-color: rgba(55, 65, 81, 0.95);
  border-color: rgba(107, 0, 219, 0.3); /* shot-purple */
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.shadow-cart-item.compact {
  grid-template-columns: 60px 1fr auto;
  gap: 0.75rem;
  padding: 0.75rem;
}

/* Image section */
.cart-item-image {
  grid-row: 1 / -1;
  width: 100%;
  aspect-ratio: 1;
  border-radius: 4px;
  overflow: hidden;
  background-color: rgba(75, 75, 75, 0.3);
}

.compact .cart-item-image {
  width: 60px;
  height: 60px;
}

.cart-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Details section */
.cart-item-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.cart-item-name {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
  color: #FFFFFF;
  line-height: 1.4;
}

.compact .cart-item-name {
  font-size: 0.875rem;
}

.cart-item-variant {
  margin: 0;
  font-size: 0.875rem;
  font-family: 'Montserrat', sans-serif;
  color: rgba(156, 163, 175, 0.9);
}

.compact .cart-item-variant {
  font-size: 0.75rem;
}

/* Price section */
.cart-item-price {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.cart-item-price .sale-price {
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
  color: #1ABC9C; /* shot-teal */
}

.cart-item-price .original-price {
  text-decoration: line-through;
  color: rgba(156, 163, 175, 0.6);
  font-size: 0.875rem;
  font-family: 'Montserrat', sans-serif;
}

.cart-item-price .discount-badge {
  background-color: #E63946;
  color: white;
  font-size: 0.75rem;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
}

/* Custom fields */
.cart-item-custom-fields {
  font-size: 0.75rem;
  font-family: 'Montserrat', sans-serif;
  color: rgba(156, 163, 175, 0.9);
}

.cart-item-custom-fields p {
  margin: 0.25rem 0;
}

.cart-item-custom-fields .field-label {
  font-weight: 600;
}

/* Controls section */
.cart-item-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.75rem;
  justify-content: space-between;
}

.quantity-controls {
  display: flex;
  align-items: center;
  background-color: rgba(18, 18, 18, 0.8);
  border: 1px solid rgba(75, 75, 75, 0.5);
  border-radius: 8px;
  overflow: hidden;
}

.quantity-button {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: rgba(156, 163, 175, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.quantity-button:hover:not(:disabled) {
  background-color: rgba(26, 188, 156, 0.1);
  color: #1ABC9C;
}

.quantity-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.quantity-button:focus-visible {
  outline: 2px solid #1ABC9C;
  outline-offset: -2px;
}

.quantity-input {
  width: 3rem;
  text-align: center;
  border: none;
  background: none;
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
  color: #FFFFFF;
  font-size: 0.875rem;
}

.quantity-input::-webkit-inner-spin-button,
.quantity-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.quantity-input[type=number] {
  -moz-appearance: textfield;
}

.quantity-input:focus {
  outline: none;
}

.remove-button {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  color: rgba(156, 163, 175, 0.9);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.remove-button:hover {
  color: #E63946;
  background-color: rgba(230, 57, 70, 0.1);
}

.remove-button:focus-visible {
  outline: 2px solid #E63946;
  outline-offset: 2px;
}

/* Subtotal section */
.cart-item-subtotal {
  grid-column: 2 / -1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(75, 75, 75, 0.3);
}

.subtotal-label {
  font-size: 0.875rem;
  font-family: 'Montserrat', sans-serif;
  color: rgba(156, 163, 175, 0.9);
}

.subtotal-amount {
  font-size: 1rem;
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
  color: #FFFFFF;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .shadow-cart-item {
    grid-template-columns: 80px 1fr;
    grid-template-rows: auto auto auto;
  }
  
  .cart-item-controls {
    grid-column: 1 / -1;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(75, 75, 75, 0.3);
  }
  
  .cart-item-subtotal {
    grid-column: 1 / -1;
    border-top: none;
    padding-top: 0;
  }
  
  .quantity-controls {
    order: 2;
  }
  
  .remove-button {
    order: 1;
  }
}