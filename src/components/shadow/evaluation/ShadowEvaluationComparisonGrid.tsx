import React from 'react';
import { ShadowEvaluationComparisonCard } from './ShadowEvaluationComparisonCard';

interface CategoryComparison {
  name: string;
  preScore: number;
  postScore: number;
  color: string;
  focus: string;
  icon: string;
}

interface PlayerEvaluation {
  playerId: string;
  playerName: string;
  eventName?: string;
  eventDate?: string;
  categories: CategoryComparison[];
}

interface ShadowEvaluationComparisonGridProps {
  evaluations: PlayerEvaluation[];
  variant?: 'compact' | 'detailed';
  columns?: 1 | 2 | 3 | 4;
  onPlayerClick?: (evaluation: PlayerEvaluation) => void;
  onCategoryClick?: (playerId: string, category: CategoryComparison) => void;
}

export const ShadowEvaluationComparisonGrid: React.FC<ShadowEvaluationComparisonGridProps> = ({
  evaluations,
  variant = 'compact',
  columns = 2,
  onPlayerClick,
  onCategoryClick
}) => {
  const getGridClass = () => {
    switch (columns) {
      case 1: return 'grid-cols-1';
      case 2: return 'grid-cols-1 md:grid-cols-2';
      case 3: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
      case 4: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
      default: return 'grid-cols-1 md:grid-cols-2';
    }
  };

  // Handle undefined or empty evaluations
  if (!evaluations || evaluations.length === 0) {
    return (
      <div className="text-center py-8 text-gray-400">
        <p>No evaluation comparisons available</p>
      </div>
    );
  }

  return (
    <div className={`grid ${getGridClass()} gap-6`}>
      {evaluations.map((evaluation) => (
        <div 
          key={evaluation.playerId}
          onClick={() => onPlayerClick?.(evaluation)}
          style={{ cursor: onPlayerClick ? 'pointer' : 'default' }}
        >
          <ShadowEvaluationComparisonCard
            categories={evaluation.categories}
            playerName={evaluation.playerName}
            eventName={evaluation.eventName}
            date={evaluation.eventDate}
            variant={variant}
            onCategoryClick={(category) => onCategoryClick?.(evaluation.playerId, category)}
          />
        </div>
      ))}
    </div>
  );
};

// Helper function to convert evaluation data from database format
export const convertEvaluationData = (
  preEvaluations: any[], // Self evaluations
  postEvaluations: any[] // Coach evaluations
): CategoryComparison[] => {
  const categoryMap: Record<string, { pre: number[], post: number[], color: string, icon: string, focus: string }> = {
    'TECHNICAL': { pre: [], post: [], color: 'blue', icon: 'crosshair', focus: 'Ball Mastery' },
    'PHYSICAL': { pre: [], post: [], color: 'green', icon: 'zap', focus: 'Co-ordination' },
    'PSYCHOLOGICAL': { pre: [], post: [], color: 'orange', icon: 'lightbulb', focus: 'Understanding' },
    'SOCIAL': { pre: [], post: [], color: 'pink', icon: 'users', focus: 'Communication' },
    'POSITIONAL': { pre: [], post: [], color: 'purple', icon: 'map-pin', focus: 'Game Awareness' }
  };

  // Process pre-evaluations (self)
  preEvaluations.forEach((evaluation) => {
    const category = evaluation.category?.toUpperCase();
    if (categoryMap[category]) {
      categoryMap[category].pre.push(evaluation.player_rating || evaluation.rating || 0);
      if (evaluation.area) {
        categoryMap[category].focus = evaluation.area;
      }
    }
  });

  // Process post-evaluations (coach)
  postEvaluations.forEach((evaluation) => {
    const category = evaluation.category?.toUpperCase();
    if (categoryMap[category]) {
      categoryMap[category].post.push(evaluation.rating || 0);
    }
  });

  // Calculate averages and create final array
  return Object.entries(categoryMap)
    .filter(([_, data]) => data.pre.length > 0 || data.post.length > 0)
    .map(([name, data]) => ({
      name: name.charAt(0) + name.slice(1).toLowerCase(),
      preScore: data.pre.length > 0 
        ? data.pre.reduce((sum, score) => sum + score, 0) / data.pre.length 
        : 0,
      postScore: data.post.length > 0 
        ? data.post.reduce((sum, score) => sum + score, 0) / data.post.length 
        : 0,
      color: data.color,
      focus: data.focus,
      icon: data.icon
    }));
};

export default ShadowEvaluationComparisonGrid;