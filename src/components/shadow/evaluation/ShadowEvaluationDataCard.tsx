import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';

interface CategoryScore {
  name: string;
  selfScore: number;
  coachScore: number;
}

interface ShadowEvaluationDataCardProps {
  playerName: string;
  eventName: string;
  date: string;
  evaluationCount: number;
  selfAverage: number;
  coachAverage: number;
  categories: CategoryScore[];
  onViewDetails?: () => void;
  variant?: 'compact' | 'detailed';
}

export const ShadowEvaluationDataCard: React.FC<ShadowEvaluationDataCardProps> = ({
  playerName,
  eventName,
  date,
  evaluationCount,
  selfAverage,
  coachAverage,
  categories,
  onViewDetails,
  variant = 'compact'
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);
  const portalRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!containerRef.current || shadowRef.current) return;

    const shadowRoot = containerRef.current.attachShadow({ mode: 'open' });
    shadowRef.current = shadowRoot;

    // Create portal container
    const portalContainer = document.createElement('div');
    portalContainer.id = 'evaluation-data-portal';
    shadowRoot.appendChild(portalContainer);
    portalRef.current = portalContainer;

    // Add styles
    const style = document.createElement('style');
    style.textContent = `
      :host {
        display: block;
        width: 100%;
      }

      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .card-container {
        background: #0a0a0a;
        border-radius: 12px;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .card-header {
        padding: 20px;
        background: #111111;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .player-info {
        margin-bottom: 8px;
      }

      .player-name {
        font-size: 18px;
        font-weight: 600;
        color: white;
        margin-bottom: 4px;
      }

      .event-info {
        font-size: 13px;
        color: #9ca3af;
        display: flex;
        align-items: center;
        gap: 4px;
      }

      .evaluation-count {
        margin-left: auto;
        background: rgba(26, 188, 156, 0.1);
        color: #1abc9c;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
      }

      .averages-section {
        padding: 20px;
        display: flex;
        gap: 20px;
        background: #0f0f0f;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .average-box {
        flex: 1;
      }

      .average-label {
        font-size: 12px;
        color: #6b7280;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 4px;
      }

      .average-value {
        font-size: 28px;
        font-weight: 700;
        color: white;
      }

      .average-value.self {
        color: #6b7280;
      }

      .average-value.coach {
        color: #1abc9c;
      }

      .divider {
        width: 1px;
        background: rgba(255, 255, 255, 0.1);
        margin: 0 10px;
      }

      .breakdown-section {
        padding: 20px;
      }

      .breakdown-title {
        font-size: 12px;
        color: #6b7280;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 16px;
      }

      .breakdown-table {
        width: 100%;
      }

      .breakdown-row {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr;
        gap: 16px;
        padding: 12px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
      }

      .breakdown-row:last-child {
        border-bottom: none;
      }

      .category-name {
        font-size: 14px;
        color: white;
        font-weight: 500;
      }

      .score-column {
        text-align: right;
      }

      .score-label {
        font-size: 11px;
        color: #6b7280;
        margin-bottom: 4px;
      }

      .score-value {
        font-size: 16px;
        font-weight: 600;
      }

      .score-value.self {
        color: #9ca3af;
      }

      .score-value.coach {
        color: white;
      }

      .difference {
        font-size: 11px;
        margin-left: 4px;
        font-weight: 500;
      }

      .difference.positive {
        color: #10b981;
      }

      .difference.negative {
        color: #ef4444;
      }

      .view-details-section {
        padding: 16px 20px;
        background: #111111;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
      }

      .view-details-button {
        width: 100%;
        padding: 12px;
        background: transparent;
        border: 1px solid rgba(26, 188, 156, 0.3);
        color: #1abc9c;
        font-size: 14px;
        font-weight: 500;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .view-details-button:hover {
        background: rgba(26, 188, 156, 0.1);
        border-color: #1abc9c;
      }

      /* Detailed variant styles */
      .card-container.detailed .breakdown-row {
        grid-template-columns: 2fr 1.5fr 1.5fr 1fr;
      }

      .progress-bar {
        width: 100%;
        height: 4px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
        margin-top: 4px;
        overflow: hidden;
      }

      .progress-fill {
        height: 100%;
        background: currentColor;
        transition: width 0.3s ease;
      }

      .trend-indicator {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        font-size: 11px;
        margin-top: 4px;
      }

      .trend-icon {
        width: 0;
        height: 0;
        border-style: solid;
      }

      .trend-icon.up {
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-bottom: 6px solid #10b981;
      }

      .trend-icon.down {
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 6px solid #ef4444;
      }

      /* Responsive */
      @media (max-width: 640px) {
        .averages-section {
          flex-direction: column;
          gap: 16px;
        }

        .divider {
          display: none;
        }

        .breakdown-row {
          grid-template-columns: 2fr 1fr 1fr !important;
          font-size: 13px;
        }

        .score-value {
          font-size: 14px;
        }
      }
    `;
    shadowRoot.appendChild(style);

    return () => {
      if (containerRef.current && shadowRef.current) {
        shadowRef.current.innerHTML = '';
      }
    };
  }, []);

  // Calculate score difference
  const getScoreDifference = (selfScore: number, coachScore: number) => {
    const diff = coachScore - selfScore;
    const sign = diff > 0 ? '+' : '';
    const className = diff > 0 ? 'positive' : diff < 0 ? 'negative' : '';
    return { text: diff !== 0 ? `${sign}${diff.toFixed(1)}` : '', className };
  };

  const content = (
    <div className={`card-container ${variant}`}>
      {/* Header */}
      <div className="card-header">
        <div className="player-info">
          <div className="player-name">{playerName}</div>
          <div className="event-info">
            <span>{date}</span>
            <span>•</span>
            <span>{eventName}</span>
            <div className="evaluation-count">
              {evaluationCount} Evaluation{evaluationCount !== 1 ? 's' : ''}
            </div>
          </div>
        </div>
      </div>

      {/* Averages Section */}
      <div className="averages-section">
        <div className="average-box">
          <div className="average-label">Self Average</div>
          <div className="average-value self">{selfAverage.toFixed(1)}</div>
        </div>
        <div className="divider"></div>
        <div className="average-box">
          <div className="average-label">Coach Average</div>
          <div className="average-value coach">{coachAverage.toFixed(1)}</div>
        </div>
      </div>

      {/* Category Breakdown */}
      <div className="breakdown-section">
        <div className="breakdown-title">Category Breakdown</div>
        <div className="breakdown-table">
          {variant === 'compact' ? (
            // Compact layout
            <>
              <div className="breakdown-row">
                <div></div>
                <div className="score-column">
                  <div className="score-label">Self</div>
                </div>
                <div className="score-column">
                  <div className="score-label">Coach</div>
                </div>
              </div>
              {categories.map((category, index) => {
                const diff = getScoreDifference(category.selfScore, category.coachScore);
                return (
                  <div key={index} className="breakdown-row">
                    <div className="category-name">{category.name}</div>
                    <div className="score-column">
                      <div className="score-value self">{category.selfScore.toFixed(1)}</div>
                    </div>
                    <div className="score-column">
                      <div className="score-value coach">
                        {category.coachScore.toFixed(1)}
                        {diff.text && (
                          <span className={`difference ${diff.className}`}>
                            ({diff.text})
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </>
          ) : (
            // Detailed layout with progress bars
            <>
              <div className="breakdown-row">
                <div></div>
                <div className="score-column">
                  <div className="score-label">Self</div>
                </div>
                <div className="score-column">
                  <div className="score-label">Coach</div>
                </div>
                <div className="score-column">
                  <div className="score-label">Trend</div>
                </div>
              </div>
              {categories.map((category, index) => {
                const diff = getScoreDifference(category.selfScore, category.coachScore);
                const trend = category.coachScore > category.selfScore ? 'up' : 
                              category.coachScore < category.selfScore ? 'down' : 'neutral';
                return (
                  <div key={index} className="breakdown-row">
                    <div className="category-name">{category.name}</div>
                    <div className="score-column">
                      <div className="score-value self">{category.selfScore.toFixed(1)}</div>
                      <div className="progress-bar">
                        <div 
                          className="progress-fill" 
                          style={{ 
                            width: `${(category.selfScore / 5) * 100}%`,
                            color: '#6b7280'
                          }}
                        ></div>
                      </div>
                    </div>
                    <div className="score-column">
                      <div className="score-value coach">{category.coachScore.toFixed(1)}</div>
                      <div className="progress-bar">
                        <div 
                          className="progress-fill" 
                          style={{ 
                            width: `${(category.coachScore / 5) * 100}%`,
                            color: '#1abc9c'
                          }}
                        ></div>
                      </div>
                    </div>
                    <div className="score-column">
                      {trend !== 'neutral' && (
                        <div className="trend-indicator">
                          <div className={`trend-icon ${trend}`}></div>
                          <span className={trend === 'up' ? 'positive' : 'negative'}>
                            {Math.abs(category.coachScore - category.selfScore).toFixed(1)}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </>
          )}
        </div>
      </div>

      {/* View Details Button */}
      {onViewDetails && (
        <div className="view-details-section">
          <button className="view-details-button" onClick={onViewDetails}>
            View Detailed Evaluations
          </button>
        </div>
      )}
    </div>
  );

  return (
    <div ref={containerRef} style={{ width: '100%' }}>
      {portalRef.current && createPortal(content, portalRef.current)}
    </div>
  );
};

export default ShadowEvaluationDataCard;