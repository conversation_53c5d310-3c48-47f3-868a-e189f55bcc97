import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';

interface CategoryComparison {
  name: string;
  preScore: number;  // Self-evaluation score
  postScore: number; // Coach evaluation score
  color: string;
  focus: string;
  icon: string;
}

interface ShadowEvaluationComparisonCardProps {
  categories: CategoryComparison[];
  playerName?: string;
  eventName?: string;
  date?: string;
  onCategoryClick?: (category: CategoryComparison) => void;
  variant?: 'compact' | 'detailed';
}

export const ShadowEvaluationComparisonCard: React.FC<ShadowEvaluationComparisonCardProps> = ({
  categories,
  playerName,
  eventName,
  date,
  onCategoryClick,
  variant = 'compact'
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const shadowRef = useRef<ShadowRoot | null>(null);
  const portalRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!containerRef.current || shadowRef.current) return;

    const shadowRoot = containerRef.current.attachShadow({ mode: 'open' });
    shadowRef.current = shadowRoot;

    // Create portal container
    const portalContainer = document.createElement('div');
    portalContainer.id = 'evaluation-comparison-portal';
    shadowRoot.appendChild(portalContainer);
    portalRef.current = portalContainer;

    // Add styles
    const style = document.createElement('style');
    style.textContent = `
      :host {
        display: block;
        width: 100%;
      }

      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      .card-container {
        background: #1a1a1a;
        border-radius: 12px;
        padding: 20px;
        width: 100%;
      }

      .card-header {
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .player-name {
        font-size: 18px;
        font-weight: 600;
        color: white;
        margin-bottom: 4px;
      }

      .event-info {
        font-size: 14px;
        color: #9ca3af;
      }

      .legend {
        display: flex;
        gap: 24px;
        margin-bottom: 16px;
        justify-content: center;
      }

      .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        color: #9ca3af;
      }

      .legend-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }

      .legend-dot.pre {
        background: #6b7280;
      }

      .legend-dot.post {
        background: currentColor;
      }

      .categories-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
      }

      .categories-grid.five-items .category-card:last-child {
        grid-column: 1 / -1;
        max-width: calc(50% - 4px);
        margin: 0 auto;
      }

      .category-card {
        background: #111111;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 16px;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
        overflow: hidden;
      }

      .category-card:hover {
        transform: translateY(-2px);
        border-color: rgba(255, 255, 255, 0.2);
      }

      .category-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
      }

      .category-icon {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .category-name {
        font-size: 14px;
        font-weight: 500;
        color: white;
        flex: 1;
      }

      .focus-text {
        font-size: 11px;
        color: #6b7280;
        margin-bottom: 16px;
      }

      .score-comparison {
        display: flex;
        flex-direction: column;
        gap: 8px;
        position: relative;
      }

      .score-bar-container {
        width: 100%;
        height: 20px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 10px;
        position: relative;
        overflow: hidden;
      }

      .score-bar {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-right: 8px;
        border-radius: 10px;
        transition: width 0.3s ease;
      }

      .score-bar.pre {
        background: #6b7280; /* Grey for self-evaluation */
      }

      .score-bar.post {
        /* Colored bars for coach evaluation */
      }

      .score-text {
        font-size: 11px;
        font-weight: 600;
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
      }
      
      .score-bar.pre .score-text {
        color: white;
      }
      
      .score-bar.post .score-text {
        color: white;
      }

      .score-difference {
        position: absolute;
        right: -45px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 10px;
        font-weight: 600;
        padding: 2px 6px;
        border-radius: 4px;
        white-space: nowrap;
      }

      .score-difference.positive {
        color: #10b981;
        background: rgba(16, 185, 129, 0.1);
      }

      .score-difference.negative {
        color: #ef4444;
        background: rgba(239, 68, 68, 0.1);
      }

      .score-difference.neutral {
        color: #6b7280;
      }

      /* Detailed variant styles */
      .card-container.detailed .categories-grid {
        grid-template-columns: 1fr;
        gap: 12px;
      }

      .card-container.detailed .category-card {
        padding: 20px;
      }

      
      /* Bar labels */
      .bar-label {
        font-size: 9px;
        color: #9ca3af;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 2px;
        font-weight: 500;
      }
      
      .bar-wrapper {
        position: relative;
      }

      /* Color variants */
      .color-blue { background-color: #3b82f6; }
      .color-green { background-color: #10b981; }
      .color-orange { background-color: #f97316; }
      .color-pink { background-color: #ec4899; }
      .color-purple { background-color: #8b5cf6; }
      .color-teal { background-color: #14b8a6; }
      .color-yellow { background-color: #f59e0b; }
      .color-red { background-color: #ef4444; }

      /* Icon styles */
      .icon-crosshair::before { content: "⊕"; }
      .icon-zap::before { content: "⚡"; }
      .icon-activity::before { content: "📊"; }
      .icon-lightbulb::before { content: "💡"; }
      .icon-users::before { content: "👥"; }
      .icon-edit3::before { content: "📍"; }
      .icon-map-pin::before { content: "📍"; }

      /* Responsive */
      @media (max-width: 640px) {
        .card-container.compact .categories-grid {
          grid-template-columns: 1fr;
        }
        
        .card-container.compact .categories-grid.five-items .category-card:last-child {
          max-width: 100%;
        }
      }
    `;
    shadowRoot.appendChild(style);

    return () => {
      if (containerRef.current && shadowRef.current) {
        // Clean way to clear shadow DOM content
        shadowRef.current.innerHTML = '';
      }
    };
  }, []);

  // Calculate score difference and format
  const getScoreDifference = (pre: number, post: number) => {
    const diff = post - pre;
    const sign = diff > 0 ? '+' : '';
    const className = diff > 0 ? 'positive' : diff < 0 ? 'negative' : 'neutral';
    return { text: diff !== 0 ? `${sign}${diff.toFixed(1)}` : '=', className };
  };

  const content = (
    <div className={`card-container ${variant}`}>
      {/* Header */}
      {(playerName || eventName || date) && (
        <div className="card-header">
          {playerName && <div className="player-name">{playerName}</div>}
          {(eventName || date) && (
            <div className="event-info">
              {eventName && <span>{eventName}</span>}
              {eventName && date && <span> • </span>}
              {date && <span>{date}</span>}
            </div>
          )}
        </div>
      )}

      {/* Legend */}
      <div className="legend">
        <div className="legend-item">
          <div className="legend-dot" style={{ backgroundColor: '#6b7280' }}></div>
          <span>Self-Evaluation</span>
        </div>
        <div className="legend-item">
          <div className="legend-dot post" style={{ backgroundColor: '#14b8a6' }}></div>
          <span>Coach Evaluation</span>
        </div>
      </div>

      {/* Categories */}
      <div className={`categories-grid ${categories.length === 5 ? 'five-items' : ''}`}>
        {categories.map((category, index) => {
          const diff = getScoreDifference(category.preScore, category.postScore);
          const maxScore = 5;
          const preWidth = (category.preScore / maxScore) * 100;
          const postWidth = (category.postScore / maxScore) * 100;

          return (
            <div
              key={index}
              className="category-card"
              onClick={() => onCategoryClick?.(category)}
            >
              <div className="category-header">
                <div className={`category-icon icon-${category.icon} color-${category.color}`}></div>
                <div className="category-name">{category.name}</div>
              </div>
              
              <div className="focus-text">{category.focus}</div>
              
              <div className="score-comparison">
                {/* Self-evaluation bar (pre) */}
                <div className="bar-wrapper">
                  <div className="bar-label">Self</div>
                  <div className="score-bar-container">
                    <div 
                      className="score-bar pre" 
                      style={{ width: `${preWidth}%` }}
                    >
                      <span className="score-text">{category.preScore.toFixed(1)}</span>
                    </div>
                  </div>
                </div>
                
                {/* Coach evaluation bar (post) */}
                <div className="bar-wrapper">
                  <div className="bar-label">Coach</div>
                  <div className="score-bar-container">
                    <div 
                      className={`score-bar post color-${category.color}`}
                      style={{ width: `${postWidth}%` }}
                    >
                      <span className="score-text">{category.postScore.toFixed(1)}</span>
                    </div>
                  </div>
                </div>
                
                {/* Score difference */}
                {variant === 'detailed' && (
                  <div className={`score-difference ${diff.className}`}>
                    {diff.text}
                  </div>
                )}
              </div>
              
            </div>
          );
        })}
      </div>
    </div>
  );

  return (
    <div ref={containerRef} style={{ width: '100%' }}>
      {portalRef.current && createPortal(content, portalRef.current)}
    </div>
  );
};

export default ShadowEvaluationComparisonCard;