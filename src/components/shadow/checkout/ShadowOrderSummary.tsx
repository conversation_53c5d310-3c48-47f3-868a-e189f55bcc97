// ABOUTME: Shadow DOM order summary component for checkout sidebar
// Displays cart items, totals, and pricing breakdown during checkout

import React, { useEffect, useRef } from 'react';
import { CartItem } from '../../../contexts/EnhancedShoppingCartContext';

interface ShadowOrderSummaryProps {
  items: CartItem[];
  subtotal: number;
  taxTotal: number;
  shippingTotal: number;
  discountTotal: number;
  grandTotal: number;
  currencyCode?: string;
  currencySymbol?: string;
  className?: string;
}

class ShadowOrderSummaryElement extends HTMLElement {
  private shadow: ShadowRoot;
  private items: CartItem[] = [];
  private subtotal = 0;
  private taxTotal = 0;
  private shippingTotal = 0;
  private discountTotal = 0;
  private grandTotal = 0;
  private currencyCode = 'USD';
  private currencySymbol = '$';

  constructor() {
    super();
    this.shadow = this.attachShadow({ mode: 'open' });
  }

  connectedCallback() {
    this.render();
  }

  setOrderData(data: {
    items: CartItem[];
    subtotal: number;
    taxTotal: number;
    shippingTotal: number;
    discountTotal: number;
    grandTotal: number;
    currencyCode?: string;
    currencySymbol?: string;
  }) {
    this.items = data.items;
    this.subtotal = data.subtotal;
    this.taxTotal = data.taxTotal;
    this.shippingTotal = data.shippingTotal;
    this.discountTotal = data.discountTotal;
    this.grandTotal = data.grandTotal;
    if (data.currencyCode) this.currencyCode = data.currencyCode;
    if (data.currencySymbol) this.currencySymbol = data.currencySymbol;
    this.render();
  }

  render() {
    this.shadow.innerHTML = `
      <style>
        :host {
          display: block;
          width: 100%;
        }

        .order-summary {
          background-color: #111827;
          border: 1px solid #1f2937;
          border-radius: 0.75rem;
          padding: 1.5rem;
        }

        .summary-title {
          font-size: 1.125rem;
          font-weight: 600;
          color: white;
          margin-bottom: 1rem;
        }

        .items-container {
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
          margin-bottom: 1rem;
          padding-bottom: 1rem;
          border-bottom: 1px solid #374151;
        }

        .item {
          display: flex;
          gap: 0.75rem;
        }

        .item-image {
          width: 4rem;
          height: 4rem;
          object-fit: cover;
          border-radius: 0.5rem;
          background-color: #1f2937;
        }

        .item-details {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 0.25rem;
        }

        .item-name {
          font-size: 0.875rem;
          font-weight: 500;
          color: white;
          line-height: 1.25;
        }

        .item-variant {
          font-size: 0.75rem;
          color: #9ca3af;
        }

        .item-quantity {
          font-size: 0.75rem;
          color: #9ca3af;
        }

        .item-price {
          font-size: 0.875rem;
          font-weight: 500;
          color: white;
          white-space: nowrap;
        }

        .totals-container {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        .total-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 0.875rem;
        }

        .total-label {
          color: #9ca3af;
        }

        .total-value {
          color: white;
          font-weight: 500;
        }

        .discount-value {
          color: #10b981;
        }

        .free-shipping {
          color: #10b981;
          font-weight: 600;
        }

        .grand-total {
          margin-top: 0.5rem;
          padding-top: 0.75rem;
          border-top: 1px solid #374151;
        }

        .grand-total .total-label {
          font-size: 1.125rem;
          font-weight: 600;
          color: white;
        }

        .grand-total .total-value {
          font-size: 1.125rem;
          font-weight: 700;
          color: #eab308;
        }

        .empty-message {
          text-align: center;
          color: #6b7280;
          padding: 2rem 0;
        }

        .promo-section {
          margin-top: 1rem;
          padding-top: 1rem;
          border-top: 1px solid #374151;
        }

        .promo-label {
          font-size: 0.875rem;
          color: #d1d5db;
          margin-bottom: 0.5rem;
        }

        .promo-input-wrapper {
          display: flex;
          gap: 0.5rem;
        }

        .promo-input {
          flex: 1;
          padding: 0.5rem 0.75rem;
          background-color: #1f2937;
          border: 1px solid #374151;
          border-radius: 0.375rem;
          color: white;
          font-size: 0.875rem;
        }

        .promo-input::placeholder {
          color: #6b7280;
        }

        .promo-button {
          padding: 0.5rem 1rem;
          background-color: #374151;
          color: white;
          border: none;
          border-radius: 0.375rem;
          font-size: 0.875rem;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s;
        }

        .promo-button:hover {
          background-color: #4b5563;
        }
      </style>

      <div class="order-summary">
        <h3 class="summary-title">Order Summary</h3>
        
        ${this.items.length > 0 ? `
          <div class="items-container">
            ${this.items.map(item => `
              <div class="item">
                ${item.imageUrl ? `
                  <img 
                    src="${item.imageUrl}" 
                    alt="${item.name}"
                    class="item-image"
                  />
                ` : `
                  <div class="item-image"></div>
                `}
                <div class="item-details">
                  <div class="item-name">${item.name}</div>
                  ${item.selectedOptions ? `
                    <div class="item-variant">
                      ${Object.entries(item.selectedOptions).map(([key, value]) => `${key}: ${value}`).join(', ')}
                    </div>
                  ` : ''}
                  <div class="item-quantity">Qty: ${item.quantity}</div>
                </div>
                <div class="item-price">
                  ${this.currencySymbol}${(item.price * item.quantity).toFixed(2)}
                </div>
              </div>
            `).join('')}
          </div>
          
          <div class="totals-container">
            <div class="total-row">
              <span class="total-label">Subtotal</span>
              <span class="total-value">${this.currencySymbol}${this.subtotal.toFixed(2)}</span>
            </div>
            
            ${this.discountTotal > 0 ? `
              <div class="total-row">
                <span class="total-label">Discount</span>
                <span class="total-value discount-value">-${this.currencySymbol}${this.discountTotal.toFixed(2)}</span>
              </div>
            ` : ''}
            
            <div class="total-row">
              <span class="total-label">Shipping</span>
              <span class="total-value ${this.shippingTotal === 0 ? 'free-shipping' : ''}">
                ${this.shippingTotal === 0 ? 'FREE' : `${this.currencySymbol}${this.shippingTotal.toFixed(2)}`}
              </span>
            </div>
            
            <div class="total-row">
              <span class="total-label">Tax</span>
              <span class="total-value">${this.currencySymbol}${this.taxTotal.toFixed(2)}</span>
            </div>
            
            <div class="total-row grand-total">
              <span class="total-label">Total</span>
              <span class="total-value">${this.currencySymbol}${this.grandTotal.toFixed(2)}</span>
            </div>
          </div>
          
          <div class="promo-section">
            <div class="promo-label">Promo Code</div>
            <div class="promo-input-wrapper">
              <input
                type="text"
                class="promo-input"
                placeholder="Enter code"
              />
              <button class="promo-button">Apply</button>
            </div>
          </div>
        ` : `
          <div class="empty-message">
            Your cart is empty
          </div>
        `}
      </div>
    `;

    // Add event listeners for promo code
    const promoButton = this.shadow.querySelector('.promo-button');
    const promoInput = this.shadow.querySelector('.promo-input') as HTMLInputElement;
    
    if (promoButton && promoInput) {
      promoButton.addEventListener('click', () => {
        const code = promoInput.value.trim();
        if (code) {
          this.dispatchEvent(new CustomEvent('applypromo', {
            detail: { code },
            bubbles: true,
            composed: true
          }));
        }
      });
    }
  }
}

// Register the custom element
if (!customElements.get('shadow-order-summary')) {
  customElements.define('shadow-order-summary', ShadowOrderSummaryElement);
}

// React wrapper component
const ShadowOrderSummary: React.FC<ShadowOrderSummaryProps> = ({
  items,
  subtotal,
  taxTotal,
  shippingTotal,
  discountTotal,
  grandTotal,
  currencyCode,
  currencySymbol,
  className
}) => {
  const elementRef = useRef<ShadowOrderSummaryElement>(null);

  useEffect(() => {
    if (elementRef.current) {
      elementRef.current.setOrderData({
        items,
        subtotal,
        taxTotal,
        shippingTotal,
        discountTotal,
        grandTotal,
        currencyCode,
        currencySymbol
      });
    }
  }, [items, subtotal, taxTotal, shippingTotal, discountTotal, grandTotal, currencyCode, currencySymbol]);

  return (
    <shadow-order-summary
      ref={elementRef}
      class={className}
    />
  );
};

export default ShadowOrderSummary;