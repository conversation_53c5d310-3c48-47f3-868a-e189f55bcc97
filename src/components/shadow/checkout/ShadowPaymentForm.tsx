// ABOUTME: Shadow DOM payment form component for Stripe Elements integration
// Provides secure payment form with card input and billing address

import React, { useEffect, useRef } from 'react';

interface ShadowPaymentFormProps {
  stripePublicKey?: string;
  onPaymentMethodChange?: (paymentMethod: string) => void;
  onBillingAddressChange?: (billingIsSameAsShipping: boolean) => void;
  className?: string;
}

class ShadowPaymentFormElement extends HTMLElement {
  private shadow: ShadowRoot;
  private selectedMethod = 'card';
  private stripePublicKey?: string;
  private billingIsSameAsShipping = true;

  constructor() {
    super();
    this.shadow = this.attachShadow({ mode: 'open' });
  }

  connectedCallback() {
    this.render();
  }

  setStripeKey(key: string) {
    this.stripePublicKey = key;
    this.render();
  }

  private handleMethodChange(method: string) {
    this.selectedMethod = method;
    this.dispatchEvent(new CustomEvent('paymentmethodchange', {
      detail: method,
      bubbles: true,
      composed: true
    }));
    this.render();
  }

  render() {
    this.shadow.innerHTML = `
      <style>
        :host {
          display: block;
          width: 100%;
        }

        .payment-form {
          background-color: #111827;
          border: 1px solid #1f2937;
          border-radius: 0.75rem;
          padding: 1.5rem;
        }

        .form-title {
          font-size: 1.25rem;
          font-weight: 600;
          color: white;
          margin-bottom: 1.5rem;
        }

        .payment-methods {
          display: flex;
          gap: 1rem;
          margin-bottom: 1.5rem;
        }

        .payment-method {
          flex: 1;
          padding: 1rem;
          background-color: #1f2937;
          border: 2px solid #374151;
          border-radius: 0.5rem;
          cursor: pointer;
          transition: all 0.2s;
          display: flex;
          align-items: center;
          gap: 0.75rem;
        }

        .payment-method:hover {
          border-color: #4b5563;
        }

        .payment-method.selected {
          border-color: #eab308;
          background-color: #1f2937;
        }

        .payment-method-icon {
          width: 2rem;
          height: 2rem;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.5rem;
        }

        .payment-method-info {
          flex: 1;
        }

        .payment-method-name {
          font-size: 0.875rem;
          font-weight: 600;
          color: white;
        }

        .payment-method-desc {
          font-size: 0.75rem;
          color: #9ca3af;
          margin-top: 0.125rem;
        }

        .card-form {
          display: flex;
          flex-direction: column;
          gap: 1rem;
        }

        .form-field {
          display: flex;
          flex-direction: column;
          gap: 0.5rem;
        }

        label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #d1d5db;
        }

        .stripe-element-container {
          padding: 0.75rem;
          background-color: #1f2937;
          border: 1px solid #374151;
          border-radius: 0.5rem;
          min-height: 2.75rem;
          display: flex;
          align-items: center;
        }

        .stripe-placeholder {
          color: #6b7280;
          font-size: 0.875rem;
        }

        .security-info {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          margin-top: 1rem;
          padding: 0.75rem;
          background-color: #1f2937;
          border-radius: 0.5rem;
        }

        .security-icon {
          color: #10b981;
          font-size: 1.25rem;
        }

        .security-text {
          font-size: 0.75rem;
          color: #9ca3af;
        }

        .billing-section {
          margin-top: 1.5rem;
          padding-top: 1.5rem;
          border-top: 1px solid #374151;
        }

        .section-title {
          font-size: 1rem;
          font-weight: 600;
          color: white;
          margin-bottom: 1rem;
        }

        .same-as-shipping {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          margin-bottom: 1rem;
        }

        .checkbox {
          width: 1.25rem;
          height: 1.25rem;
          background-color: #1f2937;
          border: 2px solid #374151;
          border-radius: 0.25rem;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s;
        }

        .checkbox:hover {
          border-color: #4b5563;
        }

        .checkbox.checked {
          background-color: #eab308;
          border-color: #eab308;
        }

        .checkbox.checked::after {
          content: '✓';
          color: black;
          font-weight: bold;
          font-size: 0.875rem;
        }

        .checkbox-label {
          font-size: 0.875rem;
          color: #d1d5db;
          cursor: pointer;
        }

        .coming-soon {
          text-align: center;
          padding: 3rem 2rem;
          background-color: #1f2937;
          border-radius: 0.5rem;
          margin-top: 1rem;
        }

        .coming-soon-icon {
          font-size: 3rem;
          margin-bottom: 1rem;
        }

        .coming-soon-title {
          font-size: 1.125rem;
          font-weight: 600;
          color: white;
          margin-bottom: 0.5rem;
        }

        .coming-soon-desc {
          font-size: 0.875rem;
          color: #9ca3af;
        }

        @media (max-width: 640px) {
          .payment-methods {
            flex-direction: column;
          }
        }
      </style>

      <div class="payment-form">
        <h2 class="form-title">Payment Information</h2>
        
        <div class="payment-methods">
          <div class="payment-method ${this.selectedMethod === 'card' ? 'selected' : ''}" data-method="card">
            <div class="payment-method-icon">💳</div>
            <div class="payment-method-info">
              <div class="payment-method-name">Credit/Debit Card</div>
              <div class="payment-method-desc">Secure payment</div>
            </div>
          </div>
          
          <div class="payment-method ${this.selectedMethod === 'paypal' ? 'selected' : ''}" data-method="paypal">
            <div class="payment-method-icon">🅿️</div>
            <div class="payment-method-info">
              <div class="payment-method-name">PayPal</div>
              <div class="payment-method-desc">Coming soon</div>
            </div>
          </div>
        </div>

        ${this.selectedMethod === 'card' ? `
          ${this.stripePublicKey ? `
            <div class="card-form">
              <div class="form-field">
                <label>Card Information</label>
                <div class="stripe-element-container" id="card-element">
                  <div class="stripe-placeholder">Stripe Elements will be mounted here</div>
                </div>
              </div>
              
              <div class="security-info">
                <span class="security-icon">🔒</span>
                <span class="security-text">
                  Your payment information is encrypted and secure. We never store your card details.
                </span>
              </div>
            </div>
          ` : `
            <div class="coming-soon">
              <div class="coming-soon-icon">💳</div>
              <div class="coming-soon-title">Stripe Payment Integration</div>
              <div class="coming-soon-desc">
                Payment processing will be available once Stripe API keys are configured.
              </div>
            </div>
          `}
        ` : `
          <div class="coming-soon">
            <div class="coming-soon-icon">🅿️</div>
            <div class="coming-soon-title">PayPal Coming Soon</div>
            <div class="coming-soon-desc">
              PayPal payment option will be available in a future update.
            </div>
          </div>
        `}

        <div class="billing-section">
          <h3 class="section-title">Billing Address</h3>
          <div class="same-as-shipping">
            <div class="checkbox ${this.billingIsSameAsShipping ? 'checked' : ''}" id="same-address-checkbox"></div>
            <label class="checkbox-label" for="same-address-checkbox">
              Same as shipping address
            </label>
          </div>
          ${!this.billingIsSameAsShipping ? `
            <div class="billing-address-form" style="margin-top: 1rem;">
              <shadow-address-form
                title="Billing Address"
                show-email-phone="false"
              ></shadow-address-form>
            </div>
          ` : ''}
        </div>
      </div>
    `;

    // Add event listeners
    const paymentMethods = this.shadow.querySelectorAll('.payment-method');
    paymentMethods.forEach(method => {
      method.addEventListener('click', () => {
        const selectedMethod = method.getAttribute('data-method') || 'card';
        this.handleMethodChange(selectedMethod);
      });
    });

    const checkbox = this.shadow.querySelector('.checkbox');
    if (checkbox) {
      checkbox.addEventListener('click', () => {
        this.billingIsSameAsShipping = !this.billingIsSameAsShipping;
        checkbox.classList.toggle('checked');
        
        // Dispatch billing address change event
        this.dispatchEvent(new CustomEvent('billingaddresschange', {
          detail: this.billingIsSameAsShipping,
          bubbles: true,
          composed: true
        }));
        
        // Re-render to show/hide billing form
        this.render();
      });
    }

    // Add listener for billing address changes
    const billingForm = this.shadow.querySelector('shadow-address-form');
    if (billingForm) {
      billingForm.addEventListener('addresschange', (e: any) => {
        // Forward the billing address data
        this.dispatchEvent(new CustomEvent('billingaddressdata', {
          detail: e.detail,
          bubbles: true,
          composed: true
        }));
      });
    }
  }
}

// Register the custom element
if (!customElements.get('shadow-payment-form')) {
  customElements.define('shadow-payment-form', ShadowPaymentFormElement);
}

// React wrapper component
const ShadowPaymentForm: React.FC<ShadowPaymentFormProps> = ({
  stripePublicKey,
  onPaymentMethodChange,
  onBillingAddressChange,
  className
}) => {
  const elementRef = useRef<ShadowPaymentFormElement>(null);

  useEffect(() => {
    if (elementRef.current && stripePublicKey) {
      elementRef.current.setStripeKey(stripePublicKey);
    }
  }, [stripePublicKey]);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const handleMethodChange = (e: Event) => {
      const customEvent = e as CustomEvent<string>;
      onPaymentMethodChange?.(customEvent.detail);
    };

    const handleBillingChange = (e: Event) => {
      const customEvent = e as CustomEvent<boolean>;
      onBillingAddressChange?.(customEvent.detail);
    };

    element.addEventListener('paymentmethodchange', handleMethodChange);
    element.addEventListener('billingaddresschange', handleBillingChange);
    return () => {
      element.removeEventListener('paymentmethodchange', handleMethodChange);
      element.removeEventListener('billingaddresschange', handleBillingChange);
    };
  }, [onPaymentMethodChange, onBillingAddressChange]);

  return (
    <shadow-payment-form
      ref={elementRef}
      class={className}
    />
  );
};

export default ShadowPaymentForm;