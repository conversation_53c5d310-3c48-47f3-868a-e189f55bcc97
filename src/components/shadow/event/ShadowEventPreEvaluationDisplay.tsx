import React, { useEffect, useState } from 'react';
import { EventSummaryService, EventComprehensiveSummary } from '../../../services/EventSummaryService';

interface ShadowEventPreEvaluationDisplayProps {
  eventId: string;
  eventTitle: string;
  eventDate: string;
  totalParticipants?: number;
  onBack?: () => void;
  onPublish?: () => void;
  className?: string;
}

/**
 * Component that correctly displays pre-evaluation percentage
 * FIX: Ensures percentage is calculated from pre_eval_total, not participant count
 */
export const ShadowEventPreEvaluationDisplay: React.FC<ShadowEventPreEvaluationDisplayProps> = ({
  eventId,
  eventTitle,
  eventDate,
  totalParticipants = 0,
  onBack,
  onPublish,
  className = ''
}) => {
  const [eventSummary, setEventSummary] = useState<EventComprehensiveSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchEventSummary = async () => {
      setIsLoading(true);
      try {
        const summary = await EventSummaryService.getEventSummary(eventId);
        setEventSummary(summary);
      } catch (error) {
        console.error('Error fetching event summary:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (eventId) {
      fetchEventSummary();
    }
  }, [eventId]);

  // FIX: Calculate percentage correctly based on pre-evaluation requests, not participants
  const getPreEvaluationPercentage = (): number => {
    if (!eventSummary || eventSummary.pre_eval_total === 0) {
      return 0;
    }
    return Math.round((eventSummary.pre_eval_completed / eventSummary.pre_eval_total) * 100);
  };

  const preEvalPercentage = getPreEvaluationPercentage();
  const preEvalCompleted = eventSummary?.pre_eval_completed || 0;
  const preEvalTotal = eventSummary?.pre_eval_total || 0;

  if (isLoading) {
    return (
      <div className={`shadow-event-pre-eval-display loading ${className}`}>
        <div style={{ textAlign: 'center', padding: '20px', color: '#9CA3AF' }}>
          Loading event data...
        </div>
      </div>
    );
  }

  return (
    <div className={`shadow-event-pre-eval-display ${className}`} style={{
      background: '#000000',
      color: '#FFFFFF',
      padding: '20px',
      borderRadius: '12px',
      fontFamily: "'Montserrat', sans-serif"
    }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px'
      }}>
        {onBack && (
          <button
            onClick={onBack}
            style={{
              background: 'transparent',
              border: '1px solid rgba(75, 75, 75, 0.3)',
              color: '#9CA3AF',
              padding: '8px 16px',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '600'
            }}
          >
            ← BACK TO TEAM
          </button>
        )}
        
        {onPublish && (
          <button
            onClick={onPublish}
            style={{
              background: '#52D4D4',
              color: '#000000',
              padding: '8px 16px',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '600',
              border: 'none'
            }}
          >
            ✈ PUBLISH EVENT
          </button>
        )}
      </div>

      {/* Event Info */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '12px',
        marginBottom: '24px'
      }}>
        <div style={{ fontSize: '32px' }}>🏃</div>
        <div>
          <h2 style={{
            margin: 0,
            fontSize: '20px',
            fontWeight: '600',
            color: '#FFFFFF'
          }}>{eventTitle}</h2>
          <div style={{
            fontSize: '14px',
            color: '#9CA3AF',
            marginTop: '4px'
          }}>{eventDate}</div>
        </div>
      </div>

      {/* Pre-Evaluation Status - FIXED */}
      <div style={{
        background: '#1F1F1F',
        padding: '20px',
        borderRadius: '12px',
        marginBottom: '20px'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'flex-start',
          gap: '16px',
          marginBottom: '16px'
        }}>
          <div style={{
            fontSize: '48px',
            fontWeight: '800',
            color: preEvalTotal === 0 ? '#9CA3AF' : (preEvalPercentage === 100 ? '#1ABC9C' : '#F7B613'),
            lineHeight: '1'
          }}>
            {preEvalPercentage}%
          </div>
          <div style={{ flex: 1 }}>
            <div style={{
              fontSize: '12px',
              fontWeight: '600',
              color: '#9CA3AF',
              textTransform: 'uppercase',
              letterSpacing: '0.05em',
              marginBottom: '4px'
            }}>
              PRE-EVALUATIONS COMPLETE
            </div>
            <div style={{
              fontSize: '14px',
              color: '#FFFFFF'
            }}>
              {preEvalCompleted} of {preEvalTotal} evaluations finalized.
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div style={{
          width: '100%',
          height: '8px',
          background: 'rgba(75, 75, 75, 0.3)',
          borderRadius: '4px',
          overflow: 'hidden'
        }}>
          <div style={{
            width: `${preEvalPercentage}%`,
            height: '100%',
            background: preEvalTotal === 0 ? 'rgba(75, 75, 75, 0.5)' : (preEvalPercentage === 100 ? '#1ABC9C' : '#F7B613'),
            transition: 'width 0.3s ease'
          }} />
        </div>

        {/* Status Message */}
        {preEvalTotal === 0 && (
          <div style={{
            marginTop: '16px',
            padding: '12px',
            background: 'rgba(75, 75, 75, 0.2)',
            borderRadius: '8px',
            fontSize: '14px',
            color: '#9CA3AF',
            textAlign: 'center'
          }}>
            No pre-evaluations have been requested yet. Players need to be invited and the event needs to be published.
          </div>
        )}
      </div>

      {/* Player Invitations Info */}
      <div style={{
        background: '#1F1F1F',
        padding: '20px',
        borderRadius: '12px'
      }}>
        <h3 style={{
          margin: '0 0 16px 0',
          fontSize: '18px',
          fontWeight: '600',
          color: '#FFFFFF'
        }}>Player Invitations</h3>
        
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          padding: '12px',
          background: '#0A0A0A',
          borderRadius: '8px'
        }}>
          <div>
            <div style={{ fontSize: '24px', fontWeight: '700', color: '#FFFFFF' }}>
              {totalParticipants}
            </div>
            <div style={{ fontSize: '14px', color: '#9CA3AF' }}>
              invited
            </div>
          </div>
          {eventSummary && (
            <>
              <div>
                <div style={{ fontSize: '24px', fontWeight: '700', color: '#1ABC9C' }}>
                  {eventSummary.confirmed_count}
                </div>
                <div style={{ fontSize: '14px', color: '#9CA3AF' }}>
                  confirmed
                </div>
              </div>
              <div>
                <div style={{ fontSize: '24px', fontWeight: '700', color: '#F7B613' }}>
                  {eventSummary.pre_eval_total}
                </div>
                <div style={{ fontSize: '14px', color: '#9CA3AF' }}>
                  pre-evals requested
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ShadowEventPreEvaluationDisplay;
