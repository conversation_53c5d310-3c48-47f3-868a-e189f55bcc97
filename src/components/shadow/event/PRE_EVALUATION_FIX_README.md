# Pre-Evaluation Percentage Fix Documentation

## Bug Description
**Issue:** Newly created events showing 29% pre-evaluation completion when it should show 0%

**Event ID:** b54332a9-4100-4eb7-9e87-0e87382453e6

## Root Cause Analysis

The bug occurs when:
1. An event has 7 participants invited
2. No pre-evaluations have been requested yet (pre_eval_total = 0)
3. The UI incorrectly calculates percentage using participant count instead of pre-evaluation count
4. Result: 2/7 = 0.285 ≈ 29% (where the 2 might come from some other calculation)

## The Fix

### 1. Created Utility Functions
File: `/src/utils/preEvaluationUtils.ts`

```typescript
export const calculatePreEvaluationPercentage = (completed: number, total: number): number => {
  if (total === 0) {
    return 0; // Key fix: return 0 when no pre-evals requested
  }
  return Math.round((completed / total) * 100);
};
```

### 2. Updated Components

#### PlayerSelfEvaluationStatus.tsx
- Now uses utility function for calculation
- Ensures percentage is based on pre_eval_total, not participant count

#### EventDetails.tsx
- Added validation when fetching event summary
- Ensures all stats are 0 when pre_eval_total is 0

#### ShadowTeamPreEvaluationCard.tsx
- Added comment noting that this component needs refactoring
- Currently uses player count, should receive pre-eval counts

### 3. Created Example Components
- `ShadowEventPreEvaluationDisplay.tsx` - Shows correct implementation
- `PreEvaluationProgressExample.tsx` - Example with debug info

## Database View (Correct)

The `event_comprehensive_summary` view already calculates this correctly:

```sql
CASE
  WHEN (count(DISTINCT pre.id) = 0) THEN (0)::numeric
  ELSE round(((100.0 * (count(DISTINCT CASE WHEN ((pre.status)::text = 'completed'::text) THEN pre.id ELSE NULL::uuid END))::numeric) / (count(DISTINCT pre.id))::numeric), 0)
END AS pre_eval_completion_percentage
```

## Implementation Checklist

✅ Created utility functions in `preEvaluationUtils.ts`
✅ Updated `PlayerSelfEvaluationStatus.tsx` to use utilities
✅ Updated `EventDetails.tsx` to validate data
✅ Created example components showing correct usage
✅ Added comprehensive test cases
✅ Documented the fix

## Components That May Need Updates

1. Any component displaying pre-evaluation percentage
2. Components using `ShadowEventCard` with pre-evaluation data
3. Event creation/management views showing pre-eval progress

## Testing Instructions

1. Create a new event with pre-evaluation enabled
2. Invite 7 players to the event
3. Verify the pre-evaluation percentage shows 0% (not 29%)
4. Send pre-evaluation requests
5. Verify percentage updates correctly as players complete evaluations

## Key Rule

**ALWAYS use `pre_eval_total` as the denominator for pre-evaluation percentage calculations, NEVER use participant count.**

## Example Usage

```typescript
import { calculatePreEvaluationPercentage } from '@/utils/preEvaluationUtils';

// In your component
const percentage = calculatePreEvaluationPercentage(
  eventSummary?.pre_eval_completed || 0,
  eventSummary?.pre_eval_total || 0  // NOT participant count!
);
```
