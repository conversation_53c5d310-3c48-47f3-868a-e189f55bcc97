# Team Display Components - Enhanced Version

## Overview
This documentation describes the enhanced team display components developed to provide a consistent, modern, and visually appealing way to display teams across the SHOT application.

## Components

### 1. StandardTeamCard
A flexible component that renders individual teams with four display modes:

- **Full**: Complete team card with all details (coaches, player count, actions)
- **Compact**: Medium-sized card with essential information
- **Minimal**: Small footprint card for lists and tight spaces
- **List-Mode**: NEW! An enhanced horizontal layout optimized for team listings

### 2. StandardTeamList
A list component that displays collections of teams with:

- Sorting by name, age group, or player count
- Search functionality
- Filter options
- Pagination for large sets
- Grid or list layouts
- Loading and empty states
- NEW! Full-width and zoom options for enhanced visual presentation

## Usage Examples

### Basic Example
```tsx
<StandardTeamList
  teams={teamsArray}
  title="Team Management"
  displayMode="list-mode"  // Use new list-mode for enhanced display
  layout="list"
  onTeamClick={handleTeamClick}
  onAddPlayer={handleAddPlayer}
  onEditTeam={handleEditTeam}
  onCreateTeam={handleCreateTeam}
  showSearch={true}
  showFilters={true}
  showActions={true}
  fullWidth={true}  // NEW! Use full width for better layout
  zoom={95}         // NEW! Apply slight zoom for visual enhancement
/>
```

### Individual Card Example
```tsx
<StandardTeamCard
  team={teamData}
  displayMode="list-mode"  // Use the new list-mode for enhanced display
  onViewTeam={handleTeamClick}
  onAddPlayer={handleAddPlayer}
  onEditTeam={handleEditTeam}
  showActions={true}
/>
```

## Enhanced Features

### List-Mode Display
The new `list-mode` display is optimized for team listings and provides:

- Larger, more readable cards that use the full width of the container
- Enhanced visual styling with subtle gradients and shadows
- Clear, visible accent color bar on the left side
- Improved spacing and typography for better readability
- More prominent age group badges
- Clearer layout of player and coach counts

### Visual Enhancements
The redesigned components include:

- **Subtle Animations**: Cards animate on hover and when lists load
- **Modern Styling**: Gradients, shadows, and glows to create a more premium look
- **Improved Icons**: Enhanced sport icons with drop shadows
- **Better Visual Hierarchy**: More attention to spacing and typography
- **Accent Colors**: Prominent use of brand colors for visual interest
- **Full-Width Option**: Better use of screen real estate
- **Zoom Feature**: Slight scaling for enhanced readability (similar to evaluations)

### Responsive Design
The components are fully responsive and provide:

- Optimized layouts for different screen sizes
- Adaptive grid layouts (2, 3, or 4 columns depending on width)
- Touch-friendly tap targets for mobile users
- Consistent appearance across devices

## Implementation Details

### Display Modes
The `displayMode` prop controls the appearance of team cards:

- **list-mode**: New horizontal layout optimized for team lists (recommended for main team listings)
- **full**: Complete card with all details (best for detailed team views)
- **compact**: Medium-sized card (good for dashboard views)
- **minimal**: Small card (good for sidebars or compact lists)

### Layout Options
The `layout` prop controls how teams are arranged:

- **list**: Teams are displayed in a vertical list (works best with `list-mode` display)
- **grid**: Teams are displayed in a responsive grid (works best with `full` or `compact` display)

### Visual Options
New visual enhancement options:

- **fullWidth**: When `true`, the list uses the full width of its container
- **zoom**: Applies a scale transform (95-100 recommended for subtle enhancement)

## Team Data Structure
The components expect team data in this format:

```typescript
interface Team {
  id: string;
  name: string;
  ageGroup?: string;
  playerCount: number;
  coaches: Coach[];
  sport_type?: string;
  logo_url?: string;
  club_id?: string;
  upcoming_events?: number;
}

interface Coach {
  id: string;
  name: string;
  role?: string;
  avatarUrl?: string;
  is_primary?: boolean;
}
```

## Implementation Notes

1. The team cards now feature a visible left accent bar by default, which was previously only visible on hover
2. Hover effects are more pronounced with subtle scaling and shadow changes
3. Card content has improved spacing and visual hierarchy
4. Age group badges are more visually distinct
5. List-mode cards are taller (90px) to allow for better readability and visual appeal
6. Animation effects are subtle but add a more premium feel

## Example Pages
To see these components in action, check the following example pages:

- `/examples/EnhancedTeamDisplayExample` - Demonstrates all display modes
- `/coach/club/{clubId}` - Shows the list-mode implementation in the Teams tab

## Migration Guide
To update existing team displays to use the enhanced components:

1. Import the StandardTeamCard and StandardTeamList components:
   ```tsx
   import { StandardTeamCard, StandardTeamList } from '../components/TeamDisplay';
   ```

2. Replace existing team card components with StandardTeamCard:
   ```tsx
   <StandardTeamCard
     team={teamData}
     displayMode="list-mode"
     onViewTeam={handleTeamClick}
   />
   ```

3. Replace team lists with StandardTeamList:
   ```tsx
   <StandardTeamList
     teams={teamsArray}
     displayMode="list-mode"
     layout="list"
     fullWidth={true}
     zoom={95}
   />
   ```

4. Ensure your team data matches the expected format (transform if needed)
