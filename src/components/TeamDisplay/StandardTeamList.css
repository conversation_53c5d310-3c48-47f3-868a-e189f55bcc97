/* StandardTeamList.css - SHOT Brand Compliant Styling */
/* This is the standard styling for all team lists across the SHOT app */

.standard-team-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 24px;
  /* Add transition for smooth zoom effects */
  transition: transform 0.3s ease;
}

/* Header Section */
.team-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

.team-list-title {
  display: flex;
  align-items: baseline;
  gap: 12px;
}

.team-list-title h2 {
  font-family: var(--shot-font-heading);
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--shot-teal);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
}

.team-count {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
}

.add-team-button {
  --background: var(--shot-teal);
  --color: var(--shot-black);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-family: var(--shot-font-heading);
}

.add-team-button:hover {
  --background: #17a589;
}

/* Search & Filters Section */
.team-list-controls {
  margin-bottom: 16px;
}

.team-search {
  --background: #1f1f1f;
  --color: white;
  --placeholder-color: rgba(255, 255, 255, 0.7);
  --placeholder-opacity: 1;
  --icon-color: #9ca3af;
  --clear-button-color: #9ca3af;
  --border-radius: var(--shot-button-radius);
  background: #1f2937;
  border-radius: var(--shot-button-radius);
  margin-bottom: 12px;
}

.team-sort-controls {
  display: flex;
  align-items: center;
  padding: 8px 4px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sort-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  margin-right: 12px;
}

.sort-buttons {
  display: flex;
  gap: 8px;
}

.sort-button {
  --color: rgba(255, 255, 255, 0.7);
  --color-activated: var(--shot-teal);
  font-size: 0.875rem;
  text-transform: none;
  letter-spacing: normal;
  font-weight: 500;
  height: 28px;
}

.sort-button.active {
  --color: var(--shot-teal);
  font-weight: 600;
}

.sort-button ion-icon {
  font-size: 14px;
  margin-left: 4px;
}

/* Team Grid Layout */
ion-grid {
  padding: 0;
}

ion-row {
  margin: 0 -8px;
}

ion-col {
  padding: 0 8px;
}

/* Team List Layout */
.team-list-vertical {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 4px;
  /* Add subtle animation for items */
  animation: fadeInList 0.5s ease-out forwards;
}

@keyframes fadeInList {
  from { opacity: 0.7; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Empty State */
.team-list-empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background: linear-gradient(135deg, rgba(26, 188, 156, 0.05) 0%, rgba(107, 0, 219, 0.05) 100%);
  border-radius: var(--shot-button-radius);
  border: 1px dashed rgba(26, 188, 156, 0.3);
  margin: 16px 0;
}

.empty-state-content {
  text-align: center;
  padding: 32px 16px;
}

.empty-state-icon {
  font-size: 48px;
  color: var(--shot-teal);
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state-content h3 {
  font-family: var(--shot-font-heading);
  font-weight: 600;
  font-size: 1.25rem;
  color: var(--shot-white);
  margin: 0 0 16px 0;
}

/* Loading State */
.team-list-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.team-list-loading ion-spinner {
  color: var(--shot-teal);
  width: 40px;
  height: 40px;
  margin-bottom: 16px;
}

.team-list-loading p {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

/* Pagination */
.team-list-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.pagination-info {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 16px;
}

/* Responsive Adjustments */
@media (max-width: 767px) {
  .team-list-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .add-team-button {
    width: 100%;
  }
  
  .team-sort-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .sort-buttons {
    width: 100%;
    justify-content: space-between;
  }
}
