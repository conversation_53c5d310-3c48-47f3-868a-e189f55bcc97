/* StandardTeamCard.css - Enhanced with sleeker design */
/* This is the standard styling for all team cards across the SHOT app */

.team-lozenge {
  display: inline-block;
  position: absolute;
  top: 12px;
  right: 12px;
  background-color: var(--shot-purple);
  color: white;
  font-family: var(--shot-font-heading);
  font-weight: 600;
  font-size: 0.7rem;
  padding: 3px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  z-index: 10;
}

.standard-team-card {
  background-color: rgba(26, 26, 26, 0.7);
  border-radius: var(--shot-button-radius);
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  width: 100%;
  /* Purple border as requested */
  border: 2px solid var(--shot-purple);
  position: relative;
}

.standard-team-card:hover {
  transform: translateY(-3px) scale(1.01);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  border-color: var(--shot-teal);
}

/* Removed the side accent gradient as requested */

/* ====== List Mode Styling (for Team Management page) ====== */
.standard-team-card.list-mode {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  height: 90px;
  background: linear-gradient(90deg, rgba(26, 188, 156, 0.08) 0%, rgba(0, 0, 0, 0.1) 100%);
}

.standard-team-card.list-mode .team-icon-wrapper {
  position: relative;
  width: 54px;
  height: 54px;
  margin-right: 16px;
  flex-shrink: 0;
}

.standard-team-card.list-mode .sport-icon {
  width: 50px;
  height: 50px;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.standard-team-card.list-mode .team-logo-container,
.standard-team-card.list-mode .team-logo-placeholder {
  position: absolute;
  bottom: -8px;
  right: -8px;
  width: 28px;
  height: 28px;
  border-radius: 6px;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(26, 188, 156, 0.7);
  box-shadow: 0 0 10px rgba(26, 188, 156, 0.4);
}

.standard-team-card.list-mode .team-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.standard-team-card.list-mode .team-logo-placeholder {
  background: linear-gradient(135deg, rgba(26, 188, 156, 0.7) 0%, rgba(107, 0, 219, 0.7) 100%);
  font-weight: 700;
  color: var(--shot-white);
  font-size: 14px;
}

.standard-team-card.list-mode .team-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.standard-team-card.list-mode .team-name {
  font-family: var(--shot-font-heading);
  font-weight: 600;
  font-size: 1.25rem;
  color: var(--shot-white);
  margin: 0 0 4px 0;
  display: flex;
  align-items: center;
}

.standard-team-card.list-mode .team-age-group {
  display: inline-block;
  font-size: 0.8rem;
  padding: 3px 10px;
  background-color: var(--shot-purple);
  color: var(--shot-white);
  border-radius: 12px;
  margin-left: 10px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.05em;
}

.standard-team-card.list-mode .team-stats {
  display: flex;
  gap: 20px;
}

.standard-team-card.list-mode .team-stat {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.9);
}

.standard-team-card.list-mode .team-stat ion-icon {
  color: var(--shot-teal);
  font-size: 18px;
}

.standard-team-card.list-mode .team-coaches-count {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 16px;
}

.standard-team-card.list-mode .forward-icon {
  color: var(--shot-teal);
  font-size: 22px;
  margin-left: 20px;
}

/* ====== Full Mode Styling (Detailed View) ====== */
.standard-team-card.full {
  display: flex;
  flex-direction: column;
  padding: 0;
  background: linear-gradient(135deg, rgba(26, 26, 26, 0.9) 0%, rgba(13, 13, 13, 0.9) 100%);
}

.standard-team-card.full .team-card-header {
  padding: 20px;
  padding-right: 60px; /* Make room for the TEAM lozenge */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  /* Removed gradient background */
  background: transparent;
  position: relative;
}

.standard-team-card.full .team-header-content {
  display: flex;
  width: 100%;
}

.standard-team-card.full .team-icon-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.standard-team-card.full .sport-icon {
  width: 48px;
  height: 48px;
  object-fit: contain;
  margin-right: 16px;
  flex-shrink: 0;
}

.standard-team-card.full .team-logo-container {
  width: 44px;
  height: 44px;
  border-radius: 8px;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(26, 188, 156, 0.4);
}

.standard-team-card.full .team-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.standard-team-card.full .team-logo-placeholder {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, rgba(26, 188, 156, 0.4) 0%, rgba(107, 0, 219, 0.4) 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: var(--shot-white);
  font-size: 18px;
  border: 2px solid rgba(26, 188, 156, 0.4);
}

.standard-team-card.full .team-title-section {
  flex: 1;
  padding-right: 16px;
}

.standard-team-card.full .team-name {
  font-family: var(--shot-font-heading) !important;
  font-weight: 700 !important;
  font-size: 1.4rem !important;
  color: var(--shot-white) !important;
  margin: 0 0 6px 0 !important;
  text-transform: uppercase !important;
}

.standard-team-card.full .team-age-group {
  font-family: var(--shot-font-body);
  font-size: 0.9rem;
  color: var(--shot-gold);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: inline-block;
  padding: 3px 12px;
  background-color: rgba(247, 182, 19, 0.2);
  border-radius: 14px;
}

.standard-team-card.full .team-card-body {
  padding: 20px;
}

.standard-team-card.full .team-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
  background-color: rgba(255, 255, 255, 0.05);
  padding: 16px;
  border-radius: var(--shot-button-radius);
}

.standard-team-card.full .team-stat {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.standard-team-card.full .team-stat ion-icon {
  color: var(--shot-teal);
  font-size: 20px;
}

.standard-team-card.full .team-coaches {
  margin-bottom: 20px;
  padding: 0 4px;
}

.standard-team-card.full .section-title {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--shot-purple);
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.standard-team-card.full .coach-display {
  display: flex;
  align-items: center;
}

.standard-team-card.full .primary-coach {
  display: flex;
  align-items: center;
  gap: 12px;
}

.standard-team-card.full .coach-avatar {
  width: 44px;
  height: 44px;
  border: 2px solid var(--shot-purple);
  box-shadow: 0 0 10px rgba(107, 0, 219, 0.3);
}

.standard-team-card.full .default-avatar {
  width: 100%;
  height: 100%;
  background-color: var(--shot-purple);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  border-radius: 50%;
  font-size: 20px;
}

.standard-team-card.full .coach-info {
  display: flex;
  flex-direction: column;
}

.standard-team-card.full .coach-name {
  font-weight: 600;
  font-size: 1rem;
  color: var(--shot-white);
}

.standard-team-card.full .coach-count {
  font-size: 0.85rem;
  color: var(--shot-purple);
  font-weight: 500;
}

.standard-team-card.full .no-coach {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

.standard-team-card.full .team-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
}

.standard-team-card.full .action-button {
  --color: var(--shot-teal);
  --border-color: var(--shot-teal);
  font-size: 0.85rem;
  height: 36px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.standard-team-card.full .action-button:hover {
  --background: rgba(26, 188, 156, 0.15);
}

/* ====== Compact Mode Styling ====== */
.standard-team-card.compact {
  padding: 16px 20px;
  background: linear-gradient(90deg, rgba(26, 188, 156, 0.08) 0%, rgba(0, 0, 0, 0) 80%);
}

.standard-team-card.compact .team-header-row {
  display: flex;
  align-items: center;
  gap: 16px;
}

.standard-team-card.compact .team-icon-wrapper {
  position: relative;
  width: 48px;
  height: 48px;
  flex-shrink: 0;
}

.standard-team-card.compact .sport-icon {
  width: 48px;
  height: 48px;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.standard-team-card.compact .team-logo-container,
.standard-team-card.compact .team-logo-placeholder {
  position: absolute;
  bottom: -8px;
  right: -8px;
  width: 28px;
  height: 28px;
  border-radius: 6px;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(26, 188, 156, 0.7);
  box-shadow: 0 0 10px rgba(26, 188, 156, 0.4);
}

.standard-team-card.compact .team-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.standard-team-card.compact .team-logo-placeholder {
  background: linear-gradient(135deg, rgba(26, 188, 156, 0.7) 0%, rgba(107, 0, 219, 0.7) 100%);
  font-weight: 700;
  color: var(--shot-white);
  font-size: 12px;
}

.standard-team-card.compact .team-info {
  flex: 1;
}

.standard-team-card.compact .team-name {
  font-family: var(--shot-font-heading);
  font-weight: 600;
  font-size: 1.1rem;
  color: var(--shot-white);
  margin: 0 0 4px 0;
}

.standard-team-card.compact .team-age-group {
  display: inline-block;
  font-size: 0.75rem;
  padding: 3px 10px;
  background-color: var(--shot-purple);
  color: var(--shot-white);
  border-radius: 12px;
  margin-right: 10px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.05em;
}

.standard-team-card.compact .team-stats {
  display: flex;
  gap: 16px;
  margin-top: 8px;
}

.standard-team-card.compact .team-stat {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.9);
}

.standard-team-card.compact .team-stat ion-icon {
  color: var(--shot-teal);
  font-size: 16px;
}

.standard-team-card.compact .forward-icon {
  color: var(--shot-teal);
  font-size: 20px;
  opacity: 0.9;
}

/* ====== Minimal Mode Styling ====== */
.standard-team-card.minimal {
  padding: 14px 16px;
  background: linear-gradient(90deg, rgba(26, 188, 156, 0.05) 0%, rgba(0, 0, 0, 0) 80%);
}

.standard-team-card.minimal .team-header-row {
  display: flex;
  align-items: center;
  gap: 14px;
}

.standard-team-card.minimal .sport-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
  flex-shrink: 0;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.standard-team-card.minimal .team-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.standard-team-card.minimal .team-name {
  font-family: var(--shot-font-heading);
  font-weight: 500;
  font-size: 1rem;
  color: var(--shot-white);
  margin: 0;
}

.standard-team-card.minimal .team-age-group {
  display: inline-block;
  font-size: 0.7rem;
  padding: 2px 8px;
  background-color: var(--shot-purple);
  color: var(--shot-white);
  border-radius: 10px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.05em;
}

.standard-team-card.minimal .forward-icon {
  color: var(--shot-teal);
  font-size: 18px;
  opacity: 0.8;
}

/* Responsive Adjustments */
@media (max-width: 767px) {
  .standard-team-card.full .team-card-header {
    padding: 16px;
  }
  
  .standard-team-card.full .team-name {
    font-size: 1.2rem;
  }
  
  .standard-team-card.full .team-card-body {
    padding: 16px;
  }
  
  .standard-team-card.list-mode, 
  .standard-team-card.compact {
    padding: 14px;
  }
  
  .standard-team-card.list-mode .team-name {
    font-size: 1.1rem;
  }
  
  .standard-team-card.list-mode .team-stat,
  .standard-team-card.list-mode .team-coaches-count {
    font-size: 0.8rem;
  }
}
