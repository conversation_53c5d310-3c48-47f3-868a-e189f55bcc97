// Utility functions for team display components

/**
 * Converts a team object from various formats to the standard format
 * expected by StandardTeamCard and StandardTeamList
 */
export const getTeamProps = (team?: any) => {
  if (!team) {
    return {
      id: '',
      name: 'Loading...',
      ageGroup: '',
      playerCount: 0,
      coaches: [],
      sport_type: 'football',
      upcoming_events: 0
    };
  }
  
  return {
    id: team.team_id || team.id,
    name: team.team_name || team.name,
    ageGroup: team.age_group || team.ageGroup,
    playerCount: team.player_count || team.playerCount || 0,
    coaches: team.coaches || [],
    sport_type: team.sport_type,
    logo_url: team.logo_url,
    club_id: team.club_id,
    upcoming_events: team.upcoming_events || 0
  };
};

/**
 * Gets the appropriate sport icon path based on sport type
 */
export const getSportIconPath = (sportType?: string) => {
  if (!sportType) return '/assets/sports/football.png'; // Default icon
  
  const sport = sportType.toLowerCase();
  
  // Match sport types to available icons
  if (sport.includes('soccer') || sport.includes('football')) return '/assets/sports/football.png';
  if (sport.includes('basketball')) return '/assets/sports/basketball.png';
  if (sport.includes('boxing')) return '/assets/sports/boxing.png';
  
  // Default to football if no match
  return '/assets/sports/football.png';
};
