import React from 'react';
import {
  IonIcon,
  IonButton,
  IonAvatar,
} from '@ionic/react';
import {
  people as peopleIcon,
  personAdd as addPlayerIcon,
  create as editIcon,
  chevronForward as forwardIcon,
  calendarOutline as calendarIcon,
} from 'ionicons/icons';
import './StandardTeamCard.css';

// Define prop types for flexibility
interface Coach {
  id: string;
  name: string;
  role?: string;
  avatarUrl?: string;
  is_primary?: boolean;
}

interface TeamCardProps {
  team: {
    id: string;
    name: string;
    ageGroup?: string;
    playerCount: number;
    coaches: Coach[];
    sport_type?: string;
    logo_url?: string;
    club_id?: string;
    upcoming_events?: number;
  };
  // Optional callback functions
  onViewTeam?: (teamId: string) => void;
  onAddPlayer?: (teamId: string) => void;
  onEditTeam?: (teamId: string) => void;
  // Control which action buttons to show
  showActions?: boolean;
}

/**
 * StandardTeamCard - The unified way to display teams across the SHOT app
 * 
 * This component provides a full-featured team card with all details and actions
 */
const StandardTeamCard: React.FC<TeamCardProps> = ({
  team,
  onViewTeam,
  onAddPlayer,
  onEditTeam,
  showActions = true,
}) => {
  // Function to get the appropriate sport icon path
  const getSportIconPath = (sportType?: string) => {
    if (!sportType) return '/assets/sports/football.png'; // Default icon
    
    const sport = sportType.toLowerCase();
    
    // Match sport types to available icons
    if (sport.includes('soccer') || sport.includes('football')) return '/assets/sports/football.png';
    if (sport.includes('basketball')) return '/assets/sports/basketball.png';
    if (sport.includes('boxing')) return '/assets/sports/boxing.png';
    
    // Default to football if no match
    return '/assets/sports/football.png';
  };

  // Function to render team logo or placeholder
  const renderTeamLogo = () => {
    if (team.logo_url) {
      return (
        <div className="team-logo-container">
          <img src={team.logo_url} alt={team.name} className="team-logo" />
        </div>
      );
    }
    
    // Return nothing if no logo (we're not using the TEAM lozenge as placeholder anymore)
    return null;
  };

  // Find primary coach
  const primaryCoach = team.coaches.find(coach => coach.is_primary);
  
  // Get coach display name
  const getCoachDisplayName = () => {
    if (team.coaches.length === 0) {
      return 'No coaches assigned';
    }
    
    if (primaryCoach) {
      return `${primaryCoach.name} (Primary)`;
    }
    
    return team.coaches[0].name;
  };

  // Handle team click
  const handleTeamClick = () => {
    if (onViewTeam) {
      onViewTeam(team.id);
    }
  };

  // Simplified component that only renders full mode
  return (
    <div className="standard-team-card full" onClick={handleTeamClick}>
      <div className="team-lozenge">TEAM</div>
      
      <div className="team-card-header">
        <div className="team-header-content">
          <div className="team-icon-container">
            <img 
              src={getSportIconPath(team.sport_type)} 
              alt={team.sport_type || 'Sport'} 
              className="sport-icon"
            />

            {/* Team name moved next to sport icon */}
            <div className="team-title-section">
              <h3 className="team-name">{team.name}</h3>
              {team.ageGroup && (
                <div className="team-age-group">{team.ageGroup}</div>
              )}
            </div>
            
            {/* Logo container moved to end */}
            {renderTeamLogo()}
          </div>
        </div>
      </div>
      
      <div className="team-card-body">
        <div className="team-stats">
          <div className="team-stat">
            <IonIcon icon={peopleIcon} />
            <span>{team.playerCount} Players</span>
          </div>
          
          <div className="team-stat">
            <IonIcon icon={calendarIcon} />
            <span>{team.upcoming_events || 0} Upcoming Events</span>
          </div>
        </div>
        
        <div className="team-coaches">
          <div className="section-title">Coach</div>
          <div className="coach-display">
            {team.coaches.length > 0 ? (
              <div className="primary-coach">
                <IonAvatar className="coach-avatar">
                  {primaryCoach?.avatarUrl || team.coaches[0]?.avatarUrl ? (
                    <img 
                      src={primaryCoach?.avatarUrl || team.coaches[0]?.avatarUrl} 
                      alt={primaryCoach?.name || team.coaches[0]?.name} 
                    />
                  ) : (
                    <div className="default-avatar">
                      {(primaryCoach?.name || team.coaches[0]?.name || '?').charAt(0)}
                    </div>
                  )}
                </IonAvatar>
                <div className="coach-info">
                  <div className="coach-name">{getCoachDisplayName()}</div>
                  {team.coaches.length > 1 && (
                    <div className="coach-count">+{team.coaches.length - 1} more</div>
                  )}
                </div>
              </div>
            ) : (
              <div className="no-coach">No coaches assigned</div>
            )}
          </div>
        </div>
        
        {showActions && (onAddPlayer || onEditTeam) && (
          <div className="team-actions">
            {onAddPlayer && (
              <IonButton 
                fill="outline" 
                size="small"
                className="action-button"
                onClick={(e) => {
                  e.stopPropagation();
                  onAddPlayer(team.id);
                }}
              >
                <IonIcon slot="start" icon={addPlayerIcon} />
                Add Player
              </IonButton>
            )}
            
            {onEditTeam && (
              <IonButton 
                fill="outline" 
                size="small"
                className="action-button"
                onClick={(e) => {
                  e.stopPropagation();
                  onEditTeam(team.id);
                }}
              >
                <IonIcon slot="start" icon={editIcon} />
                Edit
              </IonButton>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default StandardTeamCard;