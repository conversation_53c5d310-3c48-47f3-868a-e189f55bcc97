import React, { useState, useMemo } from 'react';
import {
  IonSearchbar,
  IonSegment,
  IonSegmentButton,
  IonLabel,
  IonItem,
  IonIcon,
  IonButton,
  IonGrid,
  IonRow,
  IonCol,
  IonSpinner,
} from '@ionic/react';
import {
  search as searchIcon,
  filter as filterIcon,
  add as addIcon,
  arrowDown as sortDownIcon,
  arrowUp as sortUpIcon,
} from 'ionicons/icons';
import StandardTeamCard from './StandardTeamCard';
import './StandardTeamList.css';

// Define prop types for flexibility
interface Coach {
  id: string;
  name: string;
  role?: string;
  avatarUrl?: string;
  is_primary?: boolean;
}

interface Team {
  id: string;
  name: string;
  ageGroup?: string;
  playerCount: number;
  coaches: Coach[];
  sport_type?: string;
  logo_url?: string;
  club_id?: string;
  upcoming_events?: number;
}

interface StandardTeamListProps {
  teams: Team[];
  // Display options
  title?: string;
  displayMode?: 'full' | 'compact' | 'minimal' | 'list-mode';
  layout?: 'grid' | 'list';
  loading?: boolean;
  emptyStateMessage?: string;
  // Callback functions
  onTeamClick?: (teamId: string) => void;
  onAddPlayer?: (teamId: string) => void;
  onEditTeam?: (teamId: string) => void;
  onCreateTeam?: () => void;
  // Control features
  showSearch?: boolean;
  showFilters?: boolean;
  showActions?: boolean;
  showAddTeam?: boolean;
  // Pagination
  itemsPerPage?: number;
  // Visual options
  fullWidth?: boolean;
  zoom?: number; // For 90-100% zoom similar to evaluations
}

type SortField = 'name' | 'ageGroup' | 'playerCount';

/**
 * StandardTeamList - The unified way to display a list of teams across the SHOT app
 * 
 * This component provides a standardized way to display teams with:
 * - Sorting (by name, age group, player count)
 * - Filtering
 * - Search
 * - Pagination
 * - Multiple layouts (grid/list)
 */
const StandardTeamList: React.FC<StandardTeamListProps> = ({
  teams,
  title = "Teams",
  displayMode = 'compact',
  layout = 'grid',
  loading = false,
  emptyStateMessage = "No teams found",
  onTeamClick,
  onAddPlayer,
  onEditTeam,
  onCreateTeam,
  showSearch = true,
  showFilters = true,
  showActions = true,
  showAddTeam = true,
  itemsPerPage = 12,
  fullWidth = false,
  zoom = 100,
}) => {
  // State
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortField, setSortField] = useState<SortField>('ageGroup');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [currentPage, setCurrentPage] = useState<number>(1);
  
  // Filtered and sorted teams
  const processedTeams = useMemo(() => {
    // First filter by search query
    let filtered = teams;
    
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = teams.filter(team => 
        team.name.toLowerCase().includes(query) || 
        (team.ageGroup && team.ageGroup.toLowerCase().includes(query))
      );
    }
    
    // Then sort
    return [...filtered].sort((a, b) => {
      if (sortField === 'name') {
        return sortDirection === 'asc' 
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      }
      
      if (sortField === 'ageGroup') {
        // Extract numeric values from age groups for proper sorting
        const getAgeValue = (ageGroup: string = 'Z999') => {
          const match = ageGroup.match(/(\d+)/);
          return match ? parseInt(match[0], 10) : 999;
        };
        
        const aValue = getAgeValue(a.ageGroup);
        const bValue = getAgeValue(b.ageGroup);
        
        return sortDirection === 'asc' 
          ? aValue - bValue
          : bValue - aValue;
      }
      
      if (sortField === 'playerCount') {
        return sortDirection === 'asc' 
          ? a.playerCount - b.playerCount
          : b.playerCount - a.playerCount;
      }
      
      return 0;
    });
  }, [teams, searchQuery, sortField, sortDirection]);
  
  // Pagination
  const totalPages = Math.ceil(processedTeams.length / itemsPerPage);
  const paginatedTeams = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return processedTeams.slice(startIndex, startIndex + itemsPerPage);
  }, [processedTeams, currentPage, itemsPerPage]);
  
  // Handle sort change
  const handleSortChange = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  // Handle search change
  const handleSearchChange = (e: CustomEvent) => {
    setSearchQuery(e.detail.value || '');
    setCurrentPage(1); // Reset to first page on search
  };
  
  // Pagination controls
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };
  
  // Render sort button
  const renderSortButton = (field: SortField, label: string) => {
    const isActive = sortField === field;
    
    return (
      <IonButton 
        fill="clear"
        size="small"
        className={`sort-button ${isActive ? 'active' : ''}`}
        onClick={() => handleSortChange(field)}
      >
        {label}
        {isActive && (
          <IonIcon 
            icon={sortDirection === 'asc' ? sortUpIcon : sortDownIcon} 
            slot="end"
          />
        )}
      </IonButton>
    );
  };
  
  // Render empty state
  const renderEmptyState = () => {
    return (
      <div className="team-list-empty-state">
        <div className="empty-state-content">
          <IonIcon icon={searchIcon} className="empty-state-icon" />
          <h3>{emptyStateMessage}</h3>
          
          {showAddTeam && onCreateTeam && (
            <IonButton 
              className="shot-button-primary"
              onClick={onCreateTeam}
            >
              <IonIcon icon={addIcon} slot="start" />
              Create Team
            </IonButton>
          )}
        </div>
      </div>
    );
  };
  
  // Render loading state
  const renderLoadingState = () => {
    return (
      <div className="team-list-loading">
        <IonSpinner name="dots" />
        <p>Loading teams...</p>
      </div>
    );
  };
  
  // Render team grid layout
  const renderTeamGrid = () => {
    return (
      <IonGrid>
        <IonRow>
          {paginatedTeams.map(team => (
            <IonCol 
              key={team.id} 
              size="12" 
              sizeMd="6" 
              sizeLg="4"
            >
              <StandardTeamCard 
                team={team}
                displayMode={displayMode}
                onViewTeam={onTeamClick}
                onAddPlayer={onAddPlayer}
                onEditTeam={onEditTeam}
                showActions={showActions}
              />
            </IonCol>
          ))}
        </IonRow>
      </IonGrid>
    );
  };
  
  // Render team list layout
  const renderTeamList = () => {
    return (
      <div className="team-list-vertical">
        {paginatedTeams.map(team => (
          <StandardTeamCard 
            key={team.id}
            team={team}
            displayMode={displayMode === 'list-mode' ? 'list-mode' : displayMode}
            onViewTeam={onTeamClick}
            onAddPlayer={onAddPlayer}
            onEditTeam={onEditTeam}
            showActions={showActions}
          />
        ))}
      </div>
    );
  };
  
  // Render pagination controls
  const renderPagination = () => {
    if (totalPages <= 1) return null;
    
    return (
      <div className="team-list-pagination">
        <IonButton 
          fill="clear" 
          size="small"
          disabled={currentPage === 1}
          onClick={() => goToPage(currentPage - 1)}
        >
          Previous
        </IonButton>
        
        <div className="pagination-info">
          Page {currentPage} of {totalPages}
        </div>
        
        <IonButton 
          fill="clear" 
          size="small"
          disabled={currentPage === totalPages}
          onClick={() => goToPage(currentPage + 1)}
        >
          Next
        </IonButton>
      </div>
    );
  };
  
  const listStyle = {
    width: fullWidth ? '100%' : 'auto',
    maxWidth: fullWidth ? '100%' : '1200px',
    margin: '0 auto',
    transform: `scale(${zoom / 100})`,
    transformOrigin: 'top center'
  };
  
  return (
    <div className="standard-team-list" style={listStyle}>
      {/* Header Section */}
      <div className="team-list-header">
        <div className="team-list-title">
          <h2>{title}</h2>
          {teams.length > 0 && (
            <span className="team-count">{teams.length} teams</span>
          )}
        </div>
        
        {showAddTeam && onCreateTeam && (
          <IonButton 
            className="add-team-button"
            onClick={onCreateTeam}
          >
            <IonIcon icon={addIcon} slot="start" />
            Add Team
          </IonButton>
        )}
      </div>
      
      {/* Search & Filters Section */}
      {(showSearch || showFilters) && (
        <div className="team-list-controls">
          {showSearch && (
            <IonSearchbar
              value={searchQuery}
              onIonChange={handleSearchChange}
              placeholder="Search teams..."
              className="team-search"
              showCancelButton="never"
            />
          )}
          
          {showFilters && (
            <div className="team-sort-controls">
              <div className="sort-label">Sort By:</div>
              <div className="sort-buttons">
                {renderSortButton('name', 'Name')}
                {renderSortButton('ageGroup', 'Age Group')}
                {renderSortButton('playerCount', 'Players')}
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Team List Content */}
      <div className="team-list-content">
        {loading ? (
          renderLoadingState()
        ) : paginatedTeams.length === 0 ? (
          renderEmptyState()
        ) : (
          layout === 'grid' ? renderTeamGrid() : renderTeamList()
        )}
      </div>
      
      {/* Pagination */}
      {!loading && paginatedTeams.length > 0 && renderPagination()}
    </div>
  );
};

export default StandardTeamList;