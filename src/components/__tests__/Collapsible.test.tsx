import { render, screen, fireEvent } from '@testing-library/react';
import Collapsible from '../Collapsible';
import { videocamOutline } from 'ionicons/icons';

describe('Collapsible', () => {
  it('renders with title and content', () => {
    render(
      <Collapsible title="Test Section" icon={videocamOutline}>
        <div>Test Content</div>
      </Collapsible>
    );

    expect(screen.getByText('Test Section')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('toggles content visibility when clicked', () => {
    render(
      <Collapsible title="Test Section">
        <div>Test Content</div>
      </Collapsible>
    );

    const header = screen.getByText('Test Section');
    const content = screen.getByText('Test Content');

    // Content should be visible by default
    expect(content.parentElement).toHaveClass('opacity-100');

    // Click to collapse
    fireEvent.click(header);
    expect(content.parentElement).toHaveClass('opacity-0');

    // Click to expand
    fireEvent.click(header);
    expect(content.parentElement).toHaveClass('opacity-100');
  });
}); 