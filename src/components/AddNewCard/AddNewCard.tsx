import React from 'react';
import { IonIcon } from '@ionic/react';
import { add as addIcon } from 'ionicons/icons';
import './AddNewCard.css';

interface AddNewCardProps {
  title: string;
  icon?: any;
  onClick: () => void;
  disabled?: boolean;
}

const AddNewCard: React.FC<AddNewCardProps> = ({ 
  title, 
  icon = addIcon, 
  onClick,
  disabled = false 
}) => {
  return (
    <div 
      className={`add-new-card ${disabled ? 'disabled' : ''}`} 
      onClick={disabled ? undefined : onClick}
    >
      <div className="add-new-icon">
        <IonIcon icon={icon} />
      </div>
      <h3 className="add-new-title">{title}</h3>
    </div>
  );
};

export default AddNewCard;
