.add-new-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: 2px dashed rgba(151, 71, 255, 0.5);
  border-radius: 16px;
  padding: 40px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 16px 0;
  min-height: 200px;
}

.add-new-card:hover {
  border-color: #9747FF;
  background: rgba(151, 71, 255, 0.05);
  transform: translateY(-2px);
}

.add-new-card:active {
  transform: translateY(0);
}

.add-new-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.add-new-card.disabled:hover {
  border-color: rgba(151, 71, 255, 0.5);
  background: transparent;
  transform: none;
}

.add-new-icon {
  font-size: 48px;
  color: #9747FF;
  margin-bottom: 16px;
}

.add-new-title {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  text-align: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .add-new-card {
    padding: 30px 15px;
    min-height: 160px;
  }

  .add-new-icon {
    font-size: 40px;
  }

  .add-new-title {
    font-size: 18px;
  }
}
