import React, { useState, useEffect } from 'react';
import { 
  isSuperadmin, 
  getSuperadminFlag, 
  toggleSuperadminFlag, 
  FLAGS 
} from '../../utils/superadminSession';

interface SuperadminControlsProps {
  userPrivileges: string[] | null | undefined;
}

/**
 * A component that shows admin controls for superadmins
 * Hidden for all non-superadmin users
 */
const SuperadminControls: React.FC<SuperadminControlsProps> = ({ userPrivileges }) => {
  const [showRoleContent, setShowRoleContent] = useState(false);
  
  // Check if user is superadmin
  const isAdmin = isSuperadmin(userPrivileges);
  
  // Initialize state from session storage
  useEffect(() => {
    if (isAdmin) {
      const flagValue = getSuperadminFlag(FLAGS.SHOW_ROLE_CONTENT, false);
      setShowRoleContent(flagValue);
    }
  }, [isAdmin]);
  
  // Listen for flag changes from other components
  useEffect(() => {
    const handleFlagChange = (event: CustomEvent) => {
      if (event.detail.key === FLAGS.SHOW_ROLE_CONTENT) {
        setShowRoleContent(event.detail.value);
      }
    };
    
    window.addEventListener('superadminFlagChanged', 
      handleFlagChange as EventListener);
    
    return () => {
      window.removeEventListener('superadminFlagChanged', 
        handleFlagChange as EventListener);
    };
  }, []);
  
  // Handle toggle action
  const handleToggleRoleContent = () => {
    const newValue = toggleSuperadminFlag(FLAGS.SHOW_ROLE_CONTENT);
    setShowRoleContent(newValue);
  };
  
  // Don't render anything for non-superadmins
  if (!isAdmin) {
    return null;
  }
  
  return (
    <div className="fixed bottom-20 right-4 z-50 bg-black/80 backdrop-blur-sm rounded-lg shadow-lg overflow-hidden">
      <div className="p-3">
        <div className="flex items-center justify-between mb-2">
          <span className="text-red-500 text-xs font-bold">SUPERADMIN</span>
          <div className="w-3 h-3 rounded-full bg-red-500 animate-pulse"></div>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-white text-sm mr-3">Show All Role Content:</label>
            <div 
              className={`w-10 h-6 rounded-full relative cursor-pointer ${
                showRoleContent ? 'bg-green-500' : 'bg-gray-600'
              }`}
              onClick={handleToggleRoleContent}
            >
              <div 
                className={`absolute top-1 w-4 h-4 rounded-full bg-white transition-all ${
                  showRoleContent ? 'left-5' : 'left-1'
                }`}
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuperadminControls;