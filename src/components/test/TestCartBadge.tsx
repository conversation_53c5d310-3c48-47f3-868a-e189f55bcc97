// ABOUTME: Test component to debug cart badge visibility issue
// Simple component without any CSS classes or Shadow DOM

import React from 'react';
import { ShoppingCart } from 'lucide-react';

interface TestCartBadgeProps {
  count: number;
  onClick?: () => void;
}

export const TestCartBadge: React.FC<TestCartBadgeProps> = ({ count, onClick }) => {
  return (
    <div 
      onClick={onClick}
      style={{ 
        position: 'relative', 
        display: 'inline-block',
        cursor: 'pointer',
        padding: '10px',
        backgroundColor: '#333',
        borderRadius: '8px'
      }}
    >
      <ShoppingCart size={24} color="white" />
      {count > 0 && (
        <div 
          style={{
            position: 'absolute',
            top: '0',
            right: '0',
            width: '20px',
            height: '20px',
            borderRadius: '50%',
            backgroundColor: 'yellow',
            color: 'black',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '12px',
            fontWeight: 'bold'
          }}
        >
          {count}
        </div>
      )}
    </div>
  );
};