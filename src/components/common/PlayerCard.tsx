import React from 'react';
import MemberComponent from './MemberComponent';


interface PlayerCardProps {
  player: {
    id: string;
    name: string;
    position: string;
    age?: number;
    profileImage?: string;
    status?: string;
    hasIssue?: boolean;
  };
  isSelected?: boolean;
  onSelect: (id: string) => void;
}

const PlayerCard: React.FC<PlayerCardProps> = ({ player, isSelected, onSelect }) => {
  // Player specific injected info
  const playerInfo = (
    <div className="flex items-center">
      <span className="mr-2">{player.position}</span>
      {player.age && (
        <>
          <span className="text-gray-500 mx-1">•</span>
          <span>Age {player.age}</span>
        </>
      )}
      {player.status && (
        <>
          <span className="text-gray-500 mx-1">•</span>
          <span className={`${player.status === 'active' ? 'text-green-400' : 
                              player.status === 'injured' ? 'text-red-400' : 
                              'text-yellow-400'}`}>
            {player.status.charAt(0).toUpperCase() + player.status.slice(1)}
          </span>
        </>
      )}
    </div>
  );

  // Player specific action icons - empty now as per user request
  const playerActions = (<></>);

  return (
    <MemberComponent
      name={player.name}
      profileImage={player.profileImage}
      injectedInfo={playerInfo}
      actionContent={playerActions}
      isSelected={isSelected}
      onClick={() => onSelect(player.id)}
    />
  );
};

export default PlayerCard;