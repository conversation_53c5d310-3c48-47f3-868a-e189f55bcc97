import React from 'react';
import { IonButton, IonIcon } from '@ionic/react';
import { SHOT_COLORS } from '../../../styles/updatedShotColors';
import { buttonStyles, getOutlineButtonStyle } from '../../../styles/updatedButtonStyles';

export interface EnhancedShotButtonProps {
  /**
   * Button color from the SHOT color system
   * @default 'purple'
   */
  color?: keyof typeof SHOT_COLORS;
  
  /**
   * Whether to use outline style
   * @default false
   */
  outline?: boolean;
  
  /**
   * Button content
   */
  children: React.ReactNode;
  
  /**
   * Click handler
   */
  onClick?: () => void;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Width of the button
   * - 'block': full width of its container
   * - 'full': full width of the screen
   */
  expand?: 'block' | 'full';
  
  /**
   * Button size
   * @default 'default'
   */
  size?: 'small' | 'default' | 'large';
  
  /**
   * Optional icon to display
   */
  icon?: string;
  
  /**
   * Position of the icon
   * @default 'start'
   */
  iconPosition?: 'start' | 'end';
  
  /**
   * Whether the button is disabled
   * @default false
   */
  disabled?: boolean;
  
  /**
   * Additional props to pass to IonButton
   */
  [x: string]: any;
}

/**
 * EnhancedShotButton - An updated standardized button component for the SHOT app
 * 
 * Uses the enhanced SHOT color system and provides a consistent button styling experience.
 */
const EnhancedShotButton: React.FC<EnhancedShotButtonProps> = ({
  color = 'purple',
  outline = false,
  children,
  className = '',
  expand,
  size,
  icon,
  iconPosition = 'start',
  disabled = false,
  ...props
}) => {
  // Get the appropriate style based on color and outline options
  const style = outline 
    ? getOutlineButtonStyle(color as any) 
    : buttonStyles[color as keyof typeof buttonStyles] || buttonStyles.purple;
  
  return (
    <IonButton
      fill={outline ? 'outline' : 'solid'}
      expand={expand}
      size={size}
      style={style}
      className={className}
      disabled={disabled}
      {...props}
    >
      {icon && iconPosition === 'start' && (
        <IonIcon icon={icon} slot="start" />
      )}
      
      {children}
      
      {icon && iconPosition === 'end' && (
        <IonIcon icon={icon} slot="end" />
      )}
    </IonButton>
  );
};

export default EnhancedShotButton;
