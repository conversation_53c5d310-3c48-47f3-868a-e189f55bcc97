import React from 'react';
import { IonButton, IonIcon } from '@ionic/react';
import { getShotButtonStyle } from '../../styles/shotBrandUtils';

interface ShotButtonProps {
  variant?: 'primary' | 'secondary' | 'tertiary' | 'outline' | 'ghost' | 'small';
  size?: 'default' | 'small' | 'large';
  fill?: 'solid' | 'outline' | 'clear';
  expand?: 'full' | 'block';
  icon?: string;
  iconSlot?: 'start' | 'end' | 'icon-only';
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  children?: React.ReactNode;
  style?: React.CSSProperties;
  type?: 'button' | 'submit' | 'reset';
}

/**
 * Enhanced SHOT Button Component
 * Ensures complete brand compliance with SHOT UX Design System
 * Implements consistent 8px border radius and SHOT typography
 */
const ShotButton: React.FC<ShotButtonProps> = ({
  variant = 'primary',
  size = 'default',
  fill = 'solid',
  expand,
  icon,
  iconSlot = 'start',
  onClick,
  disabled = false,
  className = '',
  children,
  style = {},
  type = 'button'
}) => {
  // Get SHOT button styling
  const shotStyle = getShotButtonStyle(variant);
  
  // Combine SHOT styling with custom styles
  const combinedStyle = {
    ...shotStyle,
    ...style
  };
  
  // Determine size-specific adjustments
  const sizeAdjustments = (() => {
    switch (size) {
      case 'small':
        return {
          '--min-height': '32px',
          '--padding-start': '16px',
          '--padding-end': '16px',
          '--border-radius': '6px',
          fontSize: '0.875rem'
        };
      case 'large':
        return {
          '--min-height': '56px',
          '--padding-start': '32px',
          '--padding-end': '32px',
          fontSize: '1.125rem'
        };
      default:
        return {};
    }
  })();
  
  // Final button style
  const finalStyle = {
    ...combinedStyle,
    ...sizeAdjustments
  };
  
  return (
    <IonButton
      fill={fill}
      expand={expand}
      onClick={onClick}
      disabled={disabled}
      className={`shot-button shot-button-${variant} ${className}`}
      style={finalStyle}
      type={type}
    >
      {icon && iconSlot === 'start' && <IonIcon slot="start" icon={icon} />}
      {icon && iconSlot === 'icon-only' && <IonIcon slot="icon-only" icon={icon} />}
      {children}
      {icon && iconSlot === 'end' && <IonIcon slot="end" icon={icon} />}
    </IonButton>
  );
};

export default ShotButton;

// Export additional utility function for consistent button styling
export const applyShotButtonStyle = (variant: 'primary' | 'secondary' | 'tertiary' | 'outline' | 'ghost' | 'small' = 'primary') => {
  return getShotButtonStyle(variant);
};

// Export SHOT button class names for CSS application
export const SHOT_BUTTON_CLASSES = {
  primary: 'shot-button-primary',
  secondary: 'shot-button-secondary', 
  tertiary: 'shot-button-tertiary',
  outline: 'shot-button-outline',
  ghost: 'shot-button-ghost',
  small: 'shot-button-small'
};
