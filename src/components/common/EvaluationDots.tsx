import React from 'react';
// Note: shot-components.css is imported globally in index.css

interface EvaluationDotsProps {
  ratings: {
    technical: number;
    physical: number;
    psychological: number;
    social: number;
    positional: number;
  };
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

const EvaluationDots: React.FC<EvaluationDotsProps> = ({ 
  ratings, 
  size = 'medium', 
  className = '' 
}) => {
  const getRatingColorClass = (rating: number): string => {
    if (rating === 0) return 'shot-evaluation-dot shot-evaluation-dot--not-started';
    if (rating <= 2) return 'shot-evaluation-dot shot-evaluation-dot--low';
    if (rating === 3) return 'shot-evaluation-dot shot-evaluation-dot--average';
    if (rating === 4) return 'shot-evaluation-dot shot-evaluation-dot--good';
    return 'shot-evaluation-dot shot-evaluation-dot--high';
  };

  const getSizeClass = (size: string): string => {
    switch (size) {
      case 'small':
        return 'shot-evaluation-dots--small';
      case 'large':
        return 'shot-evaluation-dots--large';
      default:
        return '';
    }
  };

  return (
    <div className={`shot-evaluation-dots ${getSizeClass(size)} ${className}`}>
      <div 
        className={getRatingColorClass(ratings.technical)}
        title={`Technical: ${ratings.technical}/5`}
      ></div>
      <div 
        className={getRatingColorClass(ratings.physical)}
        title={`Physical: ${ratings.physical}/5`}
      ></div>
      <div 
        className={getRatingColorClass(ratings.psychological)}
        title={`Psychological: ${ratings.psychological}/5`}
      ></div>
      <div 
        className={getRatingColorClass(ratings.social)}
        title={`Social: ${ratings.social}/5`}
      ></div>
      <div 
        className={getRatingColorClass(ratings.positional)}
        title={`Positional: ${ratings.positional}/5`}
      ></div>
    </div>
  );
};

export default EvaluationDots;