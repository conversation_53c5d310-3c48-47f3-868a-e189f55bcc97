import React, { useState } from 'react';
import PlayerCard from './PlayerCard';
import './MemberList.css';

/**
 * MemberListDemo - A demonstration component showing how to achieve the black spacing effect
 * between player cards.
 */
const MemberListDemo: React.FC = () => {
  const [selectedPlayerId, setSelectedPlayerId] = useState<string>('');

  // Sample player data
  const players = [
    {
      id: '1',
      name: '<PERSON>',
      position: 'Forward',
      age: 15,
      profileImage: '/avatar/SHOT avatar1.png',
      status: 'active'
    },
    {
      id: '2',
      name: '<PERSON>',
      position: 'Midfielder',
      age: 16,
      profileImage: '/avatar/SHOT avatar2.png',
      status: 'injured'
    },
    {
      id: '3',
      name: '<PERSON>',
      position: 'Defender',
      age: 14,
      profileImage: '/avatar/SHOT avatar3.png',
      status: 'active'
    },
    {
      id: '4',
      name: '<PERSON>',
      position: 'Goalkeeper',
      age: 15,
      profileImage: '/avatar/SHOT avatar4.png',
      status: 'active'
    }
  ];

  // Handle player selection
  const handleSelectPlayer = (playerId: string) => {
    setSelectedPlayerId(playerId);
  };

  return (
    <div className="p-4">
      <h2 className="text-white text-xl mb-4">Team Members</h2>
      
      {/* Player list with black spacing */}
      <div className="member-list">
        {players.map(player => (
          <div key={player.id} className="member-list-item">
            <PlayerCard
              player={player}
              isSelected={player.id === selectedPlayerId}
              onSelect={handleSelectPlayer}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default MemberListDemo;