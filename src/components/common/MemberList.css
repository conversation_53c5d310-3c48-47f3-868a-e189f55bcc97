/* MemberList.css - Custom styles for the member list */

.member-list {
  background-color: #000000; /* Pure black background */
  display: flex;
  flex-direction: column;
  gap: 4px; /* Creates consistent spacing of 4px between items */
  padding: 0;
  margin: 0;
  border-radius: 8px;
  overflow: hidden;
}

/* Style for each member container */
.member-list-item {
  margin: 0;
  padding: 0;
}

/* Style for first child to remove top spacing */
.member-list-item:first-child {
  margin-top: 0;
}

/* Style for last child to remove bottom spacing */
.member-list-item:last-child {
  margin-bottom: 0;
}
