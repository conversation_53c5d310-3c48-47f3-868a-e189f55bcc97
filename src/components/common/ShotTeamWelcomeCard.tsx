import React, { useState, useEffect } from 'react';
import {
  IonCard,
  IonCardContent,
  IonIcon,
  IonText,
  IonAvatar,
  IonSkeletonText
} from '@ionic/react';
import {
  checkmarkCircle,
  school,
  personCircle
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import './ShotTeamWelcomeCard.css';

interface TeamWelcomeInfo {
  team_id: string;
  team_name: string;
  club_name?: string;
  coach_name?: string;
  coach_avatar_url?: string;
  welcome_message?: string;
}

interface ShotTeamWelcomeCardProps {
  inviteCode: string;
  className?: string;
}

/**
 * ShotTeamWelcomeCard - Displays team information and coach welcome message
 * Fetches team and coach details based on invite code
 */
const ShotTeamWelcomeCard: React.FC<ShotTeamWelcomeCardProps> = ({
  inviteCode,
  className = ''
}) => {
  const [teamInfo, setTeamInfo] = useState<TeamWelcomeInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (inviteCode) {
      fetchTeamInfo();
    }
  }, [inviteCode]);

  const fetchTeamInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch invite code and team details
      const { data: inviteData, error: inviteError } = await supabase
        .from('invite_codes')
        .select(`
          team_id,
          teams!inner (
            team_name,
            club_id,
            clubs (
              club_name
            )
          )
        `)
        .eq('code', inviteCode)
        .eq('is_valid', true)
        .single();

      if (inviteError) {
        throw new Error('Invalid invite code');
      }

      if (!inviteData?.teams) {
        throw new Error('Team not found');
      }

      // Fetch coach information for the team
      const { data: coachData, error: coachError } = await supabase
        .from('team_coaches')
        .select(`
          profiles!inner (
            full_name,
            avatar_url
          )
        `)
        .eq('team_id', inviteData.team_id)
        .eq('is_active', true)
        .limit(1)
        .single();

      // Build team info object
      const teamInfo: TeamWelcomeInfo = {
        team_id: inviteData.team_id,
        team_name: inviteData.teams.team_name,
        club_name: inviteData.teams.clubs?.club_name,
        coach_name: coachData?.profiles?.full_name,
        coach_avatar_url: coachData?.profiles?.avatar_url,
        welcome_message: `Welcome to ${inviteData.teams.team_name}! We're excited to have you join our team.`
      };

      setTeamInfo(teamInfo);
    } catch (err: any) {
      console.error('Error fetching team info:', err);
      setError(err.message || 'Failed to load team information');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <IonCard className={`shot-card team-welcome-card ${className}`}>
        <IonCardContent>
          <div className="team-welcome-loading">
            <IonSkeletonText animated style={{ width: '60%', height: '24px' }} />
            <IonSkeletonText animated style={{ width: '80%', height: '16px', marginTop: '8px' }} />
            <IonSkeletonText animated style={{ width: '40%', height: '16px', marginTop: '8px' }} />
          </div>
        </IonCardContent>
      </IonCard>
    );
  }

  if (error || !teamInfo) {
    return (
      <IonCard className={`shot-card team-welcome-card error ${className}`}>
        <IonCardContent>
          <div className="team-welcome-error">
            <IonText color="danger">
              <p>{error || 'Unable to load team information'}</p>
            </IonText>
          </div>
        </IonCardContent>
      </IonCard>
    );
  }

  return (
    <IonCard className={`shot-card team-welcome-card ${className}`}>
      <IonCardContent>
        <div className="team-welcome-content">
          {/* Verification Icon */}
          <div className="verification-badge">
            <IonIcon icon={checkmarkCircle} className="verified-icon" />
          </div>

          {/* Team Information */}
          <div className="team-info">
            <h3 className="team-name">{teamInfo.team_name}</h3>
            
            {teamInfo.club_name && (
              <div className="club-info">
                <IonIcon icon={school} className="club-icon" />
                <span className="club-name">{teamInfo.club_name}</span>
              </div>
            )}
          </div>

          {/* Coach Welcome Section */}
          {teamInfo.coach_name && (
            <div className="coach-welcome">
              <div className="coach-info">
                <IonAvatar className="coach-avatar">
                  {teamInfo.coach_avatar_url ? (
                    <img 
                      src={teamInfo.coach_avatar_url} 
                      alt={`Coach ${teamInfo.coach_name}`}
                      onError={(e) => {
                        // Fallback to default icon if image fails
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const parent = target.parentElement;
                        if (parent) {
                          parent.innerHTML = '<ion-icon name="person-circle"></ion-icon>';
                        }
                      }}
                    />
                  ) : (
                    <IonIcon icon={personCircle} />
                  )}
                </IonAvatar>
                
                <div className="coach-details">
                  <span className="coach-label">Your Coach</span>
                  <span className="coach-name">{teamInfo.coach_name}</span>
                </div>
              </div>

              {/* Welcome Message */}
              {teamInfo.welcome_message && (
                <div className="welcome-message">
                  <IonText>
                    <p>"{teamInfo.welcome_message}"</p>
                  </IonText>
                </div>
              )}
            </div>
          )}
        </div>
      </IonCardContent>
    </IonCard>
  );
};

export default ShotTeamWelcomeCard;