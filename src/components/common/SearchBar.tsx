import React from 'react';
import { IonSearchbar } from '@ionic/react';
import './SearchBar.css';

interface SearchBarProps {
  value: string;
  onSearch: (value: string) => void;
  placeholder?: string;
  debounceTime?: number;
  minSearchLength?: number;
  className?: string;
  autoFocus?: boolean;
}

/**
 * A reusable search component with consistent styling across the application.
 * Features:
 * - Rounded edges with consistent styling
 * - Progressive search that triggers after typing a minimum number of characters
 * - Debounced search to reduce unnecessary API calls or filtering operations
 * - Consistent placeholder and styling
 */
const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onSearch,
  placeholder = 'Search...',
  debounceTime = 300,
  minSearchLength = 2,
  className = '',
  autoFocus = false,
}) => {
  // Internal state to track the input value
  const [inputValue, setInputValue] = React.useState(value);
  
  // Debounce the search to avoid excessive updates
  React.useEffect(() => {
    // If input is less than minSearchLength, don't trigger search
    if (inputValue.length > 0 && inputValue.length < minSearchLength) {
      return;
    }
    
    // Set up debounce timer
    const timer = setTimeout(() => {
      onSearch(inputValue);
    }, debounceTime);
    
    // Clean up timer on component unmount or when inputValue changes
    return () => clearTimeout(timer);
  }, [inputValue, onSearch, debounceTime, minSearchLength]);
  
  // Update internal state when external value changes
  React.useEffect(() => {
    setInputValue(value);
  }, [value]);
  
  return (
    <div className={`shot-search-container ${className}`}>
      <IonSearchbar
        value={inputValue}
        onIonInput={(e) => setInputValue(e.detail.value || '')}
        placeholder={placeholder}
        className="shot-search-bar ion-no-padding"
        showCancelButton="never"
        inputmode="text"
        autocomplete="off"
        autofocus={autoFocus}
        debounce={0} // We're handling debounce ourselves
        style={{
          '--border-radius': '100px',
          borderRadius: '100px',
          background: '#1f2937',
          overflow: 'hidden'
        }}
      />
    </div>
  );
};

export default SearchBar;
