import React from 'react';
import { IonIcon, IonBadge, IonButton } from '@ionic/react';
import { personOutline } from 'ionicons/icons';
export interface PlayerListItemData {
  id: string;
  full_name: string;
  position?: string;
  avatar_url?: string;
  status?: 'invited' | 'confirmed' | 'declined' | 'attended' | 'active' | 'inactive';
  role?: string;
  hasExistingEvaluation?: boolean;
}

export interface PlayerListItemActions {
  primary?: {
    icon: string;
    color?: string;
    onClick: (player: PlayerListItemData) => void;
    tooltip?: string;
  };
  secondary?: {
    icon: string;
    color?: string;
    onClick: (player: PlayerListItemData) => void;
    tooltip?: string;
  };
  badge?: {
    text: string;
    color: string;
    icon?: string;
    onClick?: (player: PlayerListItemData) => void;
    tooltip?: string;
  };
}

export interface PlayerListItemProps {
  player: PlayerListItemData;
  actions?: PlayerListItemActions;
  onClick?: (player: PlayerListItemData) => void;
  className?: string;
  showEvaluationDots?: boolean;
  evaluationRatings?: {
    technical: number;
    physical: number;
    psychological: number;
    social: number;
    positional: number;
  };
  rightContent?: React.ReactNode;
}

/**
 * PlayerListItem - Standardized player card component used across the app
 * 
 * Used in:
 * - Event Participants List
 * - Event Evaluation
 * - Team Management
 * - Attendance tracking
 */
const PlayerListItem: React.FC<PlayerListItemProps> = ({
  player,
  actions,
  onClick,
  className = '',
  showEvaluationDots = false,
  evaluationRatings,
  rightContent
}) => {
  
  const getRatingColor = (rating: number): string => {
    if (rating === 0) return 'shot-evaluation-dot--not-started';
    if (rating <= 2) return 'shot-evaluation-dot--low';
    if (rating === 3) return 'shot-evaluation-dot--average';
    return 'shot-evaluation-dot--high';
  };

  const getStatusColor = (status?: string): string => {
    switch (status) {
      case 'confirmed':
      case 'attended':
      case 'active':
        return 'success';
      case 'declined':
      case 'inactive':
        return 'danger';
      case 'invited':
        return 'warning';
      default:
        return 'medium';
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'confirmed':
      case 'attended':
      case 'active':
        return 'checkmark-circle';
      case 'declined':
      case 'inactive':
        return 'close-circle';
      case 'invited':
        return 'help-circle';
      default:
        return 'help-circle';
    }
  };

  const formatStatus = (status?: string): string => {
    if (!status) return '';
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const handleCardClick = () => {
    if (onClick) {
      onClick(player);
    }
  };

  const handleActionClick = (e: React.MouseEvent, action: () => void) => {
    e.stopPropagation();
    action();
  };

  return (
    <div 
      className={`shot-card ${className}`}
      style={{
        padding: '16px',
        marginBottom: '1px',
        backgroundColor: '#374151',
        borderBottom: '1px solid #4b5563',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        cursor: onClick ? 'pointer' : 'default',
        transition: 'all 0.2s ease',
        borderLeft: player.hasExistingEvaluation ? '4px solid #10b981' : 'none'
      }}
      onClick={handleCardClick}
      onMouseEnter={(e) => {
        if (onClick) {
          e.currentTarget.style.backgroundColor = '#4b5563';
        }
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = '#374151';
      }}
    >
      {/* Left side - Avatar and Info */}
      <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
        {/* Avatar */}
        <div style={{ marginRight: '12px' }}>
          {player.avatar_url ? (
            <img 
              src={player.avatar_url} 
              alt={player.full_name}
              style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                objectFit: 'cover'
              }}
            />
          ) : (
            <div style={{
              width: '40px',
              height: '40px',
              borderRadius: '50%',
              backgroundColor: '#4b5563',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <span style={{ color: 'white', fontSize: '18px', fontWeight: '500' }}>
                {player.full_name.charAt(0)}
              </span>
            </div>
          )}
        </div>

        {/* Player Details */}
        <div>
          <h3 style={{ 
            color: 'white', 
            fontSize: '16px', 
            fontWeight: '500', 
            margin: '0 0 4px 0',
            fontFamily: 'var(--shot-font-heading)'
          }}>
            {player.full_name}
            {player.hasExistingEvaluation && (
              <span style={{
                marginLeft: '8px',
                fontSize: '12px',
                backgroundColor: 'rgba(16, 185, 129, 0.2)',
                color: '#10b981',
                padding: '2px 6px',
                borderRadius: '12px'
              }}>
                Evaluated
              </span>
            )}
          </h3>
          <div style={{ 
            color: '#9ca3af', 
            fontSize: '14px',
            fontFamily: 'var(--shot-font-body)'
          }}>
            {player.role && (
              <span>{player.role.charAt(0).toUpperCase() + player.role.slice(1)}</span>
            )}
            {player.position && (
              <>
                {player.role && <span style={{ margin: '0 4px' }}>•</span>}
                <span>{player.position}</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Right side - Actions and Content */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        
        {/* Evaluation Dots */}
        {showEvaluationDots && evaluationRatings && (
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
            <div style={{ display: 'flex', gap: '6px', marginBottom: '4px' }}>
              <div 
                className={`shot-evaluation-dot ${getRatingColor(evaluationRatings.technical)}`}
                title={`Technical: ${evaluationRatings.technical}`}
              ></div>
              <div 
                className={`shot-evaluation-dot ${getRatingColor(evaluationRatings.physical)}`}
                title={`Physical: ${evaluationRatings.physical}`}
              ></div>
              <div 
                className={`shot-evaluation-dot ${getRatingColor(evaluationRatings.psychological)}`}
                title={`Psychological: ${evaluationRatings.psychological}`}
              ></div>
              <div 
                className={`shot-evaluation-dot ${getRatingColor(evaluationRatings.social)}`}
                title={`Social: ${evaluationRatings.social}`}
              ></div>
              <div 
                className={`shot-evaluation-dot ${getRatingColor(evaluationRatings.positional)}`}
                title={`Positional: ${evaluationRatings.positional}`}
              ></div>
            </div>
          </div>
        )}

        {/* Custom Right Content */}
        {rightContent}

        {/* Status Badge */}
        {actions?.badge && (
          <IonBadge 
            color={actions.badge.color}
            style={{ cursor: actions.badge.onClick ? 'pointer' : 'default' }}
            onClick={actions.badge.onClick ? (e) => handleActionClick(e, () => actions.badge!.onClick!(player)) : undefined}
            title={actions.badge.tooltip}
          >
            {actions.badge.icon && <IonIcon name={actions.badge.icon} style={{ marginRight: '4px' }} />}
            {actions.badge.text}
          </IonBadge>
        )}

        {/* Primary Action */}
        {actions?.primary && (
          <IonButton
            fill="clear"
            color={actions.primary.color || 'primary'}
            size="small"
            onClick={(e) => handleActionClick(e, () => actions.primary!.onClick(player))}
            title={actions.primary.tooltip}
          >
            <IonIcon name={actions.primary.icon} />
          </IonButton>
        )}

        {/* Secondary Action */}
        {actions?.secondary && (
          <IonButton
            fill="clear"
            color={actions.secondary.color || 'medium'}
            size="small"
            onClick={(e) => handleActionClick(e, () => actions.secondary!.onClick(player))}
            title={actions.secondary.tooltip}
          >
            <IonIcon name={actions.secondary.icon} />
          </IonButton>
        )}
      </div>
    </div>
  );
};

export default PlayerListItem;