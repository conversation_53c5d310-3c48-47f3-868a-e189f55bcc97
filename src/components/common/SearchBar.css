/* SearchBar component styles */

.shot-search-container {
  margin-bottom: 16px;
  padding: 0 16px;
}

/* Base styling for the search bar */
.shot-search-bar {
  --background: #1f2937 !important;
  --color: white !important;
  --placeholder-color: rgba(255, 255, 255, 0.7) !important;
  --placeholder-opacity: 1 !important;
  --icon-color: rgba(255, 255, 255, 0.7) !important;
  --clear-button-color: rgba(255, 255, 255, 0.7) !important;
  --border-radius: 100px !important;
  --box-shadow: none !important;
  
  /* Padding and margin */
  padding: 4px 8px;
  margin-bottom: 0;
  border-radius: 100px !important;
  overflow: hidden !important;
  background: #1f2937 !important;
  height: 48px !important;
}

/* Focus state for the search bar */
.shot-search-bar:focus-within {
  --background: #2d3748 !important;
}

/* Input text styling */
.shot-search-bar::part(input) {
  color: white !important;
  caret-color: #6b7280 !important;
  font-size: 14px !important;
  padding-left: 8px !important;
}

/* Search icon styling */
.shot-search-bar::part(icon) {
  color: rgba(255, 255, 255, 0.7) !important;
  opacity: 0.8 !important;
}

/* Cancel button styling */
.shot-search-bar::part(cancel-button) {
  color: white !important;
  display: none !important;
}

/* Clear button (X) styling */
.shot-search-bar::part(clear-button) {
  color: rgba(255, 255, 255, 0.7) !important;
}

/* Specific styling to force rounded look */
.shot-search-bar .searchbar-input-container {
  border-radius: 100px !important;
  overflow: hidden !important;
}

.shot-search-bar .searchbar-input {
  border-radius: 100px !important;
  background: #1f2937 !important;
}

/* Styling for small screens */
@media (max-width: 576px) {
  .shot-search-container {
    padding: 0 8px;
  }
  
  .shot-search-bar {
    height: 40px !important;
  }
}
