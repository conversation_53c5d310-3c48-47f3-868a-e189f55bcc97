import React from 'react';
import './MemberComponent.css';


/**
 * MemberComponent - Reusable component for displaying player/coach information
 * with a modern, dark design.
 * 
 * @param {Object} props
 * @param {string} props.name - The name of the member
 * @param {string} props.profileImage - URL to the profile image (optional)
 * @param {React.ReactNode} props.injectedInfo - Custom information to be displayed (position, age, etc.)
 * @param {React.ReactNode} props.actionContent - Custom action buttons/content (icons on the right)
 * @param {boolean} props.isSelected - Whether the member is selected or highlighted
 * @param {function} props.onClick - Function to call when the component is clicked
 * @returns {React.ReactElement}
 */
interface MemberComponentProps {
  name: string;
  profileImage?: string;
  injectedInfo: React.ReactNode;
  actionContent: React.ReactNode;
  isSelected?: boolean;
  onClick?: () => void;
  className?: string;
}

const MemberComponent: React.FC<MemberComponentProps> = ({ 
  name, 
  profileImage, 
  injectedInfo, 
  actionContent,
  isSelected = false,
  onClick,
  className = ''
}) => {
  return (
    <div 
      className={`w-full flex items-center p-3 pr-6 member-component ${className} ${isSelected ? 'selected' : ''}`}
      onClick={onClick}
    >
      {/* Left section with profile image */}
      <div className="flex-shrink-0 mr-4">
        <div className="h-12 w-12 rounded-full overflow-hidden bg-gray-700 flex items-center justify-center">
          {profileImage ? (
            <img 
              src={profileImage} 
              alt={`${name}'s profile`} 
              className="h-full w-full object-cover"
            />
          ) : (
            <span className="text-white text-lg font-bold">{name.charAt(0)}</span>
          )}
        </div>
      </div>
      
      {/* Middle section with name and injected info */}
      <div className="flex-grow">
        {/* Name */}
        <h3 className="text-base font-medium text-white">{name}</h3>
        
        {/* Injected Info */}
        <div className="text-sm text-gray-400">
          {injectedInfo}
        </div>
      </div>
      
      {/* Right section with action content */}
      <div className="flex-shrink-0 flex space-x-4">
        {actionContent}
      </div>
    </div>
  );
};

export default MemberComponent;