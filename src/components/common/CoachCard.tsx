import React from 'react';
import MemberComponent from './MemberComponent';

interface CoachCardProps {
  coach: {
    id: string;
    name: string;
    role: string;
    certifications?: string;
    profileImage?: string;
    isPrimary?: boolean;
  };
  isSelected?: boolean;
  onSelect: (id: string) => void;
}

const CoachCard: React.FC<CoachCardProps> = ({ coach, isSelected, onSelect }) => {
  // Coach specific injected info (yellow part)
  const coachInfo = (
    <div className="flex items-center">
      <span className="mr-2">{coach.role}</span>
      {coach.certifications && (
        <>
          <span className="text-gray-500">•</span>
          <span className="ml-2 px-2 py-0.5 bg-blue-900 text-blue-300 rounded-full text-xs">
            {coach.certifications}
          </span>
        </>
      )}
      {coach.isPrimary && (
        <>
          <span className="text-gray-500 ml-2">•</span>
          <span className="ml-2 px-2 py-0.5 bg-green-900 text-green-300 rounded-full text-xs">
            Primary
          </span>
        </>
      )}
    </div>
  );

  // Coach specific action content (red part)
  const coachActions = (
    <>
      <button className="p-1 rounded-full text-green-400 hover:bg-gray-700">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
        </svg>
      </button>
      <button className="p-1 rounded-full text-purple-400 hover:bg-gray-700">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
          <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
        </svg>
      </button>
      <button className="p-1 rounded-full text-gray-400 hover:bg-gray-700">
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z" />
        </svg>
      </button>
    </>
  );

  return (
    <MemberComponent
      name={coach.name}
      profileImage={coach.profileImage}
      injectedInfo={coachInfo}
      actionContent={coachActions}
      isSelected={isSelected}
      onClick={() => onSelect(coach.id)}
    />
  );
};

export default CoachCard;