import React from 'react';
import {
  IonGrid,
  IonRow,
  IonCol,
  IonText
} from '@ionic/react';
import './ShotAvatarSelector.css';

interface AvatarOption {
  id: string;
  label: string;
  imagePath: string;
}

interface ShotAvatarSelectorProps {
  selectedAvatar: string;
  onAvatarSelect: (avatarId: string) => void;
  className?: string;
}

/**
 * ShotAvatarSelector - Reusable avatar selection component using SHOT sport head images
 * Uses the sport head images from /public/avatar/ directory
 */
const ShotAvatarSelector: React.FC<ShotAvatarSelectorProps> = ({
  selectedAvatar,
  onAvatarSelect,
  className = ''
}) => {
  // Available sport head avatars from /public/avatar/
  const avatarOptions: AvatarOption[] = [
    { id: 'avatar1', label: 'Sport Head 1', imagePath: '/avatar/SHOT avatar1.png' },
    { id: 'avatar2', label: 'Sport Head 2', imagePath: '/avatar/SHOT avatar2.png' },
    { id: 'avatar3', label: 'Sport Head 3', imagePath: '/avatar/SHOT avatar3.png' },
    { id: 'avatar4', label: 'Sport Head 4', imagePath: '/avatar/SHOT avatar4.png' },
    { id: 'avatar5', label: 'Sport Head 5', imagePath: '/avatar/SHOT avatar5.png' },
    { id: 'avatar6', label: 'Sport Head 6', imagePath: '/avatar/SHOT avatar6.png' },
    { id: 'avatar7', label: 'Sport Head 7', imagePath: '/avatar/SHOT avatar7.png' },
    { id: 'avatar8', label: 'Sport Head 8', imagePath: '/avatar/SHOT avatar8.png' },
    { id: 'avatar9', label: 'Sport Head 9', imagePath: '/avatar/SHOT avatar9.png' }
  ];

  return (
    <div className={`shot-avatar-selector ${className}`}>
      <IonGrid>
        <IonRow>
          {avatarOptions.map((option) => (
            <IonCol size="4" sizeMd="3" key={option.id}>
              <div 
                className={`avatar-option ${selectedAvatar === option.id ? 'selected' : ''}`}
                onClick={() => onAvatarSelect(option.id)}
              >
                <div className="avatar-image-container">
                  <img 
                    src={option.imagePath} 
                    alt={option.label}
                    className="avatar-image"
                    onError={(e) => {
                      // Fallback if image fails to load
                      console.warn(`Failed to load avatar image: ${option.imagePath}`);
                    }}
                  />
                </div>
                <IonText className="avatar-label">
                  {option.label}
                </IonText>
              </div>
            </IonCol>
          ))}
        </IonRow>
      </IonGrid>
    </div>
  );
};

export default ShotAvatarSelector;