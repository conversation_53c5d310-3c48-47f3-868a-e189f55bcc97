/* ShotAvatarSelector Styles */

.shot-avatar-selector {
  padding: 16px 0;
}

.avatar-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  margin: 8px;
  border-radius: var(--shot-button-radius);
  border: 2px solid rgba(255, 255, 255, 0.1);
  background-color: rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 120px;
  justify-content: center;
}

.avatar-option:hover {
  border-color: var(--shot-teal);
  background-color: rgba(26, 188, 156, 0.1);
  transform: translateY(-2px);
}

.avatar-option.selected {
  border-color: var(--shot-teal);
  background-color: rgba(26, 188, 156, 0.15);
  box-shadow: 0 0 0 2px var(--shot-teal);
}

.avatar-image-container {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.1);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatar-label {
  font-family: var(--shot-font-body);
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  color: var(--shot-white);
  line-height: 1.2;
}

.avatar-option.selected .avatar-label {
  color: var(--shot-teal);
  font-weight: 600;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .avatar-option {
    margin: 4px;
    padding: 8px;
    min-height: 100px;
  }

  .avatar-image-container {
    width: 50px;
    height: 50px;
  }

  .avatar-label {
    font-size: 0.6rem;
  }
}
