import React from 'react';
import { IonIcon } from '@ionic/react';
import { clipboard } from 'ionicons/icons';

interface PreAssessmentBadgeProps {
  className?: string;
  style?: React.CSSProperties;
}

/**
 * A standardized badge component for indicating pre-assessment functionality
 * 
 * Usage:
 * ```jsx
 * <PreAssessmentBadge />
 * ```
 */
const PreAssessmentBadge: React.FC<PreAssessmentBadgeProps> = ({ 
  className = '', 
  style = {} 
}) => {
  return (
    <span className={`shot-badge-pre-assessment ${className}`} style={style}>
      <IonIcon icon={clipboard} />
      PRE-ASSESSMENT
    </span>
  );
};

export default PreAssessmentBadge;
