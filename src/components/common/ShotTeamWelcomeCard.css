/* ShotTeamWelcomeCard Styles */

.team-welcome-card {
  margin: 16px 0;
  border: 2px solid var(--shot-teal);
  background: linear-gradient(135deg, rgba(26, 188, 156, 0.1) 0%, rgba(0, 0, 0, 0.8) 100%);
  position: relative;
  overflow: hidden;
}

.team-welcome-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--shot-teal);
  z-index: 1;
}

.team-welcome-content {
  position: relative;
  z-index: 2;
}

.verification-badge {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.verified-icon {
  font-size: 2rem;
  color: var(--shot-teal);
  filter: drop-shadow(0 0 8px rgba(26, 188, 156, 0.5));
}

.team-info {
  text-align: center;
  margin-bottom: 20px;
}

.team-name {
  font-family: var(--shot-font-heading);
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--shot-white);
  margin: 0 0 8px 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.club-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
  font-weight: 500;
}

.club-icon {
  font-size: 1rem;
  color: var(--shot-teal);
}

.coach-welcome {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
}

.coach-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.coach-avatar {
  width: 48px;
  height: 48px;
  border: 2px solid var(--shot-teal);
}

.coach-avatar ion-icon {
  width: 100%;
  height: 100%;
  color: var(--shot-teal);
}

.coach-details {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.coach-label {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 2px;
}

.coach-name {
  font-family: var(--shot-font-heading);
  font-weight: 600;
  font-size: 1.125rem;
  color: var(--shot-white);
}

.welcome-message {
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--shot-button-radius);
  padding: 12px;
  border-left: 3px solid var(--shot-teal);
}

.welcome-message p {
  margin: 0;
  font-style: italic;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

.team-welcome-loading {
  padding: 16px 0;
}

.team-welcome-error {
  text-align: center;
  padding: 16px 0;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .team-name {
    font-size: 1.25rem;
  }

  .coach-info {
    gap: 10px;
  }

  .coach-avatar {
    width: 40px;
    height: 40px;
  }

  .coach-name {
    font-size: 1rem;
  }
}
