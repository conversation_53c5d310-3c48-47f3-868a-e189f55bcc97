import React from 'react';
import MemberComponent from './MemberComponent';
import ComponentWrapper from '../Debug/ComponentWrapper';
import { IonBadge, IonIcon } from '@ionic/react';
import { 
  checkmarkCircle,
  closeCircle,
  helpCircle
} from 'ionicons/icons';

// Define the status color mapping
const getStatusColor = (status: string): string => {
  switch (status) {
    case 'confirmed': return 'success';
    case 'declined': return 'danger';
    case 'attended': return 'primary';
    default: return 'warning'; // For 'invited' or unknown status
  }
};

// Get the appropriate icon for a status
const getStatusIcon = (status: string) => {
  switch (status) {
    case 'confirmed': return checkmarkCircle;
    case 'declined': return closeCircle;
    case 'attended': return checkmarkCircle;
    default: return helpCircle;
  }
};

// Interface for participant data
interface Participant {
  participant_id?: string;
  user_id: string;
  role?: string;
  invitation_status?: string;
  profiles?: {
    id?: string;
    full_name?: string;
    avatar_url?: string;
    position?: string;
  };
}

interface ParticipantCardProps {
  participant: Participant;
  isSelected?: boolean;
  onSelect?: (id: string) => void;
}

const ParticipantCard: React.FC<ParticipantCardProps> = ({ participant, isSelected, onSelect }) => {
  // Determine if debug mode is enabled from localStorage or environment variable
  const isDebugMode = localStorage.getItem('debugComponents') === 'true' || import.meta.env.VITE_DEBUG_COMPONENTS === 'true';
  
  // Get the participant's name or provide a fallback
  const name = participant.profiles?.full_name || 'Unknown Participant';
  
  // Get the participant's profile image if available
  const profileImage = participant.profiles?.avatar_url;
  
  // Participant specific info (role and position if available)
  const participantInfo = (
    <div className="flex items-center">
      <span className="mr-2">
        {participant.role ? participant.role.charAt(0).toUpperCase() + participant.role.slice(1) : 'Player'}
      </span>
      {participant.profiles?.position && (
        <>
          <span className="text-gray-500 mx-1">•</span>
          <span>{participant.profiles.position}</span>
        </>
      )}
    </div>
  );

  // Participant status badge
  const participantActions = (
    <IonBadge 
      color={getStatusColor(participant?.invitation_status || 'invited')}
    >
      <IonIcon icon={getStatusIcon(participant?.invitation_status || 'invited')} />
      {participant?.invitation_status 
        ? participant.invitation_status.charAt(0).toUpperCase() + participant.invitation_status.slice(1) 
        : 'Invited'}
    </IonBadge>
  );

  // Handle click on the component
  const handleClick = () => {
    if (onSelect && participant.user_id) {
      onSelect(participant.user_id);
    }
  };

  const content = (
    <MemberComponent
      name={name}
      profileImage={profileImage}
      injectedInfo={participantInfo}
      actionContent={participantActions}
      isSelected={isSelected}
      onClick={handleClick}
      className="participant-card"
    />
  );

  return isDebugMode ? (
    <ComponentWrapper name="ParticipantCard">
      {content}
    </ComponentWrapper>
  ) : content;
};

export default ParticipantCard;