# SearchBar Component

The `SearchBar` component provides a consistent search experience across the SHOT application with the following features:

- Rounded edges with consistent styling
- Progressive search that triggers after typing a minimum number of characters (defaults to 2)
- Debounced search to reduce unnecessary API calls or filtering operations
- Consistent placeholder and styling

## Usage

```tsx
import { SearchBar } from '../../../components/common';

const MyComponent = () => {
  const [searchText, setSearchText] = useState('');
  
  // Filter your data based on searchText
  const filteredData = useMemo(() => {
    // Your filtering logic here
    return data.filter(item => item.name.toLowerCase().includes(searchText.toLowerCase()));
  }, [data, searchText]);
  
  return (
    <div>
      <SearchBar
        value={searchText}
        onSearch={setSearchText}
        placeholder="Search by name..."
        minSearchLength={2}
        debounceTime={300}
      />
      
      {/* Render your filtered data */}
      {filteredData.map(item => (
        <div key={item.id}>{item.name}</div>
      ))}
    </div>
  );
};
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | string | (required) | Current search value |
| `onSearch` | function | (required) | Function called when search value changes |
| `placeholder` | string | "Search..." | Placeholder text for the search input |
| `minSearchLength` | number | 2 | Minimum number of characters to type before search is triggered |
| `debounceTime` | number | 300 | Delay in milliseconds before search is triggered after typing stops |
| `className` | string | "" | Additional CSS classes to apply to the container |
| `autoFocus` | boolean | false | Whether the search input should be focused on component mount |

## Design Consistency

The SearchBar component ensures consistency across all search fields in the application:

- Same styling, rounded edges, and visual appearance
- Same behavior for minimum search length and debouncing
- Same placeholder text formatting and focus states

Whenever you need a search field in the application, use this component instead of directly using IonSearchbar to maintain consistent user experience.
