/* MemberComponent.css - Styling for the MemberComponent */

/* Base component styles */
.member-component {
  background-color: #1e2532; /* Dark blue/gray background */
  margin-bottom: 2px; /* Gap between components */
  transition: all 0.2s ease-in-out;
  border-radius: 0; /* No rounded corners by default */
  width: 100%;
}

/* Container for ensuring proper spacing and coherent appearance */
.member-list-container {
  background-color: #111827; /* Dark background for gaps */
  padding: 0; /* No additional padding */
  border-radius: 8px; /* Rounded corners on the container */
  overflow: hidden; /* Ensure content doesn't overflow rounded corners */
}

.member-list-container > div {
  background-color: #111827; /* Dark background for gaps */
}

/* Apply rounded corners only to first and last items */
.member-list-container > div:first-child .member-component {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.member-list-container > div:last-child .member-component {
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  margin-bottom: 0; /* Remove margin from last item */
}

/* Selected state */
.member-component.selected {
  border-left: 4px solid #3b82f6; /* Blue highlight */
}

/* Hover effects */
.member-component:hover {
  background-color: #2d3748; /* Slightly lighter on hover */
}

/* Animation for any transitions */
.member-component-enter {
  opacity: 0;
  transform: translateY(5px);
}

.member-component-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 200ms, transform 200ms;
}

.member-component-exit {
  opacity: 1;
}

.member-component-exit-active {
  opacity: 0;
  transform: translateY(5px);
  transition: opacity 200ms, transform 200ms;
}

/* Fix any conflict with other styles */
.member-list-container > div:nth-child(odd) .member-component,
.member-list-container > div:nth-child(even) .member-component {
  background-color: #1e2532; /* Ensure all items have the same background */
}

/* Override any conflicting styles */
.bg-[#1e2532] {
  background-color: #1e2532 !important;
}