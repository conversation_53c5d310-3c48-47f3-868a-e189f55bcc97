// ABOUTME: Shared component for displaying coach teams list
// Used by both CoachDashboard and MemberPerform pages

import React from 'react';
import { ShadowInfoCard } from '../shadow/ShadowInfoCard';

interface CoachTeamsListProps {
  teams: Array<{
    id: string;
    name: string;
    [key: string]: any; // Allow additional properties
  }>;
  loading?: boolean;
  onTeamClick: (teamId: string) => void;
  showHeader?: boolean;
  headerText?: string;
}

export const CoachTeamsList: React.FC<CoachTeamsListProps> = ({
  teams,
  loading = false,
  onTeamClick,
  showHeader = true,
  headerText = "Your Teams"
}) => {
  if (loading) {
    return null; // Parent components handle loading state
  }

  return (
    <div className="px-4 pt-4">
      {teams.length > 0 ? (
        <>
          {/* Section Header */}
          {showHeader && (
            <h2 className="text-white text-xl font-semibold mb-4">{headerText}</h2>
          )}
          
          {/* Teams List */}
          <div className="max-w-3xl space-y-3">
            {teams.map(team => (
              <ShadowInfoCard
                key={team.id}
                variant="action"
                title={team.name}
                icon="chevronRight"
                size="medium"
                onClick={() => onTeamClick(team.id)}
              />
            ))}
          </div>
        </>
      ) : (
        <div className="text-center py-8 px-4 w-full">
          <p className="shot-body mb-4 opacity-70">You are not assigned as a coach to any teams</p>
          <p className="shot-caption">Ask your club administrator to assign you to teams</p>
        </div>
      )}
    </div>
  );
};

export default CoachTeamsList;