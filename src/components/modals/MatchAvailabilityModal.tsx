// ABOUTME:
// Modal component for players to set their match availability

import React from 'react';
import { ShadowModal } from '../shadow/ShadowModal';
import { useSetMatchAvailability } from '../../hooks/useSetMatchAvailability';

interface MatchAvailabilityModalProps {
  isOpen: boolean;
  onClose: () => void;
  matchId: string;
  userId: string;
  opponentName: string;
  matchDate: string;
  currentAvailability?: 'available' | 'unavailable' | 'maybe' | null;
  onAvailabilitySet: (availability: 'available' | 'unavailable' | 'maybe') => void;
}

const MatchAvailabilityModal: React.FC<MatchAvailabilityModalProps> = ({
  isOpen,
  onClose,
  matchId,
  userId,
  opponentName,
  matchDate,
  currentAvailability,
  onAvailabilitySet
}) => {
  const { setAvailability, isLoading } = useSetMatchAvailability();

  const handleSetAvailability = async (availability: 'available' | 'unavailable' | 'maybe') => {
    const result = await setAvailability(matchId, userId, availability);
    if (result.success) {
      onAvailabilitySet(availability);
      onClose();
    }
  };

  const availabilityOptions = [
    { value: 'available', label: 'Available', color: 'bg-green-500 hover:bg-green-600' },
    { value: 'maybe', label: 'Maybe', color: 'bg-yellow-500 hover:bg-yellow-600' },
    { value: 'unavailable', label: 'Unavailable', color: 'bg-red-500 hover:bg-red-600' }
  ] as const;

  return (
    <ShadowModal
      isOpen={isOpen}
      onClose={onClose}
      title="Set Match Availability"
    >
      <div className="p-6">
        <p className="text-white mb-2">
          Set your availability for the match:
        </p>
        <p className="text-gray-300 mb-6">
          <strong>vs {opponentName}</strong><br />
          {matchDate}
        </p>

        <div className="space-y-3">
          {availabilityOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => handleSetAvailability(option.value)}
              disabled={isLoading}
              className={`w-full py-3 px-4 rounded-lg text-white font-medium transition-colors ${option.color} ${
                isLoading ? 'opacity-50 cursor-not-allowed' : ''
              } ${currentAvailability === option.value ? 'ring-2 ring-white' : ''}`}
            >
              {option.label}
              {currentAvailability === option.value && ' (Current)'}
            </button>
          ))}
        </div>

        <button
          onClick={onClose}
          disabled={isLoading}
          className="w-full mt-4 py-2 px-4 bg-gray-600 hover:bg-gray-700 rounded-lg text-white transition-colors"
        >
          Cancel
        </button>
      </div>
    </ShadowModal>
  );
};

export default MatchAvailabilityModal;