# Debug Instructions for Pre-Evaluation Percentage Issue

## To Debug in Browser Console:

1. **Check specific event data:**
```javascript
// Replace with your event ID
await window.debugPreEval('25e5d142-3a0d-49e2-88b7-f2378385112b')
```

2. **Check all pre-evaluation data:**
```javascript
window.__debugPreEvaluationData
```

3. **Look for console logs with these emojis:**
- 🔍 ShadowEventCard DEBUG - Shows props passed to each card
- 🎯 Pre-Evaluation Data Debug - Shows calculated vs passed percentage
- 📄 Event Summaries from View - Raw data from database view
- 🌟 Event pre-eval stats from view - Parsed data for each event
- 📦 Rendering ShadowEventCard - Data just before rendering
- 🚨 FOUND PROBLEMATIC EVENT - If any event has 29% or 33%

## Expected Behavior:
- New events with no pre-evaluations should show 0%
- The percentage should come from `pre_eval_completion_percentage` in the view
- No frontend calculations should be happening

## Current Issue:
The view returns 0% correctly, but somewhere the UI is showing 33% or 29%.

## Possible Causes:
1. Stale data in React state
2. Old event data being reused
3. Component not re-rendering with new data
4. Wrong event ID being used

## Quick Fix to Test:
Add this to TeamFlat.tsx temporarily to force refresh:
```javascript
// At the top of loadTeamEvents function
setUpcomingEvents([]); // Clear old events first
```
