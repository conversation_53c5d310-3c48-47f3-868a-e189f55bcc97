import React, { useEffect, useState } from 'react';
import { IonButton, IonCard, IonCardContent, IonCardHeader, IonCardTitle } from '@ionic/react';
import { supabase } from '@/lib/supabase';

interface AuthDebugProps {
  onClose: () => void;
}

const AuthDebug: React.FC<AuthDebugProps> = ({ onClose }) => {
  const [authData, setAuthData] = useState<any>(null);
  const [sessionData, setSessionData] = useState<any>(null);

  useEffect(() => {
    // Get current user using v1.31.1 API
    const getCurrentAuth = async () => {
      try {
        // Get user from auth
        const { data: { session } } = await supabase.auth.getSession();
        const user = session?.user;
        console.log('🔍 Current user object:', user);
        setAuthData(user);
        setSessionData(session);

        // Check what email verification fields exist
        if (user) {
          console.log('🔍 User email verification fields:');
          console.log('  - email_confirmed_at:', user.email_confirmed_at);
          console.log('  - confirmed_at:', user.confirmed_at);
          console.log('  - email:', user.email);
          console.log('  - app_metadata:', user.app_metadata);
          console.log('  - user_metadata:', user.user_metadata);
        }
      } catch (error) {
        console.error('🚨 Error getting auth data:', error);
      }
    };

    getCurrentAuth();
  }, []);

  const refreshAuth = async () => {
    const { data: { session } } = await supabase.auth.getSession();
    const user = session?.user;
    setAuthData(user);
    setSessionData(session);
    console.log('🔄 Refreshed auth data');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <IonCard className="w-full max-w-2xl max-h-96 overflow-auto shot-card">
        <IonCardHeader className="shot-card-header">
          <IonCardTitle className="shot-card-title">Auth Debug Information</IonCardTitle>
        </IonCardHeader>
        <IonCardContent className="shot-card-content">
          <div className="space-y-4">
            <div>
              <h3 className="text-white font-semibold mb-2">Current User Object:</h3>
              <pre className="text-xs text-white bg-gray-800 p-2 rounded overflow-auto">
                {JSON.stringify(authData, null, 2)}
              </pre>
            </div>
            
            <div>
              <h3 className="text-white font-semibold mb-2">Current Session Object:</h3>
              <pre className="text-xs text-white bg-gray-800 p-2 rounded overflow-auto">
                {JSON.stringify(sessionData, null, 2)}
              </pre>
            </div>

            <div>
              <h3 className="text-white font-semibold mb-2">Email Verification Status:</h3>
              <div className="text-white text-sm space-y-1">
                <p>email_confirmed_at: {authData?.email_confirmed_at || 'null'}</p>
                <p>confirmed_at: {authData?.confirmed_at || 'null'}</p>
                <p>Email: {authData?.email || 'null'}</p>
                <p className="font-semibold" style={{ color: authData?.email_confirmed_at ? 'var(--shot-teal)' : 'var(--shot-red)' }}>
                  Status: {authData?.email_confirmed_at ? 'VERIFIED' : 'NOT VERIFIED'}
                </p>
              </div>
            </div>

            <div className="flex gap-2">
              <IonButton 
                onClick={refreshAuth}
                className="shot-button-secondary"
                style={{ 
                  '--background': 'var(--shot-purple)', 
                  '--color': 'var(--shot-white)',
                  '--border-radius': 'var(--shot-button-radius)'
                }}
              >
                Refresh
              </IonButton>
              <IonButton 
                onClick={onClose}
                className="shot-button-outline"
                style={{ 
                  '--border-color': 'var(--shot-teal)',
                  '--color': 'var(--shot-teal)',
                  '--border-radius': 'var(--shot-button-radius)'
                }}
              >
                Close
              </IonButton>
            </div>
          </div>
        </IonCardContent>
      </IonCard>
    </div>
  );
};

export default AuthDebug;
