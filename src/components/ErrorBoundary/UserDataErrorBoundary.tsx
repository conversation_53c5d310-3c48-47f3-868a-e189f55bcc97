import React, { Component, ReactNode } from 'react';
import { IonContent, IonHeader, IonPage, IonTitle, IonToolbar, IonButton, IonIcon } from '@ionic/react';
import { refreshOutline, homeOutline } from 'ionicons/icons';

type Props = {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
};

type State = {
  hasError: boolean;
  error: Error | null;
};

/**
 * Error boundary specifically for user data operations
 * Provides graceful fallback UI when user-related operations fail
 */
export class UserDataErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('UserDataErrorBoundary caught an error:', error, errorInfo);
    
    // Report to error tracking service if available
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
    
    // Report to external error tracking (Sentry, etc.)
    if (typeof window !== 'undefined' && (window as any).Sentry) {
      (window as any).Sentry.captureException(error, {
        contexts: {
          react: {
            componentStack: errorInfo.componentStack,
          },
        },
        tags: {
          errorBoundary: 'UserDataErrorBoundary',
        },
      });
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null });
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default fallback UI
      return (
        <IonPage>
          <IonHeader>
            <IonToolbar>
              <IonTitle>Something went wrong</IonTitle>
            </IonToolbar>
          </IonHeader>
          <IonContent className="ion-padding">
            <div className="flex flex-col items-center justify-center min-h-full text-center space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl font-bold text-gray-900">
                  Oops! Something went wrong
                </h2>
                <p className="text-gray-600 max-w-md">
                  We encountered an error while loading your user data. This is usually temporary.
                </p>
                {import.meta.env.DEV && this.state.error && (
                  <details className="text-left bg-gray-100 p-4 rounded-lg">
                    <summary className="cursor-pointer font-medium">
                      Error Details (Development)
                    </summary>
                    <pre className="mt-2 text-sm text-red-600 whitespace-pre-wrap">
                      {this.state.error.message}
                      {'\n\n'}
                      {this.state.error.stack}
                    </pre>
                  </details>
                )}
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <IonButton 
                  onClick={this.handleRetry}
                  color="primary"
                  className="min-w-32"
                >
                  <IonIcon icon={refreshOutline} slot="start" />
                  Try Again
                </IonButton>
                
                <IonButton 
                  onClick={this.handleGoHome}
                  fill="outline"
                  color="primary"
                  className="min-w-32"
                >
                  <IonIcon icon={homeOutline} slot="start" />
                  Go Home
                </IonButton>
              </div>
            </div>
          </IonContent>
        </IonPage>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook-based error boundary for functional components
 */
export const UserDataErrorFallback: React.FC<{
  error?: Error;
  resetError?: () => void;
}> = ({ error, resetError }) => {
  const handleGoHome = () => {
    window.location.href = '/';
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-6 text-center space-y-6">
      <div className="space-y-4">
        <h2 className="text-2xl font-bold text-gray-900">
          Unable to load user data
        </h2>
        <p className="text-gray-600 max-w-md">
          We're having trouble loading your profile information. Please try again.
        </p>
        {import.meta.env.DEV && error && (
          <details className="text-left bg-gray-100 p-4 rounded-lg">
            <summary className="cursor-pointer font-medium">
              Error Details (Development)
            </summary>
            <pre className="mt-2 text-sm text-red-600 whitespace-pre-wrap">
              {error.message}
              {'\n\n'}
              {error.stack}
            </pre>
          </details>
        )}
      </div>
      
      <div className="flex flex-col sm:flex-row gap-4">
        {resetError && (
          <IonButton 
            onClick={resetError}
            color="primary"
            className="min-w-32"
          >
            <IonIcon icon={refreshOutline} slot="start" />
            Try Again
          </IonButton>
        )}
        
        <IonButton 
          onClick={handleGoHome}
          fill="outline"
          color="primary"
          className="min-w-32"
        >
          <IonIcon icon={homeOutline} slot="start" />
          Go Home
        </IonButton>
      </div>
    </div>
  );
};