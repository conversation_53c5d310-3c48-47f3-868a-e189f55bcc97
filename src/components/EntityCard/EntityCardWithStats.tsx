import React, { useEffect, useState } from 'react';
import {
  IonIcon,
  IonButton,
  IonAvatar,
  IonSpinner,
} from '@ionic/react';
import {
  people as peopleIcon,
  personAdd as addPlayerIcon,
  create as editIcon,
  chevronForward as forwardIcon,
  calendarOutline as calendarIcon,
  businessOutline as clubIcon,
  locationOutline as locationIcon,
  mailOutline as mailIcon,
  callOutline as phoneIcon,
  shieldOutline as adminIcon,
  clipboardOutline as evaluationIcon,
  peopleCircle,
  calendar,
  checkmarkCircle,
  albums
} from 'ionicons/icons';
import { supabase } from '@/lib/supabase';
import './EntityCard.css';
import './EntityCardWithStats.css';
import { getSportIconPath } from './utils';

// Import types
import type { TeamEntity } from './types';

interface TeamStatisticsData {
  upcoming_events: number;
  events_today: number;
  total_evaluations: number;
  outstanding_evaluations: number;
  total_events: number;
  total_players: number;
  active_players: number;
  future_events: number;
}

interface EntityCardWithStatsProps {
  entity: TeamEntity;
  onView?: (entityId: string) => void;
  onAddPerson?: (entityId: string) => void;
  onEdit?: (entityId: string) => void;
  onManageAdmins?: (entityId: string) => void;
  onManageCoaches?: (entityId: string) => void;
  showActions?: boolean;
  teamId: string;
  onStatClick?: (statType: 'players' | 'events' | 'evaluations' | 'future') => void;
}

/**
 * EntityCardWithStats - Enhanced EntityCard with integrated team statistics
 */
const EntityCardWithStats: React.FC<EntityCardWithStatsProps> = ({
  entity,
  onView,
  onAddPerson,
  onEdit,
  onManageAdmins,
  onManageCoaches,
  showActions = true,
  teamId,
  onStatClick,
}) => {
  const [statistics, setStatistics] = useState<TeamStatisticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadStatistics = async () => {
      try {
        setLoading(true);
        setError(null);

        // Call the get_team_statistics function
        const { data, error: rpcError } = await supabase
          .rpc('get_team_statistics', { p_team_id: teamId });

        if (rpcError) {
          console.error('Error calling get_team_statistics:', rpcError);
          setError('Failed to load team statistics');
          return;
        }

        if (data && data.length > 0) {
          setStatistics(data[0]);
        } else {
          setError('No statistics data returned');
        }
      } catch (err) {
        console.error('Error loading team statistics:', err);
        setError('Failed to load team statistics');
      } finally {
        setLoading(false);
      }
    };

    if (teamId) {
      loadStatistics();
    }
  }, [teamId]);

  // Function to render entity logo or placeholder
  const renderLogo = () => {
    if (entity.logo_url) {
      return (
        <div className="entity-logo-container">
          <img src={entity.logo_url} alt={entity.name} className="entity-logo" />
        </div>
      );
    }
    return null;
  };

  // Get coach display info
  const getPersonDisplayInfo = () => {
    // Ensure coaches is defined and is an array
    const entityCoaches = entity.coaches || [];
    const primaryCoach = entityCoaches.find(coach => coach.is_primary);
    
    if (entityCoaches.length === 0) {
      return {
        name: 'No coaches assigned',
        count: 0,
        primary: null
      };
    }
    
    return {
      name: primaryCoach ? `${primaryCoach.name} (Primary)` : entityCoaches[0].name,
      count: entityCoaches.length,
      primary: primaryCoach || entityCoaches[0]
    };
  };

  // Handle entity click
  const handleClick = () => {
    if (onView) {
      onView(entity.id);
    }
  };

  const personInfo = getPersonDisplayInfo();

  return (
    <div className="entity-card entity-card-with-stats" data-entity-type={entity.entityType} onClick={handleClick}>
      <div className="entity-lozenge">{entity.entityType.toUpperCase()}</div>
      
      <div className="entity-card-header">
        <div className="entity-header-content">
          <div className="entity-icon-container">
            <img 
              src={getSportIconPath(entity.sport_type)} 
              alt={entity.sport_type || 'Sport'} 
              className="entity-icon"
            />

            <div className="entity-title-section">
              <h3 className="entity-name">{entity.name}</h3>
              {entity.ageGroup && (
                <div className="entity-subtitle">{entity.ageGroup}</div>
              )}
            </div>
            
            {renderLogo()}
          </div>
        </div>
      </div>
      
      <div className="entity-card-body">
        {/* Integrated Team Statistics */}
        {loading ? (
          <div className="stats-loading">
            <IonSpinner name="dots" />
          </div>
        ) : error || !statistics ? (
          <div className="stats-error">
            <p>{error || 'Failed to load statistics'}</p>
          </div>
        ) : (
          <div className="team-stats-grid">
            {/* Active Players */}
            <div 
              className="stat-item players"
              onClick={(e) => {
                e.stopPropagation();
                if (onStatClick) onStatClick('players');
              }}
            >
              <div className="stat-icon-container">
                <IonIcon icon={peopleCircle} className="stat-icon players" />
              </div>
              <div className="stat-content">
                <div className="stat-value">{statistics.active_players || 0}</div>
                <div className="stat-label">ACTIVE PLAYERS</div>
                <div className="stat-sublabel">({statistics.total_players || 0} total)</div>
              </div>
            </div>

            {/* Upcoming Events */}
            <div 
              className="stat-item events"
              onClick={(e) => {
                e.stopPropagation();
                if (onStatClick) onStatClick('events');
              }}
            >
              <div className="stat-icon-container">
                <IonIcon icon={calendar} className="stat-icon events" />
              </div>
              <div className="stat-content">
                <div className="stat-value">{statistics.upcoming_events || 0}</div>
                <div className="stat-label">UPCOMING</div>
                <div className="stat-sublabel">{statistics.events_today || 0} today</div>
              </div>
            </div>

            {/* Outstanding Evaluations */}
            <div 
              className="stat-item evaluations"
              onClick={(e) => {
                e.stopPropagation();
                if (onStatClick) onStatClick('evaluations');
              }}
            >
              <div className="stat-icon-container">
                <IonIcon icon={evaluationIcon} className="stat-icon evaluations" />
              </div>
              <div className="stat-content">
                <div className="stat-value">{statistics.outstanding_evaluations || 0}</div>
                <div className="stat-label">OUTSTANDING</div>
                <div className="stat-sublabel">({statistics.total_evaluations || 0} total)</div>
              </div>
            </div>

            {/* Future Events */}
            <div 
              className="stat-item total"
              onClick={(e) => {
                e.stopPropagation();
                if (onStatClick) onStatClick('future');
              }}
            >
              <div className="stat-icon-container">
                <IonIcon icon={albums} className="stat-icon total" />
              </div>
              <div className="stat-content">
                <div className="stat-value">{statistics.future_events || 0}</div>
                <div className="stat-label">FUTURE</div>
                <div className="stat-sublabel">({statistics.total_events || 0} total)</div>
              </div>
            </div>
          </div>
        )}
        
        {/* Coach section */}
        <div className="entity-coaches compact">
          <div className="section-title">Coach</div>
          <div className="coach-display">
            {entity.coaches && entity.coaches.length > 0 ? (
              <div className="coaches-list">
                {entity.coaches.map((coach, index) => (
                  <div key={coach.id} className="coach-item">
                    <IonAvatar className="coach-avatar small">
                      {coach.avatarUrl ? (
                        <img 
                          src={coach.avatarUrl} 
                          alt={coach.name} 
                        />
                      ) : (
                        <div className="default-avatar">
                          {(coach.name || '?').charAt(0)}
                        </div>
                      )}
                    </IonAvatar>
                    <div className="coach-info">
                      <div className="coach-name">
                        {coach.name}
                        {coach.is_primary && <span className="coach-badge"> (Primary)</span>}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-coach">No coaches assigned</div>
            )}
          </div>
        </div>
        
        {/* Action buttons */}
        {showActions && (
          <div className="entity-actions">
            {onAddPerson && (
              <IonButton 
                fill="outline" 
                size="small"
                className="action-button"
                onClick={(e) => {
                  e.stopPropagation();
                  onAddPerson(entity.id);
                }}
              >
                <IonIcon slot="start" icon={addPlayerIcon} />
                Add Player
              </IonButton>
            )}
            
            {onEdit && (
              <IonButton 
                fill="outline" 
                size="small"
                className="action-button"
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(entity.id);
                }}
              >
                <IonIcon slot="start" icon={editIcon} />
                Edit
              </IonButton>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default EntityCardWithStats;
