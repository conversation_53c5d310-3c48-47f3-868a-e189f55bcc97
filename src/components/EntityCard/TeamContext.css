/* EntityCard Team Context Styles */

/* Minimal styling for positioning and hover behavior */
.team-header-banner .entity-card {
  margin-bottom: 0;
  border: 2px solid var(--shot-white);
  box-shadow: none;
}

.team-header-banner .entity-card:hover {
  transform: none; /* Disable hover transform for the header card */
  border-color: var(--shot-teal);
}

.team-header-banner .entity-lozenge {
  font-size: 0.9rem;
  padding: 4px 12px;
}

/* Match styling with team card design */
.team-header-banner .entity-name {
  font-family: var(--shot-font-heading) !important;
  font-weight: 700 !important;
  font-size: 1.4rem !important;
  color: var(--shot-white) !important;
  margin: 0 0 6px 0 !important;
  text-transform: uppercase !important;
}

/* Ensure coach section styling */
.team-header-banner .section-title {
  text-transform: uppercase;
  font-weight: 600;
  margin-bottom: 12px;
}

/* Ensure stats section styling */
.team-header-banner .entity-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
  background-color: rgba(255, 255, 255, 0.05);
  padding: 16px;
  border-radius: var(--shot-button-radius);
}

.team-header-banner .entity-stat {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  color: var(--shot-teal);
}