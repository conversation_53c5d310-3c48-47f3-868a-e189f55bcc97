// Utility functions for entity card component

/**
 * Converts a team object from API format to the EntityCard team format
 */
export const getTeamEntityProps = (team?: any) => {
  if (!team) {
    return {
      entityType: 'team',
      id: '',
      name: 'Loading...',
      ageGroup: '',
      playerCount: 0,
      coaches: [],
      sport_type: 'football',
      upcoming_events: 0
    };
  }
  
  return {
    entityType: 'team',
    id: team.team_id || team.id,
    name: team.team_name || team.name,
    ageGroup: team.age_group || team.ageGroup,
    playerCount: team.player_count || team.playerCount || 0,
    coaches: team.coaches || [],
    sport_type: team.sport_type,
    logo_url: team.logo_url,
    club_id: team.club_id,
    upcoming_events: team.upcoming_events || 0,
    outstanding_evaluations: team.outstanding_evaluations || 0
  };
};

/**
 * Converts a club object from API format to the EntityCard club format
 * Enhanced to ensure proper handling of admins and coaches arrays
 */
export const getClubEntityProps = (club?: any) => {
  if (!club) {
    return {
      entityType: 'club',
      id: '',
      name: 'Loading...',
      adminCount: 0,
      teamCount: 0,
      sport_type: 'football',
      admins: [],
      coaches: []
    };
  }
  
  // Ensure admins array is valid
  let admins = [];
  if (club.admins && Array.isArray(club.admins)) {
    admins = club.admins.map(admin => ({
      id: admin.id || admin.user_id || '',
      name: admin.user_name || admin.name || 'Unknown',
      role: admin.role || 'Club Administrator',
      avatarUrl: admin.user_avatar || admin.avatarUrl,
      is_primary: admin.is_primary || false
    }));
  }
  
  // Ensure coaches array is valid
  let coaches = [];
  if (club.coaches && Array.isArray(club.coaches)) {
    coaches = club.coaches.map(coach => ({
      id: coach.id || coach.user_id || '',
      name: coach.user_name || coach.name || 'Unknown',
      role: coach.role || 'Coach',
      avatarUrl: coach.user_avatar || coach.avatarUrl,
      is_primary: coach.is_primary || false
    }));
  }
  
  return {
    entityType: 'club',
    id: club.club_id || club.id,
    name: club.club_name || club.name,
    adminCount: club.admin_count || club.adminCount || 0,
    teamCount: club.team_count || club.teamCount || 0,
    sport_type: club.sport_type,
    logo_url: club.logo_url,
    address: club.address,
    contact_email: club.contact_email,
    contact_phone: club.contact_phone,
    verification_status: club.verification_status,
    admins: admins,
    coaches: coaches
  };
};

/**
 * Gets the appropriate sport icon path based on sport type
 */
export const getSportIconPath = (sportType?: string) => {
  if (!sportType) return '/assets/sports/football.png'; // Default icon
  
  const sport = sportType.toLowerCase();
  
  // Match sport types to available icons
  if (sport.includes('soccer') || sport.includes('football')) return '/assets/sports/football.png';
  if (sport.includes('basketball')) return '/assets/sports/basketball.png';
  if (sport.includes('boxing')) return '/assets/sports/boxing.png';
  
  // Default to football if no match
  return '/assets/sports/football.png';
};