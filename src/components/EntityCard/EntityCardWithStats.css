/* EntityCardWithStats - Integrated statistics styling */

.entity-card-with-stats {
  position: relative;
}

/* Statistics loading state */
.stats-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: rgba(255, 255, 255, 0.6);
}

.stats-error {
  text-align: center;
  padding: 20px;
  color: #ff6b6b;
  font-size: 13px;
}

/* Team statistics grid - Tabletted Design */
.team-stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 16px;
  background-color: transparent;
  margin-bottom: 16px;
}

/* Individual stat item - Tablet Style */
.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  min-height: 80px;
}

/* Left accent bar for stat items */
.stat-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
}

.stat-item.players::before {
  background: var(--shot-green, #00BB00);
}

.stat-item.events::before {
  background: var(--shot-electric-blue, #296DFF);
}

.stat-item.evaluations::before {
  background: #ff6b6b;
}

.stat-item.total::before {
  background: var(--shot-purple, #6B00DB);
}

/* Hover and active states */
.stat-item:hover {
  transform: translateY(-2px);
  background-color: rgba(0, 0, 0, 0.85);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.stat-item:active {
  transform: translateY(0);
}

/* Stat icon with color coding - Larger for tablet design */
.stat-icon {
  font-size: 28px;
  opacity: 0.9;
  flex-shrink: 0;
}

.stat-icon.players {
  color: var(--shot-green, #00BB00);
}

.stat-icon.events {
  color: var(--shot-electric-blue, #296DFF);
}

.stat-icon.evaluations {
  color: #ff6b6b;
}

.stat-icon.total {
  color: var(--shot-purple, #6B00DB);
}

/* Icon container for better alignment */
.stat-icon-container {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

/* Stat content - Horizontal layout */
.stat-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1;
}

.stat-value {
  font-family: var(--shot-font-heading, 'Poppins', sans-serif);
  font-size: 32px;
  font-weight: 700;
  color: #ffffff;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-family: var(--shot-font-body, 'Montserrat', sans-serif);
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
  line-height: 1;
}

.stat-sublabel {
  font-family: var(--shot-font-body, 'Montserrat', sans-serif);
  font-size: 11px;
  font-weight: 500;
  line-height: 1;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 2px;
}

/* Compact coach section */
.entity-coaches.compact {
  padding: 12px 16px;
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  margin-bottom: 12px;
}

.entity-coaches.compact .section-title {
  font-size: 11px;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.5);
  letter-spacing: 0.5px;
}

/* Coaches list styling */
.coaches-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 150px; /* Limit height for many coaches */
  overflow-y: auto; /* Add scroll if needed */
}

/* Custom scrollbar for coaches list */
.coaches-list::-webkit-scrollbar {
  width: 4px;
}

.coaches-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.coaches-list::-webkit-scrollbar-thumb {
  background: var(--shot-teal, #1ABC9C);
  border-radius: 2px;
}

/* Individual coach item */
.coach-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 4px 0;
}

.entity-coaches.compact .coach-avatar.small {
  width: 32px;
  height: 32px;
  min-width: 32px;
}

.entity-coaches.compact .coach-name {
  font-size: 13px;
}

.entity-coaches.compact .coach-count {
  font-size: 11px;
}

/* Primary coach badge */
.coach-badge {
  font-size: 11px;
  color: var(--shot-teal, #1ABC9C);
  font-weight: 500;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .team-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    padding: 12px;
  }
  
  .stat-item {
    padding: 14px 16px;
    min-height: 70px;
  }
  
  .stat-value {
    font-size: 28px;
  }
  
  .stat-label {
    font-size: 10px;
  }
  
  .stat-sublabel {
    font-size: 10px;
  }
  
  .stat-icon {
    font-size: 24px;
  }
  
  .stat-icon-container {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .team-stats-grid {
    grid-template-columns: 1fr;
    gap: 8px;
    padding: 10px;
  }
  
  .stat-item {
    padding: 12px 14px;
    min-height: 64px;
  }
  
  .stat-value {
    font-size: 24px;
  }
  
  .stat-label {
    font-size: 9px;
    letter-spacing: 0.3px;
  }
  
  .stat-sublabel {
    font-size: 9px;
  }
  
  .stat-icon {
    font-size: 22px;
  }
  
  .stat-icon-container {
    width: 32px;
    height: 32px;
  }
}
