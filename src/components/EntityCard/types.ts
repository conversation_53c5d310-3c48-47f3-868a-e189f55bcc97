// This file re-exports all types directly to simplify imports
export interface Person {
  id: string;
  name: string;
  role?: string;
  avatarUrl?: string;
  is_primary?: boolean;
}

export interface CommonEntityProps {
  id: string;
  name: string;
  sport_type?: string;
  logo_url?: string;
}

export interface TeamEntity extends CommonEntityProps {
  entityType: 'team';
  ageGroup?: string;
  playerCount: number;
  coaches: Person[];
  club_id?: string;
  upcoming_events?: number;
  outstanding_evaluations?: number;
}

export interface ClubEntity extends CommonEntityProps {
  entityType: 'club';
  adminCount: number;
  teamCount: number;
  address?: string;
  contact_email?: string;
  contact_phone?: string;
  verification_status?: string;
  admins?: Person[];
  coaches?: Person[]; 
}

export type Entity = TeamEntity | ClubEntity;

export interface EntityCardProps {
  entity: Entity;
  onView?: (entityId: string) => void;
  onAddPerson?: (entityId: string) => void;
  onEdit?: (entityId: string) => void;
  onManageAdmins?: (entityId: string) => void;
  onManageCoaches?: (entityId: string) => void; 
  showActions?: boolean;
}