/* EntityCard.css - Core styles */

/* Role and name styles - updated */
.entity-card .coach-item .coach-name {
  font-weight: 500;
  font-size: 1.1rem;
  color: var(--shot-white);
  margin-bottom: 2px;
}

.entity-card .coach-item .coach-role {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
  margin-top: 2px;
}

.entity-card .coach-item .coach-primary {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--shot-teal);
  margin-top: 2px;
}

/* Club section title styles */
.entity-card .club-admins .section-title,
.entity-card .club-coaches .section-title {
  font-size: 0.85rem;
  font-weight: 700;
  letter-spacing: 0.05em;
  margin-bottom: 14px;
  padding-left: 0;
}

.entity-card .club-admins .section-title {
  color: var(--shot-purple);
}

.entity-card .club-coaches .section-title {
  color: var(--shot-teal);
}

/* Empty placeholder to maintain the 2x2 grid */
.entity-card .empty-button-placeholder {
  height: 44px;
  width: 100%;
}

.entity-lozenge {
  display: inline-block;
  position: absolute;
  top: 12px;
  right: 12px;
  background-color: var(--shot-purple);
  color: white;
  font-family: var(--shot-font-heading);
  font-weight: 600;
  font-size: 0.9rem;
  padding: 4px 12px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  z-index: 10;
}

.entity-card {
  background-color: rgba(26, 26, 26, 0.7);
  border-radius: var(--shot-button-radius);
  overflow: hidden;
  margin-bottom: 5px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  width: 100%;
}

/* Team-specific styling */
.entity-card[data-entity-type="team"] {
  border: 2px solid var(--shot-white);
  box-shadow: none;
  background-color: rgba(26, 26, 26, 0.7);
}

/* Club-specific styling */
.entity-card[data-entity-type="club"] {
  border: 2px solid var(--shot-purple);
  box-shadow: none;
  background-color: rgba(0, 0, 0, 0.95);
}

.entity-card:hover {
  transform: translateY(-3px) scale(1.01);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  border-color: var(--shot-teal);
}

/* ====== Card Layout ====== */
.entity-card .entity-card-header {
  padding: 20px;
  padding-right: 60px; /* Make room for the entity lozenge */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: transparent;
  position: relative;
}

.entity-card .entity-header-content {
  display: flex;
  width: 100%;
}

.entity-card .entity-icon-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.entity-card .entity-icon {
  width: 48px;
  height: 48px;
  object-fit: contain;
  margin-right: 16px;
  flex-shrink: 0;
  color: var(--shot-white); /* For IonIcon */
  font-size: 32px; /* For IonIcon */
}

.entity-card .entity-title-section {
  flex: 1;
  padding-right: 16px;
}

.entity-card .entity-name {
  font-family: var(--shot-font-heading) !important;
  font-weight: 700 !important;
  font-size: 1.4rem !important;
  color: var(--shot-white) !important;
  margin: 0 0 6px 0 !important;
  text-transform: uppercase !important;
  display: flex !important;
  align-items: center !important;
}

.entity-card .entity-subtitle {
  font-family: var(--shot-font-body);
  font-size: 0.9rem;
  color: var(--shot-gold);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: inline-block;
  padding: 3px 12px;
  background-color: rgba(247, 182, 19, 0.2);
  border-radius: 14px;
}

.entity-card .entity-logo-container {
  width: 44px;
  height: 44px;
  border-radius: 8px;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(26, 188, 156, 0.4);
}

.entity-card .entity-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.entity-card .entity-card-body {
  padding: 20px;
}

/* ====== Entity Stats ====== */
.entity-card .entity-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
  background-color: rgba(255, 255, 255, 0.05);
  padding: 16px;
  border-radius: var(--shot-button-radius);
}

.entity-card .entity-stat {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.entity-card .entity-stat ion-icon {
  color: var(--shot-teal);
  font-size: 20px;
}

.entity-card .entity-stat-warning {
  background-color: rgba(230, 57, 70, 0.1);
  border: 1px solid rgba(230, 57, 70, 0.3);
  border-radius: 8px;
  padding: 12px 16px;
  margin: 8px 0;
}

.entity-card .entity-stat-warning ion-icon {
  color: #e6394b;
}

.entity-card .entity-stat-warning span {
  color: #e6394b;
  font-weight: 600;
}

/* ====== Club Contact Info ====== */
.entity-card .entity-contact-info {
  margin-bottom: 20px;
}

.entity-card .entity-contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: var(--shot-white);
  margin-bottom: 8px;
}

.entity-card .entity-contact-item ion-icon {
  color: var(--shot-teal);
  font-size: 18px;
}

/* ====== Team Coaches ====== */
.entity-card .entity-coaches {
  margin-bottom: 20px;
  padding: 0 4px;
}

.entity-card .section-title {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--shot-purple);
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.entity-card .coach-display {
  display: flex;
  align-items: center;
}

.entity-card .primary-coach {
  display: flex;
  align-items: center;
  gap: 12px;
}

.entity-card .coach-avatar {
  width: 44px;
  height: 44px;
  border: 2px solid var(--shot-purple);
  box-shadow: 0 0 10px rgba(107, 0, 219, 0.3);
}

.entity-card .default-avatar {
  width: 100%;
  height: 100%;
  background-color: var(--shot-purple);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  border-radius: 50%;
  font-size: 20px;
}

.entity-card .coach-info {
  display: flex;
  flex-direction: column;
}

.entity-card .coach-name {
  font-weight: 600;
  font-size: 1rem;
  color: var(--shot-white);
  margin-bottom: 2px;
}

.entity-card .coach-count {
  font-size: 0.85rem;
  color: var(--shot-purple);
  font-weight: 500;
  padding: 8px 0;
}

.entity-card .no-coach,
.entity-card .no-admins,
.entity-card .no-coaches {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
  padding: 5px 0 5px 0px;
  text-align: left;
  margin-left: 0;
}

/* ====== Club Administrators ====== */
.entity-card .club-admins {
  margin-bottom: 20px;
  padding: 16px;
  background-color: rgba(0, 0, 0, 0.95);
  border-radius: 8px;
  border: 1px solid rgba(150, 150, 150, 0.3);
  margin-top: 20px;
}

/* Default avatar color for administrators */
.entity-card .club-admins .default-avatar {
  background-color: var(--shot-purple);
}

/* ====== Club Coaches ====== */
.entity-card .club-coaches {
  margin-bottom: 20px;
  padding: 16px;
  background-color: rgba(0, 0, 0, 0.95);
  border-radius: 8px;
  border: 1px solid rgba(150, 150, 150, 0.3);
  margin-top: 20px;
}

/* Coach item avatar styling - consistent across admins and coaches */
.entity-card .coach-item .coach-avatar {
  width: 45px;
  height: 45px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  border: 2px solid var(--shot-purple);
  background: #0f0f0f;
}

.entity-card .club-coaches .coach-item .coach-avatar {
  border-color: var(--shot-teal);
}

/* Make both admin and coach avatars consistent */
.entity-card .club-admins .coach-avatar,
.entity-card .club-coaches .coach-avatar {
  width: 45px !important;
  height: 45px !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

.entity-card .club-admins .coach-item .coach-avatar {
  border: 2px solid var(--shot-purple);
}

.entity-card .club-coaches .coach-item .coach-avatar {
  border: 2px solid var(--shot-purple);
}

/* Default avatar for coaches and admins */
.entity-card .coach-item .default-avatar,
.entity-card .club-admins .default-avatar,
.entity-card .club-coaches .default-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 24px;
  color: white;
  background-color: var(--shot-purple);
  border-radius: 0;
}

/* Coach item styling for consistency */
.entity-card .coach-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  margin-bottom: 5px;
  width: 100%;
  background-color: transparent;
}

.entity-card .club-coaches .coach-item,
.entity-card .club-admins .coach-item {
  background-color: transparent;
}

.entity-card .coaches-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 10px;
  width: 100%;
}

/* Specific to club coach items - OVERRIDDEN */
.entity-card .club-coaches .coach-avatar {
  width: 45px !important;
  height: 45px !important;
  border-radius: 8px !important;
  border-color: var(--shot-purple) !important;
}

/* Club coach count style */
.entity-card .club-coaches .coach-count {
  font-size: 0.85rem;
  color: var(--shot-purple);
  font-weight: 500;
  padding: 8px 0;
}

/* Primary badge styling */
.entity-card .coach-primary {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--shot-purple);
  margin-top: 2px;
}

/* ====== Verification Status ====== */
.entity-card .verification-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 20px;
  padding: 8px 12px;
  border-radius: 8px;
}

.entity-card .verification-verified {
  background-color: rgba(0, 187, 0, 0.1) !important;
  color: var(--shot-green) !important;
  border: 1px solid rgba(0, 187, 0, 0.3) !important;
}

.entity-card .verification-pending {
  background-color: rgba(247, 182, 19, 0.1);
  color: var(--shot-gold);
}

.entity-card .verification-rejected {
  background-color: rgba(230, 57, 70, 0.1);
  color: var(--shot-red);
}

.entity-card .verification-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.entity-card .verification-verified .verification-dot:before {
  content: '\2713' !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.6rem;
  color: white;
}

.entity-card .verification-verified .verification-dot {
  background-color: var(--shot-green) !important;
  box-shadow: 0 0 4px rgba(0, 187, 0, 0.3) !important;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.entity-card .verification-pending .verification-dot {
  background-color: var(--shot-gold);
  box-shadow: 0 0 6px rgba(247, 182, 19, 0.5);
}

.entity-card .verification-rejected .verification-dot {
  background-color: var(--shot-red);
  box-shadow: 0 0 6px rgba(230, 57, 70, 0.5);
}

/* ====== Actions ====== */
/* Action Buttons - Stacked layout for club cards */
.entity-card .entity-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
}

/* Default button style - team cards have buttons side-by-side */
.entity-card[data-entity-type="team"] .entity-actions .action-button {
  width: calc(50% - 5px);
  flex: 0 0 auto;
}

/* Club cards have 2x2 grid layout - all buttons 50% width */
.entity-card[data-entity-type="club"] .entity-actions {
  display: grid;
  grid-template-columns: 1fr 1fr; /* Two columns */
  grid-template-rows: auto auto; /* Two rows */
  gap: 15px;
  padding: 25px 16px;
  background-color: rgba(0, 0, 0, 0.4);
  border-top: 1px solid rgba(107, 0, 219, 0.2);
  margin-top: 0;
}

.entity-card[data-entity-type="club"] .entity-actions .action-button {
  width: 100%;
  height: 44px;
  font-size: 0.9rem;
  --border-width: 2px;
  --border-color: var(--shot-teal);
  --background: transparent;
  --color: var(--shot-white);
}

.entity-card[data-entity-type="club"] .entity-actions .action-button:hover {
  --background: rgba(26, 188, 156, 0.15);
}

.entity-card .action-button {
  --color: var(--shot-teal);
  --border-color: var(--shot-teal);
  font-size: 0.8rem;
  height: 34px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  --padding-start: 12px;
  --padding-end: 12px;
}

.entity-card .action-button ion-icon {
  font-size: 16px;
  margin-right: 4px;
}

.entity-card .action-button:hover {
  --background: rgba(26, 188, 156, 0.15);
}

/* Verified lozenge */
.entity-card .verified-lozenge {
  display: inline-flex;
  align-items: center;
  background-color: rgba(0, 187, 0, 0.1);
  color: var(--shot-green);
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 2px 8px;
  margin-left: 8px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Responsive Adjustments */
@media (max-width: 767px) {
  .entity-card .entity-card-header {
    padding: 16px;
    padding-right: 60px;
  }
  
  .entity-card .entity-name {
    font-size: 1.2rem !important;
  }
  
  .entity-card .entity-card-body {
    padding: 16px;
  }
  
  .entity-card .entity-stat {
    font-size: 0.85rem;
  }
}

/* List Variations */
.entity-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 5px;
  width: 100%;
}

.entity-list.single-column {
  display: flex;
  flex-direction: column;
}