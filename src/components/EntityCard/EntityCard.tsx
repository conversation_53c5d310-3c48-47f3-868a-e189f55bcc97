import React from 'react';
import {
  IonIcon,
  IonButton,
  IonAvatar,
} from '@ionic/react';
import {
  people as peopleIcon,
  personAdd as addPlayerIcon,
  create as editIcon,
  chevronForward as forwardIcon,
  calendarOutline as calendarIcon,
  businessOutline as clubIcon,
  locationOutline as locationIcon,
  mailOutline as mailIcon,
  callOutline as phoneIcon,
  shieldOutline as adminIcon,
  clipboard as evaluationIcon
} from 'ionicons/icons';
import './EntityCard.css';
import { getSportIconPath } from './utils';

// Define coach/admin type
interface Person {
  id: string;
  name: string;
  role?: string;
  avatarUrl?: string;
  is_primary?: boolean;
}

// Shared properties between team and club
interface CommonEntityProps {
  id: string;
  name: string;
  sport_type?: string;
  logo_url?: string;
}

// Team-specific properties
interface TeamEntity extends CommonEntityProps {
  entityType: 'team';
  ageGroup?: string;
  playerCount: number;
  coaches: Person[];
  club_id?: string;
  upcoming_events?: number;
  outstanding_evaluations?: number;
}

// Club-specific properties
interface ClubEntity extends CommonEntityProps {
  entityType: 'club';
  adminCount: number;
  teamCount: number;
  address?: string;
  contact_email?: string;
  contact_phone?: string;
  verification_status?: string;
  admins?: Person[];
  coaches?: Person[]; // Added coaches property for clubs
}

// Combined type for the entity prop
type Entity = TeamEntity | ClubEntity;

interface EntityCardProps {
  entity: Entity;
  // Optional callback functions
  onView?: (entityId: string) => void;
  onAddPerson?: (entityId: string) => void;
  onEdit?: (entityId: string) => void;
  onManageAdmins?: (entityId: string) => void;
  onManageCoaches?: (entityId: string) => void; // Added new callback for coach management
  // Control which action buttons to show
  showActions?: boolean;
}

/**
 * EntityCard - A flexible component for displaying teams and clubs
 * 
 * This component can handle both team and club entities with a consistent visual style
 */
const EntityCard: React.FC<EntityCardProps> = ({
  entity,
  onView,
  onAddPerson,
  onEdit,
  onManageAdmins,
  onManageCoaches,
  showActions = true,
}) => {
  // Debug output to help diagnose issues
  if (entity.entityType === 'club') {
    console.log('EntityCard - Club data:', {
      name: entity.name,
      id: entity.id,
      admins: entity.admins,
      coaches: entity.coaches,
      adminCount: entity.adminCount,
      teamCount: entity.teamCount,
    });
  }

  // Function to render entity logo or placeholder
  const renderLogo = () => {
    if (entity.logo_url) {
      return (
        <div className="entity-logo-container">
          <img src={entity.logo_url} alt={entity.name} className="entity-logo" />
        </div>
      );
    }
    return null;
  };

  // Get admin/coach display for team entities
  const getPersonDisplayInfo = () => {
    if (entity.entityType === 'team') {
      const primaryCoach = entity.coaches.find(coach => coach.is_primary);
      
      if (entity.coaches.length === 0) {
        return {
          name: 'No coaches assigned',
          count: 0,
          primary: null
        };
      }
      
      return {
        name: primaryCoach ? `${primaryCoach.name} (Primary)` : entity.coaches[0].name,
        count: entity.coaches.length,
        primary: primaryCoach || entity.coaches[0]
      };
    }
    
    return null;
  };

  // Handle entity click
  const handleClick = () => {
    if (onView) {
      onView(entity.id);
    }
  };

  const personInfo = getPersonDisplayInfo();

  // Render based on entity type
  return (
    <div className="entity-card" data-entity-type={entity.entityType} onClick={handleClick}>
      <div className="entity-lozenge">{entity.entityType.toUpperCase()}</div>
      
      <div className="entity-card-header">
        <div className="entity-header-content">
          <div className="entity-icon-container">
            {/* For clubs and teams, show sport icon */}
            <img 
              src={getSportIconPath(entity.sport_type)} 
              alt={entity.sport_type || 'Sport'} 
              className="entity-icon"
            />

            {/* Entity name */}
            <div className="entity-title-section">
              <h3 className="entity-name">
                {entity.name}
                {entity.entityType === 'club' && entity.verification_status === 'verified' && (
                  <span className="verified-lozenge" data-status="verified">Verified</span>
                )}
              </h3>
              
              {/* Show age group for teams */}
              {entity.entityType === 'team' && entity.ageGroup && (
                <div className="entity-subtitle">{entity.ageGroup}</div>
              )}
            </div>
            
            {/* Logo container */}
            {renderLogo()}
          </div>
        </div>
      </div>
      
      <div className="entity-card-body">
        <div className="entity-stats">
          {/* Team-specific stats */}
          {entity.entityType === 'team' && (
            <>
              <div className="entity-stat">
                <IonIcon icon={peopleIcon} />
                <span>{entity.playerCount} Players</span>
              </div>
              
              <div className="entity-stat">
                <IonIcon icon={calendarIcon} />
                <span>{entity.upcoming_events || 0} Upcoming Events</span>
              </div>
              
              {entity.outstanding_evaluations !== undefined && entity.outstanding_evaluations > 0 && (
                <div className="entity-stat entity-stat-warning">
                  <IonIcon icon={evaluationIcon} />
                  <span>{entity.outstanding_evaluations} Outstanding Evaluation{entity.outstanding_evaluations !== 1 ? 's' : ''}</span>
                </div>
              )}
            </>
          )}
          
          {/* Club-specific stats */}
          {entity.entityType === 'club' && (
            <>
              <div className="entity-stat">
              <IonIcon icon={peopleIcon} />
              <span>{entity.teamCount} Teams</span>
              </div>
              
              <div className="entity-stat">
              <IonIcon icon={peopleIcon} />
              <span>{entity.coaches?.length || 0} Coaches</span>
              </div>
            </>
          )}
        </div>
        
        {/* Club-specific contact info */}
        {entity.entityType === 'club' && (entity.address || entity.contact_email || entity.contact_phone) && (
          <div className="entity-contact-info">
            {entity.address && (
              <div className="entity-contact-item">
                <IonIcon icon={locationIcon} />
                <span>{entity.address}</span>
              </div>
            )}
            
            {entity.contact_email && (
              <div className="entity-contact-item">
                <IonIcon icon={mailIcon} />
                <span>{entity.contact_email}</span>
              </div>
            )}
            
            {entity.contact_phone && (
              <div className="entity-contact-item">
                <IonIcon icon={phoneIcon} />
                <span>{entity.contact_phone}</span>
              </div>
            )}
          </div>
        )}
        
        {/* Team-specific coach section */}
        {entity.entityType === 'team' && (
          <div className="entity-coaches">
            <div className="section-title">Coach</div>
            <div className="coach-display">
              {entity.coaches.length > 0 ? (
                <div className="primary-coach">
                  <IonAvatar className="coach-avatar">
                    {personInfo?.primary?.avatarUrl ? (
                      <img 
                        src={personInfo.primary.avatarUrl} 
                        alt={personInfo.primary.name} 
                      />
                    ) : (
                      <div className="default-avatar">
                        {(personInfo?.primary?.name || '?').charAt(0)}
                      </div>
                    )}
                  </IonAvatar>
                  <div className="coach-info">
                    <div className="coach-name">{personInfo?.name}</div>
                    {entity.coaches.length > 1 && (
                      <div className="coach-count">+{entity.coaches.length - 1} more</div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="no-coach">No coaches assigned</div>
              )}
            </div>
          </div>
        )}
        
        {/* Club Administrators section */}
        {entity.entityType === 'club' && (
          <div className="entity-coaches club-admins">
            <div className="section-title">CLUB ADMINISTRATORS</div>
            {entity.admins && entity.admins.length > 0 ? (
              <div className="coaches-list">
                {entity.admins.map((admin, index) => (
                      <div key={admin.id || index} className="coach-item">
                        <div className="coach-avatar">
                          {admin.avatarUrl ? (
                            <img src={admin.avatarUrl} alt={admin.name} />
                          ) : (
                            <div className="default-avatar">
                              {admin.name.charAt(0).toUpperCase()}
                            </div>
                          )}
                        </div>
                        <div className="coach-info">
                          <div className="coach-name">{admin.name}</div>
                          {admin.role && (
                            <div className="coach-role">{admin.role.replace(/_/g, ' ')}</div>
                          )}
                          {admin.is_primary && (
                            <div className="coach-primary">Primary</div>
                          )}
                        </div>
                      </div>
                ))}
                {entity.adminCount > entity.admins.length && (
                  <div className="coach-count">+{entity.adminCount - entity.admins.length} more</div>
                )}
              </div>
            ) : (
              <div className="no-admins">No administrators assigned</div>
            )}
          </div>
        )}
        
        {/* Club Coaches section */}
        {entity.entityType === 'club' && (
          <div className="entity-coaches club-coaches">
            <div className="section-title">CLUB LEVEL COACHES</div>
            {entity.coaches && entity.coaches.length > 0 ? (
              <div className="coaches-list">
                {entity.coaches.map((coach, index) => (
                      <div key={coach.id || index} className="coach-item">
                        <div className="coach-avatar">
                          {coach.avatarUrl ? (
                            <img src={coach.avatarUrl} alt={coach.name} />
                          ) : (
                            <div className="default-avatar">
                              {coach.name.charAt(0).toUpperCase()}
                            </div>
                          )}
                        </div>
                        <div className="coach-info">
                          <div className="coach-name">{coach.name}</div>
                          {coach.role && (
                            <div className="coach-role">{coach.role.replace(/_/g, ' ')}</div>
                          )}
                          {coach.is_primary && (
                            <div className="coach-primary">Primary</div>
                          )}
                        </div>
                      </div>
                ))}
                {/* Show more coaches count if we have partial data */}
                {entity.coaches && 
                 entity.coaches.length > 0 && 
                 typeof entity.adminCount === 'number' && 
                 entity.coaches.length < entity.adminCount && (
                  <div className="coach-count">+{entity.adminCount - entity.coaches.length} more</div>
                )}
              </div>
            ) : (
              <div className="no-coaches">No coaches assigned</div>
            )}
          </div>
        )}
        
        {/* Club verification status - only show if not already displayed as a lozenge */}
        {entity.entityType === 'club' && entity.verification_status && entity.verification_status !== 'verified' && (
          <div className={`verification-status verification-${entity.verification_status}`}>
            <div className="verification-dot"></div>
            <span>
              {entity.verification_status.charAt(0).toUpperCase() + entity.verification_status.slice(1)}
            </span>
          </div>
        )}
        
        {/* Action buttons */}
        {showActions && (
          <div className="entity-actions">
            {/* Team-specific actions */}
            {entity.entityType === 'team' && (
              <>
                {onAddPerson && (
                  <IonButton 
                    fill="outline" 
                    size="small"
                    className="action-button"
                    onClick={(e) => {
                      e.stopPropagation();
                      onAddPerson(entity.id);
                    }}
                  >
                    <IonIcon slot="start" icon={addPlayerIcon} />
                    Add Player
                  </IonButton>
                )}
                
                {onEdit && (
                  <IonButton 
                    fill="outline" 
                    size="small"
                    className="action-button"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit(entity.id);
                    }}
                  >
                    <IonIcon slot="start" icon={editIcon} />
                    Edit
                  </IonButton>
                )}
              </>
            )}
            
            {/* Club-specific actions */}
            {entity.entityType === 'club' && (
              <>
                {onEdit && (
                  <IonButton 
                    fill="outline" 
                    size="small"
                    className="action-button"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit(entity.id);
                    }}
                  >
                    <IonIcon slot="start" icon={editIcon} />
                    EDIT CLUB
                  </IonButton>
                )}
                
                <IonButton 
                  fill="outline" 
                  size="small"
                  className="action-button"
                  onClick={(e) => {
                    e.stopPropagation();
                    // Use onManageCoaches if provided, otherwise fall back to onEdit
                    if (onManageCoaches) {
                      onManageCoaches(entity.id);
                    } else if (onEdit) {
                      onEdit(entity.id);
                    }
                  }}
                >
                  <IonIcon slot="start" icon={peopleIcon} />
                  EDIT COACHES
                  </IonButton>
                  
                  {onManageAdmins && (
                  <IonButton 
                  fill="outline" 
                  size="small"
                  className="action-button"
                  onClick={(e) => {
                  e.stopPropagation();
                  onManageAdmins(entity.id);
                  }}
                  >
                  <IonIcon slot="start" icon={adminIcon} />
                  EDIT ADMINS
                  </IonButton>
                  )}
                  
                  {/* Create Team button */}
                  <IonButton 
                fill="outline" 
                size="small"
                className="action-button"
                onClick={(e) => {
                  e.stopPropagation();
                  window.location.href = `/coach/club/${entity.id}/team/create`;
                }}
              >
                <IonIcon slot="start" icon={addPlayerIcon} />
                CREATE TEAM
              </IonButton>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default EntityCard;