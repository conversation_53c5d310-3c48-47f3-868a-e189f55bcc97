/* Team Statistics Component Styles */
.team-statistics-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 14px;
  padding: 20px;
  background-color: rgba(20, 20, 20, 0.6);
  border-radius: 16px;
  margin-bottom: 20px;
}

.team-statistics-container.loading {
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-text {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  margin-top: 10px;
}

.stat-card {
  background-color: #0f0f0f;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.03);
  min-height: 140px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Top colored border */
.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
}

.stat-card.players::before { background: #00ff88; }
.stat-card.events::before { background: #00a8ff; }
.stat-card.evaluations::before { background: #ff6b6b; }
.stat-card.total::before { background: #9b59b6; }

/* Icons */
.stat-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 12px;
  opacity: 0.8;
}

.stat-card.players .stat-icon { color: #00ff88; }
.stat-card.events .stat-icon { color: #00a8ff; }
.stat-card.evaluations .stat-icon { color: #ff6b6b; }
.stat-card.total .stat-icon { color: #9b59b6; }

/* Main value */
.stat-value {
  font-size: 48px;
  font-weight: 700;
  color: #ffffff;
  line-height: 1;
  margin: 0 0 8px 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Label */
.stat-label {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.35);
  text-transform: uppercase;
  letter-spacing: 1.5px;
  font-weight: 500;
  margin: 0 0 6px 0;
}

/* Sublabel */
.stat-sublabel {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
}

.stat-card.players .stat-sublabel { color: #00ff88; }
.stat-card.events .stat-sublabel { color: #00a8ff; }
.stat-card.evaluations .stat-sublabel { color: #ff6b6b; }
.stat-card.total .stat-sublabel { color: #9b59b6; }

/* Mobile Responsive */
@media (max-width: 768px) {
  .team-statistics-container {
    padding: 16px;
    gap: 12px;
  }
  
  .stat-card {
    padding: 18px 16px;
    min-height: 130px;
  }
  
  .stat-value {
    font-size: 40px;
  }
}

@media (max-width: 480px) {
  .team-statistics-container {
    padding: 14px;
    gap: 10px;
  }

  .stat-card {
    padding: 16px 14px;
    min-height: 120px;
  }

  .stat-value {
    font-size: 36px;
  }

  .stat-label {
    font-size: 10px;
    letter-spacing: 1.2px;
  }

  .stat-sublabel {
    font-size: 13px;
  }

  .stat-icon {
    width: 20px;
    height: 20px;
    margin-bottom: 10px;
  }
}

/* Extra small screens */
@media (max-width: 360px) {
  .stat-value {
    font-size: 32px;
  }
  
  .stat-card {
    padding: 14px 12px;
    min-height: 110px;
  }
}