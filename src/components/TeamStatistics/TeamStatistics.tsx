import React, { useEffect, useState } from 'react';
import { Ion<PERSON>pinner } from '@ionic/react';
import { supabase } from '@/lib/supabase';
import './TeamStatistics.css';

interface TeamStatisticsProps {
  teamId: string;
}

interface TeamStats {
  totalPlayers: number;
  activePlayers: number;
  upcomingEvents: number;
  eventsToday: number;
  totalEvaluations: number;
  outstandingEvaluations: number;
  totalEvents: number;
}

const TeamStatistics: React.FC<TeamStatisticsProps> = ({ teamId }) => {
  const [stats, setStats] = useState<TeamStats>({
    totalPlayers: 0,
    activePlayers: 0,
    upcomingEvents: 0,
    eventsToday: 0,
    totalEvaluations: 0,
    outstandingEvaluations: 0,
    totalEvents: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (teamId) {
      fetchTeamStatistics();
    }
  }, [teamId]);

  const fetchTeamStatistics = async () => {
    try {
      const { data, error } = await supabase
        .rpc('get_team_statistics', { p_team_id: teamId });

      if (error) throw error;

      if (data && data.length > 0) {
        const statsData = data[0];
        setStats({
          totalPlayers: statsData.total_players || 0,
          activePlayers: statsData.active_players || 0,
          upcomingEvents: statsData.upcoming_events || 0,
          eventsToday: statsData.events_today || 0,
          totalEvaluations: statsData.total_evaluations || 0,
          outstandingEvaluations: statsData.outstanding_evaluations || 0,
          totalEvents: statsData.total_events || 0
        });
      }
    } catch (error) {
      console.error('Error fetching team statistics:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="team-statistics-container loading">
        <IonSpinner name="dots" />
        <div className="loading-text">Loading statistics...</div>
      </div>
    );
  }

  return (
    <div className="team-statistics-container">
      {/* Players - Green */}
      <div className="stat-card players">
        <svg className="stat-icon" viewBox="0 0 24 24" fill="none">
          <circle cx="9" cy="7" r="4" stroke="currentColor" strokeWidth="2"/>
          <path d="M9 11c-2.48 0-4.5 2.02-4.5 4.5V17h9v-1.5c0-2.48-2.02-4.5-4.5-4.5z" stroke="currentColor" strokeWidth="2"/>
          <circle cx="17" cy="7" r="3" stroke="currentColor" strokeWidth="2" opacity="0.6"/>
          <path d="M17 11c2 0 3.5 1.5 3.5 3.5V16h-3" stroke="currentColor" strokeWidth="2" opacity="0.6"/>
        </svg>
        <div className="stat-value">{stats.totalPlayers}</div>
        <div className="stat-label">PLAYERS</div>
        <div className="stat-sublabel">{stats.activePlayers} active</div>
      </div>

      {/* Upcoming Events - Blue */}
      <div className="stat-card events">
        <svg className="stat-icon" viewBox="0 0 24 24" fill="none">
          <rect x="3" y="4" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2"/>
          <line x1="7" y1="2" x2="7" y2="6" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          <line x1="17" y1="2" x2="17" y2="6" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" strokeWidth="2"/>
        </svg>
        <div className="stat-value">{stats.upcomingEvents}</div>
        <div className="stat-label">UPCOMING EVENTS</div>
        <div className="stat-sublabel">
          {stats.eventsToday > 0 ? `${stats.eventsToday} today` : 'None today'}
        </div>
      </div>

      {/* Evaluations - Red */}
      <div className="stat-card evaluations">
        <svg className="stat-icon" viewBox="0 0 24 24" fill="none">
          <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" strokeWidth="2"/>
          <polyline points="9 11 12 14 22 4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
        <div className="stat-value">{stats.totalEvaluations}</div>
        <div className="stat-label">EVALUATIONS</div>
        <div className="stat-sublabel">
          {stats.outstandingEvaluations > 0 
            ? `${stats.outstandingEvaluations} outstanding` 
            : 'All complete'}
        </div>
      </div>

      {/* Total Events - Purple */}
      <div className="stat-card total">
        <svg className="stat-icon" viewBox="0 0 24 24" fill="none">
          <rect x="3" y="3" width="6" height="18" stroke="currentColor" strokeWidth="2"/>
          <rect x="9" y="3" width="6" height="18" stroke="currentColor" strokeWidth="2"/>
          <rect x="15" y="3" width="6" height="18" stroke="currentColor" strokeWidth="2"/>
        </svg>
        <div className="stat-value">{stats.totalEvents}</div>
        <div className="stat-label">TOTAL EVENTS</div>
        <div className="stat-sublabel">This season</div>
      </div>
    </div>
  );
};

export default TeamStatistics;