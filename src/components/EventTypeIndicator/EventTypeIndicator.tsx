import React from 'react';
import './EventTypeIndicator.css';

interface EventTypeIndicatorProps {
  type: 'training' | 'match' | 'assessment';
  size?: 'small' | 'medium' | 'large';
  showLabel?: boolean;
}

/**
 * EventTypeIndicator Component
 * 
 * A consistent visual indicator for event types (Training, Match, Assessment)
 * that can be used across the app for a consistent UX
 */
const EventTypeIndicator: React.FC<EventTypeIndicatorProps> = ({ 
  type, 
  size = 'medium',
  showLabel = true
}) => {
  // Define icon and color for each event type
  const getTypeIcon = (type: string): string => {
    switch (type) {
      case 'training': return '⚽';
      case 'match': return '🏆';
      case 'assessment': return '📋';
      default: return '📅';
    }
  };
  
  const getTypeLabel = (type: string): string => {
    switch (type) {
      case 'training': return 'Training';
      case 'match': return 'Match';
      case 'assessment': return 'Assessment';
      default: return 'Event';
    }
  };
  
  const getTypeColor = (type: string): string => {
    switch (type) {
      case 'training': return 'bg-blue-600';
      case 'match': return 'bg-green-600';
      case 'assessment': return 'bg-purple-600';
      default: return 'bg-gray-600';
    }
  };
  
  // Define size classes
  const getSizeClasses = (size: string): string => {
    switch (size) {
      case 'small': return 'text-xs py-1 px-2';
      case 'large': return 'text-base py-2 px-4';
      case 'medium':
      default: return 'text-sm py-1.5 px-3';
    }
  };
  
  const iconSizeClass = size === 'small' ? 'text-sm mr-1' : size === 'large' ? 'text-xl mr-2' : 'text-base mr-1.5';
  
  return (
    <div className={`event-type-indicator ${getTypeColor(type)} text-white rounded-lg inline-flex items-center ${getSizeClasses(size)}`}>
      <span className={iconSizeClass}>{getTypeIcon(type)}</span>
      {showLabel && (
        <span>{getTypeLabel(type)}</span>
      )}
    </div>
  );
};

export default EventTypeIndicator;