import React, { useState } from 'react';
import {
  IonAvatar,
  IonIcon,
  IonButton,
  IonBadge,
  IonChip,
  IonItem,
  IonSelect,
  IonSelectOption
} from '@ionic/react';
import { 
  person, 
  checkmarkCircle, 
  closeCircle,
  helpCircle,
  pencil,
  chevronForward,
  chevronDown,
  chevronUp,
  calendar
} from 'ionicons/icons';
import './PlayerCard.css';

/**
 * Player interface - can be expanded as needed
 */
export interface Player {
  id: string;
  name: string;
  avatarUrl?: string;
  initials?: string;
  position?: string;
  jerseyNumber?: string;
  status?: 'present' | 'absent' | 'pending' | 'available' | 'selected';
  stats?: {
    [key: string]: string | number;
  };
  notes?: string;
  dateJoined?: string;
}

/**
 * Props for PlayerCard component
 */
export interface PlayerCardProps {
  player: Player;
  variant?: 'simple' | 'attendance' | 'selection' | 'details' | 'stats';
  size?: 'small' | 'medium' | 'large';
  onClick?: (player: Player) => void;
  onStatusChange?: (player: Player, status: 'present' | 'absent' | 'pending' | 'available' | 'selected') => void;
  onEditNotes?: (player: Player) => void;
  showBorder?: boolean;
  selected?: boolean;
  statusActions?: boolean;
  className?: string;
  avatarOnly?: boolean;
  expanded?: boolean;
  onPositionChange?: (player: Player, position: string) => void;
  positionOptions?: string[];
  expandable?: boolean;
}

/**
 * PlayerCard - A standardized component for displaying players throughout the app
 * 
 * The component adapts its display based on the variant and size props:
 * - simple: Just shows player info without any actions
 * - attendance: Shows attendance status and actions
 * - selection: Shows selection checkbox/radio state
 * - details: Shows more detailed player info
 * - stats: Shows player stats
 */
const PlayerCard: React.FC<PlayerCardProps> = ({
  player,
  variant = 'simple',
  size = 'medium',
  onClick,
  onStatusChange,
  onEditNotes,
  showBorder = false,
  selected = false,
  statusActions = true,
  className = '',
  avatarOnly = false,
  expanded = false,
  onPositionChange,
  positionOptions = ['Goalkeeper', 'Defender', 'Midfielder', 'Forward'],
  expandable = false
}) => {
  // Get initials if not provided
  const initials = player.initials || player.name.charAt(0);
  
  // State for expand/collapse if not controlled externally
  const [isExpanded, setIsExpanded] = useState(expanded);
  
  // Toggle expanded state
  const toggleExpanded = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };
  
  // Size classes
  const sizeClasses = {
    small: 'player-card-small',
    medium: 'player-card-medium',
    large: 'player-card-large'
  };

  // Get status color and icon
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'present': return 'var(--ion-color-success)';
      case 'absent': return 'var(--ion-color-danger)';
      case 'selected': return 'var(--ion-color-tertiary)';
      case 'pending': 
      case 'available':
      default: return 'var(--ion-color-medium)';
    }
  };

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'present': return checkmarkCircle;
      case 'absent': return closeCircle;
      case 'selected': return checkmarkCircle;
      case 'pending': 
      case 'available':
      default: return helpCircle;
    }
  };

  // Get card bg based on status
  const getCardBackground = () => {
    if (!player.status) return '';
    
    switch (player.status) {
      case 'present': return 'player-card-present';
      case 'absent': return 'player-card-absent';
      case 'selected': return 'player-card-selected';
      default: return '';
    }
  };

  // Avatar component
  const Avatar = () => (
    <div className="player-card-avatar-container">
      {player.avatarUrl ? (
        <IonAvatar className="player-card-avatar">
          <img src={player.avatarUrl} alt={player.name} />
        </IonAvatar>
      ) : (
        <div className="player-card-initial-avatar">
          <span>{initials}</span>
        </div>
      )}
      {player.status && !avatarOnly && (
        <div 
          className="player-card-status-indicator"
          style={{ backgroundColor: getStatusColor(player.status) }}
        >
          <IonIcon icon={getStatusIcon(player.status)} />
        </div>
      )}
    </div>
  );

  // Avatar-only mode
  if (avatarOnly) {
    return (
      <div 
        className={`player-card-avatar-only ${sizeClasses[size]} ${className}`}
        onClick={() => onClick && onClick(player)}
      >
        <Avatar />
        {size !== 'small' && <span className="player-card-avatar-name">{player.name}</span>}
      </div>
    );
  }

  // Base card component
  return (
    <div 
      className={`player-card ${sizeClasses[size]} ${getCardBackground()} ${showBorder ? 'player-card-bordered' : ''} ${selected ? 'player-card-selected' : ''} ${className}`}
      onClick={() => variant !== 'attendance' && onClick && onClick(player)}
    >
      <div className="player-card-content">
        <div className="player-card-main">
          <Avatar />

          <div className="player-card-info">
            <div className="player-card-name-row">
              <h3 className="player-card-name">{player.name}</h3>
              {player.jerseyNumber && variant !== 'simple' && (
                <span className="player-card-jersey">#{player.jerseyNumber}</span>
              )}
            </div>
            
            {player.position && (
              <p className="player-card-position">{player.position}</p>
            )}
            
            {variant === 'details' && player.stats && (
              <div className="player-card-stats">
                {Object.entries(player.stats).map(([key, value]) => (
                  <div key={key} className="player-card-stat-item">
                    <span className="player-card-stat-label">{key}:</span>
                    <span className="player-card-stat-value">{value}</span>
                  </div>
                ))}
              </div>
            )}
            
            {variant === 'attendance' && player.notes && (
              <p className="player-card-notes">
                {player.notes.length > 50 
                  ? `${player.notes.substring(0, 50)}...` 
                  : player.notes}
              </p>
            )}
          </div>
          
          {variant === 'details' && (
            <div 
              className="player-card-actions"
              onClick={(e) => {
                e.stopPropagation();
                if (expandable) toggleExpanded(e);
              }}
            >
              {expandable ? (
                <IonIcon icon={isExpanded ? chevronUp : chevronDown} />
              ) : (
                <IonIcon icon={chevronForward} />
              )}
            </div>
          )}
        </div>
        
        {/* Attendance actions */}
        {variant === 'attendance' && statusActions && (
          <div className="player-card-attendance-actions">
            <div className="player-card-status-buttons">
              <IonButton 
                size="small" 
                fill={player.status === 'present' ? 'solid' : 'outline'} 
                color="medium"
                onClick={(e) => {
                  e.stopPropagation();
                  onStatusChange && onStatusChange(player, 'present');
                }}
              >
                <IonIcon icon={checkmarkCircle} slot="start" />
                Present
              </IonButton>
              
              <IonButton 
                size="small" 
                fill={player.status === 'absent' ? 'solid' : 'outline'} 
                color="medium"
                onClick={(e) => {
                  e.stopPropagation();
                  onStatusChange && onStatusChange(player, 'absent');
                }}
              >
                <IonIcon icon={closeCircle} slot="start" />
                Absent
              </IonButton>
            </div>
            
            {onEditNotes && (
              <IonButton 
                size="small" 
                fill="clear" 
                color="medium"
                onClick={(e) => {
                  e.stopPropagation();
                  onEditNotes(player);
                }}
              >
                <IonIcon icon={pencil} slot="start" />
                {player.notes ? 'Edit Notes' : 'Add Notes'}
              </IonButton>
            )}
          </div>
        )}
        
        {/* Stats variant extra info */}
        {variant === 'stats' && player.stats && (
          <div className="player-card-stats-grid">
            {Object.entries(player.stats).map(([key, value]) => (
              <div key={key} className="player-card-stat-box">
                <div className="player-card-stat-value">{value}</div>
                <div className="player-card-stat-label">{key}</div>
              </div>
            ))}
          </div>
        )}
        
        {/* Expandable details section */}
        {variant === 'details' && isExpanded && expandable && (
          <div className="player-card-expanded">
            <div className="player-card-expanded-section">
              {/* Position selection */}
              <div className="player-card-field">
                <label className="player-card-field-label">Position</label>
                <IonSelect
                  value={player.position || ''}
                  interface="action-sheet"
                  placeholder="Select position"
                  className="player-card-select"
                  onIonChange={(e) => onPositionChange && onPositionChange(player, e.detail.value)}
                >
                  {positionOptions.map(pos => (
                    <IonSelectOption key={pos} value={pos}>{pos}</IonSelectOption>
                  ))}
                </IonSelect>
              </div>
              
              {/* Date joined */}
              {player.dateJoined && (
                <div className="player-card-field">
                  <label className="player-card-field-label">Date joined</label>
                  <div className="player-card-date">
                    <IonIcon icon={calendar} className="player-card-date-icon" />
                    <span>{player.dateJoined}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PlayerCard;