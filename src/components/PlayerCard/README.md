# PlayerCard Component

A standardized component for displaying player information across the SHOT application. This component is designed to be flexible and adaptable to different use cases while maintaining a consistent look and feel.

## Features

- Multiple display variants (simple, attendance, selection, details, stats)
- Size variants (small, medium, large)
- Consistent avatar display with fallback to initials
- Responsive to different screen sizes
- Themeable with status indicators (present, absent, pending, selected)
- Interactive options (status change, notes editing)

## Usage

```jsx
import PlayerCard from '../../components/PlayerCard';

// Basic usage
<PlayerCard 
  player={{
    id: 'player-1',
    name: '<PERSON>',
    position: 'Forward',
    initials: 'J<PERSON>',
    jerseyNumber: '10',
  }}
  variant="simple"
  size="medium"
/>

// Attendance tracking
<PlayerCard 
  player={{
    id: 'player-1',
    name: '<PERSON>',
    position: 'Forward',
    status: 'present', // or 'absent', 'pending'
    notes: 'Arrived late to training'
  }}
  variant="attendance"
  onStatusChange={(player, status) => handleStatusChange(player, status)}
  onEditNotes={(player) => handleEditNotes(player)}
/>

// Player selection
<PlayerCard 
  player={{
    id: 'player-1',
    name: '<PERSON>',
    position: 'Forward',
    status: 'selected'
  }}
  variant="selection"
  selected={true}
  onClick={(player) => handleSelection(player)}
/>

// Avatar only mode (for team lineups, etc.)
<PlayerCard 
  player={{
    id: 'player-1',
    name: 'John Doe',
    avatarUrl: '/path/to/avatar.jpg'
  }}
  avatarOnly={true}
  size="small"
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| player | Player | required | Player data object |
| variant | 'simple' \| 'attendance' \| 'selection' \| 'details' \| 'stats' | 'simple' | Display variant |
| size | 'small' \| 'medium' \| 'large' | 'medium' | Size variant |
| onClick | (player: Player) => void | undefined | Click handler |
| onStatusChange | (player: Player, status: string) => void | undefined | Status change handler |
| onEditNotes | (player: Player) => void | undefined | Notes edit handler |
| showBorder | boolean | false | Show border |
| selected | boolean | false | Is player selected |
| statusActions | boolean | true | Show status actions |
| className | string | '' | Additional CSS classes |
| avatarOnly | boolean | false | Show only avatar |

## Player Interface

```typescript
interface Player {
  id: string;
  name: string;
  avatarUrl?: string;
  initials?: string;
  position?: string;
  jerseyNumber?: string;
  status?: 'present' | 'absent' | 'pending' | 'available' | 'selected';
  stats?: {
    [key: string]: string | number;
  };
  notes?: string;
}
```