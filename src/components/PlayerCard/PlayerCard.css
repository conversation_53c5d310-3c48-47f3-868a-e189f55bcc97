/* PlayerCard Styles */

.player-card {
  display: flex;
  flex-direction: column;
  border-radius: 0.75rem;
  background-color: var(--ion-color-dark-shade);
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  transition: all 0.2s ease;
}

.player-card-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.player-card-main {
  display: flex;
  align-items: center;
  width: 100%;
}

.player-card-bordered {
  border: 1px solid var(--ion-color-medium);
}

/* Avatar styles */
.player-card-avatar-container {
  position: relative;
  margin-right: 0.75rem;
}

.player-card-avatar {
  width: 2.5rem;
  height: 2.5rem;
  background-color: var(--ion-color-dark-tint);
}

.player-card-initial-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background-color: var(--ion-color-medium);
  color: var(--ion-color-medium-contrast);
  font-weight: 500;
}

.player-card-status-indicator {
  position: absolute;
  bottom: -0.125rem;
  right: -0.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: var(--ion-color-medium);
  border: 2px solid var(--ion-color-dark-shade);
}

.player-card-status-indicator ion-icon {
  font-size: 0.75rem;
  color: white;
}

/* Info styles */
.player-card-info {
  flex: 1;
  min-width: 0;
}

.player-card-name-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.player-card-name {
  margin: 0;
  font-size: 0.9375rem;
  font-weight: 500;
  color: var(--ion-color-light);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.player-card-jersey {
  font-size: 0.75rem;
  color: var(--ion-color-medium);
  margin-left: 0.5rem;
}

.player-card-position {
  margin: 0;
  font-size: 0.75rem;
  color: var(--ion-color-medium);
}

.player-card-notes {
  margin: 0.5rem 0 0;
  font-size: 0.75rem;
  color: var(--ion-color-medium);
  font-style: italic;
}

/* Attendance actions */
.player-card-attendance-actions {
  margin-top: 0.75rem;
  display: flex;
  flex-direction: column;
}

.player-card-status-buttons {
  display: flex;
  gap: 0.5rem;
}

/* Stats styles */
.player-card-stats {
  margin-top: 0.5rem;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.player-card-stat-item {
  font-size: 0.75rem;
  color: var(--ion-color-medium);
}

.player-card-stat-label {
  color: var(--ion-color-medium);
  margin-right: 0.25rem;
}

.player-card-stat-value {
  color: var(--ion-color-light);
}

.player-card-stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.player-card-stat-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--ion-color-dark);
  border-radius: 0.5rem;
  padding: 0.5rem;
  text-align: center;
}

.player-card-stat-box .player-card-stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--ion-color-light);
  margin-bottom: 0.25rem;
}

.player-card-stat-box .player-card-stat-label {
  font-size: 0.625rem;
  color: var(--ion-color-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Sizing variations */
.player-card-small .player-card-avatar,
.player-card-small .player-card-initial-avatar {
  width: 2rem;
  height: 2rem;
}

.player-card-small .player-card-name {
  font-size: 0.875rem;
}

.player-card-small .player-card-position {
  font-size: 0.6875rem;
}

.player-card-large .player-card-avatar,
.player-card-large .player-card-initial-avatar {
  width: 3rem;
  height: 3rem;
}

.player-card-large .player-card-name {
  font-size: 1rem;
}

.player-card-large .player-card-position {
  font-size: 0.8125rem;
}

/* Status variations */
.player-card-present {
  border-left: 4px solid var(--ion-color-success);
}

.player-card-absent {
  border-left: 4px solid var(--ion-color-danger);
}

.player-card-selected {
  border-left: 4px solid var(--ion-color-tertiary);
}

/* Avatar-only mode */
.player-card-avatar-only {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin: 0.5rem;
  cursor: pointer;
}

.player-card-avatar-name {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--ion-color-light);
  max-width: 4rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.player-card-actions {
  margin-left: 0.5rem;
  color: var(--ion-color-medium);
}

/* Interactive states */
.player-card:active {
  opacity: 0.8;
}

@media (hover: hover) {
  .player-card:hover {
    background-color: var(--ion-color-dark);
  }
  
  .player-card-avatar-only:hover .player-card-avatar,
  .player-card-avatar-only:hover .player-card-initial-avatar {
    opacity: 0.9;
  }
}

/* Expandable section styles */
.player-card-expanded {
  margin-top: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid var(--ion-color-dark-tint);
}

.player-card-expanded-section {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.player-card-field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.player-card-field-label {
  font-size: 0.75rem;
  color: var(--ion-color-medium);
  font-weight: 500;
}

.player-card-select {
  background-color: var(--ion-color-dark-tint);
  border-radius: 0.375rem;
  --padding-start: 0.75rem;
  --padding-end: 0.75rem;
  --placeholder-color: var(--ion-color-medium);
  --placeholder-opacity: 1;
}

.player-card-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--ion-color-light);
}

.player-card-date-icon {
  color: var(--ion-color-medium);
  font-size: 1rem;
}