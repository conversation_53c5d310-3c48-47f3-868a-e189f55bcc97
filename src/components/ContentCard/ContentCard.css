/* ContentCard Component Styling */
.content-card {
  background-color: #000000; /* Changed default to black for consistency */
  border-radius: 0; /* Default to flat edges */
  overflow: hidden;
  margin-bottom: 16px;
  box-shadow: none; /* No shadow by default */
  width: 100%; /* Ensure full width */
}

/* Legacy theme variation - kept for backward compatibility */
.content-card.legacy-theme {
  background-color: #1e2532;
  border-radius: 16px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Header with underline */
.card-header.with-underline {
  border-bottom: 2px solid var(--underline-color, #3b82f6); /* Use color from prop, fallback to blue */
  position: relative;
}

.card-header.with-underline::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 2px;
  /* Fallback for browsers that don't support color-mix */
  background: linear-gradient(90deg, 
    var(--underline-color, #3b82f6) 0%, 
    transparent 100%);
  /* Modern browsers with color-mix support */
  background: linear-gradient(90deg, 
    var(--underline-color, #3b82f6) 0%, 
    color-mix(in srgb, var(--underline-color, #3b82f6) 50%, transparent) 50%, 
    transparent 100%);
  width: 100%;
}

.content-card.no-padding {
  padding: 0;
}

.card-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header.no-padding {
  padding: 12px 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  justify-content: flex-start;
}

.card-icon {
  color: #3b82f6; /* iOS blue accent color */
  font-size: 20px;
}

.content-card .card-header .header-left .card-title {
  margin: 0;
  color: white !important; /* White text color with !important to override */
  font-size: 18px;
  font-weight: 600;
}

.header-action {
  display: flex;
  align-items: center;
}

/* Card content styling */
.card-content {
  padding: 16px;
}

.card-content.no-padding {
  padding: 0;
}

/* Responsive adjustments for padding */
@media (max-width: 480px) {
  .card-content:not(.no-padding) {
    padding: 12px;
  }
}

/* Card content CTA buttons container */
.card-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  gap: 8px;
}

/* If single button should take full width */
.card-actions.full-width {
  justify-content: stretch;
}

.card-actions.full-width ion-button {
  flex: 1;
}

/* Dark theme specific styles */
.content-card .ion-item {
  --background: rgba(255, 255, 255, 0.05);
  --background-hover: rgba(255, 255, 255, 0.08);
  --color: white;
}

.content-card ion-list {
  background: transparent;
}

.content-card ion-item {
  --border-color: rgba(255, 255, 255, 0.1);
}

/* For light theme support */
.content-card.light-theme {
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content-card.light-theme .card-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.content-card.light-theme .card-title {
  color: #333;
}

.content-card.light-theme .card-icon {
  color: var(--ion-color-primary, #3880ff);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .card-header {
    padding: 12px;
  }
  
  .card-content {
    padding: 12px;
  }
  
  .card-title {
    font-size: 16px;
  }
}