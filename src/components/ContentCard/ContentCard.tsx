import React, { ReactNode } from 'react';
import { IonIcon } from '@ionic/react';
import './ContentCard.css';


interface ContentCardProps {
  title: string;
  icon?: string;
  children: ReactNode;
  className?: string;
  headerAction?: ReactNode;
  onClick?: () => void;
  actions?: ReactNode;
  fullWidthActions?: boolean;
  noPadding?: boolean;
  headerUnderline?: boolean;
  headerUnderlineColor?: string;
  legacyTheme?: boolean; // Reversed logic for backward compatibility
}

const ContentCard: React.FC<ContentCardProps> = ({
  title,
  icon,
  children,
  className = '',
  headerAction,
  onClick,
  actions,
  fullWidthActions = false,
  noPadding = false,
  headerUnderline = false,
  headerUnderlineColor = '#3b82f6', // Default blue color
  legacyTheme = false
}) => {
  const cardStyle = onClick ? { cursor: 'pointer' } : {};
  
  return (
    <div 
      className={`content-card ${className} ${noPadding ? 'no-padding' : ''} ${legacyTheme ? 'legacy-theme' : ''}`} 
      onClick={onClick}
      style={cardStyle}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
    >
      <div 
        className={`card-header ${noPadding ? 'no-padding' : ''} ${headerUnderline ? 'with-underline' : ''}`}
        style={headerUnderline ? { '--underline-color': headerUnderlineColor } as React.CSSProperties : undefined}
      >
        <div className="header-left">
          {icon && <IonIcon icon={icon} className="card-icon" />}
          <h3 className="card-title">{title}</h3>
        </div>
        {headerAction && (
          <div className="header-action">
            {headerAction}
          </div>
        )}
      </div>
      
      <div className={`card-content ${noPadding ? 'no-padding' : ''}`}>
        {children}
        
        {actions && (
          <div className={`card-actions ${fullWidthActions ? 'full-width' : ''}`}>
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentCard;