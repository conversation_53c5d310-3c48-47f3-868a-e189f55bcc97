/* PlayerList Component Styling */
.player-list-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-bottom: 20px;
  color: white;
}

.player-list-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0 0 16px;
}

.player-list-title-section {
  display: flex;
  flex-direction: column;
}

.player-list-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 4px;
  color: white;
}

.player-list-summary {
  font-size: 14px;
  color: #B0B0B0;
  margin: 0;
  font-weight: 400;
}

.add-player-button {
  --border-radius: 12px;
  --padding-top: 12px;
  --padding-bottom: 12px;
  --padding-start: 16px;
  --padding-end: 16px;
  font-size: 16px;
  font-weight: 500;
}

.player-list-search {
  margin-bottom: 16px;
}

.player-search-input {
  --background: rgba(255, 255, 255, 0.08);
  --color: white;
  --placeholder-color: rgba(255, 255, 255, 0.6);
  --border-radius: 12px;
  padding: 0;
}

.player-filters {
  margin-bottom: 20px;
}

.player-filters ion-segment {
  --background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  overflow: hidden;
}

.player-filters ion-segment-button {
  --color: #B0B0B0;
  --color-checked: white;
  --indicator-color: rgba(107, 0, 219, 0.5);
  font-size: 14px;
}

/* Player Items */
.player-items-container {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* Add Player Button */
.add-player-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  margin-top: 12px;
  background-color: rgba(255, 255, 255, 0.03);
  border: 2px dashed rgba(107, 0, 219, 0.5);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-direction: column;
  gap: 10px;
}

.add-player-item:hover {
  background-color: rgba(107, 0, 219, 0.1);
  border-color: rgba(107, 0, 219, 0.8);
}

.add-player-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: rgba(107, 0, 219, 0.8);
  color: white;
  font-size: 24px;
}

.add-player-text {
  font-size: 16px;
  font-weight: 600;
  color: rgba(107, 0, 219, 1);
}

.player-item {
  display: flex;
  align-items: center;
  padding: 12px 12px 12px 18px; /* Increased left padding to accommodate larger avatar */
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.player-item:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

.player-item.inactive {
  border-left: 4px solid #8c8c8c;
}

.player-item.injured {
  border-left: 4px solid #e74c3c;
}

.player-item.active {
  border-left: 4px solid #2ecc71;
}

/* Selection mode */
.player-item.selected {
  background-color: rgba(255, 255, 255, 0.1);
}

.player-selection {
  margin-left: auto;
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.select-button {
  background-color: rgba(107, 0, 219, 0.2);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
}

.select-button:hover {
  background-color: rgba(107, 0, 219, 0.4);
}

.player-selected {
  display: flex;
  align-items: center;
}

.selected-indicator {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #2ecc71;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.player-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 18px; /* Increased right margin for better spacing */
  flex-shrink: 0;
  transform: scale(1.4); /* Make avatar 40% bigger */
}

.player-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  color: white;
  background-color: #6B00DB;
}

.player-info {
  flex: 1;
}

.player-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.player-name {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px;
  color: white;
}

.player-position {
  font-size: 12px;
  color: #B0B0B0;
  text-transform: uppercase;
  font-weight: 500;
  margin-bottom: 4px;
}

.player-status {
  font-size: 12px;
  color: #999999;
}

.status-value {
  font-weight: 500;
}

.status-value.active {
  color: #2ecc71;
}

.status-value.inactive {
  color: #8c8c8c;
}

.status-value.injured {
  color: #e74c3c;
}

.player-options-button {
  background: none;
  border: none;
  color: #B0B0B0;
  font-size: 18px;
  cursor: pointer;
  padding: 4px 8px;
}

.player-options-button:hover {
  color: white;
}

/* Player Stats */
.player-stats {
  display: flex;
  gap: 16px;
  margin-left: auto;
  margin-right: 8px;
}

.player-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 36px;
}

.stat-number {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.stat-label {
  font-size: 10px;
  text-transform: uppercase;
  color: #B0B0B0;
}

.player-action {
  flex-shrink: 0;
  margin-left: 8px;
  color: #6B00DB;
  font-size: 20px;
}

.no-players-message {
  text-align: center;
  padding: 32px 16px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  color: #B0B0B0;
}