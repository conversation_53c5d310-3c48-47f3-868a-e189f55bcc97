import React, { useState } from 'react';
import {
  IonSearchbar,
  IonIcon,
  IonButton,
  IonBadge,
  IonSegment,
  IonSegmentButton,
  IonLabel,
} from '@ionic/react';
import { 
  ellipsisVertical,
  scanOutline,
  chevronForward,
  checkmarkCircle
} from 'ionicons/icons';
import UserAvatar from '../UserAvatar';
import './PlayerList.css';

export interface PlayerStat {
  label: string;
  value: string | number;
}

export interface Player {
  id: string;
  name: string;
  position: string;
  avatarUrl?: string;
  status: 'Active' | 'Inactive' | 'Injured';
  objectives?: boolean; // Whether player has objectives set
  stats?: PlayerStat[];
}

interface PlayerListProps {
  players: Player[];
  title?: string;
  summary?: string;
  onPlayerClick: (playerId: string) => void;
  onAddPlayer?: () => void;
  onPlayerOptionsClick?: (playerId: string, event: React.MouseEvent) => void;
  hideHeader?: boolean;
  
  // Selection mode props
  selectionMode?: boolean;
  selectedPlayerId?: string;
  onSelectPlayer?: (playerId: string) => void;
  selectionButtonText?: string;
}

const PlayerList: React.FC<PlayerListProps> = ({
  players,
  title = "Players",
  summary,
  onPlayerClick,
  onAddPlayer,
  onPlayerOptionsClick,
  hideHeader = false,
  
  // Selection mode props
  selectionMode = false,
  selectedPlayerId,
  onSelectPlayer,
  selectionButtonText = "SELECT"
}) => {
  const [searchText, setSearchText] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'injured' | 'inactive'>('all');
  
  // Filter players based on search text and status filter
  const filteredPlayers = players.filter(player => {
    const matchesSearch = searchText === '' || 
      player.name.toLowerCase().includes(searchText.toLowerCase()) ||
      player.position.toLowerCase().includes(searchText.toLowerCase());
    
    const matchesFilter = filterStatus === 'all' || 
      player.status.toLowerCase() === filterStatus.toLowerCase();
    
    return matchesSearch && matchesFilter;
  });
  
  // Count players with objectives
  const playersWithObjectives = players.filter(p => p.objectives).length;
  
  // Generate summary text if not provided
  const summaryText = summary || 
    (playersWithObjectives > 0 ? 
      `${playersWithObjectives} of ${players.length} have objectives set` : 
      `${players.length} players total`);

  // Handle player click based on mode
  const handlePlayerClick = (playerId: string) => {
    if (selectionMode && onSelectPlayer) {
      onSelectPlayer(playerId);
    } else {
      onPlayerClick(playerId);
    }
  };

  return (
    <div className="player-list-container">
      {!hideHeader && (
        <>
          <div className="player-list-header">
            <div className="player-list-title-section">
              <h2 className="player-list-title">{title}</h2>
              <p className="player-list-summary">{summaryText}</p>
            </div>
            
            {onAddPlayer && (
              <IonButton 
                className="add-player-button" 
                onClick={onAddPlayer}
                fill="solid"
                color="primary"
              >
                <IonIcon icon={scanOutline} slot="start" />
                Add Player
              </IonButton>
            )}
          </div>
          
          <div className="player-list-search">
            <IonSearchbar
              value={searchText}
              onIonChange={e => setSearchText(e.detail.value!)}
              placeholder="Search players..."
              className="player-search-input"
            />
          </div>
          
          <div className="player-filters">
            <IonSegment value={filterStatus} onIonChange={e => setFilterStatus(e.detail.value as any)}>
              <IonSegmentButton value="all">
                <IonLabel>All Players</IonLabel>
              </IonSegmentButton>
              <IonSegmentButton value="active">
                <IonLabel>Active</IonLabel>
              </IonSegmentButton>
              <IonSegmentButton value="injured">
                <IonLabel>Injured</IonLabel>
              </IonSegmentButton>
              <IonSegmentButton value="inactive">
                <IonLabel>Inactive</IonLabel>
              </IonSegmentButton>
            </IonSegment>
          </div>
        </>
      )}
      
      <div className="player-items-container">
        {filteredPlayers.length === 0 ? (
          <div className="no-players-message">
            <p>No players match your search criteria.</p>
          </div>
        ) : (
          filteredPlayers.map(player => (
            <div 
              key={player.id} 
              className={`player-item ${player.status.toLowerCase()} ${selectionMode && selectedPlayerId === player.id ? 'selected' : ''}`}
              onClick={() => handlePlayerClick(player.id)}
            >
              <div className="player-avatar">
                <UserAvatar avatarUrl={player.avatarUrl} size="small" />
              </div>
              
              <div className="player-info">
                <div className="player-name-row">
                  <h3 className="player-name">{player.name}</h3>
                  {onPlayerOptionsClick && !selectionMode && (
                    <button 
                      className="player-options-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        onPlayerOptionsClick(player.id, e);
                      }}
                    >
                      <IonIcon icon={ellipsisVertical} />
                    </button>
                  )}
                </div>
                <div className="player-position">{player.position}</div>
                <div className="player-status">
                  status: <span className={`status-value ${player.status.toLowerCase()}`}>{player.status}</span>
                </div>
              </div>
              
              {player.stats && player.stats.length > 0 && !selectionMode && (
                <div className="player-stats">
                  {player.stats.map((stat, index) => (
                    <div key={index} className="player-stat">
                      <div className="stat-number">{stat.value}</div>
                      <div className="stat-label">{stat.label}</div>
                    </div>
                  ))}
                </div>
              )}
              
              {selectionMode ? (
                <div className="player-selection">
                  {selectedPlayerId === player.id ? (
                    <div className="player-selected">
                      <div className="selected-indicator">
                        <IonIcon icon={checkmarkCircle} />
                      </div>
                    </div>
                  ) : (
                    <button className="select-button">{selectionButtonText}</button>
                  )}
                </div>
              ) : (
                <div className="player-action">
                  <IonIcon icon={chevronForward} />
                </div>
              )}
            </div>
          ))
        )}
        
        {/* Add Player Button */}
        {onAddPlayer && !selectionMode && (
          <div className="add-player-item" onClick={onAddPlayer}>
            <div className="add-player-icon">
              <IonIcon icon={scanOutline} />
            </div>
            <div className="add-player-text">Add Player</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PlayerList;