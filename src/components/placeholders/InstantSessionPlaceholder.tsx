import React from "react";
import { useParams } from "react-router-dom";

type InstantSessionParams = {
  sessionId: string;
};

const InstantSessionPlaceholder: React.FC = () => {
  const { sessionId } = useParams<InstantSessionParams>();

  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Instant Session (Stage 2)</h1>
        <p className="text-gray-400">Session ID: {sessionId}</p>
        <p className="text-sm text-gray-500 mt-4">
          This feature will be implemented in Stage 2
        </p>
      </div>
    </div>
  );
};

export default InstantSessionPlaceholder;