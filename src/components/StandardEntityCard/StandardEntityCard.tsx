// ABOUTME: Unified card component for displaying teams, clubs, and players 
// with multiple display modes (full, compact, minimal)

import React from 'react';
import { IonButton, IonIcon, IonBadge } from '@ionic/react';
import { 
  people as peopleIcon, 
  location as locationIcon,
  calendar as calendarIcon,
  checkmarkCircle as checkmarkIcon,
  pencil as editIcon,
  personAdd as addPlayerIcon,
  informationCircle,
  mail as mailIcon,
  call as phoneIcon,
  shield as shieldIcon
} from 'ionicons/icons';
import './StandardEntityCard.css';
import { getSportIconPath } from '../../utils/sportIconHelper';

// Type definitions for entity card props
export type DisplayMode = 'full' | 'compact' | 'minimal';

export interface EntityBase {
  id: string;
  name: string;
  entityType: 'team' | 'club' | 'player';
  sport_type?: string;
  verification_status?: string;
}

export interface TeamEntity extends EntityBase {
  entityType: 'team';
  ageGroup?: string;
  level?: string;
  playerCount: number;
  eventCount?: number;
  logo?: string;
  season?: string;
  coachName?: string;
  nextEvent?: {
    date: string;
    type: string;
  };
}

export interface ClubEntity extends EntityBase {
  entityType: 'club';
  teamCount: number;
  memberCount: number;
  established?: string;
  logo?: string;
  address?: string;
}

export interface PlayerEntity extends EntityBase {
  entityType: 'player';
  number?: string;
  position?: string;
  age?: number;
  photo?: string;
  teamName?: string;
  lastActive?: string;
  stats?: {
    evaluations?: number;
    events?: number;
  };
}

export type StandardEntity = TeamEntity | ClubEntity | PlayerEntity;

export interface StandardEntityCardProps {
  entity: StandardEntity;
  displayMode?: DisplayMode;
  onClick?: () => void;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  onAddPerson?: (id: string) => void;
  showActions?: boolean;
  className?: string;
}

export const StandardEntityCard: React.FC<StandardEntityCardProps> = ({
  entity,
  displayMode = 'full',
  onClick,
  onEdit,
  onDelete,
  onAddPerson,
  showActions = true,
  className = ''
}) => {
  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  // Get appropriate icon based on entity type
  const getEntityIcon = () => {
    switch (entity.entityType) {
      case 'team':
        return getSportIconPath(entity.sport_type);
      case 'club':
        return shieldIcon;
      case 'player':
        return entity.photo || '/assets/images/default-player.png';
      default:
        return informationCircle;
    }
  };

  // Get entity-specific subtitle
  const getEntitySubtitle = () => {
    switch (entity.entityType) {
      case 'team':
        const team = entity as TeamEntity;
        return `${team.ageGroup || ''} ${team.level || ''}`.trim() || 'Team';
      case 'club':
        return 'Club';
      case 'player':
        const player = entity as PlayerEntity;
        return player.position || 'Player';
      default:
        return '';
    }
  };

  // Render logo/photo for full display mode
  const renderLogo = () => {
    if (entity.entityType === 'team' || entity.entityType === 'club') {
      const logoUrl = (entity as TeamEntity | ClubEntity).logo;
      if (logoUrl) {
        return <img src={logoUrl} alt={`${entity.name} logo`} className="entity-logo" />;
      }
    } else if (entity.entityType === 'player') {
      const photoUrl = (entity as PlayerEntity).photo;
      if (photoUrl) {
        return <img src={photoUrl} alt={`${entity.name} photo`} className="entity-photo" />;
      }
    }
    return null;
  };

  // Get person-specific display info (for players)
  const getPersonDisplayInfo = () => {
    if (entity.entityType !== 'player') return null;
    
    const player = entity as PlayerEntity;
    return {
      number: player.number,
      teamName: player.teamName,
      age: player.age,
      lastActive: player.lastActive
    };
  };

  const personInfo = getPersonDisplayInfo();

  // Render based on entity type and display mode
  return (
    <div 
      className={`standard-entity-card display-mode-${displayMode}`} 
      data-entity-type={entity.entityType} 
      onClick={handleClick}
    >
      <div className="entity-lozenge">{entity.entityType.toUpperCase()}</div>
      
      <div className="entity-card-header">
        <div className="entity-header-content">
          <div className="entity-icon-container">
            {/* For clubs and teams, show sport icon */}
            <img 
              src={getSportIconPath(entity.sport_type)} 
              alt={entity.name} 
              className="entity-icon"
            />
          </div>
          
          <div className="entity-info">
            <h3 className="entity-name">{entity.name}</h3>
            <p className="entity-subtitle">{getEntitySubtitle()}</p>
          </div>
          
          {/* Person-specific info in header */}
          {personInfo && personInfo.number && displayMode !== 'minimal' && (
            <div className="person-number">#{personInfo.number}</div>
          )}
        </div>
        
        {/* Logo/Photo section for full display */}
        <div className="entity-visual">
          {entity.entityType === 'player' && personInfo && (
            <div className="person-team-badge">{personInfo.teamName}</div>
          )}
          
          {displayMode === 'full' && renderLogo()}
        </div>
      </div>
      
      {/* Skip body for minimal display mode */}
      {displayMode !== 'minimal' && (
        <div className="entity-card-body">
          <div className="entity-stats">
            {/* Team-specific stats */}
            {entity.entityType === 'team' && (
              <>
                <div className="entity-stat">
                  <IonIcon icon={peopleIcon} />
                  <span>{entity.playerCount} Players</span>
                </div>
                
                {displayMode === 'full' && (
                  <div className="entity-stat">
                    <IonIcon icon={calendarIcon} />
                    <span>{entity.eventCount || 0} Events</span>
                  </div>
                )}
                
                {displayMode === 'full' && entity.season && (
                  <div className="entity-stat">
                    <span className="stat-label">Season:</span>
                    <span>{entity.season}</span>
                  </div>
                )}
              </>
            )}
            
            {/* Club-specific stats */}
            {entity.entityType === 'club' && (
              <>
                <div className="entity-stat">
                  <IonIcon icon={shieldIcon} />
                  <span>{entity.teamCount} Teams</span>
                </div>
                
                <div className="entity-stat">
                  <IonIcon icon={peopleIcon} />
                  <span>{entity.memberCount} Members</span>
                </div>
                
                {displayMode === 'full' && entity.established && (
                  <div className="entity-stat">
                    <span className="stat-label">Est.</span>
                    <span>{entity.established}</span>
                  </div>
                )}
              </>
            )}
            
            {/* Player-specific stats */}
            {entity.entityType === 'player' && personInfo && (
              <>
                {personInfo.age && (
                  <div className="entity-stat">
                    <span className="stat-label">Age:</span>
                    <span>{personInfo.age}</span>
                  </div>
                )}
                
                {displayMode === 'full' && entity.stats && (
                  <>
                    {entity.stats.evaluations !== undefined && (
                      <div className="entity-stat">
                        <span className="stat-label">Evaluations:</span>
                        <span>{entity.stats.evaluations}</span>
                      </div>
                    )}
                    
                    {entity.stats.events !== undefined && (
                      <div className="entity-stat">
                        <span className="stat-label">Events:</span>
                        <span>{entity.stats.events}</span>
                      </div>
                    )}
                  </>
                )}
                
                {personInfo.lastActive && displayMode === 'full' && (
                  <div className="entity-stat">
                    <span className="stat-label">Last Active:</span>
                    <span>{personInfo.lastActive}</span>
                  </div>
                )}
              </>
            )}
          </div>
          
          {/* Additional details for full display mode */}
          {displayMode === 'full' && (
            <div className="entity-details">
              {/* Team details */}
              {entity.entityType === 'team' && (
                <>
                  {entity.coachName && (
                    <div className="entity-detail">
                      <span className="detail-label">Coach:</span>
                      <span>{entity.coachName}</span>
                    </div>
                  )}
                  
                  {entity.nextEvent && (
                    <div className="entity-detail next-event">
                      <span className="detail-label">Next Event:</span>
                      <span>{entity.nextEvent.type} - {entity.nextEvent.date}</span>
                    </div>
                  )}
                </>
              )}
              
              {/* Club details */}
              {entity.entityType === 'club' && entity.address && (
                <div className="entity-detail">
                  <IonIcon icon={locationIcon} />
                  <span>{entity.address}</span>
                </div>
              )}
            </div>
          )}
          
          {/* Verification badge */}
          {entity.verification_status && entity.verification_status === 'verified' && (
            <div className="entity-verification">
              <IonIcon icon={checkmarkIcon} color="success" />
              <span>
                {entity.verification_status.charAt(0).toUpperCase() + entity.verification_status.slice(1)}
              </span>
            </div>
          )}
          
          {/* Action buttons - configurable and adaptive based on display mode */}
          {showActions && (
            <div className="entity-actions">
              {/* Team-specific actions */}
              {entity.entityType === 'team' && (
                <>
                  {onAddPerson && (
                    <IonButton 
                      fill="outline" 
                      size={displayMode === 'compact' ? 'small' : 'default'}
                      className="action-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        onAddPerson(entity.id);
                      }}
                    >
                      <IonIcon slot="start" icon={addPlayerIcon} />
                      {displayMode === 'full' ? 'Add Player' : 'Add'}
                    </IonButton>
                  )}
                  
                  {onEdit && (
                    <IonButton 
                      fill="clear"
                      size={displayMode === 'compact' ? 'small' : 'default'}
                      className="action-button"
                      onClick={(e) => {
                        e.stopPropagation();
                        onEdit(entity.id);
                      }}
                    >
                      <IonIcon slot="icon-only" icon={editIcon} />
                    </IonButton>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};