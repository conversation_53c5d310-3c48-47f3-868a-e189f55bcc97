/* StandardEntityCard.css - Core styles */
.standard-entity-card {
  background-color: rgba(26, 26, 26, 0.7);
  border-radius: var(--shot-button-radius);
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  width: 100%;
}

/* Team-specific styling */
.standard-entity-card[data-entity-type="team"] {
  border: 2px solid var(--shot-white);
  box-shadow: none;
  background-color: rgba(26, 26, 26, 0.7);
}

/* Club-specific styling */
.standard-entity-card[data-entity-type="club"] {
  border: 2px solid var(--shot-purple);
  box-shadow: none;
  background-color: rgba(0, 0, 0, 0.95);
}

.standard-entity-card:hover {
  transform: translateY(-3px) scale(1.01);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
  border-color: var(--shot-teal);
}

/* Display mode variants */
.standard-entity-card.display-mode-minimal {
  padding-bottom: 0;
}

.standard-entity-card.display-mode-compact .entity-card-body {
  padding: 12px;
}

.standard-entity-card.display-mode-compact .entity-actions {
  padding: 12px;
}

/* Empty placeholder to maintain the 2x2 grid */
.standard-entity-card .empty-button-placeholder {
  height: 44px;
  width: 100%;
}

.entity-lozenge {
  display: inline-block;
  position: absolute;
  top: 12px;
  right: 12px;
  background-color: var(--shot-purple);
  color: white;
  font-family: var(--shot-font-heading);
  font-weight: 600;
  font-size: 0.9rem;
  padding: 4px 12px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  z-index: 10;
}

/* ====== Card Layout ====== */
.standard-entity-card .entity-card-header {
  padding: 20px;
  padding-right: 60px; /* Make room for the entity lozenge */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: transparent;
  position: relative;
}

.standard-entity-card .entity-header-content {
  display: flex;
  width: 100%;
}

.standard-entity-card .entity-icon-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.standard-entity-card .entity-icon {
  width: 48px;
  height: 48px;
  object-fit: contain;
  margin-right: 16px;
  flex-shrink: 0;
  color: var(--shot-white); /* For IonIcon */
  font-size: 32px; /* For IonIcon */
}

.standard-entity-card .entity-title-section {
  flex: 1;
  padding-right: 16px;
}

.standard-entity-card .entity-name {
  font-family: var(--shot-font-heading) !important;
  font-weight: 700 !important;
  font-size: 1.4rem !important;
  color: var(--shot-white) !important;
  margin: 0 0 6px 0 !important;
  text-transform: uppercase !important;
  display: flex !important;
  align-items: center !important;
}

.standard-entity-card .entity-subtitle {
  font-family: var(--shot-font-body);
  font-size: 0.9rem;
  color: var(--shot-gold);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: inline-block;
  padding: 3px 12px;
  background-color: rgba(247, 182, 19, 0.2);
  border-radius: 14px;
}

.standard-entity-card .entity-logo-container {
  width: 44px;
  height: 44px;
  border-radius: 8px;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(26, 188, 156, 0.4);
}

.standard-entity-card .entity-logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.standard-entity-card .entity-card-body {
  padding: 20px;
}

/* ====== Entity Stats ====== */
.standard-entity-card .entity-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
  background-color: rgba(255, 255, 255, 0.05);
  padding: 16px;
  border-radius: var(--shot-button-radius);
}

.standard-entity-card .entity-stat {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.standard-entity-card .entity-stat ion-icon {
  color: var(--shot-teal);
  font-size: 20px;
}

/* ====== Club Contact Info ====== */
.standard-entity-card .entity-contact-info {
  margin-bottom: 20px;
}

.standard-entity-card .entity-contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: var(--shot-white);
  margin-bottom: 8px;
}

.standard-entity-card .entity-contact-item ion-icon {
  color: var(--shot-teal);
  font-size: 18px;
}

/* ====== Team Coaches ====== */
.standard-entity-card .entity-coaches {
  margin-bottom: 20px;
  padding: 0 4px;
}

.standard-entity-card .section-title {
  font-size: 0.85rem;
  font-weight: 600;
  color: var(--shot-purple);
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.standard-entity-card .coach-display {
  display: flex;
  align-items: center;
}

.standard-entity-card .primary-coach {
  display: flex;
  align-items: center;
  gap: 12px;
}

.standard-entity-card .coach-avatar {
  width: 44px;
  height: 44px;
  border: 2px solid var(--shot-purple);
  box-shadow: 0 0 10px rgba(107, 0, 219, 0.3);
}

.standard-entity-card .default-avatar {
  width: 100%;
  height: 100%;
  background-color: var(--shot-purple);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  border-radius: 50%;
  font-size: 20px;
}

.standard-entity-card .coach-info {
  display: flex;
  flex-direction: column;
}

.standard-entity-card .coach-name {
  font-weight: 600;
  font-size: 1rem;
  color: var(--shot-white);
}

.standard-entity-card .coach-count {
  font-size: 0.85rem;
  color: var(--shot-purple);
  font-weight: 500;
}

.standard-entity-card .no-coach {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
}

/* ====== Club Administrators ====== */
.standard-entity-card .entity-admins {
  margin-bottom: 20px;
  padding: 0 4px;
  background-color: rgba(26, 26, 26, 0.5);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(107, 0, 219, 0.3);
}

.standard-entity-card .admins-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 10px;
}

.standard-entity-card .admin-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(0, 0, 0, 0.3);
  padding: 6px 10px;
  border-radius: 20px;
  width: fit-content;
}

.standard-entity-card .admin-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--shot-purple);
  display: flex;
  align-items: center;
  justify-content: center;
}

.standard-entity-card .admin-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.standard-entity-card .admin-info {
  display: flex;
  flex-direction: column;
}

.standard-entity-card .admin-name {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--shot-white);
}

.standard-entity-card .admin-role {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
}

.standard-entity-card .admin-count {
  font-size: 0.85rem;
  color: var(--shot-purple);
  font-weight: 500;
  padding: 8px 0;
}

.standard-entity-card .admin-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
}

.standard-entity-card .manage-admins-button {
  --color: var(--shot-purple);
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.03em;
  font-weight: 600;
}

/* ====== Club Coaches ====== */
.standard-entity-card .club-coaches {
  margin-bottom: 20px;
  padding: 0 4px;
  background-color: rgba(26, 26, 26, 0.5);
  border-radius: 8px;
  padding: 12px;
  border: 1px solid rgba(26, 188, 156, 0.3);
  margin-top: 20px;
}

.standard-entity-card .coaches-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 10px;
}

.standard-entity-card .coach-item {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(0, 0, 0, 0.3);
  padding: 6px 10px;
  border-radius: 20px;
  width: fit-content;
}

.standard-entity-card .coach-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--shot-teal);
  display: flex;
  align-items: center;
  justify-content: center;
}

.standard-entity-card .coach-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.standard-entity-card .coach-primary {
  font-size: 0.7rem;
  color: var(--shot-teal);
  font-weight: 600;
}

/* ====== Verification Status ====== */
.standard-entity-card .verification-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 20px;
  padding: 8px 12px;
  border-radius: 8px;
}

.standard-entity-card .verification-verified {
  background-color: rgba(0, 187, 0, 0.1) !important;
  color: var(--shot-green) !important;
  border: 1px solid rgba(0, 187, 0, 0.3) !important;
}

.standard-entity-card .verification-pending {
  background-color: rgba(247, 182, 19, 0.1);
  color: var(--shot-gold);
}

.standard-entity-card .verification-rejected {
  background-color: rgba(230, 57, 70, 0.1);
  color: var(--shot-red);
}

.standard-entity-card .verification-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.standard-entity-card .verification-verified .verification-dot:before {
  content: '\2713' !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.6rem;
  color: white;
}

.standard-entity-card .verification-verified .verification-dot {
  background-color: var(--shot-green) !important;
  box-shadow: 0 0 4px rgba(0, 187, 0, 0.3) !important;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.standard-entity-card .verification-pending .verification-dot {
  background-color: var(--shot-gold);
  box-shadow: 0 0 6px rgba(247, 182, 19, 0.5);
}

.standard-entity-card .verification-rejected .verification-dot {
  background-color: var(--shot-red);
  box-shadow: 0 0 6px rgba(230, 57, 70, 0.5);
}

/* ====== Actions ====== */
/* Action Buttons - Stacked layout for club cards */
.standard-entity-card .entity-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  margin-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
}

/* Default button style - team cards have buttons side-by-side */
.standard-entity-card[data-entity-type="team"] .entity-actions .action-button {
  width: calc(50% - 5px);
  flex: 0 0 auto;
}

/* Club cards have 2x2 grid layout - all buttons 50% width */
.standard-entity-card[data-entity-type="club"] .entity-actions {
  display: grid;
  grid-template-columns: 1fr 1fr; /* Two columns */
  grid-template-rows: auto auto; /* Two rows */
  gap: 15px;
  padding: 25px 16px;
  background-color: rgba(0, 0, 0, 0.4);
  border-top: 1px solid rgba(107, 0, 219, 0.2);
  margin-top: 0;
}

.standard-entity-card[data-entity-type="club"] .entity-actions .action-button {
  width: 100%;
  height: 44px;
  font-size: 0.9rem;
  --border-width: 2px;
  --border-color: var(--shot-teal);
  --background: transparent;
  --color: var(--shot-white);
}

.standard-entity-card[data-entity-type="club"] .entity-actions .action-button:hover {
  --background: rgba(26, 188, 156, 0.15);
}

.standard-entity-card .action-button {
  --color: var(--shot-teal);
  --border-color: var(--shot-teal);
  font-size: 0.8rem;
  height: 34px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  --padding-start: 12px;
  --padding-end: 12px;
}

.standard-entity-card .action-button ion-icon {
  font-size: 16px;
  margin-right: 4px;
}

.standard-entity-card .action-button:hover {
  --background: rgba(26, 188, 156, 0.15);
}

/* For compact display mode buttons */
.standard-entity-card.display-mode-compact .action-button {
  font-size: 0.75rem;
  --padding-start: 8px;
  --padding-end: 8px;
}

/* For minimal display mode, buttons should be smaller */
.standard-entity-card.display-mode-minimal .action-button {
  font-size: 0.7rem;
  height: 28px;
  --padding-start: 6px;
  --padding-end: 6px;
}

/* Verified lozenge */
.standard-entity-card .verified-lozenge {
  display: inline-flex;
  align-items: center;
  background-color: rgba(0, 187, 0, 0.1);
  color: var(--shot-green);
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 2px 8px;
  margin-left: 8px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Responsive Adjustments */
@media (max-width: 767px) {
  .standard-entity-card .entity-card-header {
    padding: 16px;
    padding-right: 60px;
  }
  
  .standard-entity-card .entity-name {
    font-size: 1.2rem !important;
  }
  
  .standard-entity-card .entity-card-body {
    padding: 16px;
  }
  
  .standard-entity-card .entity-stat {
    font-size: 0.85rem;
  }
  
  .standard-entity-card.display-mode-compact .entity-name {
    font-size: 1.1rem !important;
  }
  
  .standard-entity-card.display-mode-minimal .entity-name {
    font-size: 1rem !important;
  }
}

/* List Variations */
.entity-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  width: 100%;
}

.entity-list.single-column {
  display: flex;
  flex-direction: column;
}