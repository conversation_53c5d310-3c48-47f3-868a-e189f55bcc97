import React, { useEffect, useRef, useCallback } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IonIcon } from '@ionic/react';
import { videocamOutline, ticketOutline, cardOutline, personOutline, peopleOutline } from 'ionicons/icons';
import { useInfiniteQuery } from '@tanstack/react-query';
import { formatDistanceToNow } from 'date-fns';
import { supabase } from '@/lib/supabase';
import { ActivityType } from '../utils/activity';

const ACTIVITY_ICONS = {
  [ActivityType.VIDEO_WATCHED]: videocamOutline,
  [ActivityType.EVENT_RSVP]: ticketOutline,
  [ActivityType.MEMBERSHIP_CHANGED]: cardOutline,
  [ActivityType.PROFILE_UPDATED]: personOutline,
  [ActivityType.CHILD_ADDED]: peopleOutline,
  [ActivityType.CHILD_UPDATED]: peopleOutline,
};

interface Activity {
  id: string;
  activity_type: ActivityType;
  metadata: Record<string, any>;
  created_at: string;
}

const getActivityText = (activity: Activity): string => {
  switch (activity.activity_type) {
    case ActivityType.VIDEO_WATCHED:
      return `Watched video "${activity.metadata?.title}"`;
    case ActivityType.EVENT_RSVP:
      return `RSVP'd ${activity.metadata?.status} to "${activity.metadata?.eventName}"`;
    case ActivityType.MEMBERSHIP_CHANGED:
      return `Changed membership to ${activity.metadata?.planName}`;
    case ActivityType.PROFILE_UPDATED:
      return 'Updated profile information';
    case ActivityType.CHILD_ADDED:
      return `Added ${activity.metadata?.childName} to family`;
    case ActivityType.CHILD_UPDATED:
      return `Updated ${activity.metadata?.childName}'s information`;
    default:
      return 'Unknown activity';
  }
};

const Timeline: React.FC = () => {
  const loader = useRef<HTMLDivElement>(null);

  const fetchActivities = async ({ pageParam = undefined }) => {
    const { data: { session } } = await supabase.auth.getSession();
    const user = session?.user;
    if (!user) throw new Error('Not authenticated');

    let query = supabase
      .from('activities')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .limit(10);

    if (pageParam) {
      query = query.lt('created_at', pageParam);
    }

    const { data, error } = await query;
    if (error) throw error;

    const lastItem = data[data.length - 1];
    return {
      activities: data || [],
      nextCursor: lastItem?.created_at
    };
  };

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    status
  } = useInfiniteQuery({
    queryKey: ['activities'],
    queryFn: fetchActivities,
    getNextPageParam: (lastPage) => lastPage.nextCursor,
    initialPageParam: undefined
  });

  const handleObserver = useCallback((entries: IntersectionObserverEntry[]) => {
    const target = entries[0];
    if (target.isIntersecting && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage]);

  useEffect(() => {
    const element = loader.current;
    if (!element) return;

    const option = {
      root: null,
      rootMargin: '20px',
      threshold: 0
    };

    const observer = new IntersectionObserver(handleObserver, option);
    observer.observe(element);

    return () => observer.disconnect();
  }, [handleObserver]);

  if (status === 'pending') {
    return (
      <div className="flex justify-center items-center h-full">
        <IonSpinner />
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="text-center text-gray-500 py-8">
        Error loading activities
      </div>
    );
  }

  const activities = data?.pages.flatMap(page => page.activities) || [];
  
  if (activities.length === 0) {
    return (
      <div className="text-center text-gray-500 py-8">
        No activities yet
      </div>
    );
  }

  return (
    <div className="px-4">
      {activities.map((activity, index) => (
        <div 
          key={activity.id}
          className="relative flex gap-4 pb-8"
        >
          {/* Timeline line */}
          {index < activities.length - 1 && (
            <div className="absolute left-4 top-8 bottom-0 w-0.5 bg-gray-700" />
          )}

          {/* Icon */}
          <div className="relative z-10 w-8 h-8 flex items-center justify-center rounded-full bg-gray-800 text-emerald-500">
            <IonIcon 
              icon={ACTIVITY_ICONS[activity.activity_type] || personOutline} 
              className="w-5 h-5"
            />
          </div>

          {/* Content */}
          <div className="flex-1 bg-gray-800 rounded-lg p-4">
            <p className="text-white">{getActivityText(activity)}</p>
            <p className="text-sm text-gray-400 mt-1">
              {formatDistanceToNow(new Date(activity.created_at), { addSuffix: true })}
            </p>
          </div>
        </div>
      ))}

      {/* Loading spinner */}
      <div ref={loader} className="py-4 flex justify-center">
        {isFetchingNextPage && <IonSpinner />}
      </div>
    </div>
  );
};

export default Timeline;