import React, { useEffect, useState } from "react";
import { useParams, useHistory } from "react-router-dom";
import { supabase } from '@/lib/supabase';
import LoadingPage from "../../pages/Loading";
import { isCoachForTeam, hasTeamAccessPermission } from "../../services/firebase/team";
import { IonButton, IonIcon, IonContent, IonCard, IonCardContent, IonCardHeader, IonCardTitle } from '@ionic/react';
import { arrowBack, home, people, bug } from 'ionicons/icons';

interface CoachProtectedDebugRouteProps {
  children: React.ReactNode;
}

interface DebugInfo {
  currentUserId: string;
  teamCoaches: any[];
  clubCoaches: any[];
  clubAdmins: any[];
  teamData: any;
  clubId: string;
  errors: string[];
}

/**
 * A route wrapper that checks if the current user is a coach for the team specified in the URL params
 * If not, redirects to the home page
 */
const CoachProtectedDebugRoute: React.FC<CoachProtectedDebugRouteProps> = ({ children }) => {
  const { teamId, clubId } = useParams<{teamId?: string; clubId?: string}>();
  const history = useHistory();
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [isCoach, setIsCoach] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);
  const [teamName, setTeamName] = useState<string>("");
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [showDebug, setShowDebug] = useState(false);
  
  console.log('🔒 CoachProtectedRoute - teamId:', teamId, 'clubId:', clubId);

  // Get current user directly from Supabase
  useEffect(() => {
    const getUser = async () => {
      try {
        // For Supabase v2, use getSession() method
        const { data: { session } } = await supabase.auth.getSession();
        setCurrentUser(session?.user || null);
      } catch (error) {
        console.error('Error getting session:', error);
        setCurrentUser(null);
      }
    };
    getUser();

    // Subscribe to auth changes
    const { data: authListener } = supabase.auth.onAuthStateChange((_event, session) => {
      setCurrentUser(session?.user || null);
    });

    return () => {
      // Clean up the subscription
      if (authListener?.subscription) {
        authListener.subscription.unsubscribe();
      }
    };
  }, []);

  useEffect(() => {
    const checkCoachStatus = async () => {
      if (!currentUser || !teamId) {
        setIsCoach(false);
        setLoading(false);
        return;
      }

      const debug: DebugInfo = {
        currentUserId: currentUser.id,
        teamCoaches: [],
        clubCoaches: [],
        clubAdmins: [],
        teamData: null,
        clubId: '',
        errors: []
      };

      try {
        // Get team data including club_id
        try {
          const { data: teamData, error: teamError } = await supabase
            .from('teams')
            .select('*')
            .eq('team_id', teamId)
            .single();
          
          if (teamError) {
            debug.errors.push(`Team fetch error: ${teamError.message}`);
          } else if (teamData) {
            debug.teamData = teamData;
            debug.clubId = teamData.club_id;
            setTeamName(teamData.team_name || 'Unknown Team');
          }
        } catch (teamError) {
          debug.errors.push(`Team fetch exception: ${teamError}`);
        }

        // Get team coaches
        try {
          const { data: teamCoaches, error: teamCoachError } = await supabase
            .from('team_coaches')
            .select('*')
            .eq('team_id', teamId)
            .eq('status', 'active');
          
          if (teamCoachError) {
            debug.errors.push(`Team coaches error: ${teamCoachError.message}`);
          } else {
            debug.teamCoaches = teamCoaches || [];
          }
        } catch (error) {
          debug.errors.push(`Team coaches exception: ${error}`);
        }

        // Get club coaches (if we have club_id)
        if (debug.clubId) {
          try {
            const { data: clubCoaches, error: clubCoachError } = await supabase
              .from('club_coaches')
              .select('*')
              .eq('club_id', debug.clubId)
              .eq('status', 'active');
            
            if (clubCoachError) {
              debug.errors.push(`Club coaches error: ${clubCoachError.message}`);
            } else {
              debug.clubCoaches = clubCoaches || [];
            }
          } catch (error) {
            debug.errors.push(`Club coaches exception: ${error}`);
          }
        }
        
        setDebugInfo(debug);
        
        // Use the comprehensive permission check
        const result = await hasTeamAccessPermission(currentUser.id, teamId);
        // TEMPORARILY DISABLED: Allow all authenticated users
        setIsCoach(true); // TODO: Re-enable permission check: setIsCoach(result);
      } catch (error) {
        debug.errors.push(`General error: ${error}`);
        setDebugInfo(debug);
        setIsCoach(false);
      } finally {
        setLoading(false);
      }
    };

    if (currentUser) {
      checkCoachStatus();
    }
  }, [currentUser, teamId]);

  if (loading) {
    return <LoadingPage />;
  }

  if (!isCoach) {
    const isTeamCoach = debugInfo?.teamCoaches.some(coach => coach.user_id === debugInfo.currentUserId) || false;
    const isClubCoach = debugInfo?.clubCoaches.some(coach => coach.user_id === debugInfo.currentUserId) || false;
    const isClubAdmin = debugInfo?.clubAdmins.some(admin => admin.user_id === debugInfo.currentUserId) || false;

    return (
      <IonContent className="ion-padding" style={{ 
        '--background': '#1E293B',
        minHeight: '100vh',
        padding: '1rem'
      }}>
        <div style={{ 
          backgroundColor: '#111827',
          borderRadius: '10px',
          padding: '2rem',
          maxWidth: '600px',
          width: '100%',
          textAlign: 'center',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          margin: '0 auto'
        }}>
          <div style={{ marginBottom: '1.5rem' }}>
            <IonIcon 
              icon={people} 
              style={{ 
                fontSize: '3rem', 
                color: '#6F42C1'
              }} 
            />
          </div>
          
          <h2 style={{ color: 'white', marginBottom: '1rem' }}>
            Access Restricted
          </h2>
          
          <p style={{ color: '#9CA3AF', marginBottom: '2rem' }}>
            {teamName ? 
              `You need to be a coach for "${teamName}" or a coach for the club to access this page.` : 
              "You don't have the required permissions to access this team page."}
          </p>

          {/* Debug Information - Only show in development */}
          {import.meta.env.DEV && (
            <div style={{ marginBottom: '2rem' }}>
              <IonButton
                expand="block"
                fill="outline"
                onClick={() => setShowDebug(!showDebug)}
                style={{
                  '--color': '#F59E0B',
                  '--border-color': '#F59E0B',
                  marginBottom: '1rem'
                }}
              >
                <IonIcon slot="start" icon={bug} />
                {showDebug ? 'Hide Debug Info' : 'Show Debug Info'}
              </IonButton>

              {showDebug && debugInfo && (
                <div style={{ textAlign: 'left', marginBottom: '1rem' }}>
                  <IonCard style={{ '--background': '#1F2937' }}>
                    <IonCardHeader>
                      <IonCardTitle style={{ color: '#F59E0B', fontSize: '1rem' }}>Debug Information</IonCardTitle>
                    </IonCardHeader>
                    <IonCardContent>
                      <div style={{ color: '#E5E7EB', fontSize: '0.875rem', fontFamily: 'monospace' }}>
                        <p><strong>Your User ID:</strong> {debugInfo.currentUserId}</p>
                        <p><strong>Team ID:</strong> {teamId}</p>
                        <p><strong>Club ID:</strong> {debugInfo.clubId || 'Not found'}</p>
                        <p><strong>Team Name:</strong> {teamName || 'Not found'}</p>
                        
                        <hr style={{ margin: '1rem 0', borderColor: '#374151' }} />
                        
                        <p><strong>Permission Check Results:</strong></p>
                        <p style={{ color: isTeamCoach ? '#10B981' : '#EF4444' }}>• Team Coach: {isTeamCoach ? 'YES' : 'NO'}</p>
                        <p style={{ color: isClubCoach ? '#10B981' : '#EF4444' }}>• Club Coach: {isClubCoach ? 'YES' : 'NO'}</p>
                        
                        <hr style={{ margin: '1rem 0', borderColor: '#374151' }} />
                        
                        <p><strong>Team Coaches ({debugInfo.teamCoaches.length}):</strong></p>
                        {debugInfo.teamCoaches.length > 0 ? (
                          debugInfo.teamCoaches.map((coach, i) => (
                            <p key={i} style={{ marginLeft: '1rem', fontSize: '0.8rem' }}>
                              • User: {coach.user_id} | Role: {coach.role} | Primary: {coach.is_primary ? 'Yes' : 'No'}
                            </p>
                          ))
                        ) : (
                          <p style={{ marginLeft: '1rem', color: '#9CA3AF' }}>No team coaches found</p>
                        )}
                        
                        <p><strong>Club Coaches ({debugInfo.clubCoaches.length}):</strong></p>
                        {debugInfo.clubCoaches.length > 0 ? (
                          debugInfo.clubCoaches.map((coach, i) => (
                            <p key={i} style={{ marginLeft: '1rem', fontSize: '0.8rem' }}>
                              • User: {coach.user_id} | Role: {coach.role} | Primary: {coach.is_primary ? 'Yes' : 'No'}
                            </p>
                          ))
                        ) : (
                          <p style={{ marginLeft: '1rem', color: '#9CA3AF' }}>No club coaches found</p>
                        )}
                        
                        {debugInfo.errors.length > 0 && (
                          <>
                            <hr style={{ margin: '1rem 0', borderColor: '#374151' }} />
                            <p><strong style={{ color: '#EF4444' }}>Errors:</strong></p>
                            {debugInfo.errors.map((error, i) => (
                              <p key={i} style={{ marginLeft: '1rem', color: '#EF4444', fontSize: '0.8rem' }}>
                                • {error}
                              </p>
                            ))}
                          </>
                        )}
                      </div>
                    </IonCardContent>
                  </IonCard>
                </div>
              )}
            </div>
          )}
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <IonButton
              expand="block"
              onClick={() => history.goBack()}
              style={{
                '--background': '#6F42C1',
                '--background-hover': '#5E35B1',
                '--background-activated': '#5E35B1'
              }}
            >
              <IonIcon slot="start" icon={arrowBack} />
              Go Back
            </IonButton>
            
            <IonButton
              expand="block"
              fill="outline"
              onClick={() => history.push('/coach/clubs/management')}
              style={{
                '--color': '#6F42C1',
                '--border-color': '#6F42C1'
              }}
            >
              <IonIcon slot="start" icon={people} />
              View My Clubs
            </IonButton>
            
            <IonButton
              expand="block"
              fill="clear"
              onClick={() => history.push('/home')}
              style={{ '--color': '#9CA3AF' }}
            >
              <IonIcon slot="start" icon={home} />
              Go to Home
            </IonButton>
          </div>
        </div>
      </IonContent>
    );
  }

  console.log('🔒 CoachProtectedRoute - Rendering children, isCoach:', isCoach);
  return <>{children}</>;
};

export default CoachProtectedDebugRoute;
