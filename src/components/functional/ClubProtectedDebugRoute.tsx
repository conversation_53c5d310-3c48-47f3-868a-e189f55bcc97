import React, { useEffect, useState } from "react";
import { useParams, useHistory } from "react-router-dom";
import { supabase } from '@/lib/supabase';
import LoadingPage from "../../pages/Loading";
import { IonButton, IonIcon, IonContent, IonCard, IonCardContent, IonCardHeader, IonCardTitle } from '@ionic/react';
import { arrowBack, home, people, bug } from 'ionicons/icons';

interface ClubProtectedDebugRouteProps {
  children: React.ReactNode;
}

interface DebugInfo {
  currentUserId: string;
  clubCoaches: any[];
  clubAdmins: any[];
  clubData: any;
  clubId: string;
  errors: string[];
}

/**
 * A route wrapper that checks if the current user is a coach for the club specified in the URL params
 * This requires CLUB-level permissions (for operations like creating teams, club-level event management)
 * If not, redirects to the home page
 */
const ClubProtectedDebugRoute: React.FC<ClubProtectedDebugRouteProps> = ({ children }) => {
  const { clubId } = useParams<{clubId?: string}>();
  const history = useHistory();
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [isClubCoach, setIsClubCoach] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);
  const [clubName, setClubName] = useState<string>("");
  const [debugInfo, setDebugInfo] = useState<DebugInfo | null>(null);
  const [showDebug, setShowDebug] = useState(false);

  // Get current user directly from Supabase
  useEffect(() => {
    const getUser = async () => {
      try {
        // For Supabase v2, use getSession() method
        const { data: { session } } = await supabase.auth.getSession();
        setCurrentUser(session?.user || null);
      } catch (error) {
        console.error('Error getting session:', error);
        setCurrentUser(null);
      }
    };
    getUser();

    // Subscribe to auth changes
    const { data: authListener } = supabase.auth.onAuthStateChange((_event, session) => {
      setCurrentUser(session?.user || null);
    });

    return () => {
      // Clean up the subscription
      if (authListener?.subscription) {
        authListener.subscription.unsubscribe();
      }
    };
  }, []);

  useEffect(() => {
    const checkClubCoachStatus = async () => {
      if (!currentUser || !clubId) {
        setIsClubCoach(false);
        setLoading(false);
        return;
      }

      const debug: DebugInfo = {
        currentUserId: currentUser.id,
        clubCoaches: [],
        clubAdmins: [],
        clubData: null,
        clubId: clubId,
        errors: []
      };

      try {
        // Get club data
        try {
          const { data: clubData, error: clubError } = await supabase
            .from('clubs')
            .select('*')
            .eq('club_id', clubId)
            .single();
          
          if (clubError) {
            debug.errors.push(`Club fetch error: ${clubError.message}`);
          } else if (clubData) {
            debug.clubData = clubData;
            setClubName(clubData.club_name || 'Unknown Club');
          }
        } catch (clubError) {
          debug.errors.push(`Club fetch exception: ${clubError}`);
        }

        // Get club coaches
        try {
          const { data: clubCoaches, error: clubCoachError } = await supabase
            .from('club_coaches')
            .select('*')
            .eq('club_id', clubId)
            .eq('status', 'active');
          
          if (clubCoachError) {
            debug.errors.push(`Club coaches error: ${clubCoachError.message}`);
          } else {
            debug.clubCoaches = clubCoaches || [];
          }
        } catch (error) {
          debug.errors.push(`Club coaches exception: ${error}`);
        }
        
        setDebugInfo(debug);
        
        // Check if user is a coach for the club
        const isUserClubCoach = debug.clubCoaches.some(coach => coach.user_id === currentUser.id);
        // TEMPORARILY DISABLED: Allow all authenticated users
        setIsClubCoach(true); // TODO: Re-enable permission check: setIsClubCoach(isUserClubCoach);
      } catch (error) {
        debug.errors.push(`General error: ${error}`);
        setDebugInfo(debug);
        setIsClubCoach(false);
      } finally {
        setLoading(false);
      }
    };

    if (currentUser) {
      checkClubCoachStatus();
    }
  }, [currentUser, clubId]);

  if (loading) {
    return <LoadingPage />;
  }

  if (!isClubCoach) {
    const isClubCoach = debugInfo?.clubCoaches.some(coach => coach.user_id === debugInfo.currentUserId) || false;
    const isClubAdmin = debugInfo?.clubAdmins.some(admin => admin.user_id === debugInfo.currentUserId) || false;

    return (
      <IonContent className="ion-padding" style={{ 
        '--background': '#1E293B',
        minHeight: '100vh',
        padding: '1rem'
      }}>
        <div style={{ 
          backgroundColor: '#111827',
          borderRadius: '10px',
          padding: '2rem',
          maxWidth: '600px',
          width: '100%',
          textAlign: 'center',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          margin: '0 auto'
        }}>
          <div style={{ marginBottom: '1.5rem' }}>
            <IonIcon 
              icon={people} 
              style={{ 
                fontSize: '3rem', 
                color: '#6F42C1'
              }} 
            />
          </div>
          
          <h2 style={{ color: 'white', marginBottom: '1rem' }}>
            Club Access Required
          </h2>
          
          <p style={{ color: '#9CA3AF', marginBottom: '2rem' }}>
            {clubName ? 
              `You need to be a coach for "${clubName}" to perform this operation.` : 
              "You don't have the required club-level permissions to access this page."}
          </p>

          {/* Debug Information - Only show in development */}
          {import.meta.env.DEV && (
            <div style={{ marginBottom: '2rem' }}>
              <IonButton
                expand="block"
                fill="outline"
                onClick={() => setShowDebug(!showDebug)}
                style={{
                  '--color': '#F59E0B',
                  '--border-color': '#F59E0B',
                  marginBottom: '1rem'
                }}
              >
                <IonIcon slot="start" icon={bug} />
                {showDebug ? 'Hide Debug Info' : 'Show Debug Info'}
              </IonButton>

              {showDebug && debugInfo && (
                <div style={{ textAlign: 'left', marginBottom: '1rem' }}>
                  <IonCard style={{ '--background': '#1F2937' }}>
                    <IonCardHeader>
                      <IonCardTitle style={{ color: '#F59E0B', fontSize: '1rem' }}>Debug Information</IonCardTitle>
                    </IonCardHeader>
                    <IonCardContent>
                      <div style={{ color: '#E5E7EB', fontSize: '0.875rem', fontFamily: 'monospace' }}>
                        <p><strong>Your User ID:</strong> {debugInfo.currentUserId}</p>
                        <p><strong>Club ID:</strong> {debugInfo.clubId}</p>
                        <p><strong>Club Name:</strong> {clubName || 'Not found'}</p>
                        
                        <hr style={{ margin: '1rem 0', borderColor: '#374151' }} />
                        
                        <p><strong>Permission Check Results:</strong></p>
                        <p style={{ color: isClubCoach ? '#10B981' : '#EF4444' }}>• Club Coach: {isClubCoach ? 'YES' : 'NO'}</p>
                        <p style={{ color: isClubAdmin ? '#10B981' : '#EF4444' }}>• Club Admin: {isClubAdmin ? 'YES' : 'NO'}</p>
                        
                        <hr style={{ margin: '1rem 0', borderColor: '#374151' }} />
                        
                        <p><strong>Club Coaches ({debugInfo.clubCoaches.length}):</strong></p>
                        {debugInfo.clubCoaches.length > 0 ? (
                          debugInfo.clubCoaches.map((coach, i) => (
                            <p key={i} style={{ marginLeft: '1rem', fontSize: '0.8rem' }}>
                              • User: {coach.user_id} | Role: {coach.role} | Primary: {coach.is_primary ? 'Yes' : 'No'}
                            </p>
                          ))
                        ) : (
                          <p style={{ marginLeft: '1rem', color: '#9CA3AF' }}>No club coaches found</p>
                        )}
                        
                        {debugInfo.errors.length > 0 && (
                          <>
                            <hr style={{ margin: '1rem 0', borderColor: '#374151' }} />
                            <p><strong style={{ color: '#EF4444' }}>Errors:</strong></p>
                            {debugInfo.errors.map((error, i) => (
                              <p key={i} style={{ marginLeft: '1rem', color: '#EF4444', fontSize: '0.8rem' }}>
                                • {error}
                              </p>
                            ))}
                          </>
                        )}
                      </div>
                    </IonCardContent>
                  </IonCard>
                </div>
              )}
            </div>
          )}
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
            <IonButton
              expand="block"
              onClick={() => history.goBack()}
              style={{
                '--background': '#6F42C1',
                '--background-hover': '#5E35B1',
                '--background-activated': '#5E35B1'
              }}
            >
              <IonIcon slot="start" icon={arrowBack} />
              Go Back
            </IonButton>
            
            <IonButton
              expand="block"
              fill="outline"
              onClick={() => history.push('/coach/clubs/management')}
              style={{
                '--color': '#6F42C1',
                '--border-color': '#6F42C1'
              }}
            >
              <IonIcon slot="start" icon={people} />
              View My Clubs
            </IonButton>
            
            <IonButton
              expand="block"
              fill="clear"
              onClick={() => history.push('/home')}
              style={{ '--color': '#9CA3AF' }}
            >
              <IonIcon slot="start" icon={home} />
              Go to Home
            </IonButton>
          </div>
        </div>
      </IonContent>
    );
  }

  return <>{children}</>;
};

export default ClubProtectedDebugRoute;
