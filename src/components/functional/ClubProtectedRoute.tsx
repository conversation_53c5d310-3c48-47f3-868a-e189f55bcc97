// routes/wrappers/ClubProtectedRoute.tsx
import React, { useEffect, useState } from "react";
import { Redirect, useParams } from "react-router-dom";
import { supabase } from "@/lib/supabase";
import LoadingPage from "@/pages/Loading";

const ClubProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { clubId } = useParams<{ clubId: string }>();
  const [loading, setLoading] = useState(true);
  const [allowed, setAllowed] = useState(false);

  useEffect(() => {
    const checkAccess = async () => {
      setLoading(true);

      const { data: { session } } = await supabase.auth.getSession();
      const user = session?.user;

      if (!user) {
        setAllowed(false);
        setLoading(false);
        return;
      }

      // Check if user is a coach for this club
      const { data: coaches } = await supabase
        .from("club_coaches")
        .select("user_id")
        .eq("club_id", clubId)
        .eq("status", "active");

      const isCoach = coaches?.some((c) => c.user_id === user.id);

      // Check if user is an admin for this club
      const { data: admins } = await supabase
        .from("club_administrators")
        .select("user_id")
        .eq("club_id", clubId);

      const isAdmin = admins?.some((a) => a.user_id === user.id) ?? false;

      setAllowed(isCoach || isAdmin);
      setLoading(false);
    };

    if (clubId) checkAccess();
  }, [clubId]);

  if (loading) return <LoadingPage />;

  if (!allowed) return <Redirect to="/home" />;

  return <>{children}</>;
};

export default ClubProtectedRoute;