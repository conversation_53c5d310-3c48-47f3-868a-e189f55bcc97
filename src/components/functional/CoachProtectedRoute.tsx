// routes/wrappers/CoachProtectedRoute.tsx
import React, { useEffect, useState } from "react";
import { Redirect, useParams } from "react-router-dom";
import { supabase } from "@/lib/supabase";
import LoadingPage from "@/pages/Loading";

const CoachProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { teamId } = useParams<{ teamId?: string }>();
  const [loading, setLoading] = useState(true);
  const [allowed, setAllowed] = useState(false);

  useEffect(() => {
    const checkAccess = async () => {
      setLoading(true);

      const { data: { session } } = await supabase.auth.getSession();
      const user = session?.user;

      if (!user) {
        setAllowed(false);
        setLoading(false);
        return;
      }

      if (!teamId) {
        // If no teamId param, just require authentication
        setAllowed(true);
        setLoading(false);
        return;
      }

      const { data: teamCoaches } = await supabase
        .from("team_coaches")
        .select("user_id")
        .eq("team_id", teamId)
        .eq("status", "active");

      const isCoach = teamCoaches?.some((c) => c.user_id === user.id) ?? false;

      setAllowed(isCoach);
      setLoading(false);
    };

    checkAccess();
  }, [teamId]);

  if (loading) return <LoadingPage />;

  if (!allowed) return <Redirect to="/home" />;

  return <>{children}</>;
};

export default CoachProtectedRoute;