import React, { useEffect, useState } from "react";
import { useParams, useHistory } from "react-router-dom";
import { supabase } from '@/lib/supabase';
import LoadingPage from "../../pages/Loading";
import { IonButton, IonIcon, IonContent } from '@ionic/react';
import { arrowBack, home, people, lockClosed } from 'ionicons/icons';

interface AdminProtectedRouteProps {
  children: React.ReactNode;
}

/**
 * A route wrapper that ensures only superadmins can access protected administrator pages
 */
const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({ children }) => {
  const { clubId } = useParams<{clubId?: string}>();
  const history = useHistory();
  const [loading, setLoading] = useState(true);
  const [hasAccess, setHasAccess] = useState(false);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);

  useEffect(() => {
    const checkAccess = async () => {
      // Simple check to make sure user is logged in
      const { data: { session } } = await supabase.auth.getSession();
      const user = session?.user;
      if (!user) {
        setHasAccess(false);
        setLoading(false);
        return;
      }

      try {
        // Check if user has superadmin privileges in profiles table
        const { data, error } = await supabase
          .from('profiles')
          .select('privileges')
          .eq('id', user.id)
          .single();
        
        if (error) {
          console.error('Error checking superadmin status:', error);
          setHasAccess(false);
          setIsSuperAdmin(false);
          setLoading(false);
          return;
        }
        
        // Check if the privileges array contains 'superadmin'
        const hasSuperAdminPrivileges = Array.isArray(data?.privileges) && 
          data.privileges.includes('superadmin');
        
        setIsSuperAdmin(hasSuperAdminPrivileges);
        
        if (hasSuperAdminPrivileges) {
          // Superadmins always have access
          setHasAccess(true);
        } else {
          // For non-superadmin: Check if they're an administrator of this specific club
          // This allows club administrators to manage their own club, but not others
          if (clubId) {
            const { data: adminData, error: adminError } = await supabase
              .from('club_administrators')
              .select('*')
              .eq('club_id', clubId)
              .eq('user_id', user.id)
              .eq('is_active', true)
              .maybeSingle();
            
            if (adminError) {
              console.error('Error checking club admin status:', adminError);
              setHasAccess(false);
            } else {
              // If they're an admin of this club, grant access
              setHasAccess(!!adminData);
            }
          } else {
            // No clubId provided, this is a global admin area, only superadmins allowed
            setHasAccess(false);
          }
        }
      } catch (err) {
        console.error('Exception checking access permissions:', err);
        setHasAccess(false);
      } finally {
        setLoading(false);
      }
    };

    checkAccess();
  }, [clubId]);

  if (loading) {
    return <LoadingPage />;
  }

  if (!hasAccess) {
    return (
      <IonContent className="ion-padding" style={{ '--background': '#1E293B' }}>
        <div style={{ padding: '2rem', textAlign: 'center' }}>
          <IonIcon 
            icon={lockClosed} 
            style={{ 
              fontSize: '48px', 
              color: '#e74c3c', 
              marginBottom: '20px' 
            }} 
          />
          <h2 style={{ color: 'white' }}>Access Restricted</h2>
          <p style={{ color: '#9CA3AF' }}>
            {isSuperAdmin 
              ? "You don't have permission to access this page."
              : "Only superadmins can access this area."}
          </p>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', marginTop: '2rem' }}>
            <IonButton expand="block" onClick={() => history.goBack()}>
              <IonIcon slot="start" icon={arrowBack} />
              Go Back
            </IonButton>
            
            {clubId && (
              <IonButton expand="block" fill="outline" onClick={() => history.push(`/coach/club/${clubId}`)}>
                <IonIcon slot="start" icon={people} />
                Back to Club
              </IonButton>
            )}
            
            <IonButton expand="block" fill="outline" onClick={() => history.push('/coach/clubs/management')}>
              <IonIcon slot="start" icon={people} />
              View My Clubs
            </IonButton>
            
            <IonButton expand="block" fill="clear" onClick={() => history.push('/home')}>
              <IonIcon slot="start" icon={home} />
              Go to Home
            </IonButton>
          </div>
        </div>
      </IonContent>
    );
  }

  return <>{children}</>;
};

export default AdminProtectedRoute;