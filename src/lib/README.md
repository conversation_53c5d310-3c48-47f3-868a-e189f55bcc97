# React Query Infrastructure

This directory contains the React Query infrastructure and utilities for managing server state in the SHOT application, specifically optimized for Supabase operations.

## Overview

The infrastructure provides:
- Configured React Query client with Supabase-optimized defaults
- Consistent query key factory for type-safe caching
- Error handling utilities for Supabase operations
- Mutation utilities with retry logic and optimistic updates
- Error boundary components for graceful error handling

## Files

### Core Configuration
- `react-query.ts` - React Query client configuration with Supabase-optimized defaults
- `query-keys.ts` - Type-safe query key factory for consistent caching patterns

### Error Handling
- `supabase-errors.ts` - Utilities for normalizing and handling Supabase errors
- `mutation-utils.ts` - Utilities for creating mutations with proper error handling

### Components
- `../components/ErrorBoundary/UserDataErrorBoundary.tsx` - Error boundary for user data operations

### Types
- `../types/user.ts` - TypeScript types for all user-related data structures

## Usage

### Basic Setup

The React Query client is already configured in `src/App.tsx`:

```typescript
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from './lib/react-query';

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      {/* Your app components */}
    </QueryClientProvider>
  );
}
```

### Using Query Keys

```typescript
import { queryKeys } from './lib/query-keys';
import { useQuery } from '@tanstack/react-query';

// Fetch user profile
const { data: profile } = useQuery({
  queryKey: queryKeys.profile.byId(userId),
  queryFn: () => fetchProfile(userId),
});

// Fetch SHOT points
const { data: shotPoints } = useQuery({
  queryKey: queryKeys.shotPoints.bySportHeadId(sportHeadId),
  queryFn: () => fetchShotPoints(sportHeadId),
});
```

### Error Handling

```typescript
import { normalizeSupabaseError, getUserFriendlyErrorMessage } from './lib/supabase-errors';

try {
  await supabaseOperation();
} catch (error) {
  const normalizedError = normalizeSupabaseError(error);
  const userMessage = getUserFriendlyErrorMessage(normalizedError);
  // Display userMessage to user
}
```

### Creating Mutations

```typescript
import { createStandardMutation } from './lib/mutation-utils';
import { useMutation } from '@tanstack/react-query';

const updateProfileMutation = useMutation(
  createStandardMutation(
    async (profileData) => {
      const { data, error } = await supabase
        .from('profiles')
        .update(profileData)
        .eq('id', userId);
      
      if (error) throw error;
      return data;
    },
    {
      context: 'profile-update',
      retryCount: 2,
    }
  )
);
```

### Error Boundaries

Wrap components that use user data with the error boundary:

```typescript
import { UserDataErrorBoundary } from './components/ErrorBoundary/UserDataErrorBoundary';

function UserProfile() {
  return (
    <UserDataErrorBoundary>
      <ProfileComponent />
    </UserDataErrorBoundary>
  );
}
```

## Configuration Details

### Query Defaults
- **Stale Time**: 5 minutes - Data is considered fresh for 5 minutes
- **GC Time**: 10 minutes - Data is kept in cache for 10 minutes after component unmount
- **Refetch on Window Focus**: Disabled - Prevents unnecessary refetches
- **Retry Logic**: Smart retry based on error type (auth errors don't retry)

### Mutation Defaults
- **Retry Count**: 1 retry for mutations, 3 for queries
- **Retry Logic**: No retry for auth errors (401) or client errors (4xx)
- **Exponential Backoff**: Delays increase exponentially, capped at 30 seconds

### Error Handling
- **Auth Errors**: Automatically detected and handled (no retry)
- **Network Errors**: Retry with exponential backoff
- **Validation Errors**: No retry, user-friendly messages
- **Server Errors**: Retry with backoff

## Query Key Patterns

The query key factory follows consistent patterns:

```typescript
// Auth queries
queryKeys.auth.session          // ['auth', 'session']
queryKeys.auth.user            // ['auth', 'user']

// Profile queries
queryKeys.profile.all          // ['profile']
queryKeys.profile.byId(userId) // ['profile', userId]
queryKeys.profile.current      // ['profile', 'current']

// SHOT Points queries
queryKeys.shotPoints.bySportHeadId(id) // ['shotPoints', id]
queryKeys.shotPoints.byUserId(userId)  // ['shotPoints', 'user', userId]

// Subscriptions
queryKeys.subscriptions.profile(userId) // ['subscription', 'profile', userId]
```

## Testing

The infrastructure includes comprehensive tests:

```bash
# Run all infrastructure tests
npm test src/lib/__tests__/

# Run specific test files
npm test src/lib/__tests__/react-query.test.ts
npm test src/lib/__tests__/query-keys.test.ts
npm test src/lib/__tests__/integration.test.ts
```

## Best Practices

1. **Always use the query key factory** - Ensures consistent caching and invalidation
2. **Handle errors gracefully** - Use the error utilities for consistent error handling
3. **Use error boundaries** - Wrap user data components with UserDataErrorBoundary
4. **Leverage optimistic updates** - Use the mutation utilities for better UX
5. **Follow the retry logic** - Don't retry auth errors, do retry network errors
6. **Use proper TypeScript types** - Import types from `../types/user.ts`
7. **Avoid `any` types** - Use Supabase generated types from `Database` for type safety
8. **Leverage type guards** - Use the error type guards for proper error handling

## Migration Notes

This infrastructure is designed to work alongside the existing UserContext during the migration period. The query client is already integrated into the main App.tsx and ready for use by new hooks and components.
## Type 
Safety Improvements

This infrastructure has been updated to eliminate `any` types and leverage Supabase's generated types:

### Using Supabase Generated Types

```typescript
import type { Database } from '../types/database';
import type { UserProfile, UserProfileUpdate } from '../types/user';

// UserProfile is now: Database['public']['Tables']['profiles']['Row']
// UserProfileUpdate is now: Database['public']['Tables']['profiles']['Update']

const updateProfile = async (userId: string, updates: UserProfileUpdate) => {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select('*')
    .single();
    
  if (error) {
    throw normalizeSupabaseError(error); // Properly typed error handling
  }
  
  return data; // Properly typed return value
};
```

### Error Type Safety

```typescript
// Error handling with proper types
try {
  await supabaseOperation();
} catch (error: unknown) {
  const normalizedError = normalizeSupabaseError(error); // UserError type
  const userMessage = getUserFriendlyErrorMessage(normalizedError);
  
  // Type-safe error checking
  if (isAuthError(error)) {
    // Handle auth error
  } else if (isNetworkError(error)) {
    // Handle network error
  }
}
```

### Query Hook Example

See `src/hooks/useUserProfileQuery.ts` for a complete example of properly typed React Query hooks that:
- Use Supabase generated types
- Avoid `any` types completely
- Provide proper error handling
- Include comprehensive TypeScript support