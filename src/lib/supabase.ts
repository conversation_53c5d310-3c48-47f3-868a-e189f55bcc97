import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/database'

const supabaseUrl = import.meta.env.VITE_APP_SUPABASE_URL as string
const supabaseAnonKey = import.meta.env.VITE_APP_SUPABASE_ANON_KEY as string

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Updated v2.x configuration with enhanced security
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce' // Enhanced security in v2.x
  },
  db: {
    schema: 'public'
  }
})

export type { Database }

// =============================================================================
// TYPE HELPERS
// =============================================================================

// Helper types for easier database operations
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type InsertTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type UpdateTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']

// Specific table types for common use
export type Profile = Tables<'profiles'>
export type Team = Tables<'teams'>
export type Event = Tables<'events'>

// Helper function to safely cast JSON fields
export function castJsonField<T>(field: unknown): T | null {
  if (field === null || field === undefined) return null;
  if (typeof field === 'object') return field as T;
  return null;
}

// Helper function to get team_id from registration_metadata
export function getTeamIdFromProfile(profile: Profile): string | null {
  const metadata = castJsonField<{ team_id?: string }>(profile.registration_metadata);
  return metadata?.team_id || null;
}
