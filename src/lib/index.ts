/**
 * Main exports for React Query infrastructure and utilities
 */

// React Query configuration
export { createQueryClient, queryClient } from './react-query';

// Query keys factory
export { queryKeys, invalidationPatterns } from './query-keys';

// Error handling utilities
export {
  isAuthError,
  isNetworkError,
  isValidationError,
  normalizeSupabaseError,
  getUserFriendlyErrorMessage,
  shouldRetryError,
  logSupabaseError,
} from './supabase-errors';

// Mutation utilities
export {
  getDefaultMutationOptions,
  createMutationWrapper,
  createOptimisticProfileUpdate,
  exponentialBackoff,
  createStandardMutation,
} from './mutation-utils';

// Re-export types
export type * from '../types/user';