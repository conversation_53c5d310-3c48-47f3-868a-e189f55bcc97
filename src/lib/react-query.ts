import { QueryClient } from '@tanstack/react-query';

/**
 * React Query client configuration optimized for Supabase operations
 * Provides proper defaults for caching, retries, and error handling
 */
export const createQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Cache data for 5 minutes before considering it stale
        staleTime: 5 * 60 * 1000,
        // Keep data in cache for 10 minutes after component unmounts
        gcTime: 10 * 60 * 1000,
        // Don't refetch on window focus to avoid unnecessary requests
        refetchOnWindowFocus: false,
        // Retry failed requests with exponential backoff
        retry: (failureCount, error: any) => {
          // Don't retry on authentication errors
          if (error?.status === 401 || error?.code === 'PGRST301') {
            return false;
          }
          // Don't retry on client errors (4xx)
          if (error?.status >= 400 && error?.status < 500) {
            return false;
          }
          // Retry up to 3 times for other errors
          return failureCount < 3;
        },
        // Exponential backoff delay
        retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      },
      mutations: {
        // Retry mutations once on network errors
        retry: (failureCount, error: any) => {
          if (error?.status === 401 || error?.code === 'PGRST301') {
            return false;
          }
          return failureCount < 1;
        },
      },
    },
  });
};

/**
 * Default query client instance
 * Use this for the main app QueryClientProvider
 */
export const queryClient = createQueryClient();