/**
 * Real-time Subscription Utilities
 * 
 * Provides centralized utilities for managing Supabase real-time subscriptions
 * with React Query cache invalidation, proper cleanup, and error handling.
 * 
 * Features:
 * - Automatic subscription cleanup on component unmount
 * - Scoped subscriptions to prevent unnecessary updates
 * - Integration with React Query cache invalidation
 * - Comprehensive error handling and retry logic
 * - Type-safe subscription management
 */

import { useEffect, useRef, } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { queryKeys } from '@/lib/query-keys';
import type { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';

/**
 * Subscription configuration for different data types
 */
type SubscriptionConfig = {
  channelName: string;
  table: string;
  filter?: string;
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  onPayload?: (payload: RealtimePostgresChangesPayload<any>) => void;
  invalidateQueries: string[][];
};

/**
 * Subscription status type
 */
type SubscriptionStatus = 'IDLE' | 'CONNECTING' | 'SUBSCRIBED' | 'ERROR' | 'CLOSED';

/**
 * Subscription manager class for handling real-time subscriptions
 */
class SubscriptionManager {
  private channels = new Map<string, RealtimeChannel>();
  private statuses = new Map<string, SubscriptionStatus>();
  private retryTimeouts = new Map<string, NodeJS.Timeout>();

  /**
   * Create a new subscription
   */
  async createSubscription(
    config: SubscriptionConfig,
    queryClient: ReturnType<typeof useQueryClient>
  ): Promise<RealtimeChannel> {
    const { channelName, table, filter, event = '*', onPayload, invalidateQueries } = config;

    // Clean up existing subscription if it exists
    this.removeSubscription(channelName);

    try {
      this.statuses.set(channelName, 'CONNECTING');

      const channel = supabase
        .channel(channelName)
        .on(
          'postgres_changes' as any,
          {
            event,
            schema: 'public',
            table,
            ...(filter && { filter }),
          },
          (payload: RealtimePostgresChangesPayload<any>) => {
            console.log(`Real-time change detected on ${table}:`, payload);

            // Call custom payload handler if provided
            if (onPayload) {
              try {
                onPayload(payload);
              } catch (error) {
                console.error(`Error in custom payload handler for ${channelName}:`, error);
              }
            }

            // Invalidate specified queries
            invalidateQueries.forEach((queryKey) => {
              queryClient.invalidateQueries({ queryKey });
            });
          }
        )
        .subscribe((status) => {
          this.statuses.set(channelName, status as SubscriptionStatus);

          switch (status) {
            case 'SUBSCRIBED': {
              // Clear any retry timeout
              const retryTimeout = this.retryTimeouts.get(channelName);
              if (retryTimeout) {
                clearTimeout(retryTimeout);
                this.retryTimeouts.delete(channelName);
              }
              break;
            }

            case 'CHANNEL_ERROR':
              console.error(`❌ Subscription error: ${channelName}`);
              this.statuses.set(channelName, 'ERROR');
              
              // Retry subscription after a delay
              this.scheduleRetry(channelName, config, queryClient);
              break;

            case 'CLOSED':
              this.statuses.set(channelName, 'CLOSED');
              break;

            default:
              console.log(`📡 Subscription status changed: ${channelName} -> ${status}`);
          }
        });

      this.channels.set(channelName, channel);
      return channel;
    } catch (error) {
      console.error(`Failed to create subscription ${channelName}:`, error);
      this.statuses.set(channelName, 'ERROR');
      throw error;
    }
  }

  /**
   * Schedule a retry for a failed subscription
   */
  private scheduleRetry(
    channelName: string,
    config: SubscriptionConfig,
    queryClient: ReturnType<typeof useQueryClient>,
    retryCount = 0
  ): void {
    const maxRetries = 3;
    const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 30000); // Exponential backoff, max 30s

    if (retryCount >= maxRetries) {
      console.error(`Max retries reached for subscription ${channelName}`);
      return;
    }

    console.log(`Scheduling retry ${retryCount + 1}/${maxRetries} for ${channelName} in ${retryDelay}ms`);

    const timeout = setTimeout(async () => {
      try {
        await this.createSubscription(config, queryClient);
      } catch (error) {
        console.error(`Retry ${retryCount + 1} failed for ${channelName}:`, error);
        this.scheduleRetry(channelName, config, queryClient, retryCount + 1);
      }
    }, retryDelay);

    this.retryTimeouts.set(channelName, timeout);
  }

  /**
   * Remove a subscription
   */
  removeSubscription(channelName: string): void {
    const channel = this.channels.get(channelName);
    if (channel) {
      supabase.removeChannel(channel);
      this.channels.delete(channelName);
    }

    // Clear retry timeout if exists
    const retryTimeout = this.retryTimeouts.get(channelName);
    if (retryTimeout) {
      clearTimeout(retryTimeout);
      this.retryTimeouts.delete(channelName);
    }

    this.statuses.delete(channelName);
  }

  /**
   * Get subscription status
   */
  getStatus(channelName: string): SubscriptionStatus {
    return this.statuses.get(channelName) || 'IDLE';
  }

  /**
   * Remove all subscriptions (cleanup)
   */
  removeAllSubscriptions(): void {
    for (const channelName of this.channels.keys()) {
      this.removeSubscription(channelName);
    }
  }

  /**
   * Get active subscription count
   */
  getActiveSubscriptionCount(): number {
    return Array.from(this.statuses.values()).filter(status => status === 'SUBSCRIBED').length;
  }
}

// Global subscription manager instance
const subscriptionManager = new SubscriptionManager();

/**
 * Hook for managing real-time subscriptions with automatic cleanup
 * 
 * @param configs - Array of subscription configurations
 * @param enabled - Whether subscriptions should be active
 * 
 * @example
 * ```tsx
 * const { user } = useAuth();
 * 
 * useRealtimeSubscriptions([
 *   {
 *     channelName: `profile_${user?.id}`,
 *     table: 'profiles',
 *     filter: `id=eq.${user?.id}`,
 *     invalidateQueries: [queryKeys.profile.byId(user?.id)],
 *   }
 * ], !!user?.id);
 * ```
 */
export function useRealtimeSubscriptions(
  configs: SubscriptionConfig[],
  enabled: boolean = true
): {
  statuses: Record<string, SubscriptionStatus>;
  activeCount: number;
} {
  const queryClient = useQueryClient();
  const configsRef = useRef<SubscriptionConfig[]>([]);
  const enabledRef = useRef(enabled);

  // Update refs when props change
  useEffect(() => {
    configsRef.current = configs;
    enabledRef.current = enabled;
  }, [configs, enabled]);

  // Setup subscriptions
  useEffect(() => {
    if (!enabled || configs.length === 0) {
      return;
    }

    const setupSubscriptions = async () => {
      for (const config of configs) {
        try {
          await subscriptionManager.createSubscription(config, queryClient);
        } catch (error) {
          console.error(`Failed to setup subscription ${config.channelName}:`, error);
        }
      }
    };

    setupSubscriptions();

    // Cleanup function
    return () => {
      for (const config of configsRef.current) {
        subscriptionManager.removeSubscription(config.channelName);
      }
    };
  }, [configs, enabled, queryClient]);

  // Get current statuses
  const statuses = configs.reduce((acc, config) => {
    acc[config.channelName] = subscriptionManager.getStatus(config.channelName);
    return acc;
  }, {} as Record<string, SubscriptionStatus>);

  return {
    statuses,
    activeCount: subscriptionManager.getActiveSubscriptionCount(),
  };
}

/**
 * Hook for profile real-time subscriptions
 * 
 * @param userId - User ID to subscribe to profile changes for
 * @param enabled - Whether the subscription should be active
 */
export function useProfileSubscription(userId?: string, enabled: boolean = true) {
  return useRealtimeSubscriptions(
    userId ? [
      {
        channelName: `profile_changes_${userId}`,
        table: 'profiles',
        filter: `id=eq.${userId}`,
        invalidateQueries: [
          [...queryKeys.profile.byId(userId)],
          [...queryKeys.profile.current],
        ],
      },
    ] : [],
    enabled && !!userId
  );
}

/**
 * Hook for SHOT points real-time subscriptions
 * 
 * @param sportHeadId - Sport head ID to subscribe to SHOT points changes for
 * @param userId - User ID for XP transaction subscriptions
 * @param enabled - Whether the subscription should be active
 */
export function useShotPointsSubscription(
  sportHeadId?: string,
  userId?: string,
  enabled: boolean = true
) {
  const configs: SubscriptionConfig[] = [];

  if (sportHeadId) {
    configs.push({
      channelName: `shot_points_changes_${sportHeadId}`,
      table: 'sport_heads',
      filter: `id=eq.${sportHeadId}`,
      event: 'UPDATE',
      invalidateQueries: [
        [...queryKeys.shotPoints.bySportHeadId(sportHeadId)],
        ...(userId ? [queryKeys.shotPoints.byUserId(userId)].map(q => [...q]) : []),
      ],
    });
  }

  if (userId) {
    configs.push({
      channelName: `xp_transactions_${userId}`,
      table: 'player_xp_transactions',
      filter: `player_id=eq.${userId}`,
      event: 'INSERT',
      invalidateQueries: [
        [...queryKeys.xpTransactions.byPlayerId(userId)],
        ...(sportHeadId ? [queryKeys.shotPoints.bySportHeadId(sportHeadId)].map(q => [...q]) : []),
        [...queryKeys.shotPoints.byUserId(userId)],
      ],
    });
  }

  return useRealtimeSubscriptions(configs, enabled && (!!sportHeadId || !!userId));
}

/**
 * Hook for authentication real-time subscriptions
 * 
 * This hook manages auth state changes and ensures proper cache invalidation
 * when authentication events occur.
 * 
 * @param enabled - Whether the subscription should be active
 */
export function useAuthSubscription(enabled: boolean = true) {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!enabled) return;

    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔐 Auth state changed:', event, session?.user?.id);
        
        // Update the query cache with new session data
        queryClient.setQueryData(queryKeys.auth.session, {
          user: session?.user ?? null,
          session,
        });

        // Handle specific auth events
        switch (event) {
          case 'SIGNED_OUT':
            // Clear all cached data when user signs out
            queryClient.clear();
            break;
          case 'SIGNED_IN':
          case 'TOKEN_REFRESHED':
            // Invalidate user-specific queries to refetch with new session
            if (session?.user?.id) {
              queryClient.invalidateQueries({
                queryKey: queryKeys.profile.byId(session.user.id),
              });
              queryClient.invalidateQueries({
                queryKey: queryKeys.shotPoints.byUserId(session.user.id),
              });
            }
            break;
          case 'USER_UPDATED':
            // Refresh auth data when user is updated
            queryClient.invalidateQueries({ queryKey: queryKeys.auth.session });
            break;
        }
      }
    );

    return () => {
      if (authListener?.subscription) {
        authListener.subscription.unsubscribe();
      }
    };
  }, [enabled, queryClient]);
}

/**
 * Utility function to get subscription manager instance
 * Useful for debugging and monitoring
 */
export function getSubscriptionManager() {
  return subscriptionManager;
}

/**
 * Utility function to cleanup all subscriptions
 * Useful for testing and cleanup scenarios
 */
export function cleanupAllSubscriptions() {
  subscriptionManager.removeAllSubscriptions();
}