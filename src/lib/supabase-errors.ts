import type { PostgrestError } from '@supabase/supabase-js';
import type { UserError } from '../types/user';

/**
 * Utility functions for handling Supabase errors consistently
 */

/**
 * Type guard for objects with status property
 */
type ErrorWithStatus = {
  status?: number;
  code?: string;
  message?: string;
  name?: string;
};

/**
 * Check if an error is a Supabase authentication error
 */
export const isAuthError = (error: unknown): error is ErrorWithStatus => {
  const err = error as ErrorWithStatus;
  return (
    err?.status === 401 ||
    err?.code === 'PGRST301' ||
    err?.message?.includes('JWT') ||
    err?.message?.includes('authentication')
  );
};

/**
 * Check if an error is a network/connection error
 */
export const isNetworkError = (error: unknown): error is ErrorWithStatus => {
  const err = error as ErrorWithStatus;
  return (
    err?.code === 'NETWORK_ERROR' ||
    err?.message?.includes('fetch') ||
    err?.message?.includes('network') ||
    err?.name === 'NetworkError'
  );
};

/**
 * Check if an error is a validation error
 */
export const isValidationError = (error: unknown): error is ErrorWithStatus => {
  const err = error as ErrorWithStatus;
  return (
    err?.status === 400 ||
    err?.code === 'PGRST116' ||
    err?.message?.includes('validation')
  );
};

/**
 * Convert Supabase error to standardized UserError format
 */
export const normalizeSupabaseError = (error: unknown): UserError => {
  const err = error as ErrorWithStatus & { details?: unknown; hint?: unknown };
  
  // Handle PostgrestError
  if (err?.code && err?.message) {
    return {
      code: err.code,
      message: err.message,
      details: err.details || err.hint,
    };
  }

  // Handle auth errors
  if (isAuthError(error)) {
    return {
      code: 'AUTH_ERROR',
      message: 'Authentication failed. Please sign in again.',
      details: error,
    };
  }

  // Handle network errors
  if (isNetworkError(error)) {
    return {
      code: 'NETWORK_ERROR',
      message: 'Network connection failed. Please check your internet connection.',
      details: error,
    };
  }

  // Handle validation errors
  if (isValidationError(error)) {
    return {
      code: 'VALIDATION_ERROR',
      message: err.message || 'Invalid data provided.',
      details: error,
    };
  }

  // Generic error fallback
  return {
    code: 'UNKNOWN_ERROR',
    message: err?.message || 'An unexpected error occurred.',
    details: error,
  };
};

/**
 * Get user-friendly error message for display
 */
export const getUserFriendlyErrorMessage = (error: UserError | unknown): string => {
  const normalizedError = (error as UserError)?.code ? (error as UserError) : normalizeSupabaseError(error);

  switch (normalizedError.code) {
    case 'AUTH_ERROR':
    case 'PGRST301':
      return 'Please sign in to continue.';
    
    case 'NETWORK_ERROR':
      return 'Connection failed. Please check your internet and try again.';
    
    case 'VALIDATION_ERROR':
    case 'PGRST116':
      return 'Please check your information and try again.';
    
    case 'PGRST106':
      return 'The requested data was not found.';
    
    case 'PGRST204':
      return 'You do not have permission to perform this action.';
    
    default:
      return normalizedError.message || 'Something went wrong. Please try again.';
  }
};

/**
 * Determine if an error should trigger a retry
 */
export const shouldRetryError = (error: unknown): boolean => {
  const normalizedError = normalizeSupabaseError(error);
  const err = error as ErrorWithStatus;
  
  // Don't retry auth errors
  if (normalizedError.code === 'AUTH_ERROR' || isAuthError(error)) {
    return false;
  }
  
  // Don't retry validation errors
  if (normalizedError.code === 'VALIDATION_ERROR' || isValidationError(error)) {
    return false;
  }
  
  // Don't retry client errors (4xx)
  if (err?.status && err.status >= 400 && err.status < 500) {
    return false;
  }
  
  // Retry network errors and server errors
  return true;
};

/**
 * Log error for debugging and monitoring
 */
export const logSupabaseError = (error: unknown, context?: string) => {
  const normalizedError = normalizeSupabaseError(error);
  
  console.error(`Supabase Error${context ? ` (${context})` : ''}:`, {
    code: normalizedError.code,
    message: normalizedError.message,
    details: normalizedError.details,
    originalError: error,
  });
  
  // Report to external error tracking if available
  if (typeof window !== 'undefined') {
    const windowWithSentry = window as typeof window & { Sentry?: unknown };
    const sentry = windowWithSentry.Sentry as {
      captureException?: (error: unknown, options: Record<string, unknown>) => void;
    };
    
    if (sentry?.captureException) {
      sentry.captureException(error, {
        tags: {
          errorType: 'supabase',
          errorCode: normalizedError.code,
          context: context || 'unknown',
        },
        extra: {
          normalizedError,
        },
      });
    }
  }
};