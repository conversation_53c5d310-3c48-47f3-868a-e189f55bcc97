import { describe, it, expect } from 'vitest';
import { queryKeys, invalidationPatterns } from '../query-keys';

describe('Query Keys', () => {
  describe('queryKeys factory', () => {
    it('should generate consistent auth query keys', () => {
      expect(queryKeys.auth.session).toEqual(['auth', 'session']);
      expect(queryKeys.auth.user).toEqual(['auth', 'user']);
    });

    it('should generate consistent profile query keys', () => {
      const userId = 'user-123';
      
      expect(queryKeys.profile.all).toEqual(['profile']);
      expect(queryKeys.profile.byId(userId)).toEqual(['profile', userId]);
      expect(queryKeys.profile.current).toEqual(['profile', 'current']);
    });

    it('should generate consistent SHOT points query keys', () => {
      const sportHeadId = 'sport-head-123';
      const userId = 'user-123';
      
      expect(queryKeys.shotPoints.all).toEqual(['shotPoints']);
      expect(queryKeys.shotPoints.bySportHeadId(sportHeadId)).toEqual(['shotPoints', sportHeadId]);
      expect(queryKeys.shotPoints.byUserId(userId)).toEqual(['shotPoints', 'user', userId]);
    });

    it('should generate consistent XP transaction query keys', () => {
      const playerId = 'player-123';
      const source = 'training';
      
      expect(queryKeys.xpTransactions.all).toEqual(['xpTransactions']);
      expect(queryKeys.xpTransactions.byPlayerId(playerId)).toEqual(['xpTransactions', playerId]);
      expect(queryKeys.xpTransactions.bySource(source)).toEqual(['xpTransactions', 'source', source]);
    });

    it('should generate consistent sport head query keys', () => {
      const sportHeadId = 'sport-head-123';
      const userId = 'user-123';
      
      expect(queryKeys.sportHead.all).toEqual(['sportHead']);
      expect(queryKeys.sportHead.byId(sportHeadId)).toEqual(['sportHead', sportHeadId]);
      expect(queryKeys.sportHead.byUserId(userId)).toEqual(['sportHead', 'user', userId]);
    });

    it('should generate consistent subscription query keys', () => {
      const userId = 'user-123';
      const sportHeadId = 'sport-head-123';
      
      expect(queryKeys.subscriptions.profile(userId)).toEqual(['subscription', 'profile', userId]);
      expect(queryKeys.subscriptions.shotPoints(sportHeadId)).toEqual(['subscription', 'shotPoints', sportHeadId]);
    });
  });

  describe('invalidationPatterns', () => {
    it('should provide correct invalidation patterns', () => {
      expect(invalidationPatterns.allProfiles()).toEqual(['profile']);
      expect(invalidationPatterns.allShotPoints()).toEqual(['shotPoints']);
      expect(invalidationPatterns.allAuth()).toEqual(['auth']);
    });

    it('should provide user-specific invalidation patterns', () => {
      const userId = 'user-123';
      const patterns = invalidationPatterns.userSpecific(userId);
      
      expect(patterns).toEqual([
        ['profile', userId],
        ['shotPoints', 'user', userId],
        ['sportHead', 'user', userId],
      ]);
    });
  });

  describe('type safety', () => {
    it('should maintain type safety for query keys', () => {
      // This test ensures TypeScript compilation passes
      const authKey: readonly string[] = queryKeys.auth.session;
      const profileKey: readonly string[] = queryKeys.profile.byId('test');
      
      expect(authKey).toBeDefined();
      expect(profileKey).toBeDefined();
    });
  });
});