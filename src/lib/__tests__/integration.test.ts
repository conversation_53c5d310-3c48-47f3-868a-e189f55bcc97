import { describe, it, expect, vi, beforeEach } from 'vitest';
import { QueryClient } from '@tanstack/react-query';
import { 
  createQueryClient, 
  queryKeys, 
  normalizeSupabaseError, 
  createStandardMutation,
  getUserFriendlyErrorMessage 
} from '../index';

describe('React Query Infrastructure Integration', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = createQueryClient();
    vi.clearAllMocks();
  });

  describe('Query Client Integration', () => {
    it('should create a properly configured query client', () => {
      expect(queryClient).toBeInstanceOf(QueryClient);
      expect(queryClient.getDefaultOptions().queries?.staleTime).toBe(5 * 60 * 1000);
    });

    it('should handle query key generation consistently', () => {
      const userId = 'test-user-123';
      const profileKey = queryKeys.profile.byId(userId);
      
      // Simulate setting and getting data with the query key
      const testData = { id: userId, full_name: 'Test User' };
      queryClient.setQueryData(profileKey, testData);
      
      const retrievedData = queryClient.getQueryData(profileKey);
      expect(retrievedData).toEqual(testData);
    });
  });

  describe('Error Handling Integration', () => {
    it('should normalize various error types consistently', () => {
      // Test auth error
      const authError = { status: 401, message: 'Unauthorized' };
      const normalizedAuthError = normalizeSupabaseError(authError);
      expect(normalizedAuthError.code).toBe('AUTH_ERROR');
      expect(getUserFriendlyErrorMessage(normalizedAuthError)).toBe('Please sign in to continue.');

      // Test network error
      const networkError = { code: 'NETWORK_ERROR', message: 'Fetch failed' };
      const normalizedNetworkError = normalizeSupabaseError(networkError);
      expect(normalizedNetworkError.code).toBe('NETWORK_ERROR');
      expect(getUserFriendlyErrorMessage(normalizedNetworkError)).toBe('Connection failed. Please check your internet and try again.');

      // Test validation error
      const validationError = { status: 400, message: 'Invalid input' };
      const normalizedValidationError = normalizeSupabaseError(validationError);
      expect(normalizedValidationError.code).toBe('VALIDATION_ERROR');
      expect(getUserFriendlyErrorMessage(normalizedValidationError)).toBe('Please check your information and try again.');
    });
  });

  describe('Mutation Integration', () => {
    it('should create mutations with proper error handling', () => {
      const mockMutationFn = vi.fn().mockResolvedValue({ success: true });
      const mutation = createStandardMutation(mockMutationFn, {
        context: 'test-mutation',
        retryCount: 2,
      });

      expect(mutation.mutationFn).toBeDefined();
      expect(typeof mutation.retry).toBe('function');
      expect(typeof mutation.retryDelay).toBe('function');
    });

    it('should handle mutation errors properly', async () => {
      const mockError = { status: 500, message: 'Server error' };
      const mockMutationFn = vi.fn().mockRejectedValue(mockError);
      
      const mutation = createStandardMutation(mockMutationFn, {
        context: 'test-mutation',
      });

      try {
        await mutation.mutationFn('test-variables');
      } catch (error) {
        expect(error).toHaveProperty('code');
        expect(error).toHaveProperty('message');
      }
    });
  });

  describe('Query Key Consistency', () => {
    it('should maintain consistent query keys across different operations', () => {
      const userId = 'user-123';
      const sportHeadId = 'sport-head-456';

      // Test that related keys follow consistent patterns
      const profileKey = queryKeys.profile.byId(userId);
      const shotPointsKey = queryKeys.shotPoints.byUserId(userId);
      const sportHeadKey = queryKeys.sportHead.byUserId(userId);

      expect(profileKey[0]).toBe('profile');
      expect(profileKey[1]).toBe(userId);

      expect(shotPointsKey[0]).toBe('shotPoints');
      expect(shotPointsKey[1]).toBe('user');
      expect(shotPointsKey[2]).toBe(userId);

      expect(sportHeadKey[0]).toBe('sportHead');
      expect(sportHeadKey[1]).toBe('user');
      expect(sportHeadKey[2]).toBe(userId);
    });
  });

  describe('Cache Management', () => {
    it('should handle cache invalidation patterns correctly', () => {
      const userId = 'test-user';
      
      // Set some test data
      queryClient.setQueryData(queryKeys.profile.byId(userId), { id: userId, name: 'Test' });
      queryClient.setQueryData(queryKeys.shotPoints.byUserId(userId), { points: 100 });
      
      // Verify data exists
      expect(queryClient.getQueryData(queryKeys.profile.byId(userId))).toBeDefined();
      expect(queryClient.getQueryData(queryKeys.shotPoints.byUserId(userId))).toBeDefined();
      
      // Test invalidation
      queryClient.invalidateQueries({ queryKey: queryKeys.profile.all });
      
      // The invalidation should mark queries as stale but data should still exist
      expect(queryClient.getQueryData(queryKeys.profile.byId(userId))).toBeDefined();
    });
  });
});