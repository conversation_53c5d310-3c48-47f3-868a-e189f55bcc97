import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createQueryClient, queryClient } from '../react-query';

describe('React Query Configuration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('createQueryClient', () => {
    it('should create a QueryClient with proper defaults', () => {
      const client = createQueryClient();
      
      expect(client).toBeDefined();
      expect(client.getDefaultOptions().queries?.staleTime).toBe(5 * 60 * 1000);
      expect(client.getDefaultOptions().queries?.gcTime).toBe(10 * 60 * 1000);
      expect(client.getDefaultOptions().queries?.refetchOnWindowFocus).toBe(false);
    });

    it('should configure retry logic correctly', () => {
      const client = createQueryClient();
      const retryFn = client.getDefaultOptions().queries?.retry as Function;
      
      expect(typeof retryFn).toBe('function');
      
      // Should not retry on 401 errors
      expect(retryFn(1, { status: 401 })).toBe(false);
      expect(retryFn(1, { code: 'PGRST301' })).toBe(false);
      
      // Should not retry on client errors
      expect(retryFn(1, { status: 400 })).toBe(false);
      expect(retryFn(1, { status: 404 })).toBe(false);
      
      // Should retry on server errors up to 3 times
      expect(retryFn(0, { status: 500 })).toBe(true);
      expect(retryFn(2, { status: 500 })).toBe(true);
      expect(retryFn(3, { status: 500 })).toBe(false);
      
      // Should retry on network errors
      expect(retryFn(1, { code: 'NETWORK_ERROR' })).toBe(true);
    });

    it('should configure retry delay with exponential backoff', () => {
      const client = createQueryClient();
      const retryDelayFn = client.getDefaultOptions().queries?.retryDelay as Function;
      
      expect(typeof retryDelayFn).toBe('function');
      
      // Test exponential backoff
      expect(retryDelayFn(0)).toBe(1000); // 2^0 * 1000
      expect(retryDelayFn(1)).toBe(2000); // 2^1 * 1000
      expect(retryDelayFn(2)).toBe(4000); // 2^2 * 1000
      
      // Should cap at 30 seconds
      expect(retryDelayFn(10)).toBe(30000);
    });
  });

  describe('default queryClient', () => {
    it('should export a default query client instance', () => {
      expect(queryClient).toBeDefined();
      expect(queryClient.getDefaultOptions()).toBeDefined();
    });
  });

  describe('mutation configuration', () => {
    it('should configure mutations with proper retry logic', () => {
      const client = createQueryClient();
      const mutationRetryFn = client.getDefaultOptions().mutations?.retry as Function;
      
      expect(typeof mutationRetryFn).toBe('function');
      
      // Should not retry on auth errors
      expect(mutationRetryFn(0, { status: 401 })).toBe(false);
      expect(mutationRetryFn(0, { code: 'PGRST301' })).toBe(false);
      
      // Should retry once on other errors
      expect(mutationRetryFn(0, { status: 500 })).toBe(true);
      expect(mutationRetryFn(1, { status: 500 })).toBe(false);
    });
  });
});