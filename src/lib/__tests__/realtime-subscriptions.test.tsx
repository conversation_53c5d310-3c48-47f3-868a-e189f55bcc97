/**
 * Integration tests for real-time subscriptions
 * 
 * Tests the real-time subscription utilities with mocked Supabase client
 * to ensure proper subscription management, cleanup, and error handling.
 */

import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { 
  useProfileSubscription, 
  useShotPointsSubscription,
  cleanupAllSubscriptions 
} from '../realtime-subscriptions';

// Mock Supabase
vi.mock('@/lib/supabase', () => ({
  supabase: {
    channel: vi.fn().mockReturnValue({
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn().mockImplementation((callback) => {
        setTimeout(() => callback('SUBSCRIBED'), 0);
        return {};
      }),
    }),
    removeChannel: vi.fn(),
    auth: {
      onAuthStateChange: vi.fn().mockReturnValue({
        data: { subscription: { unsubscribe: vi.fn() } }
      }),
    },
  },
}));

// Test wrapper component
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('Real-time Subscriptions', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    cleanupAllSubscriptions();
  });

  afterEach(() => {
    cleanupAllSubscriptions();
  });

  describe('useProfileSubscription', () => {
    it('should create profile subscription when userId is provided', async () => {
      const userId = 'user-123';

      const { result } = renderHook(
        () => useProfileSubscription(userId, true),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.statuses).toBeDefined();
      });

      // Import the mocked supabase to check calls
      const { supabase } = await import('@/lib/supabase');
      expect(supabase.channel).toHaveBeenCalledWith(`profile_changes_${userId}`);
    });

    it('should not create subscription when userId is not provided', () => {
      renderHook(
        () => useProfileSubscription(undefined, true),
        { wrapper: createWrapper() }
      );

      // Should not create any subscriptions
      expect(true).toBe(true); // Basic test to ensure no errors
    });

    it('should not create subscription when disabled', () => {
      renderHook(
        () => useProfileSubscription('user-123', false),
        { wrapper: createWrapper() }
      );

      // Should not create any subscriptions
      expect(true).toBe(true); // Basic test to ensure no errors
    });
  });

  describe('useShotPointsSubscription', () => {
    it('should create SHOT points subscriptions when both IDs are provided', async () => {
      const sportHeadId = 'sport-head-123';
      const userId = 'user-123';

      const { result } = renderHook(
        () => useShotPointsSubscription(sportHeadId, userId, true),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.statuses).toBeDefined();
      });

      // Import the mocked supabase to check calls
      const { supabase } = await import('@/lib/supabase');
      expect(supabase.channel).toHaveBeenCalledWith(`shot_points_changes_${sportHeadId}`);
      expect(supabase.channel).toHaveBeenCalledWith(`xp_transactions_${userId}`);
    });

    it('should create only sport head subscription when userId is not provided', async () => {
      const sportHeadId = 'sport-head-123';

      const { result } = renderHook(
        () => useShotPointsSubscription(sportHeadId, undefined, true),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.statuses).toBeDefined();
      });

      // Import the mocked supabase to check calls
      const { supabase } = await import('@/lib/supabase');
      expect(supabase.channel).toHaveBeenCalledWith(`shot_points_changes_${sportHeadId}`);
    });

    it('should create only XP transaction subscription when sportHeadId is not provided', async () => {
      const userId = 'user-123';

      const { result } = renderHook(
        () => useShotPointsSubscription(undefined, userId, true),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.statuses).toBeDefined();
      });

      // Import the mocked supabase to check calls
      const { supabase } = await import('@/lib/supabase');
      expect(supabase.channel).toHaveBeenCalledWith(`xp_transactions_${userId}`);
    });

    it('should not create subscriptions when disabled', () => {
      renderHook(
        () => useShotPointsSubscription('sport-head-123', 'user-123', false),
        { wrapper: createWrapper() }
      );

      // Should not create any subscriptions
      expect(true).toBe(true); // Basic test to ensure no errors
    });
  });

  describe('Subscription Cleanup', () => {
    it('should cleanup subscriptions on unmount', async () => {
      const userId = 'user-123';

      const { unmount } = renderHook(
        () => useProfileSubscription(userId, true),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        // Wait for subscription to be created
      });

      unmount();

      // Import the mocked supabase to check cleanup calls
      const { supabase } = await import('@/lib/supabase');
      expect(supabase.removeChannel).toHaveBeenCalled();
    });

    it('should handle cleanup when no subscriptions exist', () => {
      cleanupAllSubscriptions();
      
      // Should not throw any errors
      expect(true).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle subscription creation errors gracefully', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      // Mock supabase to throw error
      vi.doMock('@/lib/supabase', () => ({
        supabase: {
          channel: vi.fn().mockImplementation(() => {
            throw new Error('Subscription failed');
          }),
          removeChannel: vi.fn(),
        },
      }));

      const { result } = renderHook(
        () => useProfileSubscription('user-123', true),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.statuses).toBeDefined();
      });

      consoleSpy.mockRestore();
    });
  });
});