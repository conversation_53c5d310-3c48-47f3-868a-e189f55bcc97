import { UseMutationOptions, MutationFunction, useQueryClient } from '@tanstack/react-query';
import { normalizeSupabaseError, logSupabaseError, shouldRetryError } from './supabase-errors';
import type { UserError } from '../types/user';

/**
 * Utility functions for creating consistent React Query mutations with Supabase
 */

/**
 * Default mutation options for Supabase operations
 */
export const getDefaultMutationOptions = <TData, TVariables>(
  context?: string
): Partial<UseMutationOptions<TData, UserError, TVariables>> => ({
  retry: (failureCount, error) => {
    if (!shouldRetryError(error)) {
      return false;
    }
    return failureCount < 1; // Retry once for mutations
  },
  onError: (error) => {
    logSupabaseError(error, context);
  },
});

/**
 * Wrapper for mutation functions to handle errors consistently
 */
export const createMutationWrapper = <TData, TVariables>(
  mutationFn: MutationFunction<TData, TVariables>,
  context?: string
): MutationFunction<TData, TVariables> => {
  return async (variables: TVariables): Promise<TData> => {
    try {
      return await mutationFn(variables);
    } catch (error) {
      const normalizedError = normalizeSupabaseError(error);
      logSupabaseError(error, context);
      throw normalizedError;
    }
  };
};

/**
 * Create optimistic update configuration for profile mutations
 */
export const createOptimisticProfileUpdate = <TData, TVariables>(
  queryKey: readonly unknown[],
  updateFn: (oldData: TData | undefined, variables: TVariables) => TData | undefined
) => ({
  onMutate: async (variables: TVariables) => {
    // Get query client from React Query context
    const { QueryClient } = await import('@tanstack/react-query');
    
    // Note: In practice, this should be called within a component that has access to useQueryClient
    // This is a utility function that returns the configuration object
    return { variables };
  },
  
  // The actual implementation should be used with useQueryClient hook
  getMutationConfig: () => ({
    onMutate: async (variables: TVariables) => {
      // This will be replaced with actual queryClient when used in a component
      return { variables };
    },
    onError: (error: UserError, variables: TVariables, context: { variables: TVariables } | undefined) => {
      // Error handling will be implemented by the consuming component
      logSupabaseError(error, 'optimistic-update');
    },
    onSettled: () => {
      // Settlement logic will be implemented by the consuming component
    },
  }),
});

/**
 * Exponential backoff delay function
 */
export const exponentialBackoff = (attemptIndex: number): number => {
  return Math.min(1000 * 2 ** attemptIndex, 30000);
};

/**
 * Create a mutation with standard error handling and retry logic
 */
export const createStandardMutation = <TData, TVariables>(
  mutationFn: MutationFunction<TData, TVariables>,
  options?: {
    context?: string;
    retryCount?: number;
    onSuccess?: (data: TData, variables: TVariables) => void;
    onError?: (error: UserError, variables: TVariables) => void;
  }
) => {
  const { context, retryCount = 1, onSuccess, onError } = options || {};

  return {
    mutationFn: createMutationWrapper(mutationFn, context),
    retry: (failureCount: number, error: UserError) => {
      if (!shouldRetryError(error)) {
        return false;
      }
      return failureCount < retryCount;
    },
    retryDelay: exponentialBackoff,
    onSuccess,
    onError: (error: UserError, variables: TVariables) => {
      logSupabaseError(error, context);
      onError?.(error, variables);
    },
  };
};