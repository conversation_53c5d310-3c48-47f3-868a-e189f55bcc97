/**
 * Query key factory for consistent naming patterns across the application
 * Provides type-safe query keys for React Query operations
 */

export const queryKeys = {
  // Authentication related queries
  auth: {
    session: ['auth', 'session'] as const,
    user: ['auth', 'user'] as const,
  },

  // Profile related queries
  profile: {
    all: ['profile'] as const,
    byId: (userId: string) => ['profile', userId] as const,
    current: ['profile', 'current'] as const,
  },

  // SHOT Points related queries
  shotPoints: {
    all: ['shotPoints'] as const,
    bySportHeadId: (sportHeadId: string) => ['shotPoints', sportHeadId] as const,
    byUserId: (userId: string) => ['shotPoints', 'user', userId] as const,
  },

  // XP Transactions related queries
  xpTransactions: {
    all: ['xpTransactions'] as const,
    byPlayerId: (playerId: string) => ['xpTransactions', playerId] as const,
    bySource: (source: string) => ['xpTransactions', 'source', source] as const,
  },

  // Sport Head related queries
  sportHead: {
    all: ['sportHead'] as const,
    byId: (sportHeadId: string) => ['sportHead', sportHeadId] as const,
    byUserId: (userId: string) => ['sportHead', 'user', userId] as const,
  },

  // Real-time subscriptions
  subscriptions: {
    profile: (userId: string) => ['subscription', 'profile', userId] as const,
    shotPoints: (sportHeadId: string) => ['subscription', 'shotPoints', sportHeadId] as const,
  },
} as const;

/**
 * Utility type to extract query key from the factory
 */
export type QueryKey = typeof queryKeys[keyof typeof queryKeys];

/**
 * Helper function to create invalidation patterns
 */
export const invalidationPatterns = {
  // Invalidate all profile-related queries
  allProfiles: () => queryKeys.profile.all,
  
  // Invalidate all SHOT points queries
  allShotPoints: () => queryKeys.shotPoints.all,
  
  // Invalidate all auth queries
  allAuth: () => ['auth'],
  
  // Invalidate user-specific data
  userSpecific: (userId: string) => [
    queryKeys.profile.byId(userId),
    queryKeys.shotPoints.byUserId(userId),
    queryKeys.sportHead.byUserId(userId),
  ],
} as const;