/**
 * SHOT Points Service
 * 
 * Provides API functions for managing SHOT points, XP transactions, and sport head data.
 * Handles all Supabase operations related to SHOT points with proper error handling.
 */

import { supabase } from '@/lib/supabase';
import { normalizeSupabaseError, logSupabaseError } from '@/lib/supabase-errors';
import type { 
  ShotPointsData, 
  XpTransaction, 
  XpTransactionInsert, 
  SportHead, 
  SportHeadUpdate,
  UserError 
} from '@/types/user';

/**
 * SHOT Points service functions
 */
export const shotPointsService = {
  /**
   * Fetch SHOT points data for a specific sport head
   */
  async fetchShotPointsBySportHeadId(sportHeadId: string): Promise<ShotPointsData | null> {
    try {
      const { data, error } = await supabase
        .from('sport_heads')
        .select('id, user_id, experience_points, achievement_points, level, prestige_level, updated_at')
        .eq('id', sportHeadId)
        .single();

      if (error) {
        // Handle "no rows returned" as null
        if (error.code === 'PGRST116') {
          return null;
        }
        throw error;
      }

      if (!data) {
        return null;
      }

      // Calculate total SHOT points
      const shotPoints = (data.experience_points || 0) + (data.achievement_points || 0);

      return {
        shotPoints,
        experiencePoints: data.experience_points || 0,
        achievementPoints: data.achievement_points || 0,
        level: data.level || 1,
        prestigeLevel: data.prestige_level || 0,
        sportHeadId: data.id,
        userId: data.user_id,
        lastUpdated: data.updated_at,
      };
    } catch (error) {
      logSupabaseError(error, 'fetchShotPointsBySportHeadId');
      throw normalizeSupabaseError(error);
    }
  },

  /**
   * Fetch SHOT points data for a specific user (using their primary sport head)
   */
  async fetchShotPointsByUserId(userId: string): Promise<ShotPointsData | null> {
    try {
      // First, try to get the primary sport head
      let { data, error } = await supabase
        .from('sport_heads')
        .select('id, user_id, experience_points, achievement_points, level, prestige_level, updated_at')
        .eq('user_id', userId)
        .eq('is_primary', true)
        .single();

      // If no primary sport head found, get the most recently used one
      if (error && error.code === 'PGRST116') {
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('sport_heads')
          .select('id, user_id, experience_points, achievement_points, level, prestige_level, updated_at')
          .eq('user_id', userId)
          .order('last_used', { ascending: false })
          .limit(1)
          .single();

        if (fallbackError) {
          if (fallbackError.code === 'PGRST116') {
            return null; // No sport heads found for this user
          }
          throw fallbackError;
        }

        data = fallbackData;
      } else if (error) {
        throw error;
      }

      if (!data) {
        return null;
      }

      // Calculate total SHOT points
      const shotPoints = (data.experience_points || 0) + (data.achievement_points || 0);

      return {
        shotPoints,
        experiencePoints: data.experience_points || 0,
        achievementPoints: data.achievement_points || 0,
        level: data.level || 1,
        prestigeLevel: data.prestige_level || 0,
        sportHeadId: data.id,
        userId: data.user_id,
        lastUpdated: data.updated_at,
      };
    } catch (error) {
      logSupabaseError(error, 'fetchShotPointsByUserId');
      throw normalizeSupabaseError(error);
    }
  },

  /**
   * Add an XP transaction and update sport head points
   */
  async addXpTransaction(
    playerId: string,
    xpAmount: number,
    sourceType: string,
    sourceId?: string,
    description?: string
  ): Promise<XpTransaction> {
    try {
      // First, add the XP transaction
      const { data: transaction, error: transactionError } = await supabase
        .from('player_xp_transactions')
        .insert({
          player_id: playerId,
          xp_amount: xpAmount,
          source_type: sourceType,
          source_id: sourceId,
          description: description || `Earned ${xpAmount} XP from ${sourceType}`,
        })
        .select('*')
        .single();

      if (transactionError) {
        throw transactionError;
      }

      if (!transaction) {
        throw new Error('XP transaction creation returned no data');
      }

      // Get the user's primary sport head
      const { data: sportHead, error: sportHeadError } = await supabase
        .from('sport_heads')
        .select('id, experience_points')
        .eq('user_id', playerId)
        .eq('is_primary', true)
        .single();

      if (sportHeadError) {
        // If no primary sport head, try to get any sport head
        const { data: fallbackSportHead, error: fallbackError } = await supabase
          .from('sport_heads')
          .select('id, experience_points')
          .eq('user_id', playerId)
          .order('last_used', { ascending: false })
          .limit(1)
          .single();

        if (fallbackError) {
          console.warn('No sport head found for user, XP transaction recorded but not applied to sport head');
          return transaction;
        }

        // Update the fallback sport head
        const newXp = (fallbackSportHead.experience_points || 0) + xpAmount;
        const { error: updateError } = await supabase
          .from('sport_heads')
          .update({ 
            experience_points: newXp,
            updated_at: new Date().toISOString(),
          })
          .eq('id', fallbackSportHead.id);

        if (updateError) {
          console.error('Failed to update sport head XP:', updateError);
          // Transaction was recorded, but sport head update failed
        }

        return transaction;
      }

      // Update the primary sport head XP
      const newXp = (sportHead.experience_points || 0) + xpAmount;
      const { error: updateError } = await supabase
        .from('sport_heads')
        .update({ 
          experience_points: newXp,
          updated_at: new Date().toISOString(),
        })
        .eq('id', sportHead.id);

      if (updateError) {
        console.error('Failed to update sport head XP:', updateError);
        // Transaction was recorded, but sport head update failed
      }

      return transaction;
    } catch (error) {
      logSupabaseError(error, 'addXpTransaction');
      throw normalizeSupabaseError(error);
    }
  },

  /**
   * Fetch XP transactions for a specific player
   */
  async fetchXpTransactions(
    playerId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<XpTransaction[]> {
    try {
      const { data, error } = await supabase
        .from('player_xp_transactions')
        .select('*')
        .eq('player_id', playerId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      logSupabaseError(error, 'fetchXpTransactions');
      throw normalizeSupabaseError(error);
    }
  },

  /**
   * Update sport head data (for achievement points, level, etc.)
   */
  async updateSportHead(
    sportHeadId: string,
    updates: SportHeadUpdate
  ): Promise<SportHead> {
    try {
      const { data, error } = await supabase
        .from('sport_heads')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', sportHeadId)
        .select('*')
        .single();

      if (error) {
        throw error;
      }

      if (!data) {
        throw new Error('Sport head update returned no data');
      }

      return data;
    } catch (error) {
      logSupabaseError(error, 'updateSportHead');
      throw normalizeSupabaseError(error);
    }
  },

  /**
   * Calculate level from experience points
   * This is a helper function that can be used client-side
   */
  calculateLevel(experiencePoints: number): number {
    // Simple level calculation - can be made more complex as needed
    // Level 1: 0-99 XP, Level 2: 100-299 XP, Level 3: 300-599 XP, etc.
    if (experiencePoints < 100) return 1;
    if (experiencePoints < 300) return 2;
    if (experiencePoints < 600) return 3;
    if (experiencePoints < 1000) return 4;
    if (experiencePoints < 1500) return 5;
    
    // For higher levels, use a more complex formula
    return Math.floor(Math.sqrt(experiencePoints / 100)) + 1;
  },

  /**
   * Calculate total SHOT points from experience and achievement points
   */
  calculateTotalShotPoints(experiencePoints: number, achievementPoints: number): number {
    return (experiencePoints || 0) + (achievementPoints || 0);
  },
};

export default shotPointsService;