// ABOUTME: Service for accessing the new evaluations schema with simplified structure
// This service uses the parallel evaluations schema while maintaining compatibility with Supabase v1

import { supabase } from '../config/supabase';

export interface EventEvaluation {
  id: string;
  event_id: string;
  player_id: string;
  team_id: string;
  criteria_id: string;
  category: 'TECHNICAL' | 'PHYSICAL' | 'PSYCHOLOGICAL' | 'SOCIAL';
  area: string;
  position: string;
  player_position: string;
  question_pre?: string;
  question_coach: string;
  question_post?: string;
  answers_pre?: any;
  answers_post?: any;
  pre_score?: number;
  coach_score?: number;
  post_score?: number;
  pre_submitted_at?: string;
  pre_submitted_by?: string;
  coach_submitted_at?: string;
  coach_submitted_by?: string;
  post_submitted_at?: string;
  post_submitted_by?: string;
  pre_notes?: string;
  coach_notes?: string;
  post_notes?: string;
  week_number: number;
  framework_version: string;
  created_at: string;
  updated_at: string;
}

export interface EvaluationSummary {
  total_players: number;
  pre_completed: number;
  coach_completed: number;
  post_completed: number;
  avg_pre_score: number;
  avg_coach_score: number;
  avg_post_score: number;
}

export interface PlayerEvaluationSummary {
  event_id: string;
  player_id: string;
  total_criteria: number;
  pre_completed: number;
  coach_completed: number;
  post_completed: number;
  avg_pre_score: number;
  avg_coach_score: number;
  avg_post_score: number;
  last_pre_update?: string;
  last_coach_update?: string;
}

export interface CategorySummary {
  event_id: string;
  player_id: string;
  category: string;
  criteria_count: number;
  avg_pre_score: number;
  avg_coach_score: number;
  avg_difference: number;
}

class NewEvaluationService {
  // Feature flag to enable/disable new system
  private useNewSystem(): boolean {
    return process.env.REACT_APP_USE_NEW_EVALUATIONS === 'true';
  }

  // Log wrapper for debugging
  private log(message: string, data?: any) {
    if (this.useNewSystem()) {
      console.log(`[NewEvaluationService] ${message}`, data || '');
    }
  }

  /**
   * Create evaluations for an event (called when event is created or attendance is updated)
   * This uses the RPC function to handle the complex logic
   */
  async createEventEvaluations(eventId: string, teamId?: string): Promise<void> {
    if (!this.useNewSystem()) {
      throw new Error('New evaluation system is not enabled');
    }

    this.log('Creating event evaluations', { eventId, teamId });

    const { error } = await supabase.rpc('create_event_evaluations', {
      p_event_id: eventId,
      p_team_id: teamId || null
    });

    if (error) {
      console.error('Error creating event evaluations:', error);
      throw error;
    }

    this.log('Event evaluations created successfully');
  }

  /**
   * Get all evaluations for an event, optionally filtered by player
   * Uses the public view which maps to evaluations.event_evaluations
   */
  async getEventEvaluations(eventId: string, playerId?: string): Promise<EventEvaluation[]> {
    if (!this.useNewSystem()) {
      throw new Error('New evaluation system is not enabled');
    }

    this.log('Getting event evaluations', { eventId, playerId });

    let query = supabase
      .from('event_evaluations') // This uses the public view
      .select('*')
      .eq('event_id', eventId);

    if (playerId) {
      query = query.eq('player_id', playerId);
    }

    query = query.order('category').order('area');

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching event evaluations:', error);
      throw error;
    }

    this.log(`Found ${data?.length || 0} evaluations`);
    return data || [];
  }

  /**
   * Get evaluations grouped by player with completion status
   */
  async getPlayerEvaluationSummaries(eventId: string): Promise<PlayerEvaluationSummary[]> {
    if (!this.useNewSystem()) {
      throw new Error('New evaluation system is not enabled');
    }

    this.log('Getting player evaluation summaries', { eventId });

    const { data, error } = await supabase
      .from('evaluation_player_summary') // Uses the view
      .select('*')
      .eq('event_id', eventId);

    if (error) {
      console.error('Error fetching player summaries:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Get evaluation summary for an event using RPC function
   */
  async getEventEvaluationSummary(eventId: string): Promise<EvaluationSummary> {
    if (!this.useNewSystem()) {
      throw new Error('New evaluation system is not enabled');
    }

    this.log('Getting event evaluation summary', { eventId });

    const { data, error } = await supabase.rpc('get_event_evaluation_summary', {
      p_event_id: eventId
    });

    if (error) {
      console.error('Error fetching event summary:', error);
      throw error;
    }

    return data[0] || {
      total_players: 0,
      pre_completed: 0,
      coach_completed: 0,
      post_completed: 0,
      avg_pre_score: 0,
      avg_coach_score: 0,
      avg_post_score: 0
    };
  }

  /**
   * Save pre-evaluation scores for a player
   * Uses RPC function for security
   */
  async savePreEvaluation(
    eventId: string, 
    playerId: string, 
    scores: Record<string, number>
  ): Promise<void> {
    if (!this.useNewSystem()) {
      throw new Error('New evaluation system is not enabled');
    }

    this.log('Saving pre-evaluation', { eventId, playerId, scores });

    const { error } = await supabase.rpc('save_pre_evaluation', {
      p_event_id: eventId,
      p_player_id: playerId,
      p_scores: scores
    });

    if (error) {
      console.error('Error saving pre-evaluation:', error);
      throw error;
    }

    this.log('Pre-evaluation saved successfully');
  }

  /**
   * Save coach evaluation scores for a player
   * Uses RPC function for security
   */
  async saveCoachEvaluation(
    eventId: string, 
    playerId: string, 
    scores: Record<string, number>
  ): Promise<void> {
    if (!this.useNewSystem()) {
      throw new Error('New evaluation system is not enabled');
    }

    this.log('Saving coach evaluation', { eventId, playerId, scores });

    const { error } = await supabase.rpc('save_coach_evaluation', {
      p_event_id: eventId,
      p_player_id: playerId,
      p_scores: scores
    });

    if (error) {
      console.error('Error saving coach evaluation:', error);
      throw error;
    }

    this.log('Coach evaluation saved successfully');
  }

  /**
   * Update a single evaluation score directly
   * Useful for individual updates in the UI
   */
  async updateEvaluationScore(
    evaluationId: string,
    type: 'pre' | 'coach' | 'post',
    score: number,
    notes?: string
  ): Promise<void> {
    if (!this.useNewSystem()) {
      throw new Error('New evaluation system is not enabled');
    }

    this.log('Updating evaluation score', { evaluationId, type, score });

    const updateData: any = {};
    const user = supabase.auth.user();

    switch (type) {
      case 'pre':
        updateData.pre_score = score;
        updateData.pre_submitted_at = new Date().toISOString();
        updateData.pre_submitted_by = user?.id;
        if (notes !== undefined) updateData.pre_notes = notes;
        break;
      case 'coach':
        updateData.coach_score = score;
        updateData.coach_submitted_at = new Date().toISOString();
        updateData.coach_submitted_by = user?.id;
        if (notes !== undefined) updateData.coach_notes = notes;
        break;
      case 'post':
        updateData.post_score = score;
        updateData.post_submitted_at = new Date().toISOString();
        updateData.post_submitted_by = user?.id;
        if (notes !== undefined) updateData.post_notes = notes;
        break;
    }

    const { error } = await supabase
      .from('event_evaluations')
      .update(updateData)
      .eq('id', evaluationId);

    if (error) {
      console.error('Error updating evaluation score:', error);
      throw error;
    }

    this.log('Evaluation score updated successfully');
  }

  /**
   * Get category summaries for a player
   */
  async getPlayerCategorySummaries(
    eventId: string, 
    playerId: string
  ): Promise<CategorySummary[]> {
    if (!this.useNewSystem()) {
      throw new Error('New evaluation system is not enabled');
    }

    const { data, error } = await supabase
      .from('evaluation_category_summary')
      .select('*')
      .eq('event_id', eventId)
      .eq('player_id', playerId);

    if (error) {
      console.error('Error fetching category summaries:', error);
      throw error;
    }

    return data || [];
  }

  /**
   * Batch update multiple evaluations at once
   * Useful for saving all ratings for a player
   */
  async batchUpdateEvaluations(
    updates: Array<{
      id: string;
      pre_score?: number;
      coach_score?: number;
      post_score?: number;
      pre_notes?: string;
      coach_notes?: string;
      post_notes?: string;
    }>
  ): Promise<void> {
    if (!this.useNewSystem()) {
      throw new Error('New evaluation system is not enabled');
    }

    this.log('Batch updating evaluations', { count: updates.length });

    const user = supabase.auth.user();
    const timestamp = new Date().toISOString();

    // Process updates in batches to avoid hitting limits
    const batchSize = 50;
    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);
      
      // Update each evaluation
      const promises = batch.map(update => {
        const updateData: any = {};
        
        if (update.pre_score !== undefined) {
          updateData.pre_score = update.pre_score;
          updateData.pre_submitted_at = timestamp;
          updateData.pre_submitted_by = user?.id;
        }
        if (update.coach_score !== undefined) {
          updateData.coach_score = update.coach_score;
          updateData.coach_submitted_at = timestamp;
          updateData.coach_submitted_by = user?.id;
        }
        if (update.post_score !== undefined) {
          updateData.post_score = update.post_score;
          updateData.post_submitted_at = timestamp;
          updateData.post_submitted_by = user?.id;
        }
        if (update.pre_notes !== undefined) updateData.pre_notes = update.pre_notes;
        if (update.coach_notes !== undefined) updateData.coach_notes = update.coach_notes;
        if (update.post_notes !== undefined) updateData.post_notes = update.post_notes;

        return supabase
          .from('event_evaluations')
          .update(updateData)
          .eq('id', update.id);
      });

      const results = await Promise.all(promises);
      
      // Check for errors
      const errors = results.filter(r => r.error);
      if (errors.length > 0) {
        console.error('Errors in batch update:', errors);
        throw new Error(`Failed to update ${errors.length} evaluations`);
      }
    }

    this.log('Batch update completed successfully');
  }

  /**
   * Check if evaluations exist for an event
   */
  async hasEventEvaluations(eventId: string): Promise<boolean> {
    if (!this.useNewSystem()) {
      return false;
    }

    const { count, error } = await supabase
      .from('event_evaluations')
      .select('id', { count: 'exact', head: true })
      .eq('event_id', eventId);

    if (error) {
      console.error('Error checking event evaluations:', error);
      return false;
    }

    return (count || 0) > 0;
  }

  /**
   * Delete all evaluations for an event
   * Usually called when event is deleted
   */
  async deleteEventEvaluations(eventId: string): Promise<void> {
    if (!this.useNewSystem()) {
      throw new Error('New evaluation system is not enabled');
    }

    this.log('Deleting event evaluations', { eventId });

    const { error } = await supabase
      .from('event_evaluations')
      .delete()
      .eq('event_id', eventId);

    if (error) {
      console.error('Error deleting event evaluations:', error);
      throw error;
    }

    this.log('Event evaluations deleted successfully');
  }
}

// Export singleton instance
export const newEvaluationService = new NewEvaluationService();

// Export class for testing
export { NewEvaluationService };