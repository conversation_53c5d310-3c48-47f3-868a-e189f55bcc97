// ABOUTME: Test component to demonstrate the new evaluation system
// This component shows how to use the new evaluations schema in parallel with the old system

import React, { useState, useEffect } from 'react';
import { IonButton, IonCard, IonCardContent, IonCardHeader, IonCardTitle, IonItem, IonLabel, IonRange, IonSpinner } from '@ionic/react';
import { newEvaluationService, EventEvaluation } from '../services/NewEvaluationService';

interface TestNewEvaluationsProps {
  eventId: string;
  playerId?: string;
}

const TestNewEvaluations: React.FC<TestNewEvaluationsProps> = ({ eventId, playerId }) => {
  const [evaluations, setEvaluations] = useState<EventEvaluation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  // Check if new system is enabled
  const isEnabled = process.env.REACT_APP_USE_NEW_EVALUATIONS === 'true';

  useEffect(() => {
    if (isEnabled) {
      loadEvaluations();
    }
  }, [eventId, playerId, isEnabled]);

  const loadEvaluations = async () => {
    try {
      setLoading(true);
      setError(null);

      // Check if evaluations exist
      const hasEvaluations = await newEvaluationService.hasEventEvaluations(eventId);
      
      if (!hasEvaluations) {
        // Create evaluations for this event
        await newEvaluationService.createEventEvaluations(eventId);
      }

      // Load evaluations
      const data = await newEvaluationService.getEventEvaluations(eventId, playerId);
      setEvaluations(data);
    } catch (err) {
      console.error('Error loading evaluations:', err);
      setError(err.message || 'Failed to load evaluations');
    } finally {
      setLoading(false);
    }
  };

  const handleScoreChange = (evaluationId: string, type: 'pre' | 'coach', score: number) => {
    // Update local state immediately for responsive UI
    setEvaluations(prev => 
      prev.map(eval => 
        eval.id === evaluationId 
          ? { ...eval, [`${type}_score`]: score }
          : eval
      )
    );
  };

  const saveEvaluations = async () => {
    try {
      setSaving(true);
      setError(null);

      // Group evaluations by player for batch saving
      const playerScores: Record<string, Record<string, number>> = {};
      
      evaluations.forEach(eval => {
        if (!playerScores[eval.player_id]) {
          playerScores[eval.player_id] = {};
        }
        
        // Save coach scores (or pre scores if player is viewing their own)
        const isOwnEvaluation = eval.player_id === playerId;
        const score = isOwnEvaluation ? eval.pre_score : eval.coach_score;
        
        if (score) {
          playerScores[eval.player_id][eval.criteria_id] = score;
        }
      });

      // Save each player's scores
      for (const [pid, scores] of Object.entries(playerScores)) {
        if (pid === playerId) {
          // Player saving their own pre-evaluation
          await newEvaluationService.savePreEvaluation(eventId, pid, scores);
        } else {
          // Coach saving evaluations
          await newEvaluationService.saveCoachEvaluation(eventId, pid, scores);
        }
      }

      // Reload to get updated timestamps
      await loadEvaluations();
      
      alert('Evaluations saved successfully!');
    } catch (err) {
      console.error('Error saving evaluations:', err);
      setError(err.message || 'Failed to save evaluations');
    } finally {
      setSaving(false);
    }
  };

  if (!isEnabled) {
    return (
      <IonCard>
        <IonCardHeader>
          <IonCardTitle>New Evaluation System</IonCardTitle>
        </IonCardHeader>
        <IonCardContent>
          <p>The new evaluation system is not enabled.</p>
          <p>Set REACT_APP_USE_NEW_EVALUATIONS=true in your environment to test it.</p>
        </IonCardContent>
      </IonCard>
    );
  }

  if (loading) {
    return (
      <IonCard>
        <IonCardContent className="ion-text-center ion-padding">
          <IonSpinner />
          <p>Loading evaluations...</p>
        </IonCardContent>
      </IonCard>
    );
  }

  if (error) {
    return (
      <IonCard color="danger">
        <IonCardHeader>
          <IonCardTitle>Error</IonCardTitle>
        </IonCardHeader>
        <IonCardContent>
          <p>{error}</p>
          <IonButton onClick={loadEvaluations}>Retry</IonButton>
        </IonCardContent>
      </IonCard>
    );
  }

  // Group evaluations by category
  const groupedEvaluations = evaluations.reduce((acc, eval) => {
    if (!acc[eval.category]) {
      acc[eval.category] = [];
    }
    acc[eval.category].push(eval);
    return acc;
  }, {} as Record<string, EventEvaluation[]>);

  return (
    <IonCard>
      <IonCardHeader>
        <IonCardTitle>New Evaluation System Test</IonCardTitle>
      </IonCardHeader>
      <IonCardContent>
        <p className="ion-margin-bottom">
          Found {evaluations.length} evaluation criteria for this event.
        </p>

        {Object.entries(groupedEvaluations).map(([category, evals]) => (
          <div key={category} className="ion-margin-bottom">
            <h3>{category}</h3>
            {evals.map(eval => (
              <IonItem key={eval.id}>
                <IonLabel>
                  <h4>{eval.area}</h4>
                  <p>{eval.question_coach}</p>
                  {eval.pre_score && (
                    <p>Pre-evaluation: {eval.pre_score}/10</p>
                  )}
                </IonLabel>
                <IonRange
                  min={1}
                  max={10}
                  step={1}
                  value={eval.coach_score || 0}
                  onIonChange={e => handleScoreChange(eval.id, 'coach', e.detail.value as number)}
                  pin={true}
                  snaps={true}
                />
              </IonItem>
            ))}
          </div>
        ))}

        <IonButton 
          expand="block" 
          onClick={saveEvaluations}
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save Evaluations'}
        </IonButton>
      </IonCardContent>
    </IonCard>
  );
};

export default TestNewEvaluations;