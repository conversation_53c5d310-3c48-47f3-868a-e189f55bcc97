import { useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/lib/supabase";
import { queryKeys } from "@/lib/query-keys";
import type { User, Session } from "@supabase/supabase-js";

type AuthData = {
  user: User | null;
  session: Session | null;
};

export function useAuth() {
  const queryClient = useQueryClient();

  const data = queryClient.getQueryData<AuthData>(queryKeys.auth.session);

  const signOutMutation = {
    mutateAsync: async () => {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      queryClient.clear();
    },
    isPending: false,
  };

  return {
    user: data?.user ?? null,
    session: data?.session ?? null,
    isAuthenticated: !!data?.user,
    isEmailVerified: !!data?.user?.email_confirmed_at,
    isLoading: false, // trust AuthProvider
    signOut: async () => await signOutMutation.mutateAsync(),
  };
}