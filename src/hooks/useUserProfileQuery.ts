/**
 * Example hook demonstrating proper usage of Supabase types with React Query
 * This replaces any usage with proper TypeScript types
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { queryKeys, createStandardMutation, normalizeSupabaseError } from '../lib';
import type { 
  UserProfile, 
  UserProfileUpdate, 
  UserError 
} from '../types/user';

/**
 * Hook to fetch user profile with proper typing
 */
export const useUserProfile = (userId: string) => {
  return useQuery({
    queryKey: queryKeys.profile.byId(userId),
    queryFn: async (): Promise<UserProfile> => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        throw normalizeSupabaseError(error);
      }

      return data;
    },
    enabled: !!userId,
  });
};

/**
 * Hook to update user profile with proper typing
 */
export const useUpdateUserProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...createStandardMutation(
      async ({ userId, updates }: { userId: string; updates: UserProfileUpdate }): Promise<UserProfile> => {
        const { data, error } = await supabase
          .from('profiles')
          .update(updates)
          .eq('id', userId)
          .select('*')
          .single();

        if (error) {
          throw normalizeSupabaseError(error);
        }

        return data;
      },
      {
        context: 'profile-update',
        retryCount: 2,
      }
    ),
    onSuccess: (data, variables) => {
      // Update the cache with the new data
      queryClient.setQueryData(queryKeys.profile.byId(variables.userId), data);
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.profile.all });
    },
  });
};

/**
 * Hook to fetch current user's profile
 */
export const useCurrentUserProfile = () => {
  return useQuery({
    queryKey: queryKeys.profile.current,
    queryFn: async (): Promise<UserProfile | null> => {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) {
        return null;
      }

      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        // If profile doesn't exist, return null instead of throwing
        if (error.code === 'PGRST116') {
          return null;
        }
        throw normalizeSupabaseError(error);
      }

      return data;
    },
  });
};

/**
 * Hook to create a new user profile with proper typing
 */
export const useCreateUserProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    ...createStandardMutation(
      async (profileData: Omit<UserProfile, 'updated_at'>): Promise<UserProfile> => {
        const { data, error } = await supabase
          .from('profiles')
          .insert(profileData)
          .select('*')
          .single();

        if (error) {
          throw normalizeSupabaseError(error);
        }

        return data;
      },
      {
        context: 'profile-create',
        retryCount: 1,
      }
    ),
    onSuccess: (data) => {
      // Update the cache with the new profile
      queryClient.setQueryData(queryKeys.profile.byId(data.id), data);
      queryClient.setQueryData(queryKeys.profile.current, data);
      
      // Invalidate profile queries
      queryClient.invalidateQueries({ queryKey: queryKeys.profile.all });
    },
  });
};