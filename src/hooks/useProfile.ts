import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { queryKeys } from '@/lib/query-keys';
import {
  normalizeSupabaseError,
  logSupabaseError,
  shouldRetryError,
  getUserFriendlyErrorMessage,
} from '@/lib/supabase-errors';
import { useProfileSubscription } from '@/lib/realtime-subscriptions';
import type { UserProfile, UserProfileUpdate, UserError } from '@/types/user';
import { useIonToast } from '@ionic/react';

type ProfileData = {
  profile: UserProfile | null;
  isLoading: boolean;
  error: UserError | null;
  isStale: boolean;
};

type ProfileActions = {
  updateProfile: (
    data: UserProfileUpdate,
    options?: { successMessage?: string }
  ) => Promise<UserProfile>;
  refreshProfile: () => Promise<void>;
};

const profileService = {
  async fetchProfile(userId: string): Promise<UserProfile | null> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // no rows
        throw error;
      }
      return data;
    } catch (error) {
      logSupabaseError(error, 'fetchProfile');
      throw normalizeSupabaseError(error);
    }
  },

  async updateProfile(
    userId: string,
    updates: UserProfileUpdate
  ): Promise<UserProfile> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId)
        .select('*')
        .single();

      if (error) throw error;
      if (!data) throw new Error('Profile update returned no data');
      return data;
    } catch (error) {
      logSupabaseError(error, 'updateProfile');
      throw normalizeSupabaseError(error);
    }
  },
};

export function useProfile(userId?: string): ProfileData & ProfileActions {
  const queryClient = useQueryClient();
  const [showToast] = useIonToast();

  // Query
  const {
    data: profile,
    isLoading,
    error: queryError,
    isStale,
    refetch,
  } = useQuery({
    queryKey: queryKeys.profile.byId(userId!),
    queryFn: () => profileService.fetchProfile(userId!),
    enabled: Boolean(userId),
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    retry: (failureCount, error) =>
      shouldRetryError(error) && failureCount < 3,
    retryDelay: (attemptIndex) =>
      Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Mutation
  const updateProfileMutation = useMutation({
    mutationFn: (updates: UserProfileUpdate) => {
      if (!userId) throw new Error('User ID required for profile update');
      return profileService.updateProfile(userId, updates);
    },
    onMutate: async (updates) => {
      if (!userId) return;
      await queryClient.cancelQueries({
        queryKey: queryKeys.profile.byId(userId),
      });

      const previousProfile = queryClient.getQueryData<UserProfile>(
        queryKeys.profile.byId(userId)
      );

      if (previousProfile) {
        queryClient.setQueryData<UserProfile>(
          queryKeys.profile.byId(userId),
          {
            ...previousProfile,
            ...updates,
            updated_at: new Date().toISOString(),
          }
        );
      }

      return { previousProfile };
    },
    onError: async (error, _updates, context) => {
      if (userId && context?.previousProfile) {
        queryClient.setQueryData(
          queryKeys.profile.byId(userId),
          context.previousProfile
        );
      }
      logSupabaseError(error, 'updateProfileMutation');

      await showToast({
        message: getUserFriendlyErrorMessage(error),
        duration: 2000,
        color: 'danger',
      });
    },
    onSuccess: async (updatedProfile, _updates, _context) => {
      if (!userId) return;
      queryClient.setQueryData(
        queryKeys.profile.byId(userId),
        updatedProfile
      );
      queryClient.invalidateQueries({
        queryKey: queryKeys.profile.current,
      });
    },
    retry: (failureCount, error) =>
      shouldRetryError(error) && failureCount < 1,
  });

  // Realtime subscription
  useProfileSubscription(userId, Boolean(userId));

  const error = queryError ? normalizeSupabaseError(queryError) : null;

  // Expose actions
  const updateProfile = async (
    updates: UserProfileUpdate,
    options?: { successMessage?: string }
  ): Promise<UserProfile> => {
    const result = await updateProfileMutation.mutateAsync(updates);

    if (options?.successMessage) {
      await showToast({
        message: options.successMessage,
        duration: 2000,
        color: 'success',
      });
    }

    return result;
  };

  const refreshProfile = async () => {
    if (userId) await refetch();
  };

  return {
    profile: profile || null,
    isLoading: isLoading || updateProfileMutation.isPending,
    error,
    isStale,
    updateProfile,
    refreshProfile,
  };
}