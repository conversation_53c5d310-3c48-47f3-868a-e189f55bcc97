/**
 * Integration tests for useProfile hook real-time functionality
 * 
 * Tests the profile hook's real-time subscription behavior,
 * cache invalidation, and proper cleanup.
 */

import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useProfile } from '../useProfile';
import { cleanupAllSubscriptions } from '@/lib/realtime-subscriptions';

// Mock the real-time subscription hook
const mockUseProfileSubscription = vi.fn();

vi.mock('@/lib/realtime-subscriptions', () => ({
  useProfileSubscription: mockUseProfileSubscription,
  cleanupAllSubscriptions: vi.fn(),
}));

// Mock Supabase
vi.mock('@/lib/supabase', () => ({
  supabase: {
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    single: vi.fn().mockResolvedValue({
      data: {
        id: 'user-123',
        full_name: 'Test User',
        avatar_url: null,
        date_of_birth: null,
        marketing_email: true,
        marketing_notifications: false,
        privileges: ['user'],
        updated_at: '2024-01-01T00:00:00Z',
      },
      error: null,
    }),
    update: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
  },
}));

// Mock error handling
vi.mock('@/lib/supabase-errors', () => ({
  normalizeSupabaseError: vi.fn((error) => error),
  logSupabaseError: vi.fn(),
  shouldRetryError: vi.fn().mockReturnValue(true),
}));

// Test wrapper component
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useProfile Real-time Integration', () => {
  let mockSupabase: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    cleanupAllSubscriptions();
    
    // Get the mocked supabase instance
    const supabaseModule = await import('@/lib/supabase');
    mockSupabase = supabaseModule.supabase;
  });

  afterEach(() => {
    cleanupAllSubscriptions();
  });

  it('should set up profile subscription when userId is provided', async () => {
    const userId = 'user-123';

    renderHook(() => useProfile(userId), { wrapper: createWrapper() });

    await waitFor(() => {
      expect(mockUseProfileSubscription).toHaveBeenCalledWith(userId, true);
    });
  });

  it('should not set up subscription when userId is not provided', () => {
    renderHook(() => useProfile(), { wrapper: createWrapper() });

    expect(mockUseProfileSubscription).toHaveBeenCalledWith(undefined, false);
  });

  it('should update subscription when userId changes', async () => {
    const { rerender } = renderHook(
      ({ userId }) => useProfile(userId),
      { 
        wrapper: createWrapper(),
        initialProps: { userId: 'user-123' }
      }
    );

    await waitFor(() => {
      expect(mockUseProfileSubscription).toHaveBeenCalledWith('user-123', true);
    });

    // Change userId
    rerender({ userId: 'user-456' });

    await waitFor(() => {
      expect(mockUseProfileSubscription).toHaveBeenCalledWith('user-456', true);
    });
  });

  it('should disable subscription when userId becomes undefined', async () => {
    const { rerender } = renderHook(
      ({ userId }) => useProfile(userId),
      { 
        wrapper: createWrapper(),
        initialProps: { userId: 'user-123' }
      }
    );

    await waitFor(() => {
      expect(mockUseProfileSubscription).toHaveBeenCalledWith('user-123', true);
    });

    // Remove userId
    rerender({ userId: undefined });

    await waitFor(() => {
      expect(mockUseProfileSubscription).toHaveBeenCalledWith(undefined, false);
    });
  });

  it('should handle profile updates with optimistic updates', async () => {
    const queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    const setQueryDataSpy = vi.spyOn(queryClient, 'setQueryData');
    const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    );

    const { result } = renderHook(() => useProfile('user-123'), { wrapper });

    // Wait for initial data to load
    await waitFor(() => {
      expect(result.current.profile).toBeTruthy();
    });

    // Mock successful update
    mockSupabase.single.mockResolvedValueOnce({
      data: {
        id: 'user-123',
        full_name: 'Updated User',
        avatar_url: null,
        date_of_birth: null,
        marketing_email: true,
        marketing_notifications: false,
        privileges: ['user'],
        updated_at: '2024-01-01T01:00:00Z',
      },
      error: null,
    });

    // Perform update
    act(() => {
      result.current.updateProfile({ full_name: 'Updated User' });
    });

    // Should perform optimistic update
    await waitFor(() => {
      expect(setQueryDataSpy).toHaveBeenCalledWith(
        ['profile', 'user-123'],
        expect.objectContaining({
          full_name: 'Updated User',
        })
      );
    });

    // Should invalidate queries after successful update
    await waitFor(() => {
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({
        queryKey: ['profile', 'current'],
      });
      expect(invalidateQueriesSpy).toHaveBeenCalledWith({
        queryKey: ['profile', 'user-123'],
      });
    });
  });

  it('should rollback optimistic updates on error', async () => {
    const queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    const setQueryDataSpy = vi.spyOn(queryClient, 'setQueryData');

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    );

    const { result } = renderHook(() => useProfile('user-123'), { wrapper });

    // Wait for initial data to load
    await waitFor(() => {
      expect(result.current.profile).toBeTruthy();
    });

    const originalProfile = result.current.profile;

    // Mock failed update
    mockSupabase.single.mockRejectedValueOnce(new Error('Update failed'));

    // Perform update that will fail
    await act(async () => {
      try {
        await result.current.updateProfile({ full_name: 'Failed Update' });
      } catch (error) {
        // Expected to fail
      }
    });

    // Should rollback to original profile data
    await waitFor(() => {
      expect(setQueryDataSpy).toHaveBeenCalledWith(
        ['profile', 'user-123'],
        originalProfile
      );
    });
  });

  it('should handle concurrent profile updates correctly', async () => {
    const queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    const cancelQueriesSpy = vi.spyOn(queryClient, 'cancelQueries');

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    );

    const { result } = renderHook(() => useProfile('user-123'), { wrapper });

    // Wait for initial data to load
    await waitFor(() => {
      expect(result.current.profile).toBeTruthy();
    });

    // Mock successful updates
    mockSupabase.single
      .mockResolvedValueOnce({
        data: {
          id: 'user-123',
          full_name: 'Update 1',
          avatar_url: null,
          date_of_birth: null,
          marketing_email: true,
          marketing_notifications: false,
          privileges: ['user'],
          updated_at: '2024-01-01T01:00:00Z',
        },
        error: null,
      })
      .mockResolvedValueOnce({
        data: {
          id: 'user-123',
          full_name: 'Update 2',
          avatar_url: null,
          date_of_birth: null,
          marketing_email: true,
          marketing_notifications: false,
          privileges: ['user'],
          updated_at: '2024-01-01T02:00:00Z',
        },
        error: null,
      });

    // Perform concurrent updates
    act(() => {
      result.current.updateProfile({ full_name: 'Update 1' });
      result.current.updateProfile({ full_name: 'Update 2' });
    });

    // Should cancel outgoing queries for each update
    await waitFor(() => {
      expect(cancelQueriesSpy).toHaveBeenCalledWith({
        queryKey: ['profile', 'user-123'],
      });
    });
  });

  it('should refresh profile data correctly', async () => {
    const { result } = renderHook(() => useProfile('user-123'), { wrapper: createWrapper() });

    // Wait for initial data to load
    await waitFor(() => {
      expect(result.current.profile).toBeTruthy();
    });

    // Mock refreshed data
    mockSupabase.single.mockResolvedValueOnce({
      data: {
        id: 'user-123',
        full_name: 'Refreshed User',
        avatar_url: 'https://example.com/avatar.jpg',
        date_of_birth: '1990-01-01',
        marketing_email: false,
        marketing_notifications: true,
        privileges: ['user', 'premium'],
        updated_at: '2024-01-01T03:00:00Z',
      },
      error: null,
    });

    // Refresh profile
    await act(async () => {
      await result.current.refreshProfile();
    });

    // Should have updated profile data
    await waitFor(() => {
      expect(result.current.profile?.full_name).toBe('Refreshed User');
      expect(result.current.profile?.avatar_url).toBe('https://example.com/avatar.jpg');
    });
  });

  it('should handle profile not found gracefully', async () => {
    // Mock profile not found
    mockSupabase.single.mockResolvedValueOnce({
      data: null,
      error: { code: 'PGRST116', message: 'No rows returned' },
    });

    const { result } = renderHook(() => useProfile('nonexistent-user'), { wrapper: createWrapper() });

    await waitFor(() => {
      expect(result.current.profile).toBeNull();
      expect(result.current.isLoading).toBe(false);
    });

    // Should still set up subscription
    expect(mockUseProfileSubscription).toHaveBeenCalledWith('nonexistent-user', true);
  });

  it('should handle subscription status changes', async () => {
    // Mock subscription with different statuses
    mockUseProfileSubscription.mockReturnValue({
      statuses: { 'profile_changes_user-123': 'SUBSCRIBED' },
      activeCount: 1,
    });

    const { result } = renderHook(() => useProfile('user-123'), { wrapper: createWrapper() });

    await waitFor(() => {
      expect(result.current.profile).toBeTruthy();
    });

    // Simulate subscription error
    mockUseProfileSubscription.mockReturnValue({
      statuses: { 'profile_changes_user-123': 'ERROR' },
      activeCount: 0,
    });

    // Profile should still be available even if subscription fails
    expect(result.current.profile).toBeTruthy();
  });

  it('should maintain profile data consistency during real-time updates', async () => {
    const queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');

    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    );

    const { result } = renderHook(() => useProfile('user-123'), { wrapper });

    // Wait for initial data to load
    await waitFor(() => {
      expect(result.current.profile).toBeTruthy();
    });

    // Simulate real-time update by manually triggering query invalidation
    // (In real scenario, this would be triggered by the subscription)
    act(() => {
      queryClient.invalidateQueries({ queryKey: ['profile', 'user-123'] });
    });

    // Should trigger refetch
    await waitFor(() => {
      expect(mockSupabase.from).toHaveBeenCalledWith('profiles');
    });
  });
});