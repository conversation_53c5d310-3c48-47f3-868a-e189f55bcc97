import { describe, expect, it } from "vitest";
import { queryKeys } from "../../lib/query-keys";

// Simple test to verify the hook exports and types
describe("useUserProfileQuery", () => {
  it("should have proper query key structure", () => {
    const userId = "test-user-123";
    const profileKey = queryKeys.profile.byId(userId);

    expect(profileKey).toEqual(["profile", userId]);
  });

  it("should have current profile query key", () => {
    const currentKey = queryKeys.profile.current;

    expect(currentKey).toEqual(["profile", "current"]);
  });

  it("should have all profiles query key", () => {
    const allKey = queryKeys.profile.all;

    expect(allKey).toEqual(["profile"]);
  });

  it("should demonstrate type safety with UserProfile type", () => {
    // This test verifies that our types are properly imported and structured
    const mockProfileUpdate = {
      full_name: "Test User",
      marketing_email: true,
    };

    // This should compile without errors, demonstrating proper typing
    expect(typeof mockProfileUpdate.full_name).toBe("string");
    expect(typeof mockProfileUpdate.marketing_email).toBe("boolean");
  });
});
