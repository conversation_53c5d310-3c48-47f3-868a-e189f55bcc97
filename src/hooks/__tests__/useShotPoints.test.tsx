/**
 * Unit tests for useShotPoints hook
 * 
 * Tests the SHOT points management hook with mocked Supabase responses,
 * covering all functionality including XP transactions, real-time subscriptions,
 * error handling, and optimistic updates.
 */

import { renderHook, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { ReactNode } from 'react';

import { 
  useShotPoints, 
  useShotPointsBySportHeadId, 
  useShotPointsByUserId,
  useShotPointsRefresh,
  shotPointsHelpers 
} from '../useShotPoints';
import { shotPointsService } from '@/services/ShotPointsService';
import type { ShotPointsData, XpTransaction } from '@/types/user';

// Mock the Supabase client
vi.mock('@/lib/supabase', () => ({
  supabase: {
    from: vi.fn(),
    channel: vi.fn(() => ({
      on: vi.fn().mockReturnThis(),
      subscribe: vi.fn(),
    })),
    removeChannel: vi.fn(),
  },
}));

// Mock the SHOT points service
vi.mock('@/services/ShotPointsService', () => ({
  shotPointsService: {
    fetchShotPointsBySportHeadId: vi.fn(),
    fetchShotPointsByUserId: vi.fn(),
    addXpTransaction: vi.fn(),
    calculateTotalShotPoints: vi.fn(),
    calculateLevel: vi.fn(),
  },
}));

// Mock the error handling utilities
vi.mock('@/lib/supabase-errors', () => ({
  normalizeSupabaseError: vi.fn((error) => error),
  logSupabaseError: vi.fn(),
  shouldRetryError: vi.fn(() => true),
}));

// Mock query keys
vi.mock('@/lib/query-keys', () => ({
  queryKeys: {
    shotPoints: {
      all: ['shotPoints'],
      bySportHeadId: (id: string) => ['shotPoints', id],
      byUserId: (id: string) => ['shotPoints', 'user', id],
    },
    xpTransactions: {
      byPlayerId: (id: string) => ['xpTransactions', id],
    },
  },
}));

// Test data
const mockShotPointsData: ShotPointsData = {
  shotPoints: 150,
  experiencePoints: 100,
  achievementPoints: 50,
  level: 2,
  prestigeLevel: 0,
  sportHeadId: 'sport-head-123',
  userId: 'user-123',
  lastUpdated: '2024-01-01T00:00:00Z',
};

const mockXpTransaction: XpTransaction = {
  id: 'transaction-123',
  player_id: 'user-123',
  xp_amount: 25,
  source_type: 'training',
  source_id: 'training-456',
  description: 'Earned 25 XP from training',
  created_at: '2024-01-01T00:00:00Z',
};

// Test wrapper component
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useShotPoints', () => {
  let mockService: typeof shotPointsService;

  beforeEach(() => {
    mockService = shotPointsService as any;
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('useShotPointsByUserId', () => {
    it('should fetch SHOT points data successfully', async () => {
      mockService.fetchShotPointsByUserId = vi.fn().mockResolvedValue(mockShotPointsData);

      const { result } = renderHook(
        () => useShotPointsByUserId('user-123'),
        { wrapper: createWrapper() }
      );

      // Initially loading
      expect(result.current.isLoading).toBe(true);
      expect(result.current.shotPoints).toBe(null);
      expect(result.current.error).toBe(null);

      // Wait for data to load
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.shotPoints).toEqual(mockShotPointsData);
      expect(result.current.error).toBe(null);
      expect(mockService.fetchShotPointsByUserId).toHaveBeenCalledWith('user-123');
    });

    it('should handle fetch errors gracefully', async () => {
      const mockError = new Error('Failed to fetch SHOT points');
      mockService.fetchShotPointsByUserId = vi.fn().mockRejectedValue(mockError);

      const { result } = renderHook(
        () => useShotPointsByUserId('user-123'),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      }, { timeout: 3000 });

      expect(result.current.shotPoints).toBe(null);
      expect(result.current.error).toBeTruthy(); // Just check that there is an error
    });

    it('should not fetch when userId is not provided', () => {
      mockService.fetchShotPointsByUserId = vi.fn();

      renderHook(
        () => useShotPointsByUserId(undefined),
        { wrapper: createWrapper() }
      );

      expect(mockService.fetchShotPointsByUserId).not.toHaveBeenCalled();
    });

    it('should add XP transaction successfully', async () => {
      mockService.fetchShotPointsByUserId = vi.fn().mockResolvedValue(mockShotPointsData);
      mockService.addXpTransaction = vi.fn().mockResolvedValue(mockXpTransaction);
      mockService.calculateTotalShotPoints = vi.fn().mockReturnValue(175);
      mockService.calculateLevel = vi.fn().mockReturnValue(3);

      const { result } = renderHook(
        () => useShotPointsByUserId('user-123'),
        { wrapper: createWrapper() }
      );

      // Wait for initial data to load
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Add XP transaction
      await act(async () => {
        const transaction = await result.current.addXpTransaction({
          xpAmount: 25,
          sourceType: 'training',
          sourceId: 'training-456',
          description: 'Earned 25 XP from training',
        });
        expect(transaction).toEqual(mockXpTransaction);
      });

      expect(mockService.addXpTransaction).toHaveBeenCalledWith(
        'user-123',
        25,
        'training',
        'training-456',
        'Earned 25 XP from training'
      );
    });

    it('should handle XP transaction errors gracefully', async () => {
      mockService.fetchShotPointsByUserId = vi.fn().mockResolvedValue(mockShotPointsData);
      const mockError = new Error('XP transaction failed');
      mockService.addXpTransaction = vi.fn().mockRejectedValue(mockError);

      const { result } = renderHook(
        () => useShotPointsByUserId('user-123'),
        { wrapper: createWrapper() }
      );

      // Wait for initial data to load
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Try to add XP transaction
      await act(async () => {
        try {
          await result.current.addXpTransaction({
            xpAmount: 25,
            sourceType: 'training',
          });
        } catch (error) {
          expect(error).toEqual(mockError);
        }
      });
    });

    it('should refresh SHOT points data', async () => {
      mockService.fetchShotPointsByUserId = vi.fn().mockResolvedValue(mockShotPointsData);

      const { result } = renderHook(
        () => useShotPointsByUserId('user-123'),
        { wrapper: createWrapper() }
      );

      // Wait for initial data to load
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Clear the mock to track refresh calls
      vi.clearAllMocks();
      mockService.fetchShotPointsByUserId = vi.fn().mockResolvedValue({
        ...mockShotPointsData,
        shotPoints: 200,
      });

      // Refresh data
      await act(async () => {
        await result.current.refreshShotPoints();
      });

      expect(mockService.fetchShotPointsByUserId).toHaveBeenCalledWith('user-123');
    });
  });

  describe('useShotPointsBySportHeadId', () => {
    it('should fetch SHOT points data by sport head ID', async () => {
      mockService.fetchShotPointsBySportHeadId = vi.fn().mockResolvedValue(mockShotPointsData);

      const { result } = renderHook(
        () => useShotPointsBySportHeadId('sport-head-123'),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.shotPoints).toEqual(mockShotPointsData);
      expect(mockService.fetchShotPointsBySportHeadId).toHaveBeenCalledWith('sport-head-123');
    });

    it('should handle missing sport head ID', () => {
      mockService.fetchShotPointsBySportHeadId = vi.fn();

      renderHook(
        () => useShotPointsBySportHeadId(undefined),
        { wrapper: createWrapper() }
      );

      expect(mockService.fetchShotPointsBySportHeadId).not.toHaveBeenCalled();
    });
  });

  describe('useShotPoints (main hook)', () => {
    it('should use useShotPointsByUserId internally', async () => {
      mockService.fetchShotPointsByUserId = vi.fn().mockResolvedValue(mockShotPointsData);

      const { result } = renderHook(
        () => useShotPoints('user-123'),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.shotPoints).toEqual(mockShotPointsData);
      expect(mockService.fetchShotPointsByUserId).toHaveBeenCalledWith('user-123');
    });
  });

  describe('useShotPointsRefresh', () => {
    it('should provide refresh functions', () => {
      const { result } = renderHook(
        () => useShotPointsRefresh(),
        { wrapper: createWrapper() }
      );

      expect(typeof result.current.refreshAllShotPoints).toBe('function');
      expect(typeof result.current.refreshUserShotPoints).toBe('function');
      expect(typeof result.current.refreshSportHeadShotPoints).toBe('function');
    });
  });

  describe('shotPointsHelpers', () => {
    it('should provide helper functions', () => {
      expect(typeof shotPointsHelpers.calculateTotalShotPoints).toBe('function');
      expect(typeof shotPointsHelpers.calculateLevel).toBe('function');
    });
  });

  describe('optimistic updates', () => {
    it('should optimistically update SHOT points during XP transaction', async () => {
      mockService.fetchShotPointsByUserId = vi.fn().mockResolvedValue(mockShotPointsData);
      mockService.addXpTransaction = vi.fn().mockResolvedValue(mockXpTransaction);
      mockService.calculateTotalShotPoints = vi.fn().mockReturnValue(175);
      mockService.calculateLevel = vi.fn().mockReturnValue(3);

      const { result } = renderHook(
        () => useShotPointsByUserId('user-123'),
        { wrapper: createWrapper() }
      );

      // Wait for initial data to load
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Verify initial state
      expect(result.current.shotPoints).toEqual(mockShotPointsData);

      // Add XP transaction
      await act(async () => {
        await result.current.addXpTransaction({
          xpAmount: 25,
          sourceType: 'training',
        });
      });

      // Verify the service was called
      expect(mockService.addXpTransaction).toHaveBeenCalledWith(
        'user-123',
        25,
        'training',
        undefined,
        undefined
      );
    });
  });

  describe('error isolation', () => {
    it('should not throw errors from XP transactions to prevent affecting core operations', async () => {
      mockService.fetchShotPointsByUserId = vi.fn().mockResolvedValue(mockShotPointsData);
      const mockError = new Error('XP transaction failed');
      mockService.addXpTransaction = vi.fn().mockRejectedValue(mockError);

      const { result } = renderHook(
        () => useShotPointsByUserId('user-123'),
        { wrapper: createWrapper() }
      );

      // Wait for initial data to load
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // XP transaction should fail gracefully without breaking the hook
      let caughtError: Error | null = null;
      await act(async () => {
        try {
          await result.current.addXpTransaction({
            xpAmount: 25,
            sourceType: 'training',
          });
        } catch (error) {
          caughtError = error as Error;
        }
      });

      expect(caughtError).toEqual(mockError);
      // Hook should still be functional
      expect(result.current.shotPoints).toEqual(mockShotPointsData);
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('stale data handling', () => {
    it('should indicate when data is stale', async () => {
      mockService.fetchShotPointsByUserId = vi.fn().mockResolvedValue(mockShotPointsData);

      const { result } = renderHook(
        () => useShotPointsByUserId('user-123'),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Initially data should not be stale
      expect(result.current.isStale).toBe(false);
    });
  });
});