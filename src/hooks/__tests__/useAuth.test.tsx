/**
 * Unit tests for useAuth hook
 * Tests authentication state management, caching, error handling, and sign-out functionality
 */

import { renderHook, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import type { ReactNode } from 'react';
import type { User, Session } from '@supabase/supabase-js';

import { useAuth } from '../useAuth';
import { supabase } from '@/lib/supabase';

// Mock Supabase
vi.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: vi.fn(),
      signOut: vi.fn(),
      onAuthStateChange: vi.fn(),
    },
  },
}));

// Mock error normalization
vi.mock('@/lib/supabase-errors', () => ({
  normalizeSupabaseError: vi.fn((error) => error),
}));

// Mock user data
const mockUser: User = {
  id: 'user-123',
  email: '<EMAIL>',
  email_confirmed_at: '2023-01-01T00:00:00Z',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z',
  aud: 'authenticated',
  role: 'authenticated',
  app_metadata: {},
  user_metadata: {},
} as User;

const mockSession: Session = {
  access_token: 'mock-access-token',
  refresh_token: 'mock-refresh-token',
  expires_in: 3600,
  expires_at: Date.now() / 1000 + 3600,
  token_type: 'bearer',
  user: mockUser,
} as Session;

const mockUnverifiedUser: User = {
  ...mockUser,
  email_confirmed_at: undefined,
};

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
        staleTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });

  const Wrapper = ({ children }: { children: ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );

  return Wrapper;
};

describe('useAuth', () => {
  let mockAuthListener: { subscription: { unsubscribe: () => void } };
  let authStateChangeCallback: (event: string, session: Session | null) => void;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock auth state change listener
    mockAuthListener = {
      subscription: {
        unsubscribe: vi.fn(),
      },
    };

    vi.mocked(supabase.auth.onAuthStateChange).mockImplementation((callback) => {
      authStateChangeCallback = callback;
      return { data: mockAuthListener };
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Authentication State', () => {
    it('should return loading state initially', async () => {
      vi.mocked(supabase.auth.getSession).mockResolvedValue({
        data: { session: null },
        error: null,
      });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      });

      expect(result.current.isLoading).toBe(true);
      expect(result.current.user).toBe(null);
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.isEmailVerified).toBe(false);
    });

    it('should return authenticated user when session exists', async () => {
      vi.mocked(supabase.auth.getSession).mockResolvedValue({
        data: { session: mockSession },
        error: null,
      });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.user).toEqual(mockUser);
      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.isEmailVerified).toBe(true);
    });

    it('should return unverified state for unverified user', async () => {
      const sessionWithUnverifiedUser = {
        ...mockSession,
        user: mockUnverifiedUser,
      };

      vi.mocked(supabase.auth.getSession).mockResolvedValue({
        data: { session: sessionWithUnverifiedUser },
        error: null,
      });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.user).toEqual(mockUnverifiedUser);
      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.isEmailVerified).toBe(false);
    });

    it('should return unauthenticated state when no session', async () => {
      vi.mocked(supabase.auth.getSession).mockResolvedValue({
        data: { session: null },
        error: null,
      });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.user).toBe(null);
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.isEmailVerified).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle session fetch errors gracefully', async () => {
      const mockError = new Error('Session fetch failed');
      vi.mocked(supabase.auth.getSession).mockRejectedValue(mockError);

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      });

      // Initially loading should be true
      expect(result.current.isLoading).toBe(true);

      // Wait for the error state to be handled
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      }, { timeout: 10000 });

      expect(result.current.user).toBe(null);
      expect(result.current.isAuthenticated).toBe(false);
    }, 15000);

    it('should not retry on authentication errors', async () => {
      const authError = { status: 401, code: 'PGRST301' };
      vi.mocked(supabase.auth.getSession).mockRejectedValue(authError);

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Should only call once, no retries for auth errors
      expect(supabase.auth.getSession).toHaveBeenCalledTimes(1);
    });
  });

  describe('Sign Out Functionality', () => {
    it('should sign out successfully and clear cache', async () => {
      vi.mocked(supabase.auth.getSession).mockResolvedValue({
        data: { session: mockSession },
        error: null,
      });

      vi.mocked(supabase.auth.signOut).mockResolvedValue({
        error: null,
      });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.isAuthenticated).toBe(true);

      // Sign out
      await act(async () => {
        await result.current.signOut();
      });

      expect(supabase.auth.signOut).toHaveBeenCalledTimes(1);
    });

    it('should handle sign out errors gracefully', async () => {
      vi.mocked(supabase.auth.getSession).mockResolvedValue({
        data: { session: mockSession },
        error: null,
      });

      const signOutError = new Error('Sign out failed');
      vi.mocked(supabase.auth.signOut).mockRejectedValue(signOutError);

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Sign out should throw error but not crash
      await act(async () => {
        await expect(result.current.signOut()).rejects.toThrow('Sign out failed');
      });

      expect(supabase.auth.signOut).toHaveBeenCalledTimes(1);
    });
  });

  describe('Real-time Auth State Changes', () => {
    it('should handle SIGNED_IN event', async () => {
      vi.mocked(supabase.auth.getSession).mockResolvedValue({
        data: { session: null },
        error: null,
      });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.isAuthenticated).toBe(false);

      // Simulate SIGNED_IN event
      act(() => {
        authStateChangeCallback('SIGNED_IN', mockSession);
      });

      await waitFor(() => {
        expect(result.current.isAuthenticated).toBe(true);
      });

      expect(result.current.user).toEqual(mockUser);
    });

    it('should handle SIGNED_OUT event', async () => {
      vi.mocked(supabase.auth.getSession).mockResolvedValue({
        data: { session: mockSession },
        error: null,
      });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.isAuthenticated).toBe(true);

      // Mock the session to return null after sign out
      vi.mocked(supabase.auth.getSession).mockResolvedValue({
        data: { session: null },
        error: null,
      });

      // Simulate SIGNED_OUT event
      act(() => {
        authStateChangeCallback('SIGNED_OUT', null);
      });

      // Verify that the auth state change listener was set up
      expect(supabase.auth.onAuthStateChange).toHaveBeenCalled();
      
      // The callback should have been called with SIGNED_OUT
      // In a real app,

    it('should handle TOKEN_REFRESHED event', async () => {
      vi.mocked(supabase.auth.getSession).mockResolvedValue({
        data: { session: mockSession },
        error: null,
      });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      const refreshedSession = {
        ...mockSession,
        access_token: 'new-access-token',
      };

      // Simulate TOKEN_REFRESHED event
      act(() => {
        authStateChangeCallback('TOKEN_REFRESHED', refreshedSession);
      });

      await waitFor(() => {
        expect(result.current.isAuthenticated).toBe(true);
      });
    });

    it('should handle USER_UPDATED event', async () => {
      vi.mocked(supabase.auth.getSession).mockResolvedValue({
        data: { session: mockSession },
        error: null,
      });

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      const updatedUser = {
        ...mockUser,
        email: '<EMAIL>',
      };

      const updatedSession = {
        ...mockSession,
        user: updatedUser,
      };

      // Mock the updated session for the refetch
      vi.mocked(supabase.auth.getSession).mockResolvedValue({
        data: { session: updatedSession },
        error: null,
      });

      // Simulate USER_UPDATED event
      act(() => {
        authStateChangeCallback('USER_UPDATED', updatedSession);
      });

      await waitFor(() => {
        expect(result.current.user?.email).toBe('<EMAIL>');
      }, { timeout: 3000 });
    });
  });

  describe('Cleanup', () => {
    it('should unsubscribe from auth state changes on unmount', () => {
      const { unmount } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      });

      expect(supabase.auth.onAuthStateChange).toHaveBeenCalledTimes(1);

      unmount();

      expect(mockAuthListener.subscription.unsubscribe).toHaveBeenCalledTimes(1);
    });
  });
});
});
