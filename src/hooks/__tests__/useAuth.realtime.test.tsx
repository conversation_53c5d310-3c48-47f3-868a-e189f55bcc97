/**
 * Integration tests for useAuth hook real-time functionality
 *
 * Tests the authentication hook's real-time subscription behavior,
 * cache invalidation, and proper cleanup.
 */

import React from "react";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { renderHook, act, waitFor } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useAuth } from "../useAuth";
import { cleanupAllSubscriptions } from "@/lib/realtime-subscriptions";

// Mock Supabase
vi.mock("@/lib/supabase", () => ({
	supabase: {
		auth: {
			getSession: vi.fn().mockResolvedValue({
				data: { session: null },
				error: null,
			}),
			signOut: vi.fn().mockResolvedValue({ error: null }),
			onAuthStateChange: vi.fn().mockReturnValue({
				data: {
					subscription: {
						unsubscribe: vi.fn(),
					},
				},
			}),
		},
	},
}));

// Mock error handling
vi.mock("@/lib/supabase-errors", () => ({
	normalizeSupabaseError: vi.fn((error) => error),
}));

// Test wrapper component
const createWrapper = () => {
	const queryClient = new QueryClient({
		defaultOptions: {
			queries: { retry: false },
			mutations: { retry: false },
		},
	});

	return ({ children }: { children: React.ReactNode }) => (
		<QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
	);
};

describe("useAuth Real-time Integration", () => {
	let mockSupabase: any;

	beforeEach(async () => {
		vi.clearAllMocks();
		cleanupAllSubscriptions();

		// Get the mocked supabase instance
		const supabaseModule = await import("@/lib/supabase");
		mockSupabase = supabaseModule.supabase;
	});

	afterEach(() => {
		cleanupAllSubscriptions();
	});

	it("should set up auth state change listener on mount", () => {
		renderHook(() => useAuth(), { wrapper: createWrapper() });

		expect(mockSupabase.auth.onAuthStateChange).toHaveBeenCalledWith(
			expect.any(Function),
		);
	});

	it("should cleanup auth listener on unmount", () => {
		const { unmount } = renderHook(() => useAuth(), {
			wrapper: createWrapper(),
		});

		expect(mockSupabase.auth.onAuthStateChange).toHaveBeenCalled();

		unmount();

		const mockAuthListener = mockSupabase.auth.onAuthStateChange().data;
		expect(mockAuthListener.subscription.unsubscribe).toHaveBeenCalled();
	});

	it("should handle SIGNED_IN event correctly", async () => {
		const queryClient = new QueryClient({
			defaultOptions: {
				queries: { retry: false },
				mutations: { retry: false },
			},
		});

		const invalidateQueriesSpy = vi.spyOn(queryClient, "invalidateQueries");
		const setQueryDataSpy = vi.spyOn(queryClient, "setQueryData");

		const wrapper = ({ children }: { children: React.ReactNode }) => (
			<QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
		);

		renderHook(() => useAuth(), { wrapper });

		// Get the auth state change handler
		const authHandler = mockSupabase.auth.onAuthStateChange.mock.calls[0][0];

		const mockSession = {
			user: { id: "user-123", email: "<EMAIL>" },
		};

		// Simulate SIGNED_IN event
		act(() => {
			authHandler("SIGNED_IN", mockSession);
		});

		// Should update auth session data
		expect(setQueryDataSpy).toHaveBeenCalledWith(["auth", "session"], {
			user: mockSession.user,
			session: mockSession,
		});

		// Should invalidate user-specific queries
		expect(invalidateQueriesSpy).toHaveBeenCalledWith({
			queryKey: ["profile", "user-123"],
		});
		expect(invalidateQueriesSpy).toHaveBeenCalledWith({
			queryKey: ["shotPoints", "user", "user-123"],
		});
	});

	it("should handle SIGNED_OUT event correctly", async () => {
		const queryClient = new QueryClient({
			defaultOptions: {
				queries: { retry: false },
				mutations: { retry: false },
			},
		});

		const clearSpy = vi.spyOn(queryClient, "clear");

		const wrapper = ({ children }: { children: React.ReactNode }) => (
			<QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
		);

		renderHook(() => useAuth(), { wrapper });

		// Get the auth state change handler
		const authHandler = mockSupabase.auth.onAuthStateChange.mock.calls[0][0];

		// Simulate SIGNED_OUT event
		act(() => {
			authHandler("SIGNED_OUT", null);
		});

		// Should clear all cached data
		expect(clearSpy).toHaveBeenCalled();
	});

	it("should handle TOKEN_REFRESHED event correctly", async () => {
		const queryClient = new QueryClient({
			defaultOptions: {
				queries: { retry: false },
				mutations: { retry: false },
			},
		});

		const invalidateQueriesSpy = vi.spyOn(queryClient, "invalidateQueries");
		const setQueryDataSpy = vi.spyOn(queryClient, "setQueryData");

		const wrapper = ({ children }: { children: React.ReactNode }) => (
			<QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
		);

		renderHook(() => useAuth(), { wrapper });

		// Get the auth state change handler
		const authHandler = mockSupabase.auth.onAuthStateChange.mock.calls[0][0];

		const mockSession = {
			user: { id: "user-123", email: "<EMAIL>" },
		};

		// Simulate TOKEN_REFRESHED event
		act(() => {
			authHandler("TOKEN_REFRESHED", mockSession);
		});

		// Should update auth session data
		expect(setQueryDataSpy).toHaveBeenCalledWith(["auth", "session"], {
			user: mockSession.user,
			session: mockSession,
		});

		// Should invalidate user-specific queries
		expect(invalidateQueriesSpy).toHaveBeenCalledWith({
			queryKey: ["profile", "user-123"],
		});
		expect(invalidateQueriesSpy).toHaveBeenCalledWith({
			queryKey: ["shotPoints", "user", "user-123"],
		});
	});

	it("should handle USER_UPDATED event correctly", async () => {
		const queryClient = new QueryClient({
			defaultOptions: {
				queries: { retry: false },
				mutations: { retry: false },
			},
		});

		const invalidateQueriesSpy = vi.spyOn(queryClient, "invalidateQueries");

		const wrapper = ({ children }: { children: React.ReactNode }) => (
			<QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
		);

		renderHook(() => useAuth(), { wrapper });

		// Get the auth state change handler
		const authHandler = mockSupabase.auth.onAuthStateChange.mock.calls[0][0];

		const mockSession = {
			user: { id: "user-123", email: "<EMAIL>" },
		};

		// Simulate USER_UPDATED event
		act(() => {
			authHandler("USER_UPDATED", mockSession);
		});

		// Should invalidate auth session query
		expect(invalidateQueriesSpy).toHaveBeenCalledWith({
			queryKey: ["auth", "session"],
		});
	});

	it("should handle auth events without user ID gracefully", async () => {
		const queryClient = new QueryClient({
			defaultOptions: {
				queries: { retry: false },
				mutations: { retry: false },
			},
		});

		const invalidateQueriesSpy = vi.spyOn(queryClient, "invalidateQueries");
		const setQueryDataSpy = vi.spyOn(queryClient, "setQueryData");

		const wrapper = ({ children }: { children: React.ReactNode }) => (
			<QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
		);

		renderHook(() => useAuth(), { wrapper });

		// Get the auth state change handler
		const authHandler = mockSupabase.auth.onAuthStateChange.mock.calls[0][0];

		const mockSessionWithoutUser = {
			user: null,
		};

		// Simulate SIGNED_IN event without user
		act(() => {
			authHandler("SIGNED_IN", mockSessionWithoutUser);
		});

		// Should still update auth session data
		expect(setQueryDataSpy).toHaveBeenCalledWith(["auth", "session"], {
			user: null,
			session: mockSessionWithoutUser,
		});

		// Should not try to invalidate user-specific queries
		expect(invalidateQueriesSpy).not.toHaveBeenCalledWith({
			queryKey: expect.arrayContaining(["profile"]),
		});
		expect(invalidateQueriesSpy).not.toHaveBeenCalledWith({
			queryKey: expect.arrayContaining(["shotPoints"]),
		});
	});

	it("should log auth state changes for debugging", async () => {
		const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {});

		renderHook(() => useAuth(), { wrapper: createWrapper() });

		// Get the auth state change handler
		const authHandler = mockSupabase.auth.onAuthStateChange.mock.calls[0][0];

		const mockSession = {
			user: { id: "user-123", email: "<EMAIL>" },
		};

		// Simulate auth event
		act(() => {
			authHandler("SIGNED_IN", mockSession);
		});

		expect(consoleSpy).toHaveBeenCalledWith(
			"🔐 Auth state changed:",
			"SIGNED_IN",
			"user-123",
		);

		consoleSpy.mockRestore();
	});

	it("should maintain auth state consistency across multiple events", async () => {
		const queryClient = new QueryClient({
			defaultOptions: {
				queries: { retry: false },
				mutations: { retry: false },
			},
		});

		const setQueryDataSpy = vi.spyOn(queryClient, "setQueryData");

		const wrapper = ({ children }: { children: React.ReactNode }) => (
			<QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
		);

		renderHook(() => useAuth(), { wrapper });

		// Get the auth state change handler
		const authHandler = mockSupabase.auth.onAuthStateChange.mock.calls[0][0];

		const mockSession1 = {
			user: { id: "user-123", email: "<EMAIL>" },
		};

		const mockSession2 = {
			user: { id: "user-123", email: "<EMAIL>" },
		};

		// Simulate sequence of auth events
		act(() => {
			authHandler("SIGNED_IN", mockSession1);
		});

		act(() => {
			authHandler("USER_UPDATED", mockSession2);
		});

		act(() => {
			authHandler("TOKEN_REFRESHED", mockSession2);
		});

		act(() => {
			authHandler("SIGNED_OUT", null);
		});

		// Should have updated auth data for each event
		expect(setQueryDataSpy).toHaveBeenCalledWith(["auth", "session"], {
			user: mockSession1.user,
			session: mockSession1,
		});

		expect(setQueryDataSpy).toHaveBeenCalledWith(["auth", "session"], {
			user: mockSession2.user,
			session: mockSession2,
		});

		expect(setQueryDataSpy).toHaveBeenCalledWith(["auth", "session"], {
			user: null,
			session: null,
		});
	});
});
