import { renderHook } from '@testing-library/react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import { useProfileAvatar } from '../useProfileAvatar';

// Mock Ionic React components
vi.mock('@ionic/react', () => ({
  IonIcon: ({ icon, className }: { icon: string; className: string }) => (
    <div data-testid="ion-icon" data-icon={icon} className={className} />
  ),
}));

// Mock ionicons
vi.mock('ionicons/icons', () => ({
  personCircleOutline: 'person-circle-outline',
}));

describe('useProfileAvatar', () => {
  it('should return memoized avatar element generator', () => {
    const { result, rerender } = renderHook(
      ({ avatarUrl }) => useProfileAvatar(avatarUrl),
      { initialProps: { avatarUrl: undefined } }
    );

    const firstResult = result.current;
    
    // Re-render with same props should return same reference
    rerender({ avatarUrl: undefined });
    expect(result.current).toBe(firstResult);
  });

  it('should return new reference when avatarUrl changes', () => {
    const { result, rerender } = renderHook(
      ({ avatarUrl }) => useProfileAvatar(avatarUrl),
      { initialProps: { avatarUrl: undefined } }
    );

    const firstResult = result.current;
    
    // Re-render with different avatarUrl should return new reference
    rerender({ avatarUrl: 'https://example.com/avatar.jpg' });
    expect(result.current).not.toBe(firstResult);
  });

  it('should render default icon when no avatar URL is provided', () => {
    const { result } = renderHook(() => useProfileAvatar());
    const avatarElement = result.current.getAvatarElement();

    render(<div>{avatarElement}</div>);

    expect(screen.getByTestId('ion-icon')).toBeInTheDocument();
    expect(screen.getByTestId('ion-icon')).toHaveAttribute('data-icon', 'person-circle-outline');
    expect(screen.getByTestId('ion-icon')).toHaveClass('text-4xl', 'text-white');
  });

  it('should render image when avatar URL is provided', () => {
    const avatarUrl = 'https://example.com/avatar.jpg';
    const { result } = renderHook(() => useProfileAvatar(avatarUrl));
    const avatarElement = result.current.getAvatarElement();

    render(<div>{avatarElement}</div>);

    const image = screen.getByRole('img');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', avatarUrl);
    expect(image).toHaveAttribute('alt', 'Profile');
    expect(image).toHaveClass('w-10', 'h-10', 'rounded-full', 'object-cover');
  });

  it('should handle click events properly', () => {
    const mockOnClick = vi.fn();
    const { result } = renderHook(() => useProfileAvatar());
    const avatarElement = result.current.getAvatarElement(mockOnClick);

    render(<div>{avatarElement}</div>);

    const button = screen.getByRole('button');
    fireEvent.click(button);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it('should have proper accessibility attributes', () => {
    const { result } = renderHook(() => useProfileAvatar());
    const avatarElement = result.current.getAvatarElement();

    render(<div>{avatarElement}</div>);

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-label', 'User profile avatar');
    expect(button).toHaveAttribute('type', 'button');
    expect(button).toHaveClass('focus:outline-none', 'focus:ring-2', 'focus:ring-white');
  });

  it('should work without onClick handler', () => {
    const { result } = renderHook(() => useProfileAvatar());
    const avatarElement = result.current.getAvatarElement();

    render(<div>{avatarElement}</div>);

    const button = screen.getByRole('button');
    expect(() => fireEvent.click(button)).not.toThrow();
  });

  it('should maintain consistent styling for both image and icon variants', () => {
    // Test with icon
    const { result: iconResult } = renderHook(() => useProfileAvatar());
    const iconElement = iconResult.current.getAvatarElement();
    render(<div data-testid="icon-container">{iconElement}</div>);

    const iconButton = screen.getByRole('button');
    expect(iconButton).toHaveClass('w-10', 'h-10', 'rounded-full');

    // Test with image
    const { result: imageResult } = renderHook(() => useProfileAvatar('https://example.com/avatar.jpg'));
    const imageElement = imageResult.current.getAvatarElement();
    render(<div data-testid="image-container">{imageElement}</div>);

    const imageButton = screen.getAllByRole('button')[1]; // Second button
    expect(imageButton).toHaveClass('w-10', 'h-10', 'rounded-full');
  });
});