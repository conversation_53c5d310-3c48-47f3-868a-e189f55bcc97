import { renderHook } from '@testing-library/react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import { useIconUtilities } from '../useIconUtilities';

// Mock Ionic React components
vi.mock('@ionic/react', () => ({
  IonButton: ({ onClick, className, fill, children, 'aria-label': ariaLabel }: any) => (
    <button
      onClick={onClick}
      className={className}
      data-fill={fill}
      aria-label={ariaLabel}
    >
      {children}
    </button>
  ),
  IonIcon: ({ icon, slot, className }: { icon: string; slot: string; className: string }) => (
    <div data-testid="ion-icon" data-icon={icon} data-slot={slot} className={className} />
  ),
}));

// Mock ionicons
vi.mock('ionicons/icons', () => ({
  filterOutline: 'filter-outline',
  qrCodeOutline: 'qr-code-outline',
}));

describe('useIconUtilities', () => {
  it('should return memoized icon utility functions', () => {
    const { result, rerender } = renderHook(() => useIconUtilities());

    const firstResult = result.current;
    
    // Re-render should return same reference (memoized)
    rerender();
    expect(result.current).toBe(firstResult);
    expect(result.current.getFilterIcon).toBe(firstResult.getFilterIcon);
    expect(result.current.getQRCodeIcon).toBe(firstResult.getQRCodeIcon);
  });

  describe('getFilterIcon', () => {
    it('should render filter icon button with proper attributes', () => {
      const { result } = renderHook(() => useIconUtilities());
      const filterIcon = result.current.getFilterIcon();

      render(<div>{filterIcon}</div>);

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('aria-label', 'Filter options');
      expect(button).toHaveAttribute('data-fill', 'clear');
      expect(button).toHaveClass('icon-button');

      const icon = screen.getByTestId('ion-icon');
      expect(icon).toHaveAttribute('data-icon', 'filter-outline');
      expect(icon).toHaveAttribute('data-slot', 'icon-only');
      expect(icon).toHaveClass('text-white');
    });

    it('should handle click events properly', () => {
      const mockOnClick = vi.fn();
      const { result } = renderHook(() => useIconUtilities());
      const filterIcon = result.current.getFilterIcon(mockOnClick);

      render(<div>{filterIcon}</div>);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it('should work without onClick handler', () => {
      const { result } = renderHook(() => useIconUtilities());
      const filterIcon = result.current.getFilterIcon();

      render(<div>{filterIcon}</div>);

      const button = screen.getByRole('button');
      expect(() => fireEvent.click(button)).not.toThrow();
    });
  });

  describe('getQRCodeIcon', () => {
    it('should render QR code icon button with proper attributes', () => {
      const { result } = renderHook(() => useIconUtilities());
      const qrIcon = result.current.getQRCodeIcon();

      render(<div>{qrIcon}</div>);

      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('aria-label', 'QR code');
      expect(button).toHaveAttribute('data-fill', 'clear');
      expect(button).toHaveClass('icon-button');

      const icon = screen.getByTestId('ion-icon');
      expect(icon).toHaveAttribute('data-icon', 'qr-code-outline');
      expect(icon).toHaveAttribute('data-slot', 'icon-only');
      expect(icon).toHaveClass('text-white');
    });

    it('should handle click events properly', () => {
      const mockOnClick = vi.fn();
      const { result } = renderHook(() => useIconUtilities());
      const qrIcon = result.current.getQRCodeIcon(mockOnClick);

      render(<div>{qrIcon}</div>);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it('should work without onClick handler', () => {
      const { result } = renderHook(() => useIconUtilities());
      const qrIcon = result.current.getQRCodeIcon();

      render(<div>{qrIcon}</div>);

      const button = screen.getByRole('button');
      expect(() => fireEvent.click(button)).not.toThrow();
    });
  });

  it('should maintain consistent styling between filter and QR code icons', () => {
    const { result } = renderHook(() => useIconUtilities());
    
    const filterIcon = result.current.getFilterIcon();
    const qrIcon = result.current.getQRCodeIcon();

    render(
      <div>
        <div data-testid="filter-container">{filterIcon}</div>
        <div data-testid="qr-container">{qrIcon}</div>
      </div>
    );

    const buttons = screen.getAllByRole('button');
    const icons = screen.getAllByTestId('ion-icon');

    // Both buttons should have consistent styling
    buttons.forEach(button => {
      expect(button).toHaveClass('icon-button');
      expect(button).toHaveAttribute('data-fill', 'clear');
    });

    // Both icons should have consistent styling
    icons.forEach(icon => {
      expect(icon).toHaveAttribute('data-slot', 'icon-only');
      expect(icon).toHaveClass('text-white');
    });
  });

  it('should not cause unnecessary re-renders', () => {
    const { result, rerender } = renderHook(() => useIconUtilities());

    const firstResult = result.current;

    // Multiple re-renders should return the same object reference (memoized)
    rerender();
    rerender();
    rerender();

    expect(result.current).toBe(firstResult);
  });
});