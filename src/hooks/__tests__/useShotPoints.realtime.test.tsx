/**
 * Integration tests for useShotPoints hook real-time functionality
 * 
 * Tests the SHOT points hook's real-time subscription behavior,
 * cache invalidation, and proper cleanup with error isolation.
 */

import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useShotPointsBySportHeadId, useShotPointsByUserId } from '../useShotPoints';
import { cleanupAllSubscriptions } from '@/lib/realtime-subscriptions';

// Mock the real-time subscription hook
const mockUseShotPointsSubscription = vi.fn();

vi.mock('@/lib/realtime-subscriptions', () => ({
  useShotPointsSubscription: mockUseShotPointsSubscription,
  cleanupAllSubscriptions: vi.fn(),
}));

// Mock SHOT points service
const mockShotPointsService = {
  fetchShotPointsBySportHeadId: vi.fn().mockResolvedValue({
    shotPoints: 150,
    experiencePoints: 100,
    achievementPoints: 50,
    level: 3,
    prestigeLevel: 0,
    sportHeadId: 'sport-head-123',
    userId: 'user-123',
    lastUpdated: '2024-01-01T00:00:00Z',
  }),
  fetchShotPointsByUserId: vi.fn().mockResolvedValue({
    shotPoints: 150,
    experiencePoints: 100,
    achievementPoints: 50,
    level: 3,
    prestigeLevel: 0,
    sportHeadId: 'sport-head-123',
    userId: 'user-123',
    lastUpdated: '2024-01-01T00:00:00Z',
  }),
  addXpTransaction: vi.fn().mockResolvedValue({
    id: 'transaction-123',
    player_id: 'user-123',
    xp_amount: 25,
    source_type: 'training',
    source_id: 'training-123',
    description: 'Completed training session',
    created_at: '2024-01-01T01:00:00Z',
  }),
  calculateTotalShotPoints: vi.fn((xp, achievement) => xp + achievement),
  calculateLevel: vi.fn((xp) => Math.floor(xp / 50) + 1),
};

vi.mock('@/services/ShotPointsService', () => ({
  shotPointsService: mockShotPointsService,
}));

// Mock Supabase
vi.mock('@/lib/supabase', () => ({
  supabase: {
    from: vi.fn().mockReturnThis(),
    select: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    single: vi.fn(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
  },
}));

// Mock error handling
vi.mock('@/lib/supabase-errors', () => ({
  normalizeSupabaseError: vi.fn((error) => error),
  logSupabaseError: vi.fn(),
  shouldRetryError: vi.fn().mockReturnValue(true),
}));

// Test wrapper component
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useShotPoints Real-time Integration', () => {
  let mockSupabase: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    cleanupAllSubscriptions();
    
    // Get the mocked supabase instance
    const supabaseModule = await import('@/lib/supabase');
    mockSupabase = supabaseModule.supabase;
  });

  afterEach(() => {
    cleanupAllSubscriptions();
  });

  describe('useShotPointsBySportHeadId', () => {
    it('should set up SHOT points subscription when sportHeadId is provided', async () => {
      const sportHeadId = 'sport-head-123';

      const { result } = renderHook(
        () => useShotPointsBySportHeadId(sportHeadId), 
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.shotPoints).toBeTruthy();
      });

      expect(mockUseShotPointsSubscription).toHaveBeenCalledWith(
        sportHeadId, 
        'user-123', // userId from the fetched data
        true
      );
    });

    it('should not set up subscription when sportHeadId is not provided', () => {
      renderHook(
        () => useShotPointsBySportHeadId(), 
        { wrapper: createWrapper() }
      );

      expect(mockUseShotPointsSubscription).toHaveBeenCalledWith(
        undefined, 
        undefined, 
        false
      );
    });

    it('should handle XP transactions with optimistic updates', async () => {
      const queryClient = new QueryClient({
        defaultOptions: {
          queries: { retry: false },
          mutations: { retry: false },
        },
      });

      const setQueryDataSpy = vi.spyOn(queryClient, 'setQueryData');
      const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useShotPointsBySportHeadId('sport-head-123'), 
        { wrapper }
      );

      // Wait for initial data to load
      await waitFor(() => {
        expect(result.current.shotPoints).toBeTruthy();
      });

      // Perform XP transaction
      await act(async () => {
        await result.current.addXpTransaction({
          xpAmount: 25,
          sourceType: 'training',
          sourceId: 'training-123',
          description: 'Completed training session',
        });
      });

      // Should perform optimistic update
      expect(setQueryDataSpy).toHaveBeenCalledWith(
        ['shotPoints', 'sport-head-123'],
        expect.objectContaining({
          experiencePoints: 125, // 100 + 25
          shotPoints: 175, // 125 + 50
          level: 3, // calculated level
        })
      );

      // Should invalidate queries after successful transaction
      await waitFor(() => {
        expect(invalidateQueriesSpy).toHaveBeenCalledWith({
          queryKey: ['shotPoints', 'sport-head-123'],
        });
        expect(invalidateQueriesSpy).toHaveBeenCalledWith({
          queryKey: ['shotPoints', 'user', 'user-123'],
        });
        expect(invalidateQueriesSpy).toHaveBeenCalledWith({
          queryKey: ['xpTransactions', 'user-123'],
        });
      });
    });

    it('should rollback optimistic updates on XP transaction error', async () => {
      const queryClient = new QueryClient({
        defaultOptions: {
          queries: { retry: false },
          mutations: { retry: false },
        },
      });

      const setQueryDataSpy = vi.spyOn(queryClient, 'setQueryData');
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useShotPointsBySportHeadId('sport-head-123'), 
        { wrapper }
      );

      // Wait for initial data to load
      await waitFor(() => {
        expect(result.current.shotPoints).toBeTruthy();
      });

      const originalShotPoints = result.current.shotPoints;

      // Mock failed XP transaction
      mockShotPointsService.addXpTransaction.mockRejectedValueOnce(
        new Error('Transaction failed')
      );

      // Perform XP transaction that will fail
      await act(async () => {
        try {
          await result.current.addXpTransaction({
            xpAmount: 25,
            sourceType: 'training',
            sourceId: 'training-123',
            description: 'Failed transaction',
          });
        } catch (error) {
          // Expected to fail but should not break the app
        }
      });

      // Should rollback to original data
      expect(setQueryDataSpy).toHaveBeenCalledWith(
        ['shotPoints', 'sport-head-123'],
        originalShotPoints
      );

      // Should log error but not throw
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('XP transaction failed'),
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });

    it('should isolate SHOT points errors from core operations', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Mock service to throw error
      mockShotPointsService.fetchShotPointsBySportHeadId.mockRejectedValueOnce(
        new Error('SHOT points service unavailable')
      );

      const { result } = renderHook(
        () => useShotPointsBySportHeadId('sport-head-123'), 
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.error).toBeTruthy();
        expect(result.current.shotPoints).toBeNull();
      });

      // Error should be isolated - app should continue to work
      expect(result.current.isLoading).toBe(false);
      
      // Should still attempt to set up subscription (even with error)
      expect(mockUseShotPointsSubscription).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('useShotPointsByUserId', () => {
    it('should set up SHOT points subscription when userId is provided', async () => {
      const userId = 'user-123';

      const { result } = renderHook(
        () => useShotPointsByUserId(userId), 
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.shotPoints).toBeTruthy();
      });

      expect(mockUseShotPointsSubscription).toHaveBeenCalledWith(
        'sport-head-123', // sportHeadId from the fetched data
        userId,
        true
      );
    });

    it('should handle user without sport head gracefully', async () => {
      // Mock user with no sport head
      mockShotPointsService.fetchShotPointsByUserId.mockResolvedValueOnce(null);

      const { result } = renderHook(
        () => useShotPointsByUserId('user-without-sport-head'), 
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.shotPoints).toBeNull();
        expect(result.current.isLoading).toBe(false);
      });

      // Should still attempt to set up subscription
      expect(mockUseShotPointsSubscription).toHaveBeenCalledWith(
        undefined, // no sportHeadId
        'user-without-sport-head',
        true
      );
    });

    it('should update subscription when userId changes', async () => {
      const { rerender } = renderHook(
        ({ userId }) => useShotPointsByUserId(userId),
        { 
          wrapper: createWrapper(),
          initialProps: { userId: 'user-123' }
        }
      );

      await waitFor(() => {
        expect(mockUseShotPointsSubscription).toHaveBeenCalledWith(
          'sport-head-123',
          'user-123',
          true
        );
      });

      // Mock different user data
      mockShotPointsService.fetchShotPointsByUserId.mockResolvedValueOnce({
        shotPoints: 200,
        experiencePoints: 150,
        achievementPoints: 50,
        level: 4,
        prestigeLevel: 0,
        sportHeadId: 'sport-head-456',
        userId: 'user-456',
        lastUpdated: '2024-01-01T00:00:00Z',
      });

      // Change userId
      rerender({ userId: 'user-456' });

      await waitFor(() => {
        expect(mockUseShotPointsSubscription).toHaveBeenCalledWith(
          'sport-head-456',
          'user-456',
          true
        );
      });
    });
  });

  describe('Real-time Subscription Integration', () => {
    it('should handle subscription status changes gracefully', async () => {
      // Mock subscription with different statuses
      mockUseShotPointsSubscription.mockReturnValue({
        statuses: { 
          'shot_points_changes_sport-head-123': 'SUBSCRIBED',
          'xp_transactions_user-123': 'SUBSCRIBED'
        },
        activeCount: 2,
      });

      const { result } = renderHook(
        () => useShotPointsBySportHeadId('sport-head-123'), 
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.shotPoints).toBeTruthy();
      });

      // Simulate subscription error
      mockUseShotPointsSubscription.mockReturnValue({
        statuses: { 
          'shot_points_changes_sport-head-123': 'ERROR',
          'xp_transactions_user-123': 'ERROR'
        },
        activeCount: 0,
      });

      // SHOT points should still be available even if subscription fails
      expect(result.current.shotPoints).toBeTruthy();
    });

    it('should maintain data consistency during real-time updates', async () => {
      const queryClient = new QueryClient({
        defaultOptions: {
          queries: { retry: false },
          mutations: { retry: false },
        },
      });

      const invalidateQueriesSpy = vi.spyOn(queryClient, 'invalidateQueries');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useShotPointsBySportHeadId('sport-head-123'), 
        { wrapper }
      );

      // Wait for initial data to load
      await waitFor(() => {
        expect(result.current.shotPoints).toBeTruthy();
      });

      // Simulate real-time update by manually triggering query invalidation
      // (In real scenario, this would be triggered by the subscription)
      act(() => {
        queryClient.invalidateQueries({ queryKey: ['shotPoints', 'sport-head-123'] });
      });

      // Should trigger refetch
      await waitFor(() => {
        expect(mockShotPointsService.fetchShotPointsBySportHeadId).toHaveBeenCalledWith('sport-head-123');
      });
    });

    it('should handle concurrent XP transactions correctly', async () => {
      const queryClient = new QueryClient({
        defaultOptions: {
          queries: { retry: false },
          mutations: { retry: false },
        },
      });

      const cancelQueriesSpy = vi.spyOn(queryClient, 'cancelQueries');

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>
          {children}
        </QueryClientProvider>
      );

      const { result } = renderHook(
        () => useShotPointsBySportHeadId('sport-head-123'), 
        { wrapper }
      );

      // Wait for initial data to load
      await waitFor(() => {
        expect(result.current.shotPoints).toBeTruthy();
      });

      // Mock successful transactions
      mockShotPointsService.addXpTransaction
        .mockResolvedValueOnce({
          id: 'transaction-1',
          player_id: 'user-123',
          xp_amount: 25,
          source_type: 'training',
          created_at: '2024-01-01T01:00:00Z',
        })
        .mockResolvedValueOnce({
          id: 'transaction-2',
          player_id: 'user-123',
          xp_amount: 15,
          source_type: 'achievement',
          created_at: '2024-01-01T01:01:00Z',
        });

      // Perform concurrent transactions
      act(() => {
        result.current.addXpTransaction({
          xpAmount: 25,
          sourceType: 'training',
          sourceId: 'training-123',
        });
        result.current.addXpTransaction({
          xpAmount: 15,
          sourceType: 'achievement',
          sourceId: 'achievement-456',
        });
      });

      // Should cancel outgoing queries for each transaction
      await waitFor(() => {
        expect(cancelQueriesSpy).toHaveBeenCalledWith({
          queryKey: ['shotPoints', 'sport-head-123'],
        });
      });
    });

    it('should refresh SHOT points data correctly', async () => {
      const { result } = renderHook(
        () => useShotPointsBySportHeadId('sport-head-123'), 
        { wrapper: createWrapper() }
      );

      // Wait for initial data to load
      await waitFor(() => {
        expect(result.current.shotPoints).toBeTruthy();
      });

      // Mock refreshed data
      mockShotPointsService.fetchShotPointsBySportHeadId.mockResolvedValueOnce({
        shotPoints: 300,
        experiencePoints: 200,
        achievementPoints: 100,
        level: 5,
        prestigeLevel: 1,
        sportHeadId: 'sport-head-123',
        userId: 'user-123',
        lastUpdated: '2024-01-01T03:00:00Z',
      });

      // Refresh SHOT points
      await act(async () => {
        await result.current.refreshShotPoints();
      });

      // Should have updated SHOT points data
      await waitFor(() => {
        expect(result.current.shotPoints?.shotPoints).toBe(300);
        expect(result.current.shotPoints?.level).toBe(5);
        expect(result.current.shotPoints?.prestigeLevel).toBe(1);
      });
    });
  });

  describe('Error Isolation', () => {
    it('should not affect core app functionality when SHOT points fail', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Mock complete SHOT points failure
      mockShotPointsService.fetchShotPointsBySportHeadId.mockRejectedValue(
        new Error('SHOT points system down')
      );

      const { result } = renderHook(
        () => useShotPointsBySportHeadId('sport-head-123'), 
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.error).toBeTruthy();
        expect(result.current.shotPoints).toBeNull();
        expect(result.current.isLoading).toBe(false);
      });

      // App should continue to function
      expect(result.current.refreshShotPoints).toBeDefined();
      expect(result.current.addXpTransaction).toBeDefined();

      // Should still set up subscription (for when service recovers)
      expect(mockUseShotPointsSubscription).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });

    it('should handle XP transaction failures gracefully without breaking app', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(
        () => useShotPointsBySportHeadId('sport-head-123'), 
        { wrapper: createWrapper() }
      );

      // Wait for initial data to load
      await waitFor(() => {
        expect(result.current.shotPoints).toBeTruthy();
      });

      // Mock XP transaction failure
      mockShotPointsService.addXpTransaction.mockRejectedValue(
        new Error('XP service unavailable')
      );

      // Attempt XP transaction
      let transactionError;
      await act(async () => {
        try {
          await result.current.addXpTransaction({
            xpAmount: 25,
            sourceType: 'training',
            sourceId: 'training-123',
          });
        } catch (error) {
          transactionError = error;
        }
      });

      // Should throw error but not break the app
      expect(transactionError).toBeTruthy();
      expect(result.current.shotPoints).toBeTruthy(); // Data still available
      expect(result.current.isLoading).toBe(false); // Not stuck in loading state

      // Should log error for debugging
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('XP transaction failed'),
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });
});