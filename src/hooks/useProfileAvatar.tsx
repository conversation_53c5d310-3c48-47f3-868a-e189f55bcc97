import { useMemo } from 'react';
import { IonIcon } from '@ionic/react';
import { personCircleOutline } from 'ionicons/icons';

/**
 * A custom hook that provides memoized avatar rendering functionality.
 *
 * @param avatarUrl - The URL of the user's avatar image (optional).
 * @returns An object containing the avatar element generator function.
 *
 * @example
 * ```ts
 * const { getAvatarElement } = useProfileAvatar(profile?.avatar_url);
 * const avatarElement = getAvatarElement(() => setShowModal(true));
 * ```
 */
export function useProfileAvatar(avatarUrl?: string) {
  return useMemo(() => ({
    /**
     * Generates a clickable avatar element with proper accessibility.
     *
     * @param onClick - Optional click handler for the avatar.
     * @returns A React element representing the user's avatar.
     */
    getAvatarElement: (onClick?: () => void) => (
      <button
        type="button"
        onClick={onClick}
        className="w-10 h-10 flex justify-center items-center cursor-pointer rounded-full focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
        aria-label="User profile avatar"
      >
        {avatarUrl ? (
          <img
            src={avatarUrl}
            alt="Profile"
            className="w-10 h-10 rounded-full object-cover"
          />
        ) : (
          <IonIcon
            icon={personCircleOutline}
            className="text-4xl text-white"
          />
        )}
      </button>
    ),
  }), [avatarUrl]);
}