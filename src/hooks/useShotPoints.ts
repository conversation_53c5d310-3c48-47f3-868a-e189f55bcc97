/**
 * SHOT Points Management Hook
 * 
 * Provides React Query-based SHOT points data fetching, caching, and mutations
 * with optimistic updates, error handling, and real-time subscriptions.
 * 
 * Features:
 * - Automatic caching and background updates
 * - XP transaction functionality with proper error isolation
 * - Real-time subscriptions for SHOT points updates
 * - Comprehensive error handling with retry logic
 * - Type-safe operations using Supabase generated types
 * - Isolated from core user operations to prevent cascading failures
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { queryKeys } from '@/lib/query-keys';
import { normalizeSupabaseError, logSupabaseError, shouldRetryError } from '@/lib/supabase-errors';
import { shotPointsService } from '@/services/ShotPointsService';
import { useShotPointsSubscription } from '@/lib/realtime-subscriptions';
import type { 
  ShotPointsData, 
  XpTransaction, 
  ShotPointsUpdatePayload, 
  UserError 
} from '@/types/user';

/**
 * SHOT Points data with loading and error states
 */
type ShotPointsHookData = {
  shotPoints: ShotPointsData | null;
  isLoading: boolean;
  error: UserError | null;
  isStale: boolean;
};

/**
 * SHOT Points mutation actions
 */
type ShotPointsActions = {
  addXpTransaction: (payload: ShotPointsUpdatePayload) => Promise<XpTransaction>;
  refreshShotPoints: () => Promise<void>;
};

/**
 * Hook for managing SHOT points data by sport head ID
 * 
 * This hook provides isolated SHOT points management that won't affect core user operations
 * if it fails. It includes automatic caching, real-time updates, and optimistic mutations.
 * 
 * @param sportHeadId - The sport head ID to fetch SHOT points for
 * @returns SHOT points data, loading states, error states, and mutation functions
 * 
 * @example
 * ```tsx
 * const { shotPoints, isLoading, error, addXpTransaction } = useShotPointsBySportHeadId('sport-head-123');
 * 
 * // Add XP transaction with error isolation
 * const handleAddXp = async () => {
 *   try {
 *     await addXpTransaction({
 *       xpAmount: 25,
 *       sourceType: 'training',
 *       description: 'Completed training session'
 *     });
 *   } catch (error) {
 *     // SHOT points failures don't break the app
 *     console.error('XP transaction failed:', error);
 *   }
 * };
 * ```
 */
export function useShotPointsBySportHeadId(sportHeadId?: string): ShotPointsHookData & ShotPointsActions {
  const queryClient = useQueryClient();

  // SHOT points query with React Query
  const {
    data: shotPoints,
    isLoading,
    error: queryError,
    isStale,
    refetch,
  } = useQuery({
    queryKey: queryKeys.shotPoints.bySportHeadId(sportHeadId!),
    queryFn: () => shotPointsService.fetchShotPointsBySportHeadId(sportHeadId!),
    enabled: !!sportHeadId, // Only run query if sportHeadId is provided
    staleTime: 2 * 60 * 1000, // 2 minutes (shorter than profile for more frequent updates)
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      // Don't retry auth errors or validation errors
      if (!shouldRetryError(error)) {
        return false;
      }
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // XP transaction mutation with optimistic updates
  const addXpTransactionMutation = useMutation({
    mutationFn: async (payload: ShotPointsUpdatePayload) => {
      if (!sportHeadId) {
        throw new Error('Sport head ID is required for XP transaction');
      }

      // Get the user ID from the current SHOT points data
      const userId = shotPoints?.userId;
      if (!userId) {
        throw new Error('User ID not found in SHOT points data');
      }

      return shotPointsService.addXpTransaction(
        userId,
        payload.xpAmount,
        payload.sourceType,
        payload.sourceId,
        payload.description
      );
    },
    onMutate: async (payload: ShotPointsUpdatePayload) => {
      if (!sportHeadId) return;

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.shotPoints.bySportHeadId(sportHeadId) });

      // Snapshot the previous value
      const previousShotPoints = queryClient.getQueryData<ShotPointsData>(
        queryKeys.shotPoints.bySportHeadId(sportHeadId)
      );

      // Optimistically update the cache
      if (previousShotPoints) {
        const newExperiencePoints = previousShotPoints.experiencePoints + payload.xpAmount;
        const newShotPoints = shotPointsService.calculateTotalShotPoints(
          newExperiencePoints,
          previousShotPoints.achievementPoints
        );
        const newLevel = shotPointsService.calculateLevel(newExperiencePoints);

        queryClient.setQueryData<ShotPointsData>(queryKeys.shotPoints.bySportHeadId(sportHeadId), {
          ...previousShotPoints,
          shotPoints: newShotPoints,
          experiencePoints: newExperiencePoints,
          level: newLevel,
          lastUpdated: new Date().toISOString(),
        });
      }

      // Return context with previous value
      return { previousShotPoints };
    },
    onError: (error, payload, context) => {
      if (!sportHeadId) return;

      // Rollback to previous value on error
      if (context?.previousShotPoints) {
        queryClient.setQueryData(queryKeys.shotPoints.bySportHeadId(sportHeadId), context.previousShotPoints);
      }

      logSupabaseError(error, 'addXpTransactionMutation');
      
      // Log error but don't throw - SHOT points failures should not affect core operations
      console.error('XP transaction failed:', error);
    },
    onSuccess: (transaction, payload) => {
      if (!sportHeadId) return;

      // Invalidate and refetch to get the latest server state
      queryClient.invalidateQueries({ queryKey: queryKeys.shotPoints.bySportHeadId(sportHeadId) });
      
      // Also invalidate user-specific queries if we have the user ID
      if (shotPoints?.userId) {
        queryClient.invalidateQueries({ queryKey: queryKeys.shotPoints.byUserId(shotPoints.userId) });
      }

      // Invalidate XP transactions queries
      if (shotPoints?.userId) {
        queryClient.invalidateQueries({ queryKey: queryKeys.xpTransactions.byPlayerId(shotPoints.userId) });
      }
    },
    onSettled: () => {
      if (!sportHeadId) return;

      // Always refetch after mutation to ensure consistency
      queryClient.invalidateQueries({ queryKey: queryKeys.shotPoints.bySportHeadId(sportHeadId) });
    },
    retry: (failureCount, error) => {
      if (!shouldRetryError(error)) {
        return false;
      }
      return failureCount < 1; // Only retry once for mutations
    },
  });

  // Real-time subscription for SHOT points changes using centralized utility
  useShotPointsSubscription(sportHeadId, shotPoints?.userId, !!sportHeadId);

  // Normalize error for consistent handling
  const error = queryError ? normalizeSupabaseError(queryError) : null;

  // SHOT points actions
  const addXpTransaction = async (payload: ShotPointsUpdatePayload): Promise<XpTransaction> => {
    try {
      const result = await addXpTransactionMutation.mutateAsync(payload);
      return result;
    } catch (error) {
      // Log error but don't re-throw - SHOT points failures should not affect core operations
      console.error('XP transaction failed in hook:', error);
      throw error;
    }
  };

  const refreshShotPoints = async (): Promise<void> => {
    if (!sportHeadId) return;
    await refetch();
  };

  return {
    shotPoints: shotPoints || null,
    isLoading: isLoading || addXpTransactionMutation.isPending,
    error,
    isStale,
    addXpTransaction,
    refreshShotPoints,
  };
}

/**
 * Hook for managing SHOT points data by user ID
 * 
 * This hook automatically finds the user's primary sport head and manages their SHOT points.
 * It provides the same isolation guarantees as the sport head version.
 * 
 * @param userId - The user ID to fetch SHOT points for (uses their primary sport head)
 * @returns SHOT points data, loading states, error states, and mutation functions
 * 
 * @example
 * ```tsx
 * const { user } = useCurrentUser();
 * const { shotPoints, isLoading, addXpTransaction, refreshShotPoints } = useShotPointsByUserId(user?.id);
 * 
 * // Display SHOT points safely
 * if (isLoading) return <div>Loading SHOT points...</div>;
 * if (!shotPoints) return <div>No SHOT points data</div>;
 * 
 * return (
 *   <div>
 *     <h3>Total: {shotPoints.shotPoints}</h3>
 *     <p>Level: {shotPoints.level}</p>
 *     <p>XP: {shotPoints.experiencePoints}</p>
 *   </div>
 * );
 * ```
 */
export function useShotPointsByUserId(userId?: string): ShotPointsHookData & ShotPointsActions {
  const queryClient = useQueryClient();

  // SHOT points query with React Query
  const {
    data: shotPoints,
    isLoading,
    error: queryError,
    isStale,
    refetch,
  } = useQuery({
    queryKey: queryKeys.shotPoints.byUserId(userId!),
    queryFn: () => shotPointsService.fetchShotPointsByUserId(userId!),
    enabled: !!userId, // Only run query if userId is provided
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error) => {
      if (!shouldRetryError(error)) {
        return false;
      }
      return failureCount < 3;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // XP transaction mutation
  const addXpTransactionMutation = useMutation({
    mutationFn: async (payload: ShotPointsUpdatePayload) => {
      if (!userId) {
        throw new Error('User ID is required for XP transaction');
      }

      return shotPointsService.addXpTransaction(
        userId,
        payload.xpAmount,
        payload.sourceType,
        payload.sourceId,
        payload.description
      );
    },
    onMutate: async (payload: ShotPointsUpdatePayload) => {
      if (!userId) return;

      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.shotPoints.byUserId(userId) });

      // Snapshot the previous value
      const previousShotPoints = queryClient.getQueryData<ShotPointsData>(
        queryKeys.shotPoints.byUserId(userId)
      );

      // Optimistically update the cache
      if (previousShotPoints) {
        const newExperiencePoints = previousShotPoints.experiencePoints + payload.xpAmount;
        const newShotPoints = shotPointsService.calculateTotalShotPoints(
          newExperiencePoints,
          previousShotPoints.achievementPoints
        );
        const newLevel = shotPointsService.calculateLevel(newExperiencePoints);

        queryClient.setQueryData<ShotPointsData>(queryKeys.shotPoints.byUserId(userId), {
          ...previousShotPoints,
          shotPoints: newShotPoints,
          experiencePoints: newExperiencePoints,
          level: newLevel,
          lastUpdated: new Date().toISOString(),
        });
      }

      return { previousShotPoints };
    },
    onError: (error, payload, context) => {
      if (!userId) return;

      // Rollback to previous value on error
      if (context?.previousShotPoints) {
        queryClient.setQueryData(queryKeys.shotPoints.byUserId(userId), context.previousShotPoints);
      }

      logSupabaseError(error, 'addXpTransactionMutation');
      console.error('XP transaction failed:', error);
    },
    onSuccess: (transaction, payload) => {
      if (!userId) return;

      // Invalidate and refetch to get the latest server state
      queryClient.invalidateQueries({ queryKey: queryKeys.shotPoints.byUserId(userId) });
      
      // Also invalidate sport head specific queries if we have the sport head ID
      if (shotPoints?.sportHeadId) {
        queryClient.invalidateQueries({ queryKey: queryKeys.shotPoints.bySportHeadId(shotPoints.sportHeadId) });
      }

      // Invalidate XP transactions queries
      queryClient.invalidateQueries({ queryKey: queryKeys.xpTransactions.byPlayerId(userId) });
    },
    onSettled: () => {
      if (!userId) return;
      queryClient.invalidateQueries({ queryKey: queryKeys.shotPoints.byUserId(userId) });
    },
    retry: (failureCount, error) => {
      if (!shouldRetryError(error)) {
        return false;
      }
      return failureCount < 1;
    },
  });

  // Real-time subscription for SHOT points changes using centralized utility
  useShotPointsSubscription(shotPoints?.sportHeadId, userId, !!userId);

  // Normalize error for consistent handling
  const error = queryError ? normalizeSupabaseError(queryError) : null;

  // SHOT points actions
  const addXpTransaction = async (payload: ShotPointsUpdatePayload): Promise<XpTransaction> => {
    try {
      const result = await addXpTransactionMutation.mutateAsync(payload);
      return result;
    } catch (error) {
      console.error('XP transaction failed in hook:', error);
      throw error;
    }
  };

  const refreshShotPoints = async (): Promise<void> => {
    if (!userId) return;
    await refetch();
  };

  return {
    shotPoints: shotPoints || null,
    isLoading: isLoading || addXpTransactionMutation.isPending,
    error,
    isStale,
    addXpTransaction,
    refreshShotPoints,
  };
}

/**
 * Main SHOT points hook - uses user ID by default
 * 
 * This is the primary hook that should be used in most cases. It provides complete
 * SHOT points management with error isolation to prevent failures from affecting
 * core user operations.
 * 
 * Key features:
 * - Automatic caching with React Query
 * - Real-time subscriptions for live updates
 * - Optimistic updates for better UX
 * - Error isolation to protect core app functionality
 * - Retry logic with exponential backoff
 * 
 * @param userId - The user ID to fetch SHOT points for
 * @returns SHOT points data, loading states, error states, and mutation functions
 * 
 * @example
 * ```tsx
 * import { useShotPoints } from '@/hooks/useShotPoints';
 * import { useCurrentUser } from '@/hooks/useCurrentUser';
 * 
 * function ShotPointsDisplay() {
 *   const { user } = useCurrentUser();
 *   const { 
 *     shotPoints, 
 *     isLoading, 
 *     error, 
 *     addXpTransaction, 
 *     refreshShotPoints 
 *   } = useShotPoints(user?.id);
 * 
 *   const handleTrainingComplete = async () => {
 *     try {
 *       await addXpTransaction({
 *         xpAmount: 50,
 *         sourceType: 'training',
 *         sourceId: 'training-session-123',
 *         description: 'Completed advanced training session'
 *       });
 *       // Success - points updated automatically via real-time subscription
 *     } catch (error) {
 *       // Error is isolated - core app functionality continues to work
 *       console.error('Failed to add XP, but app continues normally:', error);
 *     }
 *   };
 * 
 *   // Error state doesn't break the app
 *   if (error) {
 *     return (
 *       <div className="shot-points-error">
 *         <p>SHOT points temporarily unavailable</p>
 *         <button onClick={refreshShotPoints}>Retry</button>
 *       </div>
 *     );
 *   }
 * 
 *   // Loading state
 *   if (isLoading) {
 *     return <div>Loading SHOT points...</div>;
 *   }
 * 
 *   // No data state
 *   if (!shotPoints) {
 *     return <div>No SHOT points data available</div>;
 *   }
 * 
 *   // Success state
 *   return (
 *     <div className="shot-points-display">
 *       <h3>SHOT Points: {shotPoints.shotPoints.toLocaleString()}</h3>
 *       <p>Level {shotPoints.level}</p>
 *       <p>XP: {shotPoints.experiencePoints}</p>
 *       <p>Achievements: {shotPoints.achievementPoints}</p>
 *       <button onClick={handleTrainingComplete}>
 *         Complete Training (+50 XP)
 *       </button>
 *     </div>
 *   );
 * }
 * ```
 */
export function useShotPoints(userId?: string): ShotPointsHookData & ShotPointsActions {
  return useShotPointsByUserId(userId);
}

/**
 * Hook for SHOT points refresh functionality
 * 
 * Provides utility functions to refresh SHOT points data across the app.
 * Useful for global refresh operations or manual cache invalidation.
 * 
 * @example
 * ```tsx
 * const { refreshAllShotPoints, refreshUserShotPoints } = useShotPointsRefresh();
 * 
 * // Refresh all SHOT points data in the app
 * const handleGlobalRefresh = async () => {
 *   await refreshAllShotPoints();
 * };
 * 
 * // Refresh specific user's SHOT points
 * const handleUserRefresh = async (userId: string) => {
 *   await refreshUserShotPoints(userId);
 * };
 * ```
 */
export function useShotPointsRefresh() {
  const queryClient = useQueryClient();

  const refreshAllShotPoints = async (): Promise<void> => {
    await queryClient.invalidateQueries({ queryKey: queryKeys.shotPoints.all });
  };

  const refreshUserShotPoints = async (userId: string): Promise<void> => {
    await queryClient.invalidateQueries({ queryKey: queryKeys.shotPoints.byUserId(userId) });
  };

  const refreshSportHeadShotPoints = async (sportHeadId: string): Promise<void> => {
    await queryClient.invalidateQueries({ queryKey: queryKeys.shotPoints.bySportHeadId(sportHeadId) });
  };

  return {
    refreshAllShotPoints,
    refreshUserShotPoints,
    refreshSportHeadShotPoints,
  };
}

/**
 * Helper functions for SHOT points calculations
 * 
 * Provides utility functions for calculating SHOT points and levels.
 * These can be used independently of the hooks for client-side calculations.
 * 
 * @example
 * ```tsx
 * import { shotPointsHelpers } from '@/hooks/useShotPoints';
 * 
 * // Calculate total SHOT points
 * const totalPoints = shotPointsHelpers.calculateTotalShotPoints(150, 50); // 200
 * 
 * // Calculate level from experience points
 * const level = shotPointsHelpers.calculateLevel(350); // Level based on XP
 * ```
 */
export const shotPointsHelpers = {
  calculateTotalShotPoints: shotPointsService.calculateTotalShotPoints,
  calculateLevel: shotPointsService.calculateLevel,
};