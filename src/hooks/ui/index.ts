/**
 * UI utility hooks for common interface elements.
 * 
 * These hooks provide memoized, reusable UI components that were extracted
 * from UserContext to separate UI concerns from data management and prevent
 * unnecessary re-renders.
 * 
 * @example
 * ```ts
 * import { useProfileAvatar, useIconUtilities } from '@/hooks/ui';
 * 
 * const { getAvatarElement } = useProfileAvatar(user?.avatar_url);
 * const { getFilterIcon, getQRCodeIcon } = useIconUtilities();
 * ```
 */

export { useProfileAvatar } from '../useProfileAvatar';
export { useIconUtilities } from '../useIconUtilities';