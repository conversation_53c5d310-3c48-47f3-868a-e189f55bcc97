import { useMemo } from 'react';
import { IonButton, IonIcon } from '@ionic/react';
import { filterOutline, qrCodeOutline } from 'ionicons/icons';

/**
 * A custom hook that provides memoized icon utility functions for common UI icons.
 *
 * @returns An object containing icon generator functions for filter and QR code icons.
 *
 * @example
 * ```ts
 * const { getFilterIcon, getQRCodeIcon } = useIconUtilities();
 * const filterButton = getFilterIcon(() => setShowFilters(true));
 * const qrButton = getQRCodeIcon(() => setShowQRCode(true));
 * ```
 */
export function useIconUtilities() {
  return useMemo(() => ({
    /**
     * Generates a filter icon button with proper accessibility.
     *
     * @param onClick - Optional click handler for the filter icon.
     * @returns A React element representing the filter icon button.
     */
    getFilterIcon: (onClick?: () => void) => (
      <IonButton
        onClick={onClick}
        className="icon-button"
        fill="clear"
        aria-label="Filter options"
      >
        <IonIcon icon={filterOutline} slot="icon-only" className="text-white" />
      </IonButton>
    ),

    /**
     * Generates a QR code icon button with proper accessibility.
     *
     * @param onClick - Optional click handler for the QR code icon.
     * @returns A React element representing the QR code icon button.
     */
    getQRCodeIcon: (onClick?: () => void) => (
      <IonButton
        onClick={onClick}
        className="icon-button"
        fill="clear"
        aria-label="QR code"
      >
        <IonIcon icon={qrCodeOutline} slot="icon-only" className="text-white" />
      </IonButton>
    ),
  }), []);
}