import { useAuth } from "@/hooks/useAuth";
import { useProfile } from "@/hooks/useProfile";
import { useAuthHydration } from "@/providers/AuthProvider";

export function useCurrentUser() {
  const hydrated = useAuthHydration();
  const { user, session, isAuthenticated, isEmailVerified, signOut } = useAuth();

  const {
    profile,
    isLoading: profileLoading,
    updateProfile,
    refreshProfile,
    error: profileError,
  } = useProfile(user?.id);

  return {
    // Auth
    user,
    session,
    isAuthenticated,
    isEmailVerified,
    signOut,

    // Profile
    profile,
    updateProfile,
    refreshProfile,
    profileError,

    // State flags
    hydrated,
    isLoading: !hydrated || profileLoading,
  };
}