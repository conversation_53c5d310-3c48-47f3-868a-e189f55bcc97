/**
 * Base TypeScript types for all user-related data structures
 * Following the TypeScript conventions to use 'type' over 'interface'
 * Uses Supabase generated types for consistency and type safety
 */

import type { User as SupabaseUser } from '@supabase/supabase-js';
import type { Database } from './database';

/**
 * Extended user type that includes Supabase auth user data
 */
export type User = SupabaseUser;

/**
 * User profile data structure from the profiles table
 * Uses Supabase generated types for consistency
 */
export type UserProfile = Database['public']['Tables']['profiles']['Row'];

/**
 * Profile insert payload for creating new profiles
 */
export type UserProfileInsert = Database['public']['Tables']['profiles']['Insert'];

/**
 * Profile update payload for updating existing profiles
 */
export type UserProfileUpdate = Database['public']['Tables']['profiles']['Update'];

/**
 * SHOT Points data structure (computed from sport_heads table)
 */
export type ShotPointsData = {
  shotPoints: number;
  experiencePoints: number;
  achievementPoints: number;
  level: number;
  prestigeLevel: number;
  sportHeadId: string;
  userId: string;
  lastUpdated?: string;
};

/**
 * XP Transaction data structure from player_xp_transactions table
 * Uses Supabase generated types for consistency
 */
export type XpTransaction = Database['public']['Tables']['player_xp_transactions']['Row'];

/**
 * XP Transaction insert payload
 */
export type XpTransactionInsert = Database['public']['Tables']['player_xp_transactions']['Insert'];

/**
 * Sport Head data structure from sport_heads table
 * Uses Supabase generated types for consistency
 */
export type SportHead = Database['public']['Tables']['sport_heads']['Row'];

/**
 * Sport Head insert payload
 */
export type SportHeadInsert = Database['public']['Tables']['sport_heads']['Insert'];

/**
 * Sport Head update payload
 */
export type SportHeadUpdate = Database['public']['Tables']['sport_heads']['Update'];

/**
 * Authentication state
 */
export type AuthState = {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isEmailVerified: boolean;
};

/**
 * Profile update payload (legacy compatibility)
 */
export type ProfileUpdatePayload = UserProfileUpdate;

/**
 * SHOT Points update payload
 */
export type ShotPointsUpdatePayload = {
  xpAmount: number;
  sourceType: string;
  sourceId?: string;
  description?: string;
};

/**
 * Error types for user operations
 */
export type UserError = {
  code: string;
  message: string;
  details?: unknown;
};

/**
 * Loading states for different operations
 */
export type LoadingStates = {
  auth: boolean;
  profile: boolean;
  shotPoints: boolean;
  profileUpdate: boolean;
  shotPointsUpdate: boolean;
};

/**
 * User context value type (for backward compatibility)
 */
export type UserContextValue = {
  profile: UserProfile | null;
  user: User | null;
  loading: boolean;
  isEmailVerified: boolean;
  // Legacy methods for backward compatibility
  setProfile: React.Dispatch<React.SetStateAction<UserProfile | null>>;
  updateUserInContext: (updatedProfile: Partial<UserProfile>) => void;
  fetchUserProfile: () => Promise<void>;
  refreshUserData: () => Promise<void>;
  refreshShotPoints: () => Promise<void>;
  addXpTransaction: (xpAmount: number, sourceType: string, sourceId?: string, description?: string) => Promise<void>;
  // UI helper methods
  getProfileAvatar: (onClick?: () => void) => React.ReactNode;
  getFilterIcon: (onClick?: () => void) => React.ReactNode;
  getQRCodeIcon: (onClick?: () => void) => React.ReactNode;
};