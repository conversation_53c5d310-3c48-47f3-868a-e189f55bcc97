/**
 * Database Types
 * 
 * Auto-generated TypeScript types for Supabase database schema.
 * 
 * Generated: 2025-08-14T10:33:03.023Z
 * Source: local development
 * 
 * DO NOT EDIT MANUALLY - This file is auto-generated.
 * To regenerate: npm run types:generate
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      activities: {
        Row: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at: string
          created_month: string
          id: string
          metadata: Json | null
          target_id: string | null
          target_type: string | null
          user_id: string
        }
        Insert: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id: string
        }
        Update: {
          activity_type?: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month?: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "activities_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      activities_y2024m01: {
        Row: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at: string
          created_month: string
          id: string
          metadata: Json | null
          target_id: string | null
          target_type: string | null
          user_id: string
        }
        Insert: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id: string
        }
        Update: {
          activity_type?: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month?: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      activities_y2024m02: {
        Row: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at: string
          created_month: string
          id: string
          metadata: Json | null
          target_id: string | null
          target_type: string | null
          user_id: string
        }
        Insert: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id: string
        }
        Update: {
          activity_type?: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month?: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      activities_y2025m01: {
        Row: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at: string
          created_month: string
          id: string
          metadata: Json | null
          target_id: string | null
          target_type: string | null
          user_id: string
        }
        Insert: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id: string
        }
        Update: {
          activity_type?: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month?: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      activities_y2025m02: {
        Row: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at: string
          created_month: string
          id: string
          metadata: Json | null
          target_id: string | null
          target_type: string | null
          user_id: string
        }
        Insert: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id: string
        }
        Update: {
          activity_type?: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month?: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      activities_y2025m03: {
        Row: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at: string
          created_month: string
          id: string
          metadata: Json | null
          target_id: string | null
          target_type: string | null
          user_id: string
        }
        Insert: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id: string
        }
        Update: {
          activity_type?: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month?: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      activities_y2025m04: {
        Row: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at: string
          created_month: string
          id: string
          metadata: Json | null
          target_id: string | null
          target_type: string | null
          user_id: string
        }
        Insert: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id: string
        }
        Update: {
          activity_type?: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month?: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      activities_y2025m05: {
        Row: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at: string
          created_month: string
          id: string
          metadata: Json | null
          target_id: string | null
          target_type: string | null
          user_id: string
        }
        Insert: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id: string
        }
        Update: {
          activity_type?: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month?: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      activities_y2025m06: {
        Row: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at: string
          created_month: string
          id: string
          metadata: Json | null
          target_id: string | null
          target_type: string | null
          user_id: string
        }
        Insert: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id: string
        }
        Update: {
          activity_type?: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month?: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      activities_y2025m07: {
        Row: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at: string
          created_month: string
          id: string
          metadata: Json | null
          target_id: string | null
          target_type: string | null
          user_id: string
        }
        Insert: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id: string
        }
        Update: {
          activity_type?: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month?: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      activities_y2025m08: {
        Row: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at: string
          created_month: string
          id: string
          metadata: Json | null
          target_id: string | null
          target_type: string | null
          user_id: string
        }
        Insert: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id: string
        }
        Update: {
          activity_type?: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month?: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      activities_y2025m09: {
        Row: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at: string
          created_month: string
          id: string
          metadata: Json | null
          target_id: string | null
          target_type: string | null
          user_id: string
        }
        Insert: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id: string
        }
        Update: {
          activity_type?: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month?: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      activities_y2025m10: {
        Row: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at: string
          created_month: string
          id: string
          metadata: Json | null
          target_id: string | null
          target_type: string | null
          user_id: string
        }
        Insert: {
          activity_type: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id: string
        }
        Update: {
          activity_type?: Database["public"]["Enums"]["activity_type"]
          created_at?: string
          created_month?: string
          id?: string
          metadata?: Json | null
          target_id?: string | null
          target_type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      addons: {
        Row: {
          billing_period: Database["public"]["Enums"]["billing_period"]
          category: string
          created_at: string | null
          description: string | null
          id: string
          name: string
          price: number
          status: Database["public"]["Enums"]["membership_status"] | null
          updated_at: string | null
        }
        Insert: {
          billing_period: Database["public"]["Enums"]["billing_period"]
          category: string
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          price: number
          status?: Database["public"]["Enums"]["membership_status"] | null
          updated_at?: string | null
        }
        Update: {
          billing_period?: Database["public"]["Enums"]["billing_period"]
          category?: string
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          price?: number
          status?: Database["public"]["Enums"]["membership_status"] | null
          updated_at?: string | null
        }
        Relationships: []
      }
      age_transitions: {
        Row: {
          created_at: string | null
          id: string
          notification_history: Json | null
          parent_accounts: Json | null
          pre_transition_permissions: Json | null
          transition_completed: boolean | null
          transition_date: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          notification_history?: Json | null
          parent_accounts?: Json | null
          pre_transition_permissions?: Json | null
          transition_completed?: boolean | null
          transition_date: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          notification_history?: Json | null
          parent_accounts?: Json | null
          pre_transition_permissions?: Json | null
          transition_completed?: boolean | null
          transition_date?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "age_transitions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "age_transitions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "age_transitions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      app_settings: {
        Row: {
          created_at: string | null
          description: string | null
          key: string
          updated_at: string | null
          value: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          key: string
          updated_at?: string | null
          value: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          key?: string
          updated_at?: string | null
          value?: string
        }
        Relationships: []
      }
      children: {
        Row: {
          allergies: string[] | null
          avatar_url: string | null
          created_at: string
          date_of_birth: string
          emergency_contact: Json
          id: string
          medical_conditions: string[] | null
          name: string
          photography_allowed: boolean | null
          profile_id: string | null
          updated_at: string
        }
        Insert: {
          allergies?: string[] | null
          avatar_url?: string | null
          created_at?: string
          date_of_birth: string
          emergency_contact: Json
          id?: string
          medical_conditions?: string[] | null
          name: string
          photography_allowed?: boolean | null
          profile_id?: string | null
          updated_at?: string
        }
        Update: {
          allergies?: string[] | null
          avatar_url?: string | null
          created_at?: string
          date_of_birth?: string
          emergency_contact?: Json
          id?: string
          medical_conditions?: string[] | null
          name?: string
          photography_allowed?: boolean | null
          profile_id?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "children_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "children_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "children_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      club_administrators: {
        Row: {
          admin_id: string | null
          assigned_at: string | null
          assigned_by: string | null
          club_id: string
          created_at: string | null
          id: string
          is_active: boolean
          is_primary: boolean | null
          role: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          admin_id?: string | null
          assigned_at?: string | null
          assigned_by?: string | null
          club_id: string
          created_at?: string | null
          id?: string
          is_active?: boolean
          is_primary?: boolean | null
          role?: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          admin_id?: string | null
          assigned_at?: string | null
          assigned_by?: string | null
          club_id?: string
          created_at?: string | null
          id?: string
          is_active?: boolean
          is_primary?: boolean | null
          role?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "club_administrators_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "club_administrators_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["club_id"]
          },
          {
            foreignKeyName: "club_administrators_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      club_coaches: {
        Row: {
          assigned_at: string | null
          assigned_by: string | null
          club_id: string | null
          coach_assignment_id: string
          is_primary: boolean | null
          notes: string | null
          removed_at: string | null
          removed_by: string | null
          role: string
          status: string | null
          user_id: string | null
        }
        Insert: {
          assigned_at?: string | null
          assigned_by?: string | null
          club_id?: string | null
          coach_assignment_id: string
          is_primary?: boolean | null
          notes?: string | null
          removed_at?: string | null
          removed_by?: string | null
          role: string
          status?: string | null
          user_id?: string | null
        }
        Update: {
          assigned_at?: string | null
          assigned_by?: string | null
          club_id?: string | null
          coach_assignment_id?: string
          is_primary?: boolean | null
          notes?: string | null
          removed_at?: string | null
          removed_by?: string | null
          role?: string
          status?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "club_coaches_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "club_coaches_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "club_coaches_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "club_coaches_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["club_id"]
          },
          {
            foreignKeyName: "club_coaches_removed_by_fkey"
            columns: ["removed_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "club_coaches_removed_by_fkey"
            columns: ["removed_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "club_coaches_removed_by_fkey"
            columns: ["removed_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "club_coaches_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "club_coaches_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "club_coaches_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      clubs: {
        Row: {
          address: string | null
          approved_at: string | null
          approved_by: string | null
          club_id: string
          club_name: string
          contact_email: string | null
          contact_phone: string | null
          created_at: string | null
          created_by: string | null
          is_active: boolean | null
          logo_url: string | null
          rejection_reason: string | null
          sport_type: string | null
          updated_at: string | null
          verification_status: string | null
        }
        Insert: {
          address?: string | null
          approved_at?: string | null
          approved_by?: string | null
          club_id: string
          club_name: string
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string | null
          created_by?: string | null
          is_active?: boolean | null
          logo_url?: string | null
          rejection_reason?: string | null
          sport_type?: string | null
          updated_at?: string | null
          verification_status?: string | null
        }
        Update: {
          address?: string | null
          approved_at?: string | null
          approved_by?: string | null
          club_id?: string
          club_name?: string
          contact_email?: string | null
          contact_phone?: string | null
          created_at?: string | null
          created_by?: string | null
          is_active?: boolean | null
          logo_url?: string | null
          rejection_reason?: string | null
          sport_type?: string | null
          updated_at?: string | null
          verification_status?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "clubs_approved_by_fkey"
            columns: ["approved_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "clubs_approved_by_fkey"
            columns: ["approved_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "clubs_approved_by_fkey"
            columns: ["approved_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "clubs_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "clubs_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "clubs_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      coach_verification_requests: {
        Row: {
          club_name: string
          created_at: string | null
          date_of_birth: string
          id: string
          review_notes: string | null
          reviewed_at: string | null
          reviewed_by: string | null
          sport_type: string
          status: string | null
          submitted_at: string | null
          team_name: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          club_name: string
          created_at?: string | null
          date_of_birth: string
          id?: string
          review_notes?: string | null
          reviewed_at?: string | null
          reviewed_by?: string | null
          sport_type: string
          status?: string | null
          submitted_at?: string | null
          team_name: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          club_name?: string
          created_at?: string | null
          date_of_birth?: string
          id?: string
          review_notes?: string | null
          reviewed_at?: string | null
          reviewed_by?: string | null
          sport_type?: string
          status?: string | null
          submitted_at?: string | null
          team_name?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "coach_verification_requests_reviewed_by_fkey"
            columns: ["reviewed_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "coach_verification_requests_reviewed_by_fkey"
            columns: ["reviewed_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "coach_verification_requests_reviewed_by_fkey"
            columns: ["reviewed_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "coach_verification_requests_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "coach_verification_requests_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "coach_verification_requests_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      debug_logs: {
        Row: {
          created_at: string | null
          data: Json | null
          function_name: string | null
          id: number
          message: string | null
        }
        Insert: {
          created_at?: string | null
          data?: Json | null
          function_name?: string | null
          id?: number
          message?: string | null
        }
        Update: {
          created_at?: string | null
          data?: Json | null
          function_name?: string | null
          id?: number
          message?: string | null
        }
        Relationships: []
      }
      deletion_logs: {
        Row: {
          created_at: string | null
          entity_id: string | null
          entity_type: string
          log_id: string
          message: string | null
          operation: string
          status: string
        }
        Insert: {
          created_at?: string | null
          entity_id?: string | null
          entity_type: string
          log_id?: string
          message?: string | null
          operation: string
          status: string
        }
        Update: {
          created_at?: string | null
          entity_id?: string | null
          entity_type?: string
          log_id?: string
          message?: string | null
          operation?: string
          status?: string
        }
        Relationships: []
      }
      digital_club_cards: {
        Row: {
          card_design: Json | null
          created_at: string | null
          expiry_date: string | null
          generation_date: string | null
          id: string
          qr_code_data: string
          sharing_stats: Json | null
          sport_head_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          card_design?: Json | null
          created_at?: string | null
          expiry_date?: string | null
          generation_date?: string | null
          id?: string
          qr_code_data: string
          sharing_stats?: Json | null
          sport_head_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          card_design?: Json | null
          created_at?: string | null
          expiry_date?: string | null
          generation_date?: string | null
          id?: string
          qr_code_data?: string
          sharing_stats?: Json | null
          sport_head_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "digital_club_cards_sport_head_id_fkey"
            columns: ["sport_head_id"]
            isOneToOne: false
            referencedRelation: "sport_heads"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "digital_club_cards_sport_head_id_fkey"
            columns: ["sport_head_id"]
            isOneToOne: false
            referencedRelation: "user_sport_heads_complete"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "digital_club_cards_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "digital_club_cards_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "digital_club_cards_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      evaluation_criteria: {
        Row: {
          answers_post: Json | null
          answers_pre: Json | null
          area: string
          category: string
          created_at: string
          evaluation_focus: string
          framework_date: string | null
          framework_version: string
          id: string
          is_active: boolean | null
          position: string
          question: string
          question_post: string | null
          question_pre: string | null
          sport_type: string
          updated_at: string
          week_number: number
        }
        Insert: {
          answers_post?: Json | null
          answers_pre?: Json | null
          area: string
          category: string
          created_at?: string
          evaluation_focus: string
          framework_date?: string | null
          framework_version?: string
          id?: string
          is_active?: boolean | null
          position: string
          question: string
          question_post?: string | null
          question_pre?: string | null
          sport_type?: string
          updated_at?: string
          week_number: number
        }
        Update: {
          answers_post?: Json | null
          answers_pre?: Json | null
          area?: string
          category?: string
          created_at?: string
          evaluation_focus?: string
          framework_date?: string | null
          framework_version?: string
          id?: string
          is_active?: boolean | null
          position?: string
          question?: string
          question_post?: string | null
          question_pre?: string | null
          sport_type?: string
          updated_at?: string
          week_number?: number
        }
        Relationships: [
          {
            foreignKeyName: "fk_evaluation_criteria_framework"
            columns: ["framework_version"]
            isOneToOne: false
            referencedRelation: "evaluation_framework_metadata"
            referencedColumns: ["framework_version"]
          },
        ]
      }
      evaluation_framework_metadata: {
        Row: {
          categories: string[]
          description: string | null
          end_date: string
          framework_version: string
          imported_at: string
          imported_by: string | null
          is_active: boolean | null
          name: string
          positions: string[]
          sport_type: string
          start_date: string
          total_weeks: number
        }
        Insert: {
          categories: string[]
          description?: string | null
          end_date: string
          framework_version: string
          imported_at?: string
          imported_by?: string | null
          is_active?: boolean | null
          name: string
          positions: string[]
          sport_type?: string
          start_date: string
          total_weeks: number
        }
        Update: {
          categories?: string[]
          description?: string | null
          end_date?: string
          framework_version?: string
          imported_at?: string
          imported_by?: string | null
          is_active?: boolean | null
          name?: string
          positions?: string[]
          sport_type?: string
          start_date?: string
          total_weeks?: number
        }
        Relationships: [
          {
            foreignKeyName: "evaluation_framework_metadata_imported_by_fkey"
            columns: ["imported_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "evaluation_framework_metadata_imported_by_fkey"
            columns: ["imported_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "evaluation_framework_metadata_imported_by_fkey"
            columns: ["imported_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      evaluation_sessions: {
        Row: {
          created_at: string
          evaluator_id: string
          event_id: string | null
          framework_version: string
          id: string
          is_completed: boolean | null
          notes: string | null
          session_date: string
          session_type: string
          team_id: string
          updated_at: string
          week_number: number
        }
        Insert: {
          created_at?: string
          evaluator_id: string
          event_id?: string | null
          framework_version?: string
          id?: string
          is_completed?: boolean | null
          notes?: string | null
          session_date: string
          session_type?: string
          team_id: string
          updated_at?: string
          week_number: number
        }
        Update: {
          created_at?: string
          evaluator_id?: string
          event_id?: string | null
          framework_version?: string
          id?: string
          is_completed?: boolean | null
          notes?: string | null
          session_date?: string
          session_type?: string
          team_id?: string
          updated_at?: string
          week_number?: number
        }
        Relationships: [
          {
            foreignKeyName: "evaluation_sessions_evaluator_id_fkey"
            columns: ["evaluator_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "evaluation_sessions_evaluator_id_fkey"
            columns: ["evaluator_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "evaluation_sessions_evaluator_id_fkey"
            columns: ["evaluator_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_evaluation_sessions_framework"
            columns: ["framework_version"]
            isOneToOne: false
            referencedRelation: "evaluation_framework_metadata"
            referencedColumns: ["framework_version"]
          },
        ]
      }
      evaluation_templates: {
        Row: {
          categories: string[]
          club_id: string | null
          created_at: string
          created_by: string
          description: string | null
          framework_version: string
          id: string
          is_active: boolean | null
          name: string
          positions: string[]
          updated_at: string
          week_ranges: number[]
        }
        Insert: {
          categories: string[]
          club_id?: string | null
          created_at?: string
          created_by: string
          description?: string | null
          framework_version?: string
          id?: string
          is_active?: boolean | null
          name: string
          positions: string[]
          updated_at?: string
          week_ranges: number[]
        }
        Update: {
          categories?: string[]
          club_id?: string | null
          created_at?: string
          created_by?: string
          description?: string | null
          framework_version?: string
          id?: string
          is_active?: boolean | null
          name?: string
          positions?: string[]
          updated_at?: string
          week_ranges?: number[]
        }
        Relationships: [
          {
            foreignKeyName: "evaluation_templates_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "evaluation_templates_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "evaluation_templates_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_evaluation_templates_framework"
            columns: ["framework_version"]
            isOneToOne: false
            referencedRelation: "evaluation_framework_metadata"
            referencedColumns: ["framework_version"]
          },
        ]
      }
      evaluation_xp_logs: {
        Row: {
          awarded_at: string | null
          event_id: string
          id: string
          player_id: string
          reason: string
          xp_amount: number
        }
        Insert: {
          awarded_at?: string | null
          event_id: string
          id?: string
          player_id: string
          reason?: string
          xp_amount?: number
        }
        Update: {
          awarded_at?: string | null
          event_id?: string
          id?: string
          player_id?: string
          reason?: string
          xp_amount?: number
        }
        Relationships: [
          {
            foreignKeyName: "evaluation_xp_logs_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event_comprehensive_summary"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "evaluation_xp_logs_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event_evaluation_summary"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "evaluation_xp_logs_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event_summary"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "evaluation_xp_logs_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "evaluation_xp_logs_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "player_training_participation"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "evaluation_xp_logs_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "evaluation_xp_logs_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "evaluation_xp_logs_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      event_child_registrations: {
        Row: {
          child_id: string
          created_at: string
          event_id: string
          id: string
          rsvp_id: string
          updated_at: string
        }
        Insert: {
          child_id: string
          created_at?: string
          event_id: string
          id?: string
          rsvp_id: string
          updated_at?: string
        }
        Update: {
          child_id?: string
          created_at?: string
          event_id?: string
          id?: string
          rsvp_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "event_child_registrations_child_id_fkey"
            columns: ["child_id"]
            isOneToOne: false
            referencedRelation: "children"
            referencedColumns: ["id"]
          },
        ]
      }
      event_participant_status_history: {
        Row: {
          changed_at: string
          changed_by: string | null
          history_id: string
          new_status: string
          notes: string | null
          old_status: string | null
          participant_id: string
        }
        Insert: {
          changed_at?: string
          changed_by?: string | null
          history_id?: string
          new_status: string
          notes?: string | null
          old_status?: string | null
          participant_id: string
        }
        Update: {
          changed_at?: string
          changed_by?: string | null
          history_id?: string
          new_status?: string
          notes?: string | null
          old_status?: string | null
          participant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "event_participant_status_history_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_participant_status_history_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "event_participant_status_history_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_participant_status_history_participant_id_fkey"
            columns: ["participant_id"]
            isOneToOne: false
            referencedRelation: "event_participants"
            referencedColumns: ["participant_id"]
          },
          {
            foreignKeyName: "event_participant_status_history_participant_id_fkey"
            columns: ["participant_id"]
            isOneToOne: false
            referencedRelation: "player_training_participation"
            referencedColumns: ["participant_id"]
          },
        ]
      }
      event_participants: {
        Row: {
          attendance_confirmed_at: string | null
          attendance_confirmed_by: string | null
          created_at: string | null
          event_id: string | null
          invitation_status: string | null
          invited_at: string | null
          notes: string | null
          participant_id: string
          responded_at: string | null
          role: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          attendance_confirmed_at?: string | null
          attendance_confirmed_by?: string | null
          created_at?: string | null
          event_id?: string | null
          invitation_status?: string | null
          invited_at?: string | null
          notes?: string | null
          participant_id?: string
          responded_at?: string | null
          role: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          attendance_confirmed_at?: string | null
          attendance_confirmed_by?: string | null
          created_at?: string | null
          event_id?: string | null
          invitation_status?: string | null
          invited_at?: string | null
          notes?: string | null
          participant_id?: string
          responded_at?: string | null
          role?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "event_participants_attendance_confirmed_by_fkey"
            columns: ["attendance_confirmed_by"]
            isOneToOne: false
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_participants_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event_comprehensive_summary"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "event_participants_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event_evaluation_summary"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "event_participants_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event_summary"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "event_participants_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_participants_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "player_training_participation"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "event_participants_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      events: {
        Row: {
          attendance_completed: boolean | null
          club_id: string | null
          created_at: string | null
          created_by: string | null
          current_participants: number | null
          deleted_at: string | null
          details: string | null
          end_datetime: string | null
          evaluation_completed: boolean | null
          evaluation_completed_at: string | null
          evaluation_completed_by: string | null
          evaluation_completed_manually: boolean | null
          event_type: string | null
          host_contact: string | null
          hosted_by: string
          id: string
          is_active: boolean | null
          is_instant: boolean | null
          is_pre_session_evaluation: boolean | null
          location_address: string | null
          location_coordinates: unknown | null
          location_name: string | null
          max_participants: number | null
          meet_datetime: string | null
          name: string
          notification_settings: Json | null
          photos: string[] | null
          pre_evaluation_questions: Json | null
          price: number | null
          slug: string
          sport_framework: string | null
          sport_type: string | null
          start_datetime: string
          status: Database["public"]["Enums"]["event_status"] | null
          tags: string[] | null
          team_id: string | null
          updated_at: string | null
        }
        Insert: {
          attendance_completed?: boolean | null
          club_id?: string | null
          created_at?: string | null
          created_by?: string | null
          current_participants?: number | null
          deleted_at?: string | null
          details?: string | null
          end_datetime?: string | null
          evaluation_completed?: boolean | null
          evaluation_completed_at?: string | null
          evaluation_completed_by?: string | null
          evaluation_completed_manually?: boolean | null
          event_type?: string | null
          host_contact?: string | null
          hosted_by: string
          id?: string
          is_active?: boolean | null
          is_instant?: boolean | null
          is_pre_session_evaluation?: boolean | null
          location_address?: string | null
          location_coordinates?: unknown | null
          location_name?: string | null
          max_participants?: number | null
          meet_datetime?: string | null
          name: string
          notification_settings?: Json | null
          photos?: string[] | null
          pre_evaluation_questions?: Json | null
          price?: number | null
          slug: string
          sport_framework?: string | null
          sport_type?: string | null
          start_datetime: string
          status?: Database["public"]["Enums"]["event_status"] | null
          tags?: string[] | null
          team_id?: string | null
          updated_at?: string | null
        }
        Update: {
          attendance_completed?: boolean | null
          club_id?: string | null
          created_at?: string | null
          created_by?: string | null
          current_participants?: number | null
          deleted_at?: string | null
          details?: string | null
          end_datetime?: string | null
          evaluation_completed?: boolean | null
          evaluation_completed_at?: string | null
          evaluation_completed_by?: string | null
          evaluation_completed_manually?: boolean | null
          event_type?: string | null
          host_contact?: string | null
          hosted_by?: string
          id?: string
          is_active?: boolean | null
          is_instant?: boolean | null
          is_pre_session_evaluation?: boolean | null
          location_address?: string | null
          location_coordinates?: unknown | null
          location_name?: string | null
          max_participants?: number | null
          meet_datetime?: string | null
          name?: string
          notification_settings?: Json | null
          photos?: string[] | null
          pre_evaluation_questions?: Json | null
          price?: number | null
          slug?: string
          sport_framework?: string | null
          sport_type?: string | null
          start_datetime?: string
          status?: Database["public"]["Enums"]["event_status"] | null
          tags?: string[] | null
          team_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "events_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "events_evaluation_completed_by_fkey"
            columns: ["evaluation_completed_by"]
            isOneToOne: false
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_events_club"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["club_id"]
          },
          {
            foreignKeyName: "fk_events_team"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "fk_events_team"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
        ]
      }
      family_relationships: {
        Row: {
          can_approve_team_joins: boolean | null
          can_view_evaluations: boolean | null
          child_id: string
          created_at: string
          id: string
          is_approved: boolean | null
          parent_id: string
          relationship_type: string
          updated_at: string
        }
        Insert: {
          can_approve_team_joins?: boolean | null
          can_view_evaluations?: boolean | null
          child_id: string
          created_at?: string
          id?: string
          is_approved?: boolean | null
          parent_id: string
          relationship_type: string
          updated_at?: string
        }
        Update: {
          can_approve_team_joins?: boolean | null
          can_view_evaluations?: boolean | null
          child_id?: string
          created_at?: string
          id?: string
          is_approved?: boolean | null
          parent_id?: string
          relationship_type?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "family_relationships_child_id_fkey"
            columns: ["child_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "family_relationships_child_id_fkey"
            columns: ["child_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "family_relationships_child_id_fkey"
            columns: ["child_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "family_relationships_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "family_relationships_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "family_relationships_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      http_request_log: {
        Row: {
          completed_at: string | null
          created_at: string
          event_type: string
          id: string
          request_id: string
          resource_id: string
          response_body: string | null
          response_status: number | null
        }
        Insert: {
          completed_at?: string | null
          created_at: string
          event_type: string
          id?: string
          request_id: string
          resource_id: string
          response_body?: string | null
          response_status?: number | null
        }
        Update: {
          completed_at?: string | null
          created_at?: string
          event_type?: string
          id?: string
          request_id?: string
          resource_id?: string
          response_body?: string | null
          response_status?: number | null
        }
        Relationships: []
      }
      invite_code_redemptions: {
        Row: {
          activity_metadata: Json | null
          created_at: string | null
          id: string
          invite_code: string
          invitee_user_id: string
          inviter_user_id: string
          redeemed_at: string | null
          redemption_context: string
          redemption_metadata: Json | null
          redemption_source: string
        }
        Insert: {
          activity_metadata?: Json | null
          created_at?: string | null
          id?: string
          invite_code: string
          invitee_user_id: string
          inviter_user_id: string
          redeemed_at?: string | null
          redemption_context: string
          redemption_metadata?: Json | null
          redemption_source: string
        }
        Update: {
          activity_metadata?: Json | null
          created_at?: string | null
          id?: string
          invite_code?: string
          invitee_user_id?: string
          inviter_user_id?: string
          redeemed_at?: string | null
          redemption_context?: string
          redemption_metadata?: Json | null
          redemption_source?: string
        }
        Relationships: [
          {
            foreignKeyName: "invite_code_redemptions_invite_code_fkey"
            columns: ["invite_code"]
            isOneToOne: false
            referencedRelation: "invite_code_analytics"
            referencedColumns: ["code"]
          },
          {
            foreignKeyName: "invite_code_redemptions_invite_code_fkey"
            columns: ["invite_code"]
            isOneToOne: false
            referencedRelation: "invite_codes"
            referencedColumns: ["code"]
          },
          {
            foreignKeyName: "invite_code_redemptions_invitee_user_id_fkey"
            columns: ["invitee_user_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invite_code_redemptions_invitee_user_id_fkey"
            columns: ["invitee_user_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "invite_code_redemptions_invitee_user_id_fkey"
            columns: ["invitee_user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invite_code_redemptions_inviter_user_id_fkey"
            columns: ["inviter_user_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invite_code_redemptions_inviter_user_id_fkey"
            columns: ["inviter_user_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "invite_code_redemptions_inviter_user_id_fkey"
            columns: ["inviter_user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      invite_codes: {
        Row: {
          analytics_data: Json | null
          code: string
          code_type: string | null
          created_at: string | null
          created_by: string | null
          expires_at: string | null
          id: string
          is_valid: boolean | null
          max_uses: number | null
          notes: string | null
          owner_id: string | null
          personal_code: boolean | null
          redemption_location: Json | null
          redemption_source: string | null
          sport_context: string | null
          team_id: string | null
          test_mode: boolean | null
          total_redemptions: number | null
          use_count: number | null
          used_at: string | null
          used_by: string | null
        }
        Insert: {
          analytics_data?: Json | null
          code: string
          code_type?: string | null
          created_at?: string | null
          created_by?: string | null
          expires_at?: string | null
          id?: string
          is_valid?: boolean | null
          max_uses?: number | null
          notes?: string | null
          owner_id?: string | null
          personal_code?: boolean | null
          redemption_location?: Json | null
          redemption_source?: string | null
          sport_context?: string | null
          team_id?: string | null
          test_mode?: boolean | null
          total_redemptions?: number | null
          use_count?: number | null
          used_at?: string | null
          used_by?: string | null
        }
        Update: {
          analytics_data?: Json | null
          code?: string
          code_type?: string | null
          created_at?: string | null
          created_by?: string | null
          expires_at?: string | null
          id?: string
          is_valid?: boolean | null
          max_uses?: number | null
          notes?: string | null
          owner_id?: string | null
          personal_code?: boolean | null
          redemption_location?: Json | null
          redemption_source?: string | null
          sport_context?: string | null
          team_id?: string | null
          test_mode?: boolean | null
          total_redemptions?: number | null
          use_count?: number | null
          used_at?: string | null
          used_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_invite_codes_team_id"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "fk_invite_codes_team_id"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "invite_codes_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invite_codes_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invite_codes_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "invite_codes_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invite_codes_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "invite_codes_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "invite_codes_used_by_fkey"
            columns: ["used_by"]
            isOneToOne: false
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      link_tracking: {
        Row: {
          created_at: string | null
          id: string
          link_type: string
          metadata: Json | null
          opened: boolean | null
          opened_at: string | null
          resource_id: string
          resource_type: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          link_type: string
          metadata?: Json | null
          opened?: boolean | null
          opened_at?: string | null
          resource_id: string
          resource_type: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          link_type?: string
          metadata?: Json | null
          opened?: boolean | null
          opened_at?: string | null
          resource_id?: string
          resource_type?: string
          user_id?: string
        }
        Relationships: []
      }
      membership_addons: {
        Row: {
          addon_id: string
          allowed: boolean | null
          created_at: string | null
          included: boolean | null
          membership_id: string
        }
        Insert: {
          addon_id: string
          allowed?: boolean | null
          created_at?: string | null
          included?: boolean | null
          membership_id: string
        }
        Update: {
          addon_id?: string
          allowed?: boolean | null
          created_at?: string | null
          included?: boolean | null
          membership_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "membership_addons_addon_id_fkey"
            columns: ["addon_id"]
            isOneToOne: false
            referencedRelation: "addons"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "membership_addons_membership_id_fkey"
            columns: ["membership_id"]
            isOneToOne: false
            referencedRelation: "memberships"
            referencedColumns: ["id"]
          },
        ]
      }
      memberships: {
        Row: {
          billing_period: Database["public"]["Enums"]["billing_period"]
          created_at: string | null
          description: string | null
          features: string[] | null
          id: string
          name: string
          price: number
          status: Database["public"]["Enums"]["membership_status"] | null
          updated_at: string | null
        }
        Insert: {
          billing_period: Database["public"]["Enums"]["billing_period"]
          created_at?: string | null
          description?: string | null
          features?: string[] | null
          id?: string
          name: string
          price: number
          status?: Database["public"]["Enums"]["membership_status"] | null
          updated_at?: string | null
        }
        Update: {
          billing_period?: Database["public"]["Enums"]["billing_period"]
          created_at?: string | null
          description?: string | null
          features?: string[] | null
          id?: string
          name?: string
          price?: number
          status?: Database["public"]["Enums"]["membership_status"] | null
          updated_at?: string | null
        }
        Relationships: []
      }
      nfc_tap_analytics: {
        Row: {
          created_at: string | null
          device_info: Json | null
          id: string
          location: Json | null
          nfc_token_id: string
          outcome: string | null
          session_metadata: Json | null
          tap_timestamp: string | null
          tapper_id: string | null
        }
        Insert: {
          created_at?: string | null
          device_info?: Json | null
          id?: string
          location?: Json | null
          nfc_token_id: string
          outcome?: string | null
          session_metadata?: Json | null
          tap_timestamp?: string | null
          tapper_id?: string | null
        }
        Update: {
          created_at?: string | null
          device_info?: Json | null
          id?: string
          location?: Json | null
          nfc_token_id?: string
          outcome?: string | null
          session_metadata?: Json | null
          tap_timestamp?: string | null
          tapper_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "nfc_tap_analytics_nfc_token_id_fkey"
            columns: ["nfc_token_id"]
            isOneToOne: false
            referencedRelation: "nfc_tokens"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "nfc_tap_analytics_tapper_id_fkey"
            columns: ["tapper_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "nfc_tap_analytics_tapper_id_fkey"
            columns: ["tapper_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "nfc_tap_analytics_tapper_id_fkey"
            columns: ["tapper_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      nfc_tokens: {
        Row: {
          activation_date: string | null
          created_at: string | null
          embedded_url: string
          id: string
          manufacturing_date: string | null
          product_info: Json | null
          sport_head_id: string
          status: string | null
          tap_stats: Json | null
          token_serial: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          activation_date?: string | null
          created_at?: string | null
          embedded_url: string
          id?: string
          manufacturing_date?: string | null
          product_info?: Json | null
          sport_head_id: string
          status?: string | null
          tap_stats?: Json | null
          token_serial: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          activation_date?: string | null
          created_at?: string | null
          embedded_url?: string
          id?: string
          manufacturing_date?: string | null
          product_info?: Json | null
          sport_head_id?: string
          status?: string | null
          tap_stats?: Json | null
          token_serial?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "nfc_tokens_sport_head_id_fkey"
            columns: ["sport_head_id"]
            isOneToOne: false
            referencedRelation: "sport_heads"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "nfc_tokens_sport_head_id_fkey"
            columns: ["sport_head_id"]
            isOneToOne: false
            referencedRelation: "user_sport_heads_complete"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "nfc_tokens_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "nfc_tokens_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "nfc_tokens_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      notification_templates: {
        Row: {
          category: string | null
          created_at: string | null
          created_by: string | null
          description: string | null
          id: string
          is_active: boolean | null
          name: string
          notification_type: string
          template_body: string
          updated_at: string | null
          variables: Json | null
        }
        Insert: {
          category?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          notification_type: string
          template_body: string
          updated_at?: string | null
          variables?: Json | null
        }
        Update: {
          category?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          notification_type?: string
          template_body?: string
          updated_at?: string | null
          variables?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "notification_templates_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notification_templates_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "notification_templates_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          content: Json
          created_at: string
          id: string
          is_read: boolean | null
          type: string
          user_id: string
        }
        Insert: {
          content: Json
          created_at?: string
          id?: string
          is_read?: boolean | null
          type: string
          user_id: string
        }
        Update: {
          content?: Json
          created_at?: string
          id?: string
          is_read?: boolean | null
          type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      parent_approval_requests: {
        Row: {
          approved_at: string | null
          child_id: string
          created_at: string | null
          expires_at: string | null
          id: string
          invite_code: string
          parent_id: string | null
          parent_invite_code: string
          status: string | null
          team_id: string
        }
        Insert: {
          approved_at?: string | null
          child_id: string
          created_at?: string | null
          expires_at?: string | null
          id?: string
          invite_code: string
          parent_id?: string | null
          parent_invite_code: string
          status?: string | null
          team_id: string
        }
        Update: {
          approved_at?: string | null
          child_id?: string
          created_at?: string | null
          expires_at?: string | null
          id?: string
          invite_code?: string
          parent_id?: string | null
          parent_invite_code?: string
          status?: string | null
          team_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "parent_approval_requests_child_id_fkey"
            columns: ["child_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "parent_approval_requests_child_id_fkey"
            columns: ["child_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "parent_approval_requests_child_id_fkey"
            columns: ["child_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "parent_approval_requests_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "parent_approval_requests_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "parent_approval_requests_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "parent_approval_requests_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "parent_approval_requests_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
        ]
      }
      parent_onboarding_sessions: {
        Row: {
          child_date_of_birth: string | null
          child_name: string | null
          child_profile_id: string | null
          completed_at: string | null
          created_at: string | null
          id: string
          parent_email: string | null
          parent_name: string | null
          parent_profile_id: string | null
          phone_verification_completed_at: string | null
          phone_verification_started_at: string | null
          session_token: string
          started_at: string | null
          status: string | null
          steps_completed: Json | null
          updated_at: string | null
          verification_method: string | null
        }
        Insert: {
          child_date_of_birth?: string | null
          child_name?: string | null
          child_profile_id?: string | null
          completed_at?: string | null
          created_at?: string | null
          id?: string
          parent_email?: string | null
          parent_name?: string | null
          parent_profile_id?: string | null
          phone_verification_completed_at?: string | null
          phone_verification_started_at?: string | null
          session_token?: string
          started_at?: string | null
          status?: string | null
          steps_completed?: Json | null
          updated_at?: string | null
          verification_method?: string | null
        }
        Update: {
          child_date_of_birth?: string | null
          child_name?: string | null
          child_profile_id?: string | null
          completed_at?: string | null
          created_at?: string | null
          id?: string
          parent_email?: string | null
          parent_name?: string | null
          parent_profile_id?: string | null
          phone_verification_completed_at?: string | null
          phone_verification_started_at?: string | null
          session_token?: string
          started_at?: string | null
          status?: string | null
          steps_completed?: Json | null
          updated_at?: string | null
          verification_method?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "parent_onboarding_sessions_child_profile_id_fkey"
            columns: ["child_profile_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "parent_onboarding_sessions_child_profile_id_fkey"
            columns: ["child_profile_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "parent_onboarding_sessions_child_profile_id_fkey"
            columns: ["child_profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "parent_onboarding_sessions_parent_profile_id_fkey"
            columns: ["parent_profile_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "parent_onboarding_sessions_parent_profile_id_fkey"
            columns: ["parent_profile_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "parent_onboarding_sessions_parent_profile_id_fkey"
            columns: ["parent_profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      pending_profiles: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          date_of_birth: string | null
          email: string
          full_name: string | null
          invite_code: string | null
          marketing_email: boolean | null
          marketing_notifications: boolean | null
          phone: string | null
          registration_metadata: Json | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          email: string
          full_name?: string | null
          invite_code?: string | null
          marketing_email?: boolean | null
          marketing_notifications?: boolean | null
          phone?: string | null
          registration_metadata?: Json | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          email?: string
          full_name?: string | null
          invite_code?: string | null
          marketing_email?: boolean | null
          marketing_notifications?: boolean | null
          phone?: string | null
          registration_metadata?: Json | null
        }
        Relationships: []
      }
      pickup_people: {
        Row: {
          child_id: string | null
          created_at: string
          id: string
          name: string
          photo_url: string | null
        }
        Insert: {
          child_id?: string | null
          created_at?: string
          id?: string
          name: string
          photo_url?: string | null
        }
        Update: {
          child_id?: string | null
          created_at?: string
          id?: string
          name?: string
          photo_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pickup_people_child_id_fkey"
            columns: ["child_id"]
            isOneToOne: false
            referencedRelation: "children"
            referencedColumns: ["id"]
          },
        ]
      }
      player_evaluation_history: {
        Row: {
          change_reason: string | null
          change_type: string
          changed_by: string
          created_at: string | null
          evaluation_id: string
          id: string
          new_notes: string | null
          new_rating: number | null
          old_notes: string | null
          old_rating: number | null
        }
        Insert: {
          change_reason?: string | null
          change_type: string
          changed_by: string
          created_at?: string | null
          evaluation_id: string
          id?: string
          new_notes?: string | null
          new_rating?: number | null
          old_notes?: string | null
          old_rating?: number | null
        }
        Update: {
          change_reason?: string | null
          change_type?: string
          changed_by?: string
          created_at?: string | null
          evaluation_id?: string
          id?: string
          new_notes?: string | null
          new_rating?: number | null
          old_notes?: string | null
          old_rating?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "player_evaluation_history_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluation_history_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "player_evaluation_history_changed_by_fkey"
            columns: ["changed_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluation_history_evaluation_id_fkey"
            columns: ["evaluation_id"]
            isOneToOne: false
            referencedRelation: "evaluation_comparisons"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluation_history_evaluation_id_fkey"
            columns: ["evaluation_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_history_view"
            referencedColumns: ["evaluation_id"]
          },
          {
            foreignKeyName: "player_evaluation_history_evaluation_id_fkey"
            columns: ["evaluation_id"]
            isOneToOne: false
            referencedRelation: "player_evaluations"
            referencedColumns: ["id"]
          },
        ]
      }
      player_evaluations: {
        Row: {
          area: string
          category: string
          created_at: string
          evaluation_criteria_data: Json | null
          evaluation_date: string
          evaluation_status: string | null
          evaluator_id: string
          event_id: string | null
          framework_version: string
          id: string
          is_event_based: boolean | null
          notes: string | null
          player_id: string
          player_notes: string | null
          player_rating: number | null
          player_submitted_at: string | null
          player_viewed: boolean | null
          player_viewed_at: string | null
          position: string
          pre_evaluation_id: string | null
          question: string | null
          rating: number
          session_id: string | null
          sport_type: string
          team_id: string
          updated_at: string
          viewed: boolean
          viewed_at: string | null
          viewed_by: string | null
          week_number: number
        }
        Insert: {
          area: string
          category: string
          created_at?: string
          evaluation_criteria_data?: Json | null
          evaluation_date: string
          evaluation_status?: string | null
          evaluator_id: string
          event_id?: string | null
          framework_version?: string
          id?: string
          is_event_based?: boolean | null
          notes?: string | null
          player_id: string
          player_notes?: string | null
          player_rating?: number | null
          player_submitted_at?: string | null
          player_viewed?: boolean | null
          player_viewed_at?: string | null
          position: string
          pre_evaluation_id?: string | null
          question?: string | null
          rating: number
          session_id?: string | null
          sport_type?: string
          team_id: string
          updated_at?: string
          viewed?: boolean
          viewed_at?: string | null
          viewed_by?: string | null
          week_number: number
        }
        Update: {
          area?: string
          category?: string
          created_at?: string
          evaluation_criteria_data?: Json | null
          evaluation_date?: string
          evaluation_status?: string | null
          evaluator_id?: string
          event_id?: string | null
          framework_version?: string
          id?: string
          is_event_based?: boolean | null
          notes?: string | null
          player_id?: string
          player_notes?: string | null
          player_rating?: number | null
          player_submitted_at?: string | null
          player_viewed?: boolean | null
          player_viewed_at?: string | null
          position?: string
          pre_evaluation_id?: string | null
          question?: string | null
          rating?: number
          session_id?: string | null
          sport_type?: string
          team_id?: string
          updated_at?: string
          viewed?: boolean
          viewed_at?: string | null
          viewed_by?: string | null
          week_number?: number
        }
        Relationships: [
          {
            foreignKeyName: "fk_player_evaluations_framework"
            columns: ["framework_version"]
            isOneToOne: false
            referencedRelation: "evaluation_framework_metadata"
            referencedColumns: ["framework_version"]
          },
          {
            foreignKeyName: "player_evaluations_evaluator_id_fkey"
            columns: ["evaluator_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_evaluator_id_fkey"
            columns: ["evaluator_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "player_evaluations_evaluator_id_fkey"
            columns: ["evaluator_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "evaluation_comparisons"
            referencedColumns: ["pre_evaluation_id"]
          },
          {
            foreignKeyName: "player_evaluations_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "player_pre_evaluation_summary"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "player_training_participation"
            referencedColumns: ["pre_evaluation_id"]
          },
          {
            foreignKeyName: "player_evaluations_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "pre_evaluations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_session_id_fkey"
            columns: ["session_id"]
            isOneToOne: false
            referencedRelation: "evaluation_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_viewed_by_fkey"
            columns: ["viewed_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_viewed_by_fkey"
            columns: ["viewed_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "player_evaluations_viewed_by_fkey"
            columns: ["viewed_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      player_stats: {
        Row: {
          created_at: string | null
          current_level: number | null
          evaluation_streak: number | null
          longest_streak: number | null
          player_id: string
          total_evaluations: number | null
          total_xp: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          current_level?: number | null
          evaluation_streak?: number | null
          longest_streak?: number | null
          player_id: string
          total_evaluations?: number | null
          total_xp?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          current_level?: number | null
          evaluation_streak?: number | null
          longest_streak?: number | null
          player_id?: string
          total_evaluations?: number | null
          total_xp?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "player_stats_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: true
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_stats_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: true
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "player_stats_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: true
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      player_xp_transactions: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          player_id: string
          source_id: string | null
          source_type: string
          xp_amount: number
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          player_id: string
          source_id?: string | null
          source_type: string
          xp_amount: number
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          player_id?: string
          source_id?: string | null
          source_type?: string
          xp_amount?: number
        }
        Relationships: [
          {
            foreignKeyName: "player_xp_transactions_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_xp_transactions_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "player_xp_transactions_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      pre_evaluation_audit_log: {
        Row: {
          action: string
          actor_id: string | null
          actor_role: string | null
          created_at: string | null
          id: string
          ip_address: unknown | null
          new_values: Json | null
          old_values: Json | null
          pre_evaluation_id: string | null
          user_agent: string | null
        }
        Insert: {
          action: string
          actor_id?: string | null
          actor_role?: string | null
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          pre_evaluation_id?: string | null
          user_agent?: string | null
        }
        Update: {
          action?: string
          actor_id?: string | null
          actor_role?: string | null
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          new_values?: Json | null
          old_values?: Json | null
          pre_evaluation_id?: string | null
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pre_evaluation_audit_log_actor_id_fkey"
            columns: ["actor_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluation_audit_log_actor_id_fkey"
            columns: ["actor_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "pre_evaluation_audit_log_actor_id_fkey"
            columns: ["actor_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluation_audit_log_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "evaluation_comparisons"
            referencedColumns: ["pre_evaluation_id"]
          },
          {
            foreignKeyName: "pre_evaluation_audit_log_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "player_pre_evaluation_summary"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluation_audit_log_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "player_training_participation"
            referencedColumns: ["pre_evaluation_id"]
          },
          {
            foreignKeyName: "pre_evaluation_audit_log_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "pre_evaluations"
            referencedColumns: ["id"]
          },
        ]
      }
      pre_evaluation_notifications: {
        Row: {
          action_url: string | null
          body: string | null
          channel: string | null
          clicked_at: string | null
          created_at: string | null
          delivered_at: string | null
          failed_at: string | null
          failure_reason: string | null
          id: string
          metadata: Json | null
          notification_type: string | null
          opened_at: string | null
          pre_evaluation_id: string | null
          recipient_email: string | null
          recipient_id: string | null
          recipient_phone: string | null
          scheduled_for: string | null
          sent_at: string | null
          status: string | null
          subject: string | null
        }
        Insert: {
          action_url?: string | null
          body?: string | null
          channel?: string | null
          clicked_at?: string | null
          created_at?: string | null
          delivered_at?: string | null
          failed_at?: string | null
          failure_reason?: string | null
          id?: string
          metadata?: Json | null
          notification_type?: string | null
          opened_at?: string | null
          pre_evaluation_id?: string | null
          recipient_email?: string | null
          recipient_id?: string | null
          recipient_phone?: string | null
          scheduled_for?: string | null
          sent_at?: string | null
          status?: string | null
          subject?: string | null
        }
        Update: {
          action_url?: string | null
          body?: string | null
          channel?: string | null
          clicked_at?: string | null
          created_at?: string | null
          delivered_at?: string | null
          failed_at?: string | null
          failure_reason?: string | null
          id?: string
          metadata?: Json | null
          notification_type?: string | null
          opened_at?: string | null
          pre_evaluation_id?: string | null
          recipient_email?: string | null
          recipient_id?: string | null
          recipient_phone?: string | null
          scheduled_for?: string | null
          sent_at?: string | null
          status?: string | null
          subject?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pre_evaluation_notifications_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "evaluation_comparisons"
            referencedColumns: ["pre_evaluation_id"]
          },
          {
            foreignKeyName: "pre_evaluation_notifications_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "player_pre_evaluation_summary"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluation_notifications_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "player_training_participation"
            referencedColumns: ["pre_evaluation_id"]
          },
          {
            foreignKeyName: "pre_evaluation_notifications_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "pre_evaluations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluation_notifications_recipient_id_fkey"
            columns: ["recipient_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluation_notifications_recipient_id_fkey"
            columns: ["recipient_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "pre_evaluation_notifications_recipient_id_fkey"
            columns: ["recipient_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      pre_evaluations: {
        Row: {
          access_token: string | null
          coach_id: string | null
          completed_at: string | null
          completion_percentage: number | null
          concerns_text: string | null
          confidence_level: number | null
          created_at: string | null
          evaluation_status: string | null
          event_id: string | null
          expires_at: string | null
          framework_version: string | null
          goals_text: string | null
          id: string
          last_reminder_at: string | null
          mood_rating: number | null
          player_id: string | null
          player_position: string | null
          points_claimed: boolean | null
          points_earned: number | null
          questions_answered: number | null
          reminder_count: number | null
          request_method: string | null
          requested_at: string | null
          requested_by: string | null
          responses: Json | null
          sport_head_id: string | null
          started_at: string | null
          status: string | null
          team_id: string | null
          template_id: string | null
          total_questions: number | null
          updated_at: string | null
          viewed: boolean | null
          viewed_at: string | null
          viewed_by: string | null
          week_number: number | null
        }
        Insert: {
          access_token?: string | null
          coach_id?: string | null
          completed_at?: string | null
          completion_percentage?: number | null
          concerns_text?: string | null
          confidence_level?: number | null
          created_at?: string | null
          evaluation_status?: string | null
          event_id?: string | null
          expires_at?: string | null
          framework_version?: string | null
          goals_text?: string | null
          id?: string
          last_reminder_at?: string | null
          mood_rating?: number | null
          player_id?: string | null
          player_position?: string | null
          points_claimed?: boolean | null
          points_earned?: number | null
          questions_answered?: number | null
          reminder_count?: number | null
          request_method?: string | null
          requested_at?: string | null
          requested_by?: string | null
          responses?: Json | null
          sport_head_id?: string | null
          started_at?: string | null
          status?: string | null
          team_id?: string | null
          template_id?: string | null
          total_questions?: number | null
          updated_at?: string | null
          viewed?: boolean | null
          viewed_at?: string | null
          viewed_by?: string | null
          week_number?: number | null
        }
        Update: {
          access_token?: string | null
          coach_id?: string | null
          completed_at?: string | null
          completion_percentage?: number | null
          concerns_text?: string | null
          confidence_level?: number | null
          created_at?: string | null
          evaluation_status?: string | null
          event_id?: string | null
          expires_at?: string | null
          framework_version?: string | null
          goals_text?: string | null
          id?: string
          last_reminder_at?: string | null
          mood_rating?: number | null
          player_id?: string | null
          player_position?: string | null
          points_claimed?: boolean | null
          points_earned?: number | null
          questions_answered?: number | null
          reminder_count?: number | null
          request_method?: string | null
          requested_at?: string | null
          requested_by?: string | null
          responses?: Json | null
          sport_head_id?: string | null
          started_at?: string | null
          status?: string | null
          team_id?: string | null
          template_id?: string | null
          total_questions?: number | null
          updated_at?: string | null
          viewed?: boolean | null
          viewed_at?: string | null
          viewed_by?: string | null
          week_number?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "pre_evaluations_coach_id_fkey"
            columns: ["coach_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_coach_id_fkey"
            columns: ["coach_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "pre_evaluations_coach_id_fkey"
            columns: ["coach_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event_comprehensive_summary"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "pre_evaluations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event_evaluation_summary"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "pre_evaluations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event_summary"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "pre_evaluations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "player_training_participation"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "pre_evaluations_framework_version_fkey"
            columns: ["framework_version"]
            isOneToOne: false
            referencedRelation: "evaluation_framework_metadata"
            referencedColumns: ["framework_version"]
          },
          {
            foreignKeyName: "pre_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "pre_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_requested_by_fkey"
            columns: ["requested_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_requested_by_fkey"
            columns: ["requested_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "pre_evaluations_requested_by_fkey"
            columns: ["requested_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_sport_head_id_fkey"
            columns: ["sport_head_id"]
            isOneToOne: false
            referencedRelation: "sport_heads"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_sport_head_id_fkey"
            columns: ["sport_head_id"]
            isOneToOne: false
            referencedRelation: "user_sport_heads_complete"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "pre_evaluations_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "pre_evaluations_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "evaluation_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_viewed_by_fkey"
            columns: ["viewed_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_viewed_by_fkey"
            columns: ["viewed_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "pre_evaluations_viewed_by_fkey"
            columns: ["viewed_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          age_transition_date: string | null
          age_verified: boolean | null
          allergies: string[] | null
          avatar_url: string | null
          communication_preferences: Json | null
          consent_records: Json | null
          context_preferences: Json | null
          date_of_birth: string | null
          display_name: string | null
          email_verified: boolean | null
          emergency_contact: Json | null
          full_name: string | null
          id: string
          is_minor: boolean | null
          last_accessed_team: string | null
          last_login: string | null
          location: string | null
          marketing_email: boolean | null
          marketing_notifications: boolean | null
          medical_conditions: string[] | null
          nickname: string | null
          notification_preferences: Json | null
          onboarding_completed: boolean | null
          parent_handoff_completed: boolean | null
          parent_id: string | null
          personal_invite_code: string | null
          phone: string | null
          phone_normalised: string | null
          phone_verified: boolean | null
          phone_verified_at: string | null
          photography_allowed: boolean | null
          preferred_contact_method: string | null
          primary_sport_head_id: string | null
          privacy_settings: Json | null
          privileges: string[] | null
          registration_metadata: Json | null
          registration_source: string | null
          sp_balance: number | null
          sport_affiliations: Json | null
          sport_head_id: string | null
          terms_accepted: Json | null
          updated_at: string | null
          username: string | null
          website: string | null
        }
        Insert: {
          age_transition_date?: string | null
          age_verified?: boolean | null
          allergies?: string[] | null
          avatar_url?: string | null
          communication_preferences?: Json | null
          consent_records?: Json | null
          context_preferences?: Json | null
          date_of_birth?: string | null
          display_name?: string | null
          email_verified?: boolean | null
          emergency_contact?: Json | null
          full_name?: string | null
          id: string
          is_minor?: boolean | null
          last_accessed_team?: string | null
          last_login?: string | null
          location?: string | null
          marketing_email?: boolean | null
          marketing_notifications?: boolean | null
          medical_conditions?: string[] | null
          nickname?: string | null
          notification_preferences?: Json | null
          onboarding_completed?: boolean | null
          parent_handoff_completed?: boolean | null
          parent_id?: string | null
          personal_invite_code?: string | null
          phone?: string | null
          phone_normalised?: string | null
          phone_verified?: boolean | null
          phone_verified_at?: string | null
          photography_allowed?: boolean | null
          preferred_contact_method?: string | null
          primary_sport_head_id?: string | null
          privacy_settings?: Json | null
          privileges?: string[] | null
          registration_metadata?: Json | null
          registration_source?: string | null
          sp_balance?: number | null
          sport_affiliations?: Json | null
          sport_head_id?: string | null
          terms_accepted?: Json | null
          updated_at?: string | null
          username?: string | null
          website?: string | null
        }
        Update: {
          age_transition_date?: string | null
          age_verified?: boolean | null
          allergies?: string[] | null
          avatar_url?: string | null
          communication_preferences?: Json | null
          consent_records?: Json | null
          context_preferences?: Json | null
          date_of_birth?: string | null
          display_name?: string | null
          email_verified?: boolean | null
          emergency_contact?: Json | null
          full_name?: string | null
          id?: string
          is_minor?: boolean | null
          last_accessed_team?: string | null
          last_login?: string | null
          location?: string | null
          marketing_email?: boolean | null
          marketing_notifications?: boolean | null
          medical_conditions?: string[] | null
          nickname?: string | null
          notification_preferences?: Json | null
          onboarding_completed?: boolean | null
          parent_handoff_completed?: boolean | null
          parent_id?: string | null
          personal_invite_code?: string | null
          phone?: string | null
          phone_normalised?: string | null
          phone_verified?: boolean | null
          phone_verified_at?: string | null
          photography_allowed?: boolean | null
          preferred_contact_method?: string | null
          primary_sport_head_id?: string | null
          privacy_settings?: Json | null
          privileges?: string[] | null
          registration_metadata?: Json | null
          registration_source?: string | null
          sp_balance?: number | null
          sport_affiliations?: Json | null
          sport_head_id?: string | null
          terms_accepted?: Json | null
          updated_at?: string | null
          username?: string | null
          website?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_last_accessed_team_fkey"
            columns: ["last_accessed_team"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "profiles_last_accessed_team_fkey"
            columns: ["last_accessed_team"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "profiles_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "profiles_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      rpc_debug_logs: {
        Row: {
          error_message: string | null
          function_name: string | null
          headers: Json | null
          id: number
          raw_payload: string | null
          timestamp: string | null
        }
        Insert: {
          error_message?: string | null
          function_name?: string | null
          headers?: Json | null
          id?: number
          raw_payload?: string | null
          timestamp?: string | null
        }
        Update: {
          error_message?: string | null
          function_name?: string | null
          headers?: Json | null
          id?: number
          raw_payload?: string | null
          timestamp?: string | null
        }
        Relationships: []
      }
      sms_queue: {
        Row: {
          api_version: string | null
          context: Json | null
          created_at: string | null
          error_message: string | null
          event_id: string | null
          failed_at: string | null
          id: string
          last_error: string | null
          max_attempts: number | null
          message_body: string
          notification_type: string | null
          phone_number: string
          pre_evaluation_id: string | null
          processed: boolean | null
          processed_at: string | null
          recipient_name: string | null
          retry_count: number | null
          scheduled_for: string | null
          sent_at: string | null
          status: string | null
          team_id: string | null
          updated_at: string | null
        }
        Insert: {
          api_version?: string | null
          context?: Json | null
          created_at?: string | null
          error_message?: string | null
          event_id?: string | null
          failed_at?: string | null
          id?: string
          last_error?: string | null
          max_attempts?: number | null
          message_body: string
          notification_type?: string | null
          phone_number: string
          pre_evaluation_id?: string | null
          processed?: boolean | null
          processed_at?: string | null
          recipient_name?: string | null
          retry_count?: number | null
          scheduled_for?: string | null
          sent_at?: string | null
          status?: string | null
          team_id?: string | null
          updated_at?: string | null
        }
        Update: {
          api_version?: string | null
          context?: Json | null
          created_at?: string | null
          error_message?: string | null
          event_id?: string | null
          failed_at?: string | null
          id?: string
          last_error?: string | null
          max_attempts?: number | null
          message_body?: string
          notification_type?: string | null
          phone_number?: string
          pre_evaluation_id?: string | null
          processed?: boolean | null
          processed_at?: string | null
          recipient_name?: string | null
          retry_count?: number | null
          scheduled_for?: string | null
          sent_at?: string | null
          status?: string | null
          team_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "sms_queue_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event_comprehensive_summary"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "sms_queue_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event_evaluation_summary"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "sms_queue_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event_summary"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "sms_queue_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sms_queue_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "player_training_participation"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "sms_queue_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "evaluation_comparisons"
            referencedColumns: ["pre_evaluation_id"]
          },
          {
            foreignKeyName: "sms_queue_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "player_pre_evaluation_summary"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sms_queue_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "player_training_participation"
            referencedColumns: ["pre_evaluation_id"]
          },
          {
            foreignKeyName: "sms_queue_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "pre_evaluations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sms_queue_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "sms_queue_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
        ]
      }
      sport_heads: {
        Row: {
          achievement_points: number | null
          achievements: Json | null
          avatar_style: Json | null
          average_coach_rating: number | null
          average_self_rating: number | null
          avg_response_time: number | null
          created_at: string | null
          creation_date: string | null
          customization: Json | null
          display_name: string
          equipped_badges: string[] | null
          equipped_theme: string | null
          evaluation_velocity: number | null
          experience_points: number | null
          id: string
          improvement_trend: number | null
          is_primary: boolean | null
          last_activity_date: string | null
          last_used: string | null
          level: number | null
          longest_streak: number | null
          nfc_linked: boolean | null
          nft_metadata_generated: boolean | null
          peak_rating: number | null
          peak_rating_date: string | null
          position: string | null
          prestige_level: number | null
          rarity_score: number | null
          rating_gap: number | null
          response_rate: number | null
          sport: string
          sport_metadata: Json | null
          streak_days: number | null
          token_id: string | null
          total_active_days: number | null
          total_evaluations: number | null
          unlocked_badges: Json | null
          unlocked_themes: Json | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          achievement_points?: number | null
          achievements?: Json | null
          avatar_style?: Json | null
          average_coach_rating?: number | null
          average_self_rating?: number | null
          avg_response_time?: number | null
          created_at?: string | null
          creation_date?: string | null
          customization?: Json | null
          display_name: string
          equipped_badges?: string[] | null
          equipped_theme?: string | null
          evaluation_velocity?: number | null
          experience_points?: number | null
          id?: string
          improvement_trend?: number | null
          is_primary?: boolean | null
          last_activity_date?: string | null
          last_used?: string | null
          level?: number | null
          longest_streak?: number | null
          nfc_linked?: boolean | null
          nft_metadata_generated?: boolean | null
          peak_rating?: number | null
          peak_rating_date?: string | null
          position?: string | null
          prestige_level?: number | null
          rarity_score?: number | null
          rating_gap?: number | null
          response_rate?: number | null
          sport: string
          sport_metadata?: Json | null
          streak_days?: number | null
          token_id?: string | null
          total_active_days?: number | null
          total_evaluations?: number | null
          unlocked_badges?: Json | null
          unlocked_themes?: Json | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          achievement_points?: number | null
          achievements?: Json | null
          avatar_style?: Json | null
          average_coach_rating?: number | null
          average_self_rating?: number | null
          avg_response_time?: number | null
          created_at?: string | null
          creation_date?: string | null
          customization?: Json | null
          display_name?: string
          equipped_badges?: string[] | null
          equipped_theme?: string | null
          evaluation_velocity?: number | null
          experience_points?: number | null
          id?: string
          improvement_trend?: number | null
          is_primary?: boolean | null
          last_activity_date?: string | null
          last_used?: string | null
          level?: number | null
          longest_streak?: number | null
          nfc_linked?: boolean | null
          nft_metadata_generated?: boolean | null
          peak_rating?: number | null
          peak_rating_date?: string | null
          position?: string | null
          prestige_level?: number | null
          rarity_score?: number | null
          rating_gap?: number | null
          response_rate?: number | null
          sport?: string
          sport_metadata?: Json | null
          streak_days?: number | null
          token_id?: string | null
          total_active_days?: number | null
          total_evaluations?: number | null
          unlocked_badges?: Json | null
          unlocked_themes?: Json | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "sport_heads_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sport_heads_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "sport_heads_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      sport_types: {
        Row: {
          created_at: string | null
          id: number
          is_active: boolean | null
          sport_code: string
          sport_name: string
          sport_positions: Json | null
          theme_config: Json | null
        }
        Insert: {
          created_at?: string | null
          id?: number
          is_active?: boolean | null
          sport_code: string
          sport_name: string
          sport_positions?: Json | null
          theme_config?: Json | null
        }
        Update: {
          created_at?: string | null
          id?: number
          is_active?: boolean | null
          sport_code?: string
          sport_name?: string
          sport_positions?: Json | null
          theme_config?: Json | null
        }
        Relationships: []
      }
      team_coaches: {
        Row: {
          assigned_at: string | null
          assigned_by: string | null
          coach_assignment_id: string
          is_primary: boolean | null
          notes: string | null
          removed_at: string | null
          removed_by: string | null
          role: string
          status: string | null
          team_id: string | null
          user_id: string | null
        }
        Insert: {
          assigned_at?: string | null
          assigned_by?: string | null
          coach_assignment_id: string
          is_primary?: boolean | null
          notes?: string | null
          removed_at?: string | null
          removed_by?: string | null
          role: string
          status?: string | null
          team_id?: string | null
          user_id?: string | null
        }
        Update: {
          assigned_at?: string | null
          assigned_by?: string | null
          coach_assignment_id?: string
          is_primary?: boolean | null
          notes?: string | null
          removed_at?: string | null
          removed_by?: string | null
          role?: string
          status?: string | null
          team_id?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "team_coaches_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_coaches_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "team_coaches_assigned_by_fkey"
            columns: ["assigned_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_coaches_removed_by_fkey"
            columns: ["removed_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_coaches_removed_by_fkey"
            columns: ["removed_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "team_coaches_removed_by_fkey"
            columns: ["removed_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_coaches_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "team_coaches_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "team_coaches_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_coaches_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "team_coaches_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      team_join_requests: {
        Row: {
          created_at: string
          id: string
          invite_code_id: string
          notes: string | null
          parent_id: string | null
          player_id: string
          processed_at: string | null
          requested_by: string
          status: string
          team_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          invite_code_id: string
          notes?: string | null
          parent_id?: string | null
          player_id: string
          processed_at?: string | null
          requested_by: string
          status?: string
          team_id: string
        }
        Update: {
          created_at?: string
          id?: string
          invite_code_id?: string
          notes?: string | null
          parent_id?: string | null
          player_id?: string
          processed_at?: string | null
          requested_by?: string
          status?: string
          team_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_join_requests_invite_code_id_fkey"
            columns: ["invite_code_id"]
            isOneToOne: false
            referencedRelation: "invite_codes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_join_requests_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_join_requests_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "team_join_requests_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_join_requests_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_join_requests_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "team_join_requests_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_join_requests_requested_by_fkey"
            columns: ["requested_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_join_requests_requested_by_fkey"
            columns: ["requested_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "team_join_requests_requested_by_fkey"
            columns: ["requested_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_join_requests_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "team_join_requests_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
        ]
      }
      team_members: {
        Row: {
          accepted_at: string | null
          invited_by: string | null
          jersey_number: number | null
          joined_at: string | null
          left_at: string | null
          membership_id: string
          position: string | null
          role: string | null
          status: string | null
          team_id: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          accepted_at?: string | null
          invited_by?: string | null
          jersey_number?: number | null
          joined_at?: string | null
          left_at?: string | null
          membership_id?: string
          position?: string | null
          role?: string | null
          status?: string | null
          team_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          accepted_at?: string | null
          invited_by?: string | null
          jersey_number?: number | null
          joined_at?: string | null
          left_at?: string | null
          membership_id?: string
          position?: string | null
          role?: string | null
          status?: string | null
          team_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "team_members_invited_by_fkey"
            columns: ["invited_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_members_invited_by_fkey"
            columns: ["invited_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "team_members_invited_by_fkey"
            columns: ["invited_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_members_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "team_members_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "team_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "team_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      team_seasons: {
        Row: {
          created_at: string | null
          end_date: string | null
          season_id: string
          season_name: string
          start_date: string
          stats: Json | null
          status: string | null
          team_id: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          end_date?: string | null
          season_id: string
          season_name: string
          start_date: string
          stats?: Json | null
          status?: string | null
          team_id?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          end_date?: string | null
          season_id?: string
          season_name?: string
          start_date?: string
          stats?: Json | null
          status?: string | null
          team_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "team_seasons_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "team_seasons_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
        ]
      }
      teams: {
        Row: {
          age_group: string
          club_id: string | null
          color_primary: string | null
          color_secondary: string | null
          created_at: string | null
          created_by: string | null
          description: string | null
          home_location: string | null
          is_active: boolean | null
          is_individual: boolean | null
          last_activity: string | null
          logo_url: string | null
          season: string | null
          sport_type: string
          status: string | null
          team_id: string
          team_name: string
          updated_at: string | null
        }
        Insert: {
          age_group: string
          club_id?: string | null
          color_primary?: string | null
          color_secondary?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          home_location?: string | null
          is_active?: boolean | null
          is_individual?: boolean | null
          last_activity?: string | null
          logo_url?: string | null
          season?: string | null
          sport_type: string
          status?: string | null
          team_id: string
          team_name: string
          updated_at?: string | null
        }
        Update: {
          age_group?: string
          club_id?: string | null
          color_primary?: string | null
          color_secondary?: string | null
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          home_location?: string | null
          is_active?: boolean | null
          is_individual?: boolean | null
          last_activity?: string | null
          logo_url?: string | null
          season?: string | null
          sport_type?: string
          status?: string | null
          team_id?: string
          team_name?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "teams_club_id_fkey"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["club_id"]
          },
          {
            foreignKeyName: "teams_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "teams_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "teams_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      test_configuration: {
        Row: {
          created_at: string | null
          created_by: string | null
          description: string | null
          enabled: boolean
          id: string
          setting_name: string
          setting_value: Json
          updated_at: string | null
          updated_by: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          enabled?: boolean
          id?: string
          setting_name: string
          setting_value?: Json
          updated_at?: string | null
          updated_by?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          description?: string | null
          enabled?: boolean
          id?: string
          setting_name?: string
          setting_value?: Json
          updated_at?: string | null
          updated_by?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "test_configuration_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "test_configuration_updated_by_fkey"
            columns: ["updated_by"]
            isOneToOne: false
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      test_postgrest_access: {
        Row: {
          id: string
          test_value: string | null
        }
        Insert: {
          id?: string
          test_value?: string | null
        }
        Update: {
          id?: string
          test_value?: string | null
        }
        Relationships: []
      }
      trigger_debug_log: {
        Row: {
          created_at: string | null
          debug_data: Json | null
          error_message: string | null
          id: string
          stack_trace: string | null
          step_name: string | null
          step_number: number | null
          trigger_name: string
          trigger_operation: string
          user_email: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          debug_data?: Json | null
          error_message?: string | null
          id?: string
          stack_trace?: string | null
          step_name?: string | null
          step_number?: number | null
          trigger_name: string
          trigger_operation: string
          user_email?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          debug_data?: Json | null
          error_message?: string | null
          id?: string
          stack_trace?: string | null
          step_name?: string | null
          step_number?: number | null
          trigger_name?: string
          trigger_operation?: string
          user_email?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      user_addons: {
        Row: {
          addon_id: string | null
          billing_period: Database["public"]["Enums"]["billing_period"]
          created_at: string | null
          end_date: string | null
          id: string
          start_date: string | null
          status: Database["public"]["Enums"]["subscription_status"] | null
          stripe_subscription_id: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          addon_id?: string | null
          billing_period?: Database["public"]["Enums"]["billing_period"]
          created_at?: string | null
          end_date?: string | null
          id?: string
          start_date?: string | null
          status?: Database["public"]["Enums"]["subscription_status"] | null
          stripe_subscription_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          addon_id?: string | null
          billing_period?: Database["public"]["Enums"]["billing_period"]
          created_at?: string | null
          end_date?: string | null
          id?: string
          start_date?: string | null
          status?: Database["public"]["Enums"]["subscription_status"] | null
          stripe_subscription_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_addons_addon_id_fkey"
            columns: ["addon_id"]
            isOneToOne: false
            referencedRelation: "addons"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_addons_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_contexts: {
        Row: {
          context_id: string | null
          context_type: string
          created_at: string | null
          id: string
          is_active: boolean | null
          is_primary: boolean | null
          metadata: Json | null
          permissions: Json | null
          role: string
          sport: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          context_id?: string | null
          context_type: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          is_primary?: boolean | null
          metadata?: Json | null
          permissions?: Json | null
          role: string
          sport?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          context_id?: string | null
          context_type?: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          is_primary?: boolean | null
          metadata?: Json | null
          permissions?: Json | null
          role?: string
          sport?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_contexts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_contexts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "user_contexts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_memberships: {
        Row: {
          billing_period: Database["public"]["Enums"]["billing_period"]
          created_at: string | null
          end_date: string | null
          id: string
          membership_id: string | null
          start_date: string | null
          status: Database["public"]["Enums"]["subscription_status"] | null
          stripe_subscription_id: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          billing_period?: Database["public"]["Enums"]["billing_period"]
          created_at?: string | null
          end_date?: string | null
          id?: string
          membership_id?: string | null
          start_date?: string | null
          status?: Database["public"]["Enums"]["subscription_status"] | null
          stripe_subscription_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          billing_period?: Database["public"]["Enums"]["billing_period"]
          created_at?: string | null
          end_date?: string | null
          id?: string
          membership_id?: string | null
          start_date?: string | null
          status?: Database["public"]["Enums"]["subscription_status"] | null
          stripe_subscription_id?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_memberships_membership_id_fkey"
            columns: ["membership_id"]
            isOneToOne: false
            referencedRelation: "memberships"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_memberships_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      api_keys: {
        Row: {
          created_at: string | null
          description: string | null
          expires_at: string | null
          id: string | null
          is_active: boolean | null
          key_hash: string | null
          last_used_at: string | null
          metadata: Json | null
          permissions: Json | null
          rate_limit_per_hour: number | null
          service_name: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          expires_at?: string | null
          id?: string | null
          is_active?: boolean | null
          key_hash?: string | null
          last_used_at?: string | null
          metadata?: Json | null
          permissions?: Json | null
          rate_limit_per_hour?: number | null
          service_name?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          expires_at?: string | null
          id?: string | null
          is_active?: boolean | null
          key_hash?: string | null
          last_used_at?: string | null
          metadata?: Json | null
          permissions?: Json | null
          rate_limit_per_hour?: number | null
          service_name?: string | null
        }
        Relationships: []
      }
      cart_sessions: {
        Row: {
          billing_address: Json | null
          coupon_codes: string[] | null
          created_at: string | null
          discount_total: number | null
          expires_at: string | null
          grand_total: number | null
          id: string | null
          items: Json | null
          last_activity_at: string | null
          metadata: Json | null
          notes: string | null
          profile_id: string | null
          selected_shipping_option: Json | null
          session_id: string | null
          shipping_address: Json | null
          shipping_total: number | null
          subtotal: number | null
          tax_total: number | null
          updated_at: string | null
        }
        Insert: {
          billing_address?: Json | null
          coupon_codes?: string[] | null
          created_at?: string | null
          discount_total?: number | null
          expires_at?: string | null
          grand_total?: number | null
          id?: string | null
          items?: Json | null
          last_activity_at?: string | null
          metadata?: Json | null
          notes?: string | null
          profile_id?: string | null
          selected_shipping_option?: Json | null
          session_id?: string | null
          shipping_address?: Json | null
          shipping_total?: number | null
          subtotal?: number | null
          tax_total?: number | null
          updated_at?: string | null
        }
        Update: {
          billing_address?: Json | null
          coupon_codes?: string[] | null
          created_at?: string | null
          discount_total?: number | null
          expires_at?: string | null
          grand_total?: number | null
          id?: string | null
          items?: Json | null
          last_activity_at?: string | null
          metadata?: Json | null
          notes?: string | null
          profile_id?: string | null
          selected_shipping_option?: Json | null
          session_id?: string | null
          shipping_address?: Json | null
          shipping_total?: number | null
          subtotal?: number | null
          tax_total?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "cart_sessions_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "cart_sessions_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "cart_sessions_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      children_view: {
        Row: {
          allergies: string[] | null
          avatar_url: string | null
          created_at: string | null
          date_of_birth: string | null
          emergency_contact: Json | null
          id: string | null
          medical_conditions: string[] | null
          name: string | null
          photography_allowed: boolean | null
          profile_id: string | null
          updated_at: string | null
        }
        Insert: {
          allergies?: string[] | null
          avatar_url?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          emergency_contact?: Json | null
          id?: string | null
          medical_conditions?: string[] | null
          name?: string | null
          photography_allowed?: boolean | null
          profile_id?: string | null
          updated_at?: string | null
        }
        Update: {
          allergies?: string[] | null
          avatar_url?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          emergency_contact?: Json | null
          id?: string | null
          medical_conditions?: string[] | null
          name?: string | null
          photography_allowed?: boolean | null
          profile_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey"
            columns: ["id"]
            isOneToOne: true
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_parent_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_parent_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "profiles_parent_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      coach_teams_debug: {
        Row: {
          age_group: string | null
          club_name: string | null
          coach_id: string | null
          coach_name: string | null
          coach_role: string | null
          coach_status: string | null
          team_id: string | null
          team_name: string | null
        }
        Relationships: [
          {
            foreignKeyName: "team_coaches_user_id_fkey"
            columns: ["coach_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_coaches_user_id_fkey"
            columns: ["coach_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "team_coaches_user_id_fkey"
            columns: ["coach_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      evaluation_comparisons: {
        Row: {
          area: string | null
          category: string | null
          coach_id: string | null
          coach_name: string | null
          coach_notes: string | null
          coach_rating: number | null
          coach_rating_at: string | null
          event_id: string | null
          id: string | null
          player_id: string | null
          player_name: string | null
          player_notes: string | null
          player_rating: number | null
          player_submitted_at: string | null
          position: string | null
          pre_evaluation_id: string | null
          question: string | null
          rating_difference: number | null
          team_name: string | null
        }
        Relationships: [
          {
            foreignKeyName: "player_evaluations_evaluator_id_fkey"
            columns: ["coach_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_evaluator_id_fkey"
            columns: ["coach_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "player_evaluations_evaluator_id_fkey"
            columns: ["coach_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      event_comprehensive_summary: {
        Row: {
          attended_count: number | null
          club_id: string | null
          confirmed_count: number | null
          declined_count: number | null
          end_datetime: string | null
          event_id: string | null
          event_name: string | null
          event_status: Database["public"]["Enums"]["event_status"] | null
          event_type: string | null
          first_post_eval_created: string | null
          first_pre_eval_requested: string | null
          invited_count: number | null
          is_pre_session_evaluation: boolean | null
          last_post_eval_created: string | null
          last_pre_eval_completed: string | null
          location_name: string | null
          overall_evaluation_status: string | null
          post_eval_avg_rating: number | null
          post_eval_completed_players: number | null
          post_eval_completion_percentage: number | null
          post_eval_sessions: number | null
          post_eval_total_ratings: number | null
          pre_eval_completed: number | null
          pre_eval_completion_percentage: number | null
          pre_eval_in_progress: number | null
          pre_eval_pending: number | null
          pre_eval_total: number | null
          sport_framework: string | null
          start_datetime: string | null
          team_id: string | null
          total_participants: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_events_club"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["club_id"]
          },
          {
            foreignKeyName: "fk_events_team"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "fk_events_team"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
        ]
      }
      event_evaluation_summary: {
        Row: {
          attended_participants: number | null
          avg_overall_rating: number | null
          completion_percentage: number | null
          evaluated_participants: number | null
          evaluation_sessions: number | null
          evaluation_status: string | null
          event_id: string | null
          event_name: string | null
          event_type: string | null
          first_evaluation_date: string | null
          last_evaluation_date: string | null
          max_rating: number | null
          min_rating: number | null
          start_datetime: string | null
          team_id: string | null
          total_individual_evaluations: number | null
          total_participants: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_events_team"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "fk_events_team"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
        ]
      }
      event_summary: {
        Row: {
          age_group: string | null
          attended_count: number | null
          club_id: string | null
          club_name: string | null
          coach_eval_completed_count: number | null
          coach_eval_draft_count: number | null
          created_at: string | null
          end_datetime: string | null
          event_id: string | null
          event_name: string | null
          event_status: Database["public"]["Enums"]["event_status"] | null
          event_type: string | null
          invited_count: number | null
          is_pre_session_evaluation: boolean | null
          location_address: string | null
          location_name: string | null
          pre_eval_completed_count: number | null
          pre_eval_total_count: number | null
          sport_type: string | null
          start_datetime: string | null
          team_id: string | null
          team_name: string | null
          updated_at: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_events_club"
            columns: ["club_id"]
            isOneToOne: false
            referencedRelation: "clubs"
            referencedColumns: ["club_id"]
          },
          {
            foreignKeyName: "fk_events_team"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "fk_events_team"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
        ]
      }
      guardian_settings: {
        Row: {
          allowed_categories: number[] | null
          blocked_categories: number[] | null
          child_profile_id: string | null
          created_at: string | null
          guardian_profile_id: string | null
          id: string | null
          max_purchase_amount: number | null
          notification_preferences: Json | null
          purchase_approval_required: boolean | null
          updated_at: string | null
        }
        Insert: {
          allowed_categories?: number[] | null
          blocked_categories?: number[] | null
          child_profile_id?: string | null
          created_at?: string | null
          guardian_profile_id?: string | null
          id?: string | null
          max_purchase_amount?: number | null
          notification_preferences?: Json | null
          purchase_approval_required?: boolean | null
          updated_at?: string | null
        }
        Update: {
          allowed_categories?: number[] | null
          blocked_categories?: number[] | null
          child_profile_id?: string | null
          created_at?: string | null
          guardian_profile_id?: string | null
          id?: string | null
          max_purchase_amount?: number | null
          notification_preferences?: Json | null
          purchase_approval_required?: boolean | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "guardian_settings_child_profile_id_fkey"
            columns: ["child_profile_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guardian_settings_child_profile_id_fkey"
            columns: ["child_profile_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "guardian_settings_child_profile_id_fkey"
            columns: ["child_profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guardian_settings_guardian_profile_id_fkey"
            columns: ["guardian_profile_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "guardian_settings_guardian_profile_id_fkey"
            columns: ["guardian_profile_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "guardian_settings_guardian_profile_id_fkey"
            columns: ["guardian_profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      invite_code_analytics: {
        Row: {
          actual_redemptions: number | null
          code: string | null
          created_by: string | null
          creator_name: string | null
          creator_sport_head_id: string | null
          manual_redemptions: number | null
          nfc_redemptions: number | null
          personal_code: boolean | null
          qr_redemptions: number | null
          sport_context: string | null
          total_redemptions: number | null
        }
        Relationships: [
          {
            foreignKeyName: "invite_codes_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      link_tracking_analytics: {
        Row: {
          avg_hours_to_open: number | null
          resource_type: string | null
          total_opens: number | null
          unique_resources_opened: number | null
          unique_users: number | null
        }
        Relationships: []
      }
      player_evaluation_history_view: {
        Row: {
          age_group: string | null
          area: string | null
          avg_physical_coach: number | null
          avg_physical_self: number | null
          avg_positional_coach: number | null
          avg_positional_self: number | null
          avg_psychological_coach: number | null
          avg_psychological_self: number | null
          avg_social_coach: number | null
          avg_social_self: number | null
          avg_technical_coach: number | null
          avg_technical_self: number | null
          category: string | null
          coach_evaluations_count: number | null
          coach_notes: string | null
          coach_rating: number | null
          created_at: string | null
          evaluation_date: string | null
          evaluation_id: string | null
          evaluation_status: string | null
          evaluator_id: string | null
          evaluator_name: string | null
          event_date: string | null
          event_id: string | null
          event_location: string | null
          event_name: string | null
          event_type: string | null
          first_evaluation_date: string | null
          framework_version: string | null
          latest_evaluation_date: string | null
          overall_avg_coach_rating: number | null
          overall_avg_rating_difference: number | null
          overall_avg_self_rating: number | null
          player_avatar: string | null
          player_display_name: string | null
          player_id: string | null
          player_name: string | null
          position: string | null
          pre_evaluation_id: string | null
          rating_difference: number | null
          self_evaluations_count: number | null
          self_notes: string | null
          self_rating: number | null
          sport_type: string | null
          team_id: string | null
          team_name: string | null
          team_sport_type: string | null
          total_evaluations: number | null
          total_events: number | null
          viewed: boolean | null
          viewed_at: string | null
          week_number: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_player_evaluations_framework"
            columns: ["framework_version"]
            isOneToOne: false
            referencedRelation: "evaluation_framework_metadata"
            referencedColumns: ["framework_version"]
          },
          {
            foreignKeyName: "player_evaluations_evaluator_id_fkey"
            columns: ["evaluator_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_evaluator_id_fkey"
            columns: ["evaluator_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "player_evaluations_evaluator_id_fkey"
            columns: ["evaluator_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "evaluation_comparisons"
            referencedColumns: ["pre_evaluation_id"]
          },
          {
            foreignKeyName: "player_evaluations_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "player_pre_evaluation_summary"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "player_training_participation"
            referencedColumns: ["pre_evaluation_id"]
          },
          {
            foreignKeyName: "player_evaluations_pre_evaluation_id_fkey"
            columns: ["pre_evaluation_id"]
            isOneToOne: false
            referencedRelation: "pre_evaluations"
            referencedColumns: ["id"]
          },
        ]
      }
      player_evaluation_stats: {
        Row: {
          current_level: number | null
          current_streak: number | null
          display_name: string | null
          full_name: string | null
          longest_streak: number | null
          player_id: string | null
          total_evaluations: number | null
          total_xp: number | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_id_fkey"
            columns: ["player_id"]
            isOneToOne: true
            referencedRelation: "users_without_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      player_evaluation_summary_by_event: {
        Row: {
          age_group: string | null
          any_viewed: boolean | null
          avg_coach_rating: number | null
          avg_rating_difference: number | null
          avg_self_rating: number | null
          categories_evaluated: number | null
          category_breakdown: Json | null
          coach_evaluations: number | null
          evaluation_count: number | null
          event_date: string | null
          event_id: string | null
          event_location: string | null
          event_name: string | null
          event_type: string | null
          last_viewed_at: string | null
          latest_evaluation_created: string | null
          player_avatar: string | null
          player_display_name: string | null
          player_id: string | null
          player_name: string | null
          self_evaluations: number | null
          sport_type: string | null
          team_id: string | null
          team_name: string | null
        }
        Relationships: [
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      player_pre_evaluation_summary: {
        Row: {
          avg_coach_rating: number | null
          avg_rating_difference: number | null
          avg_self_rating: number | null
          coach_id: string | null
          coach_name: string | null
          completed_at: string | null
          completion_percentage: number | null
          evaluation_status: string | null
          event_id: string | null
          framework_version: string | null
          id: string | null
          last_reminder_at: string | null
          player_id: string | null
          player_name: string | null
          player_position: string | null
          questions_answered: number | null
          reminder_count: number | null
          requested_at: string | null
          requested_by: string | null
          requested_by_name: string | null
          started_at: string | null
          status: string | null
          team_id: string | null
          template_id: string | null
          total_questions: number | null
          viewed: boolean | null
          viewed_at: string | null
          viewed_by: string | null
          viewed_by_name: string | null
          week_number: number | null
        }
        Relationships: [
          {
            foreignKeyName: "pre_evaluations_coach_id_fkey"
            columns: ["coach_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_coach_id_fkey"
            columns: ["coach_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "pre_evaluations_coach_id_fkey"
            columns: ["coach_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event_comprehensive_summary"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "pre_evaluations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event_evaluation_summary"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "pre_evaluations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "event_summary"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "pre_evaluations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "player_training_participation"
            referencedColumns: ["event_id"]
          },
          {
            foreignKeyName: "pre_evaluations_framework_version_fkey"
            columns: ["framework_version"]
            isOneToOne: false
            referencedRelation: "evaluation_framework_metadata"
            referencedColumns: ["framework_version"]
          },
          {
            foreignKeyName: "pre_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "pre_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_requested_by_fkey"
            columns: ["requested_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_requested_by_fkey"
            columns: ["requested_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "pre_evaluations_requested_by_fkey"
            columns: ["requested_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "pre_evaluations_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "pre_evaluations_template_id_fkey"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "evaluation_templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_viewed_by_fkey"
            columns: ["viewed_by"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pre_evaluations_viewed_by_fkey"
            columns: ["viewed_by"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "pre_evaluations_viewed_by_fkey"
            columns: ["viewed_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      player_progress_timeline: {
        Row: {
          avg_coach_rating: number | null
          avg_difference: number | null
          avg_self_rating: number | null
          category: string | null
          coach_rating_change: number | null
          evaluation_count: number | null
          events_count: number | null
          month: string | null
          month_label: string | null
          overall_coach_avg: number | null
          overall_self_avg: number | null
          player_display_name: string | null
          player_id: string | null
          player_name: string | null
          prev_coach_rating: number | null
          prev_self_rating: number | null
          self_rating_change: number | null
          total_evaluations: number | null
        }
        Relationships: [
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "player_evaluations_player_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      player_training_participation: {
        Row: {
          attendance_confirmed_at: string | null
          attended: boolean | null
          avg_coach_rating: number | null
          avg_self_rating: number | null
          coach_evaluations_count: number | null
          end_datetime: string | null
          event_id: string | null
          event_name: string | null
          event_status: Database["public"]["Enums"]["event_status"] | null
          event_type: string | null
          has_coach_evaluation: boolean | null
          has_pre_evaluation: boolean | null
          invitation_status: string | null
          invited_at: string | null
          jersey_number: number | null
          latest_coach_evaluation_at: string | null
          location_name: string | null
          participant_id: string | null
          player_id: string | null
          player_joined_date: string | null
          player_position: string | null
          pre_eval_completed_at: string | null
          pre_eval_completion_percentage: number | null
          pre_eval_completion_status: string | null
          pre_eval_questions_answered: number | null
          pre_eval_requested_at: string | null
          pre_eval_started_at: string | null
          pre_eval_status: string | null
          pre_eval_total_questions: number | null
          pre_evaluation_id: string | null
          responded_at: string | null
          start_datetime: string | null
          team_id: string | null
          unique_coach_evaluators: number | null
          view_generated_at: string | null
        }
        Relationships: [
          {
            foreignKeyName: "team_members_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "coach_teams_debug"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "team_members_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["team_id"]
          },
          {
            foreignKeyName: "team_members_user_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_members_user_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "team_members_user_id_fkey"
            columns: ["player_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      product_cache: {
        Row: {
          availability: string | null
          bc_product_id: number | null
          brand_id: number | null
          brand_name: string | null
          cache_ttl_minutes: number | null
          cached_at: string | null
          categories: Json | null
          condition: string | null
          cost_price: number | null
          created_at: string | null
          custom_fields: Json | null
          description: string | null
          id: string | null
          images: Json | null
          inventory_level: number | null
          inventory_tracking: string | null
          is_featured: boolean | null
          is_visible: boolean | null
          meta_keywords: string[] | null
          name: string | null
          price: number | null
          retail_price: number | null
          sale_price: number | null
          search_keywords: string | null
          sku: string | null
          sort_order: number | null
          updated_at: string | null
          variants: Json | null
          weight: number | null
        }
        Insert: {
          availability?: string | null
          bc_product_id?: number | null
          brand_id?: number | null
          brand_name?: string | null
          cache_ttl_minutes?: number | null
          cached_at?: string | null
          categories?: Json | null
          condition?: string | null
          cost_price?: number | null
          created_at?: string | null
          custom_fields?: Json | null
          description?: string | null
          id?: string | null
          images?: Json | null
          inventory_level?: number | null
          inventory_tracking?: string | null
          is_featured?: boolean | null
          is_visible?: boolean | null
          meta_keywords?: string[] | null
          name?: string | null
          price?: number | null
          retail_price?: number | null
          sale_price?: number | null
          search_keywords?: string | null
          sku?: string | null
          sort_order?: number | null
          updated_at?: string | null
          variants?: Json | null
          weight?: number | null
        }
        Update: {
          availability?: string | null
          bc_product_id?: number | null
          brand_id?: number | null
          brand_name?: string | null
          cache_ttl_minutes?: number | null
          cached_at?: string | null
          categories?: Json | null
          condition?: string | null
          cost_price?: number | null
          created_at?: string | null
          custom_fields?: Json | null
          description?: string | null
          id?: string | null
          images?: Json | null
          inventory_level?: number | null
          inventory_tracking?: string | null
          is_featured?: boolean | null
          is_visible?: boolean | null
          meta_keywords?: string[] | null
          name?: string | null
          price?: number | null
          retail_price?: number | null
          sale_price?: number | null
          search_keywords?: string | null
          sku?: string | null
          sort_order?: number | null
          updated_at?: string | null
          variants?: Json | null
          weight?: number | null
        }
        Relationships: []
      }
      pulse_articles: {
        Row: {
          api_visibility: string[] | null
          created_at: string | null
          desk: string | null
          desk_emoji: string | null
          entities: Json | null
          external_id: string | null
          id: string | null
          image_url: string | null
          is_public: boolean | null
          metadata: Json | null
          narrative: string | null
          pillar: string | null
          pub_date: string | null
          social_post: string | null
          source: string | null
          story_type: Json | null
          sync_status: string | null
          title: string | null
          updated_at: string | null
          url: string | null
        }
        Insert: {
          api_visibility?: string[] | null
          created_at?: string | null
          desk?: string | null
          desk_emoji?: string | null
          entities?: Json | null
          external_id?: string | null
          id?: string | null
          image_url?: string | null
          is_public?: boolean | null
          metadata?: Json | null
          narrative?: string | null
          pillar?: string | null
          pub_date?: string | null
          social_post?: string | null
          source?: string | null
          story_type?: Json | null
          sync_status?: string | null
          title?: string | null
          updated_at?: string | null
          url?: string | null
        }
        Update: {
          api_visibility?: string[] | null
          created_at?: string | null
          desk?: string | null
          desk_emoji?: string | null
          entities?: Json | null
          external_id?: string | null
          id?: string | null
          image_url?: string | null
          is_public?: boolean | null
          metadata?: Json | null
          narrative?: string | null
          pillar?: string | null
          pub_date?: string | null
          social_post?: string | null
          source?: string | null
          story_type?: Json | null
          sync_status?: string | null
          title?: string | null
          updated_at?: string | null
          url?: string | null
        }
        Relationships: []
      }
      pulse_follows: {
        Row: {
          created_at: string | null
          id: string | null
          profile_id: string | null
          tag_name: string | null
          tag_type: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string | null
          profile_id?: string | null
          tag_name?: string | null
          tag_type?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string | null
          profile_id?: string | null
          tag_name?: string | null
          tag_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pulse_follows_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pulse_follows_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "pulse_follows_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      pulse_interactions: {
        Row: {
          action_type: string | null
          article_id: string | null
          created_at: string | null
          id: string | null
          profile_id: string | null
        }
        Insert: {
          action_type?: string | null
          article_id?: string | null
          created_at?: string | null
          id?: string | null
          profile_id?: string | null
        }
        Update: {
          action_type?: string | null
          article_id?: string | null
          created_at?: string | null
          id?: string | null
          profile_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "pulse_interactions_article_id_fkey"
            columns: ["article_id"]
            isOneToOne: false
            referencedRelation: "pulse_articles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pulse_interactions_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "pulse_interactions_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "pulse_interactions_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      pulse_sync_log: {
        Row: {
          articles_created: number | null
          articles_fetched: number | null
          articles_updated: number | null
          error_message: string | null
          id: string | null
          metadata: Json | null
          sync_completed_at: string | null
          sync_started_at: string | null
          sync_status: string | null
        }
        Insert: {
          articles_created?: number | null
          articles_fetched?: number | null
          articles_updated?: number | null
          error_message?: string | null
          id?: string | null
          metadata?: Json | null
          sync_completed_at?: string | null
          sync_started_at?: string | null
          sync_status?: string | null
        }
        Update: {
          articles_created?: number | null
          articles_fetched?: number | null
          articles_updated?: number | null
          error_message?: string | null
          id?: string | null
          metadata?: Json | null
          sync_completed_at?: string | null
          sync_started_at?: string | null
          sync_status?: string | null
        }
        Relationships: []
      }
      purchase_approvals: {
        Row: {
          cart_data: Json | null
          child_profile_id: string | null
          created_at: string | null
          expires_at: string | null
          guardian_notes: string | null
          guardian_profile_id: string | null
          id: string | null
          requested_at: string | null
          responded_at: string | null
          status: string | null
          total_amount: number | null
          updated_at: string | null
        }
        Insert: {
          cart_data?: Json | null
          child_profile_id?: string | null
          created_at?: string | null
          expires_at?: string | null
          guardian_notes?: string | null
          guardian_profile_id?: string | null
          id?: string | null
          requested_at?: string | null
          responded_at?: string | null
          status?: string | null
          total_amount?: number | null
          updated_at?: string | null
        }
        Update: {
          cart_data?: Json | null
          child_profile_id?: string | null
          created_at?: string | null
          expires_at?: string | null
          guardian_notes?: string | null
          guardian_profile_id?: string | null
          id?: string | null
          requested_at?: string | null
          responded_at?: string | null
          status?: string | null
          total_amount?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "purchase_approvals_child_profile_id_fkey"
            columns: ["child_profile_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_approvals_child_profile_id_fkey"
            columns: ["child_profile_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "purchase_approvals_child_profile_id_fkey"
            columns: ["child_profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_approvals_guardian_profile_id_fkey"
            columns: ["guardian_profile_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "purchase_approvals_guardian_profile_id_fkey"
            columns: ["guardian_profile_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "purchase_approvals_guardian_profile_id_fkey"
            columns: ["guardian_profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      sp_transactions: {
        Row: {
          action_type: string | null
          created_at: string | null
          description: string | null
          id: string | null
          points: number | null
          profile_id: string | null
          reference_id: string | null
          reference_type: string | null
        }
        Insert: {
          action_type?: string | null
          created_at?: string | null
          description?: string | null
          id?: string | null
          points?: number | null
          profile_id?: string | null
          reference_id?: string | null
          reference_type?: string | null
        }
        Update: {
          action_type?: string | null
          created_at?: string | null
          description?: string | null
          id?: string | null
          points?: number | null
          profile_id?: string | null
          reference_id?: string | null
          reference_type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "sp_transactions_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sp_transactions_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "sp_transactions_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_link_engagement: {
        Row: {
          first_interaction: string | null
          last_interaction: string | null
          resource_type: string | null
          total_interactions: number | null
          user_id: string | null
        }
        Relationships: []
      }
      user_sport_heads_complete: {
        Row: {
          avatar_style: Json | null
          club_card_count: number | null
          created_at: string | null
          creation_date: string | null
          customization: Json | null
          display_name: string | null
          id: string | null
          is_active_primary: boolean | null
          is_primary: boolean | null
          last_used: string | null
          nfc_linked: boolean | null
          nfc_token_count: number | null
          sport: string | null
          sport_metadata: Json | null
          updated_at: string | null
          user_id: string | null
          user_name: string | null
          user_sport_head_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "sport_heads_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "children_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "sport_heads_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "player_evaluation_stats"
            referencedColumns: ["player_id"]
          },
          {
            foreignKeyName: "sport_heads_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      users_without_profiles: {
        Row: {
          created_at: string | null
          email: string | null
          id: string | null
        }
        Relationships: []
      }
      widget_configurations: {
        Row: {
          allowed_domains: string[] | null
          created_at: string | null
          default_config: Json | null
          description: string | null
          id: string | null
          is_public: boolean | null
          name: string | null
          widget_key: string | null
        }
        Insert: {
          allowed_domains?: string[] | null
          created_at?: string | null
          default_config?: Json | null
          description?: string | null
          id?: string | null
          is_public?: boolean | null
          name?: string | null
          widget_key?: string | null
        }
        Update: {
          allowed_domains?: string[] | null
          created_at?: string | null
          default_config?: Json | null
          description?: string | null
          id?: string | null
          is_public?: boolean | null
          name?: string | null
          widget_key?: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      _old_trigger_send_sms_edge_function: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      add_deletion_log: {
        Args: {
          p_operation: string
          p_entity_id: string
          p_entity_type: string
          p_status: string
          p_message?: string
        }
        Returns: string
      }
      add_user_privilege: {
        Args: {
          user_id: string
          new_privilege: string
        }
        Returns: undefined
      }
      admin_delete_user: {
        Args: {
          target_user_id: string
        }
        Returns: Json
      }
      analyze_profile_creation_logs: {
        Args: Record<PropertyKey, never>
        Returns: {
          log_timestamp: string
          log_message: string
          log_user_id: string
          log_success: boolean
          log_error_message: string
        }[]
      }
      api_pre_session_direct: {
        Args: {
          _event_id: string
          _team_id: string
        }
        Returns: Json
      }
      api_pre_session_request: {
        Args: {
          _event_id: string
          _team_id: string
        }
        Returns: Json
      }
      award_sp_points: {
        Args: {
          p_profile_id: string
          p_points: number
          p_action_type: string
          p_reference_id: string
          p_description?: string
        }
        Returns: unknown
      }
      award_xp_for_evaluation: {
        Args: {
          p_player_id: string
          p_event_id: string
          p_xp_amount?: number
        }
        Returns: undefined
      }
      basic_pre_session: {
        Args: {
          event_id: string
          team_id: string
        }
        Returns: Json
      }
      bytea_to_text: {
        Args: {
          data: string
        }
        Returns: string
      }
      calculate_event_fairness_metrics: {
        Args: {
          p_event_id: string
        }
        Returns: {
          total_players: number
          active_players: number
          average_minutes: number
          min_minutes: number
          max_minutes: number
          standard_deviation: number
          fairness_score: number
        }[]
      }
      call_edge_function_send_sms: {
        Args: {
          p_sms_id: string
        }
        Returns: Json
      }
      call_edge_function_simple: {
        Args: {
          p_sms_id: string
        }
        Returns: Json
      }
      call_send_sms_edge_function: {
        Args: {
          p_notification_id: string
          p_edge_function_url?: string
          p_simulate?: boolean
        }
        Returns: Json
      }
      can_edit_evaluation: {
        Args: {
          evaluation_id: string
          editor_id: string
        }
        Returns: boolean
      }
      can_user_be_deleted: {
        Args: {
          target_user_id: string
        }
        Returns: Json
      }
      cart_upsert: {
        Args: {
          session_id_param: string
          items_param: Json
          profile_id_param: string
        }
        Returns: Json
      }
      cascade_delete_user: {
        Args: {
          target_user_id: string
        }
        Returns: Json
      }
      check_delete_constraints: {
        Args: {
          p_table_name: string
          p_record_id: string
        }
        Returns: string
      }
      check_expired_codes: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      check_pg_net_sms_results: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      check_sms_job_status: {
        Args: Record<PropertyKey, never>
        Returns: {
          job_name: string
          last_run: string
          next_run: string
          status: string
        }[]
      }
      check_sync_health: {
        Args: Record<PropertyKey, never>
        Returns: {
          alert_level: string
          message: string
          details: Json
        }[]
      }
      check_user_deletion_impact: {
        Args: {
          target_user_id: string
        }
        Returns: Json
      }
      cleanup_framework_import: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      cleanup_old_pending_profiles: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      cleanup_sms_queue: {
        Args: {
          p_days_to_keep?: number
        }
        Returns: number
      }
      complete_delete_user: {
        Args: {
          target_user_id: string
        }
        Returns: Json
      }
      correct_delete_user: {
        Args: {
          target_user_id: string
        }
        Returns: Json
      }
      create_activities_partition: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_basic_pre_evaluations:
        | {
            Args: {
              p_event_id: string
              p_player_ids: string[]
              p_team_id: string
              p_framework_version: string
              p_week_number: number
              p_expires_at: string
            }
            Returns: string[]
          }
        | {
            Args: {
              p_event_id: string
              p_team_id: string
              p_player_ids: string[]
              p_version?: string
              p_week_number?: number
              p_expires_at?: string
            }
            Returns: string[]
          }
      create_basic_pre_evaluations_fixed: {
        Args: {
          p_event_id: string
          p_team_id: string
          p_player_ids: string[]
          p_framework_version: string
          p_week_number: number
          p_expires_at?: string
        }
        Returns: string[]
      }
      create_default_player_objectives: {
        Args: {
          p_player_id: string
          p_position: string
          p_team_id?: string
        }
        Returns: boolean
      }
      create_edge_function_logs_table: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_future_partitions: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_get_teams_function: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      create_missing_pre_evaluations: {
        Args: Record<PropertyKey, never>
        Returns: {
          event_id: string
          event_name: string
          players_count: number
          pre_evaluations_created: number
        }[]
      }
      create_pending_profile: {
        Args: {
          p_email: string
          p_full_name: string
          p_avatar_url: string
          p_date_of_birth: string
          p_phone: string
          p_marketing_email: boolean
          p_marketing_notifications: boolean
          p_registration_metadata: Json
        }
        Returns: Json
      }
      create_pre_evaluation_notifications: {
        Args: {
          p_pre_evaluation_ids: string[]
          p_recipient_ids: string[]
          p_subject: string
          p_body: string
          p_action_url_prefix: string
          p_channels?: string[]
        }
        Returns: string[]
      }
      create_pre_evaluation_notifications_deprecated: {
        Args: {
          p_pre_evaluation_ids: string[]
          p_recipient_ids: string[]
          p_subject: string
          p_body: string
          p_action_url_prefix: string
          p_channels?: string[]
        }
        Returns: string[]
      }
      create_pre_evaluation_notifications_v2: {
        Args: {
          p_pre_evaluation_ids: string[]
          p_channels?: string[]
        }
        Returns: string[]
      }
      create_pre_evaluations: {
        Args: {
          event_id: string
          team_id: string
        }
        Returns: string
      }
      create_sms_cron_job: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      create_team_safe: {
        Args: {
          p_team_name: string
          p_age_group: string
          p_club_id?: string
          p_sport_type?: string
          p_description?: string
          p_color_primary?: string
          p_color_secondary?: string
          p_home_location?: string
          p_season?: string
        }
        Returns: {
          team_id: string
          team_name: string
          success: boolean
          message: string
        }[]
      }
      cron_automatically_expire_codes: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      cron_execute_send_sms_edge_function: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      cron_send_sms_edge_function: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      date_to_week_number: {
        Args: {
          input_date: string
          framework_start: string
        }
        Returns: number
      }
      debug_invite_code: {
        Args: {
          p_code: string
        }
        Returns: {
          code: string
          is_valid: boolean
          expires_at: string
          team_id: string
          team_name: string
          owner_id: string
          owner_name: string
          code_type: string
          personal_code: boolean
          use_count: number
          max_uses: number
          total_redemptions: number
          status: string
        }[]
      }
      debug_log: {
        Args: {
          p_trigger_name: string
          p_operation: string
          p_user_id: string
          p_user_email: string
          p_step: string
          p_step_num: number
          p_data: Json
          p_error?: string
        }
        Returns: undefined
      }
      debug_params: {
        Args: {
          param1: Json
        }
        Returns: Json
      }
      debug_record_link_open: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      debug_rpc_call: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      debug_team_evaluation_status: {
        Args: {
          p_team_id: string
        }
        Returns: {
          event_id: string
          event_name: string
          start_datetime: string
          is_past: boolean
          attended_count: number
          evaluation_count: number
          evaluation_completed: boolean
          evaluation_completed_manually: boolean
        }[]
      }
      deduplicate_sms_queue: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      delete_club_cascade: {
        Args: {
          p_club_id: string
        }
        Returns: Json
      }
      delete_team_events_as_superadmin: {
        Args: {
          event_ids: string[]
        }
        Returns: Json
      }
      delete_team_events_superadmin: {
        Args: {
          p_event_ids: string[]
        }
        Returns: Json
      }
      delete_user: {
        Args: {
          user_id: string
        }
        Returns: string
      }
      delete_user_cascade: {
        Args: {
          user_id: string
        }
        Returns: Json
      }
      delete_user_cleanly: {
        Args: {
          user_id: string
        }
        Returns: Json
      }
      delete_user_completely: {
        Args: {
          user_email: string
        }
        Returns: {
          step_number: number
          step_name: string
          deleted_count: number
          status: string
          details: string
        }[]
      }
      delete_user_safely: {
        Args: {
          user_id: string
        }
        Returns: boolean
      }
      diagnose_pre_evaluation_questions: {
        Args: {
          pre_eval_id: string
        }
        Returns: Json
      }
      diagnose_user_login: {
        Args: {
          user_email: string
        }
        Returns: Json
      }
      direct_delete_user: {
        Args: {
          target_user_id: string
        }
        Returns: Json
      }
      direct_send_email: {
        Args: {
          to_email: string
          to_name: string
          subject: string
          html_content: string
        }
        Returns: Json
      }
      direct_send_twilio_sms: {
        Args: {
          p_pre_evaluation_id: string
          p_phone_number?: string
          p_message?: string
        }
        Returns: Json
      }
      end_participation_time: {
        Args: {
          p_participant_id: string
          p_segment_number: number
        }
        Returns: boolean
      }
      exec_sql: {
        Args: {
          sql: string
        }
        Returns: undefined
      }
      execute_sql_for_email: {
        Args: {
          user_id_param: string
        }
        Returns: {
          email: string
        }[]
      }
      export_sms_details_for_external_handling: {
        Args: {
          p_pre_evaluation_id: string
        }
        Returns: Json
      }
      final_delete_user: {
        Args: {
          target_user_id: string
        }
        Returns: Json
      }
      find_user_by_email: {
        Args: {
          user_email: string
        }
        Returns: {
          user_id: string
          email: string
          full_name: string
          username: string
          created_at: string
          email_verified: boolean
          last_sign_in_at: string
        }[]
      }
      fix_pending_sms_notifications: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      fix_pre_evaluations_without_questions: {
        Args: Record<PropertyKey, never>
        Returns: {
          pre_evaluation_id: string
          result: string
        }[]
      }
      fix_sms_cron_job: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      fix_trigger_send_sms_on_notification_create: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      force_create_all_missing_profiles: {
        Args: Record<PropertyKey, never>
        Returns: {
          user_id: string
          email: string
          profile_created: boolean
          error_message: string
        }[]
      }
      format_notification_message: {
        Args: {
          p_notification_type: string
          p_variables: Json
        }
        Returns: string
      }
      generate_personal_invite_code: {
        Args: {
          user_id: string
          sport_preference?: string
        }
        Returns: string
      }
      generate_slug: {
        Args: {
          title: string
        }
        Returns: string
      }
      generate_sport_head_id: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_available_administrators: {
        Args: {
          p_club_id: string
        }
        Returns: {
          id: string
          email: string
        }[]
      }
      get_brevo_api_key: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_cart_session: {
        Args: {
          p_session_id: string
        }
        Returns: {
          like: unknown
        }[]
      }
      get_children_for_parent: {
        Args: {
          parent_id_param: string
        }
        Returns: {
          child_id: string
          child_name: string
          child_dob: string
        }[]
      }
      get_club_administrators: {
        Args: {
          p_club_id: string
        }
        Returns: {
          id: string
          club_id: string
          user_id: string
          role: string
          is_active: boolean
          created_at: string
          updated_at: string
          is_primary: boolean
          assigned_by: string
          admin_id: string
        }[]
      }
      get_club_teams: {
        Args: {
          p_club_id: string
          p_include_inactive?: boolean
        }
        Returns: {
          age_group: string
          club_id: string | null
          color_primary: string | null
          color_secondary: string | null
          created_at: string | null
          created_by: string | null
          description: string | null
          home_location: string | null
          is_active: boolean | null
          is_individual: boolean | null
          last_activity: string | null
          logo_url: string | null
          season: string | null
          sport_type: string
          status: string | null
          team_id: string
          team_name: string
          updated_at: string | null
        }[]
      }
      get_coach_teams: {
        Args: {
          coach_user_id: string
        }
        Returns: {
          team_id: string
          team_name: string
          age_group: string
          created_at: string
          club_id: string
          club_name: string
          logo_url: string
          sport_type: string
          is_active: boolean
        }[]
      }
      get_evaluation_criteria_for_week: {
        Args: {
          target_position: string
          target_week: number
          framework_version?: string
        }
        Returns: {
          category: string
          area: string
          evaluation_focus: string
          question: string
          week_number: number
        }[]
      }
      get_event_evaluation_category_stats: {
        Args: {
          event_uuid: string
        }
        Returns: {
          category: string
          avg_rating: number
          evaluation_count: number
          player_count: number
          completion_rate: number
        }[]
      }
      get_event_evaluation_summary_for_user: {
        Args: {
          event_uuid: string
        }
        Returns: {
          attended_participants: number | null
          avg_overall_rating: number | null
          completion_percentage: number | null
          evaluated_participants: number | null
          evaluation_sessions: number | null
          evaluation_status: string | null
          event_id: string | null
          event_name: string | null
          event_type: string | null
          first_evaluation_date: string | null
          last_evaluation_date: string | null
          max_rating: number | null
          min_rating: number | null
          start_datetime: string | null
          team_id: string | null
          total_individual_evaluations: number | null
          total_participants: number | null
        }[]
      }
      get_event_participant_counts: {
        Args: {
          event_id: string
        }
        Returns: {
          total_count: number
          invited_count: number
          tentative_count: number
          confirmed_count: number
          declined_count: number
          checked_in_count: number
          late_count: number
          attended_count: number
          left_early_count: number
          no_show_count: number
          excused_count: number
        }[]
      }
      get_event_participation_summary: {
        Args: {
          p_event_id: string
        }
        Returns: {
          user_id: string
          full_name: string
          avatar_url: string
          total_minutes: number
          segments_played: number
          positions: string[]
        }[]
      }
      get_event_player_evaluation_status: {
        Args: {
          event_uuid: string
        }
        Returns: {
          participant_id: string
          player_id: string
          player_name: string
          is_evaluated: boolean
          evaluation_count: number
          avg_rating: number
          last_evaluated_date: string
        }[]
      }
      get_latest_sync_status: {
        Args: Record<PropertyKey, never>
        Returns: {
          status: string
          started_at: string
          completed_at: string
          articles_synced: number
          error_message: string
        }[]
      }
      get_or_create_sport_head: {
        Args: {
          p_user_id: string
          p_sport: string
          p_display_name: string
        }
        Returns: string
      }
      get_parent_for_child: {
        Args: {
          child_id_param: string
        }
        Returns: {
          parent_id: string
          parent_name: string
        }[]
      }
      get_pending_notifications: {
        Args: {
          max_count?: number
        }
        Returns: {
          id: string
          pre_evaluation_id: string
          recipient_id: string
          recipient_email: string
          subject: string
          status: string
          scheduled_for: string
          created_at: string
        }[]
      }
      get_pending_sms: {
        Args: {
          p_limit?: number
        }
        Returns: {
          id: string
          phone: string
          message: string
          recipient: string
          notification_type: string
          pre_eval_id: string
          created_at: string
        }[]
      }
      get_pending_sms_v2: {
        Args: {
          p_limit?: number
        }
        Returns: {
          id: string
          phone: string
          message: string
          recipient: string
          notification_type: string
          pre_eval_id: string
          created_at: string
        }[]
      }
      get_player_evaluation_summary: {
        Args: {
          target_player_id: string
          start_week?: number
          end_week?: number
          framework_version?: string
        }
        Returns: {
          category: string
          avg_rating: number
          total_evaluations: number
          latest_evaluation_date: string
        }[]
      }
      get_player_objectives_summary: {
        Args: {
          p_player_id: string
        }
        Returns: {
          objective_type: string
          objective_text: string
          current_level: number
          target_level: number
          progress_percentage: number
          created_date: string
          target_date: string
          is_active: boolean
        }[]
      }
      get_player_sms_status: {
        Args: {
          p_event_id: string
        }
        Returns: Json
      }
      get_player_training_stats: {
        Args: {
          p_player_id: string
          p_team_id?: string
        }
        Returns: {
          total_sessions: number
          attended_sessions: number
          missed_sessions: number
          attendance_rate: number
          pre_evaluations_completed: number
          pre_evaluation_rate: number
          coach_evaluations_received: number
          coach_evaluation_rate: number
          avg_self_rating: number
          avg_coach_rating: number
          upcoming_sessions: number
          past_sessions: number
        }[]
      }
      get_profiles_by_ids: {
        Args: {
          user_ids: string[]
        }
        Returns: {
          age_transition_date: string | null
          age_verified: boolean | null
          allergies: string[] | null
          avatar_url: string | null
          communication_preferences: Json | null
          consent_records: Json | null
          context_preferences: Json | null
          date_of_birth: string | null
          display_name: string | null
          email_verified: boolean | null
          emergency_contact: Json | null
          full_name: string | null
          id: string
          is_minor: boolean | null
          last_accessed_team: string | null
          last_login: string | null
          location: string | null
          marketing_email: boolean | null
          marketing_notifications: boolean | null
          medical_conditions: string[] | null
          nickname: string | null
          notification_preferences: Json | null
          onboarding_completed: boolean | null
          parent_handoff_completed: boolean | null
          parent_id: string | null
          personal_invite_code: string | null
          phone: string | null
          phone_normalised: string | null
          phone_verified: boolean | null
          phone_verified_at: string | null
          photography_allowed: boolean | null
          preferred_contact_method: string | null
          primary_sport_head_id: string | null
          privacy_settings: Json | null
          privileges: string[] | null
          registration_metadata: Json | null
          registration_source: string | null
          sp_balance: number | null
          sport_affiliations: Json | null
          sport_head_id: string | null
          terms_accepted: Json | null
          updated_at: string | null
          username: string | null
          website: string | null
        }[]
      }
      get_pulse_articles_with_stats: {
        Args: Record<PropertyKey, never>
        Returns: {
          article_id: string
          title: string
          pillar: string
          desk: string
          desk_emoji: string
          narrative: string
          url: string
          source: string
          pub_date: string
          entities: Json
          social_post: string
          story_type: Json
          image_url: string
          like_count: number
          bookmark_count: number
          share_count: number
          user_liked: boolean
          user_bookmarked: boolean
          user_shared: boolean
        }[]
      }
      get_recent_debug_logs: {
        Args: {
          p_email?: string
          p_hours?: number
        }
        Returns: {
          created_at: string
          trigger_name: string
          user_email: string
          step: string
          data: Json
          error: string
        }[]
      }
      get_recent_registrations_with_invites: {
        Args: {
          p_limit?: number
        }
        Returns: {
          user_id: string
          email: string
          full_name: string
          registration_source: string
          invite_code: string
          team_id: string
          team_name: string
          created_at: string
          email_confirmed_at: string
          profile_created: boolean
          team_member: boolean
          join_request: boolean
        }[]
      }
      get_recent_trigger_logs: {
        Args: {
          p_limit?: number
        }
        Returns: {
          id: string
          trigger_name: string
          user_id: string
          event_type: string
          metadata: Json
          created_at: string
        }[]
      }
      get_single_team_membership: {
        Args: {
          _team_id: string
          _user_id?: string
        }
        Returns: {
          team_id: string
          user_id: string
          role: string
        }[]
      }
      get_sms_queue_stats: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_sp_balance: {
        Args: {
          p_profile_id: string
        }
        Returns: number
      }
      get_sync_stats: {
        Args: {
          time_period?: unknown
        }
        Returns: {
          total_syncs: number
          successful_syncs: number
          failed_syncs: number
          success_rate: number
          total_articles_synced: number
          avg_sync_duration_seconds: number
          last_sync_at: string
          last_sync_status: string
        }[]
      }
      get_team_by_id: {
        Args: {
          p_team_id: string
        }
        Returns: {
          team_id: string
          team_name: string
          age_group: string
          club_id: string
          logo_url: string
          created_at: string
          updated_at: string
          created_by: string
          is_active: boolean
          description: string
          color_primary: string
          color_secondary: string
          home_location: string
          season: string
          status: string
          last_activity: string
          sport_type: string
          is_individual: boolean
        }[]
      }
      get_team_details_by_id: {
        Args: {
          team_id_param: string
        }
        Returns: {
          team_id: string
          team_name: string
          age_group: string
          sport_type: string
          logo_url: string
        }[]
      }
      get_team_evaluation_count: {
        Args: {
          team_id_param: string
        }
        Returns: number
      }
      get_team_events_minimal: {
        Args: {
          team_id_param: string
          limit_param?: number
          offset_param?: number
        }
        Returns: {
          id: string
          name: string
          start_datetime: string
          team_id: string
        }[]
      }
      get_team_member_count: {
        Args: {
          p_team_id: string
        }
        Returns: number
      }
      get_team_membership_for_pre_session: {
        Args: {
          _team_id: string
          _user_id: string
        }
        Returns: {
          accepted_at: string | null
          invited_by: string | null
          jersey_number: number | null
          joined_at: string | null
          left_at: string | null
          membership_id: string
          position: string | null
          role: string | null
          status: string | null
          team_id: string | null
          updated_at: string | null
          user_id: string | null
        }[]
      }
      get_team_membership_safe: {
        Args: {
          _team_id: string
          _user_id?: string
        }
        Returns: {
          team_id: string
          user_id: string
          role: string
          status: string
          is_member: boolean
        }[]
      }
      get_team_outstanding_evaluations_count: {
        Args: {
          team_id_param: string
        }
        Returns: number
      }
      get_team_outstanding_evaluations_details: {
        Args: {
          team_id_param: string
        }
        Returns: {
          event_id: string
          event_name: string
          start_datetime: string
          total_attended: number
          completed_evaluations: number
          completion_percentage: number
          evaluation_completed: boolean
          evaluation_completed_manually: boolean
        }[]
      }
      get_team_player_sms_status: {
        Args: {
          p_event_id: string
          p_team_id: string
        }
        Returns: Json
      }
      get_team_sport_type: {
        Args: {
          team_id_param: string
        }
        Returns: string
      }
      get_team_statistics: {
        Args: {
          p_team_id: string
        }
        Returns: {
          upcoming_events: number
          events_today: number
          total_evaluations: number
          outstanding_evaluations: number
          total_events: number
          total_players: number
          active_players: number
          future_events: number
        }[]
      }
      get_teams_by_ids: {
        Args: {
          team_ids: string[]
        }
        Returns: {
          team_id: string
          team_name: string
          age_group: string
          created_at: string
          club_id: string
          is_active: boolean
        }[]
      }
      get_templates_for_team: {
        Args: {
          team_id_param: string
        }
        Returns: {
          categories: string[]
          club_id: string | null
          created_at: string
          created_by: string
          description: string | null
          framework_version: string
          id: string
          is_active: boolean | null
          name: string
          positions: string[]
          updated_at: string
          week_ranges: number[]
        }[]
      }
      get_test_configuration: {
        Args: {
          config_name?: string
        }
        Returns: Json
      }
      get_user_details: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_user_email: {
        Args: {
          user_id: string
        }
        Returns: {
          email: string
        }[]
      }
      handle_pre_session_request: {
        Args: {
          _event_id: string
          _team_id: string
        }
        Returns: Json
      }
      handle_profile_deletion: {
        Args: {
          target_user_id: string
        }
        Returns: boolean
      }
      has_privilege: {
        Args: {
          user_id: string
          required_privilege: string
        }
        Returns: boolean
      }
      http: {
        Args: {
          request: Database["public"]["CompositeTypes"]["http_request"]
        }
        Returns: unknown
      }
      http_delete:
        | {
            Args: {
              uri: string
            }
            Returns: unknown
          }
        | {
            Args: {
              uri: string
              content: string
              content_type: string
            }
            Returns: unknown
          }
      http_get:
        | {
            Args: {
              uri: string
            }
            Returns: unknown
          }
        | {
            Args: {
              uri: string
              data: Json
            }
            Returns: unknown
          }
      http_head: {
        Args: {
          uri: string
        }
        Returns: unknown
      }
      http_header: {
        Args: {
          field: string
          value: string
        }
        Returns: Database["public"]["CompositeTypes"]["http_header"]
      }
      http_list_curlopt: {
        Args: Record<PropertyKey, never>
        Returns: {
          curlopt: string
          value: string
        }[]
      }
      http_patch: {
        Args: {
          uri: string
          content: string
          content_type: string
        }
        Returns: unknown
      }
      http_post:
        | {
            Args: {
              uri: string
              content: string
              content_type: string
            }
            Returns: unknown
          }
        | {
            Args: {
              uri: string
              data: Json
            }
            Returns: unknown
          }
      http_put: {
        Args: {
          uri: string
          content: string
          content_type: string
        }
        Returns: unknown
      }
      http_reset_curlopt: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      http_set_curlopt: {
        Args: {
          curlopt: string
          value: string
        }
        Returns: boolean
      }
      import_shot_framework_from_staging: {
        Args: Record<PropertyKey, never>
        Returns: {
          total_processed: number
          successfully_imported: number
          duplicates_skipped: number
          errors_encountered: number
          import_summary: string
        }[]
      }
      initiate_pre_session: {
        Args: {
          _event_id: string
          _team_id: string
          _player_ids?: string[]
        }
        Returns: Json
      }
      insert_sample_membership_data: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      is_club_administrator: {
        Args: {
          p_club_id: string
          p_user_id: string
        }
        Returns: boolean
      }
      is_parent_of: {
        Args: {
          potential_parent_id: string
          potential_child_id: string
        }
        Returns: boolean
      }
      is_superadmin: {
        Args: {
          user_id: string
        }
        Returns: boolean
      }
      is_team_coach: {
        Args: {
          _team_id: string
          _user_id?: string
        }
        Returns: boolean
      }
      list_all_users: {
        Args: Record<PropertyKey, never>
        Returns: {
          user_id: string
          email: string
          full_name: string
          username: string
          created_at: string
          email_verified: boolean
          last_sign_in_at: string
          is_superadmin: boolean
          team_count: number
          club_count: number
        }[]
      }
      list_all_users_v1: {
        Args: {
          requesting_user_id: string
        }
        Returns: {
          user_id: string
          email: string
          full_name: string
          username: string
          created_at: string
          email_verified: boolean
          last_sign_in_at: string
          is_superadmin: boolean
          team_count: number
          club_count: number
        }[]
      }
      mark_evaluation_viewed: {
        Args: {
          evaluation_id: string
          viewer_id: string
        }
        Returns: boolean
      }
      mark_sms_sent: {
        Args: {
          p_sms_id: string
          p_success: boolean
          p_error?: string
        }
        Returns: boolean
      }
      master_delete_user: {
        Args: {
          user_id: string
        }
        Returns: Json
      }
      migrate_children_to_profiles: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      minimal_delete_user: {
        Args: {
          target_user_id: string
        }
        Returns: Json
      }
      monitor_deprecated_notification_calls: {
        Args: Record<PropertyKey, never>
        Returns: {
          call_count: number
          last_called: string
          sample_body: string
        }[]
      }
      optimize_evaluation_framework: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      pre_session_button: {
        Args: {
          event_id: string
          team_id: string
        }
        Returns: Json
      }
      process_invited_user_verification: {
        Args: {
          user_id: string
        }
        Returns: Json
      }
      process_pending_notifications_batch: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      process_pending_profile_with_invite: {
        Args: {
          p_email: string
        }
        Returns: Json
      }
      process_sms_notifications_batch: {
        Args: {
          p_limit?: number
        }
        Returns: number
      }
      process_sms_queue_batch: {
        Args: {
          batch_size?: number
        }
        Returns: number
      }
      process_sms_queue_via_notification: {
        Args: {
          sms_id: string
        }
        Returns: Json
      }
      process_sms_queue_with_edge_function: {
        Args: {
          sms_id: string
        }
        Returns: Json
      }
      recalculate_all_event_participant_counts: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      record_link_open: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      regenerate_player_evaluations_for_pre_evaluation: {
        Args: {
          pre_eval_id: string
        }
        Returns: string
      }
      register_invited_user: {
        Args: {
          user_email: string
          user_password: string
          user_full_name: string
          avatar_url: string
          avatar_preference: string
          invite_code: string
          team_id: string
        }
        Returns: Json
      }
      reset_event_evaluation_completion_manual: {
        Args: {
          event_id_param: string
        }
        Returns: boolean
      }
      reset_stuck_sms_notifications: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      retry_failed_sms: {
        Args: {
          p_max_retries?: number
          p_retry_window?: unknown
        }
        Returns: number
      }
      rpc_cart_exists: {
        Args: {
          p_session_id: string
        }
        Returns: boolean
      }
      rpc_create_basic_pre_evaluations:
        | {
            Args: {
              event_id: string
              team_id: string
              player_ids: string[]
              version: string
              week_number?: number
              expires_at?: string
            }
            Returns: string[]
          }
        | {
            Args: {
              p_event_id: string
              p_team_id: string
              p_player_ids: string[]
              p_framework_version?: string
              p_version?: string
              p_week_number?: number
              p_expires_at?: string
            }
            Returns: string[]
          }
      rpc_delete_user: {
        Args: {
          user_id: string
        }
        Returns: Json
      }
      rpc_force_delete_user: {
        Args: {
          user_id: string
        }
        Returns: Json
      }
      rpc_get_cart_session: {
        Args: {
          p_session_id: string
        }
        Returns: {
          id: string
          session_id: string
          profile_id: string
          items: Json
          subtotal: number
          tax_total: number
          shipping_total: number
          discount_total: number
          grand_total: number
          coupon_codes: string[]
          shipping_address: Json
          billing_address: Json
          selected_shipping_option: Json
          notes: string
          metadata: Json
          last_activity_at: string
          created_at: string
          updated_at: string
        }[]
      }
      rpc_initiate_pre_session: {
        Args: {
          event_id: string
          team_id: string
        }
        Returns: Json
      }
      rpc_upsert_cart_session: {
        Args: {
          p_session_id: string
          p_items?: Json
          p_profile_id?: string
        }
        Returns: {
          id: string
          session_id: string
          profile_id: string
          items: Json
          created_at: string
          updated_at: string
        }[]
      }
      scheduled_process_sms_queue: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      search_pulse_articles: {
        Args: {
          search_query: string
        }
        Returns: unknown[]
      }
      send_brevo_email: {
        Args: {
          to_email: string
          to_name: string
          subject: string
          html_content: string
          sender_email?: string
          sender_name?: string
        }
        Returns: Json
      }
      send_pre_evaluation_email: {
        Args: {
          notification_id: string
        }
        Returns: string
      }
      send_sms_directly: {
        Args: {
          p_notification_id: string
        }
        Returns: Json
      }
      send_twilio_sms: {
        Args: {
          phone_number: string
          message_body: string
          notification_id: string
        }
        Returns: boolean
      }
      send_twilio_sms_fixed: {
        Args: {
          phone_number: string
          message_body: string
          notification_id: string
        }
        Returns: boolean
      }
      set_event_evaluation_completion_manual: {
        Args: {
          event_id_param: string
          completed: boolean
          user_id_param?: string
        }
        Returns: boolean
      }
      setup_evaluation_test_data: {
        Args: {
          p_event_id: string
          p_user_ids: string[]
        }
        Returns: string
      }
      should_send_notification_for_event: {
        Args: {
          p_event_id: string
          p_notification_type: string
        }
        Returns: boolean
      }
      should_use_sms_v2: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      simple_delete_user: {
        Args: {
          target_user_id: string
        }
        Returns: Json
      }
      simple_profile_delete: {
        Args: {
          target_user_id: string
        }
        Returns: Json
      }
      start_participation_time: {
        Args: {
          p_participant_id: string
          p_segment_number: number
          p_position?: string
        }
        Returns: string
      }
      sync_missing_profiles: {
        Args: Record<PropertyKey, never>
        Returns: {
          user_id: string
          email: string
          created: boolean
        }[]
      }
      test_brevo_api: {
        Args: {
          email: string
        }
        Returns: Json
      }
      test_edge_function_sms: {
        Args: {
          p_phone?: string
        }
        Returns: Json
      }
      test_populate_evaluation_trigger: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      test_profile_permissions: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      test_send_sms_direct: {
        Args: {
          p_phone?: string
          p_message?: string
        }
        Returns: Json
      }
      test_send_sms_final: {
        Args: {
          p_phone?: string
          p_message?: string
        }
        Returns: Json
      }
      test_simple_http: {
        Args: {
          p_phone?: string
        }
        Returns: Json
      }
      test_simple_twilio_request: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      test_sms_cron_fix: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      test_sms_sending: {
        Args: {
          phone_number: string
          test_message?: string
        }
        Returns: Json
      }
      test_sms_system: {
        Args: {
          p_phone?: string
          p_simulate?: boolean
        }
        Returns: Json
      }
      test_sms_v2_flow: {
        Args: {
          p_phone?: string
          p_message?: string
        }
        Returns: Json
      }
      test_sms_with_id: {
        Args: {
          sms_id: string
        }
        Returns: Json
      }
      test_trigger_order: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      test_twilio_sms_request: {
        Args: {
          phone_number?: string
          message?: string
        }
        Returns: Json
      }
      text_to_bytea: {
        Args: {
          data: string
        }
        Returns: string
      }
      toggle_sms_v2: {
        Args: {
          p_enabled: boolean
        }
        Returns: Json
      }
      trigger_notification_directly: {
        Args: {
          notification_id: string
        }
        Returns: Json
      }
      trigger_send_sms_edge_function: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      update_app_url: {
        Args: {
          new_url: string
        }
        Returns: Json
      }
      update_event_evaluation_completion: {
        Args: {
          event_id_param: string
        }
        Returns: boolean
      }
      update_event_notification_settings: {
        Args: {
          p_event_id: string
          p_enabled_types: string[]
          p_disabled_types: string[]
        }
        Returns: boolean
      }
      update_event_participant_count: {
        Args: {
          event_id: string
        }
        Returns: undefined
      }
      update_last_accessed_team: {
        Args: {
          p_user_id: string
          p_team_id: string
        }
        Returns: undefined
      }
      update_participation_time_position: {
        Args: {
          p_participant_id: string
          p_segment_number: number
          p_position: string
        }
        Returns: boolean
      }
      update_player_evaluation: {
        Args: {
          _pre_evaluation_id: string
          _position: string
          _category: string
          _area: string
          _player_rating: number
          _player_notes: string
        }
        Returns: boolean
      }
      update_pre_evaluation:
        | {
            Args: Record<PropertyKey, never>
            Returns: Json
          }
        | {
            Args: {
              evaluation_id: string
            }
            Returns: boolean
          }
        | {
            Args: {
              id: string
              status?: string
              metadata?: Json
            }
            Returns: Json
          }
      update_pre_evaluation_by_id: {
        Args: {
          _evaluation_id: string
        }
        Returns: Json
      }
      update_pre_evaluation_secure: {
        Args: {
          _pre_evaluation_id: string
          _access_token?: string
          _status?: string
          _player_rating?: number
          _player_notes?: string
        }
        Returns: Json
      }
      update_sms_edge_function_cron: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      update_test_configuration: {
        Args: {
          config_name: string
          is_enabled: boolean
          config_settings?: Json
        }
        Returns: boolean
      }
      update_twilio_settings: {
        Args: {
          account_sid: string
          auth_token: string
          phone_number: string
        }
        Returns: boolean
      }
      upsert_cart_session: {
        Args: {
          p_session_id: string
          p_profile_id?: string
          p_items?: Json
          p_subtotal?: number
          p_tax_total?: number
          p_shipping_total?: number
          p_discount_total?: number
          p_grand_total?: number
          p_coupon_codes?: string[]
          p_shipping_address?: Json
          p_billing_address?: Json
          p_selected_shipping_option?: Json
          p_notes?: string
          p_metadata?: Json
        }
        Returns: {
          like: unknown
        }[]
      }
      url_encode: {
        Args: {
          "": string
        }
        Returns: string
      }
      urlencode:
        | {
            Args: {
              data: Json
            }
            Returns: string
          }
        | {
            Args: {
              string: string
            }
            Returns: string
          }
        | {
            Args: {
              string: string
            }
            Returns: string
          }
      user_has_privilege: {
        Args: {
          user_id: string
          privilege: string
        }
        Returns: boolean
      }
      validate_and_process_invite_code:
        | {
            Args: {
              p_invite_code: string
              p_user_id: string
              p_user_email: string
            }
            Returns: Json
          }
        | {
            Args: {
              p_invite_code: string
              p_user_id: string
              p_user_email: string
            }
            Returns: Json
          }
      validate_framework_import: {
        Args: Record<PropertyKey, never>
        Returns: {
          validation_check: string
          expected_count: number
          actual_count: number
          status: string
        }[]
      }
      verify_enhanced_auth_migration: {
        Args: Record<PropertyKey, never>
        Returns: {
          table_name: string
          table_exists: boolean
          row_count: number
        }[]
      }
    }
    Enums: {
      activity_type:
        | "membership_changed"
        | "video_watched"
        | "event_rsvp"
        | "profile_updated"
        | "child_added"
        | "child_updated"
      billing_period: "monthly" | "yearly"
      event_status: "draft" | "published" | "cancelled" | "completed"
      invite_code_status: "active" | "used" | "expired"
      membership_status: "active" | "inactive"
      subscription_status: "active" | "cancelled" | "expired"
    }
    CompositeTypes: {
      http_header: {
        field: string | null
        value: string | null
      }
      http_request: {
        method: unknown | null
        uri: string | null
        headers: Database["public"]["CompositeTypes"]["http_header"][] | null
        content_type: string | null
        content: string | null
      }
      http_response: {
        status: number | null
        content_type: string | null
        headers: Database["public"]["CompositeTypes"]["http_header"][] | null
        content: string | null
      }
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

