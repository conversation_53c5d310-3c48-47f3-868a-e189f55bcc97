# Deprecated Files Directory

This directory contains files that have been deprecated during the SHOT app architecture migration. Files are organized by their target feature area to make it easy to track what was replaced.

## Directory Structure

```
_deprecated/
├── foundation/     # Replaced by new foundation modules
├── identity/       # Old auth and family management files
├── locker/         # Deprecated e-commerce files
├── pulse/          # Old content/social files
├── control/        # Deprecated admin files
├── schedule/       # Old event management files
├── clubhouse/      # Deprecated club management files
├── assess/         # Old evaluation files
├── perform/        # Deprecated performance tracking files
└── unknown/        # Files where target area is unclear
```

## Deprecation Process

1. **Move files here** instead of deleting during migration
2. **Document in area-specific README** why files were deprecated
3. **Set deletion date** (typically 2 months after migration)
4. **Verify no dependencies** before final deletion
5. **Remove after validation** in production

## File Naming Convention

When moving files here, maintain the original path structure:
- Original: `src/pages/Login.tsx`
- Deprecated: `src/_deprecated/identity/pages/Login.tsx`

## Deletion Schedule

Files can be safely deleted after:
- ✅ Feature migration is complete
- ✅ All tests pass with new implementation
- ✅ 2 months in production with no issues
- ✅ No references found in codebase
- ✅ Team approval for deletion

## Notes

- Keep git history by using `git mv` when moving files
- Update imports to show deprecation warnings if still referenced
- Document migration path in area-specific README files