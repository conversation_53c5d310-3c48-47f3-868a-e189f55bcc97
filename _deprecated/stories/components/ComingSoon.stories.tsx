// ABOUTME: ComingSoon.stories.tsx - Storybook stories for the ComingSoon component
// This file showcases different variations and use cases of the ComingSoon component

import type { Meta, StoryObj } from '@storybook/react';
import ComingSoon from './ComingSoon';
import { chatbubbles, construct, analytics, people, trophy, calendar } from 'ionicons/icons';

const meta = {
  title: 'Components/ComingSoon',
  component: ComingSoon,
  parameters: {
    layout: 'centered',
    backgrounds: {
      default: 'dark',
      values: [
        { name: 'dark', value: '#000000' },
        { name: 'light', value: '#ffffff' }
      ]
    }
  },
  tags: ['autodocs'],
  argTypes: {
    title: {
      control: 'text',
      description: 'The main title text'
    },
    description: {
      control: 'text',
      description: 'The description text below the title'
    },
    icon: {
      control: 'select',
      options: ['timeOutline', 'chatbubbles', 'construct', 'analytics', 'people', 'trophy', 'calendar'],
      mapping: {
        timeOutline: undefined, // Will use default
        chatbubbles: chatbubbles,
        construct: construct,
        analytics: analytics,
        people: people,
        trophy: trophy,
        calendar: calendar
      },
      description: 'The icon to display'
    },
    iconColor: {
      control: 'color',
      description: 'The color of the icon'
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes'
    }
  }
} satisfies Meta<typeof ComingSoon>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
  args: {
    // Uses default props
  }
};

// Communications feature example
export const Communications: Story = {
  args: {
    title: 'Team Communications Coming Soon!',
    description: 'We\'re building an amazing communication system to help you stay connected with your team. Features will include group messaging, announcements, and notification management.',
    icon: chatbubbles,
    iconColor: 'var(--shot-purple)'
  }
};

// Analytics feature example
export const Analytics: Story = {
  args: {
    title: 'Advanced Analytics',
    description: 'Detailed performance metrics and insights are on the way. Track progress, identify trends, and make data-driven decisions.',
    icon: analytics,
    iconColor: 'var(--shot-gold)'
  }
};

// Team management example
export const TeamManagement: Story = {
  args: {
    title: 'Team Roster Management',
    description: 'Enhanced team management features coming soon. Easily organize players, manage positions, and track availability.',
    icon: people,
    iconColor: 'var(--shot-teal)'
  }
};

// Tournament feature
export const Tournaments: Story = {
  args: {
    title: 'Tournament Mode',
    description: 'Create and manage tournaments with ease. Bracket generation, match scheduling, and live standings coming soon.',
    icon: trophy,
    iconColor: '#FFD700'
  }
};

// Construction/Development
export const UnderConstruction: Story = {
  args: {
    title: 'Under Construction',
    description: 'Our team is working hard to bring you this feature. Check back soon for updates!',
    icon: construct,
    iconColor: '#FF6B6B'
  }
};

// Minimal version
export const Minimal: Story = {
  args: {
    title: 'Coming Soon',
    description: 'This feature is in development.',
    iconColor: '#666666'
  }
};

// Custom styling example
export const CustomStyling: Story = {
  args: {
    title: 'Exciting Features Ahead',
    description: 'We have amazing things planned for you. Stay tuned!',
    className: 'bg-gradient-to-br from-purple-900 to-pink-900 rounded-lg p-8'
  }
};