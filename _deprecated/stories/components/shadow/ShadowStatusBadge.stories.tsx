// ABOUTME: Storybook stories for the ShadowStatusBadge component
// Demonstrates different status states, sizes, and animations

import type { Meta, StoryObj } from '@storybook/react';
import ShadowStatusBadge from './ShadowStatusBadge';

const meta: Meta<typeof ShadowStatusBadge> = {
  title: 'Shadow DOM/Atoms/ShadowStatusBadge',
  component: ShadowStatusBadge,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Shadow DOM status badge component for displaying document or item statuses.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    status: {
      control: 'select',
      options: ['draft', 'published', 'cancelled', 'completed'],
      description: 'Status type of the badge',
    },
    className: {
      control: 'text',
      description: 'Additional CSS class for styling',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Draft status
export const Draft: Story = {
  args: {
    status: 'draft',
  },
};

// Published status
export const Published: Story = {
  args: {
    status: 'published',
  },
};

// Cancelled status
export const Cancelled: Story = {
  args: {
    status: 'cancelled',
  },
};

// Completed status
export const Completed: Story = {
  args: {
    status: 'completed',
  },
};

// All statuses
export const AllStatuses: Story = {
  render: () => (
    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>
      <ShadowStatusBadge status="draft" />
      <ShadowStatusBadge status="published" />
      <ShadowStatusBadge status="cancelled" />
      <ShadowStatusBadge status="completed" />
    </div>
  ),
};


// In context
export const InContext: Story = {
  render: () => (
    <div style={{ 
      border: '1px solid #e0e0e0', 
      borderRadius: '8px', 
      padding: '1rem',
      maxWidth: '300px'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h3 style={{ margin: 0 }}>User Account</h3>
        <ShadowStatusBadge status="published" />
      </div>
      <p style={{ marginTop: '0.5rem', marginBottom: 0, color: '#666' }}>
        Last login: 2 hours ago
      </p>
    </div>
  ),
};