// ABOUTME: Storybook stories for the ShadowEventCard component  
// Demonstrates event cards with dates, titles, and various states

import type { Meta, StoryObj } from '@storybook/react';
import { ShadowEventCard } from './ShadowEventCard';

const meta: Meta<typeof ShadowEventCard> = {
  title: 'Shadow DOM/Molecules/ShadowEventCard',
  component: ShadowEventCard,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Shadow DOM event card component for displaying event information with date, time, title, and location.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: 'select',
      options: ['training', 'match', 'session', 'event'],
      description: 'Event type',
    },
    title: {
      control: 'text',
      description: 'Event title',
    },
    date: {
      control: 'text',
      description: 'Event date',
    },
    time: {
      control: 'text',
      description: 'Event time',
    },
    status: {
      control: 'select',
      options: ['upcoming', 'complete', 'in-progress'],
      description: 'Event status',
    },
    completionPercentage: {
      control: 'number',
      description: 'Completion percentage (0-100)',
      min: 0,
      max: 100,
    },
    evaluationsCompleted: {
      control: 'number',
      description: 'Number of evaluations completed',
    },
    evaluationsTotal: {
      control: 'number',
      description: 'Total number of evaluations',
    },
    onClick: {
      description: 'Click handler',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default event
export const Default: Story = {
  args: {
    title: 'Team Practice',
    date: 'March 15, 2024',
    time: '3:00 PM',
    location: 'Main Field',
  },
};

// Match event
export const Match: Story = {
  args: {
    title: 'Championship Match',
    date: 'March 20, 2024',
    time: '7:00 PM',
    location: 'Stadium A',
    type: 'match',
    status: 'upcoming',
  },
};

// Training event
export const Training: Story = {
  args: {
    title: 'Skills Training',
    date: 'March 18, 2024',
    time: '4:00 PM',
    location: 'Training Ground',
    type: 'training',
    description: 'Focus on defensive strategies',
  },
};

// Cancelled event
export const Cancelled: Story = {
  args: {
    title: 'Team Meeting',
    date: 'March 16, 2024',
    time: '6:00 PM',
    location: 'Conference Room',
    type: 'meeting',
    status: 'cancelled',
  },
};

// Different statuses
export const EventStatuses: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem', width: '350px' }}>
      <ShadowEventCard 
        title="Upcoming Match"
        date="March 25, 2024"
        time="5:00 PM"
        location="Field 1"
        status="upcoming"
      />
      <ShadowEventCard 
        title="Live Game"
        date="Today"
        time="Now"
        location="Stadium"
        status="ongoing"
      />
      <ShadowEventCard 
        title="Past Training"
        date="March 10, 2024"
        time="3:00 PM"
        location="Gym"
        status="completed"
      />
      <ShadowEventCard 
        title="Cancelled Meeting"
        date="March 12, 2024"
        time="2:00 PM"
        location="Room 101"
        status="cancelled"
      />
    </div>
  ),
};

// Event types
export const EventTypes: Story = {
  render: () => (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '1rem' }}>
      <ShadowEventCard 
        title="League Match"
        date="April 1, 2024"
        time="7:30 PM"
        type="match"
      />
      <ShadowEventCard 
        title="Training Session"
        date="April 2, 2024"
        time="4:00 PM"
        type="training"
      />
      <ShadowEventCard 
        title="Team Session"
        date="April 3, 2024"
        time="6:00 PM"
        type="session"
      />
      <ShadowEventCard 
        title="Tournament"
        date="April 5-7, 2024"
        time="All Day"
        type="tournament"
      />
    </div>
  ),
};

// Clickable events
export const ClickableEvents: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem' }}>
      <ShadowEventCard 
        title="View Details"
        date="March 30, 2024"
        time="2:00 PM"
        location="Click for info"
        onClick={() => console.log('Event clicked')}
      />
    </div>
  ),
};

// Calendar view
export const CalendarView: Story = {
  render: () => (
    <div style={{ maxWidth: '800px' }}>
      <h3 style={{ marginBottom: '1rem' }}>This Week's Schedule</h3>
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))', gap: '1rem' }}>
        <ShadowEventCard 
          title="Monday Practice"
          date="Mon, Mar 18"
          time="4:00 PM"
          type="training"
        />
        <ShadowEventCard 
          title="Team Session"
          date="Tue, Mar 19"
          time="6:00 PM"
          type="session"
        />
        <ShadowEventCard 
          title="Friendly Match"
          date="Wed, Mar 20"
          time="7:00 PM"
          type="match"
        />
        <ShadowEventCard 
          title="Recovery Session"
          date="Thu, Mar 21"
          time="3:00 PM"
          type="training"
        />
        <ShadowEventCard 
          title="League Game"
          date="Sat, Mar 23"
          time="2:00 PM"
          type="match"
          status="upcoming"
        />
      </div>
    </div>
  ),
};