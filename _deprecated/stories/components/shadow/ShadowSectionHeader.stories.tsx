// ABOUTME: Storybook stories for the ShadowSectionHeader component
// Demonstrates section headers with different styles and configurations

import type { Meta, StoryObj } from '@storybook/react';
import { ShadowSectionHeader } from './ShadowSectionHeader';

const meta: Meta<typeof ShadowSectionHeader> = {
  title: 'Shadow DOM/Atoms/ShadowSectionHeader',
  component: ShadowSectionHeader,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Shadow DOM section header component for organizing content into sections.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    title: {
      control: 'text',
      description: 'Main title text',
    },
    subtitle: {
      control: 'text',
      description: 'Optional subtitle text',
    },
    icon: {
      control: 'text',
      description: 'Optional icon name',
    },
    variant: {
      control: 'select',
      options: ['default', 'primary', 'secondary', 'dark'],
      description: 'Visual variant of the header',
    },
    size: {
      control: 'radio',
      options: ['small', 'medium', 'large'],
      description: 'Size of the header',
    },
    centered: {
      control: 'boolean',
      description: 'Center align the header content',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default header
export const Default: Story = {
  args: {
    title: 'Section Title',
  },
};

// With subtitle
export const WithSubtitle: Story = {
  args: {
    title: 'Main Section',
    subtitle: 'This is a descriptive subtitle for the section',
  },
};

// With icon
export const WithIcon: Story = {
  args: {
    title: 'Settings',
    icon: 'settings',
  },
};

// Complete header
export const Complete: Story = {
  args: {
    title: 'Team Overview',
    subtitle: 'Manage your team members and their roles',
    icon: 'users',
  },
};

// Different variants
export const Variants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem', width: '400px' }}>
      <ShadowSectionHeader title="Default Variant" variant="default" />
      <ShadowSectionHeader title="Primary Variant" variant="primary" />
      <ShadowSectionHeader title="Secondary Variant" variant="secondary" />
      <ShadowSectionHeader title="Dark Variant" variant="dark" />
    </div>
  ),
};

// Different sizes
export const Sizes: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem', width: '400px' }}>
      <ShadowSectionHeader title="Small Header" size="small" />
      <ShadowSectionHeader title="Medium Header" size="medium" />
      <ShadowSectionHeader title="Large Header" size="large" />
    </div>
  ),
};

// Centered alignment
export const Centered: Story = {
  args: {
    title: 'Centered Header',
    subtitle: 'This header is center-aligned',
    centered: true,
  },
};

// In context
export const InContext: Story = {
  render: () => (
    <div style={{ width: '600px', padding: '2rem', background: '#f5f5f5' }}>
      <ShadowSectionHeader 
        title="Recent Activities" 
        subtitle="View your team's latest updates"
        icon="activity"
      />
      <div style={{ marginTop: '1rem', padding: '1rem', background: 'white', borderRadius: '8px' }}>
        <p>Content goes here...</p>
      </div>
    </div>
  ),
};