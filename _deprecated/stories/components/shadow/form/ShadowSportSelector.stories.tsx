// ABOUTME: 
// Storybook stories for ShadowSportSelector component showcasing sport selection grid

import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import ShadowSportSelector from './ShadowSportSelector';

const meta = {
  title: 'Shadow/Form/ShadowSportSelector',
  component: ShadowSportSelector,
  parameters: {
    layout: 'centered',
    backgrounds: {
      default: 'dark',
      values: [
        { name: 'dark', value: '#1a1a1a' },
        { name: 'shot-dark', value: '#000000' },
      ],
    },
  },
  tags: ['autodocs'],
  argTypes: {
    onChange: { action: 'changed' },
    value: { control: 'text' },
    disabled: { control: 'boolean' },
    required: { control: 'boolean' },
    label: { control: 'text' },
    error: { control: 'text' },
    helpText: { control: 'text' },
  },
} satisfies Meta<typeof ShadowSportSelector>;

export default meta;
type Story = StoryObj<typeof meta>;

const customSports = [
  { value: 'swimming', label: 'Swimming', image: '🏊' },
  { value: 'cycling', label: 'Cycling', image: '🚴' },
  { value: 'running', label: 'Running', image: '🏃' },
  { value: 'golf', label: 'Golf', image: '⛳' },
  { value: 'volleyball', label: 'Volleyball', image: '🏐' },
  { value: 'baseball', label: 'Baseball', image: '⚾' },
];

const olympicSports = [
  { value: 'archery', label: 'Archery', image: '🏹' },
  { value: 'fencing', label: 'Fencing', image: '🤺' },
  { value: 'weightlifting', label: 'Weightlifting', image: '🏋️' },
  { value: 'gymnastics', label: 'Gymnastics', image: '🤸' },
  { value: 'rowing', label: 'Rowing', image: '🚣' },
  { value: 'equestrian', label: 'Equestrian', image: '🏇' },
];

export const Default: Story = {
  args: {
    label: 'Select Your Sport',
  },
};

export const WithValue: Story = {
  args: {
    label: 'Favorite Sport',
    value: 'football',
  },
};

export const Required: Story = {
  args: {
    label: 'Primary Sport',
    required: true,
  },
};

export const WithError: Story = {
  args: {
    label: 'Sport Selection',
    error: 'Please select a sport to continue',
  },
};

export const WithHelpText: Story = {
  args: {
    label: 'Choose Your Activity',
    helpText: 'Select the sport you play most frequently',
  },
};

export const Disabled: Story = {
  args: {
    label: 'Sport Selection',
    value: 'basketball',
    disabled: true,
  },
};

export const CustomSports: Story = {
  args: {
    label: 'Olympic Sports',
    sports: customSports,
  },
};

export const OlympicSports: Story = {
  args: {
    label: 'Summer Olympics',
    sports: olympicSports,
  },
};

export const MinimalSports: Story = {
  args: {
    label: 'Quick Selection',
    sports: [
      { value: 'soccer', label: 'Soccer', image: '⚽' },
      { value: 'basketball', label: 'Basketball', image: '🏀' },
      { value: 'tennis', label: 'Tennis', image: '🎾' },
    ],
  },
};

export const NoLabel: Story = {
  args: {
    sports: [
      { value: 'football', label: 'Football', image: '⚽' },
      { value: 'cricket', label: 'Cricket', image: '🏏' },
      { value: 'rugby', label: 'Rugby', image: '🏉' },
      { value: 'hockey', label: 'Hockey', image: '🏒' },
    ],
  },
};

export const Interactive: Story = {
  render: () => {
    const [selectedSport, setSelectedSport] = React.useState('');
    
    const sportDescriptions: { [key: string]: string } = {
      football: 'The beautiful game, played by millions worldwide',
      boxing: 'The sweet science of self-defense and strategy',
      basketball: 'Fast-paced court action with high-flying dunks',
      tennis: 'Elegant racquet sport requiring precision and endurance',
      cricket: 'Strategic bat-and-ball game with rich traditions',
      rugby: 'Physical team sport with honor and camaraderie',
    };
    
    return (
      <div style={{ minWidth: '400px' }}>
        <ShadowSportSelector
          label="What's your sport?"
          value={selectedSport}
          onChange={(e) => setSelectedSport(e.detail.value)}
          helpText={selectedSport ? sportDescriptions[selectedSport] : 'Click on a sport to learn more'}
        />
        {selectedSport && (
          <div style={{ marginTop: '20px', padding: '16px', backgroundColor: '#222', borderRadius: '8px' }}>
            <p style={{ color: 'white', margin: 0, fontFamily: 'Poppins, sans-serif' }}>
              You selected: <strong style={{ color: '#1ABC9C' }}>{selectedSport}</strong>
            </p>
          </div>
        )}
      </div>
    );
  },
};

export const FormExample: Story = {
  render: () => {
    const [formData, setFormData] = React.useState({
      primarySport: '',
      secondarySport: '',
    });
    
    const [errors, setErrors] = React.useState({
      primarySport: '',
      secondarySport: '',
    });
    
    const handleChange = (field: string) => (e: CustomEvent) => {
      const newValue = e.detail.value;
      
      // Don't allow same sport for both selections
      if (field === 'primarySport' && newValue === formData.secondarySport) {
        setErrors(prev => ({
          ...prev,
          primarySport: 'Primary and secondary sports must be different',
        }));
        return;
      }
      
      if (field === 'secondarySport' && newValue === formData.primarySport) {
        setErrors(prev => ({
          ...prev,
          secondarySport: 'Secondary sport must be different from primary',
        }));
        return;
      }
      
      setFormData(prev => ({
        ...prev,
        [field]: newValue,
      }));
      
      // Clear error when user selects a valid value
      setErrors(prev => ({
        ...prev,
        [field]: '',
      }));
    };
    
    const handleSubmit = () => {
      const newErrors = {
        primarySport: !formData.primarySport ? 'Please select your primary sport' : '',
        secondarySport: !formData.secondarySport ? 'Please select your secondary sport' : '',
      };
      setErrors(newErrors);
    };
    
    return (
      <div style={{ minWidth: '500px', padding: '20px', backgroundColor: '#222', borderRadius: '8px' }}>
        <h3 style={{ color: 'white', marginBottom: '20px', fontFamily: 'Poppins, sans-serif' }}>
          Athlete Profile
        </h3>
        
        <ShadowSportSelector
          label="Primary Sport"
          value={formData.primarySport}
          onChange={handleChange('primarySport')}
          required
          error={errors.primarySport}
        />
        
        <ShadowSportSelector
          label="Secondary Sport"
          value={formData.secondarySport}
          onChange={handleChange('secondarySport')}
          required
          error={errors.secondarySport}
          helpText="Choose a different sport from your primary"
        />
        
        <button
          onClick={handleSubmit}
          style={{
            marginTop: '20px',
            padding: '10px 20px',
            backgroundColor: '#6B00DB',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            fontFamily: 'Poppins, sans-serif',
            fontSize: '16px',
          }}
        >
          Submit Profile
        </button>
        
        {formData.primarySport && formData.secondarySport && (
          <div style={{ marginTop: '24px', padding: '16px', backgroundColor: '#333', borderRadius: '4px' }}>
            <pre style={{ color: '#1ABC9C', margin: 0, fontSize: '14px' }}>
              {JSON.stringify(formData, null, 2)}
            </pre>
          </div>
        )}
      </div>
    );
  },
};

export const ResponsiveGrid: Story = {
  args: {
    label: 'Select Sport (Resize window to see responsive behavior)',
    sports: [
      { value: 'soccer', label: 'Soccer', image: '⚽' },
      { value: 'basketball', label: 'Basketball', image: '🏀' },
      { value: 'baseball', label: 'Baseball', image: '⚾' },
      { value: 'tennis', label: 'Tennis', image: '🎾' },
      { value: 'golf', label: 'Golf', image: '⛳' },
      { value: 'swimming', label: 'Swimming', image: '🏊' },
      { value: 'cycling', label: 'Cycling', image: '🚴' },
      { value: 'running', label: 'Running', image: '🏃' },
      { value: 'volleyball', label: 'Volleyball', image: '🏐' },
    ],
    helpText: 'Grid changes from 3 columns to 2 on mobile screens',
  },
};