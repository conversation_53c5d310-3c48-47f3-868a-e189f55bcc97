// ABOUTME: 
// Storybook stories for ShadowRadioGroup component showcasing different layouts and states

import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import ShadowRadioGroup from './ShadowRadioGroup';

const meta = {
  title: 'Shadow/Form/ShadowRadioGroup',
  component: ShadowRadioGroup,
  parameters: {
    layout: 'centered',
    backgrounds: {
      default: 'dark',
      values: [
        { name: 'dark', value: '#1a1a1a' },
        { name: 'shot-dark', value: '#000000' },
      ],
    },
  },
  tags: ['autodocs'],
  argTypes: {
    onChange: { action: 'changed' },
    value: { control: 'text' },
    disabled: { control: 'boolean' },
    required: { control: 'boolean' },
    label: { control: 'text' },
    error: { control: 'text' },
    helpText: { control: 'text' },
    layout: {
      control: { type: 'select' },
      options: ['vertical', 'horizontal'],
    },
  },
} satisfies Meta<typeof ShadowRadioGroup>;

export default meta;
type Story = StoryObj<typeof meta>;

const genderOptions = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
  { value: 'prefer-not-to-say', label: 'Prefer not to say' },
];

const experienceOptions = [
  { value: 'beginner', label: 'Beginner (0-1 years)' },
  { value: 'intermediate', label: 'Intermediate (2-5 years)' },
  { value: 'advanced', label: 'Advanced (5-10 years)' },
  { value: 'expert', label: 'Expert (10+ years)' },
];

const planOptions = [
  { value: 'free', label: 'Free Plan' },
  { value: 'basic', label: 'Basic ($9/month)' },
  { value: 'pro', label: 'Pro ($29/month)' },
  { value: 'enterprise', label: 'Enterprise (Contact us)' },
];

export const Default: Story = {
  args: {
    name: 'gender',
    label: 'Gender',
    options: genderOptions,
  },
};

export const WithValue: Story = {
  args: {
    name: 'experience',
    label: 'Experience Level',
    value: 'intermediate',
    options: experienceOptions,
  },
};

export const Required: Story = {
  args: {
    name: 'plan',
    label: 'Select a Plan',
    options: planOptions,
    required: true,
  },
};

export const HorizontalLayout: Story = {
  args: {
    name: 'size',
    label: 'T-Shirt Size',
    layout: 'horizontal',
    options: [
      { value: 'xs', label: 'XS' },
      { value: 's', label: 'S' },
      { value: 'm', label: 'M' },
      { value: 'l', label: 'L' },
      { value: 'xl', label: 'XL' },
      { value: 'xxl', label: 'XXL' },
    ],
  },
};

export const WithError: Story = {
  args: {
    name: 'agreement',
    label: 'Do you agree to the terms?',
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
    ],
    error: 'This field is required',
  },
};

export const WithHelpText: Story = {
  args: {
    name: 'notifications',
    label: 'Email Notifications',
    options: [
      { value: 'all', label: 'All notifications' },
      { value: 'important', label: 'Important only' },
      { value: 'none', label: 'No notifications' },
    ],
    helpText: 'You can change this setting anytime in your preferences',
  },
};

export const Disabled: Story = {
  args: {
    name: 'disabled-options',
    label: 'Service Type',
    value: 'standard',
    options: [
      { value: 'standard', label: 'Standard Shipping' },
      { value: 'express', label: 'Express Shipping' },
      { value: 'overnight', label: 'Overnight Shipping' },
    ],
    disabled: true,
  },
};

export const LongLabels: Story = {
  args: {
    name: 'preferences',
    label: 'Privacy Preferences',
    layout: 'vertical',
    options: [
      { value: 'public', label: 'Public - Anyone can see your profile and activities' },
      { value: 'friends', label: 'Friends Only - Only approved friends can see your content' },
      { value: 'private', label: 'Private - Your profile is completely hidden from others' },
    ],
  },
};

export const Interactive: Story = {
  render: () => {
    const [selectedValue, setSelectedValue] = React.useState('medium');
    
    const coffeeOptions = [
      { value: 'small', label: 'Small (8oz)' },
      { value: 'medium', label: 'Medium (12oz)' },
      { value: 'large', label: 'Large (16oz)' },
      { value: 'extra-large', label: 'Extra Large (20oz)' },
    ];
    
    return (
      <div style={{ minWidth: '300px' }}>
        <ShadowRadioGroup
          name="coffee-size"
          label="Coffee Size"
          value={selectedValue}
          options={coffeeOptions}
          onChange={(e) => setSelectedValue(e.detail.value)}
          helpText={`You selected: ${selectedValue}`}
        />
        <div style={{ marginTop: '20px', padding: '16px', backgroundColor: '#222', borderRadius: '4px' }}>
          <p style={{ color: 'white', margin: 0, fontFamily: 'Poppins, sans-serif' }}>
            Current selection: <strong style={{ color: '#6B00DB' }}>{selectedValue}</strong>
          </p>
        </div>
      </div>
    );
  },
};

export const FormExample: Story = {
  render: () => {
    const [formData, setFormData] = React.useState({
      deliverySpeed: 'standard',
      paymentMethod: 'credit',
    });
    
    const handleChange = (field: string) => (e: CustomEvent) => {
      setFormData(prev => ({
        ...prev,
        [field]: e.detail.value,
      }));
    };
    
    return (
      <div style={{ minWidth: '400px', padding: '20px', backgroundColor: '#222', borderRadius: '8px' }}>
        <h3 style={{ color: 'white', marginBottom: '20px', fontFamily: 'Poppins, sans-serif' }}>
          Checkout Options
        </h3>
        
        <ShadowRadioGroup
          name="delivery"
          label="Delivery Speed"
          value={formData.deliverySpeed}
          options={[
            { value: 'standard', label: 'Standard (5-7 days) - Free' },
            { value: 'express', label: 'Express (2-3 days) - $9.99' },
            { value: 'overnight', label: 'Overnight - $24.99' },
          ]}
          onChange={handleChange('deliverySpeed')}
          required
        />
        
        <ShadowRadioGroup
          name="payment"
          label="Payment Method"
          value={formData.paymentMethod}
          options={[
            { value: 'credit', label: 'Credit Card' },
            { value: 'paypal', label: 'PayPal' },
            { value: 'apple', label: 'Apple Pay' },
            { value: 'google', label: 'Google Pay' },
          ]}
          onChange={handleChange('paymentMethod')}
          layout="horizontal"
          required
        />
        
        <div style={{ marginTop: '24px', padding: '16px', backgroundColor: '#333', borderRadius: '4px' }}>
          <pre style={{ color: '#6B00DB', margin: 0, fontSize: '14px' }}>
            {JSON.stringify(formData, null, 2)}
          </pre>
        </div>
      </div>
    );
  },
};

export const NoLabel: Story = {
  args: {
    name: 'quick-choice',
    options: [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2' },
      { value: 'option3', label: 'Option 3' },
    ],
  },
};

export const TwoOptions: Story = {
  args: {
    name: 'binary',
    label: 'Enable Feature?',
    options: [
      { value: 'yes', label: 'Yes' },
      { value: 'no', label: 'No' },
    ],
    layout: 'horizontal',
  },
};