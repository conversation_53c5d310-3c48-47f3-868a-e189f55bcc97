// ABOUTME: Storybook stories for the ShadowBackButton component
// Demonstrates the back navigation button with different states and configurations

import type { Meta, StoryObj } from '@storybook/react';
import { ShadowBackButton } from './ShadowBackButton';

const meta: Meta<typeof ShadowBackButton> = {
  title: 'Shadow DOM/Atoms/ShadowBackButton',
  component: ShadowBackButton,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Shadow DOM back button component for navigation. Typically used in headers or navigation bars.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    text: {
      control: 'text',
      description: 'Text label for the back button',
    },
    color: {
      control: 'select',
      options: ['white', 'teal', 'purple', 'gold', 'green'],
      description: 'Text color of the button',
    },
    iconColor: {
      control: 'select',
      options: ['white', 'teal', 'purple', 'gold', 'green'],
      description: 'Icon color (defaults to text color)',
    },
    onClick: {
      description: 'Click event handler for navigation',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default back button
export const Default: Story = {
  args: {
    text: 'Back',
    onClick: () => console.log('Navigate back'),
  },
};

// Back button with custom text
export const CustomText: Story = {
  args: {
    text: 'Go to Dashboard',
    onClick: () => console.log('Navigate to dashboard'),
  },
};

// Different colors
export const ColorVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
      <div style={{ background: '#333', padding: '1rem' }}>
        <ShadowBackButton text="White" color="white" onClick={() => console.log('White')} />
      </div>
      <div style={{ background: '#f5f5f5', padding: '1rem' }}>
        <ShadowBackButton text="Teal" color="teal" onClick={() => console.log('Teal')} />
      </div>
      <div style={{ background: '#f5f5f5', padding: '1rem' }}>
        <ShadowBackButton text="Purple" color="purple" onClick={() => console.log('Purple')} />
      </div>
      <div style={{ background: '#f5f5f5', padding: '1rem' }}>
        <ShadowBackButton text="Gold" color="gold" onClick={() => console.log('Gold')} />
      </div>
      <div style={{ background: '#f5f5f5', padding: '1rem' }}>
        <ShadowBackButton text="Green" color="green" onClick={() => console.log('Green')} />
      </div>
    </div>
  ),
};

// With different icon colors
export const IconColors: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem', background: '#f5f5f5', padding: '1rem' }}>
      <ShadowBackButton text="Back" color="teal" iconColor="purple" onClick={() => console.log('Mixed colors')} />
      <ShadowBackButton text="Back" color="gold" iconColor="green" onClick={() => console.log('Mixed colors')} />
    </div>
  ),
};

// In header context
export const InHeader: Story = {
  render: () => (
    <div style={{ 
      width: '100%', 
      background: '#f8f9fa', 
      padding: '1rem',
      display: 'flex',
      alignItems: 'center',
      gap: '1rem',
      borderBottom: '1px solid #dee2e6'
    }}>
      <ShadowBackButton onClick={() => console.log('Navigate back')} />
      <h2 style={{ margin: 0, fontSize: '1.25rem' }}>Page Title</h2>
    </div>
  ),
};