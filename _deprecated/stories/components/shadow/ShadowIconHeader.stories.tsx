// ABOUTME: Storybook stories for the ShadowIconHeader component
// Demonstrates icon headers with different styles, colors and configurations

import type { Meta, StoryObj } from '@storybook/react';
import { ShadowIconHeader } from './ShadowIconHeader';

const meta: Meta<typeof ShadowIconHeader> = {
  title: 'Shadow DOM/Atoms/ShadowIconHeader',
  component: ShadowIconHeader,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A Shadow DOM icon header component for displaying headers with prominent icons.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    icon: {
      control: 'text',
      description: 'Icon name or emoji',
    },
    title: {
      control: 'text',
      description: 'Header title text',
    },
    subtitle: {
      control: 'text',
      description: 'Optional subtitle text',
    },
    color: {
      control: 'text',
      description: 'Icon color (hex or color name)',
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large'],
      description: 'Size of the icon header',
    },
    onClick: {
      description: 'Click event handler',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default icon header
export const Default: Story = {
  args: {
    icon: '🏆',
    title: 'Achievements',
  },
};

// With subtitle
export const WithSubtitle: Story = {
  args: {
    icon: '📊',
    title: 'Analytics',
    subtitle: 'View your performance metrics',
  },
};

// Custom color
export const CustomColor: Story = {
  args: {
    icon: '⚡',
    title: 'Quick Actions',
    color: '#FF6B6B',
  },
};

// Clickable header
export const Clickable: Story = {
  args: {
    icon: '⚙️',
    title: 'Settings',
    subtitle: 'Click to open settings',
    onClick: () => console.log('Header clicked'),
  },
};

// Different sizes
export const Sizes: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '2rem', alignItems: 'flex-start' }}>
      <ShadowIconHeader icon="📱" title="Small" size="small" />
      <ShadowIconHeader icon="💻" title="Medium" size="medium" />
      <ShadowIconHeader icon="🖥️" title="Large" size="large" />
    </div>
  ),
};

// Various icons
export const IconVariety: Story = {
  render: () => (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '2rem' }}>
      <ShadowIconHeader icon="🎯" title="Goals" />
      <ShadowIconHeader icon="👥" title="Teams" />
      <ShadowIconHeader icon="📈" title="Growth" />
      <ShadowIconHeader icon="🔔" title="Notifications" />
      <ShadowIconHeader icon="💬" title="Messages" />
      <ShadowIconHeader icon="⭐" title="Featured" />
    </div>
  ),
};

// With colors
export const ColorVariations: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '2rem', flexWrap: 'wrap' }}>
      <ShadowIconHeader icon="🔥" title="Hot" color="#FF4757" />
      <ShadowIconHeader icon="🌊" title="Cool" color="#3498DB" />
      <ShadowIconHeader icon="🌿" title="Fresh" color="#2ECC71" />
      <ShadowIconHeader icon="☀️" title="Bright" color="#F39C12" />
      <ShadowIconHeader icon="🌙" title="Dark" color="#34495E" />
    </div>
  ),
};

// In card context
export const InCard: Story = {
  render: () => (
    <div style={{ 
      width: '300px', 
      padding: '1.5rem',
      background: 'white',
      borderRadius: '12px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
    }}>
      <ShadowIconHeader 
        icon="📊" 
        title="Performance Report" 
        subtitle="Last 30 days"
        color="#6B00DB"
      />
      <div style={{ marginTop: '1rem', color: '#666' }}>
        <p>Your team has improved by 23% this month.</p>
      </div>
    </div>
  ),
};