import React from 'react';
import { IonContent, IonPage, IonHeader, IonToolbar, IonTitle, IonButton } from '@ionic/react';
import { useHistory } from 'react-router-dom';

const TestPage: React.FC = () => {
  const history = useHistory();

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>Test Page</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent className="ion-padding">
        <h1>This is a test page</h1>
        <p>If you can see this, the routing is working correctly!</p>
        
        <IonButton onClick={() => history.push('/coach/events')}>
          Go Back to Events
        </IonButton>
      </IonContent>
    </IonPage>
  );
};

export default TestPage;