// Import the debug profile page
// import DebugProfile from './pages/DebugProfile'; // Moved to outbox
// import StandardHeaderTest from './pages/StandardHeaderTest'; // Moved to outbox
// import SimpleDropdownTest from './pages/SimpleDropdownTest'; // Moved to outbox

// Import demo pages
// import LoadingDemo from './pages/LoadingDemo'; // Moved to outbox
// import AccountTest from './pages/AccountTest'; // Moved to outbox
import EmailVerificationPage from '../src/pages/EmailVerificationPage';
// import { TestNavigation } from './pages/TestNavigation'; // Moved to outbox
// import { TestNavigation as TestNavigationClean } from './pages/TestNavigationClean'; // Moved to outbox
// import { TransparencyDebug } from './pages/TransparencyDebug'; // Moved to outbox
// import { TestTransparencyBright } from './pages/TestTransparencyBright'; // Moved to outbox
// import { TestTransparencyDirect } from './pages/TestTransparencyDirect'; // Moved to outbox
// import { TestDemoStyle } from './pages/TestDemoStyle'; // Moved to outbox
// import { TestDemoStyleSimple } from './pages/TestDemoStyleSimple'; // Moved to outbox
// import { TestDemoStandalone } from './pages/TestDemoStandalone'; // Moved to outbox
// import { TestDemoDebug } from './pages/TestDemoDebug'; // Moved to outbox
// import { TestDemoNoComponents } from './pages/TestDemoNoComponents'; // Moved to outbox
// import { TestDemoInline } from './pages/TestDemoInline'; // Moved to outbox

import React, { useState, Suspense } from 'react';
import { Route, Redirect, Switch, useLocation } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { ProfileCompletionGuard } from '../src/components/ProfileCompletionGuard';

// Import V2 Routes
import { V2Routes } from '../src/routes/V2Routes';

// Import AuthDebug component for troubleshooting
import AuthDebug from '../src/components/debug/AuthDebug';

// Import the Team form components
import EditTeam from '../src/pages/section/Coach/EditTeam';
import EditTeamSimple from '../src/pages/section/Coach/EditTeamSimple';
import EditTeamShadowDemo from '../src/pages/section/Coach/EditTeamShadowDemo';
import EditTeamShadow from '../src/pages/section/Coach/EditTeamShadow';

// Import Coming Soon component
import ComingSoon from '../src/components/ComingSoon';
// import TestBackgroundColors from './components/TestBackgroundColors';
import TestBackgroundColors from '../src/components/test/TestBackgroundColors';
// import BackgroundDebugReport from './components/BackgroundDebugReport';
import BackgroundDebugReport from '../src/components/debug/BackgroundDebugReport';

// Import Session Calendar and Team Stats pages
import SessionCalendar from '../src/pages/section/Coach/SessionCalendar/SessionCalendar';
import TeamStats from '../src/pages/section/Coach/TeamStats/TeamStats';

// Import the player management component
import TeamPlayerManager from '../src/pages/section/Coach/components/PlayerManagement/TeamPlayerManager';

// Import demo pages router
// import DemoRouter from './pages/demo/DemoRouter'; // Moved to outbox

// Import debug mode test page
// import DebugModeTest from './pages/v2/DebugModeTest'; // Moved to outbox

// STAGE 1 IMPORTS - Open Registration Flow
import OpenRegistration from '../src/pages/registration/OpenRegistration';
import MemberPerformV2 from '../src/pages/perform/MemberPerformV2';
import UpdatedLoginV2 from '../src/pages/UpdatedLoginV2';
// import Stage1TestPage from './pages/Stage1TestPage'; // Moved to outbox
// import DebugTest from './pages/test/DebugTest'; // Moved to outbox
import PrivateRoute from '../src/components/PrivateRoute';
import CoachProtectedRoute from '../src/components/functional/CoachProtectedRoute';
import ClubProtectedRoute from '../src/components/functional/ClubProtectedRoute';
import AdminProtectedRoute from '../src/components/functional/AdminProtectedRoute';
import ConditionalEvaluationProvider from '../src/components/ConditionalEvaluationProvider';

// Import only the required components for club administrators
import NewClubAdministrators from '../src/pages/section/Coach/supporting/ClubManagement/NewClubAdministrators';
import ClubAdminsComponent from '../src/pages/section/Coach/supporting/ClubManagement/ClubAdministrators';
// Import the simplified admin page for testing
import SimpleAdminPage from '../src/pages/section/Coach/supporting/ClubManagement/SimpleAdminPage';
// Import the Shadow DOM version of team creation
import CreateTeamShadow from '../src/pages/section/Coach/supporting/ClubManagement/components/CreateTeamShadow';

// Event components that are still needed for team-level events
import EventsList from '../src/pages/section/Coach/events/EventsList';
import EventPage from '../src/pages/section/Coach/events/EventPage';
// Re-enable EventEvaluation to fix the navigation error
import EventEvaluation from '../src/pages/section/Coach/events/EventEvaluation';
import EventEvaluationTest from '../src/pages/section/Coach/events/EventEvaluationTest';
import EventParticipantsList from '../src/pages/section/Coach/events/EventParticipantsList';
import SmsStatusPage from '../src/pages/section/Coach/events/SmsStatusPage';
import SimpleSmsStatusPage from '../src/pages/section/Coach/events/SimpleSmsStatusPage';
import EventPreEvaluationNotifications from '../src/pages/section/Coach/events/EventPreEvaluationNotifications';
// import ScrollFixPage from './pages/ScrollFixPage'; // Moved to outbox
import ButtonShowcasePage from '../src/pages/section/ButtonShowcase'; // Import the ButtonShowcase page
import DesignSystem from '../src/pages/v2/DesignSystem'; // Import the DesignSystem page (now imports from index.tsx)

// Import player self-evaluation components
import PlayerSelfEvaluationPage from '../src/pages/section/Coach/events/PlayerSelfEvaluationPage';
import SelfEvaluationSummary from '../src/pages/section/Coach/events/SelfEvaluationSummary';
import { PlayerSelfEvaluationForm } from '../src/pages/section/Coach/components';

// Import player evaluation history components
import PlayerEvaluationHistory from '../src/pages/section/Player/PlayerEvaluationHistory';
import PlayerProgressView from '../src/pages/section/Player/PlayerProgressView';

// Team imports
import TeamFlat from '../src/pages/section/Coach/TeamFlat';
import TeamAddPlayer from '../src/pages/section/Coach/supporting/ClubManagement/TeamAddPlayer';
import CreateEvent from '../src/pages/section/Coach/createEvent';
import QuickAddEventPage from '../src/pages/section/Coach/supporting/EventManagement/QuickAddEventPage';
import EvaluationsPage from '../src/pages/section/Coach/evaluations/EvaluationsPage';

// Perform Framework imports
import PerformFrameworkPage from '../src/pages/section/Coach/supporting/PerformFramework';
// import TestBasicPage from './pages/section/Coach/TestBasicPage'; // Moved to outbox
// import SuperBasicPage from './pages/section/Coach/SuperBasicPage'; // Moved to outbox
// import EventCardTestPage from './pages/section/Coach/EventCardTestPage'; // Moved to outbox
// import DirectTestPage from './pages/section/Coach/DirectTestPage'; // Moved to outbox
// import ExactFormatTestPage from './pages/section/Coach/ExactFormatTestPage'; // Moved to outbox
// import ShotColorTestPage from './pages/section/Coach/ShotColorTestPage'; // Moved to outbox
import { TeamComms } from '../src/pages/section/Coach/TeamComms'; // Import the Team Communications component
import PlayerDetailPage from '../src/pages/section/Coach/PlayerDetailPage'; // Import the Player Detail Page

// Club Management imports
import ClubDeletionDebugger from '../src/pages/section/Coach/supporting/ClubManagement/ClubDeletionDebugger';

// Import specific ViewIDP component directly
// import ViewIDPEvaluationStyle from './pages/ViewIDP_EvaluationStyle';

// Import all pages from your pages index
import {
  // Pages used for the fixed paths
  LoginPage,
  ActivitiesPage,
  SplashPage,
  AccountPage,
  SignupPage,
  ResetPasswordPage,
  RegistrationPage,
  InvitedRegistrationPage,
  HomePage,
  FamilyPage,
  EventsPage,
  // VideosPage, // Moved to features/pulse
  MembershipPage,
  CheckoutPage,
  ViewIDPPage,
  ImproveIDPPage,
  WelcomeFollowPage,
  GenerateCodePage,
  AddFamilyPage,
  // ScrollTestPage, // Moved to outbox
  SuperAdminPage,
  // PulsePage, // Moved to features/pulse
  PerformPage,

  // Pages used for parameterized paths
  // VideoPlayer, // Moved to features/pulse
  EventDetailsPage,
  EditFamilyPage,
  
  // Coach section
  CoachDashboard,
  CoachHome,
  PlayerManagement,
  UserManagement,
  EventManagement,
  CommunicationManagement,
  PlayerObjectives,
  TeamObjectives,
  TeamEvaluation,
  PlayerOnboarding,
  ParentApprovalRequests,
  PlayerOnboardingIntegration,

  SessionPlanner,
  MatchSetup,
  AddPlayer,
  
  // Club Management
  ClubsList,
  ClubDashboard,
  ClubSettings,
  ClubPlayers,
  ClubAdministrators,
  ClubVerification,
  // ClubTeams has been moved to archive
  // TeamCreationForm removed (IonicTeamCreationForm is used instead)
  TeamEvents,
  ClubManagementPage,
  EditClubPage,
  // Coach Management
  ClubCoaches,
  TeamCoaches,
  UnifiedCoachManagement,
  TestCoachesComponent,
  // Unified form components
  UnifiedClubCreationForm,
  UnifiedClubCreationFormFixed,
  IonicTeamCreationForm,
  
  // Event Management removed
  
  // Club Analytics
  ClubAnalytics as ClubAnalyticsComponent,
  
  // Ecommerce pages removed - transitioning to BigCommerce
} from '../src/pages';

// Import the debug page for registration
// import RegistrationDebugPage from './pages/RegistrationDebugPage'; // Moved to outbox
// import VercelRouteDebug from './pages/VercelRouteDebug'; // Moved to outbox

// Domain mockup components
import DomainsIndex from '../src/components/domains/DomainsIndex';
import {
  IdentityProfileMockup,
  OrganizationMockup,
  PeopleRolesMockup,
  CommunicationMockup,
  EvaluationMockup
} from '../src/components/domains/DomainMockupPages';
import ManageChildren from '../src/pages/ManageChildren';
import { useCurrentUser } from '../src/hooks/useCurrentUser';

// Shop pages are now in the locker feature
// Removed old shop imports as we're using the new locker structure





// All components are imported from pages index

const AppRoutes: React.FC = () => {
  const location = useLocation();
  
  // Check for V2 routes and handle within the main routing structure
  const isV2Route = location.pathname.startsWith('/v2');
  
  // Check for evaluation routes SECOND, before any auth logic
  if (location.pathname.startsWith('/evaluation/pre/')) {
    const pathParts = location.pathname.split('/');
    const preEvaluationId = pathParts[3]; // Get ID from /evaluation/pre/{id}
    
    if (preEvaluationId) {
      return <PlayerSelfEvaluationForm preEvaluationId={preEvaluationId} />;
    }
  }
  
  // Also check for the test HTML page format
  if (location.pathname === '/pre-evaluation-test.html') {
    const params = new URLSearchParams(location.search);
    const id = params.get('id');
    if (id) {
      return <PlayerSelfEvaluationForm preEvaluationId={id} />;
    }
  }
  
  // NOW continue with authentication logic
  const { user, isLoading: loading, signOut } = useCurrentUser();
  const [showDebug, setShowDebug] = useState(false);
  
  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-900">
        <div className="text-white text-lg">Loading...</div>
      </div>
    );
  }
  
  // If user is not authenticated, only show public routes
  if (!user) {
    return (
      <Switch>
        <Route exact path="/" component={SplashPage} />
        {/* STAGE 1: Updated login page pointing to open registration */}
        <Route exact path="/login" component={UpdatedLoginV2} />
        <Route exact path="/signup" component={SignupPage} />
        {/* STAGE 1: Open registration without invite code */}
        <Route exact path="/register" component={OpenRegistration} />
        {/* Redirect invited registrations to the standard registration page */}
        <Route exact path="/register-invited" render={() => <Redirect to="/register" />} />
        <Route exact path="/reset-password" component={ResetPasswordPage} />
        
        {/* Player Self-Evaluation Routes - Allow anonymous access */}
        <Route exact path="/evaluation/pre/:preEvaluationId" render={({ match, location }) => {
          console.log('📌 DEBUG: URL matched for /evaluation/pre route (anonymous):', location.pathname);
          console.log('📌 DEBUG: preEvaluationId param:', match.params.preEvaluationId);
          return <PlayerSelfEvaluationForm preEvaluationId={match.params.preEvaluationId} />;
        }} />
        
        {/* Test route for debugging - must come after specific routes */}
        <Route path="/evaluation" render={({ location }) => {
          console.log('🔥 DEBUG: /evaluation fallback path accessed:', location.pathname);
          return <div style={{ padding: '20px', color: 'white', backgroundColor: 'black' }}>Evaluation route accessed: {location.pathname}<br/>This is a fallback route for debugging.</div>;
        }} />
        
        {/* Pre-evaluation test page with query parameter - Allow anonymous access */}
        <Route exact path="/pre-evaluation-test.html" render={({ location }) => {
          const params = new URLSearchParams(location.search);
          const id = params.get('id');
          console.log('📌 DEBUG: URL matched for test page route (anonymous):', location.pathname);
          console.log('📌 DEBUG: preEvaluationId from query:', id);
          return id ? <PlayerSelfEvaluationForm preEvaluationId={id} /> : <Redirect to="/" />;
        }} />
        
        {/* Debug route for Vercel routing issues */}
        {/* <Route exact path="/route-debug" component={VercelRouteDebug} /> */} {/* Moved to outbox */}
        
        {/* Design System - accessible without authentication */}
        <Route exact path="/design-system" component={DesignSystem} />
        <Route exact path="/design-system/event-cards" component={() => {
          const EventCardShowcase = React.lazy(() => import('../src/pages/design-system/EventCardShowcase'));
          return <React.Suspense fallback={<div>Loading...</div>}><EventCardShowcase /></React.Suspense>;
        }} />
        <Route exact path="/design/buttons" component={ButtonShowcasePage} />
        
        {/* Redirect all other routes to splash/login for unauthenticated users */}
        <Route path="*">
          <Redirect to="/" />
        </Route>
      </Switch>
    );
  }
  
  // Check if user's email is verified (for Supabase v1.31.1, we need to check this)
  const isEmailVerified = user?.email_confirmed_at != null;
  
  // Debug helper - remove in production
  const isDev = import.meta.env.DEV;
  
  // If user is authenticated but email not verified, show verification screen
  if (!isEmailVerified) {
    return (
      <>
        <Switch>
          <Route exact path="/email-verification" render={() => (
            <EmailVerificationPage user={user} />
          )} />
          <Route exact path="/logout" render={() => {
            const performSignOut = async () => {
              try {
                await signOut();
              } catch (error) {
                console.error('Error signing out:', error);
              }
            };
            performSignOut();
            return <Redirect to="/" />;
          }} />
          {/* Design System accessible without email verification */}
          <Route exact path="/design-system" component={DesignSystem} />
          <Route exact path="/design/buttons" component={ButtonShowcasePage} />
          <Route exact path="/design-system/event-cards" component={() => {
            const EventCardShowcase = React.lazy(() => import('../src/pages/design-system/EventCardShowcase'));
            return <React.Suspense fallback={<div>Loading...</div>}><EventCardShowcase /></React.Suspense>;
          }} />
          {/* Add debug route for development */}
          {isDev && (
            <Route exact path="/auth-debug" render={() => (
              <div>
                <EmailVerificationPage user={user} />
                <AuthDebug onClose={() => {}} />
              </div>
            )} />
          )}
          {/* Redirect all other routes to email verification */}
          <Route path="*">
            <Redirect to="/email-verification" />
          </Route>
        </Switch>
        
        {/* Debug component for development */}
        {isDev && (
          <div className="fixed bottom-4 right-4 z-50">
            <button
              onClick={() => setShowDebug(!showDebug)}
              className="shot-button-small"
              style={{ 
                backgroundColor: 'var(--shot-purple)', 
                color: 'var(--shot-white)',
                padding: '8px 12px',
                borderRadius: 'var(--shot-button-radius)',
                border: 'none',
                fontSize: '12px'
              }}
            >
              Debug Auth
            </button>
          </div>
        )}
        
        {showDebug && <AuthDebug onClose={() => setShowDebug(false)} />}
      </>
    );
  }

  // User is authenticated, show all protected routes
  // console.log('🔵 Authenticated routes section reached, pathname:', location.pathname);
  
  // Create dev routes array outside of JSX
  const devRoutes = isDev ? [
    <Route key="test-bg" exact path="/test-backgrounds" component={TestBackgroundColors} />,
    <Route key="bg-debug" exact path="/background-debug" component={BackgroundDebugReport} />
  ] : [];

  return (
    <Switch>
      {/* ========== TEST ROUTES (DEV ONLY) ========== */}
      {devRoutes}
      
      {/* ========== DOMAIN MOCKUPS ========== */}
      {/* Domain Mockups Index */}
      <Route exact path="/domains" component={DomainsIndex} />
      
      {/* Individual Domain Mockup Routes */}
      <Route exact path="/domains/identity-profile" component={IdentityProfileMockup} />
      <Route exact path="/domains/organization" component={OrganizationMockup} />
      <Route exact path="/domains/people-roles" component={PeopleRolesMockup} />
      <Route exact path="/domains/communication" component={CommunicationMockup} />
      <Route exact path="/domains/evaluation" component={EvaluationMockup} />
      
      {/* ========== BLOCK 1: MAIN ROUTES ========== */}
      {/* Root Main Routes */}
      <Route exact path="/home" component={HomePage} />
      <Route exact path="/account" render={() => {
        // console.log('🎯 Account route matched');
        try {
          return <AccountPage />;
        } catch (error) {
          console.error('🚨 AccountPage render error:', error);
          return <div style={{ color: 'red', padding: '20px' }}>AccountPage Error: {String(error)}</div>;
        }
      }} />
      <Route exact path="/superadmin" component={SuperAdminPage} />
      <Route exact path="/activities" component={ActivitiesPage} />
      <Route exact path="/profile" render={() => <Redirect to="/account" />} />
      <Route exact path="/family" component={FamilyPage} />
      <Route exact path="/videos" render={() => {
        const VideoLibrary = React.lazy(() => import('../src/features/pulse/pages/videos/VideoLibrary'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><VideoLibrary /></React.Suspense>;
      }} />
      <Route exact path="/membership" component={MembershipPage} />
      <Route exact path="/checkout" component={CheckoutPage} />
      <Route exact path="/view-idp" component={ViewIDPPage} />
      <Route exact path="/improveIDP" component={ImproveIDPPage} />
      {/* <Route exact path="/scrollfix" component={ScrollFixPage} /> */} {/* Moved to outbox */}
      {/* <Route exact path="/scroll-test" component={ScrollTestPage} /> */} {/* Moved to outbox */}
      <Route exact path="/pulse" render={() => {
        const PulseFeed = React.lazy(() => import('../src/features/pulse/pages/feed/PulseFeed'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><PulseFeed /></React.Suspense>;
      }} />
      {/* Use the v2 perform page with proper navigation */}
      <Route exact path="/perform" render={() => {
        const PerformV2 = React.lazy(() => import('../src/pages/v2/perform/Perform'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><PerformV2 /></React.Suspense>;
      }} />
      
      {/* STAGE 1: Perform Sub-Routes */}
      <Route exact path="/perform/personal" component={() => {
        const JustForMe = React.lazy(() => import('../src/pages/section/Perform/JustForMe'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><JustForMe /></React.Suspense>;
      }} />
      <Route exact path="/perform/join-team" component={() => {
        const JoinTeam = React.lazy(() => import('../src/pages/section/Perform/JoinTeam'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><JoinTeam /></React.Suspense>;
      }} />
      <Route exact path="/perform/coach-request" component={() => {
        const CoachRequest = React.lazy(() => import('../src/pages/section/Perform/CoachRequest'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><CoachRequest /></React.Suspense>;
      }} />
      
      {/* Event Main Routes */}
      <Route exact path="/events" component={EventsPage} />
      
      {/* Coach Main Routes */}
      <Route exact path="/coach/dashboard" component={CoachDashboard} />
      <Route exact path="/coach/home" component={CoachHome} />
      <Route exact path="/coach/player-management" component={PlayerManagement} />
      <Route exact path="/player-management" component={PlayerOnboardingIntegration} />
      <Route exact path="/player/join-team" component={PlayerOnboarding} />
      <Route exact path="/parent/approval-requests" component={ParentApprovalRequests} />
      <Route exact path="/manage-children" component={ManageChildren} />
      
      {/* NEW: Add route for user management */}
      <Route exact path="/coach/user-management" component={UserManagement} />
      
      {/* NEW: Add route for event management */}
      <Route exact path="/coach/event-management" component={EventManagement} />
      
      {/* NEW: Add route for communication management */}
      <Route exact path="/coach/communication-management" component={CommunicationManagement} />
      
      <Route exact path="/coach/session-planner" component={SessionPlanner} />
      <Route exact path="/coach/match-setup" component={MatchSetup} />
      {/* Removed events routes */}
      <Route exact path="/coach/clubs" render={() => <Redirect to="/coach/clubs/management" />} />
      <Route exact path="/coach/clubs/management" component={ClubManagementPage} />
      
      {/* Perform Framework Routes */}
      <Route exact path="/coach/perform-framework-management" component={PerformFrameworkPage} />
      <Route exact path="/coach/perform-framework-management/:frameworkVersion/criteria" render={() => {
        const CriteriaManager = React.lazy(() => import('../src/pages/section/Coach/supporting/PerformFramework/components/CriteriaManager').then(module => ({ default: module.CriteriaManager })));
        return <React.Suspense fallback={<div>Loading...</div>}><CriteriaManager /></React.Suspense>;
      }} />
      
      {/* Player Self-Evaluation Routes - Available for both authenticated and anonymous users */}
      <Route exact path="/evaluation/pre/:preEvaluationId" render={({ match, location }) => {
        console.log('📌 DEBUG: URL matched for /evaluation/pre route (authenticated):', location.pathname);
        console.log('📌 DEBUG: preEvaluationId param:', match.params.preEvaluationId);
        return <PlayerSelfEvaluationForm preEvaluationId={match.params.preEvaluationId} />;
      }} />
      
      {/* Pre-evaluation test page with query parameter */}
      <Route exact path="/pre-evaluation-test.html" render={({ location }) => {
        const params = new URLSearchParams(location.search);
        const id = params.get('id');
        console.log('📌 DEBUG: URL matched for test page route (authenticated):', location.pathname);
        console.log('📌 DEBUG: preEvaluationId from query:', id);
        return id ? <PlayerSelfEvaluationForm preEvaluationId={id} /> : <Redirect to="/" />;
      }} />
      
      {/* Currency Test Page */}
      <Route exact path="/test/currency" render={() => {
        const CurrencyTest = React.lazy(() => import('../src/pages/CurrencyTest'));
        return (
          <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}>
            <CurrencyTest />
          </React.Suspense>
        );
      }} />
      
      {/* Future Stage 2: Instant Session for "Just for Me" */}
      <Route exact path="/perform/instant-session/:sessionId" render={({ match }) => (
        <div className="min-h-screen bg-black text-white flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Instant Session (Stage 2)</h1>
            <p className="text-gray-400">Session ID: {match.params.sessionId}</p>
            <p className="text-sm text-gray-500 mt-4">This feature will be implemented in Stage 2</p>
          </div>
        </div>
      )} />
      
      {/* Player Evaluation History Routes */}
      <Route exact path="/player/:playerId/evaluation-history" render={({ match }) => {
        const PlayerEvaluationHistory = React.lazy(() => import('../src/pages/section/Player/PlayerEvaluationHistory'));
        return (
          <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}>
            <PlayerEvaluationHistory playerId={match.params.playerId} />
          </React.Suspense>
        );
      }} />
      <Route exact path="/player/:playerId/progress" render={({ match }) => {
        const PlayerProgressView = React.lazy(() => import('../src/pages/section/Player/PlayerProgressView'));
        return (
          <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}>
            <PlayerProgressView playerId={match.params.playerId} />
          </React.Suspense>
        );
      }} />
      
      {/* Player Training History Route */}
      <Route exact path="/player/:playerId/training-history" render={({ match }) => {
        const TrainingHistoryPage = React.lazy(() => import('../src/pages/section/Player/TrainingHistoryPage'));
        return (
          <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}>
            <TrainingHistoryPage />
          </React.Suspense>
        );
      }} />
      
      {/* BigCommerce test routes removed - using new locker structure */}
      
      {/* ========== BLOCK 2: FIXED PATHS ========== */}
      {/* Root Fixed Paths */}
      <Route exact path="/welcome-follow" component={WelcomeFollowPage} />
      <Route exact path="/generate-code" component={GenerateCodePage} />
      <Route exact path="/add-family" component={AddFamilyPage} />
      
      {/* Demo Pages - Moved to outbox */}
      {/* <Route exact path="/demo/loading" component={LoadingDemo} /> */}
      {/* <Route exact path="/demo/account" component={AccountTest} /> */}
      {/* <Route exact path="/test-navigation" component={TestNavigation} /> */}
      {/* <Route exact path="/test-navigation-clean" component={TestNavigationClean} /> */}
      {/* <Route exact path="/test-transparency" component={TransparencyDebug} /> */}
      {/* <Route exact path="/test-transparency-bright" component={TestTransparencyBright} /> */}
      {/* <Route exact path="/test-transparency-direct" component={TestTransparencyDirect} /> */}
      {/* <Route exact path="/test-demo-style" component={TestDemoStyle} /> */}
      {/* <Route exact path="/test-demo-style-simple" component={TestDemoStyleSimple} /> */}
      {/* <Route exact path="/test-demo-standalone" component={TestDemoStandalone} /> */}
      {/* <Route exact path="/test-demo-debug" component={TestDemoDebug} /> */}
      {/* <Route exact path="/test-demo-no-components" component={TestDemoNoComponents} /> */}
      {/* <Route exact path="/test-demo-inline" component={TestDemoInline} /> */}
      
      {/* Demo Router - for all demo pages */}
      {/* <Route path="/demo" component={DemoRouter} /> */} {/* Moved to outbox */}
      
      {/* Coach Fixed Paths */}
      <Route exact path="/coach/add-club" render={() => <Redirect to="/coach/club/create" />} />
      <Route exact path="/coach/add-player" component={AddPlayer} />
      
      {/* Add the club deletion debugger route */}
      <Route exact path="/coach/clubs/debug/:clubId" render={() => (
        <CoachProtectedRoute>
          <ClubDeletionDebugger />
        </CoachProtectedRoute>
      )} />
      
      {/* Unified club creation path */}
      <Route exact path="/coach/club/create" render={() => {
        console.log('🚀 Route matched: /coach/club/create');
        return <UnifiedClubCreationFormFixed />;
      }} />
      
      {/* Coach Events Fixed Paths */}
      {/* Removed events routes */}
      
      {/* Design System Routes */}
      <Route exact path="/design-system" component={DesignSystem} />
      <Route exact path="/design/buttons" component={ButtonShowcasePage} />
      <Route exact path="/sms-test" component={SimpleSmsStatusPage} />
      
      {/* Debug Routes */}
      {/* <Route exact path="/debug/registration" component={RegistrationDebugPage} /> */} {/* Moved to outbox */}
      
      {/* STAGE 1: Test page to verify implementation */}
      {/* <Route exact path="/stage1-test" component={Stage1TestPage} /> */} {/* Moved to outbox */}
      {/* <Route exact path="/debug/profile" component={DebugProfile} /> */} {/* Moved to outbox */}
      {/* <Route exact path="/debug/header" component={StandardHeaderTest} /> */} {/* Moved to outbox */}
      {/* <Route exact path="/debug/dropdown" component={SimpleDropdownTest} /> */} {/* Moved to outbox */}
      {/* <Route exact path="/route-debug" component={VercelRouteDebug} /> */} {/* Moved to outbox */}
      {/* <Route exact path="/DebugModeTest" component={DebugModeTest} /> */} {/* Moved to outbox */}
      {/* <Route exact path="/test/debug" component={DebugTest} /> */} {/* Moved to outbox */}
      <Route exact path="/perform/debug" component={() => {
        const PerformDebugModeDemo = React.lazy(() => import('../src/pages/section/Coach/dashboards/PerformDebugModeDemo'));
        return <React.Suspense fallback={<div>Loading...</div>}><PerformDebugModeDemo /></React.Suspense>;
      }} />
      <Route exact path="/evaluation/demo" component={() => {
        const EvaluationSystemDemo = React.lazy(() => import('../src/components/v2/EvaluationSystemDemo'));
        return <React.Suspense fallback={<div>Loading...</div>}><EvaluationSystemDemo /></React.Suspense>;
      }} />
      
      
      {/* ========== BLOCK 3: PARAMETERIZED PATHS ========== */}
      {/* Root Parameterized Paths */}
      <Route exact path="/edit-family/:id" component={EditFamilyPage} />
      <Route exact path="/videoplayer/:id" render={({ match }) => {
        const VideoPlayer = React.lazy(() => import('../src/features/pulse/pages/videos/VideoPlayer'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><VideoPlayer /></React.Suspense>;
      }} />
      
      {/* Event Parameterized Paths */}
      <Route exact path="/events/:id" component={EventDetailsPage} />
      
      {/* Coach Event Parameterized Paths - All specific routes first */}
      {/* Removed events routes */}
      
      {/* Coach Parameterized Paths */}
      {/* Legacy player objectives path - redirect to club structure */}
      {/* Test route updated to use TeamFlat instead of legacy Team component */}
      <Route exact path="/coach/test/team/:id" render={({ match }) => (
        <CoachProtectedRoute>
          <TeamFlat />
        </CoachProtectedRoute>
      )} />
      
      {/* Coach Tab Navigation */}
      <Route exact path="/coach/club/:clubId/coaches" component={UnifiedCoachManagement} />
      <Route exact path="/coach/club/:clubId/settings" component={ClubSettings} />
      <Route exact path="/coach/club/:clubId/players" component={ClubPlayers} />
      {/* Club Administrators Page */}
      <Route exact path="/coach/club/:clubId/administrators" render={({ match }) => (
        <AdminProtectedRoute>
          <ClubAdminsComponent />
        </AdminProtectedRoute>
      )} />
      {/* Redirect old 'new-administrators' path to 'administrators' */}
      <Route exact path="/coach/club/:clubId/new-administrators" render={({ match }) => (
        <Redirect to={`/coach/club/${match.params.clubId}/administrators`} />
      )} />
      <Route exact path="/coach/club/:clubId/debug-administrators" render={({ match }) => (
        <AdminProtectedRoute>
          <NewClubAdministrators />
        </AdminProtectedRoute>
      )} />
      <Route exact path="/coach/club/:clubId/verification" component={ClubVerification} />
      <Route exact path="/coach/club/:clubId/teams" render={({ match }) => <Redirect to={`/coach/club/${match.params.clubId}`} />} />
      
      {/* Club-level Events Routes */}
      <Route exact path="/coach/club/:clubId/events" render={({ match }) => (
        <CoachProtectedRoute>
          <EventsList clubId={match.params.clubId} />
        </CoachProtectedRoute>
      )} />
      <Route exact path="/coach/club/:clubId/event/:eventId" render={({ match }) => (
        <CoachProtectedRoute>
          <EventPage />
        </CoachProtectedRoute>
      )} />
      
      {/* SMS Status page for club-level event pre-assessments */}
      <Route exact path="/coach/club/:clubId/event/:eventId/sms-status" render={({ match }) => (
        <CoachProtectedRoute>
          <SmsStatusPage />
        </CoachProtectedRoute>
      )} />
      
      {/* Redirect old routes to the combined event page */}
      <Route exact path="/coach/club/:clubId/event/:eventId/attendance" render={({ match }) => (
        <Redirect to={`/coach/club/${match.params.clubId}/event/${match.params.eventId}`} />
      )} />
      <Route exact path="/coach/club/:clubId/event/:eventId/invite" render={({ match }) => (
        <Redirect to={`/coach/club/${match.params.clubId}/event/${match.params.eventId}`} />
      )} />
      <Route exact path="/coach/club/:clubId/event/:eventId/participants" render={({ match }) => (
        <CoachProtectedRoute>
          <EventParticipantsList />
        </CoachProtectedRoute>
      )} />
      
      <Route exact path="/coach/club/:clubId/analytics" component={ClubAnalyticsComponent} />
      <Route exact path="/coach/club/:clubId/edit" component={EditClubPage} />
      
      {/* Team Management - All specific routes first */}
      <Route exact path="/coach/club/:clubId/team/create" component={CreateTeamShadow} />
      <Route exact path="/coach/club/:clubId/team/:teamId/coaches" component={UnifiedCoachManagement} />
      <Route exact path="/coach/club/:clubId/team/:teamId/db" render={({ match }) => <Redirect to={`/coach/club/${match.params.clubId}/team/${match.params.teamId}`} />} />
      
      {/* Team Management - Shadow DOM components */}
      <Route exact path="/coach/club/:clubId/team/:teamId/edit" component={EditTeamShadow} />
      <Route exact path="/coach/club/:clubId/team/:teamId/coaches-shadow" render={({ match }) => {
        const TeamCoachesShadow = React.lazy(() => import('../src/pages/section/Coach/supporting/ClubManagement/CoachManagement/TeamCoachesShadow'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}>
          <TeamCoachesShadow />
        </React.Suspense>;
      }} />
      
      {/* Test route for Shadow DOM form implementation */}
      <Route exact path="/coach/club/:clubId/team/:teamId/edit-shadow" component={EditTeamShadowDemo} />
      
      {/* Team-specific event creation - NEW CLEAN VERSION */}
      <Route exact path="/coach/club/:clubId/team/:teamId/create-event" component={CreateEvent} />
      
      {/* Team-specific event creation - LEGACY VERSION (keep for now) */}
      <Route exact path="/coach/club/:clubId/team/:teamId/add-event" component={QuickAddEventPage} />
      
      {/* Redirect /squad route to main team page */}
      <Route exact path="/coach/club/:clubId/team/:teamId/squad" render={({ match }) => 
        <Redirect to={`/coach/club/${match.params.clubId}/team/${match.params.teamId}`} />
      } />
      
      {/* Coach Events Team-specific Routes */}
      <Route exact path="/coach/club/:clubId/team/:teamId/events" render={({ match }) => (
        <CoachProtectedRoute>
          <EventsList teamId={match.params.teamId} clubId={match.params.clubId} />
        </CoachProtectedRoute>
      )} />
      <Route exact path="/coach/club/:clubId/team/:teamId/evaluations" render={({ match }) => (
        <CoachProtectedRoute>
          <EvaluationsPage />
        </CoachProtectedRoute>
      )} />
      <Route exact path="/coach/club/:clubId/team/:teamId/event/:eventId" render={({ match }) => (
        <CoachProtectedRoute>
          <EventPage />
        </CoachProtectedRoute>
      )} />
      
      {/* Add Self-Evaluation Summary route */}
      <Route exact path="/coach/club/:clubId/team/:teamId/event/:eventId/pre-evaluations" render={({ match }) => (
        <CoachProtectedRoute>
          <SelfEvaluationSummary />
        </CoachProtectedRoute>
      )} />
      
      {/* Add Event Evaluation route (batch evaluation) - RE-ENABLED */}
      {/* Event Evaluation routes */}
      <Route exact path="/coach/club/:clubId/team/:teamId/event/:eventId/evaluate" render={({ match, location }) => (
        <CoachProtectedRoute>
          <EventEvaluation />
        </CoachProtectedRoute>
      )} />

      {/* Event Notifications route */}
      <Route exact path="/coach/club/:clubId/team/:teamId/event/:eventId/notifications" render={({ match }) => (
        <CoachProtectedRoute>
          <EventPreEvaluationNotifications />
        </CoachProtectedRoute>
      )} />
      
      {/* SMS Status page - Add before other routes that might match */}
      <Route exact path="/coach/club/:clubId/team/:teamId/event/:eventId/sms-status" component={SmsStatusPage} />
      
      {/* Evaluation Dots Test Routes - TEMPORARILY DISABLED */}
      {/* <Route exact path="/coach/club/:clubId/team/:teamId/event/:eventId/evaluate/test" render={({ match, location }) => (
        <CoachProtectedRoute>
          <EvaluationDotsTest />
        </CoachProtectedRoute>
      )} /> */}
      
      {/* <Route exact path="/coach/club/:clubId/team/:teamId/event/:eventId/evaluate/test-layout" render={({ match, location }) => (
        <CoachProtectedRoute>
          <EvaluationDotsTestWithLayout />
        </CoachProtectedRoute>
      )} /> */}
      
      {/* <Route exact path="/coach/club/:clubId/team/:teamId/event/:eventId/evaluate/test-card" render={({ match, location }) => (
        <CoachProtectedRoute>
          <EvaluationDotsTestWithCard />
        </CoachProtectedRoute>
      )} /> */}
      
      {/* <Route exact path="/coach/club/:clubId/team/:teamId/event/:eventId/evaluate/test-players" render={({ match, location }) => (
        <CoachProtectedRoute>
          <EvaluationDotsTestWithPlayerCards />
        </CoachProtectedRoute>
      )} /> */}
      
      {/* <Route exact path="/coach/club/:clubId/team/:teamId/event/:eventId/evaluate/test-simple" render={({ match, location }) => (
        <CoachProtectedRoute>
          <EvaluationDotsTestSimplified />
        </CoachProtectedRoute>
      )} /> */}

      
      {/* Add Player Evaluation route (individual evaluation) - TEMPORARILY DISABLED */}
      {/* <Route exact path="/coach/club/:clubId/team/:teamId/event/:eventId/evaluate/:playerId" render={({ match, location }) => (
        <CoachProtectedRoute>
          <PlayerEvaluation />
        </CoachProtectedRoute>
      )} /> */}
      
      {/* Redirect old routes to the combined event page */}
      <Route exact path="/coach/club/:clubId/team/:teamId/event/:eventId/attendance" render={({ match }) => (
        <Redirect to={`/coach/club/${match.params.clubId}/team/${match.params.teamId}/event/${match.params.eventId}`} />
      )} />
      <Route exact path="/coach/club/:clubId/team/:teamId/event/:eventId/invite" render={({ match }) => (
        <Redirect to={`/coach/club/${match.params.clubId}/team/${match.params.teamId}/event/${match.params.eventId}`} />
      )} />
      <Route exact path="/coach/club/:clubId/team/:teamId/event/:eventId/participants" render={({ match }) => (
        <CoachProtectedRoute>
          <EventParticipantsList />
        </CoachProtectedRoute>
      )} />
      
      <Route exact path="/coach/club/:clubId/team/:teamId/add-player" component={TeamAddPlayer} />
      <Route exact path="/coach/club/:clubId/team/:teamId/manage-players" render={({ match }) => (
        <CoachProtectedRoute>
          <TeamPlayerManager />
        </CoachProtectedRoute>
      )} />
      <Route exact path="/coach/club/:clubId/team/:teamId/objectives" component={TeamObjectives} />
      <Route exact path="/coach/club/:clubId/team/:teamId/evaluation" component={TeamEvaluation} />
      <Route exact path="/coach/club/:clubId/team/:teamId/player/:playerId/objectives" component={PlayerObjectives} />
      
      {/* Generic club/team/event routes - MUST come after all specific routes */}
      <Route exact path="/coach/club/:clubId/player/:playerId" component={PlayerDetailPage} />
      
      {/* Team Communications route */}
      <Route exact path="/coach/club/:clubId/team/:teamId/comms" component={TeamComms} />
      
      {/* Session Calendar and Team Stats routes */}
      <Route exact path="/coach/club/:clubId/team/:teamId/calendar" render={() => (
        <CoachProtectedRoute>
          <SessionCalendar />
        </CoachProtectedRoute>
      )} />
      
      <Route exact path="/coach/club/:clubId/team/:teamId/stats" render={() => (
        <CoachProtectedRoute>
          <TeamStats />
        </CoachProtectedRoute>
      )} />

      {/* Use the new TeamFlat component instead of the tabbed Team component - accessible to all */}
      <Route exact path="/coach/club/:clubId/team/:teamId" component={TeamFlat} />

      {/* Test Basic Routes for PRE-ASSESSMENT styling - matching user's URL pattern */}
      {/* <Route exact path="/coach/club/:clubId/team-basic/:teamId" component={TestBasicPage} /> */} {/* Moved to outbox */}
      {/* <Route exact path="/coach/club/:clubId/team-super/:teamId" component={SuperBasicPage} /> */} {/* Moved to outbox */}
      {/* <Route exact path="/coach/club/:clubId/team-event/:teamId" component={EventCardTestPage} /> */} {/* Moved to outbox */}
      {/* <Route exact path="/coach/club/:clubId/team-color/:teamId" component={ShotColorTestPage} /> */} {/* Moved to outbox */}
      
      {/* Exact format test page matching user's example URL pattern */}
      {/* <Route exact path="/coach/club/:clubId/team-basic/:teamId-basic" component={ExactFormatTestPage} /> */} {/* Moved to outbox */}
      
      {/* Base club dashboard - MUST come last of all club routes */}
      <Route exact path="/coach/club/:clubId" component={ClubDashboard} />
      
      {/* Shop Routes - Redirect to new Locker structure */}
      <Route exact path="/shop" render={() => <Redirect to="/locker" />} />
      <Route exact path="/shop/cart" render={() => <Redirect to="/locker/cart" />} />
      <Route exact path="/shop/product/:productId" render={({ match }) => <Redirect to={`/locker/product/${match.params.productId}`} />} />
      <Route exact path="/shop/checkout" render={() => <Redirect to="/locker/checkout" />} />
      <Route exact path="/shop/order-confirmation" render={() => <Redirect to="/locker/order-confirmation" />} />
      
      {/* Locker Routes - New Feature-Based Structure */}
      <Route exact path="/locker" render={() => {
        const LockerHome = React.lazy(() => import('../src/features/locker/pages/shop/LockerHome'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><LockerHome /></React.Suspense>;
      }} />
      <Route exact path="/locker/shop/:category?" render={() => {
        const ProductListing = React.lazy(() => import('../src/features/locker/pages/shop/ProductListing'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><ProductListing /></React.Suspense>;
      }} />
      <Route exact path="/locker/product/:productId" render={() => {
        const ProductDetail = React.lazy(() => import('../src/features/locker/pages/products/ProductDetail'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><ProductDetail /></React.Suspense>;
      }} />
      <Route exact path="/locker/cart" render={() => {
        const Cart = React.lazy(() => import('../src/features/locker/pages/cart/Cart'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><Cart /></React.Suspense>;
      }} />
      <Route exact path="/locker/checkout" render={() => {
        const Checkout = React.lazy(() => import('../src/features/locker/pages/checkout/Checkout'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><Checkout /></React.Suspense>;
      }} />
      <Route exact path="/locker/order-confirmation" render={() => {
        const OrderConfirmation = React.lazy(() => import('../src/features/locker/pages/checkout/OrderConfirmation'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><OrderConfirmation /></React.Suspense>;
      }} />
      <Route exact path="/locker/orders" render={() => {
        const ComingSoonWithNav = React.lazy(() => import('../src/components/ComingSoonWithNav'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><ComingSoonWithNav title="My Orders" description="Track your order history and manage returns." /></React.Suspense>;
      }} />
      <Route exact path="/locker/wishlist" render={() => {
        const ComingSoonWithNav = React.lazy(() => import('../src/components/ComingSoonWithNav'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><ComingSoonWithNav title="Wishlist" description="Save your favorite items for later." /></React.Suspense>;
      }} />
      <Route exact path="/locker/size-guide" render={() => {
        const ComingSoonWithNav = React.lazy(() => import('../src/components/ComingSoonWithNav'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><ComingSoonWithNav title="Size Guide" description="Find your perfect fit with our comprehensive size guide." showCart={false} /></React.Suspense>;
      }} />
      <Route exact path="/locker/drops" render={() => {
        const DropsPage = React.lazy(() => import('../src/features/locker/pages/drops/DropsPage'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><DropsPage /></React.Suspense>;
      }} />
      <Route exact path="/locker/admin/drops" render={() => {
        const DropsAdmin = React.lazy(() => import('../src/features/locker/admin/DropsAdmin'));
        return <React.Suspense fallback={<div className="flex items-center justify-center h-screen bg-gray-900"><div className="text-white text-lg">Loading...</div></div>}><DropsAdmin /></React.Suspense>;
      }} />
      
      {/* Temporary database check */}
      <Route exact path="/check-db" component={() => {
        const CheckDatabaseTables = React.lazy(() => import('../src/pages/CheckDatabaseTables'));
        return <React.Suspense fallback={<div>Loading...</div>}><CheckDatabaseTables /></React.Suspense>;
      }} />
      
      {/* Legacy redirects */}
      <Route exact path="/perform/choose-role" render={() => <Redirect to="/coach/dashboard" />} />
      <Route exact path="/coach/edit-club/:id" render={({ match }) => <Redirect to={`/coach/club/${match.params.id}/edit`} />} />
      <Route exact path="/player/:id" render={({ match }) => <Redirect to={`/coach/player-management`} />} />
      
      {/* Redirect old DesignSystem route to new kebab-case route */}
      <Route exact path="/DesignSystem" render={() => <Redirect to="/design-system" />} />
      
      {/* V2 Routes - Add them here within the main Switch */}
      <Route path="/v2" component={V2Routes} />
      
      {/* Test route for global footer */}
      {/* <Route exact path="/test-global-footer" render={() => {
        const TestGlobalFooter = React.lazy(() => import('./pages/TestGlobalFooter').then(module => ({ default: module.TestGlobalFooter })));
        return <React.Suspense fallback={<div>Loading...</div>}><TestGlobalFooter /></React.Suspense>;
      }} /> */} {/* Moved to outbox */}
      
      {/* Test route for evaluation comparison */}
      {/* <Route exact path="/test-evaluation-comparison" render={() => {
        const TestEvaluationComparison = React.lazy(() => import('./pages/TestEvaluationComparison'));
        return <React.Suspense fallback={<div>Loading...</div>}><TestEvaluationComparison /></React.Suspense>;
      }} /> */} {/* Moved to outbox */}
      
      {/* Test route for pre-evaluation debugging */}
      {/* <Route exact path="/test/pre-evaluation" render={() => {
        const TestPreEvaluation = React.lazy(() => import('./pages/test/TestPreEvaluation'));
        return <React.Suspense fallback={<div>Loading...</div>}><TestPreEvaluation /></React.Suspense>;
      }} /> */} {/* Moved to outbox */}
      
      {/* Root redirects */}
      <Route exact path="/" render={() => <Redirect to="/home" />} />
      
      {/* Catch-all route to home */}
      <Route path="*" render={({ location }) => {
        console.log('💱 DEBUG: Catch-all route triggered for path:', location.pathname);
        if (location.pathname.startsWith('/evaluation')) {
          console.log('💱 DEBUG: Evaluation URL detected in catch-all! This suggests the route definition may be wrong');
          // Look for potential evaluation ID in the URL
          const parts = location.pathname.split('/');
          if (parts.length >= 3) {
            console.log('💱 DEBUG: Potential evaluation ID from URL:', parts[parts.length - 1]);
          }
        }
        return <Redirect to="/home" />;
      }} />
    </Switch>
  );
};

export default AppRoutes;