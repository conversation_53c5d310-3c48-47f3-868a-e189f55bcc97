import React, { useState, useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { Zap } from 'lucide-react';
import { configs } from '../configs';
import { useCurrentUser } from '@/hooks/useCurrentUser';
import { useCurrentRole } from '../hooks/useCurrentRole';
import { useSportHeadPoints, calculateTotalSP } from '../hooks/useSportHeadPoints';
import ShadowNavigationDrawerEnhanced from './shadow/ShadowNavigationDrawerEnhanced';
import ShadowAvatar from './shadow/ShadowAvatar';
import { ShadowMiniCart } from './shadow/cart/ShadowMiniCart';

interface DemoStyleHeaderProps {
  cartItemCount?: number; // @deprecated - Cart count is now automatically fetched from context
  showCartIcon?: boolean;
  style?: React.CSSProperties; // Allow style override
}

/**
 * Header component matching the exact style from demoapp.shotclubhouse.com
 * Uses sticky positioning with transparent background and backdrop blur
 * 
 * Note: Cart count is automatically fetched from EnhancedShoppingCartContext,
 * no need to pass cartItemCount prop
 */
export const DemoStyleHeader: React.FC<DemoStyleHeaderProps> = ({ 
  cartItemCount,
  showCartIcon = true,
  style
}) => {
  const history = useHistory();
  const { profile, user } = useCurrentUser();
  const { currentRoleInfo, currentRole, availableRoles } = useCurrentRole();
  const { experiencePoints, achievementPoints, loading: spLoading } = useSportHeadPoints();
  const spPoints = calculateTotalSP(experiencePoints, achievementPoints);
  const [isNavigationOpen, setIsNavigationOpen] = useState(false);
  const [currentSport, setCurrentSport] = useState('Football');
  
  
  // Debug log on mount and role changes
  useEffect(() => {
    // console.log('🎩 [DemoStyleHeader] Header state:', {
    //   currentRole,
    //   currentRoleInfo,
    //   availableRoles,
    //   profileEmail: profile?.email,
    //   userId: user?.id
    // });
  }, [currentRole, currentRoleInfo, availableRoles, profile, user]);

  const handleLogoClick = () => {
    history.replace('/home');
  };

  const handleProfileClick = () => {
    // console.log('👤 [DemoStyleHeader] Profile clicked, opening navigation drawer:', {
    //   currentRole: currentRoleInfo?.role,
    //   roleDisplayName: currentRoleInfo?.displayName,
    //   user: user?.email,
    //   profile: profile?.email
    // });
    setIsNavigationOpen(true);
  };

  const handleCartClick = () => {
    history.push('/locker/cart');
  };

  // Merge default styles with any passed-in styles - Frost glass effect
  const headerStyle: React.CSSProperties = {
    background: 'linear-gradient(to bottom, rgba(26, 26, 26, 0.85), rgba(18, 18, 18, 0.65))',
    backdropFilter: 'blur(16px) saturate(180%)',
    WebkitBackdropFilter: 'blur(16px) saturate(180%)',
    ...style
  };

  return (
    <header 
      className="sticky top-0 z-40 backdrop-blur-sm"
      style={{ ...headerStyle, overflow: 'visible' }}
    >
      <div className="flex items-center justify-between h-[72px] px-4 border-b" style={{ borderColor: 'rgba(255, 255, 255, 0.1)', overflow: 'visible' }}>
        {/* Left section: Logo */}
        <img 
          src={configs.logo} 
          alt="SHOT Logo" 
          className="h-10 cursor-pointer"
          onClick={handleLogoClick}
        />
      
        {/* Right section: Cart, SP, Profile */}
        <div className="flex items-center space-x-2" style={{ overflow: 'visible' }}>
          {/* Shopping Cart */}
          {showCartIcon && (
            <ShadowMiniCart onClick={handleCartClick} />
          )}
          
          {/* SP Points Display */}
          <div className="flex items-center space-x-2 px-4 py-2 bg-shot-gold/10 border border-shot-gold/30 rounded-lg">
            <Zap className="w-5 h-5 text-shot-gold" />
            <span id="sp-total" className="font-bold text-shot-gold text-lg">
              {spLoading ? '...' : spPoints.toLocaleString()}
            </span>
            <span className="text-gray-400 text-sm">SP</span>
          </div>
          
          {/* Profile */}
          <ShadowAvatar 
            key={`avatar-${currentRoleInfo?.role || 'default'}`}
            src={profile?.avatar_url}
            name={profile?.display_name || profile?.email?.split('@')[0] || 'User'}
            size="medium"
            onClick={handleProfileClick}
            showBorder={true}
            borderColor={currentRoleInfo?.bgColor || '#6B00DB'}
          />
        </div>
      </div>
      
      {/* Enhanced Navigation Drawer */}
      <ShadowNavigationDrawerEnhanced
        isOpen={isNavigationOpen}
        onClose={() => setIsNavigationOpen(false)}
        user={{
          name: profile?.full_name || user?.email || 'User',
          sport: currentSport,
          avatar: profile?.avatar_url || undefined
        }}
        // Sport configuration
        availableSports={['Football', 'Boxing']}
        currentSport={currentSport}
        onSportChange={(sport) => setCurrentSport(sport)}
        // Navigation callbacks
        onTimelineClick={() => {
          history.push('/timeline');
          setIsNavigationOpen(false);
        }}
        onRewardsClick={() => {
          history.push('/rewards');
          setIsNavigationOpen(false);
        }}
        onSettingsClick={() => {
          history.push('/account');
          setIsNavigationOpen(false);
        }}
        // Show all sections
        showMyShot={true}
        showAchievements={true}
        showRoleSelector={true}
        showSportSelector={true}
        showSettings={true}
      />
      
    </header>
  );
};