// ABOUTME: TestBackgroundColors - Component to test if Tailwind background colors are working
// This component displays various background color implementations to debug the issue

import React from 'react';

const TestBackgroundColors: React.FC = () => {
  return (
    <div className="p-8 space-y-4">
      <h2 className="text-2xl font-bold text-white mb-6">Background Color Test</h2>
      
      {/* Tailwind Classes */}
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-white">Tailwind Classes:</h3>
        <div className="bg-gray-900 p-4 rounded">bg-gray-900 (should be dark gray)</div>
        <div className="bg-gray-800 p-4 rounded">bg-gray-800 (should be slightly lighter)</div>
        <div className="bg-gray-700 p-4 rounded">bg-gray-700 (should be medium gray)</div>
      </div>

      {/* Inline Styles */}
      <div className="space-y-2 mt-6">
        <h3 className="text-lg font-semibold text-white">Inline Styles:</h3>
        <div style={{ backgroundColor: '#111827' }} className="p-4 rounded">
          backgroundColor: '#111827' (gray-900 equivalent)
        </div>
        <div style={{ backgroundColor: '#1F2937' }} className="p-4 rounded">
          backgroundColor: '#1F2937' (gray-800 equivalent)
        </div>
        <div style={{ backgroundColor: '#374151' }} className="p-4 rounded">
          backgroundColor: '#374151' (gray-700 equivalent)
        </div>
      </div>

      {/* Custom SHOT Colors from Tailwind Config */}
      <div className="space-y-2 mt-6">
        <h3 className="text-lg font-semibold text-white">Custom SHOT Colors (from config):</h3>
        <div className="bg-shot-bg p-4 rounded">bg-shot-bg (should be rgb(18 18 18))</div>
        <div className="bg-shot-surface p-4 rounded">bg-shot-surface (should be rgb(30 30 30))</div>
        <div className="bg-surface-primary p-4 rounded">bg-surface-primary (should be #1a1a1a)</div>
        <div className="bg-surface-secondary p-4 rounded">bg-surface-secondary (should be #1a1a1a)</div>
        <div className="bg-surface-tertiary p-4 rounded">bg-surface-tertiary (should be #232323)</div>
      </div>

      {/* CSS Variables Test */}
      <div className="space-y-2 mt-6">
        <h3 className="text-lg font-semibold text-white">CSS Variables:</h3>
        <div style={{ backgroundColor: 'var(--surface-primary, #FF0000)' }} className="p-4 rounded">
          var(--surface-primary) with red fallback
        </div>
        <div style={{ backgroundColor: 'var(--surface-secondary, #00FF00)' }} className="p-4 rounded">
          var(--surface-secondary) with green fallback
        </div>
      </div>

      {/* Computed Style Check */}
      <div className="mt-6">
        <h3 className="text-lg font-semibold text-white mb-2">Debug Info:</h3>
        <div id="debug-output" className="bg-gray-900 p-4 rounded font-mono text-sm">
          Check console for computed styles...
        </div>
      </div>
    </div>
  );
};

export default TestBackgroundColors;