/* Global debug provider styles */

.global-debug-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 999999;
}

.global-debug-label {
  position: absolute;
  font-size: 11px;
  font-family: 'Courier New', monospace;
  color: #00ff00;
  background: rgba(0, 0, 0, 0.9);
  padding: 2px 8px;
  border-radius: 4px;
  white-space: nowrap;
  pointer-events: none;
  border: 1px solid #00ff00;
  opacity: 0;
  transition: opacity 0.2s ease;
  transform: translateX(-50%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

.global-debug-label.active {
  opacity: 1;
}

/* Component path tooltip styles */
.component-path-tooltip {
  position: absolute;
  top: -25px;
  left: 0;
  font-size: 11px;
  font-family: 'Courier New', monospace;
  color: #00ff00;
  background: rgba(0, 0, 0, 0.95);
  padding: 3px 8px;
  border-radius: 4px;
  white-space: nowrap;
  z-index: 999999;
  pointer-events: none;
  border: 1px solid #00ff00;
  max-width: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Prevent debug labels from interfering with page layout */
.component-with-path {
  position: relative;
}