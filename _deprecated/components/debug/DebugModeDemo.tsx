import React from 'react';
import { isDebugMode, getDebugStyles } from '../../utils/debugMode';
import { ShadowButton } from '../shadow/ShadowButton';
import { ShadowInfoCard } from '../shadow/ShadowInfoCard';
import { ShadowStatCard } from '../shadow/ShadowStatCard';

/**
 * Debug Mode Demo Component
 * 
 * This component demonstrates how the debug mode feature works.
 * When debug mode is enabled via sessionStorage, role-specific components
 * will show colored borders and labels.
 * 
 * To enable debug mode:
 * 1. Open browser console
 * 2. Run: sessionStorage.setItem('superadmin_session_flags', '{"debug_mode": true}')
 * 3. Refresh the page
 */
export const DebugModeDemo: React.FC = () => {
  const debugEnabled = isDebugMode();
  
  // Example of how to apply debug styles to a custom component
  const CustomRoleComponent: React.FC<{ role: 'player' | 'coach' | 'parent' | 'member'; title: string; description: string }> = ({ role, title, description }) => {
    const debugStyles = getDebugStyles(role);
    
    return (
      <>
        {debugEnabled && (
          <style dangerouslySetInnerHTML={{ __html: debugStyles }} />
        )}
        <div className={`bg-gray-800 rounded-lg p-4 ${debugEnabled ? 'role-debug' : ''}`}>
          <h3 className="text-white font-semibold mb-2">{title}</h3>
          <p className="text-gray-400 text-sm">{description}</p>
        </div>
      </>
    );
  };
  
  return (
    <div className="space-y-6">
      {/* Debug Mode Status */}
      <div className="bg-gray-900 rounded-xl p-6">
        <h2 className="text-xl font-bold text-white mb-4">Debug Mode Demo</h2>
        <p className="text-gray-400 mb-4">
          Debug Mode Status: <span className={debugEnabled ? 'text-green-500' : 'text-red-500'}>
            {debugEnabled ? 'ENABLED' : 'DISABLED'}
          </span>
        </p>
        
        {!debugEnabled && (
          <div className="bg-gray-800 rounded-lg p-4">
            <p className="text-gray-300 mb-2">To enable debug mode:</p>
            <ol className="list-decimal list-inside text-sm text-gray-400 space-y-1">
              <li>Open browser console (F12)</li>
              <li>Run: <code className="bg-gray-700 px-2 py-1 rounded text-xs">{'sessionStorage.setItem(\'superadmin_session_flags\', \'{"debug_mode": true}\')'}</code></li>
              <li>Refresh the page</li>
            </ol>
          </div>
        )}
      </div>
      
      {/* Role-Specific Components Example */}
      <div className="bg-gray-900 rounded-xl p-6">
        <h3 className="text-white text-lg font-bold mb-4">Role-Specific Components</h3>
        <p className="text-gray-400 mb-6">
          When debug mode is enabled, these components will show colored borders and role labels.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <CustomRoleComponent 
            role="player"
            title="Player Dashboard"
            description="View your stats, evaluations, and progress tracking."
          />
          
          <CustomRoleComponent 
            role="coach"
            title="Team Management"
            description="Manage your teams, schedule training, and evaluate players."
          />
          
          <CustomRoleComponent 
            role="parent"
            title="Child Progress"
            description="Monitor your children's development and coach feedback."
          />
          
          <CustomRoleComponent 
            role="member"
            title="Member Hub"
            description="Access community resources and join local teams."
          />
        </div>
      </div>
      
      {/* Shadow Components with Debug */}
      <div className="bg-gray-900 rounded-xl p-6">
        <h3 className="text-white text-lg font-bold mb-4">Shadow Components in Debug Mode</h3>
        <p className="text-gray-400 mb-6">
          Shadow DOM components can also utilize debug mode for role identification.
        </p>
        
        <div className="space-y-4">
          {/* Player-specific stat */}
          <div className={debugEnabled ? 'role-debug-container' : ''}>
            {debugEnabled && (
              <style dangerouslySetInnerHTML={{ __html: getDebugStyles('player') }} />
            )}
            <div className={debugEnabled ? 'role-debug' : ''}>
              <ShadowStatCard 
                value={85}
                label="Player Rating"
                color="blue"
                size="medium"
                labelSize="sm"
              />
            </div>
          </div>
          
          {/* Coach-specific info */}
          <div className={debugEnabled ? 'role-debug-container' : ''}>
            {debugEnabled && (
              <style dangerouslySetInnerHTML={{ __html: getDebugStyles('coach') }} />
            )}
            <div className={debugEnabled ? 'role-debug' : ''}>
              <ShadowInfoCard 
                variant="info"
                title="Coach Tools"
                description="Access evaluation forms, training plans, and team analytics."
                size="medium"
              />
            </div>
          </div>
          
          {/* Parent-specific action */}
          <div className={debugEnabled ? 'role-debug-container' : ''}>
            {debugEnabled && (
              <style dangerouslySetInnerHTML={{ __html: getDebugStyles('parent') }} />
            )}
            <div className={debugEnabled ? 'role-debug' : ''}>
              <ShadowButton 
                text="View Child's Progress" 
                variant="primary" 
                size="medium" 
                onClick={() => console.log('Navigate to child progress')}
              />
            </div>
          </div>
          
          {/* Member-specific card */}
          <div className={debugEnabled ? 'role-debug-container' : ''}>
            {debugEnabled && (
              <style dangerouslySetInnerHTML={{ __html: getDebugStyles('member') }} />
            )}
            <div className={debugEnabled ? 'role-debug' : ''}>
              <ShadowInfoCard 
                variant="action"
                title="Join a Team"
                description="Find and connect with local teams in your area."
                icon="chevronRight"
                size="medium"
                onClick={() => console.log('Find teams')}
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Debug Styles Reference */}
      <div className="bg-gray-900 rounded-xl p-6">
        <h3 className="text-white text-lg font-bold mb-4">Role Color Reference</h3>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-2 rounded-lg bg-blue-500/20 border-2 border-blue-500 flex items-center justify-center">
              <span className="text-blue-500 font-bold text-xs">PLAYER</span>
            </div>
            <p className="text-gray-400 text-sm">Blue</p>
            <p className="text-gray-500 text-xs">#296DFF</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-2 rounded-lg bg-orange-500/20 border-2 border-orange-500 flex items-center justify-center">
              <span className="text-orange-500 font-bold text-xs">COACH</span>
            </div>
            <p className="text-gray-400 text-sm">Orange</p>
            <p className="text-gray-500 text-xs">#FF6F3C</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-2 rounded-lg bg-green-500/20 border-2 border-green-500 flex items-center justify-center">
              <span className="text-green-500 font-bold text-xs">PARENT</span>
            </div>
            <p className="text-gray-400 text-sm">Green</p>
            <p className="text-gray-500 text-xs">#00BB00</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-2 rounded-lg bg-purple-500/20 border-2 border-purple-500 flex items-center justify-center">
              <span className="text-purple-500 font-bold text-xs">MEMBER</span>
            </div>
            <p className="text-gray-400 text-sm">Purple</p>
            <p className="text-gray-500 text-xs">#9333EA</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-2 rounded-lg bg-red-500/20 border-2 border-red-500 flex items-center justify-center">
              <span className="text-red-500 font-bold text-xs">SUPER</span>
            </div>
            <p className="text-gray-400 text-sm">Red</p>
            <p className="text-gray-500 text-xs">#DC2626</p>
          </div>
        </div>
      </div>
    </div>
  );
};
