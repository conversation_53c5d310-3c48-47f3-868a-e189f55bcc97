import React, { useEffect, useRef, useState } from 'react';

interface DebugShadowModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const DebugShadowModal: React.FC<DebugShadowModalProps> = ({ isOpen, onClose }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  const addDebug = (message: string) => {
    console.log(`[DebugShadowModal] ${message}`);
    setDebugInfo(prev => [...prev, `${new Date().toISOString().split('T')[1]}: ${message}`]);
  };

  useEffect(() => {
    if (!isOpen) {
      addDebug('Modal closed');
      return;
    }

    addDebug('Starting modal render');

    // Create portal container if it doesn't exist
    let portalContainer = document.getElementById('shadow-modal-portal');
    if (!portalContainer) {
      addDebug('Creating portal container');
      portalContainer = document.createElement('div');
      portalContainer.id = 'shadow-modal-portal';
      portalContainer.style.position = 'fixed';
      portalContainer.style.top = '0';
      portalContainer.style.left = '0';
      portalContainer.style.width = '100%';
      portalContainer.style.height = '100%';
      portalContainer.style.pointerEvents = 'none';
      portalContainer.style.zIndex = '99999';
      document.body.appendChild(portalContainer);
    }

    // Create modal container
    const modalContainer = document.createElement('div');
    modalContainer.style.pointerEvents = 'auto';
    
    // Attach shadow root
    const shadowRoot = modalContainer.attachShadow({ mode: 'open' });
    addDebug('Shadow root created');

    // Create styles
    const style = document.createElement('style');
    style.textContent = `
      :host {
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
      }
      
      .backdrop {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.95);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
      
      .modal {
        background-color: #1E1E1E;
        border: 2px solid #6B00DB;
        padding: 32px;
        border-radius: 16px;
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.8);
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        cursor: default;
      }
      
      h2 {
        color: white;
        margin: 0 0 16px 0;
        font-family: Arial, sans-serif;
        font-size: 24px;
      }
      
      .debug-info {
        background: #111;
        border: 1px solid #333;
        border-radius: 8px;
        padding: 16px;
        margin: 16px 0;
        font-family: monospace;
        font-size: 12px;
        color: #0f0;
        max-height: 200px;
        overflow-y: auto;
      }
      
      .debug-line {
        margin: 2px 0;
      }
      
      p {
        color: #B3B3B3;
        margin: 0 0 24px 0;
        font-family: Arial, sans-serif;
        font-size: 16px;
      }
      
      .button-group {
        display: flex;
        gap: 12px;
      }
      
      button {
        background-color: #6B00DB;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 16px;
        cursor: pointer;
        font-family: Arial, sans-serif;
        flex: 1;
      }
      
      button:hover {
        background-color: #5A00BA;
      }
      
      button.secondary {
        background-color: #333;
      }
      
      button.secondary:hover {
        background-color: #444;
      }
    `;

    // Create elements
    const backdrop = document.createElement('div');
    backdrop.className = 'backdrop';
    backdrop.onclick = (e) => {
      if (e.target === backdrop) {
        addDebug('Backdrop clicked');
        onClose();
      }
    };

    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.onclick = (e) => e.stopPropagation();

    const title = document.createElement('h2');
    title.textContent = 'Debug Shadow Modal';

    const message = document.createElement('p');
    message.textContent = 'This modal uses Shadow DOM with a portal approach. Background should be solid black.';

    // Debug info section
    const debugDiv = document.createElement('div');
    debugDiv.className = 'debug-info';
    debugDiv.innerHTML = '<div class="debug-line">Debug Log:</div>';

    // Buttons
    const buttonGroup = document.createElement('div');
    buttonGroup.className = 'button-group';

    const testButton = document.createElement('button');
    testButton.className = 'secondary';
    testButton.textContent = 'Test Alert';
    testButton.onclick = () => {
      addDebug('Test button clicked');
      alert('Button works inside shadow DOM!');
    };

    const closeButton = document.createElement('button');
    closeButton.textContent = 'Close Modal';
    closeButton.onclick = () => {
      addDebug('Close button clicked');
      onClose();
    };

    // Assemble
    buttonGroup.appendChild(testButton);
    buttonGroup.appendChild(closeButton);
    
    modal.appendChild(title);
    modal.appendChild(message);
    modal.appendChild(debugDiv);
    modal.appendChild(buttonGroup);
    backdrop.appendChild(modal);
    
    shadowRoot.appendChild(style);
    shadowRoot.appendChild(backdrop);

    // Add to portal
    portalContainer.appendChild(modalContainer);
    addDebug('Modal added to portal');

    // Update debug info
    const updateDebugInfo = () => {
      const debugContent = shadowRoot.querySelector('.debug-info');
      if (debugContent) {
        debugContent.innerHTML = `
          <div class="debug-line">Debug Log:</div>
          ${debugInfo.map(line => `<div class="debug-line">${line}</div>`).join('')}
          <div class="debug-line">${new Date().toISOString().split('T')[1]}: Live update</div>
        `;
      }
    };

    const interval = setInterval(updateDebugInfo, 1000);

    // Cleanup
    return () => {
      clearInterval(interval);
      if (modalContainer.parentNode) {
        addDebug('Cleaning up modal');
        modalContainer.remove();
      }
      
      // Remove portal if no more modals
      if (portalContainer && portalContainer.children.length === 0) {
        portalContainer.remove();
      }
    };
  }, [isOpen, onClose, debugInfo]);

  return null; // No React render needed
};

export default DebugShadowModal;