import React, { useState, useEffect, createContext, useContext } from 'react';
import './debug-margins.css';

// Create context for debug state
const DebugContext = createContext<{
  showLegend: boolean;
  setShowLegend: (show: boolean) => void;
}>({
  showLegend: false,
  setShowLegend: () => {}
});

/**
 * DebugMargins Component
 * 
 * Adds a toggle button to enable/disable visual debugging of component margins.
 * When enabled, shows colored borders and component names for all major components.
 * 
 * Usage: Add <DebugMargins /> to your App.tsx
 */
export const DebugMargins: React.FC = () => {
  const [enabled, setEnabled] = useState(false);
  const [showLegend, setShowLegend] = useState(false);

  useEffect(() => {
    // Check localStorage for saved state
    const saved = localStorage.getItem('debug-margins');
    if (saved === 'true') {
      setEnabled(true);
      document.body.classList.add('debug-margins');
    }
  }, []);

  const toggleDebug = () => {
    const newState = !enabled;
    setEnabled(newState);
    
    if (newState) {
      document.body.classList.add('debug-margins');
      localStorage.setItem('debug-margins', 'true');
      // Show legend when turning on debug mode
      setShowLegend(true);
    } else {
      document.body.classList.remove('debug-margins');
      localStorage.setItem('debug-margins', 'false');
      setShowLegend(false);
    }
  };

  const toggleLegend = () => {
    if (enabled) {
      setShowLegend(prev => !prev);
    }
  };

  return (
    <DebugContext.Provider value={{ showLegend, setShowLegend }}>
      <div style={{
        position: 'fixed',
        bottom: '20px',
        left: '20px',
        zIndex: 10000,
        display: 'flex',
        gap: '8px',
        alignItems: 'center'
      }}>
        <div style={{
          background: enabled ? '#00ff00' : '#333',
          padding: '8px 12px',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '12px',
          color: enabled ? '#000' : '#fff',
          border: '2px solid ' + (enabled ? '#00ff00' : '#666'),
          fontFamily: 'monospace',
          boxShadow: '0 2px 8px rgba(0,0,0,0.3)'
        }} onClick={toggleDebug}>
          <span style={{ marginRight: '5px' }}>🔍</span>
          Debug: {enabled ? 'ON' : 'OFF'}
        </div>
        
        {enabled && (
          <div style={{
            background: showLegend ? '#9333EA' : '#555',
            padding: '8px 12px',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '12px',
            color: '#fff',
            border: '2px solid ' + (showLegend ? '#9333EA' : '#777'),
            fontFamily: 'monospace',
            boxShadow: '0 2px 8px rgba(0,0,0,0.3)',
            transition: 'all 0.2s ease'
          }} onClick={toggleLegend}>
            <span style={{ marginRight: '5px' }}>🎨</span>
            Legend
          </div>
        )}
      </div>
      <DebugLegend />
    </DebugContext.Provider>
  );
};

/**
 * Legend for debug colors
 */
export const DebugLegend: React.FC = () => {
  const { showLegend, setShowLegend } = useContext(DebugContext);
  const [isDebugEnabled, setIsDebugEnabled] = useState(false);
  const [timeLeft, setTimeLeft] = useState(10);

  useEffect(() => {
    // Check if debug mode is enabled
    const checkDebugMode = () => {
      const enabled = document.body.classList.contains('debug-margins');
      setIsDebugEnabled(enabled);
    };

    // Initial check
    checkDebugMode();

    // Watch for changes to debug mode
    const observer = new MutationObserver(() => {
      checkDebugMode();
    });

    observer.observe(document.body, {
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (showLegend && isDebugEnabled) {
      setTimeLeft(10);
      
      // ESC key handler
      const handleEsc = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          setShowLegend(false);
        }
      };
      window.addEventListener('keydown', handleEsc);
      
      // Countdown timer
      const interval = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            setShowLegend(false);
            return 10;
          }
          return prev - 1;
        });
      }, 1000);

      // Auto-hide after 10 seconds
      const timer = setTimeout(() => {
        setShowLegend(false);
      }, 10000);

      return () => {
        window.removeEventListener('keydown', handleEsc);
        clearInterval(interval);
        clearTimeout(timer);
      };
    }
  }, [showLegend, isDebugEnabled, setShowLegend]);

  if (!isDebugEnabled || !showLegend) return null;

  const colors = [
    { name: 'StandardHeader', color: '#ff00ff', textColor: '#fff' },
    { name: 'SecondaryNav', color: '#00ff00', textColor: '#000' },
    { name: 'nav-header', color: '#ffff00', textColor: '#000' },
    { name: 'page-content', color: '#00ffff', textColor: '#000' },
    { name: 'shot-card', color: '#ff6600', textColor: '#fff' },
    { name: 'EntityCard', color: '#9333EA', textColor: '#fff' },
    { name: 'IonContent', color: '#ff1493', textColor: '#fff' },
    { name: 'grid', color: '#ff69b4', textColor: '#fff' }
  ];

  return (
    <div style={{
      position: 'fixed',
      top: '20px',
      right: '20px',
      zIndex: 10000,
      background: 'rgba(20, 20, 20, 0.98)',
      padding: '16px',
      borderRadius: '8px',
      fontSize: '12px',
      color: '#fff',
      fontFamily: 'monospace',
      border: '2px solid #444',
      minWidth: '250px',
      opacity: showLegend ? 1 : 0,
      transition: 'opacity 0.3s ease-out',
      boxShadow: '0 8px 24px rgba(0,0,0,0.8)',
      backdropFilter: 'blur(10px)'
    }}>
      <div style={{ 
        marginBottom: '12px', 
        fontWeight: 'bold', 
        borderBottom: '2px solid #444', 
        paddingBottom: '8px', 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center' 
      }}>
        <span style={{ fontSize: '14px' }}>Component Colors</span>
        <span style={{ 
          fontSize: '11px', 
          color: '#999',
          background: 'rgba(255,255,255,0.1)',
          padding: '2px 8px',
          borderRadius: '12px'
        }}>{timeLeft}s</span>
      </div>
      
      {colors.map((item, index) => (
        <div key={index} style={{ 
          display: 'flex', 
          alignItems: 'center', 
          marginBottom: '8px',
          padding: '4px',
          borderRadius: '4px',
          background: 'rgba(255,255,255,0.05)',
          transition: 'background 0.2s ease'
        }} onMouseEnter={(e) => {
          e.currentTarget.style.background = 'rgba(255,255,255,0.1)';
        }} onMouseLeave={(e) => {
          e.currentTarget.style.background = 'rgba(255,255,255,0.05)';
        }}>
          <div style={{ 
            width: '24px', 
            height: '24px', 
            backgroundColor: item.color, 
            marginRight: '12px', 
            borderRadius: '4px',
            border: '2px solid rgba(255,255,255,0.3)',
            boxShadow: `0 2px 8px ${item.color}66, inset 0 0 8px ${item.color}44`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontWeight: 'bold',
            fontSize: '10px',
            color: item.textColor
          }}>
            {index + 1}
          </div>
          <span style={{ 
            fontSize: '13px',
            letterSpacing: '0.5px'
          }}>{item.name}</span>
        </div>
      ))}
      
      <div style={{ 
        marginTop: '12px', 
        fontSize: '10px', 
        color: '#666', 
        textAlign: 'center', 
        borderTop: '1px solid #333', 
        paddingTop: '8px',
        cursor: 'pointer',
        transition: 'color 0.2s ease'
      }} 
      onMouseEnter={(e) => { e.currentTarget.style.color = '#999'; }}
      onMouseLeave={(e) => { e.currentTarget.style.color = '#666'; }}
      onClick={() => setShowLegend(false)}>
        Click to close • ESC key
      </div>
    </div>
  );
};
