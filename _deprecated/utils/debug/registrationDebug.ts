// Registration debugging utility functions

/**
 * Helper function to verify registration data across auth and profiles
 */
export const verifyRegistrationData = async (supabase: any, userId: string) => {
  console.log('=== REGISTRATION DATA VERIFICATION ===');
  console.log(`Checking data for user ID: ${userId}`);

  try {
    // 1. Get current user data from auth
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    const userData = session?.user;
    const userError = sessionError;
    
    if (userError) {
      console.error('Error fetching auth user data:', userError);
    } else if (!userData) {
      console.error('No user data returned from auth.user()');
    } else {
      console.log('Auth user data:', {
        id: userData.id,
        email: userData.email,
        metadata: userData.user_metadata
      });
    }

    // 2. Get profile data
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (profileError) {
      console.error('Error fetching profile data:', profileError);
    } else if (!profileData) {
      console.error('No profile found for user');
    } else {
      console.log('Profile data:', profileData);
      
      // 3. Check for discrepancies
      const discrepancies = [];
      
      if (userData?.user_metadata?.full_name !== profileData.full_name) {
        discrepancies.push({
          field: 'full_name',
          auth: userData?.user_metadata?.full_name,
          profile: profileData.full_name
        });
      }
      
      if (userData?.user_metadata?.avatar_url !== profileData.avatar_url) {
        discrepancies.push({
          field: 'avatar_url',
          auth: userData?.user_metadata?.avatar_url,
          profile: profileData.avatar_url
        });
      }
      
      if (userData?.user_metadata?.date_of_birth !== profileData.date_of_birth) {
        discrepancies.push({
          field: 'date_of_birth',
          auth: userData?.user_metadata?.date_of_birth,
          profile: profileData.date_of_birth
        });
      }
      
      if (discrepancies.length > 0) {
        console.warn('Data discrepancies found:', discrepancies);
      } else {
        console.log('✅ No data discrepancies found!');
      }
    }

    // 4. Check team membership
    if (userData?.user_metadata?.team_id) {
      const teamId = userData?.user_metadata?.team_id;
      console.log(`Checking team membership for team ID: ${teamId}`);
      
      const { data: teamMemberData, error: teamMemberError } = await supabase
        .from('team_members')
        .select('*')
        .eq('team_id', teamId)
        .eq('user_id', userId)
        .maybeSingle();
      
      if (teamMemberError) {
        console.error('Error checking team membership:', teamMemberError);
      } else if (!teamMemberData) {
        console.warn('⚠️ User is not a member of the team specified in metadata');
        
        // Check for join requests (for minors)
        const { data: joinRequestData, error: joinRequestError } = await supabase
          .from('team_join_requests')
          .select('*')
          .eq('team_id', teamId)
          .eq('player_id', userId)
          .maybeSingle();
        
        if (joinRequestError) {
          console.error('Error checking team join requests:', joinRequestError);
        } else if (joinRequestData) {
          console.log('ℹ️ Found pending team join request:', joinRequestData);
        } else {
          console.error('❌ No team membership or join request found!');
        }
      } else {
        console.log('✅ User is properly added to the team:', teamMemberData);
      }
    } else {
      console.log('No team ID in user metadata - skipping team membership check');
    }

    console.log('=== VERIFICATION COMPLETE ===');
  } catch (error) {
    console.error('Error during registration verification:', error);
  }
};

/**
 * Fix common registration data issues
 */
export const fixRegistrationData = async (supabase: any, userId: string, correctData: any) => {
  console.log('=== ATTEMPTING TO FIX REGISTRATION DATA ===');
  
  try {
    // 1. Update profile with correct data
    const { data: updateData, error: updateError } = await supabase
      .from('profiles')
      .update({
        full_name: correctData.fullName,
        avatar_url: correctData.avatarUrl,
        date_of_birth: correctData.dateOfBirth,
        registration_metadata: {
          fixed_at: new Date().toISOString(),
          team_id: correctData.teamId,
          team_name: correctData.teamName,
          manual_fix: true
        }
      })
      .eq('id', userId)
      .select();
    
    if (updateError) {
      console.error('Error updating profile:', updateError);
    } else {
      console.log('✅ Profile updated successfully:', updateData);
    }
    
    // 2. Fix team membership if needed
    if (correctData.teamId) {
      // Check if user is already a team member
      const { data: existingMember } = await supabase
        .from('team_members')
        .select('membership_id')
        .eq('team_id', correctData.teamId)
        .eq('user_id', userId)
        .maybeSingle();
      
      if (!existingMember) {
        // Add user to team if not already a member
        const membershipId = crypto.randomUUID();
        
        const { data: memberData, error: memberError } = await supabase
          .from('team_members')
          .insert({
            membership_id: membershipId,
            team_id: correctData.teamId,
            user_id: userId,
            role: 'player',
            status: 'active',
            joined_at: new Date().toISOString()
          })
          .select();
        
        if (memberError) {
          console.error('Error adding user to team:', memberError);
        } else {
          console.log('✅ User added to team successfully:', memberData);
        }
      } else {
        console.log('User is already a team member, no action needed');
      }
    }
    
    console.log('=== FIX COMPLETE ===');
  } catch (error) {
    console.error('Error during fix process:', error);
  }
};
