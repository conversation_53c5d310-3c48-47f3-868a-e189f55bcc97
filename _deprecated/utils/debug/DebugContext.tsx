import React, { createContext, useContext, useState } from 'react';

// Create a context for debug mode
const DebugContext = createContext({
  isDebugModeActive: false,
  toggleDebugMode: () => {},
});

// Provider component
export const DebugProvider = ({ children }) => {
  const [isDebugModeActive, setIsDebugModeActive] = useState(false);

  const toggleDebugMode = () => {
    setIsDebugModeActive(prev => !prev);
  };

  return (
    <DebugContext.Provider value={{ isDebugModeActive, toggleDebugMode }}>
      {children}
    </DebugContext.Provider>
  );
};

// Hook for components to access debug context
export const useDebug = () => useContext(DebugContext);
