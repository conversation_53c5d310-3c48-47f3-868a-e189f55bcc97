import React from 'react';

// Simple HOC that adds a visible border and label to any component
export const withDebugBorders = (Component, name = 'Component') => {
  return (props) => {
    return (
      <div style={{
        position: 'relative',
        border: '2px dashed red',
        margin: '2px',
        padding: '2px',
      }}>
        <div style={{
          position: 'absolute',
          top: '-10px',
          left: '5px',
          background: 'red',
          color: 'white',
          padding: '0 5px',
          fontSize: '10px',
          fontWeight: 'bold',
          borderRadius: '3px',
          zIndex: 1000,
        }}>
          {name}
        </div>
        <Component {...props} />
      </div>
    );
  };
};
