// ABOUTME: Global component debugging that hooks into React DevTools to show component paths
// This provides automatic path display for ALL components when debug mode is enabled

import React from 'react';
import { isDebugMode } from './debugMode';

// Store original createElement to restore later
const originalCreateElement = React.createElement;

// Track component paths
const componentPaths = new WeakMap<React.ComponentType<any>, string>();

/**
 * Register a component with its file path
 * This should be called in each component file
 */
export const registerComponent = (component: React.ComponentType<any>, path: string) => {
  componentPaths.set(component, path);
};

/**
 * HOC that adds debug info to any component
 */
const withGlobalDebug = <P extends object>(
  Component: React.ComponentType<P>,
  displayName?: string
): React.ComponentType<P> => {
  const DebugWrapped = React.forwardRef<any, P>((props, ref) => {
    const [showPath, setShowPath] = React.useState(false);
    
    if (!isDebugMode()) {
      return <Component {...props} ref={ref} />;
    }

    // Try to get the component path
    const componentPath = componentPaths.get(Component) || 
                         Component.displayName || 
                         Component.name || 
                         'Unknown Component';

    return (
      <div 
        className="global-debug-wrapper"
        onMouseEnter={() => setShowPath(true)}
        onMouseLeave={() => setShowPath(false)}
        style={{ position: 'relative' }}
      >
        {showPath && (
          <div
            className="global-debug-tooltip"
            style={{
              position: 'absolute',
              top: '-25px',
              left: '0',
              fontSize: '11px',
              fontFamily: 'monospace',
              color: '#00ff00',
              background: 'rgba(0, 0, 0, 0.95)',
              padding: '3px 8px',
              borderRadius: '4px',
              whiteSpace: 'nowrap',
              zIndex: 999999,
              pointerEvents: 'none',
              border: '1px solid #00ff00',
              maxWidth: 'none',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.5)'
            }}
          >
            {componentPath}
          </div>
        )}
        <Component {...props} ref={ref} />
      </div>
    );
  });

  DebugWrapped.displayName = displayName || Component.displayName || Component.name;
  return DebugWrapped;
};

/**
 * Enable global component debugging
 * This wraps React.createElement to add debug info to all components
 */
export const enableGlobalComponentDebug = () => {
  // Override React.createElement
  (React as any).createElement = function(...args: any[]) {
    const [type, props, ...children] = args;
    
    // Only wrap function/class components, not DOM elements
    if (typeof type === 'function' && isDebugMode()) {
      // Skip if already wrapped or is a Fragment/StrictMode/etc
      if (type._isDebugWrapped || 
          type === React.Fragment || 
          type === React.StrictMode ||
          type === React.Suspense ||
          type === React.Profiler ||
          type.$$typeof) {
        return originalCreateElement.apply(React, args);
      }

      // Create wrapped version
      const wrappedType = withGlobalDebug(type, type.displayName || type.name);
      (wrappedType as any)._isDebugWrapped = true;
      
      return originalCreateElement(wrappedType, props, ...children);
    }
    
    return originalCreateElement.apply(React, args);
  };
};

/**
 * Disable global component debugging
 */
export const disableGlobalComponentDebug = () => {
  (React as any).createElement = originalCreateElement;
};

/**
 * React DevTools integration for better component path detection
 */
export const enhanceWithDevTools = () => {
  if (typeof window !== 'undefined' && (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    const hook = (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__;
    const originalInject = hook.inject;
    
    hook.inject = function(renderer: any) {
      const result = originalInject.call(this, renderer);
      
      // Try to enhance component stack traces
      if (renderer && renderer.findFiberByHostInstance) {
        const originalFindFiber = renderer.findFiberByHostInstance;
        renderer.findFiberByHostInstance = function(...args: any[]) {
          const fiber = originalFindFiber.apply(this, args);
          if (fiber && fiber._debugSource) {
            console.log('Component source:', fiber._debugSource);
          }
          return fiber;
        };
      }
      
      return result;
    };
  }
};

/**
 * Babel plugin helper for automatic registration
 * This would be used in a babel plugin to automatically register components
 */
export const babelPluginHelper = `
// Add this to your babel config to automatically register components:
// babel-plugin-component-path-register.js
module.exports = function() {
  return {
    visitor: {
      ExportDefaultDeclaration(path, state) {
        const filename = state.filename;
        const relativePath = filename.replace(process.cwd() + '/', '');
        
        // Add registration call
        path.insertAfter(
          t.expressionStatement(
            t.callExpression(
              t.memberExpression(
                t.identifier('globalComponentDebug'),
                t.identifier('registerComponent')
              ),
              [
                path.node.declaration,
                t.stringLiteral(relativePath)
              ]
            )
          )
        );
      }
    }
  };
};
`;