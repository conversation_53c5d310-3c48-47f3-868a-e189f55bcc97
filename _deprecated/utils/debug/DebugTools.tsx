import React from 'react';
import { useDebug } from './DebugContext';

// CSS for the component borders
const debugStyles = {
  wrapper: {
    position: 'relative',
    border: '2px dashed red',
    margin: '4px',
    padding: '4px',
  },
  label: {
    position: 'absolute',
    top: '-10px',
    left: '5px',
    background: 'red',
    color: 'white',
    padding: '0 5px',
    fontSize: '10px',
    fontWeight: 'bold',
    borderRadius: '3px',
    zIndex: 1000,
  }
};

// Higher Order Component to wrap components with debugging
export const withDebug = (WrappedComponent, componentName) => {
  // Return the enhanced component
  const WithDebugComponent = (props) => {
    const { isDebugModeActive } = useDebug();

    if (!isDebugModeActive) {
      return <WrappedComponent {...props} />;
    }

    // Apply debugging styles when debug mode is active
    return (
      <div style={debugStyles.wrapper}>
        <div style={debugStyles.label}>{componentName}</div>
        <WrappedComponent {...props} />
      </div>
    );
  };

  // Set display name for the component
  WithDebugComponent.displayName = `Debug(${componentName})`;
  
  return WithDebugComponent;
};

// Component to toggle debug mode
export const DebugToggle = () => {
  const { isDebugModeActive, toggleDebugMode } = useDebug();
  
  const toggleButtonStyle = {
    position: 'fixed',
    bottom: '20px',
    right: '20px',
    zIndex: 10000,
    backgroundColor: isDebugModeActive ? 'red' : 'green',
    color: 'white',
    padding: '10px 15px',
    borderRadius: '5px',
    fontWeight: 'bold',
    boxShadow: '0 2px 10px rgba(0,0,0,0.2)',
    cursor: 'pointer',
  };
  
  return (
    <button 
      style={toggleButtonStyle} 
      onClick={toggleDebugMode}
    >
      {isDebugModeActive ? 'Disable Debug' : 'Enable Debug'}
    </button>
  );
};
