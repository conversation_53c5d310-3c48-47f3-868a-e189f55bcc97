import { supabase } from '@/lib/supabase';

export interface AuthDebugInfo {
  supabaseUrl: string;
  supabaseAnonKey: string;
  authConfig: any;
  emailSettings: any;
  recentUsers: any[];
  projectInfo: any;
}

/**
 * Debug utility to help troubleshoot authentication and email verification issues
 */
export class AuthDebugger {
  
  /**
   * Test the current auth configuration and email settings
   */
  static async debugAuthConfig(): Promise<AuthDebugInfo> {
    console.log('%c === AUTH DEBUG SESSION ===', 'background: #e67e22; color: white; padding: 3px 6px; border-radius: 3px; font-weight: bold;');
    
    const debugInfo: Partial<AuthDebugInfo> = {};
    
    try {
      // 1. Basic Supabase configuration
      console.log('Checking Supabase configuration...');
      debugInfo.supabaseUrl = supabase.supabaseUrl;
      debugInfo.supabaseAnonKey = supabase.supabaseKey.substring(0, 20) + '...';
      
      console.log('Supabase URL:', debugInfo.supabaseUrl);
      console.log('Anon Key (truncated):', debugInfo.supabaseAnonKey);
      
      // 2. Test basic connectivity
      console.log('Testing Supabase connectivity...');
      const { data: pingData, error: pingError } = await supabase
        .from('profiles')
        .select('count')
        .limit(1);
        
      if (pingError) {
        console.error('Connectivity test failed:', pingError);
      } else {
        console.log('Supabase connectivity confirmed');
      }
      
      // 3. Check recent users in auth.users table
      console.log('Checking recent user registrations...');
      try {
        const { data: usersData, error: usersError } = await supabase
          .from('auth.users')
          .select('id, email, email_confirmed_at, created_at')
          .order('created_at', { ascending: false })
          .limit(5);
          
        if (usersError) {
          console.warn('Cannot access auth.users directly (this is normal):', usersError.message);
          
          // Try alternative approach via profiles table
          const { data: profilesData, error: profilesError } = await supabase
            .from('profiles')
            .select('id, email, created_at')
            .order('created_at', { ascending: false })
            .limit(5);
            
          if (profilesData) {
            debugInfo.recentUsers = profilesData;
            console.log('Recent profiles:', profilesData);
          }
        } else {
          debugInfo.recentUsers = usersData;
          console.log('Recent users:', usersData);
        }
      } catch (usersFetchError) {
        console.warn('Could not fetch recent users:', usersFetchError);
      }
      
      // 4. Test auth configuration by trying to get current session
      console.log('Checking current auth session...');
      try {
        // Use Supabase v1.31.1 API for session
        const { data: { session } } = await supabase.auth.getSession();
        const user = session?.user;
        
        console.log('Current session:', {
          hasSession: !!session,
          user: user ? {
            id: user.id,
            email: user.email,
            confirmed: !!user.email_confirmed_at
          } : null
        });
      } catch (sessionError) {
        console.warn('Session check error:', sessionError);
      }
      
      // 5. Check auth settings (this might not work depending on RLS)
      console.log('Attempting to check auth settings...');
      try {
        // This is a system query and likely won't work, but worth trying
        const { data: settingsData, error: settingsError } = await supabase
          .rpc('get_auth_settings'); // Custom function if it exists
          
        if (!settingsError && settingsData) {
          debugInfo.authConfig = settingsData;
          console.log('Auth settings:', settingsData);
        }
      } catch (settingsErr) {
        console.log('Auth settings not accessible (this is normal for client-side)');
      }
      
      // 6. Environmental information
      console.log('Environment information:');
      console.log('  - Current URL:', window.location.href);
      console.log('  - Origin:', window.location.origin);
      console.log('  - User Agent:', navigator.userAgent);
      console.log('  - Environment:', import.meta.env.MODE || 'unknown');
      
      // 7. Email verification test
      console.log('Email verification information:');
      console.log('  - Configured redirect URL:', window.location.origin + '/login');
      console.log('  - Is localhost:', window.location.hostname === 'localhost');
      console.log('  - Is development:', import.meta.env.DEV);
      
      if (window.location.hostname === 'localhost') {
        console.log('IMPORTANT: Email delivery may be limited on localhost');
        console.log('  - Consider checking spam folder');
        console.log('  - Check Supabase project email settings');
        console.log('  - Verify SMTP configuration in Supabase dashboard');
      }
      
    } catch (error) {
      console.error('Auth debug error:', error);
    }
    
    console.log('%c === AUTH DEBUG COMPLETE ===', 'background: #e67e22; color: white; padding: 3px 6px; border-radius: 3px; font-weight: bold;');
    
    return debugInfo as AuthDebugInfo;
  }
  
  /**
   * Test email verification by attempting to resend for a specific email
   */
  static async testEmailResend(email: string): Promise<void> {
    console.log('%c === TESTING EMAIL RESEND ===', 'background: #3498db; color: white; padding: 3px 6px; border-radius: 3px; font-weight: bold;');
    console.log('Target email:', email);
    
    try {
      // Use Supabase v1.31.1 API for resend
      const { data, error } = await supabase.auth.api.resendConfirmationEmail(email);
      
      if (error) {
        console.error('Resend failed:', error);
      } else {
        console.log('Resend successful:', data);
      }
    } catch (resendError) {
      console.error('Resend exception:', resendError);
      
      // Try alternative v1 approach
      try {
        console.log('Trying alternative resend method...');
        const { error: altError } = await supabase.auth.resetPasswordForEmail(email, {
          redirectTo: window.location.origin + '/login'
        });
        
        if (altError) {
          console.error('Alternative resend failed:', altError);
        } else {
          console.log('Alternative resend successful');
        }
      } catch (altError) {
        console.error('All resend methods failed:', altError);
      }
    }
  }
  
  /**
   * Monitor auth state changes
   */
  static startAuthStateMonitoring(): () => void {
    console.log('Starting auth state monitoring...');
    
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('%c AUTH STATE CHANGE', 'background: #9b59b6; color: white; padding: 2px 5px; border-radius: 3px;');
      console.log('Event:', event);
      console.log('Session:', session ? {
        access_token: session.access_token ? 'present' : 'missing',
        user: session.user ? {
          id: session.user.id,
          email: session.user.email,
          confirmed: !!session.user.email_confirmed_at
        } : null
      } : 'null');
    });
    
    // Return cleanup function
    return () => {
      console.log('Stopping auth state monitoring');
      subscription.unsubscribe();
    };
  }
}

// Auto-start monitoring in development
if (import.meta.env.DEV) {
  // Add a global debug function
  (window as any).debugAuth = () => AuthDebugger.debugAuthConfig();
  (window as any).testEmailResend = (email: string) => AuthDebugger.testEmailResend(email);
  
  console.log('Auth debug utilities available:');
  console.log('  - debugAuth() - Run full auth diagnostics');
  console.log('  - testEmailResend(email) - Test email resend for specific email');
}
