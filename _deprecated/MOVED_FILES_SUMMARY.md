# Deprecated Files Summary

This folder contains files that were moved from active development directories to clean up the codebase. Files were moved on 2025-08-17.

## Total Files Moved: 90

## Categories of Moved Files:

### 1. Files with Deprecated Extensions (17 files)
- `.backup` files - Backup versions of files that were kept as safety copies
- `.old` files - Older versions of files that were replaced
- `.deleted` files - Files that were marked for deletion but kept
- `.disabled` files - Files that were disabled but kept for reference

**Moved files:**
- `styles/shot-components.css.backup`
- `styles/v2-evaluation.css.backup`
- `supabase/supabaseClient.ts.backup`
- `routes/CleanRoutes.tsx.old`
- `styles/global-footer.css.deleted`
- `styles/v2-navigation-fix.css.deleted`
- `test.css.deleted`
- `styles/black-theme-global.css.disabled`
- `styles/design-system.css.disabled`
- `styles/global-background-override.css.disabled`
- `styles/v2-design-system.css.disabled`
- `theme/DateTimePickerFix.css.disabled`
- `theme/button-fix.css.disabled`
- `theme/button-showcase-fix.css.disabled`
- `theme/darkmode.css.disabled`
- `theme/debug-content-fix.css.disabled`
- `theme/header-fix.css.disabled`
- `theme/ionic-dark-theme.css.disabled`
- `theme/shot-brand-enhanced.css.disabled`
- `theme/shot-brand.css.disabled`
- `theme/variables.css.disabled`

### 2. Storybook Files (26 files)
- Component story files (`.stories.tsx`, `.stories.ts`)
- Main Storybook configuration and assets

**Moved directories:**
- `stories/main-stories/` - Complete Storybook setup with assets
- `components/ComingSoon.stories.tsx`
- `components/shadow/form/ShadowSportSelector.stories.tsx`
- `components/shadow/form/ShadowRadioGroup.stories.tsx`
- `components/shadow/ShadowStatusBadge.stories.tsx`
- `components/shadow/ShadowSectionHeader.stories.tsx`
- `components/shadow/ShadowIconHeader.stories.tsx`
- `components/shadow/ShadowBackButton.stories.tsx`
- `components/shadow/ShadowEventCard.stories.tsx`

### 3. Test/Demo/Debug Components (22 files)
- Test components used for development/testing
- Demo components used for showcasing features
- Debug utilities and components
- Showcase pages

**Moved files include:**
- Test components: `TestBackgroundColors.tsx`, `TestCartBadge.tsx`, `TestDirect.tsx`, `TestPage.tsx`
- Demo components: `DemoStyleHeader.tsx`
- Debug utilities: `authDebug.ts`, `registrationDebug.ts`, `DebugTools.tsx`, `DebugContext.tsx`
- Debug components: `AuthDebug.tsx`, `BackgroundDebugReport.tsx`, `DebugShadowModal.tsx`
- Showcase pages: `ButtonShowcasePage.tsx`, `EvaluationShowcase.tsx`, `TeamSelectorShowcase.tsx`

### 4. Coach Debug Components (6 files)
- Debug components specific to coach functionality
- Event debug components
- Club management debug utilities

### 5. Design System Debug (1 file)
- Debug sections for the design system

## Folder Structure Maintained:
All files were moved while maintaining their relative folder structure within the `_deprecated` directory. For example:
- `src/styles/file.css.disabled` → `src/_deprecated/styles/file.css.disabled`
- `src/components/shadow/Component.stories.tsx` → `src/_deprecated/stories/components/shadow/Component.stories.tsx`

## Files NOT Moved:
- Core application files that are actively used
- Files in `src/foundation/` (as requested)
- Actual test files (`*.test.tsx`) that test core functionality
- Components that are part of the main application flow

## Next Steps:
1. Verify that the application still works without these files
2. Update any imports that might reference these files (though most should be development-only)
3. Consider removing these files entirely after confirming they're not needed
4. Review the deprecated folder periodically and remove files that are confirmed to be unnecessary

## Note:
Some of these files may still be referenced in development configurations (like Storybook config files outside the src folder). Those references should be updated or removed as appropriate.