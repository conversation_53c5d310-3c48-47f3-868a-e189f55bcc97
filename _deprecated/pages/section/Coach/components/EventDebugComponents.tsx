import React from 'react';
import { IonIcon, IonBadge } from '@ionic/react';
import { 
  chevronDownOutline, 
  chevronForwardOutline, 
  informationCircleOutline,
  calendarOutline,
  trophyOutline,
  bugOutline
} from 'ionicons/icons';
import './EventDebugComponents.css';

// Debug component for ShadowEventCard
export const ShadowEventCardDebug: React.FC<{
  type?: string;
  title: string;
  date: string;
  time?: string;
  status?: string;
  completionPercentage?: number;
  evaluationsCompleted?: number;
  evaluationsTotal?: number;
  onClick?: () => void;
  className?: string;
  size?: string;
  role?: string;
  fullEventData?: any;
  eventParticipantCount?: number;
  evaluationCompletion?: number;
  preEvaluationPercentage?: number;
  preEvaluationTotal?: number;
  preEvaluationCompleted?: number;
  [key: string]: any;
}> = ({ 
  type,
  title,
  date,
  time,
  status,
  completionPercentage,
  evaluationsCompleted,
  evaluationsTotal,
  onClick,
  className,
  size,
  role,
  fullEventData,
  eventParticipantCount,
  evaluationCompletion,
  preEvaluationPercentage,
  preEvaluationTotal,
  preEvaluationCompleted,
  ...otherProps 
}) => {
  const [expanded, setExpanded] = React.useState(false);
  
  // Count of debug instances to position them correctly
  const debugInstanceId = React.useId();
  
  return (
    <div className="debug-component bottom-right" style={{
      backgroundColor: '#111827',
      color: 'white',
      padding: '16px',
      borderRadius: '8px',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
      maxWidth: '400px',
      maxHeight: '80vh',
      overflowY: 'auto'
    }}>
      <div 
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          cursor: 'pointer',
          marginBottom: '8px'
        }}
        onClick={() => setExpanded(!expanded)}
      >
        <h3 style={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: '8px', margin: 0 }}>
          <IonIcon icon={informationCircleOutline} style={{ fontSize: '16px' }} />
          ShadowEventCard Debug
        </h3>
        <IonIcon icon={expanded ? chevronDownOutline : chevronForwardOutline} style={{ fontSize: '16px' }} />
      </div>
      
      {expanded && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', fontSize: '14px' }}>
          <div>
            <h4 style={{ fontWeight: '600', color: '#34d399', marginBottom: '8px' }}>Component Props:</h4>
            <pre style={{
              fontSize: '12px',
              backgroundColor: '#1f2937',
              padding: '8px',
              borderRadius: '4px',
              overflowX: 'auto',
              margin: 0
            }}>
{JSON.stringify({
  type,
  title,
  date,
  time,
  status,
  completionPercentage,
  evaluationsCompleted,
  evaluationsTotal,
  size,
  role,
  onClick: onClick ? '[Function]' : undefined,
  className
}, null, 2)}
            </pre>
          </div>
          
          {(preEvaluationPercentage !== undefined || preEvaluationTotal !== undefined || preEvaluationCompleted !== undefined) && (
            <div>
              <h4 style={{ fontWeight: '600', color: '#60a5fa', marginBottom: '8px' }}>Pre-Evaluation Data:</h4>
              <pre style={{
                fontSize: '12px',
                backgroundColor: '#1f2937',
                padding: '8px',
                borderRadius: '4px',
                overflowX: 'auto',
                margin: 0
              }}>
{JSON.stringify({
  preEvaluationPercentage,
  preEvaluationTotal,
  preEvaluationCompleted,
  status: preEvaluationTotal > 0 ? 
    (preEvaluationPercentage === 100 ? 'All Complete' : 
     preEvaluationPercentage > 0 ? 'In Progress' : 'Not Started') : 
    'No Pre-Evaluations'
}, null, 2)}
              </pre>
            </div>
          )}
          
          {fullEventData && (
            <div>
              <h4 style={{ fontWeight: '600', color: '#f59e0b', marginBottom: '8px' }}>Full Event Data:</h4>
              <pre style={{
                fontSize: '12px',
                backgroundColor: '#1f2937',
                padding: '8px',
                borderRadius: '4px',
                overflowX: 'auto',
                margin: 0
              }}>
{JSON.stringify(fullEventData, null, 2)}
              </pre>
            </div>
          )}
          
          <div>
            <h4 style={{ fontWeight: '600', color: '#34d399', marginBottom: '8px' }}>Other Props:</h4>
            <pre style={{
              fontSize: '12px',
              backgroundColor: '#1f2937',
              padding: '8px',
              borderRadius: '4px',
              overflowX: 'auto',
              margin: 0
            }}>
              {JSON.stringify(otherProps, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

// Debug component for EnhancedEventCard
export const EnhancedEventCardDebug: React.FC<{
  id: string;
  name: string;
  eventType: string;
  date: string;
  location?: string;
  participantCount: number;
  confirmedCount: number;
  clubId?: string;
  teamId?: string;
  status?: string;
  isPreSessionEvaluation?: boolean;
  [key: string]: any;
}> = ({ 
  id,
  name,
  eventType,
  date,
  location,
  participantCount,
  confirmedCount,
  clubId,
  teamId,
  status,
  isPreSessionEvaluation,
  ...otherProps
}) => {
  const [expanded, setExpanded] = React.useState(false);
  
  return (
    <div style={{
      position: 'fixed',
      bottom: '16px',
      left: '16px',
      zIndex: 50,
      backgroundColor: '#111827',
      color: 'white',
      padding: '16px',
      borderRadius: '8px',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
      maxWidth: '500px',
      maxHeight: '80vh',
      overflowY: 'auto'
    }}>
      <div 
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          cursor: 'pointer',
          marginBottom: '8px'
        }}
        onClick={() => setExpanded(!expanded)}
      >
        <h3 style={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: '8px', margin: 0 }}>
          <IonIcon icon={calendarOutline} style={{ fontSize: '16px' }} />
          EnhancedEventCard Debug
        </h3>
        <IonIcon icon={expanded ? chevronDownOutline : chevronForwardOutline} style={{ fontSize: '16px' }} />
      </div>
      
      {expanded && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', fontSize: '14px' }}>
          <div>
            <h4 style={{ fontWeight: '600', color: '#34d399', marginBottom: '8px' }}>Event Data:</h4>
            <pre style={{
              fontSize: '12px',
              backgroundColor: '#1f2937',
              padding: '8px',
              borderRadius: '4px',
              overflowX: 'auto',
              margin: 0
            }}>
{JSON.stringify({
  id,
  name,
  eventType,
  date,
  location,
  participantCount,
  confirmedCount,
  clubId,
  teamId,
  status,
  isPreSessionEvaluation
}, null, 2)}
            </pre>
          </div>
          
          <div>
            <h4 style={{ fontWeight: '600', color: '#34d399', marginBottom: '8px' }}>Other Props:</h4>
            <pre style={{
              fontSize: '12px',
              backgroundColor: '#1f2937',
              padding: '8px',
              borderRadius: '4px',
              overflowX: 'auto',
              margin: 0
            }}>
              {JSON.stringify(otherProps, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

// Debug component for TeamFlat page showing all event data
export const TeamFlatEventDebug: React.FC<{
  team: any;
  upcomingEvents: any[];
  pastEvents: any[];
  todayEvents: any[];
  futureEvents: any[];
  eventParticipants: {[key: string]: number};
  evaluationCompletions: {[key: string]: number};
  eventsLoading: boolean;
  [key: string]: any;
}> = ({ 
  team,
  upcomingEvents,
  pastEvents,
  todayEvents,
  futureEvents,
  eventParticipants,
  evaluationCompletions,
  eventsLoading,
  ...otherProps
}) => {
  const [expanded, setExpanded] = React.useState(false);
  const [activeTab, setActiveTab] = React.useState('upcoming');
  
  const tabs = ['upcoming', 'today', 'future', 'past', 'participants', 'completions'];
  
  return (
    <div style={{
      position: 'fixed',
      top: '16px',
      right: '16px',
      zIndex: 50,
      backgroundColor: '#111827',
      color: 'white',
      padding: '16px',
      borderRadius: '8px',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
      maxWidth: '800px',
      maxHeight: '90vh',
      overflowY: 'auto'
    }}>
      <div 
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          cursor: 'pointer',
          marginBottom: '8px'
        }}
        onClick={() => setExpanded(!expanded)}
      >
        <h3 style={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: '8px', margin: 0 }}>
          <IonIcon icon={trophyOutline} style={{ fontSize: '16px' }} />
          TeamFlat Events Debug
        </h3>
        <IonIcon icon={expanded ? chevronDownOutline : chevronForwardOutline} style={{ fontSize: '16px' }} />
      </div>
      
      {expanded && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '16px', fontSize: '14px' }}>
          <div style={{
            display: 'flex',
            gap: '8px',
            borderBottom: '1px solid #374151',
            paddingBottom: '8px',
            flexWrap: 'wrap'
          }}>
            {tabs.map(tab => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                style={{
                  padding: '4px 12px',
                  borderRadius: '4px',
                  border: 'none',
                  backgroundColor: activeTab === tab ? '#2563eb' : '#374151',
                  color: 'white',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </div>
          
          <div style={{
            backgroundColor: '#1f2937',
            padding: '12px',
            borderRadius: '4px',
            maxHeight: '400px',
            overflowY: 'auto'
          }}>
            {activeTab === 'upcoming' && (
              <div>
                <h4 style={{ fontWeight: '600', color: '#34d399', marginBottom: '8px' }}>
                  Upcoming Events ({upcomingEvents?.length || 0}):
                </h4>
                <pre style={{ fontSize: '12px', margin: 0 }}>{JSON.stringify(upcomingEvents || [], null, 2)}</pre>
              </div>
            )}
            
            {activeTab === 'today' && (
              <div>
                <h4 style={{ fontWeight: '600', color: '#34d399', marginBottom: '8px' }}>
                  Today's Events ({todayEvents?.length || 0}):
                </h4>
                <pre style={{ fontSize: '12px', margin: 0 }}>{JSON.stringify(todayEvents || [], null, 2)}</pre>
              </div>
            )}
            
            {activeTab === 'future' && (
              <div>
                <h4 style={{ fontWeight: '600', color: '#34d399', marginBottom: '8px' }}>
                  Future Events ({futureEvents?.length || 0}):
                </h4>
                <pre style={{ fontSize: '12px', margin: 0 }}>{JSON.stringify(futureEvents || [], null, 2)}</pre>
              </div>
            )}
            
            {activeTab === 'past' && (
              <div>
                <h4 style={{ fontWeight: '600', color: '#34d399', marginBottom: '8px' }}>
                  Past Events ({pastEvents?.length || 0}):
                </h4>
                <pre style={{ fontSize: '12px', margin: 0 }}>{JSON.stringify(pastEvents || [], null, 2)}</pre>
              </div>
            )}
            
            {activeTab === 'participants' && (
              <div>
                <h4 style={{ fontWeight: '600', color: '#34d399', marginBottom: '8px' }}>Event Participants:</h4>
                <pre style={{ fontSize: '12px', margin: 0 }}>{JSON.stringify(eventParticipants || {}, null, 2)}</pre>
              </div>
            )}
            
            {activeTab === 'completions' && (
              <div>
                <h4 style={{ fontWeight: '600', color: '#34d399', marginBottom: '8px' }}>Evaluation Completions:</h4>
                <pre style={{ fontSize: '12px', margin: 0 }}>{JSON.stringify(evaluationCompletions || {}, null, 2)}</pre>
              </div>
            )}
          </div>
          
          <div>
            <h4 style={{ fontWeight: '600', color: '#34d399', marginBottom: '8px' }}>Event Data Structure:</h4>
            <pre style={{
              fontSize: '12px',
              backgroundColor: '#1f2937',
              padding: '8px',
              borderRadius: '4px',
              overflowX: 'auto',
              margin: 0
            }}>
{`interface Event {
  id?: string;
  name: string;
  slug?: string;
  details?: string;
  start_datetime: string;
  end_datetime?: string;
  meet_datetime?: string;
  location_name?: string;
  location_address?: string;
  hosted_by: string;
  host_contact?: string;
  team_id?: string;
  club_id?: string;
  event_type?: 'training' | 'match' | 'assessment';
  status: 'draft' | 'published' | 'cancelled' | 'completed';
  created_by?: string;
  created_at?: string;
  updated_at?: string;
  max_participants?: number;
  current_participants?: number;
  photos?: string[];
  tags?: string[];
  deleted_at?: string;
  is_pre_session_evaluation?: boolean;
  sport_framework?: string;
}`}
            </pre>
          </div>
          
          <div>
            <h4 style={{ fontWeight: '600', color: '#34d399', marginBottom: '8px' }}>Loading State:</h4>
            <p style={{ margin: 0 }}>Events Loading: {eventsLoading ? 'Yes' : 'No'}</p>
          </div>
        </div>
      )}
    </div>
  );
};
