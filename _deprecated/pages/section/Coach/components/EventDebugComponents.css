/* Debug Component Styles */
.debug-component {
  position: fixed;
  background-color: #111827;
  color: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  z-index: 9999;
  font-family: monospace;
}

.debug-component.bottom-right {
  bottom: 16px;
  right: 16px;
  max-width: 400px;
  max-height: 80vh;
}

.debug-component.bottom-left {
  bottom: 16px;
  left: 16px;
  max-width: 500px;
  max-height: 80vh;
}

.debug-component.top-right {
  top: 16px;
  right: 16px;
  max-width: 800px;
  max-height: 90vh;
}

.debug-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  margin-bottom: 8px;
}

.debug-title {
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

.debug-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  font-size: 14px;
}

.debug-section h4 {
  font-weight: 600;
  color: #34d399;
  margin-bottom: 8px;
}

.debug-code {
  font-size: 12px;
  background-color: #1f2937;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.debug-tabs {
  display: flex;
  gap: 8px;
  border-bottom: 1px solid #374151;
  padding-bottom: 8px;
  flex-wrap: wrap;
}

.debug-tab {
  padding: 4px 12px;
  border-radius: 4px;
  border: none;
  background-color: #374151;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.debug-tab:hover {
  background-color: #4b5563;
}

.debug-tab.active {
  background-color: #2563eb;
}

.debug-tab-content {
  background-color: #1f2937;
  padding: 12px;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
}

/* Add subtle animation when debug panels appear */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.debug-component {
  animation: slideIn 0.2s ease-out;
}

/* Custom scrollbar for debug panels */
.debug-component::-webkit-scrollbar,
.debug-tab-content::-webkit-scrollbar {
  width: 8px;
}

.debug-component::-webkit-scrollbar-track,
.debug-tab-content::-webkit-scrollbar-track {
  background: #1f2937;
  border-radius: 4px;
}

.debug-component::-webkit-scrollbar-thumb,
.debug-tab-content::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

.debug-component::-webkit-scrollbar-thumb:hover,
.debug-tab-content::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Debug toggle button in header */
.debug-toggle-button {
  --background: transparent;
  --background-hover: rgba(156, 163, 175, 0.1);
  --background-activated: rgba(156, 163, 175, 0.2);
  --padding-start: 8px;
  --padding-end: 8px;
}

.debug-toggle-button.active {
  --color: #34d399;
}

/* Debug info badges */
.debug-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
}

.debug-badge.success {
  background-color: rgba(52, 211, 153, 0.2);
  color: #34d399;
}

.debug-badge.warning {
  background-color: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.debug-badge.error {
  background-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}
