import React from 'react';
import { IonButton, IonIcon } from '@ionic/react';
import { closeOutline, informationCircleOutline } from 'ionicons/icons';

interface DebugModeGuideProps {
  isOpen: boolean;
  onClose: () => void;
}

export const DebugModeGuide: React.FC<DebugModeGuideProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      backgroundColor: '#1f2937',
      color: 'white',
      padding: '24px',
      borderRadius: '12px',
      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.3)',
      maxWidth: '600px',
      maxHeight: '80vh',
      overflowY: 'auto',
      zIndex: 10000,
      border: '2px solid #34d399'
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '16px'
      }}>
        <h2 style={{ 
          margin: 0, 
          display: 'flex', 
          alignItems: 'center', 
          gap: '8px',
          color: '#34d399' 
        }}>
          <IonIcon icon={informationCircleOutline} />
          Debug Mode Guide
        </h2>
        <IonButton
          fill="clear"
          size="small"
          onClick={onClose}
          style={{ '--color': '#9ca3af' }}
        >
          <IonIcon icon={closeOutline} slot="icon-only" />
        </IonButton>
      </div>

      <div style={{ fontSize: '14px', lineHeight: '1.6' }}>
        <p>Debug mode is now active! This shows you all the data available to event components on this page.</p>
        
        <h3 style={{ color: '#34d399', marginTop: '16px' }}>What you'll see:</h3>
        <ul style={{ paddingLeft: '20px' }}>
          <li><strong>ShadowEventCard Debug</strong> - Shows props for each event card in the Next/Past events sections</li>
          <li><strong>TeamFlat Events Debug</strong> - Shows all event arrays and data available on the page</li>
        </ul>

        <h3 style={{ color: '#34d399', marginTop: '16px' }}>Debug Panel Features:</h3>
        <ul style={{ paddingLeft: '20px' }}>
          <li>Click panel headers to expand/collapse</li>
          <li>View complete event objects with all properties</li>
          <li>See participant counts and evaluation completions</li>
          <li>Inspect how events are filtered (today/future/past)</li>
        </ul>

        <h3 style={{ color: '#34d399', marginTop: '16px' }}>Panel Locations:</h3>
        <ul style={{ paddingLeft: '20px' }}>
          <li><strong>Bottom Right</strong> - Individual ShadowEventCard debug</li>
          <li><strong>Top Right</strong> - TeamFlat comprehensive event data</li>
        </ul>

        <p style={{ marginTop: '16px', color: '#9ca3af' }}>
          Toggle debug mode off/on using the gear icon in the header.
        </p>
      </div>
    </div>
  );
};
