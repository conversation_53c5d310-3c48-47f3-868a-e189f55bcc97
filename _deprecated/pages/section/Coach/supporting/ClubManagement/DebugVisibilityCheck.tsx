import React, { useEffect } from 'react';
import {
  IonContent,
  IonFab,
  IonFabButton,
  IonIcon,
  IonCard,
  IonCardContent
} from '@ionic/react';
import { bug as bugIcon } from 'ionicons/icons';
import PageWithNavigation from '../../../../../components/PageWithNavigation';
import { WithInlineDebug } from '../../../../../components/Debug/DebugTools';
import { clubCreationDebugConfig } from './debug/club/clubCreationDebug';

const DebugVisibilityCheck: React.FC = () => {
  useEffect(() => {
    console.log('DebugVisibilityCheck mounted');
    console.log('NODE_ENV:', process.env.NODE_ENV);
    console.log('Debug config exists:', !!clubCreationDebugConfig);
  }, []);

  return (
    <>
      <h1>Debug Visibility Check</h1>
      <p>Direct FAB Test</p>
      
      {/* Direct FAB button - should always show */}
      <IonFab vertical="bottom" horizontal="start" slot="fixed">
        <IonFabButton color="warning" onClick={() => alert('Direct FAB clicked!')}>
          <IonIcon icon={bugIcon} />
        </IonFabButton>
      </IonFab>
    </>
  );
};

const DebugVisibilityCheckWithWrapper: React.FC = () => {
  return (
    <PageWithNavigation
      showBackButton={true}
      backUrl="/coach/clubs"
      title="Debug Check"
    >
      <IonContent>
        <IonCard>
          <IonCardContent>
            <h2>Debug Button Visibility Test</h2>
            <p>You should see a yellow bug FAB button at bottom left.</p>
          </IonCardContent>
        </IonCard>
        
        <IonCard>
          <IonCardContent>
            <WithInlineDebug debugConfig={clubCreationDebugConfig}>
              <h3>Content wrapped with WithInlineDebug</h3>
              <p>The debug button should appear below.</p>
            </WithInlineDebug>
          </IonCardContent>
        </IonCard>
        
        <DebugVisibilityCheck />
      </IonContent>
    </PageWithNavigation>
  );
};

export default DebugVisibilityCheckWithWrapper;
