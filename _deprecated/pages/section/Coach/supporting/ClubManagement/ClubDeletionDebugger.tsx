import React, { useState, useEffect } from 'react';
import { IonCard, IonCardHeader, IonCardContent, IonCardTitle, IonButton, 
  IonGrid, IonRow, IonCol, IonList, IonItem, IonLabel, IonIcon, IonBadge, 
  IonLoading, IonAlert, IonText, IonAccordion, IonAccordionGroup, IonNote } from '@ionic/react';
import { 
  alertCircleOutline, checkmarkCircleOutline, warningOutline, 
  timeOutline, informationCircleOutline, codeOutline, trashOutline, 
  refreshOutline, arrowUndoOutline, documentTextOutline, extensionPuzzleOutline 
} from 'ionicons/icons';
import { useParams } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
// import { EnhancedClubDeletionService } from '../../../../../services/EnhancedClubDeletionService'; // Moved to outbox
import { ClubService } from '../../../../../services/ClubService';

// Interfaces for TypeScript
interface DeletionLog {
  log_id: string;
  operation: string;
  entity_id: string;
  entity_type: string;
  status: string;
  message: string | null;
  created_at: string;
}

interface RelatedRecord {
  tableName: string;
  count: number;
  dependentTables?: string[];
  status?: 'pending' | 'success' | 'error';
  errorMessage?: string;
}

const ClubDeletionDebugger: React.FC = () => {
  const { clubId } = useParams<{ clubId: string }>();
  
  // State variables
  const [clubDetails, setClubDetails] = useState<any>(null);
  const [relatedRecords, setRelatedRecords] = useState<RelatedRecord[]>([]);
  const [deletionLogs, setDeletionLogs] = useState<DeletionLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [deleteAttempted, setDeleteAttempted] = useState(false);
  const [deleteResult, setDeleteResult] = useState<any>(null);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [constraintInfo, setConstraintInfo] = useState<string | null>(null);
  
  // Fetch club details and related data
  useEffect(() => {
    if (clubId) {
      loadClubData();
    }
  }, [clubId]);
  
  const loadClubData = async () => {
    setLoading(true);
    try {
      // Fetch club details
      const { data: club, error: clubError } = await supabase
        .from('clubs')
        .select('*')
        .eq('club_id', clubId)
        .single();
      
      if (clubError) {
        console.error('Error fetching club details:', clubError);
        setAlertMessage(`Error fetching club details: ${clubError.message}`);
        setShowAlert(true);
      } else {
        setClubDetails(club);
      }
      
      // Fetch related record counts
      // TODO: Replace with ClubService equivalent
      // const counts = await EnhancedClubDeletionService.getRelatedRecordCounts(clubId);
      const counts = { teams: 0, players: 0, events: 0, evaluations: 0 }; // Placeholder
      
      // Format related records for display
      const records: RelatedRecord[] = [
        { tableName: 'Teams', count: counts.teams || 0, dependentTables: ['team_members', 'team_coaches', 'team_events', 'evaluations'] },
        { tableName: 'Team Members', count: counts.team_members || 0 },
        { tableName: 'Team Coaches', count: counts.team_coaches || 0 },
        { tableName: 'Team Events', count: counts.team_events || 0 },
        { tableName: 'Evaluations', count: counts.evaluations || 0 },
        { tableName: 'Club Administrators', count: counts.club_administrators || 0 },
        { tableName: 'Club Coaches', count: counts.club_coaches || 0 },
        { tableName: 'Club Events', count: counts.club_events || 0 }
      ];
      
      setRelatedRecords(records);
      
      // Check for constraint issues
      // TODO: Replace with ClubService equivalent
      // const constraints = await EnhancedClubDeletionService.checkConstraintIssues(clubId);
      const constraints: any[] = []; // Placeholder
      if (constraints) {
        setConstraintInfo(constraints);
      }
      
      // Check for existing deletion logs
      await loadDeletionLogs();
      
    } catch (error) {
      console.error('Error loading club data:', error);
      setAlertMessage(`Unexpected error loading club data: ${error instanceof Error ? error.message : String(error)}`);
      setShowAlert(true);
    } finally {
      setLoading(false);
    }
  };
  
  const loadDeletionLogs = async () => {
    try {
      // TODO: Replace with ClubService equivalent
      // const logs = await EnhancedClubDeletionService.getDeletionLogs(clubId);
      const logs: any[] = []; // Placeholder
      setDeletionLogs(logs);
      
      // Check if a deletion has been attempted
      const deleteOperations = logs.filter(log => 
        log.operation === 'delete_club_cascade' && 
        (log.status === 'completed' || log.status === 'error')
      );
      
      setDeleteAttempted(deleteOperations.length > 0);
      
    } catch (error) {
      console.error('Error loading deletion logs:', error);
    }
  };
  
  const handleDeleteClub = async () => {
    setLoading(true);
    try {
      // TODO: Replace with ClubService equivalent
      // const result = await EnhancedClubDeletionService.deleteClub(clubId);
      console.log('TODO: Delete club', clubId);
      const result = { success: false, message: 'Not implemented yet' }; // Placeholder
      setDeleteResult(result);
      
      // Reload logs to show the new deletion attempt
      await loadDeletionLogs();
      
      if (result.success) {
        setAlertMessage(`Club deletion succeeded! ${result.message}`);
      } else {
        setAlertMessage(`Club deletion failed: ${result.message}`);
      }
      setShowAlert(true);
      
    } catch (error) {
      console.error('Error deleting club:', error);
      setAlertMessage(`Unexpected error during deletion: ${error instanceof Error ? error.message : String(error)}`);
      setShowAlert(true);
    } finally {
      setLoading(false);
    }
  };
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'started':
        return timeOutline;
      case 'processing':
        return refreshOutline;
      case 'completed':
        return checkmarkCircleOutline;
      case 'error':
        return alertCircleOutline;
      case 'constraint_issue':
        return warningOutline;
      default:
        return informationCircleOutline;
    }
  };
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'started':
        return 'warning';
      case 'processing':
        return 'warning';
      case 'completed':
        return 'success';
      case 'error':
        return 'danger';
      case 'constraint_issue':
        return 'warning';
      default:
        return 'medium';
    }
  };
  
  return (
    <div className="ion-padding">
      <IonLoading isOpen={loading} message="Processing..." />
      
      <IonAlert
        isOpen={showAlert}
        onDidDismiss={() => setShowAlert(false)}
        header="Club Deletion Debug"
        message={alertMessage}
        buttons={['OK']}
      />
      
      <h1 className="shot-h1">Club Deletion Debugger</h1>
      <IonText color="medium">
        <p>This tool helps diagnose issues with club deletion. Club ID: {clubId}</p>
      </IonText>
      
      {/* Club Details Card */}
      {clubDetails && (
        <IonCard className="shot-card">
          <IonCardHeader className="shot-card-header">
            <IonCardTitle className="shot-card-title">
              Club Details: {clubDetails.club_name}
            </IonCardTitle>
          </IonCardHeader>
          <IonCardContent className="shot-card-content">
            <IonGrid>
              <IonRow>
                <IonCol size="6">
                  <IonText>
                    <strong>Club ID:</strong> {clubDetails.club_id}
                  </IonText>
                </IonCol>
                <IonCol size="6">
                  <IonText>
                    <strong>Created:</strong> {new Date(clubDetails.created_at).toLocaleString()}
                  </IonText>
                </IonCol>
              </IonRow>
            </IonGrid>
            
            {/* Constraint Issues Warning */}
            {constraintInfo && (
              <div className="ion-margin-top">
                <IonText color="warning">
                  <h3 className="shot-h3">
                    <IonIcon icon={warningOutline} /> Potential Deletion Constraints Detected
                  </h3>
                  <p className="shot-caption">{constraintInfo}</p>
                </IonText>
              </div>
            )}
            
            {/* Action Buttons */}
            <div className="ion-margin-top ion-text-end">
              <IonButton 
                fill="outline" 
                color="medium" 
                onClick={loadClubData}
              >
                <IonIcon slot="start" icon={refreshOutline} />
                Refresh Data
              </IonButton>
              <IonButton 
                color="danger" 
                onClick={handleDeleteClub}
                disabled={loading}
              >
                <IonIcon slot="start" icon={trashOutline} />
                Attempt Deletion
              </IonButton>
            </div>
          </IonCardContent>
        </IonCard>
      )}
      
      {/* Related Records Card */}
      <IonCard className="shot-card">
        <IonCardHeader className="shot-card-header">
          <IonCardTitle className="shot-card-title">
            <IonIcon icon={extensionPuzzleOutline} className="ion-margin-end" />
            Related Records
          </IonCardTitle>
        </IonCardHeader>
        <IonCardContent className="shot-card-content">
          <IonList lines="full">
            {relatedRecords.map((record) => (
              <IonItem key={record.tableName} className="bg-black text-white">
                <IonLabel>
                  {record.tableName}
                  {record.dependentTables && record.dependentTables.length > 0 && (
                    <p className="shot-caption">
                      Has dependent tables: {record.dependentTables.join(', ')}
                    </p>
                  )}
                </IonLabel>
                <IonBadge slot="end" color={record.count > 0 ? "warning" : "success"}>
                  {record.count}
                </IonBadge>
              </IonItem>
            ))}
          </IonList>
          
          <div className="ion-text-center ion-margin-top">
            <IonText color="medium">
              <p className="shot-caption">
                Records must be deleted in the correct order to avoid foreign key constraint violations.
                The SQL function attempts to handle this automatically.
              </p>
            </IonText>
          </div>
        </IonCardContent>
      </IonCard>
      
      {/* Deletion Logs Card */}
      <IonCard className="shot-card">
        <IonCardHeader className="shot-card-header">
          <IonCardTitle className="shot-card-title">
            <IonIcon icon={documentTextOutline} className="ion-margin-end" />
            Deletion Logs
          </IonCardTitle>
        </IonCardHeader>
        <IonCardContent className="shot-card-content">
          {deletionLogs.length > 0 ? (
            <IonAccordionGroup>
              {deletionLogs.map((log) => (
                <IonAccordion key={log.log_id}>
                  <IonItem slot="header" className="bg-black text-white">
                    <IonIcon 
                      icon={getStatusIcon(log.status)} 
                      color={getStatusColor(log.status)} 
                      slot="start"
                    />
                    <IonLabel>
                      <h3>{log.operation}</h3>
                      <p>{new Date(log.created_at).toLocaleString()}</p>
                    </IonLabel>
                    <IonBadge color={getStatusColor(log.status)} slot="end">
                      {log.status}
                    </IonBadge>
                  </IonItem>
                  <div slot="content" className="ion-padding bg-black text-white">
                    <IonGrid>
                      <IonRow>
                        <IonCol size="12">
                          <IonText>
                            <strong>Entity Type:</strong> {log.entity_type}
                          </IonText>
                        </IonCol>
                        <IonCol size="12">
                          <IonText>
                            <strong>Entity ID:</strong> {log.entity_id}
                          </IonText>
                        </IonCol>
                        <IonCol size="12">
                          <IonText>
                            <strong>Message:</strong>
                          </IonText>
                          <IonNote className="ion-margin-top ion-padding">
                            {log.message || 'No message provided'}
                          </IonNote>
                        </IonCol>
                      </IonRow>
                    </IonGrid>
                  </div>
                </IonAccordion>
              ))}
            </IonAccordionGroup>
          ) : (
            <div className="ion-text-center ion-padding">
              <IonText color="medium">
                <p>No deletion logs found for this club.</p>
                <p className="shot-caption">
                  Logs are generated when deletion operations are performed.
                </p>
              </IonText>
            </div>
          )}
        </IonCardContent>
      </IonCard>
      
      {/* Deletion Result Card (when available) */}
      {deleteResult && (
        <IonCard className="shot-card">
          <IonCardHeader className={`shot-card-header ${deleteResult.success ? 'bg-success' : 'bg-danger'}`}>
            <IonCardTitle className="shot-card-title">
              <IonIcon 
                icon={deleteResult.success ? checkmarkCircleOutline : alertCircleOutline} 
                className="ion-margin-end" 
              />
              Deletion Result
            </IonCardTitle>
          </IonCardHeader>
          <IonCardContent className="shot-card-content">
            <IonText>
              <p><strong>Status:</strong> {deleteResult.success ? 'Success' : 'Failed'}</p>
              <p><strong>Message:</strong> {deleteResult.message}</p>
              
              {deleteResult.operationId && (
                <p><strong>Operation ID:</strong> {deleteResult.operationId}</p>
              )}
              
              {deleteResult.counts && (
                <div className="ion-margin-top">
                  <h3 className="shot-h3">Related Record Counts</h3>
                  <pre className="ion-padding ion-margin-top bg-black text-white">
                    {JSON.stringify(deleteResult.counts, null, 2)}
                  </pre>
                </div>
              )}
              
              {deleteResult.error && (
                <div className="ion-margin-top">
                  <h3 className="shot-h3">Error Details</h3>
                  <pre className="ion-padding ion-margin-top bg-black text-white">
                    {JSON.stringify(deleteResult.error, null, 2)}
                  </pre>
                </div>
              )}
            </IonText>
            
            <div className="ion-text-center ion-margin-top">
              <IonButton 
                fill="outline" 
                color="medium" 
                onClick={loadClubData}
              >
                <IonIcon slot="start" icon={refreshOutline} />
                Refresh Data
              </IonButton>
            </div>
          </IonCardContent>
        </IonCard>
      )}
      
      {/* Debug Query Card */}
      <IonCard className="shot-card">
        <IonCardHeader className="shot-card-header">
          <IonCardTitle className="shot-card-title">
            <IonIcon icon={codeOutline} className="ion-margin-end" />
            SQL Debugging Queries
          </IonCardTitle>
        </IonCardHeader>
        <IonCardContent className="shot-card-content">
          <IonText>
            <p>Use these queries in the Supabase SQL Editor to further debug deletion issues:</p>
          </IonText>
          
          <IonAccordionGroup>
            <IonAccordion>
              <IonItem slot="header" className="bg-black text-white">
                <IonLabel>View Deletion Logs</IonLabel>
              </IonItem>
              <div slot="content" className="ion-padding bg-black text-white">
                <pre>
{`SELECT * FROM deletion_logs 
WHERE entity_id = '${clubId}'
ORDER BY created_at DESC;`}
                </pre>
              </div>
            </IonAccordion>
            
            <IonAccordion>
              <IonItem slot="header" className="bg-black text-white">
                <IonLabel>Check Foreign Key Constraints</IonLabel>
              </IonItem>
              <div slot="content" className="ion-padding bg-black text-white">
                <pre>
{`SELECT
  tc.constraint_name,
  tc.table_schema AS referencing_schema,
  tc.table_name AS referencing_table,
  kcu.column_name AS referencing_column,
  ccu.table_schema AS referenced_schema,
  ccu.table_name AS referenced_table,
  ccu.column_name AS referenced_column
FROM information_schema.table_constraints tc
JOIN information_schema.key_column_usage kcu
  ON tc.constraint_name = kcu.constraint_name
  AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage ccu
  ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY'
  AND ccu.table_name = 'clubs';`}
                </pre>
              </div>
            </IonAccordion>
            
            <IonAccordion>
              <IonItem slot="header" className="bg-black text-white">
                <IonLabel>Find References to Club</IonLabel>
              </IonItem>
              <div slot="content" className="ion-padding bg-black text-white">
                <pre>
{`-- Run this for each table that might reference the club
SELECT * FROM teams WHERE club_id = '${clubId}';
SELECT * FROM club_administrators WHERE club_id = '${clubId}';
SELECT * FROM club_coaches WHERE club_id = '${clubId}';
SELECT * FROM events WHERE club_id = '${clubId}';`}
                </pre>
              </div>
            </IonAccordion>
            
            <IonAccordion>
              <IonItem slot="header" className="bg-black text-white">
                <IonLabel>Manual Deletion Script</IonLabel>
              </IonItem>
              <div slot="content" className="ion-padding bg-black text-white">
                <pre>
{`-- This script can be used for manual deletion if the function fails
-- Start a transaction
BEGIN;

-- Get team IDs for this club
DO $$
DECLARE
  v_team_id UUID;
  v_team_cursor CURSOR FOR 
    SELECT team_id FROM teams WHERE club_id = '${clubId}';
BEGIN
  -- First delete all teams and their related records
  OPEN v_team_cursor;
  LOOP
    FETCH v_team_cursor INTO v_team_id;
    EXIT WHEN NOT FOUND;
    
    -- Delete team members
    DELETE FROM team_members WHERE team_id = v_team_id;
    
    -- Delete team coaches
    DELETE FROM team_coaches WHERE team_id = v_team_id;
    
    -- Delete events associated with the team
    DELETE FROM events WHERE team_id = v_team_id;
    
    -- Delete evaluations associated with the team
    DELETE FROM evaluations WHERE team_id = v_team_id;
    
    -- Delete the team itself
    DELETE FROM teams WHERE team_id = v_team_id;
  END LOOP;
  CLOSE v_team_cursor;
  
  -- Delete club administrators
  DELETE FROM club_administrators WHERE club_id = '${clubId}';
  
  -- Delete club coaches
  DELETE FROM club_coaches WHERE club_id = '${clubId}';
  
  -- Delete any remaining club events
  DELETE FROM events WHERE club_id = '${clubId}';
  
  -- Finally, delete the club itself
  DELETE FROM clubs WHERE club_id = '${clubId}';
END $$;

-- Commit the transaction
COMMIT;`}
                </pre>
              </div>
            </IonAccordion>
          </IonAccordionGroup>
        </IonCardContent>
      </IonCard>
    </div>
  );
};

export default ClubDeletionDebugger;
