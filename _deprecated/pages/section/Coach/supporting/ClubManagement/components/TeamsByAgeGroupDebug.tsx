import React from 'react';
import Coach<PERSON>ist from './CoachList/CoachList';
import { IonBadge, IonIcon, IonButton } from '@ionic/react';
import { add as addIcon, football as teamIcon } from 'ionicons/icons';

// Debug style for component borders
const debugStyles = {
  componentContainer: {
    border: '2px dashed red',
    borderRadius: '4px',
    margin: '2px',
    padding: '2px',
    position: 'relative'
  },
  componentLabel: {
    position: 'absolute',
    top: '-10px',
    left: '10px',
    background: 'red',
    color: 'white',
    padding: '0 4px',
    fontSize: '10px',
    fontWeight: 'bold',
    borderRadius: '3px',
    zIndex: 1000
  }
};

// Helper to wrap any component with a debug border
const withDebugBorder = (Component, label) => {
  return (
    <div style={debugStyles.componentContainer}>
      <div style={debugStyles.componentLabel}>{label}</div>
      {Component}
    </div>
  );
};

interface Coach {
  id: string;
  name: string;
  role: string;
  avatarUrl?: string;
  is_primary?: boolean;
}

interface Team {
  id: string;
  name: string;
  ageGroup: string;
  playerCount: number;
  coaches: Coach[];
  logo_url?: string;
  upcoming_events?: number;
  sport_type?: string;
}

interface TeamsByAgeGroupDebugProps {
  teams: Team[];
  onTeamClick: (teamId: string) => void;
  onCreateTeam?: (ageGroup?: string) => void;
  title?: string;
  hideCreateTeam?: boolean;
}

const TeamsByAgeGroupDebug: React.FC<TeamsByAgeGroupDebugProps> = ({ 
  teams, 
  onTeamClick, 
  onCreateTeam,
  title = "Teams by Age Group",
  hideCreateTeam = false
}) => {
  // Group teams by age group
  const groupTeamsByAge = () => {
    const grouped: Record<string, Team[]> = {};
    
    teams.forEach(team => {
      if (!grouped[team.ageGroup]) {
        grouped[team.ageGroup] = [];
      }
      grouped[team.ageGroup].push(team);
    });
    
    // Sort age groups
    return Object.keys(grouped)
      .sort((a, b) => {
        // Extract numbers from age groups (e.g., "U10" -> 10)
        const numA = parseInt(a.replace(/[^\d]/g, ''));
        const numB = parseInt(b.replace(/[^\d]/g, ''));
        return numA - numB;
      })
      .map(ageGroup => ({
        ageGroup,
        teams: grouped[ageGroup].sort((a, b) => a.name.localeCompare(b.name))
      }));
  };
  
  const ageGroups = groupTeamsByAge();

  // Render a single team card with minimal styling
  const renderTeamCard = (team: Team) => {
    // Transform coaches to match CoachList format
    const coachesForDisplay = team.coaches.map(coach => ({
      coach_assignment_id: `coach-${coach.id}`,
      user_name: coach.name,
      user_avatar: coach.avatarUrl,
      role: coach.role,
      is_primary: coach.is_primary || false,
      notes: undefined
    }));
    
    return withDebugBorder(
      <div key={team.id} onClick={() => onTeamClick(team.id)}>
        <div style={{ marginBottom: '10px', backgroundColor: '#374151', padding: '10px', borderRadius: '8px' }}>
          {/* Team header with minimal styling */}
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
            <div>
              <h3 style={{ color: 'white', margin: 0 }}>{team.name}</h3>
              <div style={{ color: '#9ca3af', fontSize: '14px' }}>
                <span>{team.playerCount} players</span>
              </div>
            </div>
          </div>
          
          {/* Coaches Section */}
          {withDebugBorder(
            <div>
              {coachesForDisplay.length > 0 ? (
                <CoachList 
                  coaches={coachesForDisplay} 
                  isTeamLevel={true}
                  maxDisplay={99} // Show all coaches
                />
              ) : (
                <div style={{ color: '#9ca3af', fontSize: '14px' }}>No coaches assigned</div>
              )}
            </div>, 
            'Coaches-Container'
          )}
        </div>
      </div>,
      `Team-${team.id}`
    );
  };
  
  return withDebugBorder(
    <div>
      {ageGroups.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <div style={{ fontSize: '30px', marginBottom: '15px' }}>🏆</div>
          <h3 style={{ color: 'white', marginBottom: '10px' }}>No Teams Created Yet</h3>
          <p style={{ color: '#9ca3af', marginBottom: '20px' }}>
            {hideCreateTeam ? 
              "Create teams from the Club Management section." :
              "Create your first team to start managing players, positions, and matches."
            }
          </p>
          {!hideCreateTeam && onCreateTeam && (
            <button onClick={() => onCreateTeam()}>
              Create First Team
            </button>
          )}
        </div>
      ) : (
        <>
          {ageGroups.map(({ ageGroup, teams }) => (
            withDebugBorder(
              <div key={ageGroup}>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: 'white', marginBottom: '10px', display: 'flex', alignItems: 'center' }}>
                  {ageGroup}
                  <span style={{ marginLeft: '8px', backgroundColor: '#4B5563', color: 'white', padding: '2px 8px', borderRadius: '9999px', fontSize: '12px' }}>
                    {teams.length}
                  </span>
                </div>
                
                <div>
                  {teams.map(team => renderTeamCard(team))}
                </div>
              </div>,
              `AgeGroup-${ageGroup}`
            )
          ))}
          
          {/* Add New Team Card at the bottom - only show if not hidden */}
          {!hideCreateTeam && onCreateTeam && (
            withDebugBorder(
              <div 
                onClick={() => onCreateTeam()}
                style={{ 
                  border: '2px dashed #3d3d3d', 
                  borderRadius: '8px', 
                  padding: '15px', 
                  textAlign: 'center',
                  cursor: 'pointer',
                  marginTop: '15px'
                }}
              >
                <div style={{ color: '#6B21A8', fontSize: '24px', marginBottom: '8px' }}>+</div>
                <span style={{ color: '#E5E7EB' }}>Add New Team</span>
              </div>,
              'Add-Team-Button'
            )
          )}
        </>
      )}
    </div>,
    'TeamsByAgeGroupDebug'
  );
};

export default TeamsByAgeGroupDebug;