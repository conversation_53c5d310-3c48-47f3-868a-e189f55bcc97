// PerformDebugModeDemo.tsx - Demo page showing debug mode for all perform dashboards

import React, { useState } from 'react';
import { PageWithNavigation } from '../../Navigation';
import { RoleSpecificContent } from '../../../../components/v2/RoleSpecificContent';
import { isDebugMode } from '../../../../utils/debugMode';
import { ShadowStatCard } from '@/foundation/design-system/components/molecules/Cards';
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowInfoCard } from '@/foundation/design-system/components/molecules/Cards';
import { ShadowActionGrid } from '@/foundation/design-system/components/molecules/Navigation';

const PerformDebugModeDemo: React.FC = () => {
  const [showAllRoles, setShowAllRoles] = useState(true);
  const debugEnabled = isDebugMode();

  // Mock data
  const mockTeams = [
    { id: '1', name: 'U12 Skylarks', players: 15 },
    { id: '2', name: 'U14 Eagles', players: 18 }
  ];

  return (
    <PageWithNavigation
      showBackButton={true}
      title="PERFORM Hub Debug Mode Demo"
      className="bg-gray-900"
    >
      <div className="p-4 space-y-6">
        {/* Debug Mode Status */}
        <div className="bg-gray-800 rounded-xl p-6">
          <h2 className="text-xl font-bold text-white mb-4">Debug Mode Demo</h2>
          <p className="text-gray-300 mb-4">
            This page demonstrates how role-specific dashboards look with debug mode indicators.
          </p>
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-400">
              Debug Mode: <span className={debugEnabled ? 'text-green-500' : 'text-red-500'}>
                {debugEnabled ? 'ENABLED' : 'DISABLED'}
              </span>
            </span>
            <button
              onClick={() => setShowAllRoles(!showAllRoles)}
              className="px-4 py-2 bg-shot-purple hover:bg-shot-purple/80 rounded-lg text-white text-sm transition-colors"
            >
              {showAllRoles ? 'Show Single Role' : 'Show All Roles'}
            </button>
          </div>
        </div>

        {showAllRoles ? (
          <>
            {/* Coach Dashboard - Orange */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-white px-2">Coach Dashboard</h3>
              <RoleSpecificContent role="coach">
                <div className="bg-gray-800 rounded-xl p-6">
                  <h2 className="text-shot-teal text-xl font-bold mb-4">Team Management</h2>
                  
                  <div className="grid grid-cols-3 gap-4 mb-6">
                    <ShadowStatCard
                      value={mockTeams.length}
                      label="Active Teams"
                      color="teal"
                      role="coach"
                    />
                    <ShadowStatCard
                      value={33}
                      label="Total Players"
                      color="purple"
                      role="coach"
                    />
                    <ShadowStatCard
                      value={156}
                      label="Sessions"
                      color="green"
                      role="coach"
                    />
                  </div>
                  
                  <ShadowActionGrid
                    columns={2}
                    items={[
                      { id: 'create', icon: 'users', label: 'Create Team' },
                      { id: 'evaluate', icon: 'clipboard', label: 'Evaluations' }
                    ]}
                    role="coach"
                  />
                </div>
              </RoleSpecificContent>
            </div>

            {/* Player Dashboard - Blue */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-white px-2">Player Dashboard</h3>
              <RoleSpecificContent role="player">
                <div className="bg-gray-800 rounded-xl p-6">
                  <h2 className="text-shot-teal text-xl font-bold mb-4">My Performance</h2>
                  
                  <div className="grid grid-cols-4 gap-4 mb-6">
                    <ShadowStatCard value={89} label="Goals" color="green" role="player" />
                    <ShadowStatCard value={45} label="Assists" color="teal" role="player" />
                    <ShadowStatCard value="A+" label="Rating" color="purple" role="player" />
                    <ShadowStatCard value={95} label="Fitness %" color="yellow" role="player" />
                  </div>
                  
                  <div className="flex gap-4">
                    <ShadowButton text="View Stats" variant="primary" role="player" />
                    <ShadowButton text="Self-Evaluate" variant="secondary" role="player" />
                  </div>
                </div>
              </RoleSpecificContent>
            </div>

            {/* Parent Dashboard - Green */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-white px-2">Parent Dashboard</h3>
              <RoleSpecificContent role="parent">
                <div className="bg-gray-800 rounded-xl p-6">
                  <h2 className="text-shot-teal text-xl font-bold mb-4">Family Overview</h2>
                  
                  <div className="grid grid-cols-3 gap-4 mb-6">
                    <ShadowStatCard value={2} label="Children" color="green" role="parent" />
                    <ShadowStatCard value={8} label="Events" color="yellow" role="parent" />
                    <ShadowStatCard value="£120" label="Fees Due" color="red" role="parent" />
                  </div>
                  
                  <div className="space-y-3">
                    <ShadowInfoCard
                      variant="action"
                      title="Tommy - U12 Skylarks"
                      description="Next match: Saturday 2PM"
                      icon="chevronRight"
                      role="parent"
                    />
                    <ShadowInfoCard
                      variant="action"
                      title="Sarah - U10 Eagles"
                      description="Training: Thursday 5PM"
                      icon="chevronRight"
                      role="parent"
                    />
                  </div>
                </div>
              </RoleSpecificContent>
            </div>

            {/* Admin Dashboard - Purple */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-white px-2">Admin Dashboard</h3>
              <RoleSpecificContent role="admin">
                <div className="bg-gray-800 rounded-xl p-6">
                  <h2 className="text-shot-teal text-xl font-bold mb-4">Organization Overview</h2>
                  
                  <div className="grid grid-cols-4 gap-4 mb-6">
                    <ShadowStatCard value={12} label="Teams" color="teal" role="admin" />
                    <ShadowStatCard value={156} label="Members" color="purple" role="admin" />
                    <ShadowStatCard value={42} label="Coaches" color="green" role="admin" />
                    <ShadowStatCard value={8} label="Events" color="yellow" role="admin" />
                  </div>
                  
                  <ShadowActionGrid
                    columns={3}
                    items={[
                      { id: 'users', icon: 'users', label: 'Users' },
                      { id: 'teams', icon: 'users', label: 'Teams' },
                      { id: 'reports', icon: 'clipboard', label: 'Reports' }
                    ]}
                    role="admin"
                  />
                </div>
              </RoleSpecificContent>
            </div>
          </>
        ) : (
          /* Single Role View Example */
          <RoleSpecificContent role="coach" className="min-h-[600px]">
            <div className="bg-gray-800 rounded-xl p-6">
              <h2 className="text-shot-teal text-xl font-bold mb-4">Single Role View (Coach)</h2>
              <p className="text-gray-300 mb-6">
                This shows how a single dashboard looks with debug indicators when viewing as a specific role.
              </p>
              
              <div className="grid grid-cols-3 gap-4 mb-6">
                <ShadowStatCard value={5} label="Teams" color="teal" role="coach" />
                <ShadowStatCard value={42} label="Players" color="purple" role="coach" />
                <ShadowStatCard value={156} label="Sessions" color="green" role="coach" />
              </div>
              
              <div className="flex gap-4">
                <ShadowButton text="Create Session" variant="primary" role="coach" />
                <ShadowButton text="View Teams" variant="secondary" role="coach" />
              </div>
            </div>
          </RoleSpecificContent>
        )}

        {/* Visual Guide */}
        <div className="bg-gray-800 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Debug Mode Color Guide</h3>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-2 rounded-lg border-2 border-orange-500 bg-orange-500/20"></div>
              <p className="text-sm text-white">Coach</p>
              <p className="text-xs text-gray-400">Orange</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-2 rounded-lg border-2 border-blue-500 bg-blue-500/20"></div>
              <p className="text-sm text-white">Player</p>
              <p className="text-xs text-gray-400">Blue</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-2 rounded-lg border-2 border-green-500 bg-green-500/20"></div>
              <p className="text-sm text-white">Parent</p>
              <p className="text-xs text-gray-400">Green</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-2 rounded-lg border-2 border-purple-500 bg-purple-500/20"></div>
              <p className="text-sm text-white">Admin</p>
              <p className="text-xs text-gray-400">Purple</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-2 rounded-lg border-2 border-red-500 bg-red-500/20"></div>
              <p className="text-sm text-white">Superadmin</p>
              <p className="text-xs text-gray-400">Red</p>
            </div>
          </div>
        </div>
      </div>
    </PageWithNavigation>
  );
};

export default PerformDebugModeDemo;
