// ABOUTME: Showcase page demonstrating the 3 variations of team selector components
// Displays list, grid, and compact variations with different configurations

import React, { useState } from 'react';
import { ShadowTeamSelector, Team } from '../../foundation/design-system/components/organisms/Teams/ShadowTeamSelector';
import { ShadowButton } from '../../foundation/design-system/components/atoms/Button/ShadowButton';

const TeamSelectorShowcase: React.FC = () => {
  const [selectedTeamId, setSelectedTeamId] = useState<string>('');
  const [selectedVariant, setSelectedVariant] = useState<'list' | 'grid' | 'compact'>('list');

  // Mock team data with various configurations
  const mockTeams: Team[] = [
    {
      id: 'team-1',
      name: 'Manchester United U18',
      sportType: 'football',
      memberCount: 24,
      description: 'Elite youth football team competing in Premier League South'
    },
    {
      id: 'team-2', 
      name: 'Lakers Academy',
      sportType: 'basketball',
      memberCount: 16,
      description: 'Development squad for talented basketball players ages 14-18'
    },
    {
      id: 'team-3',
      name: 'City Tennis Club Juniors',
      sportType: 'tennis',
      memberCount: 32,
      description: 'Junior tennis program with focus on technique and competition'
    },
    {
      id: 'team-4',
      name: 'Regional Boxing Squad',
      sportType: 'boxing',
      memberCount: 12,
      description: 'Competitive boxing team for regional and national tournaments'
    },
    {
      id: 'team-5',
      name: 'County Cricket XI',
      sportType: 'cricket',
      memberCount: 18,
      description: 'Representative cricket team for county-level competitions'
    },
    {
      id: 'team-6',
      name: 'Rugby Development XV',
      sportType: 'rugby',
      memberCount: 30,
      description: 'Pathway program for aspiring professional rugby players'
    }
  ];

  const handleTeamClick = (teamId: string) => {
    setSelectedTeamId(teamId);
    console.log('Team selected:', teamId);
  };

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <div className="bg-gray-900 border-b border-gray-800 px-6 py-4">
        <h1 className="text-2xl font-bold">Team Selector Showcase</h1>
        <p className="text-gray-400 mt-1">Three variations of team selection components</p>
      </div>

      {/* Controls */}
      <div className="p-6 bg-gray-900/50 border-b border-gray-800">
        <div className="max-w-6xl mx-auto">
          <h3 className="text-sm font-medium text-gray-400 mb-3">Select Variant</h3>
          <div className="flex gap-3 flex-wrap">
            <ShadowButton
              label="List View"
              variant={selectedVariant === 'list' ? 'primary' : 'secondary'}
              onClick={() => setSelectedVariant('list')}
              size="small"
            />
            <ShadowButton
              label="Grid View"
              variant={selectedVariant === 'grid' ? 'primary' : 'secondary'}
              onClick={() => setSelectedVariant('grid')}
              size="small"
            />
            <ShadowButton
              label="Compact View"
              variant={selectedVariant === 'compact' ? 'primary' : 'secondary'}
              onClick={() => setSelectedVariant('compact')}
              size="small"
            />
          </div>
          {selectedTeamId && (
            <div className="mt-4 p-3 bg-teal-900/20 border border-teal-700/50 rounded-lg">
              <p className="text-sm text-teal-400">
                Selected Team ID: <span className="font-mono font-medium">{selectedTeamId}</span>
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Main Content Area */}
      <div className="p-6">
        <div className="max-w-7xl mx-auto space-y-12">
          {/* Current Selected Variant */}
          <section>
            <h2 className="text-xl font-semibold mb-6 text-gray-300">
              {selectedVariant === 'list' && 'List Variant - Classic Card Style'}
              {selectedVariant === 'grid' && 'Grid Variant - Visual Card Layout'}
              {selectedVariant === 'compact' && 'Compact Variant - Space-Efficient Design'}
            </h2>
            <div className="bg-gray-900/30 rounded-xl">
              <ShadowTeamSelector
                teams={mockTeams}
                onTeamClick={handleTeamClick}
                variant={selectedVariant}
                showMemberCount={true}
                showDescription={selectedVariant !== 'compact'}
                selectedTeamId={selectedTeamId}
                headerText="Select Your Team"
              />
            </div>
          </section>

          {/* Configuration Examples */}
          <section className="space-y-8">
            <h2 className="text-xl font-semibold text-gray-300">Configuration Examples</h2>
            
            {/* Minimal Configuration */}
            <div>
              <h3 className="text-lg font-medium mb-3 text-gray-400">Minimal Configuration</h3>
              <div className="bg-gray-900/30 rounded-xl">
                <ShadowTeamSelector
                  teams={mockTeams.slice(0, 3)}
                  onTeamClick={handleTeamClick}
                  variant="list"
                  showHeader={false}
                />
              </div>
            </div>

            {/* With Loading State */}
            <div>
              <h3 className="text-lg font-medium mb-3 text-gray-400">Loading State</h3>
              <div className="bg-gray-900/30 rounded-xl">
                <ShadowTeamSelector
                  teams={[]}
                  loading={true}
                  onTeamClick={handleTeamClick}
                  variant="list"
                />
              </div>
            </div>

            {/* Empty State */}
            <div>
              <h3 className="text-lg font-medium mb-3 text-gray-400">Empty State</h3>
              <div className="bg-gray-900/30 rounded-xl">
                <ShadowTeamSelector
                  teams={[]}
                  onTeamClick={handleTeamClick}
                  variant={selectedVariant}
                  headerText="Available Teams"
                />
              </div>
            </div>
          </section>

          {/* Code Examples */}
          <section className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-300">Code Examples</h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-400 mb-2">Basic Usage</h3>
                <pre className="bg-gray-900 rounded-lg p-4 text-sm overflow-x-auto">
                  <code className="text-gray-300">{`<ShadowTeamSelector
  teams={teams}
  onTeamClick={(teamId) => console.log(teamId)}
  variant="list"
/>`}</code>
                </pre>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-400 mb-2">Full Featured</h3>
                <pre className="bg-gray-900 rounded-lg p-4 text-sm overflow-x-auto">
                  <code className="text-gray-300">{`<ShadowTeamSelector
  teams={teams}
  loading={loading}
  onTeamClick={handleTeamClick}
  variant="grid"
  showHeader={true}
  headerText="Choose Your Team"
  showMemberCount={true}
  showDescription={true}
  selectedTeamId={selectedId}
/>`}</code>
                </pre>
              </div>
            </div>
          </section>

          {/* Props Documentation */}
          <section>
            <h2 className="text-xl font-semibold mb-4 text-gray-300">Props Documentation</h2>
            <div className="bg-gray-900 rounded-lg overflow-hidden">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-800">
                    <th className="text-left px-4 py-3 text-gray-400">Prop</th>
                    <th className="text-left px-4 py-3 text-gray-400">Type</th>
                    <th className="text-left px-4 py-3 text-gray-400">Default</th>
                    <th className="text-left px-4 py-3 text-gray-400">Description</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-800">
                  <tr>
                    <td className="px-4 py-3 font-mono text-teal-400">teams</td>
                    <td className="px-4 py-3 text-gray-300">Team[]</td>
                    <td className="px-4 py-3 text-gray-500">required</td>
                    <td className="px-4 py-3 text-gray-300">Array of team objects</td>
                  </tr>
                  <tr>
                    <td className="px-4 py-3 font-mono text-teal-400">onTeamClick</td>
                    <td className="px-4 py-3 text-gray-300">(id: string) =&gt; void</td>
                    <td className="px-4 py-3 text-gray-500">required</td>
                    <td className="px-4 py-3 text-gray-300">Callback when team is clicked</td>
                  </tr>
                  <tr>
                    <td className="px-4 py-3 font-mono text-teal-400">variant</td>
                    <td className="px-4 py-3 text-gray-300">'list' | 'grid' | 'compact'</td>
                    <td className="px-4 py-3 text-gray-500">'list'</td>
                    <td className="px-4 py-3 text-gray-300">Visual style variant</td>
                  </tr>
                  <tr>
                    <td className="px-4 py-3 font-mono text-teal-400">loading</td>
                    <td className="px-4 py-3 text-gray-300">boolean</td>
                    <td className="px-4 py-3 text-gray-500">false</td>
                    <td className="px-4 py-3 text-gray-300">Show loading skeleton</td>
                  </tr>
                  <tr>
                    <td className="px-4 py-3 font-mono text-teal-400">selectedTeamId</td>
                    <td className="px-4 py-3 text-gray-300">string</td>
                    <td className="px-4 py-3 text-gray-500">undefined</td>
                    <td className="px-4 py-3 text-gray-300">ID of selected team</td>
                  </tr>
                  <tr>
                    <td className="px-4 py-3 font-mono text-teal-400">showHeader</td>
                    <td className="px-4 py-3 text-gray-300">boolean</td>
                    <td className="px-4 py-3 text-gray-500">true</td>
                    <td className="px-4 py-3 text-gray-300">Show section header</td>
                  </tr>
                  <tr>
                    <td className="px-4 py-3 font-mono text-teal-400">headerText</td>
                    <td className="px-4 py-3 text-gray-300">string</td>
                    <td className="px-4 py-3 text-gray-500">'Select Team'</td>
                    <td className="px-4 py-3 text-gray-300">Header text content</td>
                  </tr>
                  <tr>
                    <td className="px-4 py-3 font-mono text-teal-400">showMemberCount</td>
                    <td className="px-4 py-3 text-gray-300">boolean</td>
                    <td className="px-4 py-3 text-gray-500">false</td>
                    <td className="px-4 py-3 text-gray-300">Display member count</td>
                  </tr>
                  <tr>
                    <td className="px-4 py-3 font-mono text-teal-400">showDescription</td>
                    <td className="px-4 py-3 text-gray-300">boolean</td>
                    <td className="px-4 py-3 text-gray-500">false</td>
                    <td className="px-4 py-3 text-gray-300">Display team description</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};

export default TeamSelectorShowcase;