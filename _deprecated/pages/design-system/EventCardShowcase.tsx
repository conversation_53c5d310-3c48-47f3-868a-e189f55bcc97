// ABOUTME: Showcase page for the enhanced event card component
// Demonstrates all badge positioning options and usage scenarios

import React from 'react';
import { IonContent, IonHeader, IonPage, IonTitle, IonToolbar } from '@ionic/react';
import { ShadowEventCardEnhanced } from '../../foundation/design-system/components/molecules/Cards/ShadowEventCardEnhanced';

const EventCardShowcase: React.FC = () => {
  const handleCardClick = () => {
    console.log('Event card clicked');
  };

  const handleSmsClick = () => {
    console.log('SMS reminder clicked');
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>Event Card Showcase</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent fullscreen>
        <div style={{ padding: '20px', backgroundColor: '#000', minHeight: '100vh' }}>
          <h1 style={{ color: '#fff', marginBottom: '32px' }}>Shadow Event Card Enhanced</h1>

          {/* Draft Badge Positions Section */}
          <section style={{ marginBottom: '48px' }}>
            <h2 style={{ color: '#1ABC9C', marginBottom: '24px' }}>Draft Badge Positions</h2>
            
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))', gap: '24px' }}>
              {/* Bottom Right (Default) */}
              <div>
                <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Bottom Right (Default)</h3>
                <ShadowEventCardEnhanced
                  type="training"
                  status="upcoming"
                  title="Team Training Session"
                  subtitle="Weekly practice"
                  date="Tomorrow, March 15"
                  time="4:00 PM"
                  location="Main Field"
                  published={false}
                  invitedCount={6}
                  draftBadgePosition="bottom-right"
                  onClick={handleCardClick}
                />
              </div>

              {/* Bottom Left */}
              <div>
                <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Bottom Left</h3>
                <ShadowEventCardEnhanced
                  type="training"
                  status="upcoming"
                  title="Team Training Session"
                  subtitle="Weekly practice"
                  date="Tomorrow, March 15"
                  time="4:00 PM"
                  location="Main Field"
                  published={false}
                  invitedCount={6}
                  draftBadgePosition="bottom-left"
                  onClick={handleCardClick}
                />
              </div>

              {/* Top Right */}
              <div>
                <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Top Right</h3>
                <ShadowEventCardEnhanced
                  type="training"
                  status="upcoming"
                  title="Team Training Session"
                  subtitle="Weekly practice"
                  date="Tomorrow, March 15"
                  time="4:00 PM"
                  location="Main Field"
                  published={false}
                  invitedCount={6}
                  draftBadgePosition="top-right"
                  onClick={handleCardClick}
                />
              </div>

              {/* Top Left */}
              <div>
                <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Top Left</h3>
                <ShadowEventCardEnhanced
                  type="training"
                  status="upcoming"
                  title="Team Training Session"
                  subtitle="Weekly practice"
                  date="Tomorrow, March 15"
                  time="4:00 PM"
                  location="Main Field"
                  published={false}
                  invitedCount={6}
                  draftBadgePosition="top-left"
                  onClick={handleCardClick}
                />
              </div>

              {/* Inline */}
              <div>
                <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Inline with Title</h3>
                <ShadowEventCardEnhanced
                  type="training"
                  status="upcoming"
                  title="Team Training Session"
                  subtitle="Weekly practice"
                  date="Tomorrow, March 15"
                  time="4:00 PM"
                  location="Main Field"
                  published={false}
                  invitedCount={6}
                  draftBadgePosition="inline"
                  onClick={handleCardClick}
                />
              </div>
            </div>
          </section>

          {/* Real World Scenarios */}
          <section style={{ marginBottom: '48px' }}>
            <h2 style={{ color: '#1ABC9C', marginBottom: '24px' }}>Real World Scenarios</h2>
            
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))', gap: '24px' }}>
              {/* Draft with Player Circle */}
              <div>
                <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Draft Event with Player Circle</h3>
                <ShadowEventCardEnhanced
                  type="match"
                  status="upcoming"
                  title="Championship Final"
                  date="Saturday, March 18"
                  time="2:00 PM"
                  location="Stadium A"
                  published={false}
                  invitedCount={11}
                  draftBadgePosition="bottom-right"
                  size="large"
                  onClick={handleCardClick}
                />
              </div>

              {/* Published with Pre-Evaluations */}
              <div>
                <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Published with Pre-Evaluations</h3>
                <ShadowEventCardEnhanced
                  type="training"
                  status="upcoming"
                  title="Pre-Season Training"
                  date="Monday, March 20"
                  time="5:00 PM"
                  location="Training Ground"
                  published={true}
                  isPreEvaluationEnabled={true}
                  preEvaluationCompleted={3}
                  preEvaluationTotal={10}
                  totalPlayers={10}
                  onSmsReminder={handleSmsClick}
                  onClick={handleCardClick}
                />
              </div>

              {/* In Progress */}
              <div>
                <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>In Progress with Evaluations</h3>
                <ShadowEventCardEnhanced
                  type="match"
                  status="in-progress"
                  title="League Match vs Tigers"
                  date="Today"
                  time="3:00 PM"
                  location="Home Field"
                  published={true}
                  evaluationsCompleted={7}
                  evaluationsTotal={11}
                  attendedCount={11}
                  attendanceCompleted={true}
                  onClick={handleCardClick}
                />
              </div>

              {/* Complete with Drafts */}
              <div>
                <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Complete with Draft Evaluations</h3>
                <ShadowEventCardEnhanced
                  type="session"
                  status="complete"
                  title="Technical Skills Workshop"
                  date="Yesterday"
                  time="4:30 PM"
                  location="Indoor Court"
                  published={true}
                  evaluationsCompleted={8}
                  evaluationsTotal={12}
                  coachEvaluationDraftCount={4}
                  attendedCount={12}
                  attendanceCompleted={true}
                  onClick={handleCardClick}
                />
              </div>
            </div>
          </section>

          {/* Different Sizes */}
          <section style={{ marginBottom: '48px' }}>
            <h2 style={{ color: '#1ABC9C', marginBottom: '24px' }}>Size Variations</h2>
            
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))', gap: '24px' }}>
              {/* Small */}
              <div>
                <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Small</h3>
                <ShadowEventCardEnhanced
                  type="event"
                  status="upcoming"
                  title="Team Meeting"
                  date="Tomorrow"
                  time="7:00 PM"
                  published={true}
                  size="small"
                  onClick={handleCardClick}
                />
              </div>

              {/* Medium */}
              <div>
                <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Medium (Default)</h3>
                <ShadowEventCardEnhanced
                  type="event"
                  status="upcoming"
                  title="Team Meeting"
                  date="Tomorrow"
                  time="7:00 PM"
                  published={true}
                  size="medium"
                  onClick={handleCardClick}
                />
              </div>

              {/* Large */}
              <div>
                <h3 style={{ color: '#fff', marginBottom: '12px', fontSize: '14px' }}>Large</h3>
                <ShadowEventCardEnhanced
                  type="event"
                  status="upcoming"
                  title="Team Meeting"
                  date="Tomorrow"
                  time="7:00 PM"
                  published={true}
                  size="large"
                  onClick={handleCardClick}
                />
              </div>
            </div>
          </section>

          {/* Code Example */}
          <section>
            <h2 style={{ color: '#1ABC9C', marginBottom: '24px' }}>Usage Example</h2>
            <pre style={{ 
              backgroundColor: '#1f1f1f', 
              padding: '20px', 
              borderRadius: '8px',
              overflow: 'auto',
              color: '#e0e0e0'
            }}>
{`import { ShadowEventCardEnhanced } from '@/components/design-system/ShadowEventCardEnhanced';

// Basic usage with draft badge at bottom right
<ShadowEventCardEnhanced
  type="training"
  status="upcoming"
  title="Team Training"
  date="Tomorrow"
  time="4:00 PM"
  location="Main Field"
  published={false}
  invitedCount={15}
  draftBadgePosition="bottom-right"
  onClick={() => console.log('Navigate to event')}
/>

// With pre-evaluations and SMS button
<ShadowEventCardEnhanced
  type="match"
  status="upcoming"
  title="Championship Game"
  date="Saturday"
  published={true}
  isPreEvaluationEnabled={true}
  preEvaluationCompleted={5}
  preEvaluationTotal={11}
  onSmsReminder={() => sendSmsReminder()}
/>`}
            </pre>
          </section>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default EventCardShowcase;