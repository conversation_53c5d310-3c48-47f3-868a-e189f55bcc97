import React from 'react';
import { DebugModeDemo } from '../../../../components/v2/DebugModeDemo';
import { SectionWrapper, SectionTitle, ExampleGrid, SectionSpacer } from '../components/SectionComponents';
import { useSectionMetadata } from '../utils/sectionHelpers';

export const DebugModeSection: React.FC = () => {
  const metadata = useSectionMetadata('debug-mode');
  
  return (
    <SectionWrapper id="debug-mode">
      <SectionTitle 
        title="Debug Mode" 
        description="Role-specific content visualization for superadmins"
        {...metadata}
      />
      
      <ExampleGrid>
        <DebugModeDemo />
      </ExampleGrid>

      {/* Debug Mode Explanation */}
      <ExampleGrid>
        <div className="bg-gray-900 rounded-xl p-6">
          <h2 className="text-xl font-bold text-white mb-4">Debug Mode Feature</h2>
          <p className="text-gray-400 mb-4">
            Debug mode allows superadmins to see role-specific content across the app. When enabled, 
            components will show colored borders and labels indicating which user role they're designed for.
          </p>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
            <div className="text-center">
              <div className="inline-block px-3 py-1 rounded bg-blue-500/20 border border-blue-500 text-blue-500 text-sm font-bold mb-2">PLAYER</div>
              <p className="text-xs text-gray-500">Blue components</p>
            </div>
            <div className="text-center">
              <div className="inline-block px-3 py-1 rounded bg-orange-500/20 border border-orange-500 text-orange-500 text-sm font-bold mb-2">COACH</div>
              <p className="text-xs text-gray-500">Orange components</p>
            </div>
            <div className="text-center">
              <div className="inline-block px-3 py-1 rounded bg-green-500/20 border border-green-500 text-green-500 text-sm font-bold mb-2">PARENT</div>
              <p className="text-xs text-gray-500">Green components</p>
            </div>
            <div className="text-center">
              <div className="inline-block px-3 py-1 rounded bg-purple-500/20 border border-purple-500 text-purple-500 text-sm font-bold mb-2">ADMIN</div>
              <p className="text-xs text-gray-500">Purple components</p>
            </div>
            <div className="text-center">
              <div className="inline-block px-3 py-1 rounded bg-red-500/20 border border-red-500 text-red-500 text-sm font-bold mb-2">SUPER</div>
              <p className="text-xs text-gray-500">Red components</p>
            </div>
          </div>
          <div className="bg-gray-800 rounded-lg p-4">
            <p className="text-sm text-gray-300 font-mono mb-2">Usage in code:</p>
            <pre className="text-xs text-gray-400 overflow-x-auto">{`// Check if debug mode is enabled
const debugEnabled = isDebugMode();

// Get debug styles for a component
const debugStyles = getDebugStyles('coach');

// Apply debug class to element
applyDebugClass(element, 'full'); // or 'minimal'`}</pre>
          </div>
        </div>
      </ExampleGrid>

      <SectionSpacer />
    </SectionWrapper>
  );
};
