import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';
import PageWithNavigation from '../../section/Navigation/PageWithNavigation';
import { PreEvaluationCard } from '../../../components/v2/cards/PreEvaluationCard';
import { EvaluationCompletionCard } from '../../../components/v2/cards/EvaluationCompletionCard';
import { Trophy, Zap, Users, Target } from 'lucide-react';

const EvaluationShowcase: React.FC = () => {
  const history = useHistory();
  const [showCompletion, setShowCompletion] = useState(false);

  // Mock data for demonstration
  const mockEvent = {
    id: 'demo-event-id',
    name: 'Weekly Training Session',
    teamName: 'SHOT Elite U16',
    startTime: new Date(Date.now() + 3 * 60 * 60 * 1000).toISOString(), // 3 hours from now
    expiresAt: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString()
  };

  return (
    <PageWithNavigation showBackButton={true} title="Evaluation Features">
      <div className="max-w-4xl mx-auto px-4 py-6 space-y-8">
        
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">SHOT Evaluation System</h1>
          <p className="text-gray-400">Complete evaluations, earn XP, track your streaks!</p>
        </div>

        {/* XP & Streaks Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-gray-800 rounded-xl p-6 text-center">
            <div className="w-12 h-12 bg-shot-purple/20 rounded-full flex items-center justify-center mx-auto mb-3">
              <Zap className="w-6 h-6 text-shot-purple" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-1">Earn XP</h3>
            <p className="text-gray-400 text-sm">+50 XP for each evaluation</p>
          </div>

          <div className="bg-gray-800 rounded-xl p-6 text-center">
            <div className="w-12 h-12 bg-orange-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
              <Trophy className="w-6 h-6 text-orange-500" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-1">Build Streaks</h3>
            <p className="text-gray-400 text-sm">Complete evaluations consistently</p>
          </div>

          <div className="bg-gray-800 rounded-xl p-6 text-center">
            <div className="w-12 h-12 bg-shot-teal/20 rounded-full flex items-center justify-center mx-auto mb-3">
              <Users className="w-6 h-6 text-shot-teal" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-1">Team Progress</h3>
            <p className="text-gray-400 text-sm">See who's completed their eval</p>
          </div>
        </div>

        {/* Demo Section */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold text-white">Try It Out</h2>
          
          {!showCompletion ? (
            <>
              <p className="text-gray-400 mb-4">
                This is what players see when they have a pending evaluation:
              </p>
              
              <PreEvaluationCard
                eventId={mockEvent.id}
                eventName={mockEvent.name}
                eventStartTime={mockEvent.startTime}
                teamName={mockEvent.teamName}
                expiresAt={mockEvent.expiresAt}
                onStartEvaluation={() => setShowCompletion(true)}
              />
              
              <div className="text-center text-sm text-gray-500">
                Click "Complete Evaluation" to see what happens after completion
              </div>
            </>
          ) : (
            <>
              <p className="text-gray-400 mb-4">
                After completing an evaluation, players see:
              </p>
              
              <EvaluationCompletionCard
                eventId={mockEvent.id}
                eventName={mockEvent.name}
                teamName={mockEvent.teamName}
                xpEarned={50}
              />
              
              <div className="text-center">
                <button
                  onClick={() => setShowCompletion(false)}
                  className="text-shot-purple hover:text-shot-purple/80 text-sm"
                >
                  ← Back to pre-evaluation view
                </button>
              </div>
            </>
          )}
        </div>

        {/* Feature List */}
        <div className="bg-gray-800 rounded-xl p-6 mt-8">
          <h3 className="text-lg font-semibold text-white mb-4">New Features</h3>
          <ul className="space-y-3 text-gray-300">
            <li className="flex items-start gap-2">
              <Target className="w-5 h-5 text-shot-green mt-0.5 flex-shrink-0" />
              <div>
                <strong>XP System:</strong> Players earn 50 XP for each completed evaluation
              </div>
            </li>
            <li className="flex items-start gap-2">
              <Target className="w-5 h-5 text-shot-green mt-0.5 flex-shrink-0" />
              <div>
                <strong>Streak Tracking:</strong> Shows current and longest evaluation streaks
              </div>
            </li>
            <li className="flex items-start gap-2">
              <Target className="w-5 h-5 text-shot-green mt-0.5 flex-shrink-0" />
              <div>
                <strong>Team Visibility:</strong> See which teammates have completed their evaluations
              </div>
            </li>
            <li className="flex items-start gap-2">
              <Target className="w-5 h-5 text-shot-green mt-0.5 flex-shrink-0" />
              <div>
                <strong>Real-time Updates:</strong> Progress bars and completion stats update live
              </div>
            </li>
          </ul>
        </div>

        {/* Setup Instructions */}
        <div className="bg-shot-purple/10 rounded-xl p-6 border border-shot-purple/30">
          <h3 className="text-lg font-semibold text-shot-purple mb-3">Setup Required</h3>
          <p className="text-gray-300 mb-3">
            To enable XP and streak tracking, run the SQL setup script in Supabase:
          </p>
          <code className="block bg-black/50 rounded p-3 text-xs text-gray-400 font-mono">
            setup_xp_and_streaks.sql
          </code>
        </div>
      </div>
    </PageWithNavigation>
  );
};

export default EvaluationShowcase;
