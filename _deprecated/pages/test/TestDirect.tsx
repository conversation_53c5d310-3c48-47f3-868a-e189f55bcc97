// ABOUTME: TestDirect - Direct test component without lazy loading
// Testing if the issue is with lazy loading or routing

import React from 'react';

const TestDirect: React.FC = () => {
  console.log('🔴 TestDirect rendering!');
  
  return (
    <div style={{ 
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: '#FF0000',
      color: '#FFFFFF',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '48px',
      fontWeight: 'bold',
      zIndex: 99999
    }}>
      🔴 TEST DIRECT - If you see this RED screen, routing works!
    </div>
  );
};

export default TestDirect;