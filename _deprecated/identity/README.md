# Deprecated Identity Files

Files moved here during Identity area migration (Authentication & Family Management).

## Deprecated Files

### Authentication Pages (Consolidated into one)
- `pages/Login.tsx` - Basic login (replaced by unified login)
- `pages/UpdatedLogin.tsx` - Enhanced login (replaced by unified login)
- `pages/LoginUpdated.tsx` - Another variant (replaced by unified login)
- `pages/CapLogin.tsx` - Special login variant (replaced by unified login)
- `pages/NonIonicLogin.tsx` - Non-Ionic version (replaced by unified login)
- `pages/SimpleLogin.tsx` - Simplified version (replaced by unified login)

**Note**: `UpdatedLoginV2.tsx` was kept as the base for the new unified login

### Reason for Deprecation
- Multiple login pages created confusion
- Inconsistent user experience
- Maintenance overhead
- Code duplication

### Migration Path
Old: `/login`, `/updated-login`, etc.
New: `/identity/login` (unified experience)

### Target Deletion Date
- Migration completed: [DATE]
- Safe to delete after: [DATE + 2 months]