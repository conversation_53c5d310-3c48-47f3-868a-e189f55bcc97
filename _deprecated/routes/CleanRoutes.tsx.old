// src/routes/CleanRoutes.tsx
import React, { lazy, Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';
import { CleanLayout } from '../layouts/CleanLayout';

// Lazy load clean pages to keep bundles separate
const CleanEvaluationSummary = lazy(() => import('../pages/clean/EvaluationSummary'));
const CleanDashboard = lazy(() => import('../pages/clean/Dashboard'));

const LoadingFallback = () => (
  <div style={{ 
    display: 'flex', 
    justifyContent: 'center', 
    alignItems: 'center', 
    height: '100vh',
    color: '#666' 
  }}>
    Loading...
  </div>
);

export const CleanRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/clean" element={<CleanLayout />}>
        <Route index element={
          <Suspense fallback={<LoadingFallback />}>
            <CleanDashboard />
          </Suspense>
        } />
        <Route path="evaluation" element={
          <Suspense fallback={<LoadingFallback />}>
            <CleanEvaluationSummary />
          </Suspense>
        } />
      </Route>
    </Routes>
  );
};