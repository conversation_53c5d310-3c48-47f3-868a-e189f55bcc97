/* Dark Mode Styles for Team Selection View */

.dark-card {
  background-color: #1a1f2c !important;
  color: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  margin: 16px;
}

.dark-card ion-card-content {
  padding: 20px;
}

.page-title {
  color: #6b7aff;
  font-family: var(--font-heading);
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

.page-subtitle {
  color: #b4b6c6;
  font-size: 16px;
  margin-bottom: 24px;
  font-weight: 400;
  line-height: 1.4;
}

.team-search {
  margin-bottom: 16px;
  --background: #2a2f3c;
  --color: #ffffff;
  --placeholder-color: #8d91a5;
  --icon-color: #6b7aff;
  --border-radius: 8px;
}

/* Override any Ionic styles that might conflict */
ion-card.dark-card ion-card-content {
  --background: #1a1f2c;
  --color: #ffffff;
}

/* Ensure proper contrast for the forward icon */
.team-forward-icon {
  color: #6b7aff !important;
}

/* Style the floating action button for dark mode */
ion-fab-button {
  --background: #6b7aff;
  --color: #ffffff;
  --box-shadow: 0 4px 12px rgba(107, 122, 255, 0.4);
}
