/* SHOT Button Fix - Force consistent button styling */

/* Ensure all buttons get the correct colors and styles */
ion-button {
  --border-radius: 8px !important;
  min-height: 44px !important;
  font-family: 'Poppins', sans-serif !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
}

/* Core button variants */
ion-button.shot-button-teal,
ion-button.teal-button,
ion-button[color="teal"] {
  --background: #1ABC9C !important;
  --color: #000000 !important;
  --background-hover: #17a589 !important;
  --background-activated: #17a589 !important;
}

ion-button.shot-button-purple,
ion-button.purple-button,
ion-button[color="purple"] {
  --background: #6B00DB !important;
  --color: #FFFFFF !important;
  --background-hover: #5e00c1 !important;
  --background-activated: #5e00c1 !important;
}

ion-button.shot-button-gold,
ion-button.gold-button,
ion-button[color="gold"] {
  --background: #F7B613 !important;
  --color: #000000 !important;
  --background-hover: #d9a011 !important;
  --background-activated: #d9a011 !important;
}

/* Fix for the ButtonShowcase page specifically */
[style*="--background:"] {
  background-color: var(--background) !important;
  color: var(--color) !important;
}

/* Force button text color based on background */
ion-button[style*="--background: #1ABC9C"],
ion-button[style*="--background:#1ABC9C"],
ion-button[style*="background-color: #1ABC9C"] {
  --color: #000000 !important;
  color: #000000 !important;
}

ion-button[style*="--background: #6B00DB"],
ion-button[style*="--background:#6B00DB"],
ion-button[style*="background-color: #6B00DB"] {
  --color: #FFFFFF !important;
  color: #FFFFFF !important;
}

ion-button[style*="--background: #F7B613"],
ion-button[style*="--background:#F7B613"],
ion-button[style*="background-color: #F7B613"] {
  --color: #000000 !important;
  color: #000000 !important;
}

/* Fix specific to any inline styles that might be overriding colors */
ion-button[style] {
  border-radius: 8px !important;
}

/* Fix for dark backgrounds */
body.dark ion-button,
.ion-page ion-button,
.dark-theme ion-button {
  --background-color: var(--background) !important;
  --color: var(--color) !important;
}

/* Override any conflicting Tailwind styles */
.btn-primary,
.btn-secondary {
  border-radius: 8px !important;
  min-height: 44px !important;
}

/* Fix any potential inline style overrides */
[style*="border-radius: 0"],
[style*="border-radius:0"] {
  border-radius: 8px !important;
}
