/* Global fix for code blocks and debug content in dark theme */

/* Define CSS variables for consistent debug styling */
:root {
  /* Debug color palette */
  --debug-bg-primary: #1a1a1a;
  --debug-bg-secondary: #2a2a2a;
  --debug-bg-tertiary: #0d0d0d;
  
  --debug-text-primary: #e0e0e0;
  --debug-text-secondary: #999999;
  --debug-text-muted: #666666;
  
  --debug-border: rgba(255, 255, 255, 0.1);
  --debug-border-hover: rgba(255, 255, 255, 0.2);
  
  /* Syntax highlighting colors */
  --debug-syntax-key: #79c0ff;
  --debug-syntax-string: #a5d6ff;
  --debug-syntax-number: #79dac8;
  --debug-syntax-boolean: #ff7b72;
  --debug-syntax-null: #8b949e;
}

/* Utility class for debug pages */
.debug-page {
  --ion-background-color: #000000;
  --ion-text-color: #ffffff;
  --ion-toolbar-background: #000000;
  --ion-toolbar-color: #ffffff;
}

.debug-page ion-content {
  --background: #000000;
}

/* Quick fix for light theme elements */
.force-dark-theme {
  background-color: #000000 !important;
  color: #ffffff !important;
}

.force-dark-theme * {
  color: inherit !important;
}

/* Fix for all <pre> elements - dark background with light text */
pre {
  background-color: var(--debug-bg-primary) !important;
  color: var(--debug-text-primary) !important;
  padding: 12px !important;
  border-radius: 8px !important;
  border: 1px solid var(--debug-border) !important;
  overflow: auto !important;
  font-family: 'Courier New', Courier, monospace !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

/* Fix for all <code> elements */
code {
  background-color: var(--debug-bg-secondary) !important;
  color: var(--debug-text-primary) !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-family: 'Courier New', Courier, monospace !important;
}

/* Fix for code inside pre (don't double-apply background) */
pre code {
  background-color: transparent !important;
  padding: 0 !important;
}

/* Debug panels and cards */
.debug-panel,
.debug-card {
  background-color: var(--debug-bg-primary) !important;
  color: var(--debug-text-primary) !important;
  border: 1px solid var(--debug-border) !important;
}

/* JSON syntax highlighting */
.json-key {
  color: var(--debug-syntax-key) !important;
}

.json-string {
  color: var(--debug-syntax-string) !important;
}

.json-number {
  color: var(--debug-syntax-number) !important;
}

.json-boolean {
  color: var(--debug-syntax-boolean) !important;
}

.json-null {
  color: var(--debug-syntax-null) !important;
}

/* Fix for Ionic cards in dark theme */
ion-card {
  --background: var(--debug-bg-primary);
  --color: var(--debug-text-primary);
}

ion-card-header {
  --background: var(--debug-bg-primary);
  --color: var(--debug-text-primary);
}

ion-card-content {
  --background: var(--debug-bg-primary);
  --color: var(--debug-text-primary);
}

ion-card-title {
  --color: #ffffff;
}

/* Fix for debug-specific content */
.debug-content {
  background-color: var(--debug-bg-primary) !important;
  color: var(--debug-text-primary) !important;
}

/* Ensure proper contrast for all text in debug contexts */
.debug-text,
[class*="debug"] p,
[class*="debug"] span,
[class*="debug"] div {
  color: var(--debug-text-primary) !important;
}

/* Fix for any inline styles that might override */
[style*="background: #f0f0f0"],
[style*="background:#f0f0f0"],
[style*="background-color: #f0f0f0"],
[style*="background-color:#f0f0f0"] {
  background-color: var(--debug-bg-primary) !important;
  color: var(--debug-text-primary) !important;
}