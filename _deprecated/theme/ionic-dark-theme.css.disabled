/* Global Ionic dark theme configuration */

/* Ensure Ionic uses grey theme everywhere */
:root {
  --ion-background-color: rgb(31, 41, 55); /* gray-800 */
  --ion-background-color-rgb: 31,41,55;
  
  --ion-text-color: #ffffff;
  --ion-text-color-rgb: 255,255,255;
  
  --ion-color-step-50: #0d0d0d;
  --ion-color-step-100: #1a1a1a;
  --ion-color-step-150: #262626;
  --ion-color-step-200: #333333;
  --ion-color-step-250: #404040;
  --ion-color-step-300: #4d4d4d;
  --ion-color-step-350: #595959;
  --ion-color-step-400: #666666;
  --ion-color-step-450: #737373;
  --ion-color-step-500: #808080;
  --ion-color-step-550: #8c8c8c;
  --ion-color-step-600: #999999;
  --ion-color-step-650: #a6a6a6;
  --ion-color-step-700: #b3b3b3;
  --ion-color-step-750: #bfbfbf;
  --ion-color-step-800: #cccccc;
  --ion-color-step-850: #d9d9d9;
  --ion-color-step-900: #e6e6e6;
  --ion-color-step-950: #f2f2f2;
  
  --ion-item-background: rgb(55, 65, 81); /* gray-700 */
  --ion-card-background: rgb(55, 65, 81); /* gray-700 */
  --ion-toolbar-background: rgb(31, 41, 55);
}

/* Ensure all Ionic components use dark theme */
ion-content {
  --background: var(--ion-background-color);
  --color: var(--ion-text-color);
}

ion-card {
  --background: rgb(55, 65, 81); /* gray-700 */
  --color: #e0e0e0;
}

ion-item {
  --background: rgb(55, 65, 81); /* gray-700 */
  --color: #e0e0e0;
  --border-color: rgba(255, 255, 255, 0.1);
}

ion-list {
  --background: transparent;
}

ion-toolbar {
  --background: rgb(31, 41, 55);
  --color: #ffffff;
  --border-color: transparent;
}

ion-header {
  --background: rgb(31, 41, 55);
}

/* Fix for any modals or popovers */
ion-modal,
ion-popover {
  --background: rgb(55, 65, 81); /* gray-700 */
  --color: #e0e0e0;
}

/* Ensure all text is visible */
p, span, div, h1, h2, h3, h4, h5, h6 {
  color: var(--ion-text-color);
}

/* Override any hardcoded light colors */
.md body,
.ios body {
  --ion-background-color: rgb(31, 41, 55);
  --ion-text-color: #ffffff;
}