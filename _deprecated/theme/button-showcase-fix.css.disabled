/* Button Showcase Page Specific Fixes */

/* Force button colors on the showcase page */
.button-showcase-page ion-button,
[id*="button-showcase"] ion-button {
  border-radius: 8px !important;
}

/* Core color buttons */
.button-showcase-page ion-button.shot-button-teal,
.button-showcase-page ion-button[style*="--background: #1ABC9C"],
.button-showcase-page ion-button[style*="background-color: #1ABC9C"] {
  --background: #1ABC9C !important;
  --color: #000000 !important;
  background-color: #1ABC9C !important;
  color: #000000 !important;
}

.button-showcase-page ion-button.shot-button-purple,
.button-showcase-page ion-button[style*="--background: #6B00DB"],
.button-showcase-page ion-button[style*="background-color: #6B00DB"] {
  --background: #6B00DB !important;
  --color: #FFFFFF !important;
  background-color: #6B00DB !important;
  color: #FFFFFF !important;
}

.button-showcase-page ion-button.shot-button-gold,
.button-showcase-page ion-button[style*="--background: #F7B613"],
.button-showcase-page ion-button[style*="background-color: #F7B613"] {
  --background: #F7B613 !important;
  --color: #000000 !important;
  background-color: #F7B613 !important;
  color: #000000 !important;
}

/* PERFORM system colors */
.button-showcase-page ion-button.shot-button-electricblue,
.button-showcase-page ion-button[style*="--background: #296DFF"] {
  --background: #296DFF !important;
  --color: #FFFFFF !important;
  background-color: #296DFF !important;
  color: #FFFFFF !important;
}

.button-showcase-page ion-button.shot-button-forestgreen,
.button-showcase-page ion-button[style*="--background: #2E8B57"] {
  --background: #2E8B57 !important;
  --color: #FFFFFF !important;
  background-color: #2E8B57 !important;
  color: #FFFFFF !important;
}

.button-showcase-page ion-button.shot-button-burntorange,
.button-showcase-page ion-button[style*="--background: #FF6F3C"] {
  --background: #FF6F3C !important;
  --color: #000000 !important;
  background-color: #FF6F3C !important;
  color: #000000 !important;
}

.button-showcase-page ion-button.shot-button-warmcoral,
.button-showcase-page ion-button[style*="--background: #FF5D73"] {
  --background: #FF5D73 !important;
  --color: #FFFFFF !important;
  background-color: #FF5D73 !important;
  color: #FFFFFF !important;
}

.button-showcase-page ion-button.shot-button-charcoalgrey,
.button-showcase-page ion-button[style*="--background: #5C5C5C"] {
  --background: #5C5C5C !important;
  --color: #FFFFFF !important;
  background-color: #5C5C5C !important;
  color: #FFFFFF !important;
}

.button-showcase-page ion-button.shot-button-crimsonred,
.button-showcase-page ion-button[style*="--background: #E63946"] {
  --background: #E63946 !important;
  --color: #FFFFFF !important;
  background-color: #E63946 !important;
  color: #FFFFFF !important;
}

/* Fix outline buttons */
.button-showcase-page ion-button[fill="outline"] {
  --background: transparent !important;
  background-color: transparent !important;
}

.button-showcase-page ion-button[fill="outline"][style*="#1ABC9C"] {
  --border-color: #1ABC9C !important;
  --color: #1ABC9C !important;
  border-color: #1ABC9C !important;
  color: #1ABC9C !important;
}

.button-showcase-page ion-button[fill="outline"][style*="#6B00DB"] {
  --border-color: #6B00DB !important;
  --color: #6B00DB !important;
  border-color: #6B00DB !important;
  color: #6B00DB !important;
}

.button-showcase-page ion-button[fill="outline"][style*="#F7B613"] {
  --border-color: #F7B613 !important;
  --color: #F7B613 !important;
  border-color: #F7B613 !important;
  color: #F7B613 !important;
}
