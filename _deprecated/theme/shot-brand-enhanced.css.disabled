/* SHOT Brand Enhanced CSS - Full Compliance Implementation */
/* Fixes button consistency and ensures proper brand application */

/* Import SHOT Brand Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* SHOT Brand Color System */
:root {
  /* SHOT Core Pillar Colors */
  --shot-teal: #1ABC9C;           /* Take Your Shot */
  --shot-purple: #6B00DB;         /* Own It */
  --shot-gold: #F7B613;           /* Make Impact */
  
  /* SHOT Foundation Colors */
  --shot-black: #000000;
  --shot-white: #FFFFFF;
  --shot-grey: #808080;  /* Pure grey for inactive status */
  --shot-green: #00BB00; /* Vibrant green for active status */
  
  /* SHOT PERFORM System Colors */
  --shot-electric-blue: #296DFF;   /* Technical */
  --shot-forest-green: #2E8B57;   /* Physical */
  --shot-burnt-orange: #FF6F3C;   /* Psychological */
  --shot-warm-coral: #FF5D73;     /* Social */
  --shot-charcoal-grey: #5C5C5C;  /* Tactical/Positional */
  
  /* SHOT Functional Colors */
  --shot-red: #E63946;            /* Errors/Warnings only */
  
  /* SHOT Role-Specific Colors */
  --shot-role-superadmin: #DC2626;     /* Red for SuperAdmin */
  --shot-role-coach: #FF6F3C;          /* Orange for Coach */
  --shot-role-player: #22C55E;         /* Green for Player */
  --shot-role-club-admin: #9333EA;     /* Purple for Club Admin */
  
  /* SHOT Typography Variables */
  --shot-font-heading: 'Poppins', sans-serif;
  --shot-font-body: 'Montserrat', sans-serif;
  
  /* SHOT Button System - ENHANCED FOR CONSISTENCY */
  --shot-button-radius: 8px;      /* CRITICAL: All buttons must use this */
  --shot-button-height: 44px;     /* Standard button height */
  --shot-button-padding: 12px 24px;
  --shot-button-font-weight: 600;
  --shot-button-letter-spacing: 0.05em;
  --shot-button-transition: all 0.2s ease;
  
  /* Update Ionic Variables to use SHOT Brand Colors */
  --ion-color-primary: var(--shot-purple);
  --ion-color-primary-rgb: 107, 0, 219;
  --ion-color-primary-contrast: var(--shot-white);
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #5e00c1;
  --ion-color-primary-tint: #7a1adf;
  
  --ion-color-success: var(--shot-teal);
  --ion-color-success-rgb: 26, 188, 156;
  --ion-color-success-contrast: var(--shot-black);
  --ion-color-success-contrast-rgb: 0, 0, 0;
  --ion-color-success-shade: #17a589;
  --ion-color-success-tint: #31c3a6;
  
  --ion-color-tertiary: var(--shot-gold);
  --ion-color-tertiary-rgb: 247, 182, 19;
  --ion-color-tertiary-contrast: var(--shot-black);
  --ion-color-tertiary-contrast-rgb: 0, 0, 0;
  --ion-color-tertiary-shade: #d9a011;
  --ion-color-tertiary-tint: #f8bd2b;
  
  --ion-color-danger: var(--shot-red);
  --ion-color-danger-rgb: 230, 57, 70;
  --ion-color-danger-contrast: var(--shot-white);
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #cb333e;
  --ion-color-danger-tint: #ed576b;
}

/* CRITICAL: Global Ionic Button Override for Complete Consistency */
ion-button {
  --border-radius: var(--shot-button-radius) !important;
  --background: var(--background) !important;
  --color: var(--color) !important;
  font-family: var(--shot-font-heading) !important;
  font-weight: var(--shot-button-font-weight) !important;
  text-transform: uppercase !important;
  letter-spacing: var(--shot-button-letter-spacing) !important;
  transition: var(--shot-button-transition) !important;
  min-height: var(--shot-button-height) !important;
}

/* Force consistent styling for all buttons */
ion-button,
button.button-solid,
button.button-outline,
button.button-clear {
  --border-radius: var(--shot-button-radius) !important;
  border-radius: var(--shot-button-radius) !important;
  overflow: hidden !important;
}

/* Fix for Ionic's internal button styling */
.button-solid,
.button-outline,
.button-clear {
  --border-radius: var(--shot-button-radius) !important;
  border-radius: var(--shot-button-radius) !important;
}

/* Force button background colors */
ion-button.button-solid.ion-color-primary,
ion-button.ion-color-primary {
  --background: var(--shot-purple) !important;
  --color: var(--shot-white) !important;
}

ion-button.button-solid.ion-color-success,
ion-button.ion-color-success {
  --background: var(--shot-teal) !important;
  --color: var(--shot-black) !important;
}

ion-button.button-solid.ion-color-tertiary,
ion-button.ion-color-tertiary {
  --background: var(--shot-gold) !important;
  --color: var(--shot-black) !important;
}

/* Ensure all input elements also use consistent border radius */
ion-input,
ion-textarea,
ion-select,
ion-searchbar,
ion-chip,
ion-card,
ion-modal,
ion-popover,
ion-alert,
ion-action-sheet,
ion-toast {
  --border-radius: var(--shot-button-radius) !important;
}

/* SHOT Typography System - Global Implementation */
body, 
ion-content,
ion-app,
.ion-page {
  font-family: var(--shot-font-body) !important;
  background-color: var(--shot-black) !important;
  color: var(--shot-white) !important;
}

/* All headings use Poppins */
h1, h2, h3, h4, h5, h6,
.shot-heading,
ion-title,
ion-label[slot="start"],
ion-label[position="stacked"],
.heading,
.title {
  font-family: var(--shot-font-heading) !important;
  color: var(--shot-white) !important;
}

/* H1 - Primary Headlines/Taglines */
h1,
.shot-h1 {
  font-family: var(--shot-font-heading) !important;
  font-weight: 800 !important; /* ExtraBold */
  text-transform: uppercase !important;
  letter-spacing: 0.15em !important;
  font-size: 2rem !important;
  line-height: 1.2 !important;
  color: var(--shot-white) !important;
  margin: 0 !important;
}

/* H2 - Major Section Titles */
h2,
.shot-h2 {
  font-family: var(--shot-font-heading) !important;
  font-weight: 700 !important; /* Bold */
  font-size: 1.5rem !important;
  line-height: 1.3 !important;
  letter-spacing: 0.05em !important;
  color: var(--shot-white) !important;
  margin: 0 !important;
}

/* H3 - Subheadings */
h3, 
.shot-h3 {
  font-family: var(--shot-font-body) !important;
  font-weight: 600 !important; /* SemiBold */
  font-size: 1.25rem !important;
  line-height: 1.4 !important;
  color: var(--shot-white) !important;
  margin: 0 !important;
}

/* Body Text */
p,
.shot-body,
ion-text,
.text,
span {
  font-family: var(--shot-font-body) !important;
  font-weight: 400 !important; /* Regular */
  font-size: 1rem !important;
  line-height: 1.6 !important;
  color: var(--shot-white) !important;
}

/* UI Elements & Labels */
.shot-ui-text,
ion-label,
ion-button,
.ui-text {
  font-family: var(--shot-font-body) !important;
  font-weight: 500 !important; /* Medium */
  font-size: 0.875rem !important;
  line-height: 1.4 !important;
  color: var(--shot-white) !important;
}

/* Caption Text */
.shot-caption,
.caption,
small {
  font-family: var(--shot-font-body) !important;
  font-weight: 400 !important;
  font-size: 0.75rem !important;
  line-height: 1.4 !important;
  color: rgba(255, 255, 255, 0.7) !important;
}

/* SHOT Button System - Enhanced with Complete Variants */
.shot-button-primary,
.shot-btn-primary {
  font-family: var(--shot-font-heading) !important;
  font-weight: var(--shot-button-font-weight) !important;
  text-transform: uppercase !important;
  letter-spacing: var(--shot-button-letter-spacing) !important;
  background-color: var(--shot-teal) !important;
  color: var(--shot-black) !important;
  border: none !important;
  padding: var(--shot-button-padding) !important;
  border-radius: var(--shot-button-radius) !important;
  min-height: var(--shot-button-height) !important;
  transition: var(--shot-button-transition) !important;
  cursor: pointer !important;
}

.shot-button-primary:hover,
.shot-btn-primary:hover {
  background-color: #17a589 !important;
  transform: translateY(-1px) !important;
}

.shot-button-secondary,
.shot-btn-secondary {
  font-family: var(--shot-font-heading) !important;
  font-weight: var(--shot-button-font-weight) !important;
  text-transform: uppercase !important;
  letter-spacing: var(--shot-button-letter-spacing) !important;
  background-color: var(--shot-purple) !important;
  color: var(--shot-white) !important;
  border: none !important;
  padding: var(--shot-button-padding) !important;
  border-radius: var(--shot-button-radius) !important;
  min-height: var(--shot-button-height) !important;
  transition: var(--shot-button-transition) !important;
  cursor: pointer !important;
}

.shot-button-secondary:hover,
.shot-btn-secondary:hover {
  background-color: #5e00c1 !important;
  transform: translateY(-1px) !important;
}

.shot-button-tertiary,
.shot-btn-tertiary {
  font-family: var(--shot-font-heading) !important;
  font-weight: var(--shot-button-font-weight) !important;
  text-transform: uppercase !important;
  letter-spacing: var(--shot-button-letter-spacing) !important;
  background-color: var(--shot-gold) !important;
  color: var(--shot-black) !important;
  border: none !important;
  padding: var(--shot-button-padding) !important;
  border-radius: var(--shot-button-radius) !important;
  min-height: var(--shot-button-height) !important;
  transition: var(--shot-button-transition) !important;
  cursor: pointer !important;
}

.shot-button-tertiary:hover,
.shot-btn-tertiary:hover {
  background-color: #d9a011 !important;
  transform: translateY(-1px) !important;
}

/* Purple Button Variant */
.shot-button-purple,
.shot-btn-purple {
  font-family: var(--shot-font-heading) !important;
  font-weight: var(--shot-button-font-weight) !important;
  text-transform: uppercase !important;
  letter-spacing: var(--shot-button-letter-spacing) !important;
  background-color: var(--shot-purple) !important;
  color: var(--shot-white) !important;
  border: none !important;
  padding: var(--shot-button-padding) !important;
  border-radius: var(--shot-button-radius) !important;
  min-height: var(--shot-button-height) !important;
  transition: var(--shot-button-transition) !important;
  cursor: pointer !important;
}

.shot-button-purple:hover,
.shot-btn-purple:hover {
  background-color: #5e00c1 !important;
  transform: translateY(-1px) !important;
}

/* Orange Button Variant */
.shot-button-orange,
.shot-btn-orange {
  font-family: var(--shot-font-heading) !important;
  font-weight: var(--shot-button-font-weight) !important;
  text-transform: uppercase !important;
  letter-spacing: var(--shot-button-letter-spacing) !important;
  background-color: var(--shot-burnt-orange) !important;
  color: var(--shot-black) !important;
  border: none !important;
  padding: var(--shot-button-padding) !important;
  border-radius: var(--shot-button-radius) !important;
  min-height: var(--shot-button-height) !important;
  transition: var(--shot-button-transition) !important;
  cursor: pointer !important;
}

.shot-button-orange:hover,
.shot-btn-orange:hover {
  background-color: #e5623a !important;
  transform: translateY(-1px) !important;
}

/* Outline Button Variant */
.shot-button-outline,
.shot-btn-outline {
  font-family: var(--shot-font-heading) !important;
  font-weight: var(--shot-button-font-weight) !important;
  text-transform: uppercase !important;
  letter-spacing: var(--shot-button-letter-spacing) !important;
  background-color: transparent !important;
  color: var(--shot-teal) !important;
  border: 2px solid var(--shot-teal) !important;
  padding: 10px 22px !important;
  border-radius: var(--shot-button-radius) !important;
  min-height: var(--shot-button-height) !important;
  transition: var(--shot-button-transition) !important;
  cursor: pointer !important;
}

.shot-button-outline:hover,
.shot-btn-outline:hover {
  background-color: var(--shot-teal) !important;
  color: var(--shot-black) !important;
}

/* Small Button Variant */
.shot-button-small,
.shot-btn-small {
  font-family: var(--shot-font-heading) !important;
  font-weight: 500 !important;
  text-transform: uppercase !important;
  letter-spacing: var(--shot-button-letter-spacing) !important;
  background-color: var(--shot-teal) !important;
  color: var(--shot-black) !important;
  border: none !important;
  padding: 8px 16px !important;
  border-radius: 6px !important;
  min-height: 32px !important;
  font-size: 0.875rem !important;
  transition: var(--shot-button-transition) !important;
  cursor: pointer !important;
}

/* SHOT Card System */
.shot-card {
  background-color: var(--shot-black) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: var(--shot-button-radius) !important;
  padding: 0 !important;
  margin-bottom: 16px !important;
  box-shadow: none !important;
}

.shot-card-header {
  padding: 16px !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.shot-card-title {
  font-family: var(--shot-font-heading) !important;
  font-weight: 600 !important;
  font-size: 1.125rem !important;
  color: var(--shot-white) !important;
  margin: 0 !important;
}

.shot-card-content {
  padding: 16px !important;
}

/* SHOT Interactive Card */
.shot-card-interactive {
  background-color: var(--shot-black) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: var(--shot-button-radius) !important;
  margin-bottom: 12px !important;
  transition: var(--shot-button-transition) !important;
  cursor: pointer !important;
}

.shot-card-interactive:hover {
  border-color: var(--shot-teal) !important;
  box-shadow: 0 0 0 1px var(--shot-teal) !important;
}

/* SHOT Pillar-Specific Color Classes */
.shot-take-your-shot,
.shot-teal {
  color: var(--shot-teal) !important;
}

.shot-own-it,
.shot-purple {
  color: var(--shot-purple) !important;
}

.shot-make-impact,
.shot-gold {
  color: var(--shot-gold) !important;
}

/* Background Variants */
.shot-bg-teal {
  background-color: var(--shot-teal) !important;
}

.shot-bg-purple {
  background-color: var(--shot-purple) !important;
}

.shot-bg-gold {
  background-color: var(--shot-gold) !important;
}

.shot-bg-gray {
  background-color: #6b7280 !important;
}

/* Ensure orange buttons always have black text */
ion-button[style*="--background: #FF6F3C"],
ion-button[style*="--background:#FF6F3C"],
ion-button[style*='--background: var(--shot-burnt-orange)'],
ion-button[style*='--background:var(--shot-burnt-orange)'],
.shot-button-orange {
  --color: #000000 !important;
  color: #000000 !important;
}

/* SHOT Navigation Items */
.shot-nav-item {
  display: flex !important;
  align-items: center !important;
  padding: 16px !important;
  background-color: var(--shot-black) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: var(--shot-button-radius) !important;
  transition: var(--shot-button-transition) !important;
  cursor: pointer !important;
  margin-bottom: 8px !important;
}

.shot-nav-item:hover {
  background-color: rgba(26, 188, 156, 0.1) !important;
  border-left: 4px solid var(--shot-teal) !important;
}

/* SHOT Evaluation Dots - Matching Line Colors */
.shot-evaluation-dot {
  width: 16px !important;
  height: 16px !important;
  border-radius: 50% !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  display: inline-block !important;
  margin: 0 2px !important;
}

.shot-evaluation-dot--not-started {
  background-color: #6b7280 !important; /* Gray - Not rated */
}

.shot-evaluation-dot--low {
  background-color: #ef4444 !important; /* Red - matches red evaluation line */
}

.shot-evaluation-dot--average {
  background-color: #f59e0b !important; /* Orange/amber - matches orange evaluation line */
}

.shot-evaluation-dot--good {
  background-color: #f59e0b !important; /* Orange/amber - matches orange evaluation line */
}

.shot-evaluation-dot--high {
  background-color: #10b981 !important; /* Green - matches green evaluation line */
}

/* SHOT Evaluation Dots Container */
.shot-evaluation-dots {
  display: flex !important;
  align-items: center !important;
  gap: 4px !important;
}

/* Size variants */
.shot-evaluation-dots--small .shot-evaluation-dot {
  width: 12px !important;
  height: 12px !important;
  margin: 0 1px !important;
}

.shot-evaluation-dots--large .shot-evaluation-dot {
  width: 20px !important;
  height: 20px !important;
  margin: 0 3px !important;
}

/* SHOT Form Elements */
.shot-input,
ion-input,
ion-textarea,
ion-select {
  --background: rgba(255, 255, 255, 0.1) !important;
  --border-color: rgba(255, 255, 255, 0.2) !important;
  --border-radius: var(--shot-button-radius) !important;
  --color: var(--shot-white) !important;
  --padding-start: 16px !important;
  --padding-end: 16px !important;
  font-family: var(--shot-font-body) !important;
}

.shot-input:focus-within,
ion-input.has-focus,
ion-textarea.has-focus,
ion-select.has-focus {
  --border-color: var(--shot-teal) !important;
  --highlight-color: var(--shot-teal) !important;
}

/* SHOT Searchbar */
ion-searchbar {
  --background: rgba(255, 255, 255, 0.1) !important;
  --border-radius: var(--shot-button-radius) !important;
  --color: var(--shot-white) !important;
  --icon-color: var(--shot-teal) !important;
  --clear-button-color: var(--shot-teal) !important;
}

/* SHOT Chip Styling */
ion-chip {
  --background: rgba(255, 255, 255, 0.1) !important;
  --color: var(--shot-white) !important;
  --border-radius: 16px !important;
  font-family: var(--shot-font-body) !important;
  font-weight: 500 !important;
}

/* SHOT Badge System */
.shot-badge {
  font-family: var(--shot-font-body) !important;
  font-weight: 500 !important;
  font-size: 0.75rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  padding: 4px 8px !important;
  border-radius: 12px !important;
  display: inline-flex !important;
  align-items: center !important;
}

.shot-badge-teal {
  background-color: var(--shot-teal) !important;
  color: var(--shot-black) !important;
}

.shot-badge-purple {
  background-color: var(--shot-purple) !important;
  color: var(--shot-white) !important;
}

.shot-badge-gold {
  background-color: var(--shot-gold) !important;
  color: var(--shot-black) !important;
}

/* Pre-assessment badge specifically */
.shot-badge-pre-assessment {
  background-color: var(--shot-teal) !important;
  color: var(--shot-black) !important;
  font-weight: 700 !important;
  font-size: 10px !important;
  letter-spacing: 0.5px !important;
  border-radius: 10px !important;
  padding: 4px 8px !important;
  display: inline-flex !important;
  align-items: center !important;
  margin-left: 12px !important;
  vertical-align: middle !important;
}

.shot-badge-pre-assessment ion-icon {
  font-size: 10px !important;
  margin-right: 3px !important;
  color: var(--shot-black) !important;
}

/* SHOT Loading and Status Indicators */
.shot-loading {
  color: var(--shot-teal) !important;
}

.shot-success {
  color: var(--shot-teal) !important;
}

.shot-warning {
  color: var(--shot-gold) !important;
}

.shot-error {
  color: var(--shot-red) !important;
}

/* Utility Classes for Consistent Spacing */
.shot-rounded {
  border-radius: var(--shot-button-radius) !important;
}

.shot-rounded-full {
  border-radius: 50% !important;
}

.shot-text-heading {
  font-family: var(--shot-font-heading) !important;
}

.shot-text-body {
  font-family: var(--shot-font-body) !important;
}

/* Force border radius on any remaining non-compliant elements */
* {
  --border-radius: var(--shot-button-radius);
}

/* Specific overrides for problematic elements */
.pagination-button,
.nav-button,
.action-button,
.modal-button,
.alert-button,
[role="button"] {
  border-radius: var(--shot-button-radius) !important;
}

/* Animation for pulsing elements to draw attention */
@keyframes pulse {
  0% { transform: scale(1); box-shadow: 0 4px 10px rgba(107, 0, 219, 0.4); }
  50% { transform: scale(1.05); box-shadow: 0 4px 15px rgba(107, 0, 219, 0.7); }
  100% { transform: scale(1); box-shadow: 0 4px 10px rgba(107, 0, 219, 0.4); }
}

.pulse-animation {
  animation: pulse 2s infinite ease-in-out;
}

/* Disable animations for reduced-motion preference */
@media (prefers-reduced-motion: reduce) {
  .pulse-animation {
    animation: none !important;
  }
}

/* High contrast support */
@media (prefers-contrast: high) {
  .shot-card,
  .shot-nav-item {
    border: 2px solid var(--shot-white) !important;
  }
  
  .shot-button-primary,
  .shot-button-secondary,
  .shot-button-tertiary,
  .shot-button-outline {
    border: 2px solid var(--shot-white) !important;
  }
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  .shot-button-primary,
  .shot-button-secondary,
  .shot-button-tertiary,
  .shot-button-outline {
    min-height: 48px !important; /* Larger touch targets on mobile */
    padding: 14px 24px !important;
  }
  
  h1, .shot-h1 {
    font-size: 1.75rem !important;
  }
  
  h2, .shot-h2 {
    font-size: 1.375rem !important;
  }
  
  /* Prevent autofocus keyboard issues on mobile searchbars */
  ion-searchbar input {
    /* Prevent autofocus */
    pointer-events: none !important;
    animation: enable-after-delay 0.5s forwards !important;
  }
  
  @keyframes enable-after-delay {
    to { pointer-events: auto; }
  }
}

/* Print styles */
@media print {
  .shot-card {
    border: 1px solid #000 !important;
    background: white !important;
  }
  
  h1, h2, h3, p {
    color: #000 !important;
  }
}

/* Dark mode (default for SHOT) */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
  
  body,
  ion-content,
  ion-app {
    background-color: var(--shot-black) !important;
    color: var(--shot-white) !important;
  }
}

/* Ensure ion-title uses SHOT typography */
ion-toolbar ion-title {
  font-family: var(--shot-font-heading) !important;
  font-weight: 600 !important;
  color: var(--shot-white) !important;
}

/* Override any inline styles that might conflict */
[style*="border-radius: 0"] {
  border-radius: var(--shot-button-radius) !important;
}

[style*="border-radius:0"] {
  border-radius: var(--shot-button-radius) !important;
}

/* Final catch-all for button consistency */
button:not(.shot-button-small):not(.shot-btn-small),
ion-button:not([size="small"]) {
  min-height: var(--shot-button-height) !important;
  border-radius: var(--shot-button-radius) !important;
  font-family: var(--shot-font-heading) !important;
  font-weight: var(--shot-button-font-weight) !important;
  text-transform: uppercase !important;
  letter-spacing: var(--shot-button-letter-spacing) !important;
}

/* SHOT Logo Container - Yellow Box Removed */
.shot-logo-container {
  /* Yellow box styling removed per request */
  display: inline-block !important;
  transition: all 0.3s ease !important;
}

.shot-logo-container:hover {
  /* Subtle hover effect without yellow box */
  transform: scale(1.02) !important;
}

/* Clean logo container in toolbar */
.ion-toolbar-custom .shot-logo-container,
ion-toolbar .shot-logo-container,
.standard-header-toolbar .shot-logo-container {
  /* Yellow box styling removed per request */
  display: inline-block !important;
}

/* Player Status Pills - Core SHOT Style */
.shot-status-pill {
  border-radius: 20px !important;
  padding: 6px 12px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  min-width: 80px !important;
  text-align: center !important;
  display: inline-block !important;
}

.shot-status-active {
  background-color: var(--shot-green) !important;
  color: var(--shot-white) !important;
  border: none !important;
}

.shot-status-inactive {
  background-color: var(--shot-grey) !important;
  color: var(--shot-white) !important;
  border: none !important;
}

.shot-status-invited {
  background-color: var(--shot-gold) !important;
  color: var(--shot-black) !important;
  border: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 4px !important;
}

/* ==== ENHANCED ROLE SELECTOR STYLES ==== */
/* More prominent role selector button with accent colors */
.role-selector-button {
  position: relative !important;
  padding: 8px 20px !important;
  min-width: 160px !important;
  border-radius: 25px !important;
  font-weight: 600 !important;
  font-size: 15px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  overflow: hidden !important;
}

/* Add subtle glow effect for better visibility */
.role-selector-button[style*="#DC2626"] {
  box-shadow: 0 0 20px rgba(220, 38, 38, 0.3) !important;
  border: 2px solid rgba(220, 38, 38, 0.5) !important;
}

.role-selector-button[style*="#FF6F3C"] {
  box-shadow: 0 0 20px rgba(255, 111, 60, 0.3) !important;
  border: 2px solid rgba(255, 111, 60, 0.5) !important;
}

.role-selector-button[style*="#22C55E"] {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.3) !important;
  border: 2px solid rgba(34, 197, 94, 0.5) !important;
}

.role-selector-button[style*="#9333EA"] {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.3) !important;
  border: 2px solid rgba(147, 51, 234, 0.5) !important;
}

/* Add animated background pattern for active role */
.role-selector-button::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: -100% !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;
  transition: left 0.5s ease !important;
}

.role-selector-button:hover::before {
  left: 100% !important;
}

/* Enhanced role dropdown items */
.role-selector-popover ion-item {
  transition: all 0.2s ease !important;
  position: relative !important;
}

/* Larger color indicator in dropdown */
.role-color-indicator {
  width: 6px !important;
  height: 24px !important;
  margin-right: 16px !important;
  border-radius: 3px !important;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.2) !important;
}

/* Hover effect for dropdown items */
.role-selector-popover ion-item:hover .role-color-indicator {
  width: 8px !important;
  box-shadow: 0 0 12px currentColor !important;
}

/* Role-specific badges and indicators */
.shot-role-badge {
  display: inline-flex !important;
  align-items: center !important;
  padding: 4px 12px !important;
  border-radius: 16px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  gap: 4px !important;
}

.shot-role-badge--superadmin {
  background-color: var(--shot-role-superadmin) !important;
  color: var(--shot-white) !important;
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3) !important;
}

.shot-role-badge--coach {
  background-color: var(--shot-role-coach) !important;
  color: var(--shot-black) !important;
  box-shadow: 0 2px 8px rgba(255, 111, 60, 0.3) !important;
}

.shot-role-badge--player {
  background-color: var(--shot-role-player) !important;
  color: var(--shot-white) !important;
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3) !important;
}

.shot-role-badge--club-admin {
  background-color: var(--shot-role-club-admin) !important;
  color: var(--shot-white) !important;
  box-shadow: 0 2px 8px rgba(147, 51, 234, 0.3) !important;
}

/* Role indicator dots */
.shot-role-indicator {
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  display: inline-block !important;
  position: relative !important;
}

.shot-role-indicator--superadmin {
  background-color: var(--shot-role-superadmin) !important;
  box-shadow: 0 0 8px rgba(220, 38, 38, 0.5) !important;
}

.shot-role-indicator--coach {
  background-color: var(--shot-role-coach) !important;
  box-shadow: 0 0 8px rgba(255, 111, 60, 0.5) !important;
}

.shot-role-indicator--player {
  background-color: var(--shot-role-player) !important;
  box-shadow: 0 0 8px rgba(34, 197, 94, 0.5) !important;
}

.shot-role-indicator--club-admin {
  background-color: var(--shot-role-club-admin) !important;
  box-shadow: 0 0 8px rgba(147, 51, 234, 0.5) !important;
}

/* ==== CONSOLIDATED COACH STYLES ==== */
/* Styles consolidated from CoachStyles.css and SearchStyles.css */

/* Global dark theme background */
.bg-black {
  --background: #000000 !important;
  background: #000000 !important;
}

/* Dark theme headers and text */
.text-white {
  --color: #ffffff !important;
  color: #ffffff !important;
}

/* Enhanced searchbar styling for dark theme */
ion-searchbar.custom-searchbar,
ion-searchbar.custom-dark-searchbar,
.custom-searchbar {
  --background: #1f1f1f !important;
  --color: white !important;
  --placeholder-color: rgba(255, 255, 255, 0.7) !important;
  --placeholder-opacity: 1 !important;
  --icon-color: #9ca3af !important;
  --clear-button-color: #9ca3af !important;
  --border-radius: var(--shot-button-radius) !important;
  background: #1f2937 !important;
  border-radius: var(--shot-button-radius) !important;
  margin-bottom: 16px !important;
}

/* Search input styling */
ion-searchbar.custom-searchbar .searchbar-input,
ion-searchbar.custom-dark-searchbar .searchbar-input,
.custom-searchbar input {
  background: #1f1f1f !important;
  color: white !important;
}

/* Search bar parts styling */
.custom-searchbar::part(icon) {
  color: white !important;
  opacity: 0.7 !important;
}

.custom-searchbar::part(input) {
  color: white !important;
}

.custom-searchbar::part(cancel-button) {
  color: white !important;
}

/* Force the searchbar to always have dark theme */
.sc-ion-searchbar-md {
  --background: #1f1f1f !important;
  --color: white !important;
  background: #1f1f1f !important;
  color: white !important;
}

/* Search container */
.search-container {
  margin-bottom: 16px !important;
  padding: 0 16px !important;
}

/* Override segment control styling */
ion-segment {
  --background: #1f1f1f !important;
  background: #1f1f1f !important;
}

ion-segment-button {
  --color: white !important;
  --color-checked: #111111 !important;
  --background-checked: var(--shot-gold) !important;
}

.segment-button-checked {
  background: var(--shot-gold) !important;
}

/* Override list item styling */
ion-item.bg-gray-700 {
  --background: #374151 !important;
  --color: white !important;
}

/* Override checkbox styling */
ion-checkbox {
  --background: #1f1f1f !important;
  --background-checked: var(--shot-gold) !important;
  --border-color: #4b5563 !important;
  --border-color-checked: var(--shot-gold) !important;
  --checkmark-color: black !important;
}

/* Custom input fields */
ion-input.bg-gray-600 {
  --background: #4b5563 !important;
  --color: white !important;
  --placeholder-color: #9ca3af !important;
}

ion-textarea {
  --background: #4b5563 !important;
  --color: white !important;
  --placeholder-color: #9ca3af !important;
}

/* Coach accent color for icons and highlights */
.coach-accent {
  color: var(--shot-gold) !important;
}

/* Dark surfaces for cards and containers */
.coach-surface {
  background-color: #1a1a1a !important;
}

/* Secondary surface for nested containers */
.coach-surface-secondary {
  background-color: #232323 !important;
}

/* SHOT Team Header Banner Styles - Enhanced */
.team-header-banner {
  padding: 0;
  margin-bottom: 16px;
}

.team-header-banner .team-entity-card-container {
  background-color: rgba(26, 188, 156, 0.05);
  border: 1px solid rgba(26, 188, 156, 0.2);
  border-radius: var(--shot-button-radius);
  padding: 16px;
  position: relative;
  overflow: hidden;
}

.team-header-banner .team-entity-card-container::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: var(--shot-teal);
}

.team-header-banner .entity-card {
  background-color: transparent;
  border: none;
  box-shadow: none;
  margin-bottom: 0;
}

.team-header-banner .team-entity-actions {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* SHOT Evaluation Blocks - Enhanced Design */

/* Evaluation Card Base Styling */
.shot-evaluation-card {
  background-color: var(--shot-black);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--shot-button-radius);
  overflow: hidden;
  transition: all 0.2s ease;
  position: relative;
  margin-bottom: 12px;
}

.shot-evaluation-card:hover {
  border-color: var(--shot-teal);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(26, 188, 156, 0.1);
}

/* Evaluation Card Header - With Gradient Based on Type */
.shot-evaluation-header {
  padding: 16px;
  position: relative;
  overflow: hidden;
}

.shot-evaluation-header--match {
  background: linear-gradient(135deg, rgba(247, 182, 19, 0.15) 0%, rgba(0, 0, 0, 0) 100%);
  border-bottom: 1px solid rgba(247, 182, 19, 0.2);
}

.shot-evaluation-header--training {
  background: linear-gradient(135deg, rgba(26, 188, 156, 0.15) 0%, rgba(0, 0, 0, 0) 100%);
  border-bottom: 1px solid rgba(26, 188, 156, 0.2);
}

.shot-evaluation-header--assessment {
  background: linear-gradient(135deg, rgba(107, 0, 219, 0.15) 0%, rgba(0, 0, 0, 0) 100%);
  border-bottom: 1px solid rgba(107, 0, 219, 0.2);
}

/* Event Type Badge */
.shot-event-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.shot-event-badge ion-icon {
  margin-right: 4px;
  font-size: 12px;
}

.shot-event-badge--match {
  background-color: rgba(247, 182, 19, 0.15);
  color: var(--shot-gold);
  border: 1px solid rgba(247, 182, 19, 0.3);
}

.shot-event-badge--training {
  background-color: rgba(26, 188, 156, 0.15);
  color: var(--shot-teal);
  border: 1px solid rgba(26, 188, 156, 0.3);
}

.shot-event-badge--assessment {
  background-color: rgba(107, 0, 219, 0.15);
  color: var(--shot-purple);
  border: 1px solid rgba(107, 0, 219, 0.3);
}

/* Event Title */
.shot-event-title {
  font-family: var(--shot-font-heading);
  font-weight: 700;
  font-size: 1.125rem;
  margin-bottom: 4px;
  color: var(--shot-white);
  line-height: 1.3;
}

/* Event Meta Info */
.shot-event-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
}

/* Evaluation Progress Section - Enhanced */
.shot-evaluation-progress {
  padding: 16px;
  position: relative;
}

/* Multi-state Progress Indicator */
.shot-progress-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  position: relative;
}

/* Progress Text */
.shot-progress-text {
  flex: 1;
  font-size: 0.875rem;
  font-weight: 500;
}

.shot-progress-text--not-started {
  color: #6b7280;
}

.shot-progress-text--in-progress {
  color: var(--shot-gold);
}

.shot-progress-text--completed {
  color: var(--shot-teal);
}

/* Progress Percentage Badge */
.shot-progress-percentage {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  font-size: 0.875rem;
  font-weight: 700;
  position: relative;
}

.shot-progress-percentage--not-started {
  color: #6b7280;
  background-color: rgba(107, 114, 128, 0.1);
  border: 1px solid rgba(107, 114, 128, 0.3);
}

.shot-progress-percentage--in-progress {
  color: var(--shot-gold);
  background-color: rgba(247, 182, 19, 0.1);
  border: 1px solid rgba(247, 182, 19, 0.3);
}

.shot-progress-percentage--completed {
  color: var(--shot-teal);
  background-color: rgba(26, 188, 156, 0.1);
  border: 1px solid rgba(26, 188, 156, 0.3);
}

/* Enhanced Progress Bar with Clear Segments */
.shot-progress-bar-container {
  height: 12px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
  margin-bottom: 12px;
}

.shot-progress-bar {
  height: 100%;
  border-radius: 6px;
  transition: width 0.3s ease-out;
}

.shot-progress-bar--not-started {
  background-color: #6b7280;
  width: 0;
}

.shot-progress-bar--in-progress {
  background: linear-gradient(90deg, var(--shot-gold), var(--shot-purple));
}

.shot-progress-bar--completed {
  background: linear-gradient(90deg, var(--shot-teal), #2ae5bc);
}

/* Player Evaluation Segments */
.shot-player-segments {
  display: flex;
  gap: 2px;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.shot-player-segment {
  flex: 1;
  height: 100%;
  border-right: 1px dashed rgba(255, 255, 255, 0.2);
}

.shot-player-segment:last-child {
  border-right: none;
}

/* Progress Details with Player Counters */
.shot-progress-details {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.shot-player-counter {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  margin-right: 16px;
}

.shot-player-counter ion-icon {
  margin-right: 4px;
  font-size: 16px;
}

.shot-player-counter--evaluated {
  color: var(--shot-teal);
}

.shot-player-counter--remaining {
  color: var(--shot-gold);
}

.shot-player-counter--not-started {
  color: #6b7280;
}

/* Status Text */
.shot-status-text {
  font-size: 0.75rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.shot-status-text--not-started {
  color: #6b7280;
}

.shot-status-text--in-progress {
  color: var(--shot-gold);
}

.shot-status-text--completed {
  color: var(--shot-teal);
}

.shot-status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.shot-status-dot--not-started {
  background-color: #6b7280;
}

.shot-status-dot--in-progress {
  background-color: var(--shot-gold);
  box-shadow: 0 0 6px rgba(247, 182, 19, 0.5);
}

.shot-status-dot--completed {
  background-color: var(--shot-teal);
  box-shadow: 0 0 6px rgba(26, 188, 156, 0.5);
}

/* Save Status */
.shot-save-status {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
}

/* Evaluation Actions Row */
.shot-evaluation-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Action Button */
.shot-evaluation-action-button {
  font-family: var(--shot-font-heading);
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 8px 16px;
  border-radius: var(--shot-button-radius);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.shot-evaluation-action-button--not-started {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--shot-white);
}

.shot-evaluation-action-button--in-progress {
  background-color: var(--shot-gold);
  color: var(--shot-black);
}

.shot-evaluation-action-button--completed {
  background-color: var(--shot-teal);
  color: var(--shot-black);
}

.shot-evaluation-action-button:hover {
  transform: translateY(-1px);
}

/* Enhanced Progress Circle (SVG) */
.shot-progress-circle-container {
  position: relative;
  width: 48px;
  height: 48px;
}

.shot-progress-circle-background {
  fill: none;
  stroke: rgba(255, 255, 255, 0.1);
  stroke-width: 4;
}

.shot-progress-circle--not-started {
  fill: none;
  stroke: #6b7280;
  stroke-width: 4;
  stroke-linecap: round;
}

.shot-progress-circle--in-progress {
  fill: none;
  stroke: var(--shot-gold);
  stroke-width: 4;
  stroke-linecap: round;
}

.shot-progress-circle--completed {
  fill: none;
  stroke: var(--shot-teal);
  stroke-width: 4;
  stroke-linecap: round;
}

.shot-progress-circle-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.75rem;
  font-weight: 700;
  text-align: center;
}

.shot-progress-circle-text--not-started {
  color: #6b7280;
}

.shot-progress-circle-text--in-progress {
  color: var(--shot-gold);
}

.shot-progress-circle-text--completed {
  color: var(--shot-teal);
}

/* Player Markers for Progress Bar */
.shot-player-markers {
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-evenly;
  padding: 0 2px;
}

.shot-player-marker {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  transform: translateY(4px);
  transition: all 0.2s ease;
}

.shot-player-marker--evaluated {
  background-color: var(--shot-teal);
  transform: translateY(0);
  box-shadow: 0 0 4px rgba(26, 188, 156, 0.5);
}

/* Remaining Players Number */
.shot-remaining-counter {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
}

.shot-remaining-counter--not-started {
  background-color: rgba(107, 114, 128, 0.2);
  color: #6b7280;
}

.shot-remaining-counter--in-progress {
  background-color: rgba(247, 182, 19, 0.2);
  color: var(--shot-gold);
}

.shot-remaining-counter--completed {
  background-color: rgba(26, 188, 156, 0.2);
  color: var(--shot-teal);
}

/* Evaluation Cards Animation */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.shot-evaluation-card {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Evaluation Card - Status Indicator Strip */
.shot-status-indicator-strip {
  position: absolute;
  top: 0;
  left: 0;
  height: 4px;
  width: 100%;
}

.shot-status-indicator-strip--not-started {
  background-color: #6b7280;
}

.shot-status-indicator-strip--in-progress {
  background: linear-gradient(90deg, var(--shot-gold) 0%, var(--shot-purple) 100%);
}

.shot-status-indicator-strip--completed {
  background: linear-gradient(90deg, var(--shot-teal) 0%, #2ae5bc 100%);
}

/* SHOT SignIn Verification Banner Styles */
.verification-banner {
  margin: 16px;
  border: 2px solid;
  border-radius: var(--shot-button-radius);
  padding: 16px;
  position: relative;
  animation: slideIn 0.3s ease-out;
  backdrop-filter: blur(10px);
}

@keyframes slideIn {
  from { 
    transform: translateY(-20px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}

.verification-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding-right: 24px;
}

.verification-icon {
  font-size: 24px;
  flex-shrink: 0;
  margin-top: 2px;
}

.verification-text {
  flex: 1;
}

.verification-title {
  font-family: var(--shot-font-heading);
  font-weight: 600;
  font-size: 1rem;
  margin: 0 0 4px 0;
  color: var(--shot-white);
}

.verification-message {
  font-family: var(--shot-font-body);
  font-size: 0.875rem;
  line-height: 1.4;
  margin: 0 0 8px 0;
  color: rgba(255, 255, 255, 0.9);
}

.resend-button {
  font-size: 0.75rem;
  --color: var(--shot-white);
  --border-color: rgba(255, 255, 255, 0.3);
  margin-top: 4px;
}

.close-banner {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 24px;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-banner:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--shot-white);
}

/* Mobile optimizations for verification banner */
@media (max-width: 768px) {
  .verification-content {
    flex-direction: column;
    gap: 8px;
    padding-right: 32px;
  }

  .verification-icon {
    align-self: flex-start;
  }
}
