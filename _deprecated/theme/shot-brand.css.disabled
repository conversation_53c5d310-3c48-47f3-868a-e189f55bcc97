/* SHOT Brand Design System Implementation - UPDATED FOR FULL COMPLIANCE */
/* Based on "SHOT UX Design System: Visualising the Movement" */
/* FIXES: Rounded buttons, consistent styling, proper typography, brand compliance */

/* Import SHOT Brand Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* SHOT Brand Color System */
:root {
  /* SHOT Core Pillar Colors */
  --shot-teal: #1ABC9C;           /* Take Your Shot */
  --shot-purple: #6B00DB;         /* Own It */
  --shot-gold: #F7B613;           /* Make Impact */
  
  /* SHOT Foundation Colors */
  --shot-black: #000000;
  --shot-white: #FFFFFF;
  
  /* SHOT PERFORM System Colors */
  --shot-electric-blue: #296DFF;   /* Technical */
  --shot-forest-green: #2E8B57;   /* Physical */
  --shot-burnt-orange: #FF6F3C;   /* Psychological */
  --shot-warm-coral: #FF5D73;     /* Social */
  --shot-charcoal-grey: #5C5C5C;  /* Tactical/Positional */
  
  /* SHOT Functional Colors */
  --shot-red: #E63946;            /* Errors/Warnings only */
  
  /* SHOT Typography Variables */
  --shot-font-heading: 'Poppins', sans-serif;
  --shot-font-body: 'Montserrat', sans-serif;
  
  /* SHOT Button System - UPDATED FOR ROUNDED EDGES */
  --shot-button-radius: 8px;      /* Consistent rounded corners for ALL buttons */
  --shot-button-height: 44px;     /* Standard button height */
  --shot-button-padding: 12px 24px;
  
  /* Update Ionic Variables to use SHOT Brand Colors */
  --ion-color-primary: var(--shot-purple);
  --ion-color-primary-rgb: 107, 0, 219;
  --ion-color-primary-contrast: var(--shot-white);
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #5e00c1;
  --ion-color-primary-tint: #7a1adf;
  
  --ion-color-success: var(--shot-teal);
  --ion-color-success-rgb: 26, 188, 156;
  --ion-color-success-contrast: var(--shot-black);
  --ion-color-success-contrast-rgb: 0, 0, 0;
  --ion-color-success-shade: #17a589;
  --ion-color-success-tint: #31c3a6;
  
  --ion-color-tertiary: var(--shot-gold);
  --ion-color-tertiary-rgb: 247, 182, 19;
  --ion-color-tertiary-contrast: var(--shot-black);
  --ion-color-tertiary-contrast-rgb: 0, 0, 0;
  --ion-color-tertiary-shade: #d9a011;
  --ion-color-tertiary-tint: #f8bd2b;
  
  --ion-color-danger: var(--shot-red);
  --ion-color-danger-rgb: 230, 57, 70;
  --ion-color-danger-contrast: var(--shot-white);
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #cb333e;
  --ion-color-danger-tint: #ed576b;
}

/* CRITICAL: Global Ionic Button Override for Rounded Edges */
ion-button {
  --border-radius: var(--shot-button-radius) !important;
}

/* SHOT Typography System */
/* Based on Section 19: SHOT Typography: The Voice Made Visual */

/* H1 - Primary Headlines/Taglines */
.shot-h1, h1.shot-heading {
  font-family: var(--shot-font-heading) !important;
  font-weight: 800; /* ExtraBold */
  text-transform: uppercase;
  letter-spacing: 0.15em; /* Generous letter spacing */
  font-size: 2rem;
  line-height: 1.2;
  color: var(--shot-white);
}

/* H2 - Major Section Titles/Secondary Headlines */
.shot-h2, h2.shot-heading {
  font-family: var(--shot-font-heading) !important;
  font-weight: 700; /* Bold */
  font-size: 1.5rem;
  line-height: 1.3;
  letter-spacing: 0.05em;
  color: var(--shot-white);
}

/* H3 - Subheadings/Feature Titles */
.shot-h3, h3.shot-heading {
  font-family: var(--shot-font-body) !important;
  font-weight: 600; /* SemiBold */
  font-size: 1.25rem;
  line-height: 1.4;
  color: var(--shot-white);
}

/* Body Text */
.shot-body, p.shot-text {
  font-family: var(--shot-font-body) !important;
  font-weight: 400; /* Regular */
  font-size: 1rem;
  line-height: 1.6;
  color: var(--shot-white);
}

/* UI Elements & Labels */
.shot-ui-text {
  font-family: var(--shot-font-body) !important;
  font-weight: 500; /* Medium */
  font-size: 0.875rem;
  line-height: 1.4;
  color: var(--shot-white);
}

/* Caption Text */
.shot-caption {
  font-family: var(--shot-font-body) !important;
  font-weight: 400; /* Regular */
  font-size: 0.75rem;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.7);
}

/* SHOT Pillar-Specific Color Classes */
.shot-take-your-shot {
  color: var(--shot-teal) !important;
}

.shot-own-it {
  color: var(--shot-purple) !important;
}

.shot-make-impact {
  color: var(--shot-gold) !important;
}

/* SHOT Button System - UPDATED WITH ROUNDED EDGES */
.shot-button-primary {
  font-family: var(--shot-font-heading) !important;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  background-color: var(--shot-teal);
  color: var(--shot-black);
  border: none;
  padding: var(--shot-button-padding);
  border-radius: var(--shot-button-radius) !important; /* UPDATED: Rounded edges */
  min-height: var(--shot-button-height);
  transition: all 0.2s ease;
  cursor: pointer;
}

.shot-button-primary:hover {
  background-color: #17a589;
  transform: translateY(-1px);
}

.shot-button-secondary {
  font-family: var(--shot-font-heading) !important;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  background-color: transparent;
  color: var(--shot-teal);
  border: 2px solid var(--shot-teal);
  padding: 10px 22px;
  border-radius: var(--shot-button-radius) !important; /* UPDATED: Rounded edges */
  min-height: var(--shot-button-height);
  transition: all 0.2s ease;
  cursor: pointer;
}

.shot-button-secondary:hover {
  background-color: var(--shot-teal);
  color: var(--shot-black);
}

/* SHOT Pillar Buttons - Take Your Shot (Teal) */
.shot-button-teal {
  font-family: var(--shot-font-heading) !important;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  background-color: var(--shot-teal);
  color: var(--shot-black);
  border: none;
  padding: var(--shot-button-padding);
  border-radius: var(--shot-button-radius) !important;
  min-height: var(--shot-button-height);
  transition: all 0.2s ease;
  cursor: pointer;
}

.shot-button-teal:hover {
  background-color: #17a589;
  transform: translateY(-1px);
}

/* SHOT Pillar Buttons - Own It (Purple) */
.shot-button-purple {
  font-family: var(--shot-font-heading) !important;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  background-color: var(--shot-purple);
  color: var(--shot-white);
  border: none;
  padding: var(--shot-button-padding);
  border-radius: var(--shot-button-radius) !important;
  min-height: var(--shot-button-height);
  transition: all 0.2s ease;
  cursor: pointer;
}

.shot-button-purple:hover {
  background-color: #5e00c1;
  transform: translateY(-1px);
}

/* SHOT Pillar Buttons - Make Impact (Gold) */
.shot-button-gold {
  font-family: var(--shot-font-heading) !important;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  background-color: var(--shot-gold);
  color: var(--shot-black);
  border: none;
  padding: var(--shot-button-padding);
  border-radius: var(--shot-button-radius) !important;
  min-height: var(--shot-button-height);
  transition: all 0.2s ease;
  cursor: pointer;
}

.shot-button-gold:hover {
  background-color: #d9a011;
  transform: translateY(-1px);
}

/* SHOT Card System */
.shot-card {
  background-color: var(--shot-black);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px; /* Consistent rounded corners */
  padding: 0;
  margin-bottom: 16px;
  box-shadow: none;
}

.shot-card-header {
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.shot-card-title {
  font-family: var(--shot-font-heading) !important;
  font-weight: 600;
  font-size: 1.125rem;
  color: var(--shot-white);
  margin: 0;
}

.shot-card-content {
  padding: 16px;
}

/* SHOT Dashboard Specific Styles */
.shot-dashboard {
  background-color: var(--shot-black);
  color: var(--shot-white);
}

.shot-dashboard-title {
  font-family: var(--shot-font-heading) !important;
  font-weight: 700;
  font-size: 1.5rem;
  color: var(--shot-white);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 24px;
}

.shot-section-title {
  font-family: var(--shot-font-heading) !important;
  font-weight: 600;
  font-size: 1.125rem;
  color: var(--shot-teal);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 16px;
}

/* SHOT Navigation Items */
.shot-nav-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: var(--shot-black);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px; /* Consistent rounded corners */
  transition: all 0.2s ease;
  cursor: pointer;
  margin-bottom: 8px;
}

.shot-nav-item:hover {
  background-color: rgba(26, 188, 156, 0.1);
  border-left: 4px solid var(--shot-teal);
}

.shot-nav-item-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.shot-nav-item-content {
  flex: 1;
}

.shot-nav-item-title {
  font-family: var(--shot-font-heading) !important;
  font-weight: 600;
  font-size: 1rem;
  color: var(--shot-white);
  margin: 0 0 4px 0;
}

.shot-nav-item-subtitle {
  font-family: var(--shot-font-body) !important;
  font-weight: 400;
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.shot-nav-item-arrow {
  color: var(--shot-teal);
  width: 24px;
  height: 24px;
}

/* Global Typography Enforcement */
body, ion-content {
  font-family: var(--shot-font-body) !important;
  background-color: var(--shot-black) !important;
  color: var(--shot-white) !important;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--shot-font-heading) !important;
  color: var(--shot-white) !important;
}

/* Ionic Component Overrides for SHOT Compliance */
ion-title {
  font-family: var(--shot-font-heading) !important;
  font-weight: 600 !important;
  color: var(--shot-white) !important;
}

ion-card {
  border-radius: 8px !important;
}

ion-input, ion-textarea, ion-select {
  --border-radius: 8px !important;
}

ion-chip {
  --border-radius: 16px !important;
}

/* SHOT Team Card Styles */
.shot-team-card {
  background-color: var(--shot-black);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
}

.shot-team-card:hover {
  border-color: var(--shot-teal);
  box-shadow: 0 0 0 1px var(--shot-teal);
}

/* SHOT Badge System */
.shot-badge {
  font-family: var(--shot-font-body) !important;
  font-weight: 500;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 4px 8px;
  border-radius: 12px;
}

.shot-badge-teal {
  background-color: var(--shot-teal);
  color: var(--shot-black);
}

.shot-badge-purple {
  background-color: var(--shot-purple);
  color: var(--shot-white);
}

.shot-badge-gold {
  background-color: var(--shot-gold);
  color: var(--shot-black);
}

/* SHOT Evaluation Dots - Global System */
.shot-evaluation-dot {
  width: 16px !important;
  height: 16px !important;
  border-radius: 50% !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  display: inline-block !important;
}

.shot-evaluation-dot--not-started {
  background-color: #6b7280 !important; /* Gray */
}

.shot-evaluation-dot--low {
  background-color: var(--shot-red) !important; /* SHOT Red */
}

.shot-evaluation-dot--average {
  background-color: var(--shot-gold) !important; /* SHOT Gold */
}

.shot-evaluation-dot--high {
  background-color: var(--shot-teal) !important; /* SHOT Teal */
}

/* SHOT Form Elements */
.shot-input {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: var(--shot-white);
  padding: 12px 16px;
  font-family: var(--shot-font-body);
}

.shot-input:focus {
  border-color: var(--shot-teal);
  outline: none;
  box-shadow: 0 0 0 2px rgba(26, 188, 156, 0.2);
}

/* SHOT Loading and Status Indicators */
.shot-loading {
  color: var(--shot-teal);
}

.shot-success {
  color: var(--shot-teal);
}

.shot-warning {
  color: var(--shot-gold);
}

.shot-error {
  color: var(--shot-red);
}

/* Accessibility - WCAG AA Compliance */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast support */
@media (prefers-contrast: high) {
  .shot-card {
    border: 2px solid var(--shot-white);
  }
  
  .shot-nav-item {
    border: 2px solid var(--shot-white);
  }
  
  .shot-button-primary,
  .shot-button-teal,
  .shot-button-purple,
  .shot-button-gold {
    border: 2px solid var(--shot-white);
  }
}

/* SHOT Brand Spring Collection Transparency Effects */
.shot-hero-banner {
  position: relative !important;
  overflow: hidden !important;
  border-radius: 8px !important;
}

.shot-hero-banner::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.3) !important; /* 30% black transparency */
  backdrop-filter: blur(1px) !important;
  z-index: 1 !important;
}

.shot-hero-banner > * {
  position: relative !important;
  z-index: 2 !important;
}

.shot-hero-content {
  background: rgba(0, 0, 0, 0.4) !important;
  backdrop-filter: blur(2px) !important;
  border-radius: 8px !important;
  padding: 24px !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Additional Utility Classes for SHOT Compliance */
.shot-rounded {
  border-radius: 8px !important;
}

.shot-rounded-full {
  border-radius: 50% !important;
}

.shot-text-heading {
  font-family: var(--shot-font-heading) !important;
}

.shot-text-body {
  font-family: var(--shot-font-body) !important;
}

.shot-bg-teal {
  background-color: var(--shot-teal) !important;
}

.shot-bg-purple {
  background-color: var(--shot-purple) !important;
}

.shot-bg-gold {
  background-color: var(--shot-gold) !important;
}

.shot-text-teal {
  color: var(--shot-teal) !important;
}

.shot-text-purple {
  color: var(--shot-purple) !important;
}

.shot-text-gold {
  color: var(--shot-gold) !important;
}