/* SHOT Brand Fonts - Per SHOT UX Design System */
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* Ionic Variables and Theming. For more info, please see:
http://ionicframework.com/docs/theming/ */

/** Ionic CSS Variables **/
:root {
  /* SHOT Brand Foundation */
  /* --ion-background-color: #000000; */ /* SHOT Black - REMOVED */
  --ion-item-background: #1a1a1a;
  
  --ion-text-color: #ffffff; /* SHOT White */
  
  /* SHOT Purple - Own It Pillar */
  --ion-color-primary: #6B00DB;
  --ion-color-primary-rgb: 107, 0, 219;
  --ion-color-primary-contrast: #ffffff;
  --ion-color-primary-contrast-rgb: 255, 255, 255;
  --ion-color-primary-shade: #5e00c1;
  --ion-color-primary-tint: #7a1adf;

  /** secondary **/
  --ion-color-secondary: #fff;
  --ion-color-secondary-rgb: 0, 0, 0;
  --ion-color-secondary-contrast: #ffffff;
  --ion-color-secondary-contrast-rgb: 255, 255, 255;
  --ion-color-secondary-shade: #000000;
  --ion-color-secondary-tint: #1a1a1a;

  /* SHOT Gold - Make Impact Pillar */
  --ion-color-tertiary: #F7B613;
  --ion-color-tertiary-rgb: 247, 182, 19;
  --ion-color-tertiary-contrast: #000000;
  --ion-color-tertiary-contrast-rgb: 0, 0, 0;
  --ion-color-tertiary-shade: #d9a011;
  --ion-color-tertiary-tint: #f8bd2b;

  /** coach - uses orange accent color **/
  --ion-color-coach: #F2B543;
  --ion-color-coach-rgb: 242, 181, 67;
  --ion-color-coach-contrast: #000000;
  --ion-color-coach-contrast-rgb: 0, 0, 0;
  --ion-color-coach-shade: #d5a03b;
  --ion-color-coach-tint: #f3bc56;

  /* SHOT Teal - Take Your Shot Pillar */
  --ion-color-success: #1ABC9C;
  --ion-color-success-rgb: 26, 188, 156;
  --ion-color-success-contrast: #000000;
  --ion-color-success-contrast-rgb: 0, 0, 0;
  --ion-color-success-shade: #17a589;
  --ion-color-success-tint: #31c3a6;

  /** warning **/
  --ion-color-warning: #ffc409;
  --ion-color-warning-rgb: 255, 196, 9;
  --ion-color-warning-contrast: #000000;
  --ion-color-warning-contrast-rgb: 0, 0, 0;
  --ion-color-warning-shade: #e0ac08;
  --ion-color-warning-tint: #ffca22;

  /* SHOT Red - Functional Use Only */
  --ion-color-danger: #E63946;
  --ion-color-danger-rgb: 230, 57, 70;
  --ion-color-danger-contrast: #ffffff;
  --ion-color-danger-contrast-rgb: 255, 255, 255;
  --ion-color-danger-shade: #cb333e;
  --ion-color-danger-tint: #ed576b;

  /** dark **/
  --ion-color-dark: #fff;
  --ion-color-dark-rgb: 34, 36, 40;
  --ion-color-dark-contrast: #ffffff;
  --ion-color-dark-contrast-rgb: 255, 255, 255;
  --ion-color-dark-shade: #1e2023;
  --ion-color-dark-tint: #383a3e;

  /** medium **/
  --ion-color-medium: #92949c;
  --ion-color-medium-rgb: 146, 148, 156;
  --ion-color-medium-contrast: #ffffff;
  --ion-color-medium-contrast-rgb: 255, 255, 255;
  --ion-color-medium-shade: #808289;
  --ion-color-medium-tint: #9d9fa6;

  /** light **/
  --ion-color-light: #fff;
  --ion-color-light-rgb: 244, 245, 248;
  --ion-color-light-contrast: #000000;
  --ion-color-light-contrast-rgb: 0, 0, 0;
  --ion-color-light-shade: #d7d8da;
  --ion-color-light-tint: #f5f6f9;

  /* SHOT Brand Typography System */
  --font-heading: 'Poppins', sans-serif;    /* Headlines, Titles, CTAs */
  --font-body: 'Montserrat', sans-serif;    /* Body copy, UI text */
  
  /* SHOT Brand Color Variables */
  --shot-teal: #1ABC9C;      /* Take Your Shot */
  --shot-purple: #6B00DB;    /* Own It */
  --shot-gold: #F7B613;      /* Make Impact */
  --shot-black: #1a1a1a;     /* Foundation - Changed from pure black */
  --shot-white: #FFFFFF;     /* Foundation */
  --shot-red: #E63946;       /* Functional only */
}

@media (prefers-color-scheme: dark) {
  /*
   * Dark Colors
   * -------------------------------------------
   */

  body {
    --ion-color-primary: #6b00db;
    --ion-color-primary-rgb: 66,140,255;
    --ion-color-primary-contrast: #ffffff;
    --ion-color-primary-contrast-rgb: 255,255,255;
    --ion-color-primary-shade: #3a7be0;
    --ion-color-primary-tint: #5598ff;

    --ion-color-secondary: #50c8ff;
    --ion-color-secondary-rgb: 80,200,255;
    --ion-color-secondary-contrast: #ffffff;
    --ion-color-secondary-contrast-rgb: 255,255,255;
    --ion-color-secondary-shade: #46b0e0;
    --ion-color-secondary-tint: #62ceff;

    --ion-color-tertiary: #6a64ff;
    --ion-color-tertiary-rgb: 106,100,255;
    --ion-color-tertiary-contrast: #ffffff;
    --ion-color-tertiary-contrast-rgb: 255,255,255;
    --ion-color-tertiary-shade: #5d58e0;
    --ion-color-tertiary-tint: #7974ff;

    --ion-color-success: #2fdf75;
    --ion-color-success-rgb: 47,223,117;
    --ion-color-success-contrast: #000000;
    --ion-color-success-contrast-rgb: 0,0,0;
    --ion-color-success-shade: #29c467;
    --ion-color-success-tint: #44e283;

    --ion-color-warning: #ffd534;
    --ion-color-warning-rgb: 255,213,52;
    --ion-color-warning-contrast: #000000;
    --ion-color-warning-contrast-rgb: 0,0,0;
    --ion-color-warning-shade: #e0bb2e;
    --ion-color-warning-tint: #ffd948;

    --ion-color-danger: #ff4961;
    --ion-color-danger-rgb: 255,73,97;
    --ion-color-danger-contrast: #ffffff;
    --ion-color-danger-contrast-rgb: 255,255,255;
    --ion-color-danger-shade: #e04055;
    --ion-color-danger-tint: #ff5b71;

    --ion-color-dark: #f4f5f8;
    --ion-color-dark-rgb: 244,245,248;
    --ion-color-dark-contrast: #000000;
    --ion-color-dark-contrast-rgb: 0,0,0;
    --ion-color-dark-shade: #d7d8da;
    --ion-color-dark-tint: #f5f6f9;

    --ion-color-medium: #989aa2;
    --ion-color-medium-rgb: 152,154,162;
    --ion-color-medium-contrast: #000000;
    --ion-color-medium-contrast-rgb: 0,0,0;
    --ion-color-medium-shade: #86888f;
    --ion-color-medium-tint: #a2a4ab;

    --ion-color-light: #222428;
    --ion-color-light-rgb: 34,36,40;
    --ion-color-light-contrast: #ffffff;
    --ion-color-light-contrast-rgb: 255,255,255;
    --ion-color-light-shade: #1e2023;
    --ion-color-light-tint: #383a3e;
  }

  /*
   * iOS Dark Theme
   * -------------------------------------------
   */

  .ios body {
    /* --ion-background-color: #000000; - REMOVED */
    /* --ion-background-color-rgb: 0,0,0; - REMOVED */

    --ion-text-color: #ffffff;
    --ion-text-color-rgb: 255,255,255;

    --ion-color-step-50: #0d0d0d;
    --ion-color-step-100: #1a1a1a;
    --ion-color-step-150: #262626;
    --ion-color-step-200: #333333;
    --ion-color-step-250: #404040;
    --ion-color-step-300: #4d4d4d;
    --ion-color-step-350: #595959;
    --ion-color-step-400: #666666;
    --ion-color-step-450: #737373;
    --ion-color-step-500: #808080;
    --ion-color-step-550: #8c8c8c;
    --ion-color-step-600: #999999;
    --ion-color-step-650: #a6a6a6;
    --ion-color-step-700: #b3b3b3;
    --ion-color-step-750: #bfbfbf;
    --ion-color-step-800: #cccccc;
    --ion-color-step-850: #d9d9d9;
    --ion-color-step-900: #e6e6e6;
    --ion-color-step-950: #f2f2f2;

    --ion-item-background: #000;
    --ion-item-color: #fff;

    --ion-card-background: #1a1a1a;
  }

  .ios ion-modal {
    --ion-background-color: var(--shot-black);
    --ion-toolbar-background: var(--shot-black);
    --ion-toolbar-border-color: rgba(255, 255, 255, 0.1);
  }


  /*
   * Material Design Dark Theme
   * -------------------------------------------
   */

  .md body {
    /* --ion-background-color: #121212; - REMOVED */
    /* --ion-background-color-rgb: 18,18,18; - REMOVED */

    --ion-text-color: #ffffff;
    --ion-text-color-rgb: 255,255,255;

    --ion-border-color: #222222;

    --ion-color-step-50: #1e1e1e;
    --ion-color-step-100: #2a2a2a;
    --ion-color-step-150: #363636;
    --ion-color-step-200: #414141;
    --ion-color-step-250: #4d4d4d;
    --ion-color-step-300: #595959;
    --ion-color-step-350: #656565;
    --ion-color-step-400: #717171;
    --ion-color-step-450: #7d7d7d;
    --ion-color-step-500: #898989;
    --ion-color-step-550: #949494;
    --ion-color-step-600: #a0a0a0;
    --ion-color-step-650: #acacac;
    --ion-color-step-700: #b8b8b8;
    --ion-color-step-750: #c4c4c4;
    --ion-color-step-800: #d0d0d0;
    --ion-color-step-850: #dbdbdb;
    --ion-color-step-900: #e7e7e7;
    --ion-color-step-950: #f3f3f3;

    --ion-item-background: #1e1e1e;

    --ion-toolbar-background: #1f1f1f;

    --ion-tab-bar-background: #1f1f1f;

    --ion-card-background: #1e1e1e;
  }
}

/* Custom Colors */
.ion-color-coach {
  --ion-color-base: var(--ion-color-coach);
  --ion-color-base-rgb: var(--ion-color-coach-rgb);
  --ion-color-contrast: var(--ion-color-coach-contrast);
  --ion-color-contrast-rgb: var(--ion-color-coach-contrast-rgb);
  --ion-color-shade: var(--ion-color-coach-shade);
  --ion-color-tint: var(--ion-color-coach-tint);
}

/* SHOT Brand Typography Application */
/* Headlines use Poppins (bold, impactful) */
h1, h2, h3, h4, h5, h6, .ion-title {
  font-family: var(--font-heading) !important;
  font-weight: 600 !important;
  color: var(--shot-white) !important;
}

/* Body text uses Montserrat (clear, accessible) */
body, p, div, span, ion-content {
  font-family: var(--font-body) !important;
  /* background-color: var(--shot-black) !important; - REMOVED */
  color: var(--shot-white) !important;
}

/* Ensure proper contrast and accessibility */
ion-item {
  /* --background: var(--shot-black); - REMOVED */
  --color: var(--shot-white);
}

ion-card {
  /* --background: var(--shot-black); - REMOVED */
  --color: var(--shot-white);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

ion-button {
  --color: var(--shot-white);
}

/* Ionic Button Override to Enforce SHOT Brand Colors */
ion-button.ion-color-primary {
  --ion-color-base: var(--ion-color-primary) !important;
  --ion-color-base-rgb: var(--ion-color-primary-rgb) !important;
  --ion-color-contrast: var(--ion-color-primary-contrast) !important;
  --ion-color-contrast-rgb: var(--ion-color-primary-contrast-rgb) !important;
  --ion-color-shade: var(--ion-color-primary-shade) !important;
  --ion-color-tint: var(--ion-color-primary-tint) !important;
  --background: var(--ion-color-primary) !important;
  --color: var(--ion-color-primary-contrast) !important;
}

ion-button.ion-color-success {
  --ion-color-base: var(--ion-color-success) !important;
  --ion-color-base-rgb: var(--ion-color-success-rgb) !important;
  --ion-color-contrast: var(--ion-color-success-contrast) !important;
  --ion-color-contrast-rgb: var(--ion-color-success-contrast-rgb) !important;
  --ion-color-shade: var(--ion-color-success-shade) !important;
  --ion-color-tint: var(--ion-color-success-tint) !important;
  --background: var(--ion-color-success) !important;
  --color: var(--ion-color-success-contrast) !important;
}

ion-button.ion-color-tertiary {
  --ion-color-base: var(--ion-color-tertiary) !important;
  --ion-color-base-rgb: var(--ion-color-tertiary-rgb) !important;
  --ion-color-contrast: var(--ion-color-tertiary-contrast) !important;
  --ion-color-contrast-rgb: var(--ion-color-tertiary-contrast-rgb) !important;
  --ion-color-shade: var(--ion-color-tertiary-shade) !important;
  --ion-color-tint: var(--ion-color-tertiary-tint) !important;
  --background: var(--ion-color-tertiary) !important;
  --color: var(--ion-color-tertiary-contrast) !important;
}

/* SHOT Brand Button Color Mapping */
ion-button.shot-teal, ion-button[color="teal"] {
  --background: var(--shot-teal) !important;
  --color: var(--shot-black) !important;
}

ion-button.shot-purple, ion-button[color="purple"] {
  --background: var(--shot-purple) !important;
  --color: var(--shot-white) !important;
}

ion-button.shot-gold, ion-button[color="gold"] {
  --background: var(--shot-gold) !important;
  --color: var(--shot-black) !important;
}
