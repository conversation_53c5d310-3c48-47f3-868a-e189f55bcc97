/* 
  Global fix for the green box in the header 
  This file should be imported in index.tsx or App.tsx to ensure it's applied globally
*/

/* Target the specific green box in the top left */
ion-header,
ion-header *,
ion-toolbar, 
ion-toolbar *,
.standard-header-toolbar,
.standard-header-toolbar * {
  --background: black !important;
  background-color: black !important;
}

/* Target the specific back button */
ion-button.back-button,
ion-button.back-button::part(native) {
  --background: black !important;
  background-color: black !important;
}

/* Exceptions for logos and icons */
ion-icon,
img {
  background-color: transparent !important;
}

/* Ensure any nested elements are also fixed */
ion-header > ion-toolbar > div,
ion-header > ion-toolbar > div > div,
ion-header > ion-toolbar > div > div > div {
  background-color: black !important;
}

/* Specific fix for the green box */
ion-header > ion-toolbar > div > div:first-child {
  background-color: black !important;
}

/* Fix for any button backgrounds */
ion-button::part(native) {
  --background: transparent !important;
  background-color: transparent !important;
}
