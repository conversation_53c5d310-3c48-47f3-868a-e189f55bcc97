/* 
 * DateTimePickerFix.css
 * Fixes white-on-white issues in IonDatetime components
 */

/* Selected day in calendar (white circle) */
ion-datetime .calendar-day-highlight,
ion-datetime .calendar-day.today {
  background-color: var(--ion-color-coach) !important; /* Coach orange color */
  color: #000000 !important; /* Black text */
}

/* Time display at bottom (white box) */
ion-datetime .time-header,
ion-datetime .time-body,
ion-datetime .time-placeholder {
  background-color: var(--ion-color-coach) !important; /* Coach orange */
  color: #000000 !important; /* Black text */
}

/* Custom rule for ios time display */
ion-datetime .picker-opts .picker-opt.picker-opt-selected {
  color: #000000 !important;
  background-color: var(--ion-color-coach) !important;
}

/* Additional styles for ios time display */
.picker-above-highlight,
.picker-below-highlight {
  background: rgba(0,0,0,0.5) !important;
}
