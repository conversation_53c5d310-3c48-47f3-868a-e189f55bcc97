/* shot-components.css - SHOT Clubhouse Tailwind Components */

@layer components {
  /* ===== Layout Components ===== */
  .shot-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .shot-page {
    @apply min-h-screen bg-shot-black text-shot-white;
  }

  .shot-section {
    @apply py-8 space-y-6;
  }

  /* ===== Header/Navigation ===== */
  .shot-header {
    @apply fixed top-0 left-0 right-0 z-50 border-b border-white/10;
    background: rgba(26, 26, 26, 0.7);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    transition: all 0.3s ease;
  }

  .shot-header-glass {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background: linear-gradient(
      to bottom,
      rgba(26, 26, 26, 0.85),
      rgba(26, 26, 26, 0.65)
    );
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    transition: all 0.3s ease;
  }

  .shot-header:hover {
    background: rgba(26, 26, 26, 0.8);
    border-color: rgba(255, 255, 255, 0.15);
  }

  .shot-nav {
    @apply flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8;
  }

  .shot-nav-title {
    @apply font-heading text-xl font-bold uppercase tracking-wider text-shot-white;
  }

  .shot-nav-logo {
    @apply h-10 w-auto cursor-pointer transition-transform duration-200 hover:scale-105;
  }

  .shot-nav-profile {
    @apply w-10 h-10 rounded-full border-2 border-shot-gold/50 cursor-pointer transition-all duration-200 hover:border-shot-gold hover:scale-105;
  }

  /* ===== Footer ===== */
  .shot-footer {
    @apply border-t border-white/10 mt-auto;
    background: rgba(26, 26, 26, 0.7);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    transition: all 0.3s ease;
  }

  .shot-footer-glass {
    @apply shot-footer;
    background: linear-gradient(
      to top,
      rgba(26, 26, 26, 0.85),
      rgba(26, 26, 26, 0.65)
    );
  }

  .shot-footer:hover {
    background: rgba(26, 26, 26, 0.8);
    border-color: rgba(255, 255, 255, 0.15);
  }

  .shot-footer-content {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8;
  }

  .shot-footer-grid {
    @apply grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-8 mb-8;
  }

  .shot-footer-section {
    @apply space-y-4;
  }

  .shot-footer-heading {
    @apply font-heading font-bold text-shot-gold uppercase tracking-wider text-sm;
  }

  .shot-footer-link {
    @apply block text-shot-white/70 hover:text-shot-teal transition-colors duration-200 text-sm;
  }

  .shot-footer-social {
    @apply flex items-center gap-4 mt-4;
  }

  .shot-footer-social-icon {
    @apply w-10 h-10 rounded-full bg-white/10 flex items-center justify-center text-shot-white/70 hover:bg-shot-teal hover:text-shot-black transition-all duration-200;
  }

  .shot-footer-bottom {
    @apply pt-8 border-t border-white/10 flex flex-col sm:flex-row items-center justify-between gap-4;
  }

  .shot-footer-copyright {
    @apply text-shot-white/50 text-sm;
  }

  .shot-footer-sp-summary {
    @apply flex items-center gap-4 px-4 py-2 bg-shot-gold/10 border border-shot-gold/30 rounded-shot;
  }

  /* ===== Bottom Navigation (Mobile Footer) ===== */
  .shot-footer-bottom-nav {
    @apply fixed bottom-0 left-0 right-0 z-50 border-t border-white/10;
    background: rgba(26, 26, 26, 0.85);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    padding-bottom: env(safe-area-inset-bottom);
  }

  .shot-footer-nav {
    @apply flex items-center justify-around h-14 relative;
  }

  .shot-nav-item {
    @apply flex flex-col items-center justify-center flex-1 h-full relative py-2 transition-all duration-200;
  }

  .shot-nav-item:not(.shot-nav-center):active {
    @apply scale-95;
  }

  .shot-nav-label {
    @apply text-[10px] font-heading font-semibold uppercase tracking-wider mt-1;
  }

  /* Center Navigation Button */
  .shot-nav-center {
    @apply relative flex items-center justify-center;
  }

  .shot-center-button {
    @apply absolute -top-7 w-14 h-14 bg-shot-gold rounded-full flex items-center justify-center shadow-lg transition-all duration-200;
  }

  .shot-center-button:active {
    @apply scale-95;
  }

  .shot-center-button svg {
    @apply text-shot-black;
  }

  /* Active state */
  .shot-nav-active {
    @apply text-shot-gold;
  }

  /* ===== Cards ===== */
  .shot-card {
    @apply bg-surface-primary border border-white/10 rounded-shot overflow-hidden transition-all duration-200;
  }

  .shot-card-interactive {
    @apply shot-card cursor-pointer hover:border-shot-teal/50 hover:shadow-shot-glow-teal hover:-translate-y-0.5;
  }

  .shot-card-header {
    @apply px-4 py-3 border-b border-white/10;
  }

  .shot-card-content {
    @apply px-4 py-4;
  }

  .shot-card-footer {
    @apply px-4 py-3 border-t border-white/10;
  }

  /* Team Card Specific */
  .shot-team-card {
    @apply shot-card-interactive group;
  }

  .shot-team-card-icon {
    @apply text-2xl mb-3;
  }

  .shot-team-card-title {
    @apply font-heading font-bold text-shot-white group-hover:text-shot-teal transition-colors;
  }

  .shot-team-card-subtitle {
    @apply text-shot-white/60 text-sm font-body;
  }

  .shot-team-card-arrow {
    @apply w-5 h-5 text-shot-white/40 group-hover:text-shot-teal group-hover:translate-x-1 transition-all;
  }

  /* Player Card */
  .shot-player-card {
    @apply shot-card p-4;
  }

  .shot-player-avatar {
    @apply w-16 h-16 rounded-full object-cover border-2 border-white/20;
  }

  .shot-player-name {
    @apply font-heading font-semibold text-shot-white;
  }

  .shot-player-position {
    @apply text-sm text-shot-white/60;
  }

  .shot-player-stats {
    @apply grid grid-cols-4 gap-2 mt-3;
  }

  .shot-player-stat {
    @apply text-center;
  }

  .shot-player-stat-label {
    @apply text-xs text-shot-white/50 uppercase;
  }

  .shot-player-stat-value {
    @apply text-lg font-bold;
  }

  /* ===== Buttons ===== */
  .shot-btn {
    @apply inline-flex items-center justify-center font-heading font-semibold uppercase tracking-wider transition-all duration-200 rounded-shot cursor-pointer;
  }

  .shot-btn-primary {
    @apply shot-btn bg-shot-teal text-shot-black hover:bg-shot-teal/90 hover:shadow-shot-glow-teal hover:-translate-y-0.5 focus:ring-2 focus:ring-shot-teal focus:ring-offset-2 focus:ring-offset-shot-black;
  }

  .shot-btn-secondary {
    @apply shot-btn bg-shot-purple text-shot-white hover:bg-shot-purple/90 hover:shadow-shot-glow-purple hover:-translate-y-0.5 focus:ring-2 focus:ring-shot-purple focus:ring-offset-2 focus:ring-offset-shot-black;
  }

  .shot-btn-tertiary {
    @apply shot-btn bg-shot-gold text-shot-black hover:bg-shot-gold/90 hover:shadow-shot-glow-gold hover:-translate-y-0.5 focus:ring-2 focus:ring-shot-gold focus:ring-offset-2 focus:ring-offset-shot-black;
  }

  .shot-btn-outline {
    @apply shot-btn bg-transparent border-2 border-shot-teal text-shot-teal hover:bg-shot-teal hover:text-shot-black hover:shadow-shot-glow-teal focus:ring-2 focus:ring-shot-teal focus:ring-offset-2 focus:ring-offset-shot-black;
  }

  .shot-btn-ghost {
    @apply shot-btn bg-transparent text-shot-white hover:bg-white/10 focus:ring-2 focus:ring-shot-white focus:ring-offset-2 focus:ring-offset-shot-black;
  }

  /* Button Sizes */
  .shot-btn-sm {
    @apply text-sm px-3 py-1.5 min-h-[32px];
  }

  .shot-btn-md {
    @apply text-base px-6 py-3 min-h-[44px];
  }

  .shot-btn-lg {
    @apply text-lg px-8 py-4 min-h-[52px];
  }

  /* ===== Badges ===== */
  .shot-badge {
    @apply inline-flex items-center justify-center font-body font-medium rounded-shot-pill transition-all duration-200;
  }

  .shot-badge-sm {
    @apply text-xs px-2 py-1 gap-1;
  }

  .shot-badge-md {
    @apply text-sm px-3 py-1.5 gap-1.5;
  }

  .shot-badge-primary {
    @apply shot-badge bg-shot-teal text-shot-black;
  }

  .shot-badge-secondary {
    @apply shot-badge bg-shot-purple text-shot-white;
  }

  .shot-badge-coach {
    @apply shot-badge bg-shot-coach text-shot-black shadow-shot-glow-gold;
  }

  .shot-badge-player {
    @apply shot-badge bg-shot-player text-shot-white;
  }

  .shot-badge-active {
    @apply shot-badge bg-shot-green text-shot-white;
  }

  .shot-badge-inactive {
    @apply shot-badge bg-shot-grey text-shot-white;
  }

  /* ===== SP Points Display ===== */
  .shot-sp-display {
    @apply inline-flex items-center gap-2 px-4 py-2 bg-shot-gold/10 border border-shot-gold/30 rounded-shot;
  }

  .shot-sp-icon {
    @apply w-6 h-6 text-shot-gold;
  }

  .shot-sp-value {
    @apply font-heading font-bold text-shot-gold text-lg;
  }

  .shot-sp-label {
    @apply text-shot-white/60 text-sm ml-1;
  }

  /* ===== Evaluation Components ===== */
  .shot-evaluation-card {
    @apply shot-card;
  }

  .shot-evaluation-header {
    @apply shot-card-header flex items-center justify-between;
  }

  .shot-evaluation-status-pending {
    @apply shot-badge-sm bg-shot-gold/20 text-shot-gold border border-shot-gold/30;
  }

  .shot-evaluation-status-completed {
    @apply shot-badge-sm bg-shot-green/20 text-shot-green border border-shot-green/30;
  }

  .shot-evaluation-status-awaiting {
    @apply shot-badge-sm bg-shot-grey/20 text-shot-grey border border-shot-grey/30;
  }

  .shot-evaluation-grid {
    @apply grid grid-cols-2 gap-4 p-4;
  }

  .shot-evaluation-corner {
    @apply space-y-2;
  }

  .shot-evaluation-corner-label {
    @apply text-xs uppercase tracking-wider text-shot-white/50;
  }

  .shot-evaluation-rating {
    @apply flex items-center gap-1;
  }

  .shot-evaluation-dot {
    @apply w-4 h-4 rounded-full border border-white/20;
  }

  .shot-evaluation-dot-0 {
    @apply shot-evaluation-dot bg-shot-grey;
  }

  .shot-evaluation-dot-1,
  .shot-evaluation-dot-2 {
    @apply shot-evaluation-dot bg-shot-red;
  }

  .shot-evaluation-dot-3,
  .shot-evaluation-dot-4 {
    @apply shot-evaluation-dot bg-shot-gold;
  }

  .shot-evaluation-dot-5 {
    @apply shot-evaluation-dot bg-shot-green;
  }

  /* ===== Achievement Components ===== */
  .shot-achievement {
    @apply flex items-center gap-3 p-3 bg-surface-secondary rounded-shot;
  }

  .shot-achievement-icon {
    @apply w-10 h-10 bg-shot-purple/20 rounded-full flex items-center justify-center text-shot-purple;
  }

  .shot-achievement-title {
    @apply font-semibold text-shot-white;
  }

  .shot-achievement-date {
    @apply text-xs text-shot-white/50;
  }

  /* ===== Social Feed ===== */
  .shot-feed-item {
    @apply shot-card p-4 space-y-3;
  }

  .shot-feed-header {
    @apply flex items-center gap-3;
  }

  .shot-feed-avatar {
    @apply w-10 h-10 rounded-full object-cover;
  }

  .shot-feed-user {
    @apply font-semibold text-shot-white;
  }

  .shot-feed-time {
    @apply text-sm text-shot-white/50;
  }

  .shot-feed-content {
    @apply text-shot-white/90;
  }

  .shot-feed-image {
    @apply w-full rounded-shot object-cover max-h-64;
  }

  .shot-feed-actions {
    @apply flex items-center gap-6 pt-2;
  }

  .shot-feed-action {
    @apply flex items-center gap-2 text-sm text-shot-white/60 hover:text-shot-teal transition-colors cursor-pointer;
  }

  /* ===== Shop Item ===== */
  .shot-shop-item {
    @apply shot-card overflow-hidden group cursor-pointer;
  }

  .shot-shop-image {
    @apply w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300;
  }

  .shot-shop-content {
    @apply p-4 space-y-2;
  }

  .shot-shop-title {
    @apply font-heading font-semibold text-shot-white;
  }

  .shot-shop-price {
    @apply text-xl font-bold text-shot-gold;
  }

  .shot-shop-stock {
    @apply text-sm text-shot-white/60;
  }

  /* ===== Fixture Card ===== */
  .shot-fixture {
    @apply shot-card p-4;
  }

  .shot-fixture-header {
    @apply flex items-center justify-between mb-3;
  }

  .shot-fixture-type {
    @apply shot-badge-sm;
  }

  .shot-fixture-teams {
    @apply flex items-center justify-between mb-2;
  }

  .shot-fixture-team {
    @apply font-heading font-semibold;
  }

  .shot-fixture-vs {
    @apply text-shot-white/50 px-3;
  }

  .shot-fixture-details {
    @apply flex items-center gap-4 text-sm text-shot-white/60;
  }

  /* ===== Leaderboard ===== */
  .shot-leaderboard {
    @apply shot-card;
  }

  .shot-leaderboard-item {
    @apply flex items-center gap-4 p-4 border-b border-white/10 last:border-0;
  }

  .shot-leaderboard-rank {
    @apply w-8 h-8 rounded-full bg-shot-gold/20 flex items-center justify-center font-bold text-shot-gold;
  }

  .shot-leaderboard-rank-1 {
    @apply bg-shot-gold text-shot-black;
  }

  .shot-leaderboard-info {
    @apply flex-1;
  }

  .shot-leaderboard-name {
    @apply font-semibold text-shot-white;
  }

  .shot-leaderboard-team {
    @apply text-sm text-shot-white/60;
  }

  .shot-leaderboard-sp {
    @apply font-heading font-bold text-shot-gold;
  }

  /* ===== Forms ===== */
  .shot-input {
    @apply w-full bg-white/10 border border-white/20 rounded-shot px-4 py-2 text-shot-white placeholder-white/50 focus:border-shot-teal focus:ring-2 focus:ring-shot-teal/20 focus:outline-none transition-all;
  }

  .shot-label {
    @apply block text-sm font-medium text-shot-white/80 mb-2;
  }

  .shot-select {
    @apply shot-input appearance-none bg-[url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2212%22%20height%3D%228%22%20viewBox%3D%220%200%2012%208%22%3E%3Cpath%20fill%3D%22%23ffffff%22%20d%3D%22M6%208L0%200h12z%22%2F%3E%3C%2Fsvg%3E')] bg-[length:12px_8px] bg-[position:right_16px_center] bg-no-repeat pr-10;
  }

  .shot-textarea {
    @apply shot-input min-h-[100px] resize-none;
  }

  /* ===== Utility Classes ===== */
  .shot-divider {
    @apply border-t border-white/10 my-4;
  }

  .shot-text-gradient {
    @apply bg-gradient-to-r from-shot-teal to-shot-purple bg-clip-text text-transparent;
  }

  .shot-skeleton {
    @apply animate-pulse bg-white/10 rounded-shot;
  }

  .shot-skeleton-text {
    @apply shot-skeleton h-4 w-full;
  }

  .shot-skeleton-avatar {
    @apply shot-skeleton w-10 h-10 rounded-full;
  }

  /* ===== Grid Layouts ===== */
  .shot-grid-teams {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
  }

  .shot-grid-players {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
  }

  .shot-grid-shop {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }

  /* ===== Tabs ===== */
  .shot-tabs {
    @apply flex items-center gap-1 p-1 bg-surface-secondary rounded-shot;
  }

  .shot-tab {
    @apply flex-1 px-4 py-2 text-center font-medium text-shot-white/60 rounded-shot transition-all cursor-pointer hover:text-shot-white;
  }

  .shot-tab-active {
    @apply shot-tab bg-shot-gold text-shot-black;
  }

  /* ===== Modal/Dialog ===== */
  .shot-modal-backdrop {
    @apply fixed inset-0 bg-black/80 backdrop-blur-sm z-50;
  }

  .shot-modal {
    @apply fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-md bg-surface-primary border border-white/20 rounded-shot-lg shadow-xl z-50;
  }

  .shot-modal-header {
    @apply px-6 py-4 border-b border-white/10;
  }

  .shot-modal-content {
    @apply px-6 py-4;
  }

  .shot-modal-footer {
    @apply px-6 py-4 border-t border-white/10 flex items-center justify-end gap-3;
  }

  /* ===== Animations ===== */
  @keyframes shot-fade-in {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .shot-animate-in {
    animation: shot-fade-in 0.3s ease-out forwards;
  }
}