/* V2 Navigation Overrides - Ensures V2 navigation displays correctly */

/* Hide any non-V2 navigation on V2 routes */
body.v2-active #bottom-nav:not(.v2-bottom-nav) {
  display: none !important;
}

/* Ensure V2 navigation is always visible and on top */
body.v2-active .v2-bottom-nav {
  display: block !important;
  z-index: 9999 !important;
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
}

/* Hide any other fixed bottom elements on V2 pages */
body.v2-active .fixed.bottom-0:not(.v2-bottom-nav) {
  display: none !important;
}

/* Ensure V2 layout takes full height */
body.v2-active .v2-layout {
  min-height: 100vh !important;
  position: relative !important;
  z-index: 1 !important;
}

/* Debug styles to highlight navigation issues */
body.v2-active.debug-nav #bottom-nav:not(.v2-bottom-nav) {
  border: 3px solid red !important;
  opacity: 0.5 !important;
}

body.v2-active.debug-nav .v2-bottom-nav {
  border: 3px solid green !important;
}
