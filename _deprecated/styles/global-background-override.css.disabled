/* Global Background Override - Import this LAST to fix all black background issues */

/* Override ALL Ionic dark theme defaults */
:root {
  --ion-background-color: #ffffff !important;
  --ion-background-color-rgb: 255,255,255 !important;
  --ion-text-color: #000000 !important;
  --ion-text-color-rgb: 0,0,0 !important;
  --ion-item-background: #ffffff !important;
  --ion-card-background: #ffffff !important;
  --ion-toolbar-background: #f0f0f0 !important;
}

/* Force light theme on all Ionic components */
body,
html,
ion-app,
ion-content,
ion-page,
.ion-page,
.app-container,
#root {
  background-color: #ffffff !important;
  background: #ffffff !important;
  color: #000000 !important;
}

/* Override any element with dark backgrounds */
*[style*="background-color: black"],
*[style*="background-color: #000"],
*[style*="background-color: #000000"],
*[style*="background-color: rgb(0, 0, 0)"],
*[style*="background: black"],
*[style*="background: #000"],
*[style*="background: #000000"],
*[style*="background: rgb(0, 0, 0)"] {
  background-color: #ffffff !important;
  background: #ffffff !important;
}

/* Specific overrides for common problem areas */
.bg-black,
.bg-gray-900,
.bg-gray-800,
.dark,
.dark-mode,
.dark-theme {
  background-color: #ffffff !important;
  color: #000000 !important;
}

/* Override Ionic's internal styles */
.ios body,
.md body {
  --ion-background-color: #ffffff !important;
  --ion-text-color: #000000 !important;
}

/* Override any CSS custom properties that might be set to black */
* {
  --background: #ffffff !important;
  --color: #000000 !important;
}

/* For design system pages specifically */
.design-system-wrapper,
.design-system-page,
.design-system-container {
  background-color: #ffffff !important;
  color: #000000 !important;
}

/* Override shot-black variable wherever it's used */
* {
  --shot-black: #f0f0f0 !important;
}

/* Ensure cards and components have appropriate backgrounds */
.shot-card,
.shot-nav-item,
.shot-team-card,
.shot-dashboard,
.shot-card-interactive,
.shot-evaluation-card {
  background-color: #f8f8f8 !important;
  border: 1px solid #e0e0e0 !important;
  color: #000000 !important;
}

/* Fix text colors */
h1, h2, h3, h4, h5, h6, p, span, div, a, li, td, th {
  color: #000000 !important;
}

/* Fix any remaining white text on white background issues */
.text-white,
.text-gray-100,
.text-gray-200 {
  color: #000000 !important;
}

/* For the specific test card page */
.test-card-page,
.test-card-container {
  background-color: #f0f0f0 !important;
  color: #000000 !important;
}

/* Nuclear option - use only if needed */
/* 
* {
  background-color: transparent !important;
}

body {
  background-color: #ffffff !important;
}
*/
