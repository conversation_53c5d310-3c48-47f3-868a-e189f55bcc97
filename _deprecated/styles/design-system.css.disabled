/* Design System Specific Overrides */

/* Fix scrolling for Design System pages */
body.design-system-active,
html:has(.design-system-active) {
  overflow: auto !important;
  height: auto !important;
  position: relative !important;
}

/* Override any Ionic-specific styles for Design System pages */
body.design-system-active ion-app,
body.design-system-active ion-content,
body.design-system-active ion-page {
  position: relative !important;
  height: auto !important;
  overflow: visible !important;
  contain: none !important;
  --overflow: visible !important;
}

/* Reset Ionic content padding */
body.design-system-active ion-content {
  --padding-bottom: 0 !important;
}

body.design-system-active ion-content::part(scroll) {
  padding-bottom: 0 !important;
  overflow-y: auto !important;
}

/* Ensure Design System containers allow scrolling */
.design-system-wrapper {
  position: relative !important;
  overflow: visible !important;
  height: auto !important;
  min-height: 100vh;
}

/* Ensure main content area is scrollable */
.design-system-container {
  position: relative !important;
  overflow: visible !important;
}

/* Override any global overflow-x hidden that might affect vertical scrolling */
body.design-system-active {
  overflow-x: auto !important;
  overflow-y: auto !important;
}

/* Specific fix for evaluation summary cards */
.evaluation-summary-container {
  position: relative !important;
  overflow: visible !important;
}

/* Ensure all Design System showcase pages are scrollable */
[class*="design-system"] {
  overflow: visible !important;
}

/* Override any fixed positioning on the app root */
body.design-system-active #root {
  position: relative !important;
  overflow: visible !important;
  height: auto !important;
  min-height: 100vh;
}
