/* Global black theme overrides - Import this last if needed */

/* Ensure all backgrounds respect the black theme */
* {
  scrollbar-width: thin;
  scrollbar-color: rgb(75 85 99) rgb(31 41 55);
}

/* Webkit scrollbar styling */
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background: rgb(31 41 55);
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb {
  background: rgb(75 85 99);
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb:hover {
  background: rgb(107 114 128);
}

/* Ensure modals and overlays use black theme */
[role="dialog"],
[role="alertdialog"],
.modal,
.overlay {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
}

/* Selection colors */
::selection {
  background-color: rgba(26, 188, 156, 0.3);
  color: white;
}

::-moz-selection {
  background-color: rgba(26, 188, 156, 0.3);
  color: white;
}

/* Focus styles for accessibility */
:focus-visible {
  outline: 2px solid #1ABC9C;
  outline-offset: 2px;
}

/* Ensure inputs don't have white backgrounds from browser defaults */
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  -webkit-text-fill-color: white !important;
  -webkit-box-shadow: 0 0 0px 1000px rgb(30, 30, 30) inset !important;
  transition: background-color 5000s ease-in-out 0s;
}

/* Ensure no white flashes on page load */
html {
  background-color: rgb(31, 41, 55);
}

/* Print styles - revert to white background for printing */
@media print {
  body {
    background-color: white !important;
    color: black !important;
  }
  
  .shot-card,
  .shot-surface,
  .shot-surface-secondary,
  .shot-surface-tertiary,
  .shot-surface-elevated {
    background-color: white !important;
    border: 1px solid black !important;
  }
}
