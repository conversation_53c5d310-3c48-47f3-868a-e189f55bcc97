/* Global styles to ensure proper scrolling for V2 pages */
html, body {
  overflow: auto !important;
  height: auto !important;
  position: relative !important;
}

/* Override any potential Ionic interference */
ion-app, 
ion-router-outlet, 
ion-content {
  display: contents !important;
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
  --offset-top: 0 !important;
  --offset-bottom: 0 !important;
}

/* Ensure the root div can scroll */
#root {
  overflow: auto !important;
  height: auto !important;
  min-height: 100vh;
}

/* Clean reset for v2 pages */
.v2-page {
  position: relative !important;
  overflow: auto !important;
  width: 100% !important;
  height: auto !important;
  min-height: 100vh !important;
  display: block !important;
}

/* Ensure body can scroll when v2-page is active */
body:has(.v2-page) {
  overflow: auto !important;
  height: auto !important;
}