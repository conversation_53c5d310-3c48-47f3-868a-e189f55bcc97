/* Global Footer Styles - Tailwind-based */

/* Ensure the footer is properly positioned */
#bottom-nav {
  @apply fixed bottom-0 left-0 right-0 z-50;
  padding-bottom: env(safe-area-inset-bottom); /* Respect iOS safe areas */
}

/* Add padding to all main content areas to account for footer */
.page-content,
.main-content,
main {
  padding-bottom: calc(80px + env(safe-area-inset-bottom)) !important;
}

/* Ensure footer background is solid on all pages */
#bottom-nav {
  @apply bg-gray-900/95;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Fix for any scrolling issues */
.app-container {
  @apply min-h-screen flex flex-col;
}

/* Ensure proper spacing for the last elements on any page */
.pb-footer-spacing {
  padding-bottom: calc(80px + env(safe-area-inset-bottom)) !important;
}

/* Override any conflicting styles from other components */
.shot-footer-bottom-nav {
  @apply fixed bottom-0;
}

/* Utility class for footer spacing */
.pb-footer {
  padding-bottom: calc(80px + env(safe-area-inset-bottom)) !important;
}
