/* V2 Evaluation specific styles */
.v2-evaluation-container {
  background-color: #000000;
  color: #ffffff;
  min-height: 100vh;
}

.v2-category-card {
  background-color: #1a1a1a;
  border-radius: 12px;
  padding: 20px;
  border-left: 4px solid var(--card-color);
  transition: all 0.2s ease;
  cursor: pointer;
}

.v2-category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.v2-progress-bar {
  height: 8px;
  background-color: #374151;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.v2-progress-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease-out;
}