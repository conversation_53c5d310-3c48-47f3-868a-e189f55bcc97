# 🚀 Deployment Guide

This document explains how deployments work for the SHOT app using **Vercel** and **Supabase**.  
It covers **preview deployments**, **branch deployments**, **production releases**, and **environment variable management**.

---

## 1. Overview

- **Frontend** → deployed on **Vercel**  
- **Backend (DB, Auth, Storage, Functions)** → deployed on **Supabase**  
- **Integration** → Vercel is connected to Supabase so that **every PR** automatically gets:
  - A **Vercel Preview Deployment** (frontend)  
  - A **Supabase Branch Deployment** (backend)  

This means each PR has its **own isolated environment** for testing.

---

## 2. Deployment Flow

```mermaid
flowchart TD
    Dev[Developer] -->|Push PR| GitHub
    GitHub -->|Triggers| Vercel
    GitHub -->|Triggers| Supabase

    Vercel -->|Build & Deploy| PreviewFrontend[Preview Frontend URL]
    Supabase -->|Branch Deploy| PreviewBackend[Preview Supabase Project]

    PreviewFrontend -->|Connected to| PreviewBackend
    MainBranch[Main Branch Merge] -->|Deploys| ProdFrontend[Production Frontend]
    MainBranch -->|Deploys| ProdBackend[Production Supabase]
```

---

## 3. Environments

### Local
- Run with `pnpm dev` + `supabase start`  
- Uses `.env.local` with **local Supabase**  

### Preview (PR)
- Triggered automatically when you open a PR  
- Vercel creates a **Preview URL** (e.g., `https://shotclubhouse-git-feature-branch.vercel.app`)  
- Supabase creates a **Branch Deployment** (isolated DB + services)  
- The preview frontend is automatically connected to the preview Supabase backend  

### Production
- Triggered when merging to `main`  
- Vercel deploys to **production domain** (e.g., `https://shotclubhouse.com`)  
- Supabase deploys to the **production project**  
- Uses `.env.production` values  

---

## 4. Environment Variables 🔑

### Golden Rule
⚠️ **Never commit environment variables to Git.**  
- `.env`, `.env.local`, `.env.production` must always be in `.gitignore`.  
- Secrets must only live in **Vercel**, **Supabase**, or **GitHub Actions**.  

### Where to store environment variables
- **Local Development** → `.env.local` (ignored by Git)  
- **Vercel** → Project Settings → Environment Variables  
- **Supabase** → Project Settings → API Keys / Config  
- **GitHub** → Repository Settings → Secrets and Variables  

### Why?
- Prevents accidental leaks of API keys, DB credentials, or tokens.  
- Keeps production secrets safe.  
- Ensures preview deployments get the right environment automatically.  

### Best Practices
- ✅ Use `.env.example` to document required variables (no secrets).  
- ✅ Use `VITE_` prefix for frontend variables (Vite requirement).  
- ✅ Rotate keys if they are ever exposed.  
- ❌ Never hardcode secrets in code.  
- ❌ Never commit `.env` files.  

---

## 5. Preview Deployment Flow

1. **Open a PR** → GitHub triggers Vercel + Supabase  
2. **Vercel** builds the frontend and deploys to a unique preview URL  
3. **Supabase** creates a branch deployment (new DB + services)  
4. **Integration** → Vercel injects the preview Supabase URL + anon key into the preview frontend  
5. **Result** → You get a fully isolated environment (frontend + backend) for testing your PR  

---

## 6. Production Deployment Flow

1. **Merge PR into `main`**  
2. **Vercel** deploys the frontend to the production domain  
3. **Supabase** applies migrations and updates the production project  
4. **Result** → Production app is live with the latest changes  

---

## 7. Developer Workflow

1. **Create a feature branch**  
2. **Open a PR** → Preview deployment is created automatically  
3. **Test your changes** on the preview URL (frontend + backend)  
4. **Review & QA** → Team tests the preview environment  
5. **Merge to main** → Deploys to production  

---

## 8. Common Commands

- **Check Vercel deployments**  
  ```bash
  vercel ls
  ```
  Lists recent deployments.

- **Check Supabase branch deployments**  
  ```bash
  supabase projects list
  ```
  Shows active projects (including preview branches).

- **Check current project**  
  ```bash
  supabase status
  ```
  Ensures you’re not connected to production by mistake.

---

## 9. Best Practices

- ✅ Always test your PR on the **preview deployment** before merging.  
- ✅ Use **Supabase branch deployments** for schema changes — test migrations in preview before production.  
- ✅ Keep `.env.local` for local dev only.  
- ✅ Store secrets in **Vercel**, **Supabase**, or **GitHub**.  
- ❌ Never run `supabase migration up` against production directly.  
- ❌ Never use production keys in local development.  
- ❌ Never commit `.env` files to Git.  

---

# ✅ Summary

- **PR opened** → Vercel + Supabase create preview deployments  
- **PR merged** → Vercel + Supabase deploy to production  
- **Local dev** → Use `.env.local` + `supabase start`  
- **Secrets** → Always stored in Vercel, Supabase, or GitHub — never in Git  
- **Always check** which Supabase project you’re connected to before running migrations