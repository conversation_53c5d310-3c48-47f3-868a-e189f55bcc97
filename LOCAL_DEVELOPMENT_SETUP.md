# Local Development Setup - SHOT App

This guide will help you set up a local Supabase instance and connect the SHOT app to it for development purposes.

## Prerequisites

- Node.js (v18 or later)
- npm or yarn
- Docker Desktop (for local Supabase)
- Git

## 1. Install Supabase CLI

```bash
npm install -g supabase
```

Or using npm scripts (already configured in package.json):

```bash
npm run supabase:start  # Uses npx supabase start
npm run supabase:stop   # Uses npx supabase stop
```

## 2. Clone and Setup Project

```bash
git clone [your-repo-url]
cd SHOTclubhouse
npm install
```

## 3. Initialize Local Supabase

```bash
# Initialize Supabase (if not already initialized)
supabase init

# Start local Supabase services
supabase start
```

This will start:

- PostgreSQL database
- Auth server
- Realtime server
- Storage server
- Edge Functions runtime
- Supabase Studio (dashboard)

**Important:** The first time you run `supabase start`, it will:

- Download Docker images (~500MB)
- Start all services
- Show you the local URLs and keys

## 4. Create Environment Configuration

### 4.1 Main App Configuration (.env.local)

Create a `.env.local` file in the project root:

```bash
# Local Supabase Configuration
VITE_APP_SUPABASE_URL=http://localhost:54321
VITE_APP_SUPABASE_ANON_KEY=[your-local-anon-key]

# Development Server Configuration
VITE_PORT=5160

# Optional: Disable external services for local development
VITE_CLARITY_TRACKING_CODE=disabled-local
VITE_SENTRY_DSN=disabled-local

# Stripe (use test keys)
VITE_APP_STRIPE_PUBLIC_KEY=pk_test_your_test_key

# These can be empty for basic local development
AIRTABLE_API_KEY=
AIRTABLE_BASE_ID=
SUPABASE_ACCESS_TOKEN=
SENTRY_AUTH_TOKEN=
```

### 4.2 Edge Functions Configuration

Create `supabase/.env` file for Edge Functions:

```bash
# For Edge Functions development
VIMEO_ACCESS_TOKEN=your_test_token
AIRTABLE_API_KEY=your_test_api_key
AIRTABLE_BASE_ID=your_test_base_id

# Add any other function-specific environment variables
```

## 5. Get Your Local Supabase Credentials

After running `supabase start`, you'll see output similar to:

```
Started supabase local development setup.

         API URL: http://localhost:54321
     GraphQL URL: http://localhost:54321/graphql/v1
          DB URL: postgresql://postgres:postgres@localhost:54322/postgres
      Studio URL: http://localhost:54323
    Inbucket URL: http://localhost:54324
      JWT secret: [jwt-secret]
       anon key: [your-anon-key]
service_role key: [your-service-role-key]
```

**Copy the `anon key` and use it in your `.env.local` file.**

## 6. Database Setup

### 6.1 Apply Existing Migrations (if any)

```bash
# If you have existing migrations
supabase db reset
```

### 6.2 Import Production Schema (Recommended)

If you want to work with the production schema:

```bash
# Pull schema and data from production (requires production access)
supabase db pull
```

## 7. Email Confirmation Setup

The SHOT app requires email confirmation for user registration. Here's how to set it up locally:

### 7.1 Enable Email Confirmations

Edit `supabase/config.toml` and ensure email confirmations are enabled:

```toml
[auth.email]
enable_confirmations = true
enable_signup = true
```

**Important**: Also update the `site_url` and `additional_redirect_urls` to match your frontend port (default 5160, or your VITE_PORT):

```toml
[auth]
site_url = "http://127.0.0.1:5160"
additional_redirect_urls = ["https://127.0.0.1:5160"]
```

### 7.2 Restart Supabase Services

After making configuration changes, restart Supabase:

```bash
supabase stop
supabase start
```

### 7.3 Test Email Flow

1. **Access Inbucket (Local Email Catcher)**: `http://localhost:54324`
2. **Sign up a new user** through your app
3. **Check Inbucket** for the confirmation email
4. **Click the confirmation link** to verify the email
5. **Verify profile creation** in Supabase Studio

**Important Notes:**

- Emails are captured by Inbucket locally, not actually sent
- The `on_email_confirmation` trigger is automatically created by the migration
- The trigger creates profiles with `email_verified = true` upon confirmation
- Users must confirm their email before accessing the app

## 8. Start Development Servers

```bash
# Terminal 1: Start the app
npm run dev

# Terminal 2: Start Edge Functions (if needed)
npm run functions:serve
```

Your app will be available at `http://localhost:5173`
Supabase Studio (database dashboard) at `http://localhost:54323`
Inbucket (email testing) at `http://localhost:54324`

## 9. Database Connection Details

If you need direct database access:

```bash
# Connection string
postgresql://postgres:postgres@localhost:54322/postgres

# Or using psql directly
psql 'postgresql://postgres:postgres@localhost:54322/postgres'
```

## 10. Working with Edge Functions

### Start Functions Locally

```bash
npm run functions:serve
# Or for specific function
npm run functions:serve:airtable
```

Functions will be available at `http://localhost:54321/functions/v1/[function-name]`

### Deploy Functions (when ready)

```bash
npm run functions:deploy
```

## 11. Common Development Tasks

### Reset Database

```bash
supabase db reset
```

### View Logs

```bash
supabase logs
```

### Generate Types (optional)

```bash
supabase gen types typescript --local > src/types/database.types.ts
```

## 12. Testing Configuration

The app uses the following test commands:

```bash
npm test           # Unit tests
npm run test:e2e   # End-to-end tests
npm run test:all   # Both unit and e2e tests
```

## 13. Stopping Services

When you're done developing:

```bash
supabase stop
```

This will stop all local Supabase services but preserve your data.

To completely reset (delete all data):

```bash
supabase stop --no-backup
```

## Troubleshooting

### Port Conflicts

If you get port conflicts, you can modify ports in `supabase/config.toml`:

```toml
[api]
port = 54321

[db]
port = 54322
```

### Environment Variables Not Loading

- Ensure `.env.local` is in the project root
- Restart your dev server after changing env variables
- Check that variables start with `VITE_` for client-side access

### Database Connection Issues

- Verify Docker is running
- Check `supabase status` to see service health
- Try `supabase restart` if services are unhealthy

### Functions Not Working

- Check that `.env` file exists in `supabase/` directory
- Verify function is properly deployed with `supabase functions list`
- Check function logs with `supabase functions logs [function-name]`

## Project-Specific Notes

### Database Schema

The SHOT app uses these main tables:

- `profiles` - User profiles and authentication
- `teams` - Team management
- `events` - Matches and training sessions
- `evaluations` - Player assessments
- `memberships` - Subscription and payment data
- `sport_heads` - Administrative roles

### Key Features to Test

- User authentication and role management
- Team creation and management
- Event scheduling and RSVP system
- Player evaluation system
- Family account relationships

### Important: Row Level Security (RLS)

The production database uses extensive RLS policies. When working locally:

- Test with different user roles (player, coach, admin, sport_head)
- Verify data isolation between teams/clubs
- Check that users can only access their authorized data

## Next Steps

1. Create your `.env.local` file with the local Supabase credentials
2. Start the local services with `npm run supabase:start`
3. Run the app with `npm run dev`
4. Access Supabase Studio at `http://localhost:54323` to manage your local database
5. Begin development with a fully functional local environment!

For questions or issues, refer to the [Supabase CLI documentation](https://supabase.com/docs/guides/cli) or check the project's main documentation.
