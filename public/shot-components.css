/* shot-components.css - SHOT Clubhouse Tailwind Components */

/* Font imports to match demo site */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&family=Montserrat:wght@400;500;600;700&display=swap');

/* Base theme styles */
@layer base {
  /* Ensure dark theme is applied globally */
  :root {
    color-scheme: dark;
  }
  
  body {
    @apply bg-shot-bg text-white font-body antialiased;
  }
  
  /* Default text colors for common elements */
  h1, h2, h3, h4, h5, h6 {
    @apply text-white font-heading;
  }
  
  p {
    @apply text-gray-300;
  }
  
  a {
    @apply text-shot-teal hover:text-shot-teal/80 transition-colors;
  }
}

@layer components {
  /* ===== Layout Components ===== */
  .shot-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .shot-page {
    @apply min-h-screen;
  }

  .shot-section {
    @apply py-8 space-y-6;
  }

  /* ===== Cards ===== */
  .shot-card {
    @apply bg-surface-primary border border-gray-800 rounded-lg overflow-hidden transition-all duration-200;
  }

  .shot-card-interactive {
    @apply shot-card cursor-pointer hover:border-shot-teal/50 hover:shadow-lg hover:shadow-shot-teal/10 hover:-translate-y-0.5;
  }

  .shot-card-header {
    @apply px-4 py-3 border-b border-gray-800 bg-surface-secondary;
  }

  .shot-card-content {
    @apply px-4 py-4;
  }

  .shot-card-footer {
    @apply px-4 py-3 border-t border-gray-800 bg-surface-secondary;
  }
  
  /* Card variants */
  .shot-card-elevated {
    @apply shot-card bg-surface-elevated shadow-shot-sm;
  }
  
  .shot-card-dark {
    @apply shot-card bg-shot-black border-gray-900;
  }

  /* ===== Buttons ===== */
  .shot-btn {
    @apply inline-flex items-center justify-center font-heading font-semibold uppercase tracking-wider transition-all duration-200 rounded-lg cursor-pointer;
  }

  .shot-btn-primary {
    @apply shot-btn bg-shot-teal text-black hover:bg-shot-teal/90 hover:shadow-lg hover:-translate-y-0.5 focus:ring-2 focus:ring-shot-teal focus:ring-offset-2;
  }

  .shot-btn-secondary {
    @apply shot-btn bg-shot-purple text-white hover:bg-shot-purple/90 hover:shadow-lg hover:-translate-y-0.5 focus:ring-2 focus:ring-shot-purple focus:ring-offset-2;
  }

  .shot-btn-tertiary {
    @apply shot-btn bg-shot-gold text-black hover:bg-shot-gold/90 hover:shadow-lg hover:-translate-y-0.5 focus:ring-2 focus:ring-shot-gold focus:ring-offset-2;
  }

  .shot-btn-outline {
    @apply shot-btn bg-transparent border-2 border-shot-teal text-shot-teal hover:bg-shot-teal hover:text-black focus:ring-2 focus:ring-shot-teal focus:ring-offset-2;
  }

  .shot-btn-ghost {
    @apply shot-btn bg-transparent text-gray-300 hover:bg-gray-800 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  /* Button Sizes */
  .shot-btn-sm {
    @apply text-sm px-3 py-1.5 min-h-[32px];
  }

  .shot-btn-md {
    @apply text-base px-6 py-3 min-h-[44px];
  }

  .shot-btn-lg {
    @apply text-lg px-8 py-4 min-h-[52px];
  }

  /* ===== Badges ===== */
  .shot-badge {
    @apply inline-flex items-center justify-center font-body font-medium rounded-full transition-all duration-200;
  }

  .shot-badge-sm {
    @apply text-xs px-2 py-1 gap-1;
  }

  .shot-badge-md {
    @apply text-sm px-3 py-1.5 gap-1.5;
  }

  .shot-badge-primary {
    @apply shot-badge bg-shot-teal text-black;
  }

  .shot-badge-secondary {
    @apply shot-badge bg-shot-purple text-white;
  }

  /* ===== Forms ===== */
  .shot-input {
    @apply w-full bg-surface-secondary border border-gray-800 rounded-lg px-4 py-2 text-white placeholder-gray-500 focus:border-shot-teal focus:ring-2 focus:ring-shot-teal/20 focus:outline-none transition-all;
  }

  .shot-label {
    @apply block text-sm font-medium text-gray-300 mb-2;
  }

  .shot-select {
    @apply shot-input appearance-none bg-[url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2212%22%20height%3D%228%22%20viewBox%3D%220%200%2012%208%22%3E%3Cpath%20fill%3D%22%239CA3AF%22%20d%3D%22M6%208L0%200h12z%22%2F%3E%3C%2Fsvg%3E')] bg-[length:12px_8px] bg-[position:right_16px_center] bg-no-repeat pr-10;
  }

  .shot-textarea {
    @apply shot-input min-h-[100px] resize-none;
  }
  
  .shot-checkbox {
    @apply w-4 h-4 bg-surface-secondary border border-gray-700 rounded focus:ring-2 focus:ring-shot-teal focus:ring-offset-0 text-shot-teal;
  }
  
  .shot-radio {
    @apply w-4 h-4 bg-surface-secondary border border-gray-700 focus:ring-2 focus:ring-shot-teal focus:ring-offset-0 text-shot-teal;
  }

  /* ===== Grid Layouts ===== */
  .shot-grid-teams {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4;
  }

  .shot-grid-players {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
  }

  /* ===== Utility Classes ===== */
  .shot-divider {
    @apply border-t border-gray-800 my-4;
  }

  .shot-text-gradient {
    @apply bg-gradient-to-r from-shot-teal to-shot-purple bg-clip-text text-transparent;
  }

  .shot-skeleton {
    @apply animate-pulse bg-surface-tertiary rounded-lg;
  }

  .shot-skeleton-text {
    @apply shot-skeleton h-4 w-full;
  }

  .shot-skeleton-avatar {
    @apply shot-skeleton w-10 h-10 rounded-full;
  }
  
  /* ===== Black Theme Specific ===== */
  .shot-surface {
    @apply bg-surface-primary;
  }
  
  .shot-surface-secondary {
    @apply bg-surface-secondary;
  }
  
  .shot-surface-tertiary {
    @apply bg-surface-tertiary;
  }
  
  .shot-surface-elevated {
    @apply bg-surface-elevated;
  }
  
  /* Text utilities for black theme */
  .shot-text-primary {
    @apply text-white;
  }
  
  .shot-text-secondary {
    @apply text-gray-300;
  }
  
  .shot-text-tertiary {
    @apply text-gray-500;
  }
  
  /* Border utilities */
  .shot-border {
    @apply border-gray-800;
  }
  
  .shot-border-light {
    @apply border-gray-700;
  }
  
  /* Shadow utilities for black theme */
  .shot-shadow-elevated {
    @apply shadow-xl shadow-black/50;
  }
  
  /* ===== Evaluation Dots ===== */
  .shot-evaluation-dot {
    @apply w-2 h-2 rounded-full transition-colors;
  }
  
  .shot-evaluation-dot--not-started {
    background-color: #6b7280; /* gray-600 */
  }
  
  .shot-evaluation-dot--low {
    background-color: #E63946; /* shot-red */
  }
  
  .shot-evaluation-dot--average {
    background-color: #F7B613; /* shot-gold */
  }
  
  .shot-evaluation-dot--good {
    background-color: #1ABC9C; /* shot-teal */
  }
  
  .shot-evaluation-dot--high {
    background-color: #6B00DB; /* shot-purple */
  }
  
  /* Evaluation Dots Container */
  .shot-evaluation-dots {
    @apply flex gap-1;
  }
  
  .shot-evaluation-dots--small .shot-evaluation-dot {
    @apply w-1.5 h-1.5;
  }
  
  .shot-evaluation-dots--large .shot-evaluation-dot {
    @apply w-3 h-3;
  }
}

/* Tailwind font family utilities */
@layer utilities {
  .font-heading {
    font-family: 'Poppins', sans-serif;
  }
  
  .font-body {
    font-family: 'Montserrat', sans-serif;
  }
}
