<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shadow DOM Component Test</title>
    <style>
        body {
            background-color: #000;
            color: #fff;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            padding: 2rem;
            margin: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            margin-bottom: 2rem;
        }
        .test-section {
            background-color: #1a1a1a;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
        }
        /* These styles should NOT affect the Shadow DOM component */
        .category-card {
            background-color: red !important;
            color: yellow !important;
        }
        .title {
            font-size: 100px !important;
            color: lime !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Shadow DOM Test Page</h1>
        
        <div class="test-section">
            <h2>Shadow Evaluation Card</h2>
            <p>This component is completely isolated from the aggressive red/yellow/lime styles on this page.</p>
            <shadow-evaluation-card id="testCard"></shadow-evaluation-card>
        </div>

        <div class="test-section">
            <h2>Test Controls</h2>
            <button onclick="updateCategories()">Update Categories</button>
            <button onclick="resetCategories()">Reset Categories</button>
        </div>
    </div>

    <script type="module">
        // Import and register the component
        import { ShadowEvaluationCard } from './src/components/shadow/organisms/ShadowEvaluationCard.js';
        
        customElements.define('shadow-evaluation-card', ShadowEvaluationCard);
        
        // Make functions available globally
        window.updateCategories = function() {
            const card = document.getElementById('testCard');
            const newCategories = [
                { name: 'Speed', score: 4.5, color: 'blue', focus: 'Lightning fast', icon: 'zap' },
                { name: 'Strength', score: 4.0, color: 'green', focus: 'Power shots', icon: 'activity' },
                { name: 'Vision', score: 4.8, color: 'orange', focus: 'Field awareness', icon: 'lightbulb' },
                { name: 'Leadership', score: 5.0, color: 'pink', focus: 'Team captain', icon: 'users' },
                { name: 'Tactics', score: 3.5, color: 'purple', focus: 'Game reading', icon: 'map-pin' }
            ];
            card.setCategories(newCategories);
        };
        
        window.resetCategories = function() {
            const card = document.getElementById('testCard');
            card.removeAttribute('categories');
        };
        
        // Listen for category clicks
        document.getElementById('testCard').addEventListener('category-click', (e) => {
            console.log('Category clicked:', e.detail);
            alert(`Clicked: ${e.detail.category.name} - Score: ${e.detail.category.score}`);
        });
    </script>
</body>
</html>