<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Default Images</title>
    <style>
        body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #10B981;
        }
        button {
            background: #10B981;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            margin: 20px 0;
        }
        button:hover {
            background: #047857;
        }
        .progress {
            margin: 20px 0;
            background: #f0f0f0;
            border-radius: 4px;
            height: 20px;
            position: relative;
        }
        .progress-bar {
            height: 100%;
            background: #10B981;
            border-radius: 4px;
            width: 0;
            transition: width 0.3s;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            margin-top: 20px;
            font-family: monospace;
        }
        .success {
            color: #047857;
        }
        .error {
            color: #DC2626;
        }
    </style>
</head>
<body>
    <h1>SHOT Ecommerce Default Image Generator</h1>
    <p>This tool will create all the necessary default SVG images for the ecommerce section. Click the button below to generate the default images.</p>
    
    <button id="generate">Generate All Default Images</button>
    
    <div class="progress">
        <div class="progress-bar" id="progress-bar"></div>
    </div>
    
    <div class="log" id="log"></div>
    
    <script>
        // All the required image names
        const requiredImages = [
            // Product images
            { name: 'training-tee.jpg', type: 'product', title: 'Pro Training Tee' },
            { name: 'training-kit.jpg', type: 'product', title: 'Elite Training Kit' },
            { name: 'track-jacket.jpg', type: 'product', title: 'Performance Track Jacket' },
            { name: 'shorts.jpg', type: 'product', title: 'Competition Shorts' },
            { name: 'jersey.jpg', type: 'product', title: 'SF Giants Collab Jersey' },
            { name: 'elite-gear.jpg', type: 'product', title: 'Bay Area Elite Gear' },
            { name: 'coach-kit.jpg', type: 'product', title: 'Coach Training Kit' },
            { name: 'player-kit.jpg', type: 'product', title: 'Pro Player Kit' },
            { name: 'training-set.jpg', type: 'product', title: 'Performance Training Set' },
            { name: 'coach-jacket.jpg', type: 'product', title: 'Coach Sideline Jacket' },
            { name: 'coach-set.jpg', type: 'product', title: 'Professional Coach Set' },
            { name: 'fan-jersey.jpg', type: 'product', title: 'Team Supporter Jersey' },
            { name: 'supporter-set.jpg', type: 'product', title: 'Premium Supporter Set' },
            
            // Category images
            { name: 'category-apparel.jpg', type: 'category', title: 'Apparel' },
            { name: 'category-equipment.jpg', type: 'category', title: 'Equipment' },
            { name: 'category-training.jpg', type: 'category', title: 'Training' },
            { name: 'category-teamwear.jpg', type: 'category', title: 'Teamwear' },
            
            // Collection images
            { name: 'spring-collection.jpg', type: 'collection', title: 'Spring Collection 2025' },
            { name: 'urban-collection.jpg', type: 'collection', title: 'Urban Performance Collection' }
        ];
        
        // Log element
        const logElement = document.getElementById('log');
        const progressBar = document.getElementById('progress-bar');
        
        // Add log entry
        function log(message, type = 'info') {
            const entry = document.createElement('div');
            entry.textContent = message;
            if (type) entry.className = type;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // Update progress
        function updateProgress(current, total) {
            const percentage = (current / total) * 100;
            progressBar.style.width = `${percentage}%`;
        }
        
        // Generate SVG for product
        function generateProductSVG(title, color = '#10B981') {
            return `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="300" height="400" viewBox="0 0 300 400" xmlns="http://www.w3.org/2000/svg">
  <rect width="300" height="400" fill="${color}" />
  <text x="150" y="200" font-family="Arial" font-size="100" font-weight="bold" fill="rgba(255,255,255,0.1)" text-anchor="middle" dominant-baseline="middle">SHOT</text>
  
  <!-- T-shirt outline -->
  <path d="M75,100 L225,100 L225,250 L150,300 L75,250 Z" stroke="rgba(255,255,255,0.3)" stroke-width="2" fill="none" />
  
  <!-- Sleeves -->
  <path d="M75,100 L50,133" stroke="rgba(255,255,255,0.3)" stroke-width="2" fill="none" />
  <path d="M225,100 L250,133" stroke="rgba(255,255,255,0.3)" stroke-width="2" fill="none" />
  
  <!-- Product name -->
  <text x="150" y="370" font-family="Arial" font-size="20" font-weight="bold" fill="white" text-anchor="middle">${title}</text>
</svg>`;
        }
        
        // Generate SVG for category
        function generateCategorySVG(title, color = '#3B82F6') {
            return `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="400" height="225" viewBox="0 0 400 225" xmlns="http://www.w3.org/2000/svg">
  <rect width="400" height="225" fill="${color}" />
  <text x="200" y="112" font-family="Arial" font-size="60" font-weight="bold" fill="rgba(255,255,255,0.1)" text-anchor="middle" dominant-baseline="middle">SHOT</text>
  
  <!-- Diagonal stripes pattern -->
  <g stroke="rgba(255,255,255,0.2)" stroke-width="10">
    <line x1="-50" y1="0" x2="175" y2="225" />
    <line x1="0" y1="0" x2="225" y2="225" />
    <line x1="50" y1="0" x2="275" y2="225" />
    <line x1="100" y1="0" x2="325" y2="225" />
    <line x1="150" y1="0" x2="375" y2="225" />
    <line x1="200" y1="0" x2="425" y2="225" />
    <line x1="250" y1="0" x2="475" y2="225" />
    <line x1="300" y1="0" x2="525" y2="225" />
    <line x1="350" y1="0" x2="575" y2="225" />
  </g>
  
  <!-- Category name -->
  <text x="200" y="190" font-family="Arial" font-size="25" font-weight="bold" fill="white" text-anchor="middle">${title}</text>
</svg>`;
        }
        
        // Generate SVG for collection
        function generateCollectionSVG(title, color = '#0F766E') {
            return `<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="400" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
  <rect width="800" height="400" fill="${color}" />
  <text x="400" y="200" font-family="Arial" font-size="120" font-weight="bold" fill="rgba(255,255,255,0.1)" text-anchor="middle" dominant-baseline="middle">SHOT</text>
  
  <!-- Geometric pattern -->
  <g stroke="rgba(255,255,255,0.2)" stroke-width="3" fill="none">
    <circle cx="200" cy="100" r="50" />
    <circle cx="600" cy="100" r="75" />
    <circle cx="150" cy="300" r="100" />
    <circle cx="650" cy="300" r="70" />
    <circle cx="400" cy="200" r="120" />
  </g>
  
  <!-- Collection name -->
  <text x="400" y="350" font-family="Arial" font-size="40" font-weight="bold" fill="white" text-anchor="middle">${title}</text>
  <text x="400" y="380" font-family="Arial" font-size="20" fill="white" text-anchor="middle">SHOT Sportswear</text>
</svg>`;
        }
        
        // Create an SVG file and simulate download
        function createAndDownloadSVG(name, type, title) {
            return new Promise((resolve, reject) => {
                try {
                    // Generate SVG content based on type
                    let svgContent;
                    const colors = [
                        '#10B981', '#0F766E', '#047857', '#3B82F6', '#1D4ED8', 
                        '#7C3AED', '#BE185D', '#0369A1', '#065F46', '#9333EA'
                    ];
                    const color = colors[Math.floor(Math.random() * colors.length)];
                    
                    if (type === 'product') {
                        svgContent = generateProductSVG(title, color);
                    } else if (type === 'category') {
                        svgContent = generateCategorySVG(title, color);
                    } else if (type === 'collection') {
                        svgContent = generateCollectionSVG(title, color);
                    }
                    
                    // Create a blob
                    const blob = new Blob([svgContent], { type: 'image/svg+xml' });
                    
                    // Create a download link
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(blob);
                    
                    // Rename .jpg extension to .svg
                    const svgName = name.replace('.jpg', '.svg');
                    link.download = svgName;
                    
                    // Append to body, click, and remove
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    setTimeout(() => {
                        resolve();
                    }, 300); // Small delay to space out downloads
                } catch (error) {
                    reject(error);
                }
            });
        }
        
        // Generate all images
        async function generateAllImages() {
            log('Starting generation of default images...');
            
            for (let i = 0; i < requiredImages.length; i++) {
                const image = requiredImages[i];
                try {
                    await createAndDownloadSVG(image.name, image.type, image.title);
                    log(`✓ Created ${image.name}`, 'success');
                } catch (error) {
                    log(`✗ Error creating ${image.name}: ${error.message}`, 'error');
                }
                updateProgress(i + 1, requiredImages.length);
            }
            
            log('Generation complete!', 'success');
            log('Please move all downloaded SVG files to the /public/images/ecommerce directory.');
        }
        
        // Event listener for generate button
        document.getElementById('generate').addEventListener('click', generateAllImages);
    </script>
</body>
</html>