<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SHOT Widgets - Examples</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      background: #f5f5f5;
      color: #333;
      line-height: 1.6;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 40px 20px;
    }
    
    h1, h2, h3 {
      margin-bottom: 20px;
    }
    
    h1 {
      font-size: 36px;
      color: #1E1E1E;
    }
    
    h2 {
      font-size: 28px;
      color: #333;
      margin-top: 40px;
    }
    
    .subtitle {
      font-size: 18px;
      color: #666;
      margin-bottom: 40px;
    }
    
    .example {
      background: white;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .example-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .example-title {
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }
    
    .theme-toggle {
      display: flex;
      gap: 10px;
    }
    
    .theme-btn {
      padding: 6px 12px;
      border: 1px solid #ddd;
      background: white;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }
    
    .theme-btn.active {
      background: #6B00DB;
      color: white;
      border-color: #6B00DB;
    }
    
    .code-block {
      background: #f8f8f8;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      padding: 15px;
      margin-top: 20px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 13px;
      overflow-x: auto;
    }
    
    .code-block pre {
      margin: 0;
    }
    
    .widget-container {
      margin: 20px 0;
      padding: 20px;
      background: #f9f9f9;
      border-radius: 8px;
    }
    
    .dark-bg {
      background: #1a1a1a;
    }
    
    .section {
      margin-bottom: 60px;
    }
    
    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      margin-top: 30px;
    }
    
    .feature-card {
      background: white;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e0e0e0;
    }
    
    .feature-card h3 {
      font-size: 18px;
      margin-bottom: 10px;
      color: #6B00DB;
    }
    
    .feature-card p {
      color: #666;
      font-size: 14px;
    }
    
    .btn {
      display: inline-block;
      padding: 10px 20px;
      background: #6B00DB;
      color: white;
      text-decoration: none;
      border-radius: 6px;
      font-weight: 500;
      margin-top: 20px;
    }
    
    .btn:hover {
      background: #5500BA;
    }
    
    .notice {
      background: #FFF3CD;
      border: 1px solid #FFE69C;
      border-radius: 6px;
      padding: 15px;
      margin: 20px 0;
      color: #856404;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>SHOT Widgets</h1>
    <p class="subtitle">Embed SHOT Sports content on your website with our customizable widgets</p>
    
    <!-- Features Section -->
    <section class="section">
      <h2>Features</h2>
      <div class="feature-grid">
        <div class="feature-card">
          <h3>Easy Integration</h3>
          <p>Just copy and paste two lines of code to add SHOT content to your site</p>
        </div>
        <div class="feature-card">
          <h3>Fully Responsive</h3>
          <p>Widgets automatically adapt to any screen size and device</p>
        </div>
        <div class="feature-card">
          <h3>Real-time Updates</h3>
          <p>Content refreshes automatically every 15 minutes</p>
        </div>
        <div class="feature-card">
          <h3>Customizable Themes</h3>
          <p>Choose between light and dark themes to match your site</p>
        </div>
      </div>
    </section>

    <!-- Basic Example -->
    <section class="section">
      <h2>Basic Pulse Feed</h2>
      <div class="example">
        <div class="example-header">
          <h3 class="example-title">Default Configuration</h3>
        </div>
        
        <div class="widget-container">
          <div data-shot-widget="pulse-feed"></div>
        </div>
        
        <div class="code-block">
          <pre>&lt;div data-shot-widget="pulse-feed"&gt;&lt;/div&gt;
&lt;script src="https://shot.com/widgets/shot-widgets.js"&gt;&lt;/script&gt;</pre>
        </div>
      </div>
    </section>

    <!-- Light Theme Example -->
    <section class="section">
      <h2>Light Theme</h2>
      <div class="example">
        <div class="example-header">
          <h3 class="example-title">Perfect for light backgrounds</h3>
        </div>
        
        <div class="widget-container" style="background: white;">
          <div data-shot-widget="pulse-feed" data-theme="light" data-count="3"></div>
        </div>
        
        <div class="code-block">
          <pre>&lt;div data-shot-widget="pulse-feed" 
     data-theme="light" 
     data-count="3"&gt;&lt;/div&gt;
&lt;script src="https://shot.com/widgets/shot-widgets.js"&gt;&lt;/script&gt;</pre>
        </div>
      </div>
    </section>

    <!-- Filtered Example -->
    <section class="section">
      <h2>Filtered by Pillar</h2>
      <div class="example">
        <div class="example-header">
          <h3 class="example-title">Show only "TAKE YOUR SHOT" articles</h3>
        </div>
        
        <div class="widget-container dark-bg">
          <div data-shot-widget="pulse-feed" 
               data-pillar="TAKE YOUR SHOT" 
               data-count="4"></div>
        </div>
        
        <div class="code-block">
          <pre>&lt;div data-shot-widget="pulse-feed" 
     data-pillar="TAKE YOUR SHOT" 
     data-count="4"&gt;&lt;/div&gt;
&lt;script src="https://shot.com/widgets/shot-widgets.js"&gt;&lt;/script&gt;</pre>
        </div>
      </div>
    </section>

    <!-- Multiple Widgets -->
    <section class="section">
      <h2>Multiple Widgets</h2>
      <div class="example">
        <div class="example-header">
          <h3 class="example-title">Display different pillars side by side</h3>
        </div>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
          <div class="widget-container dark-bg">
            <h4 style="color: white; margin-bottom: 10px;">Performance</h4>
            <div data-shot-widget="pulse-feed" 
                 data-pillar="TAKE YOUR SHOT" 
                 data-count="2"></div>
          </div>
          <div class="widget-container dark-bg">
            <h4 style="color: white; margin-bottom: 10px;">Leadership</h4>
            <div data-shot-widget="pulse-feed" 
                 data-pillar="OWN IT" 
                 data-count="2"></div>
          </div>
        </div>
        
        <div class="code-block">
          <pre>&lt;!-- Performance Column --&gt;
&lt;div data-shot-widget="pulse-feed" 
     data-pillar="TAKE YOUR SHOT" 
     data-count="2"&gt;&lt;/div&gt;

&lt;!-- Leadership Column --&gt;
&lt;div data-shot-widget="pulse-feed" 
     data-pillar="OWN IT" 
     data-count="2"&gt;&lt;/div&gt;

&lt;script src="https://shot.com/widgets/shot-widgets.js"&gt;&lt;/script&gt;</pre>
        </div>
      </div>
    </section>

    <!-- Configuration Options -->
    <section class="section">
      <h2>Configuration Options</h2>
      <div class="example">
        <table style="width: 100%; border-collapse: collapse;">
          <thead>
            <tr style="border-bottom: 2px solid #ddd;">
              <th style="text-align: left; padding: 10px;">Attribute</th>
              <th style="text-align: left; padding: 10px;">Values</th>
              <th style="text-align: left; padding: 10px;">Default</th>
              <th style="text-align: left; padding: 10px;">Description</th>
            </tr>
          </thead>
          <tbody>
            <tr style="border-bottom: 1px solid #eee;">
              <td style="padding: 10px;"><code>data-shot-widget</code></td>
              <td style="padding: 10px;">"pulse-feed"</td>
              <td style="padding: 10px;">Required</td>
              <td style="padding: 10px;">Widget type</td>
            </tr>
            <tr style="border-bottom: 1px solid #eee;">
              <td style="padding: 10px;"><code>data-count</code></td>
              <td style="padding: 10px;">1-20</td>
              <td style="padding: 10px;">5</td>
              <td style="padding: 10px;">Number of articles to display</td>
            </tr>
            <tr style="border-bottom: 1px solid #eee;">
              <td style="padding: 10px;"><code>data-theme</code></td>
              <td style="padding: 10px;">"light" | "dark"</td>
              <td style="padding: 10px;">"dark"</td>
              <td style="padding: 10px;">Widget theme</td>
            </tr>
            <tr style="border-bottom: 1px solid #eee;">
              <td style="padding: 10px;"><code>data-pillar</code></td>
              <td style="padding: 10px;">"TAKE YOUR SHOT" | "OWN IT" | "MAKE IMPACT"</td>
              <td style="padding: 10px;">All</td>
              <td style="padding: 10px;">Filter by pillar</td>
            </tr>
            <tr style="border-bottom: 1px solid #eee;">
              <td style="padding: 10px;"><code>data-show-images</code></td>
              <td style="padding: 10px;">"true" | "false"</td>
              <td style="padding: 10px;">"true"</td>
              <td style="padding: 10px;">Show article images</td>
            </tr>
          </tbody>
        </table>
      </div>
    </section>

    <!-- JavaScript API -->
    <section class="section">
      <h2>JavaScript API</h2>
      <div class="example">
        <h3 class="example-title">Programmatic Control</h3>
        
        <div class="notice">
          The widget library exposes a global <code>SHOTWidgets</code> object for advanced control
        </div>
        
        <div class="code-block">
          <pre>// Get widget instance
const widget = SHOTWidgets.getWidget(element);

// Refresh widget content
SHOTWidgets.refresh(element);

// Destroy widget
SHOTWidgets.destroy(element);

// Get all widgets on page
const allWidgets = SHOTWidgets.getAll();

// Configure API endpoint (for development)
SHOTWidgets.configure({
  apiBase: 'http://localhost:54321/functions/v1/widget-api'
});</pre>
        </div>
      </div>
    </section>

    <!-- Get Started -->
    <section class="section">
      <h2>Get Started</h2>
      <div class="example">
        <p>Ready to add SHOT content to your website?</p>
        <a href="/v2/admin/widget-builder" class="btn">Open Widget Builder</a>
        
        <div class="notice" style="margin-top: 30px;">
          <strong>Note:</strong> For production use, please contact support to whitelist your domain.
        </div>
      </div>
    </section>
  </div>

  <!-- Load widgets -->
  <script src="/widgets/shot-widgets.js"></script>
  
  <!-- For local development -->
  <script>
    if (window.location.hostname === 'localhost') {
      SHOTWidgets.configure({
        apiBase: 'http://localhost:54321/functions/v1/widget-api'
      });
    }
  </script>
</body>
</html>