/*!
 * SHOT Widgets v1.0.0
 * Embeddable widgets for SHOT Sports content
 * (c) 2024 SHOT Sports
 */

(function() {
  'use strict';

  // Configuration
  const WIDGET_API_BASE = 'https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/widget-api';
  const WIDGET_VERSION = '1.0.0';
  
  // For local development
  const IS_DEV = window.location.hostname === 'localhost';
  const API_BASE = IS_DEV ? 'http://localhost:54321/functions/v1/widget-api' : WIDGET_API_BASE;

  // Widget class
  class SHOTWidget {
    constructor(element) {
      this.element = element;
      this.config = this.parseConfig();
      this.shadowRoot = null;
      this.initialized = false;
      
      // Bind methods
      this.init = this.init.bind(this);
      this.loadWidget = this.loadWidget.bind(this);
      this.trackEvent = this.trackEvent.bind(this);
      
      // Initialize
      this.init();
    }

    parseConfig() {
      const dataset = this.element.dataset;
      return {
        type: dataset.shotWidget || 'pulse-feed',
        count: parseInt(dataset.count) || 5,
        theme: dataset.theme || 'dark',
        pillar: dataset.pillar || undefined,
        showImages: dataset.showImages !== 'false',
        autoPlay: dataset.autoPlay === 'true',
        interval: parseInt(dataset.interval) || 5000,
        apiBase: dataset.apiBase || API_BASE
      };
    }

    async init() {
      if (this.initialized) return;
      
      try {
        // Check if shadow root already exists, otherwise create it
        if (!this.element.shadowRoot) {
          this.shadowRoot = this.element.attachShadow({ mode: 'open' });
        } else {
          this.shadowRoot = this.element.shadowRoot;
        }
        
        // Add loading state
        this.showLoading();
        
        // Load widget content
        await this.loadWidget();
        
        // Track widget view
        this.trackEvent('view');
        
        this.initialized = true;
      } catch (error) {
        console.error('SHOT Widget initialization error:', error);
        this.showError(error.message);
      }
    }

    showLoading() {
      this.shadowRoot.innerHTML = `
        <style>
          .widget-loading {
            padding: 40px;
            text-align: center;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            color: #666;
          }
        </style>
        <div class="widget-loading">Loading SHOT content...</div>
      `;
    }

    showError(message) {
      this.shadowRoot.innerHTML = `
        <style>
          .widget-error {
            padding: 40px;
            text-align: center;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            color: #E63946;
            background: #FEE;
            border: 1px solid #FCC;
            border-radius: 8px;
          }
        </style>
        <div class="widget-error">
          Error loading widget: ${message}
        </div>
      `;
    }

    async loadWidget() {
      const endpoint = `${this.config.apiBase}/${this.config.type}`;
      const params = new URLSearchParams({
        count: this.config.count,
        theme: this.config.theme,
        showImages: this.config.showImages
      });
      
      if (this.config.pillar && this.config.pillar !== 'all') {
        params.append('pillar', this.config.pillar);
      }

      try {
        const response = await fetch(`${endpoint}?${params}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
// Authorization header removed for security - configure via API endpoint if needed
          }
        });

        if (!response.ok) {
          throw new Error(`API returned ${response.status}`);
        }

        const { html, css } = await response.json();
        
        // Inject content into shadow DOM
        this.shadowRoot.innerHTML = `
          <style>${css}</style>
          ${html}
        `;

        // Add click tracking to article cards
        this.addEventListeners();

      } catch (error) {
        console.error('Widget load error:', error);
        throw error;
      }
    }

    addEventListeners() {
      // Track clicks on article cards
      const cards = this.shadowRoot.querySelectorAll('.article-card');
      cards.forEach((card, index) => {
        card.addEventListener('click', (e) => {
          e.preventDefault();
          const url = card.getAttribute('onclick').match(/window\.open\('([^']+)'/)[1];
          this.trackEvent('click', { 
            url, 
            position: index + 1 
          });
          window.open(url, '_blank');
        });
      });

      // Add refresh capability
      if (this.config.type === 'pulse-feed') {
        this.addRefreshButton();
      }
    }

    addRefreshButton() {
      const header = this.shadowRoot.querySelector('.widget-header');
      if (!header) return;

      const refreshBtn = document.createElement('button');
      refreshBtn.innerHTML = '↻';
      refreshBtn.style.cssText = `
        background: none;
        border: 1px solid #666;
        border-radius: 4px;
        color: #666;
        cursor: pointer;
        font-size: 16px;
        padding: 4px 8px;
        margin-left: 10px;
      `;
      refreshBtn.title = 'Refresh content';
      refreshBtn.onclick = () => {
        this.loadWidget();
        this.trackEvent('refresh');
      };

      header.appendChild(refreshBtn);
    }

    async trackEvent(event, data = {}) {
      try {
        await fetch(`${this.config.apiBase}/track`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            widgetType: this.config.type,
            event,
            domain: window.location.hostname,
            url: window.location.href,
            timestamp: new Date().toISOString(),
            ...data
          })
        });
      } catch (error) {
        console.warn('Widget tracking error:', error);
      }
    }

    // Public methods
    refresh() {
      return this.loadWidget();
    }

    destroy() {
      if (this.shadowRoot) {
        this.shadowRoot.innerHTML = '';
      }
      this.initialized = false;
    }
  }

  // Widget manager
  class SHOTWidgetManager {
    constructor() {
      this.widgets = new Map();
      this.observer = null;
      this.init();
    }

    init() {
      // Initialize on DOM ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => this.scanAndInitialize());
      } else {
        this.scanAndInitialize();
      }

      // Watch for dynamically added widgets
      this.observeDOM();
    }

    scanAndInitialize() {
      const elements = document.querySelectorAll('[data-shot-widget]');
      elements.forEach(element => this.initializeWidget(element));
    }

    initializeWidget(element) {
      if (this.widgets.has(element)) return;

      const widget = new SHOTWidget(element);
      this.widgets.set(element, widget);
    }

    observeDOM() {
      if (!window.MutationObserver) return;

      this.observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === 1) { // Element node
              if (node.hasAttribute('data-shot-widget')) {
                this.initializeWidget(node);
              }
              // Check children
              const widgets = node.querySelectorAll('[data-shot-widget]');
              widgets.forEach(widget => this.initializeWidget(widget));
            }
          });
        });
      });

      this.observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    }

    // Public API
    refresh(element) {
      const widget = this.widgets.get(element);
      if (widget) {
        return widget.refresh();
      }
    }

    destroy(element) {
      const widget = this.widgets.get(element);
      if (widget) {
        widget.destroy();
        this.widgets.delete(element);
      }
    }

    getWidget(element) {
      return this.widgets.get(element);
    }

    getAll() {
      return Array.from(this.widgets.values());
    }
  }

  // Initialize widget manager
  const widgetManager = new SHOTWidgetManager();

  // Expose global API
  window.SHOTWidgets = {
    version: WIDGET_VERSION,
    manager: widgetManager,
    refresh: (element) => widgetManager.refresh(element),
    destroy: (element) => widgetManager.destroy(element),
    getWidget: (element) => widgetManager.getWidget(element),
    getAll: () => widgetManager.getAll(),
    
    // Configuration helper
    configure: (options) => {
      if (options.apiBase) {
        API_BASE = options.apiBase;
      }
    }
  };

  // AMD/CommonJS/Global support
  if (typeof define === 'function' && define.amd) {
    define([], function() { return window.SHOTWidgets; });
  } else if (typeof module === 'object' && module.exports) {
    module.exports = window.SHOTWidgets;
  }

})();