<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        .error {
            background: #fee;
            border-color: #fcc;
            color: #c00;
        }
        .success {
            background: #efe;
            border-color: #cfc;
            color: #060;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <h1>Widget API Test Page</h1>

    <!-- Direct API Test -->
    <div class="test-section">
        <h2>1. Direct API Test</h2>
        <p>Test the widget API endpoint directly without the widget library.</p>
        
        <button class="test-button" onclick="testDirectAPI()">Test Pulse Feed API</button>
        <button class="test-button" onclick="testCORS()">Test CORS</button>
        
        <div id="direct-result" class="result" style="display:none;"></div>
    </div>

    <!-- Raw Fetch Test -->
    <div class="test-section">
        <h2>2. Raw Fetch Test</h2>
        <p>Test different API configurations.</p>
        
        <div style="margin-bottom: 10px;">
            <label>Count: <input type="number" id="count" value="5" min="1" max="20"></label>
            <label style="margin-left: 20px;">Theme: 
                <select id="theme">
                    <option value="dark">Dark</option>
                    <option value="light">Light</option>
                </select>
            </label>
            <label style="margin-left: 20px;">Pillar: 
                <select id="pillar">
                    <option value="">All</option>
                    <option value="TAKE YOUR SHOT">Take Your Shot</option>
                    <option value="OWN IT">Own It</option>
                    <option value="MAKE IMPACT">Make Impact</option>
                </select>
            </label>
        </div>
        
        <button class="test-button" onclick="testCustomAPI()">Test Custom Configuration</button>
        
        <div id="custom-result" class="result" style="display:none;"></div>
    </div>

    <!-- Widget Library Test -->
    <div class="test-section">
        <h2>3. Widget Library Test</h2>
        <p>Test the actual widget implementation.</p>
        
        <button class="test-button" onclick="loadWidget()">Load Widget</button>
        
        <div id="widget-container" style="margin-top: 20px;"></div>
    </div>

    <script>
        const API_BASE = 'https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/widget-api';
        
        // Helper to display results
        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result ' + (isError ? 'error' : 'success');
            element.innerHTML = content;
        }
        
        // Test direct API
        async function testDirectAPI() {
            const resultEl = document.getElementById('direct-result');
            showResult('direct-result', 'Testing API...');
            
            try {
                const response = await fetch(`${API_BASE}/pulse-feed?count=5&theme=dark`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });
                
                const responseText = await response.text();
                
                if (!response.ok) {
                    showResult('direct-result', 
                        `<strong>Error ${response.status}:</strong><br>
                        ${response.statusText}<br><br>
                        <strong>Response:</strong><br>
                        <pre>${responseText}</pre>`, 
                        true
                    );
                    return;
                }
                
                // Check if response is HTML (widget) or JSON (error)
                if (responseText.includes('<!DOCTYPE html>') || responseText.includes('<html>')) {
                    showResult('direct-result', 
                        `<strong>Success!</strong> Widget HTML received (${responseText.length} bytes)<br><br>
                        <details>
                            <summary>View HTML</summary>
                            <pre>${responseText.substring(0, 500)}...</pre>
                        </details>`
                    );
                } else {
                    const data = JSON.parse(responseText);
                    showResult('direct-result', 
                        `<strong>Success!</strong> JSON response received<br><br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>`
                    );
                }
                
            } catch (error) {
                showResult('direct-result', 
                    `<strong>Fetch Error:</strong><br>${error.message}<br><br>
                    This usually means CORS is blocking the request or the function is not deployed.`,
                    true
                );
            }
        }
        
        // Test CORS
        async function testCORS() {
            const resultEl = document.getElementById('direct-result');
            showResult('direct-result', 'Testing CORS preflight...');
            
            try {
                const response = await fetch(`${API_BASE}/pulse-feed`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'GET',
                        'Access-Control-Request-Headers': 'content-type',
                    }
                });
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
                };
                
                showResult('direct-result', 
                    `<strong>CORS Preflight ${response.ok ? 'Success' : 'Failed'}!</strong><br><br>
                    <strong>Status:</strong> ${response.status}<br>
                    <strong>Headers:</strong><br>
                    <pre>${JSON.stringify(corsHeaders, null, 2)}</pre>`
                );
                
            } catch (error) {
                showResult('direct-result', 
                    `<strong>CORS Test Error:</strong><br>${error.message}`,
                    true
                );
            }
        }
        
        // Test with custom configuration
        async function testCustomAPI() {
            const count = document.getElementById('count').value;
            const theme = document.getElementById('theme').value;
            const pillar = document.getElementById('pillar').value;
            
            let url = `${API_BASE}/pulse-feed?count=${count}&theme=${theme}`;
            if (pillar) {
                url += `&pillar=${encodeURIComponent(pillar)}`;
            }
            
            showResult('custom-result', `Testing: ${url}`);
            
            try {
                const response = await fetch(url);
                const text = await response.text();
                
                if (!response.ok) {
                    showResult('custom-result', 
                        `<strong>Error ${response.status}:</strong> ${text}`,
                        true
                    );
                    return;
                }
                
                showResult('custom-result', 
                    `<strong>Success!</strong> Received ${text.length} bytes<br><br>
                    <iframe 
                        srcdoc="${text.replace(/"/g, '&quot;')}" 
                        style="width: 100%; height: 400px; border: 1px solid #ddd; margin-top: 10px;"
                    ></iframe>`
                );
                
            } catch (error) {
                showResult('custom-result', 
                    `<strong>Error:</strong> ${error.message}`,
                    true
                );
            }
        }
        
        // Load actual widget
        function loadWidget() {
            const container = document.getElementById('widget-container');
            container.innerHTML = `
                <div 
                    data-shot-widget="pulse-feed"
                    data-count="5"
                    data-theme="dark"
                    data-api-base="${API_BASE}"
                ></div>
            `;
            
            // Load widget script
            const script = document.createElement('script');
            script.src = '/widgets/shot-widgets.js';
            script.onload = () => {
                container.innerHTML += '<p style="margin-top: 10px; color: green;">✓ Widget script loaded</p>';
            };
            script.onerror = () => {
                container.innerHTML += '<p style="margin-top: 10px; color: red;">✗ Failed to load widget script</p>';
            };
            document.body.appendChild(script);
        }
    </script>
</body>
</html>