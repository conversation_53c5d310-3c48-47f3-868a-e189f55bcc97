<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHOT Pulse Widget Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        header {
            text-align: center;
            margin-bottom: 60px;
        }
        
        h1 {
            font-size: 48px;
            font-weight: 700;
            margin: 0 0 20px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 20px;
            color: #666;
            margin: 0 0 40px 0;
        }
        
        .demo-section {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }
        
        .section-title {
            font-size: 28px;
            font-weight: 600;
            margin: 0 0 30px 0;
            color: #222;
        }
        
        .widget-container {
            margin: 30px 0;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .code-block code {
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            color: #212529;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        
        .feature {
            text-align: center;
        }
        
        .feature-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .feature-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0 0 10px 0;
        }
        
        .feature-desc {
            font-size: 16px;
            color: #666;
            line-height: 1.5;
        }
        
        .cta {
            text-align: center;
            margin: 60px 0;
        }
        
        .cta-button {
            display: inline-block;
            padding: 15px 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 30px;
            font-size: 18px;
            font-weight: 600;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }
        
        footer {
            text-align: center;
            padding: 40px 0;
            color: #666;
        }
        
        footer a {
            color: #667eea;
            text-decoration: none;
        }
        
        footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>SHOT Pulse Widget</h1>
            <p class="subtitle">Embed dynamic sports content anywhere on the web</p>
        </header>

        <div class="demo-section">
            <h2 class="section-title">Live Demo</h2>
            <p>Here's the SHOT Pulse widget in action, displaying the latest sports content:</p>
            
            <div class="widget-container">
                <!-- SHOT PULSE FEED Widget -->
                <div data-shot-widget="pulse-feed"
                     data-count="5"
                     data-theme="dark"
                     data-api-base="https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/widget-api"></div>
                <script src="https://shot-pi.vercel.app/widgets/shot-widgets.js"></script>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">How to Embed</h2>
            <p>Adding the SHOT Pulse widget to your website is simple. Just copy and paste this code:</p>
            
            <div class="code-block">
                <code>&lt;!-- SHOT PULSE FEED Widget --&gt;<br>
&lt;div data-shot-widget="pulse-feed"<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;data-count="5"<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;data-theme="dark"<br>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;data-api-base="https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/widget-api"&gt;&lt;/div&gt;<br>
&lt;script src="https://shot-pi.vercel.app/widgets/shot-widgets.js"&gt;&lt;/script&gt;</code>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">Features</h2>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">⚡</div>
                    <h3 class="feature-title">Lightning Fast</h3>
                    <p class="feature-desc">Loads in under 200ms with optimized caching and edge delivery</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">🎨</div>
                    <h3 class="feature-title">Customizable</h3>
                    <p class="feature-desc">Light and dark themes, adjustable article count, and filter by content pillar</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">🔒</div>
                    <h3 class="feature-title">Isolated Styling</h3>
                    <p class="feature-desc">Shadow DOM ensures widget styles never conflict with your site</p>
                </div>
                
                <div class="feature">
                    <div class="feature-icon">📱</div>
                    <h3 class="feature-title">Responsive</h3>
                    <p class="feature-desc">Automatically adapts to any screen size for perfect mobile display</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2 class="section-title">Configuration Options</h2>
            
            <div class="code-block">
                <code>data-shot-widget="pulse-feed"  // Widget type (required)<br>
data-count="5"                // Number of articles (1-20)<br>
data-theme="dark"             // Theme: "dark" or "light"<br>
data-pillar="TAKE YOUR SHOT"  // Filter by pillar (optional)<br>
data-show-images="true"       // Show article images (optional)</code>
            </div>
        </div>

        <div class="cta">
            <a href="/v2/admin/widget-builder" class="cta-button">Build Your Widget</a>
        </div>

        <footer>
            <p>© 2025 SHOT Ltd. All rights reserved. | <a href="https://www.shotclubhouse.com">shotclubhouse.com</a></p>
        </footer>
    </div>
</body>
</html>