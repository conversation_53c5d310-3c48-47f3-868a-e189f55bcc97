<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHOT - Team Management</title>
    <link href="https://unpkg.com/@ionic/core@latest/css/ionic.bundle.css" rel="stylesheet">
    <link href="/src/theme/shot-brand-enhanced.css" rel="stylesheet">
    <style>
        /* Organization domain-specific styles */
        .team-banner {
            background: linear-gradient(135deg, rgba(26, 188, 156, 0.15) 0%, rgba(0, 0, 0, 0) 100%);
            border: 1px solid rgba(26, 188, 156, 0.3);
            border-radius: var(--shot-button-radius);
            padding: 20px;
            margin-bottom: 24px;
            position: relative;
            overflow: hidden;
        }
        
        .team-banner::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--shot-teal);
        }
        
        .club-badge {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
        }
        
        .player-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        
        .player-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--shot-button-radius);
            padding: 16px;
            text-align: center;
            transition: all 0.2s ease;
            cursor: pointer;
            position: relative;
        }
        
        .player-card:hover {
            background: rgba(26, 188, 156, 0.1);
            transform: translateY(-2px);
        }
        
        .player-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 8px;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        .player-status {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        .player-status.active {
            background: var(--shot-green);
        }
        
        .player-status.inactive {
            background: var(--shot-grey);
        }
        
        .team-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            margin: 16px 0;
        }
        
        .stat-box {
            background: rgba(255, 255, 255, 0.05);
            padding: 12px;
            border-radius: 8px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--shot-teal);
        }
        
        .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 4px;
        }
        
        .action-fab {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: var(--shot-purple);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(107, 0, 219, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .action-fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(107, 0, 219, 0.6);
        }
        
        .invite-code-box {
            background: rgba(247, 182, 19, 0.1);
            border: 1px dashed var(--shot-gold);
            border-radius: var(--shot-button-radius);
            padding: 16px;
            text-align: center;
            margin: 16px 0;
        }
        
        .invite-code {
            font-size: 24px;
            font-weight: 700;
            color: var(--shot-gold);
            letter-spacing: 2px;
            margin: 8px 0;
        }
        
        .offline-sync-status {
            background: rgba(255, 111, 60, 0.1);
            border: 1px solid var(--shot-burnt-orange);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .sync-icon {
            animation: spin 2s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <ion-app>
        <ion-header>
            <ion-toolbar class="shot-toolbar">
                <ion-buttons slot="start">
                    <ion-back-button defaultHref="/coach"></ion-back-button>
                </ion-buttons>
                <ion-title>Team Management</ion-title>
                <ion-buttons slot="end">
                    <ion-button>
                        <ion-icon name="search-outline"></ion-icon>
                    </ion-button>
                    <ion-button>
                        <ion-badge color="danger" style="position: absolute; top: 4px; right: 4px; font-size: 10px;">3</ion-badge>
                        <ion-icon name="notifications-outline"></ion-icon>
                    </ion-button>
                </ion-buttons>
            </ion-toolbar>
        </ion-header>
        
        <ion-content class="ion-padding bg-black">
            <!-- Offline Sync Status -->
            <div class="offline-sync-status" id="syncStatus" style="display: none;">
                <ion-icon name="sync" class="sync-icon" style="color: var(--shot-burnt-orange); font-size: 24px;"></ion-icon>
                <div style="flex: 1;">
                    <div style="font-weight: 600; color: var(--shot-burnt-orange);">Syncing Team Data...</div>
                    <div style="font-size: 12px; color: rgba(255, 255, 255, 0.6);">3 changes pending</div>
                </div>
            </div>
            
            <!-- Team Banner -->
            <div class="team-banner">
                <div style="display: flex; align-items: center;">
                    <div class="club-badge">
                        <ion-icon name="shield" style="font-size: 32px; color: var(--shot-teal);"></ion-icon>
                    </div>
                    <div style="flex: 1;">
                        <h2 class="shot-h2" style="margin: 0;">City FC Academy</h2>
                        <p style="color: rgba(255, 255, 255, 0.7); margin: 4px 0;">U16 Boys</p>
                        <div style="display: flex; gap: 8px; margin-top: 8px;">
                            <span class="shot-badge shot-badge-teal">Active Season</span>
                            <span class="shot-badge shot-badge-purple">Premier League</span>
                        </div>
                    </div>
                </div>
                
                <!-- Team Stats -->
                <div class="team-stats">
                    <div class="stat-box">
                        <div class="stat-value">18</div>
                        <div class="stat-label">Players</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-value">3</div>
                        <div class="stat-label">Coaches</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-value">85%</div>
                        <div class="stat-label">Attendance</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-value">12</div>
                        <div class="stat-label">Next Match</div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="shot-card" style="margin-bottom: 24px;">
                <div class="shot-card-header">
                    <h3 class="shot-card-title">Quick Actions</h3>
                </div>
                <div class="shot-card-content">
                    <ion-segment value="players">
                        <ion-segment-button value="players">
                            <ion-label>Players</ion-label>
                        </ion-segment-button>
                        <ion-segment-button value="events">
                            <ion-label>Events</ion-label>
                        </ion-segment-button>
                        <ion-segment-button value="evaluations">
                            <ion-label>Evaluations</ion-label>
                        </ion-segment-button>
                    </ion-segment>
                </div>
            </div>
            
            <!-- Invite Code Section -->
            <div class="invite-code-box">
                <ion-icon name="people" style="font-size: 32px; color: var(--shot-gold);"></ion-icon>
                <p style="margin: 8px 0; color: rgba(255, 255, 255, 0.7);">Team Invite Code</p>
                <div class="invite-code">CITY-U16-2024</div>
                <ion-button size="small" fill="clear" style="--color: var(--shot-gold);">
                    <ion-icon name="copy" slot="start"></ion-icon>
                    Copy Code
                </ion-button>
            </div>
            
            <!-- Player Roster -->
            <div class="shot-card">
                <div class="shot-card-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3 class="shot-card-title">Player Roster</h3>
                    <ion-button size="small" fill="clear">
                        <ion-icon name="filter" slot="icon-only"></ion-icon>
                    </ion-button>
                </div>
                <div class="shot-card-content">
                    <div class="player-grid">
                        <!-- Player 1 -->
                        <div class="player-card">
                            <div class="player-status active"></div>
                            <img src="/avatar/SHOT avatar1.png" alt="Player" class="player-avatar">
                            <h4 style="margin: 4px 0; font-size: 14px;">Alex Smith</h4>
                            <p style="font-size: 12px; color: rgba(255, 255, 255, 0.6);">Forward</p>
                            <div style="display: flex; justify-content: center; gap: 4px; margin-top: 8px;">
                                <div class="shot-evaluation-dot shot-evaluation-dot--high"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--high"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--good"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--average"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--high"></div>
                            </div>
                        </div>
                        
                        <!-- Player 2 -->
                        <div class="player-card">
                            <div class="player-status active"></div>
                            <img src="/avatar/SHOT avatar2.png" alt="Player" class="player-avatar">
                            <h4 style="margin: 4px 0; font-size: 14px;">Jamie Brown</h4>
                            <p style="font-size: 12px; color: rgba(255, 255, 255, 0.6);">Midfielder</p>
                            <div style="display: flex; justify-content: center; gap: 4px; margin-top: 8px;">
                                <div class="shot-evaluation-dot shot-evaluation-dot--good"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--high"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--good"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--good"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--average"></div>
                            </div>
                        </div>
                        
                        <!-- Player 3 -->
                        <div class="player-card">
                            <div class="player-status inactive"></div>
                            <img src="/avatar/SHOT avatar3.png" alt="Player" class="player-avatar">
                            <h4 style="margin: 4px 0; font-size: 14px;">Jordan Taylor</h4>
                            <p style="font-size: 12px; color: rgba(255, 255, 255, 0.6);">Defender</p>
                            <div style="display: flex; justify-content: center; gap: 4px; margin-top: 8px;">
                                <div class="shot-evaluation-dot shot-evaluation-dot--not-started"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--not-started"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--not-started"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--not-started"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--not-started"></div>
                            </div>
                        </div>
                        
                        <!-- Player 4 -->
                        <div class="player-card">
                            <div class="player-status active"></div>
                            <img src="/avatar/SHOT avatar4.png" alt="Player" class="player-avatar">
                            <h4 style="margin: 4px 0; font-size: 14px;">Casey Evans</h4>
                            <p style="font-size: 12px; color: rgba(255, 255, 255, 0.6);">Goalkeeper</p>
                            <div style="display: flex; justify-content: center; gap: 4px; margin-top: 8px;">
                                <div class="shot-evaluation-dot shot-evaluation-dot--high"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--high"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--high"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--good"></div>
                                <div class="shot-evaluation-dot shot-evaluation-dot--high"></div>
                            </div>
                        </div>
                        
                        <!-- Add Player Card -->
                        <div class="player-card" style="border: 2px dashed rgba(255, 255, 255, 0.2); background: transparent;">
                            <div style="margin-top: 12px;">
                                <ion-icon name="add-circle" style="font-size: 40px; color: var(--shot-teal);"></ion-icon>
                                <h4 style="margin: 8px 0; font-size: 14px; color: var(--shot-teal);">Add Player</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Floating Action Button -->
            <div class="action-fab">
                <ion-icon name="add" style="color: white; font-size: 24px;"></ion-icon>
            </div>
        </ion-content>
    </ion-app>
    
    <script type="module" src="https://unpkg.com/@ionic/core@latest/dist/ionic/ionic.esm.js"></script>
    <script nomodule src="https://unpkg.com/@ionic/core@latest/dist/ionic/ionic.js"></script>
    <script>
        // Simulate offline sync behavior
        let syncInProgress = false;
        
        function simulateSync() {
            const syncStatus = document.getElementById('syncStatus');
            
            if (!navigator.onLine && !syncInProgress) {
                syncStatus.style.display = 'flex';
                syncInProgress = true;
            } else if (navigator.onLine && syncInProgress) {
                // Simulate sync completion
                setTimeout(() => {
                    syncStatus.style.display = 'none';
                    syncInProgress = false;
                    showToast('Team data synced successfully!');
                }, 3000);
            }
        }
        
        function showToast(message) {
            const toast = document.createElement('ion-toast');
            toast.message = message;
            toast.duration = 2000;
            toast.position = 'bottom';
            toast.color = 'success';
            
            document.body.appendChild(toast);
            toast.present();
        }
        
        // Copy invite code functionality
        document.addEventListener('DOMContentLoaded', function() {
            const copyButton = document.querySelector('ion-button[slot="start"]');
            if (copyButton) {
                copyButton.addEventListener('click', function() {
                    navigator.clipboard.writeText('CITY-U16-2024');
                    showToast('Invite code copied!');
                });
            }
        });
        
        window.addEventListener('online', simulateSync);
        window.addEventListener('offline', simulateSync);
        
        // Initial check
        simulateSync();
    </script>
</body>
</html>