<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHOT V3 Domain Mockups</title>
    <link href="https://unpkg.com/@ionic/core@latest/css/ionic.bundle.css" rel="stylesheet">
    <link href="/src/theme/shot-brand-enhanced.css" rel="stylesheet">
    <style>
        .domain-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            padding: 24px;
        }
        
        .domain-card {
            background: var(--shot-black);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--shot-button-radius);
            padding: 24px;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            display: block;
        }
        
        .domain-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
        }
        
        .domain-card.identity:hover {
            border-color: var(--shot-purple);
            box-shadow: 0 8px 24px rgba(107, 0, 219, 0.3);
        }
        
        .domain-card.organization:hover {
            border-color: var(--shot-teal);
            box-shadow: 0 8px 24px rgba(26, 188, 156, 0.3);
        }
        
        .domain-card.roles:hover {
            border-color: var(--shot-gold);
            box-shadow: 0 8px 24px rgba(247, 182, 19, 0.3);
        }
        
        .domain-card.communication:hover {
            border-color: var(--shot-burnt-orange);
            box-shadow: 0 8px 24px rgba(255, 111, 60, 0.3);
        }
        
        .domain-card.evaluation:hover {
            border-color: var(--shot-forest-green);
            box-shadow: 0 8px 24px rgba(46, 139, 87, 0.3);
        }
        
        .domain-icon {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-bottom: 16px;
        }
        
        .domain-title {
            font-family: var(--shot-font-heading);
            font-size: 20px;
            font-weight: 700;
            color: var(--shot-white);
            margin-bottom: 8px;
        }
        
        .domain-description {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            line-height: 1.4;
        }
        
        .header-banner {
            background: linear-gradient(135deg, rgba(107, 0, 219, 0.2) 0%, rgba(26, 188, 156, 0.1) 100%);
            padding: 40px 24px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature-list {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
        }
        
        .feature-item ion-icon {
            color: var(--shot-teal);
            font-size: 16px;
        }
    </style>
</head>
<body style="background-color: #000000; color: #ffffff;">
    <ion-app style="background-color: #000000;">
        <div class="header-banner">
            <h1 class="shot-h1">SHOT V3 DOMAIN MOCKUPS</h1>
            <p style="color: rgba(255, 255, 255, 0.8); max-width: 600px; margin: 16px auto;">
                Domain-Driven Design implementation mockups showcasing the new architecture 
                with offline-first capabilities and improved user experience.
            </p>
            <div style="display: flex; justify-content: center; gap: 12px; margin-top: 24px;">
                <span class="shot-badge shot-badge-teal">Offline Ready</span>
                <span class="shot-badge shot-badge-purple">DDD Architecture</span>
                <span class="shot-badge shot-badge-gold">SHOT Brand</span>
            </div>
        </div>
        
        <div class="domain-grid">
            <!-- Identity & Profile Domain -->
            <a href="identity-profile/profile-management.html" class="domain-card identity">
                <div class="domain-icon" style="background: rgba(107, 0, 219, 0.2);">
                    <ion-icon name="person-circle" style="color: var(--shot-purple);"></ion-icon>
                </div>
                <div class="domain-title">Identity & Profile</div>
                <div class="domain-description">
                    Central user management, SportHead progression, and SHOT ID system.
                </div>
                <div class="feature-list">
                    <div class="feature-item">
                        <ion-icon name="checkmark-circle"></ion-icon>
                        <span>SportHead XP & Levels</span>
                    </div>
                    <div class="feature-item">
                        <ion-icon name="checkmark-circle"></ion-icon>
                        <span>Profile Management</span>
                    </div>
                    <div class="feature-item">
                        <ion-icon name="checkmark-circle"></ion-icon>
                        <span>Privacy Controls</span>
                    </div>
                </div>
            </a>
            
            <!-- Organization Domain -->
            <a href="organization/team-management.html" class="domain-card organization">
                <div class="domain-icon" style="background: rgba(26, 188, 156, 0.2);">
                    <ion-icon name="people" style="color: var(--shot-teal);"></ion-icon>
                </div>
                <div class="domain-title">Organization</div>
                <div class="domain-description">
                    Club and team management with roster tracking and team statistics.
                </div>
                <div class="feature-list">
                    <div class="feature-item">
                        <ion-icon name="checkmark-circle"></ion-icon>
                        <span>Team Roster</span>
                    </div>
                    <div class="feature-item">
                        <ion-icon name="checkmark-circle"></ion-icon>
                        <span>Invite System</span>
                    </div>
                    <div class="feature-item">
                        <ion-icon name="checkmark-circle"></ion-icon>
                        <span>Player Status</span>
                    </div>
                </div>
            </a>
            
            <!-- People & Roles Domain -->
            <a href="people-roles/role-management.html" class="domain-card roles">
                <div class="domain-icon" style="background: rgba(247, 182, 19, 0.2);">
                    <ion-icon name="shield-checkmark" style="color: var(--shot-gold);"></ion-icon>
                </div>
                <div class="domain-title">People & Roles</div>
                <div class="domain-description">
                    Role-based access control, permissions, and team hierarchy management.
                </div>
                <div class="feature-list">
                    <div class="feature-item">
                        <ion-icon name="checkmark-circle"></ion-icon>
                        <span>Role Assignment</span>
                    </div>
                    <div class="feature-item">
                        <ion-icon name="checkmark-circle"></ion-icon>
                        <span>Permission Matrix</span>
                    </div>
                    <div class="feature-item">
                        <ion-icon name="checkmark-circle"></ion-icon>
                        <span>Approval Workflow</span>
                    </div>
                </div>
            </a>
            
            <!-- Communication Domain -->
            <a href="communication/communication-hub.html" class="domain-card communication">
                <div class="domain-icon" style="background: rgba(255, 111, 60, 0.2);">
                    <ion-icon name="chatbubbles" style="color: var(--shot-burnt-orange);"></ion-icon>
                </div>
                <div class="domain-title">Communication</div>
                <div class="domain-description">
                    Centralized messaging, notifications, and team communication channels.
                </div>
                <div class="feature-list">
                    <div class="feature-item">
                        <ion-icon name="checkmark-circle"></ion-icon>
                        <span>Multi-channel Messaging</span>
                    </div>
                    <div class="feature-item">
                        <ion-icon name="checkmark-circle"></ion-icon>
                        <span>Message Templates</span>
                    </div>
                    <div class="feature-item">
                        <ion-icon name="checkmark-circle"></ion-icon>
                        <span>Offline Queue</span>
                    </div>
                </div>
            </a>
            
            <!-- Evaluation Domain -->
            <a href="evaluation/evaluation-flow.html" class="domain-card evaluation">
                <div class="domain-icon" style="background: rgba(46, 139, 87, 0.2);">
                    <ion-icon name="analytics" style="color: var(--shot-forest-green);"></ion-icon>
                </div>
                <div class="domain-title">Evaluation</div>
                <div class="domain-description">
                    Player assessments with multi-framework support and offline capabilities.
                </div>
                <div class="feature-list">
                    <div class="feature-item">
                        <ion-icon name="checkmark-circle"></ion-icon>
                        <span>Multi-framework Support</span>
                    </div>
                    <div class="feature-item">
                        <ion-icon name="checkmark-circle"></ion-icon>
                        <span>Offline Evaluations</span>
                    </div>
                    <div class="feature-item">
                        <ion-icon name="checkmark-circle"></ion-icon>
                        <span>Team Insights</span>
                    </div>
                </div>
            </a>
        </div>
        
        <div style="text-align: center; padding: 40px 24px; color: rgba(255, 255, 255, 0.6);">
            <p>These mockups demonstrate the new domain-driven architecture for SHOT V3.</p>
            <p>Each domain is designed to be independently deployable with offline-first capabilities.</p>
        </div>
    </ion-app>
    
    <script type="module" src="https://unpkg.com/@ionic/core@latest/dist/ionic/ionic.esm.js"></script>
    <script nomodule src="https://unpkg.com/@ionic/core@latest/dist/ionic/ionic.js"></script>
</body>
</html>