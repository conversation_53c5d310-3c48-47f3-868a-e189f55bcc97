<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHOT - Domain Analysis Prompt Generator</title>
    <style>
        :root {
            --shot-black: #000000;
            --shot-white: #FFFFFF;
            --shot-teal: #1ABC9C;
            --shot-purple: #6B00DB;
            --shot-gold: #F7B613;
            --shot-grey: #9E9E9E;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--shot-black);
            color: var(--shot-white);
            line-height: 1.6;
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            color: var(--shot-teal);
            border-bottom: 2px solid var(--shot-teal);
            padding-bottom: 10px;
        }
        
        .form-section {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            color: var(--shot-teal);
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            color: var(--shot-white);
            font-family: inherit;
        }
        
        textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        button {
            background: var(--shot-purple);
            color: var(--shot-white);
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        button:hover {
            background: var(--shot-teal);
            transform: translateY(-2px);
        }
        
        .output-section {
            background: rgba(26, 188, 156, 0.1);
            border: 1px solid var(--shot-teal);
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .output-content {
            background: rgba(0, 0, 0, 0.5);
            padding: 20px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        .copy-button {
            background: var(--shot-gold);
            color: var(--shot-black);
            margin-top: 10px;
        }
        
        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .checkbox-item input {
            width: auto;
        }
        
        .info-box {
            background: rgba(247, 182, 19, 0.1);
            border: 1px solid var(--shot-gold);
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .tag {
            display: inline-block;
            background: rgba(107, 0, 219, 0.2);
            border: 1px solid var(--shot-purple);
            border-radius: 20px;
            padding: 4px 12px;
            margin: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px;">
        <a href="/domains" style="text-decoration: none; color: var(--shot-teal); display: flex; align-items: center; gap: 8px;">
            <span style="font-size: 24px;">←</span> Back to Domains
        </a>
    </div>
    <h1>🎯 SHOT Domain Analysis Prompt Generator</h1>
    
    <div class="info-box">
        <strong>📋 Instructions:</strong> Fill out this form after creating your HTML mockup. The generated prompt will help analyze API requirements and implementation approach.
    </div>
    
    <form id="promptForm">
        <!-- Basic Information -->
        <div class="form-section">
            <h2>Basic Information</h2>
            <div class="form-group">
                <label for="domainName">Domain Name</label>
                <select id="domainName" required>
                    <option value="">Select a domain...</option>
                    <option value="Identity & Profile">Identity & Profile</option>
                    <option value="Organization">Organization</option>
                    <option value="People & Roles">People & Roles</option>
                    <option value="Communication">Communication</option>
                    <option value="Evaluation">Evaluation</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="mockupPath">Mockup File Path</label>
                <input type="text" id="mockupPath" placeholder="/src/domains/evaluation/evaluation-flow.html" required>
            </div>
            
            <div class="form-group">
                <label for="domainPurpose">Domain Purpose (Brief Description)</label>
                <textarea id="domainPurpose" placeholder="Enable coaches to evaluate players during matches and training sessions with offline support" required></textarea>
            </div>
        </div>
        
        <!-- User Flows -->
        <div class="form-section">
            <h2>Key User Flows</h2>
            <div class="form-group">
                <label for="userFlow1">Primary User Flow</label>
                <input type="text" id="userFlow1" placeholder="Coach evaluates multiple players during a match" required>
            </div>
            <div class="form-group">
                <label for="userFlow2">Secondary User Flow</label>
                <input type="text" id="userFlow2" placeholder="Coach reviews team progress and insights">
            </div>
            <div class="form-group">
                <label for="userFlow3">Additional User Flow (Optional)</label>
                <input type="text" id="userFlow3" placeholder="Player views their evaluation history">
            </div>
        </div>
        
        <!-- Data Elements -->
        <div class="form-section">
            <h2>Data Elements</h2>
            <div class="form-group">
                <label for="dataElements">Key Data Displayed (comma-separated)</label>
                <textarea id="dataElements" placeholder="Player names, evaluation scores, team statistics, progress indicators, sync status" required></textarea>
            </div>
            
            <div class="form-group">
                <label for="userActions">User Actions Available (comma-separated)</label>
                <textarea id="userActions" placeholder="Create evaluation, rate player, save draft, complete session, download for offline" required></textarea>
            </div>
        </div>
        
        <!-- Features -->
        <div class="form-section">
            <h2>Feature Requirements</h2>
            <div class="form-group">
                <label>Real-time Features</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="rt1" value="Live updates">
                        <label for="rt1">Live updates</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="rt2" value="Notifications">
                        <label for="rt2">Push notifications</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="rt3" value="Collaboration">
                        <label for="rt3">Multi-user collaboration</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="rt4" value="Presence">
                        <label for="rt4">User presence indicators</label>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label>Offline Capabilities</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="of1" value="Read offline">
                        <label for="of1">Read data offline</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="of2" value="Create offline">
                        <label for="of2">Create records offline</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="of3" value="Edit offline">
                        <label for="of3">Edit records offline</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="of4" value="Queue sync">
                        <label for="of4">Queue & sync changes</label>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Database Context -->
        <div class="form-section">
            <h2>Database Context</h2>
            <div class="form-group">
                <label for="existingTables">Relevant Existing Tables (comma-separated)</label>
                <input type="text" id="existingTables" placeholder="players, teams, attributes, player_attributes">
            </div>
            
            <div class="form-group">
                <label for="performanceConcerns">Performance Concerns</label>
                <textarea id="performanceConcerns" placeholder="Need to load 20+ players quickly, handle 100+ evaluations per session"></textarea>
            </div>
        </div>
        
        <!-- Generate Button -->
        <button type="submit">🚀 Generate Analysis Prompt</button>
    </form>
    
    <!-- Output Section -->
    <div id="outputSection" class="output-section" style="display: none;">
        <h2>Generated Prompt</h2>
        <div id="outputContent" class="output-content"></div>
        <button class="copy-button" onclick="copyToClipboard()">📋 Copy to Clipboard</button>
        
        <h3 style="margin-top: 30px;">Next Steps:</h3>
        <ol>
            <li>Copy this prompt and paste it into your AI assistant</li>
            <li>Review the generated API analysis</li>
            <li>Share with your backend team for feasibility review</li>
            <li>Iterate on the UX if needed based on technical constraints</li>
            <li>Document final decisions in the domain analysis file</li>
        </ol>
    </div>
    
    <script>
        document.getElementById('promptForm').addEventListener('submit', function(e) {
            e.preventDefault();
            generatePrompt();
        });
        
        function generatePrompt() {
            // Gather form data
            const domainName = document.getElementById('domainName').value;
            const mockupPath = document.getElementById('mockupPath').value;
            const domainPurpose = document.getElementById('domainPurpose').value;
            const userFlow1 = document.getElementById('userFlow1').value;
            const userFlow2 = document.getElementById('userFlow2').value;
            const userFlow3 = document.getElementById('userFlow3').value;
            const dataElements = document.getElementById('dataElements').value;
            const userActions = document.getElementById('userActions').value;
            const existingTables = document.getElementById('existingTables').value;
            const performanceConcerns = document.getElementById('performanceConcerns').value;
            
            // Get checked features
            const realtimeFeatures = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                .filter(cb => cb.id.startsWith('rt'))
                .map(cb => cb.value);
            
            const offlineFeatures = Array.from(document.querySelectorAll('input[type="checkbox"]:checked'))
                .filter(cb => cb.id.startsWith('of'))
                .map(cb => cb.value);
            
            // Build user flows list
            let userFlows = [`1. ${userFlow1}`];
            if (userFlow2) userFlows.push(`2. ${userFlow2}`);
            if (userFlow3) userFlows.push(`3. ${userFlow3}`);
            
            // Generate the prompt
            const prompt = `# Domain UX-to-API Analysis Prompt

## Context
I have created a UX mockup for the ${domainName} domain in a domain-driven architecture sports application.

## Mockup Details
- **File Location**: ${mockupPath}
- **Domain Purpose**: ${domainPurpose}
- **Key User Flows**: 
${userFlows.map(f => `  ${f}`).join('\n')}

## Current Mockup Features
- **Data Displayed**: ${dataElements}
- **User Actions**: ${userActions}
- **Real-time Requirements**: ${realtimeFeatures.length > 0 ? realtimeFeatures.join(', ') : 'None'}
- **Offline Capabilities**: ${offlineFeatures.length > 0 ? offlineFeatures.join(', ') : 'None'}

## Task
Please analyze this mockup and provide:

### 1. API Requirements Extraction
- List all data entities needed
- Identify CRUD operations required
- Map relationships between entities
- Specify query patterns (filters, sorts, pagination)

### 2. Suggested API Endpoints
Provide REST/GraphQL endpoint designs with:
- Endpoint paths
- Request/response schemas
- Query parameters
- Error states

### 3. State Management Requirements
- Local state needs
- Global state needs
- Offline queue requirements
- Sync strategies

### 4. Data Model Assessment
Based on these tables: ${existingTables}
- Can existing schema support this UX?
- What modifications are needed?
- Are there performance concerns?

${performanceConcerns ? `### 5. Performance Considerations
${performanceConcerns}` : ''}

### 6. Offline Strategy
- What data needs offline storage?
- Sync conflict resolution approach
- Queue management for offline actions

### 7. Implementation Priority
Rank features by:
- User value (High/Medium/Low)
- Technical complexity (High/Medium/Low)
- Dependencies

### 8. Alternative UX Suggestions
If any requirements are technically prohibitive, suggest minimal UX modifications that preserve core value.

## Constraints
- Must maintain domain boundaries
- Minimize cross-domain dependencies
- Support offline-first architecture
- Use existing auth patterns`;
            
            // Display the output
            document.getElementById('outputContent').textContent = prompt;
            document.getElementById('outputSection').style.display = 'block';
            
            // Scroll to output
            document.getElementById('outputSection').scrollIntoView({ behavior: 'smooth' });
        }
        
        function copyToClipboard() {
            const text = document.getElementById('outputContent').textContent;
            navigator.clipboard.writeText(text).then(() => {
                // Change button text temporarily
                const btn = document.querySelector('.copy-button');
                const originalText = btn.textContent;
                btn.textContent = '✅ Copied!';
                btn.style.background = 'var(--shot-green)';
                
                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = 'var(--shot-gold)';
                }, 2000);
            });
        }
    </script>
</body>
</html>