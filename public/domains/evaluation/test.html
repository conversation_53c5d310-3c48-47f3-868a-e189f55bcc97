<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHOT - Player Evaluation</title>
    <link href="https://unpkg.com/@ionic/core@latest/css/ionic.bundle.css" rel="stylesheet">
    <link href="../../theme/shot-brand-enhanced.css" rel="stylesheet">
    <style>
        /* Evaluation domain-specific styles */
        .evaluation-header {
            background: linear-gradient(135deg, rgba(26, 188, 156, 0.1) 0%, rgba(107, 0, 219, 0.1) 100%);
            border-radius: var(--shot-button-radius);
            padding: 20px;
            margin-bottom: 24px;
            position: relative;
        }
        
        .framework-selector {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            overflow-x: auto;
            padding-bottom: 8px;
        }
        
        .framework-chip {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 8px 16px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .framework-chip.active {
            background: var(--shot-purple);
            border-color: var(--shot-purple);
            color: white;
        }
        
        .player-evaluation-card {
            background: var(--shot-black);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--shot-button-radius);
            margin-bottom: 16px;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .player-evaluation-card.offline-pending {
            border-color: var(--shot-burnt-orange);
        }
        
        .player-evaluation-card.completed {
            border-left: 4px solid var(--shot-green);
        }
        
        .evaluation-progress-bar {
            height: 12px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            overflow: hidden;
            margin: 12px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--shot-teal), var(--shot-purple));
            transition: width 0.5s ease;
            position: relative;
        }
        
        .progress-segments {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
        }
        
        .progress-segment {
            flex: 1;
            border-right: 1px dashed rgba(255, 255, 255, 0.2);
        }
        
        .progress-segment:last-child {
            border-right: none;
        }
        
        .category-rating {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            margin-bottom: 12px;
        }
        
        .category-icon-wrapper {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .rating-slider {
            flex: 1;
        }
        
        .rating-value {
            min-width: 60px;
            text-align: center;
            font-size: 18px;
            font-weight: 700;
        }
        
        .offline-indicator {
            position: fixed;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--shot-burnt-orange);
            color: var(--shot-black);
            padding: 12px 24px;
            border-radius: 24px;
            display: flex;
            align-items: center;
            gap: 8px;
            z-index: 1000;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .sync-queue-counter {
            background: rgba(0, 0, 0, 0.3);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 8px;
        }
        
        .insight-card {
            background: rgba(26, 188, 156, 0.1);
            border: 1px solid var(--shot-teal);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
        }
        
        .insight-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .comparison-chart {
            display: flex;
            justify-content: space-around;
            padding: 20px 0;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            margin: 16px 0;
        }
        
        .comparison-item {
            text-align: center;
        }
        
        .comparison-bar {
            width: 40px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            margin: 0 auto 8px;
            display: flex;
            align-items: flex-end;
            overflow: hidden;
        }
        
        .comparison-fill {
            width: 100%;
            background: var(--shot-teal);
            transition: height 0.5s ease;
        }
        
        .question-popup {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--shot-black);
            border: 2px solid var(--shot-purple);
            border-radius: var(--shot-button-radius);
            padding: 24px;
            max-width: 90%;
            width: 400px;
            z-index: 2000;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5);
        }
        
        .question-progress {
            display: flex;
            gap: 4px;
            margin-bottom: 20px;
        }
        
        .question-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .question-dot.active {
            background: var(--shot-purple);
            transform: scale(1.5);
        }
        
        .question-dot.completed {
            background: var(--shot-teal);
        }
        
        .download-status {
            background: rgba(107, 0, 219, 0.1);
            border: 1px solid var(--shot-purple);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            text-align: center;
        }
        
        .download-progress {
            margin: 12px 0;
        }
    </style>
</head>
<body>
    <ion-app>
        <ion-header>
            <ion-toolbar class="shot-toolbar">
                <ion-buttons slot="start">
                    <ion-back-button defaultHref="/coach"></ion-back-button>
                </ion-buttons>
                <ion-title>Player Evaluation</ion-title>
                <ion-buttons slot="end">
                    <ion-button id="downloadBtn">
                        <ion-icon name="cloud-download-outline"></ion-icon>
                    </ion-button>
                    <ion-button>
                        <ion-icon name="calendar-outline"></ion-icon>
                    </ion-button>
                </ion-buttons>
            </ion-toolbar>
        </ion-header>
        
        <ion-content class="ion-padding bg-black">
            <!-- Offline Indicator -->
            <div class="offline-indicator" id="offlineIndicator" style="display: none;">
                <ion-icon name="cloud-offline"></ion-icon>
                <span>Offline Mode</span>
                <span class="sync-queue-counter">3</span>
            </div>
            
            <!-- Download Status -->
            <div class="download-status" id="downloadStatus" style="display: none;">
                <ion-icon name="cloud-download" style="font-size: 32px; color: var(--shot-purple); margin-bottom: 8px;"></ion-icon>
                <h4 style="margin: 0 0 8px 0;">Downloading Team Data</h4>
                <div class="download-progress">
                    <ion-progress-bar value="0.7" color="primary"></ion-progress-bar>
                </div>
                <p style="margin: 0; font-size: 14px; color: rgba(255, 255, 255, 0.7);">
                    70% - Frameworks and player data
                </p>
            </div>
            
            <!-- Evaluation Header -->
            <div class="evaluation-header">
                <h2 class="shot-h2" style="margin: 0 0 8px 0;">Weekly Evaluation</h2>
                <p style="color: rgba(255, 255, 255, 0.7); margin: 0;">
                    Saturday, June 14, 2025 • Training Session
                </p>
                <div style="display: flex; gap: 8px; margin-top: 12px;">
                    <span class="shot-badge shot-badge-teal">SHOT PERFORM</span>
                    <span class="shot-badge shot-badge-purple">Week 12</span>
                </div>
            </div>
            
            <!-- Framework Selector -->
            <div class="framework-selector">
                <div class="framework-chip active">SHOT PERFORM 2024/25</div>
                <div class="framework-chip">Summer Camp</div>
                <div class="framework-chip">Tournament</div>
                <div class="framework-chip">Skills Clinic</div>
            </div>
            
            <!-- Evaluation Progress Overview -->
            <div class="shot-card" style="margin-bottom: 24px;">
                <div class="shot-card-header">
                    <h3 class="shot-card-title">Team Progress</h3>
                </div>
                <div class="shot-card-content">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>12 of 18 players evaluated</span>
                        <span style="color: var(--shot-teal); font-weight: 600;">67%</span>
                    </div>
                    <div class="evaluation-progress-bar">
                        <div class="progress-fill" style="width: 67%;">
                            <div class="progress-segments">
                                <div class="progress-segment"></div>
                                <div class="progress-segment"></div>
                                <div class="progress-segment"></div>
                                <div class="progress-segment"></div>
                            </div>
                        </div>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 12px;">
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 700; color: var(--shot-teal);">12</div>
                            <div style="font-size: 12px; color: rgba(255, 255, 255, 0.6);">Completed</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 700; color: var(--shot-gold);">3</div>
                            <div style="font-size: 12px; color: rgba(255, 255, 255, 0.6);">In Progress</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 700; color: var(--shot-grey);">3</div>
                            <div style="font-size: 12px; color: rgba(255, 255, 255, 0.6);">Not Started</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Player Evaluations -->
            <div style="margin-bottom: 80px;">
                <!-- Player 1 - Completed -->
                <div class="player-evaluation-card completed">
                    <div style="padding: 16px;">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <img src="/avatar/SHOT avatar1.png" alt="Player" 
                                     style="width: 48px; height: 48px; border-radius: 50%;">
                                <div>
                                    <h4 style="margin: 0;">Alex Smith</h4>
                                    <p style="margin: 0; font-size: 14px; color: rgba(255, 255, 255, 0.6);">
                                        Forward • Completed
                                    </p>
                                </div>
                            </div>
                            <ion-button fill="clear">
                                <ion-icon name="checkmark-circle" style="color: var(--shot-green); font-size: 24px;"></ion-icon>
                            </ion-button>
                        </div>
                        
                        <!-- Quick Rating Display -->
                        <div style="display: flex; justify-content: center; gap: 4px; margin-top: 12px;">
                            <div class="shot-evaluation-dot shot-evaluation-dot--high"></div>
                            <div class="shot-evaluation-dot shot-evaluation-dot--high"></div>
                            <div class="shot-evaluation-dot shot-evaluation-dot--good"></div>
                            <div class="shot-evaluation-dot shot-evaluation-dot--average"></div>
                            <div class="shot-evaluation-dot shot-evaluation-dot--high"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Player 2 - In Progress (Expanded) -->
                <div class="player-evaluation-card" style="border: 2px solid var(--shot-purple);">
                    <div style="padding: 16px;">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <img src="/avatar/SHOT avatar2.png" alt="Player" 
                                     style="width: 48px; height: 48px; border-radius: 50%;">
                                <div>
                                    <h4 style="margin: 0;">Jamie Brown</h4>
                                    <p style="margin: 0; font-size: 14px; color: rgba(255, 255, 255, 0.6);">
                                        Midfielder • In Progress
                                    </p>
                                </div>
                            </div>
                            <ion-button fill="clear">
                                <ion-icon name="chevron-up"></ion-icon>
                            </ion-button>
                        </div>
                        
                        <!-- Expanded Evaluation Form -->
                        <div style="margin-top: 20px;">
                            <!-- Technical -->
                            <div class="category-rating">
                                <div class="category-icon-wrapper" style="background: rgba(59, 130, 246, 0.2);">
                                    ⚽
                                </div>
                                <div style="flex: 1;">
                                    <h5 style="margin: 0 0 4px 0; color: #3b82f6;">Ball Control : First Touch</h5>
                                    <p style="margin: 0; font-size: 12px; color: rgba(255, 255, 255, 0.6);">
                                        Can you control the ball with one touch?
                                    </p>
                                </div>
                                <div class="rating-value" style="color: #3b82f6;">4</div>
                            </div>
                            
                            <!-- Physical -->
                            <div class="category-rating">
                                <div class="category-icon-wrapper" style="background: rgba(239, 68, 68, 0.2);">
                                    💪
                                </div>
                                <div style="flex: 1;">
                                    <h5 style="margin: 0 0 4px 0; color: #ef4444;">Speed : Acceleration</h5>
                                    <p style="margin: 0; font-size: 12px; color: rgba(255, 255, 255, 0.6);">
                                        How quickly can you reach top speed?
                                    </p>
                                </div>
                                <div class="rating-value" style="color: #ef4444;">3</div>
                            </div>
                            
                            <!-- Psychological -->
                            <div class="category-rating">
                                <div class="category-icon-wrapper" style="background: rgba(245, 158, 11, 0.2);">
                                    🧠
                                </div>
                                <div style="flex: 1;">
                                    <h5 style="margin: 0 0 4px 0; color: #f59e0b;">Focus : Concentration</h5>
                                    <p style="margin: 0; font-size: 12px; color: rgba(255, 255, 255, 0.6);">
                                        Can you maintain focus throughout training?
                                    </p>
                                </div>
                                <div class="rating-value" style="color: #f59e0b;">4</div>
                            </div>
                            
                            <!-- Save Button -->
                            <div style="display: flex; gap: 12px; margin-top: 20px;">
                                <ion-button expand="block" style="flex: 1;">
                                    Save & Continue
                                </ion-button>
                                <ion-button expand="block" fill="outline" style="flex: 1;">
                                    Save for Later
                                </ion-button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Player 3 - Offline Pending -->
                <div class="player-evaluation-card offline-pending">
                    <div style="padding: 16px;">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <img src="/avatar/SHOT avatar3.png" alt="Player" 
                                     style="width: 48px; height: 48px; border-radius: 50%;">
                                <div>
                                    <h4 style="margin: 0;">Jordan Taylor</h4>
                                    <p style="margin: 0; font-size: 14px; color: rgba(255, 255, 255, 0.6);">
                                        Defender • Pending Sync
                                    </p>
                                </div>
                            </div>
                            <ion-icon name="cloud-offline" style="color: var(--shot-burnt-orange); font-size: 24px;"></ion-icon>
                        </div>
                        
                        <div style="display: flex; justify-content: center; gap: 4px; margin-top: 12px;">
                            <div class="shot-evaluation-dot shot-evaluation-dot--good"></div>
                            <div class="shot-evaluation-dot shot-evaluation-dot--good"></div>
                            <div class="shot-evaluation-dot shot-evaluation-dot--average"></div>
                            <div class="shot-evaluation-dot shot-evaluation-dot--good"></div>
                            <div class="shot-evaluation-dot shot-evaluation-dot--high"></div>
                        </div>
                    </div>
                </div>
                
                <!-- Player 4 - Not Started -->
                <div class="player-evaluation-card">
                    <div style="padding: 16px;">
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <img src="/avatar/SHOT avatar4.png" alt="Player" 
                                     style="width: 48px; height: 48px; border-radius: 50%;">
                                <div>
                                    <h4 style="margin: 0;">Casey Evans</h4>
                                    <p style="margin: 0; font-size: 14px; color: rgba(255, 255, 255, 0.6);">
                                        Goalkeeper • Not Started
                                    </p>
                                </div>
                            </div>
                            <ion-button fill="clear">
                                <ion-icon name="chevron-down"></ion-icon>
                            </ion-button>
                        </div>
                        
                        <div style="display: flex; justify-content: center; gap: 4px; margin-top: 12px;">
                            <div class="shot-evaluation-dot shot-evaluation-dot--not-started"></div>
                            <div class="shot-evaluation-dot shot-evaluation-dot--not-started"></div>
                            <div class="shot-evaluation-dot shot-evaluation-dot--not-started"></div>
                            <div class="shot-evaluation-dot shot-evaluation-dot--not-started"></div>
                            <div class="shot-evaluation-dot shot-evaluation-dot--not-started"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Insights Section -->
            <div class="shot-card" style="margin-bottom: 24px;">
                <div class="shot-card-header">
                    <h3 class="shot-card-title">Team Insights</h3>
                </div>
                <div class="shot-card-content">
                    <div class="insight-card">
                        <div class="insight-icon">📈</div>
                        <h4 style="margin: 0 0 4px 0;">Improvement Trend</h4>
                        <p style="margin: 0; font-size: 14px;">
                            Technical skills showing 15% improvement across the team compared to last month.
                        </p>
                    </div>
                    
                    <div class="insight-card" style="background: rgba(247, 182, 19, 0.1); border-color: var(--shot-gold);">
                        <div class="insight-icon">⚡</div>
                        <h4 style="margin: 0 0 4px 0;">Focus Area</h4>
                        <p style="margin: 0; font-size: 14px;">
                            Physical conditioning scores are below average. Consider additional fitness sessions.
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div style="position: fixed; bottom: 0; left: 0; right: 0; background: var(--shot-black); 
                        border-top: 1px solid rgba(255, 255, 255, 0.1); padding: 16px; display: flex; gap: 12px;">
                <ion-button expand="block" style="flex: 1;" id="syncBtn">
                    <ion-icon name="sync" slot="start"></ion-icon>
                    Sync All
                </ion-button>
                <ion-button expand="block" color="success" style="flex: 1;">
                    <ion-icon name="checkmark-done" slot="start"></ion-icon>
                    Complete Session
                </ion-button>
            </div>
        </ion-content>
    </ion-app>
    
    <script type="module" src="https://unpkg.com/@ionic/core@latest/dist/ionic/ionic.esm.js"></script>
    <script nomodule src="https://unpkg.com/@ionic/core@latest/dist/ionic/ionic.js"></script>
    <script>
        // Offline functionality
        let offlineQueue = [];
        let isDownloading = false;
        
        function updateOfflineUI() {
            const offlineIndicator = document.getElementById('offlineIndicator');
            const syncBtn = document.getElementById('syncBtn');
            
            if (!navigator.onLine) {
                offlineIndicator.style.display = 'flex';
                syncBtn.disabled = true;
                console.log('Offline mode - evaluations will be queued');
            } else {
                offlineIndicator.style.display = 'none';
                syncBtn.disabled = false;
                
                if (offlineQueue.length > 0) {
                    syncOfflineData();
                }
            }
        }
        
        function syncOfflineData() {
            console.log('Syncing offline data...');
            const queueCounter = document.querySelector('.sync-queue-counter');
            
            // Simulate sync process
            let remaining = offlineQueue.length;
            const interval = setInterval(() => {
                remaining--;
                queueCounter.textContent = remaining;
                
                if (remaining === 0) {
                    clearInterval(interval);
                    offlineQueue = [];
                    showToast('All evaluations synced successfully!', 'success');
                }
            }, 1000);
        }
        
        // Download functionality
        document.getElementById('downloadBtn').addEventListener('click', function() {
            if (isDownloading) return;
            
            isDownloading = true;
            const downloadStatus = document.getElementById('downloadStatus');
            downloadStatus.style.display = 'block';
            
            // Simulate download progress
            const progressBar = downloadStatus.querySelector('ion-progress-bar');
            let progress = 0;
            
            const downloadInterval = setInterval(() => {
                progress += 0.1;
                progressBar.value = Math.min(progress, 1);
                
                if (progress >= 1) {
                    clearInterval(downloadInterval);
                    setTimeout(() => {
                        downloadStatus.style.display = 'none';
                        showToast('Team data downloaded for offline use', 'success');
                        isDownloading = false;
                    }, 500);
                }
            }, 300);
        });
        
        // Sync button functionality
        document.getElementById('syncBtn').addEventListener('click', function() {
            if (navigator.onLine && offlineQueue.length === 0) {
                showToast('All evaluations are already synced', 'primary');
            }
        });
        
        function showToast(message, color = 'primary') {
            const toast = document.createElement('ion-toast');
            toast.message = message;
            toast.duration = 3000;
            toast.position = 'bottom';
            toast.color = color;
            
            document.body.appendChild(toast);
            toast.present();
        }
        
        // Simulate offline evaluation saves
        document.addEventListener('DOMContentLoaded', function() {
            const saveButtons = document.querySelectorAll('ion-button[expand="block"]');
            saveButtons.forEach(button => {
                if (button.textContent.includes('Save')) {
                    button.addEventListener('click', function() {
                        if (!navigator.onLine) {
                            offlineQueue.push({
                                playerId: 'player-' + Date.now(),
                                timestamp: new Date(),
                                data: {} // Evaluation data
                            });
                            
                            const queueCounter = document.querySelector('.sync-queue-counter');
                            queueCounter.textContent = offlineQueue.length;
                            
                            showToast('Evaluation saved offline', 'warning');
                        } else {
                            showToast('Evaluation saved', 'success');
                        }
                    });
                }
            });
        });
        
        // Monitor online/offline status
        window.addEventListener('online', updateOfflineUI);
        window.addEventListener('offline', updateOfflineUI);
        
        // Initial check
        updateOfflineUI();
    </script>
</body>
</html>