<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHOT - Profile Management</title>
    <link href="https://unpkg.com/@ionic/core@latest/css/ionic.bundle.css" rel="stylesheet">
    <link href="/src/theme/shot-brand-enhanced.css" rel="stylesheet">
    <style>
        /* Additional domain-specific styles */
        .profile-header {
            background: linear-gradient(135deg, rgba(107, 0, 219, 0.1) 0%, rgba(26, 188, 156, 0.1) 100%);
            padding: 24px;
            border-radius: 16px;
            margin-bottom: 24px;
            position: relative;
        }
        
        .shot-id-badge {
            background: var(--shot-purple);
            color: var(--shot-white);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .sporthead-card {
            background: var(--shot-black);
            border: 2px solid var(--shot-teal);
            border-radius: var(--shot-button-radius);
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .sporthead-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(26, 188, 156, 0.3);
        }
        
        .sporthead-level {
            font-size: 48px;
            font-weight: 800;
            color: var(--shot-gold);
            margin: 16px 0;
        }
        
        .xp-progress {
            background: rgba(255, 255, 255, 0.1);
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin: 16px 0;
        }
        
        .xp-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--shot-teal), var(--shot-purple));
            transition: width 0.5s ease;
        }
        
        .offline-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--shot-burnt-orange);
            color: var(--shot-black);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            z-index: 1000;
        }
        
        .sync-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--shot-green);
        }
        
        .sync-badge.pending {
            background: var(--shot-gold);
            animation: pulse 2s infinite;
        }
        
        .profile-metric {
            background: rgba(255, 255, 255, 0.05);
            padding: 16px;
            border-radius: 8px;
            text-align: center;
        }
        
        .profile-metric-value {
            font-size: 32px;
            font-weight: 700;
            color: var(--shot-teal);
        }
        
        .profile-metric-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 4px;
        }
    </style>
</head>
<body>
    <ion-app>
        <ion-header>
            <ion-toolbar class="shot-toolbar">
                <ion-buttons slot="start">
                    <ion-back-button defaultHref="/"></ion-back-button>
                </ion-buttons>
                <ion-title>My Profile</ion-title>
                <ion-buttons slot="end">
                    <ion-button>
                        <ion-icon name="settings-outline"></ion-icon>
                    </ion-button>
                </ion-buttons>
            </ion-toolbar>
        </ion-header>
        
        <ion-content class="ion-padding bg-black">
            <!-- Offline Indicator -->
            <div class="offline-indicator" style="display: none;">
                <ion-icon name="cloud-offline"></ion-icon>
                Offline Mode
            </div>
            
            <!-- Profile Header -->
            <div class="profile-header">
                <div class="sync-badge pending"></div>
                <div class="text-center">
                    <div style="position: relative; display: inline-block;">
                        <img src="/avatar/SHOT avatar1.png" alt="Profile" 
                             style="width: 120px; height: 120px; border-radius: 50%; border: 3px solid var(--shot-teal);">
                        <div style="position: absolute; bottom: 0; right: 0; background: var(--shot-purple); 
                                    width: 36px; height: 36px; border-radius: 50%; display: flex; 
                                    align-items: center; justify-content: center; border: 3px solid var(--shot-black);">
                            <ion-icon name="camera" style="color: white; font-size: 18px;"></ion-icon>
                        </div>
                    </div>
                    
                    <h2 class="shot-h2" style="margin-top: 16px;">Jordan Taylor</h2>
                    <div class="shot-id-badge">
                        <ion-icon name="fingerprint"></ion-icon>
                        SHOT ID: JT-2024-1234
                    </div>
                    
                    <!-- Profile Metrics -->
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px; margin-top: 24px;">
                        <div class="profile-metric">
                            <div class="profile-metric-value">15</div>
                            <div class="profile-metric-label">Age</div>
                        </div>
                        <div class="profile-metric">
                            <div class="profile-metric-value">FWD</div>
                            <div class="profile-metric-label">Position</div>
                        </div>
                        <div class="profile-metric">
                            <div class="profile-metric-value">U16</div>
                            <div class="profile-metric-label">Team</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- SportHead Section -->
            <div class="shot-card" style="margin-bottom: 24px;">
                <div class="shot-card-header">
                    <h3 class="shot-card-title">My SportHead</h3>
                </div>
                <div class="shot-card-content">
                    <div class="sporthead-card">
                        <img src="/SportHeads/Comet.svg" alt="Comet SportHead" style="width: 100px; height: 100px;">
                        <h4 style="color: var(--shot-teal); margin: 8px 0;">Comet</h4>
                        <div class="sporthead-level">12</div>
                        <div style="color: rgba(255, 255, 255, 0.7); font-size: 14px; margin-bottom: 8px;">
                            2,450 / 3,000 XP
                        </div>
                        <div class="xp-progress">
                            <div class="xp-bar" style="width: 81.67%;"></div>
                        </div>
                        <div style="display: flex; justify-content: center; gap: 8px; margin-top: 16px;">
                            <span class="shot-badge shot-badge-teal">Rising Star</span>
                            <span class="shot-badge shot-badge-purple">Team Player</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Profile Information -->
            <div class="shot-card" style="margin-bottom: 24px;">
                <div class="shot-card-header">
                    <h3 class="shot-card-title">Profile Information</h3>
                </div>
                <div class="shot-card-content">
                    <ion-list lines="none">
                        <ion-item>
                            <ion-label>
                                <p>Full Name</p>
                                <h3>Jordan Taylor</h3>
                            </ion-label>
                            <ion-button fill="clear" slot="end">
                                <ion-icon name="pencil"></ion-icon>
                            </ion-button>
                        </ion-item>
                        
                        <ion-item>
                            <ion-label>
                                <p>Date of Birth</p>
                                <h3>March 15, 2009</h3>
                            </ion-label>
                        </ion-item>
                        
                        <ion-item>
                            <ion-label>
                                <p>Preferred Foot</p>
                                <h3>Right</h3>
                            </ion-label>
                            <ion-button fill="clear" slot="end">
                                <ion-icon name="pencil"></ion-icon>
                            </ion-button>
                        </ion-item>
                        
                        <ion-item>
                            <ion-label>
                                <p>Height</p>
                                <h3>168 cm</h3>
                            </ion-label>
                            <ion-button fill="clear" slot="end">
                                <ion-icon name="pencil"></ion-icon>
                            </ion-button>
                        </ion-item>
                        
                        <ion-item>
                            <ion-label>
                                <p>Weight</p>
                                <h3>58 kg</h3>
                            </ion-label>
                            <ion-button fill="clear" slot="end">
                                <ion-icon name="pencil"></ion-icon>
                            </ion-button>
                        </ion-item>
                    </ion-list>
                </div>
            </div>
            
            <!-- Privacy & Consent -->
            <div class="shot-card">
                <div class="shot-card-header">
                    <h3 class="shot-card-title">Privacy & Consent</h3>
                </div>
                <div class="shot-card-content">
                    <ion-list lines="none">
                        <ion-item>
                            <ion-label>
                                <h3>Data Sharing</h3>
                                <p>Control who can see your profile</p>
                            </ion-label>
                            <ion-toggle slot="end" checked></ion-toggle>
                        </ion-item>
                        
                        <ion-item>
                            <ion-label>
                                <h3>Parent/Guardian Access</h3>
                                <p>Sarah Taylor (Mother)</p>
                            </ion-label>
                            <ion-badge color="success" slot="end">Verified</ion-badge>
                        </ion-item>
                        
                        <ion-item button>
                            <ion-label>
                                <h3>Download My Data</h3>
                                <p>Export all your SHOT data</p>
                            </ion-label>
                            <ion-icon name="download-outline" slot="end"></ion-icon>
                        </ion-item>
                    </ion-list>
                </div>
            </div>
        </ion-content>
    </ion-app>
    
    <script type="module" src="https://unpkg.com/@ionic/core@latest/dist/ionic/ionic.esm.js"></script>
    <script nomodule src="https://unpkg.com/@ionic/core@latest/dist/ionic/ionic.js"></script>
    <script>
        // Simulate offline detection
        function updateOfflineStatus() {
            const offlineIndicator = document.querySelector('.offline-indicator');
            const syncBadges = document.querySelectorAll('.sync-badge');
            
            if (!navigator.onLine) {
                offlineIndicator.style.display = 'flex';
                syncBadges.forEach(badge => badge.classList.add('pending'));
            } else {
                offlineIndicator.style.display = 'none';
                // Simulate sync completion after 2 seconds
                setTimeout(() => {
                    syncBadges.forEach(badge => badge.classList.remove('pending'));
                }, 2000);
            }
        }
        
        window.addEventListener('online', updateOfflineStatus);
        window.addEventListener('offline', updateOfflineStatus);
        
        // Initial check
        updateOfflineStatus();
    </script>
</body>
</html>