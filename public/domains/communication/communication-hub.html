<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHOT - Team Communication</title>
    <link href="https://unpkg.com/@ionic/core@latest/css/ionic.bundle.css" rel="stylesheet">
    <link href="/src/theme/shot-brand-enhanced.css" rel="stylesheet">
    <style>
        /* Communication domain-specific styles */
        .message-thread {
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--shot-button-radius);
            padding: 16px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }
        
        .message-thread:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }
        
        .message-thread.unread {
            border-left: 3px solid var(--shot-teal);
        }
        
        .message-badge {
            position: absolute;
            top: 16px;
            right: 16px;
            background: var(--shot-purple);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        
        .channel-card {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--shot-button-radius);
            padding: 20px;
            margin-bottom: 16px;
            transition: all 0.2s ease;
        }
        
        .channel-card.active {
            border-color: var(--shot-teal);
            background: rgba(26, 188, 156, 0.05);
        }
        
        .channel-icon {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
        }
        
        .announcement-banner {
            background: linear-gradient(135deg, rgba(247, 182, 19, 0.2) 0%, rgba(107, 0, 219, 0.1) 100%);
            border: 1px solid var(--shot-gold);
            border-radius: var(--shot-button-radius);
            padding: 16px;
            margin-bottom: 24px;
            position: relative;
        }
        
        .announcement-banner::before {
            content: '📢';
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 24px;
        }
        
        .notification-item {
            display: flex;
            align-items: start;
            gap: 12px;
            padding: 16px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .notification-icon.evaluation {
            background: rgba(26, 188, 156, 0.2);
            color: var(--shot-teal);
        }
        
        .notification-icon.event {
            background: rgba(107, 0, 219, 0.2);
            color: var(--shot-purple);
        }
        
        .notification-icon.message {
            background: rgba(247, 182, 19, 0.2);
            color: var(--shot-gold);
        }
        
        .chat-bubble {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 16px;
            margin-bottom: 8px;
        }
        
        .chat-bubble.sent {
            background: var(--shot-purple);
            color: white;
            margin-left: auto;
            border-bottom-right-radius: 4px;
        }
        
        .chat-bubble.received {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-bottom-left-radius: 4px;
        }
        
        .preference-toggle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .preference-toggle:last-child {
            border-bottom: none;
        }
        
        .offline-queue-indicator {
            background: rgba(255, 111, 60, 0.1);
            border: 1px solid var(--shot-burnt-orange);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .template-preview {
            background: rgba(255, 255, 255, 0.03);
            border: 1px dashed rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
            font-family: monospace;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .quick-action-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 24px;
        }
        
        .quick-action-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--shot-button-radius);
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .quick-action-card:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <ion-app>
        <ion-header>
            <ion-toolbar class="shot-toolbar">
                <ion-buttons slot="start">
                    <ion-back-button defaultHref="/coach"></ion-back-button>
                </ion-buttons>
                <ion-title>Communication Hub</ion-title>
                <ion-buttons slot="end">
                    <ion-button>
                        <ion-badge color="danger" style="position: absolute; top: 4px; right: 4px; font-size: 10px;">5</ion-badge>
                        <ion-icon name="notifications-outline"></ion-icon>
                    </ion-button>
                </ion-buttons>
            </ion-toolbar>
        </ion-header>
        
        <ion-content class="ion-padding bg-black">
            <!-- Offline Queue Indicator -->
            <div class="offline-queue-indicator" id="offlineQueue" style="display: none;">
                <ion-icon name="cloud-offline" style="color: var(--shot-burnt-orange); font-size: 24px;"></ion-icon>
                <div style="flex: 1;">
                    <div style="font-weight: 600; color: var(--shot-burnt-orange);">Offline Mode</div>
                    <div style="font-size: 12px; color: rgba(255, 255, 255, 0.6);">3 messages queued for sending</div>
                </div>
            </div>
            
            <!-- Active Announcement -->
            <div class="announcement-banner">
                <div style="padding-left: 40px;">
                    <h4 style="margin: 0 0 4px 0; color: var(--shot-gold);">Team Announcement</h4>
                    <p style="margin: 0; font-size: 14px;">
                        Next match moved to Saturday 2PM due to weather. Please confirm attendance.
                    </p>
                    <div style="display: flex; gap: 12px; margin-top: 8px;">
                        <ion-button size="small" color="success">
                            <ion-icon name="checkmark" slot="start"></ion-icon>
                            Attending
                        </ion-button>
                        <ion-button size="small" fill="outline" color="medium">
                            <ion-icon name="close" slot="start"></ion-icon>
                            Can't Make It
                        </ion-button>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="quick-action-grid">
                <div class="quick-action-card">
                    <ion-icon name="megaphone" style="font-size: 32px; color: var(--shot-purple); margin-bottom: 8px;"></ion-icon>
                    <h4 style="margin: 0; font-size: 14px;">Broadcast</h4>
                </div>
                <div class="quick-action-card">
                    <ion-icon name="mail" style="font-size: 32px; color: var(--shot-teal); margin-bottom: 8px;"></ion-icon>
                    <h4 style="margin: 0; font-size: 14px;">Message</h4>
                </div>
                <div class="quick-action-card">
                    <ion-icon name="calendar" style="font-size: 32px; color: var(--shot-gold); margin-bottom: 8px;"></ion-icon>
                    <h4 style="margin: 0; font-size: 14px;">Event Invite</h4>
                </div>
                <div class="quick-action-card">
                    <ion-icon name="document-text" style="font-size: 32px; color: var(--shot-burnt-orange); margin-bottom: 8px;"></ion-icon>
                    <h4 style="margin: 0; font-size: 14px;">Template</h4>
                </div>
            </div>
            
            <!-- Communication Channels -->
            <div class="shot-card" style="margin-bottom: 24px;">
                <div class="shot-card-header">
                    <h3 class="shot-card-title">Communication Channels</h3>
                </div>
                <div class="shot-card-content">
                    <div class="channel-card active">
                        <div style="display: flex; align-items: center;">
                            <div class="channel-icon" style="background: rgba(26, 188, 156, 0.2);">
                                <ion-icon name="people" style="font-size: 24px; color: var(--shot-teal);"></ion-icon>
                            </div>
                            <div style="flex: 1; margin-left: 16px;">
                                <h4 style="margin: 0;">Team Channel</h4>
                                <p style="margin: 4px 0; font-size: 14px; color: rgba(255, 255, 255, 0.7);">
                                    All players and coaches • 21 members
                                </p>
                            </div>
                            <span class="message-badge">3</span>
                        </div>
                    </div>
                    
                    <div class="channel-card">
                        <div style="display: flex; align-items: center;">
                            <div class="channel-icon" style="background: rgba(107, 0, 219, 0.2);">
                                <ion-icon name="school" style="font-size: 24px; color: var(--shot-purple);"></ion-icon>
                            </div>
                            <div style="flex: 1; margin-left: 16px;">
                                <h4 style="margin: 0;">Coaches Only</h4>
                                <p style="margin: 4px 0; font-size: 14px; color: rgba(255, 255, 255, 0.7);">
                                    Private channel • 3 members
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="channel-card">
                        <div style="display: flex; align-items: center;">
                            <div class="channel-icon" style="background: rgba(247, 182, 19, 0.2);">
                                <ion-icon name="home" style="font-size: 24px; color: var(--shot-gold);"></ion-icon>
                            </div>
                            <div style="flex: 1; margin-left: 16px;">
                                <h4 style="margin: 0;">Parents Group</h4>
                                <p style="margin: 4px 0; font-size: 14px; color: rgba(255, 255, 255, 0.7);">
                                    Parent updates • 18 members
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Messages -->
            <div class="shot-card" style="margin-bottom: 24px;">
                <div class="shot-card-header">
                    <h3 class="shot-card-title">Recent Messages</h3>
                </div>
                <div class="shot-card-content">
                    <div class="message-thread unread">
                        <div style="display: flex; align-items: start;">
                            <img src="/avatar/SHOT avatar2.png" alt="Sender" 
                                 style="width: 40px; height: 40px; border-radius: 50%; margin-right: 12px;">
                            <div style="flex: 1;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                                    <h4 style="margin: 0; font-size: 16px;">Jamie Brown's Parent</h4>
                                    <span style="font-size: 12px; color: rgba(255, 255, 255, 0.6);">2m ago</span>
                                </div>
                                <p style="margin: 0; font-size: 14px; color: rgba(255, 255, 255, 0.8);">
                                    Jamie will be 10 minutes late to training today due to a doctor's appointment.
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="message-thread">
                        <div style="display: flex; align-items: start;">
                            <img src="/avatar/SHOT avatar5.png" alt="Sender" 
                                 style="width: 40px; height: 40px; border-radius: 50%; margin-right: 12px;">
                            <div style="flex: 1;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                                    <h4 style="margin: 0; font-size: 16px;">Sarah Johnson</h4>
                                    <span style="font-size: 12px; color: rgba(255, 255, 255, 0.6);">1h ago</span>
                                </div>
                                <p style="margin: 0; font-size: 14px; color: rgba(255, 255, 255, 0.8);">
                                    Great session today team! Remember to review the tactical videos I shared.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Notification Preferences -->
            <div class="shot-card">
                <div class="shot-card-header">
                    <h3 class="shot-card-title">Notification Preferences</h3>
                </div>
                <div class="shot-card-content">
                    <div class="preference-toggle">
                        <div>
                            <h4 style="margin: 0; font-size: 16px;">Team Messages</h4>
                            <p style="margin: 4px 0; font-size: 12px; color: rgba(255, 255, 255, 0.6);">
                                Receive notifications for team channel
                            </p>
                        </div>
                        <ion-toggle checked></ion-toggle>
                    </div>
                    
                    <div class="preference-toggle">
                        <div>
                            <h4 style="margin: 0; font-size: 16px;">Evaluation Reminders</h4>
                            <p style="margin: 4px 0; font-size: 12px; color: rgba(255, 255, 255, 0.6);">
                                Weekly evaluation notifications
                            </p>
                        </div>
                        <ion-toggle checked></ion-toggle>
                    </div>
                    
                    <div class="preference-toggle">
                        <div>
                            <h4 style="margin: 0; font-size: 16px;">Event Updates</h4>
                            <p style="margin: 4px 0; font-size: 12px; color: rgba(255, 255, 255, 0.6);">
                                Match and training changes
                            </p>
                        </div>
                        <ion-toggle checked></ion-toggle>
                    </div>
                    
                    <div class="preference-toggle">
                        <div>
                            <h4 style="margin: 0; font-size: 16px;">Parent Communications</h4>
                            <p style="margin: 4px 0; font-size: 12px; color: rgba(255, 255, 255, 0.6);">
                                Messages from parents
                            </p>
                        </div>
                        <ion-toggle checked></ion-toggle>
                    </div>
                </div>
            </div>
            
            <!-- Message Template Preview -->
            <div class="shot-card" style="margin-top: 24px;">
                <div class="shot-card-header">
                    <h3 class="shot-card-title">Template Preview</h3>
                </div>
                <div class="shot-card-content">
                    <select style="width: 100%; padding: 8px; background: rgba(255, 255, 255, 0.1); 
                                   color: white; border: 1px solid rgba(255, 255, 255, 0.2); 
                                   border-radius: 8px; margin-bottom: 12px;">
                        <option>Match Reminder</option>
                        <option>Training Update</option>
                        <option>Evaluation Complete</option>
                        <option>Welcome Message</option>
                    </select>
                    
                    <div class="template-preview">
                        Hi {{player_name}},<br><br>
                        
                        This is a reminder about our upcoming match:<br>
                        📅 Date: {{match_date}}<br>
                        ⏰ Time: {{match_time}}<br>
                        📍 Location: {{venue}}<br>
                        👕 Kit: {{kit_color}}<br><br>
                        
                        Please arrive 30 minutes early for warm-up.<br><br>
                        
                        See you there!<br>
                        Coach {{coach_name}}
                    </div>
                </div>
            </div>
        </ion-content>
    </ion-app>
    
    <script type="module" src="https://unpkg.com/@ionic/core@latest/dist/ionic/ionic.esm.js"></script>
    <script nomodule src="https://unpkg.com/@ionic/core@latest/dist/ionic/ionic.js"></script>
    <script>
        // Offline queue management
        let messageQueue = [];
        
        function updateOfflineStatus() {
            const offlineQueue = document.getElementById('offlineQueue');
            
            if (!navigator.onLine) {
                offlineQueue.style.display = 'flex';
                // Simulate queuing messages
                console.log('Offline - messages will be queued');
            } else {
                if (messageQueue.length > 0) {
                    // Simulate sending queued messages
                    setTimeout(() => {
                        offlineQueue.style.display = 'none';
                        showToast(`${messageQueue.length} queued messages sent successfully`);
                        messageQueue = [];
                    }, 2000);
                } else {
                    offlineQueue.style.display = 'none';
                }
            }
        }
        
        function showToast(message) {
            const toast = document.createElement('ion-toast');
            toast.message = message;
            toast.duration = 3000;
            toast.position = 'bottom';
            toast.color = 'success';
            
            document.body.appendChild(toast);
            toast.present();
        }
        
        // Simulate new message notifications
        function simulateNewMessage() {
            const badge = document.querySelector('ion-badge');
            if (badge) {
                const currentCount = parseInt(badge.textContent) || 0;
                badge.textContent = currentCount + 1;
                
                // Show notification
                if ('Notification' in window && Notification.permission === 'granted') {
                    new Notification('New SHOT Message', {
                        body: 'You have a new message in Team Channel',
                        icon: '/avatar/SHOT avatar1.png'
                    });
                }
            }
        }
        
        // Request notification permission
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
        
        // Handle preference toggles
        document.addEventListener('DOMContentLoaded', function() {
            const toggles = document.querySelectorAll('ion-toggle');
            toggles.forEach(toggle => {
                toggle.addEventListener('ionChange', function(e) {
                    const label = this.closest('.preference-toggle').querySelector('h4').textContent;
                    console.log(`${label}: ${e.detail.checked ? 'Enabled' : 'Disabled'}`);
                    
                    // Queue preference change if offline
                    if (!navigator.onLine) {
                        messageQueue.push({
                            type: 'preference',
                            data: { preference: label, enabled: e.detail.checked }
                        });
                    }
                });
            });
        });
        
        window.addEventListener('online', updateOfflineStatus);
        window.addEventListener('offline', updateOfflineStatus);
        
        // Initial check
        updateOfflineStatus();
        
        // Simulate occasional new messages
        setInterval(simulateNewMessage, 30000);
    </script>
</body>
</html>