<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHOT - Role Management</title>
    <link href="https://unpkg.com/@ionic/core@latest/css/ionic.bundle.css" rel="stylesheet">
    <link href="/src/theme/shot-brand-enhanced.css" rel="stylesheet">
    <style>
        /* People & Roles domain-specific styles */
        .role-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--shot-button-radius);
            padding: 20px;
            margin-bottom: 16px;
            transition: all 0.2s ease;
        }
        
        .role-card:hover {
            border-color: var(--shot-purple);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(107, 0, 219, 0.2);
        }
        
        .role-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .role-badge.coach {
            background: rgba(107, 0, 219, 0.2);
            color: var(--shot-purple);
            border: 1px solid var(--shot-purple);
        }
        
        .role-badge.player {
            background: rgba(26, 188, 156, 0.2);
            color: var(--shot-teal);
            border: 1px solid var(--shot-teal);
        }
        
        .role-badge.parent {
            background: rgba(247, 182, 19, 0.2);
            color: var(--shot-gold);
            border: 1px solid var(--shot-gold);
        }
        
        .role-badge.admin {
            background: rgba(255, 111, 60, 0.2);
            color: var(--shot-burnt-orange);
            border: 1px solid var(--shot-burnt-orange);
        }
        
        .permission-grid {
            display: grid;
            gap: 12px;
            margin-top: 16px;
        }
        
        .permission-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
        }
        
        .permission-category {
            margin-bottom: 24px;
        }
        
        .permission-category-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .user-list-item {
            display: flex;
            align-items: center;
            padding: 16px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--shot-button-radius);
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .user-list-item:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateX(4px);
        }
        
        .user-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            margin-right: 16px;
        }
        
        .pending-approval {
            background: rgba(247, 182, 19, 0.1);
            border: 1px solid var(--shot-gold);
            border-radius: var(--shot-button-radius);
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .approval-actions {
            display: flex;
            gap: 12px;
            margin-top: 12px;
        }
        
        .context-badge {
            background: rgba(255, 255, 255, 0.1);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .sync-status-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.9);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 12px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            z-index: 100;
        }
        
        .hierarchy-tree {
            margin: 16px 0;
        }
        
        .hierarchy-node {
            margin-left: 20px;
            padding: 8px 0;
            position: relative;
        }
        
        .hierarchy-node::before {
            content: '';
            position: absolute;
            left: -16px;
            top: 20px;
            width: 12px;
            height: 1px;
            background: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <ion-app>
        <ion-header>
            <ion-toolbar class="shot-toolbar">
                <ion-buttons slot="start">
                    <ion-back-button defaultHref="/"></ion-back-button>
                </ion-buttons>
                <ion-title>Roles & Permissions</ion-title>
                <ion-buttons slot="end">
                    <ion-button>
                        <ion-icon name="people-outline"></ion-icon>
                    </ion-button>
                </ion-buttons>
            </ion-toolbar>
        </ion-header>
        
        <ion-content class="ion-padding bg-black">
            <!-- Pending Approvals Alert -->
            <div class="pending-approval">
                <div style="display: flex; align-items: center; gap: 12px;">
                    <ion-icon name="alert-circle" style="font-size: 24px; color: var(--shot-gold);"></ion-icon>
                    <div style="flex: 1;">
                        <h4 style="margin: 0; color: var(--shot-gold);">3 Pending Approvals</h4>
                        <p style="margin: 4px 0; font-size: 14px; color: rgba(255, 255, 255, 0.7);">
                            Parent verification and role assignments waiting
                        </p>
                    </div>
                    <ion-button size="small" color="warning">
                        Review
                    </ion-button>
                </div>
            </div>
            
            <!-- Current User Context -->
            <div class="shot-card" style="margin-bottom: 24px;">
                <div class="shot-card-header">
                    <h3 class="shot-card-title">Your Roles</h3>
                </div>
                <div class="shot-card-content">
                    <div class="role-card">
                        <div style="display: flex; justify-content: space-between; align-items: start;">
                            <div>
                                <div class="role-badge coach">
                                    <ion-icon name="whistle"></ion-icon>
                                    Head Coach
                                </div>
                                <div style="margin-top: 8px;">
                                    <span class="context-badge">City FC Academy - U16 Boys</span>
                                </div>
                            </div>
                            <ion-icon name="checkmark-circle" style="color: var(--shot-green); font-size: 24px;"></ion-icon>
                        </div>
                        <div style="margin-top: 12px; font-size: 14px; color: rgba(255, 255, 255, 0.7);">
                            Full access to team management, evaluations, and player development
                        </div>
                    </div>
                    
                    <div class="role-card">
                        <div style="display: flex; justify-content: space-between; align-items: start;">
                            <div>
                                <div class="role-badge admin">
                                    <ion-icon name="shield-checkmark"></ion-icon>
                                    Club Administrator
                                </div>
                                <div style="margin-top: 8px;">
                                    <span class="context-badge">City FC Academy</span>
                                </div>
                            </div>
                            <ion-icon name="checkmark-circle" style="color: var(--shot-green); font-size: 24px;"></ion-icon>
                        </div>
                        <div style="margin-top: 12px; font-size: 14px; color: rgba(255, 255, 255, 0.7);">
                            Manage club settings, teams, and approve registrations
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Permission Categories -->
            <div class="shot-card" style="margin-bottom: 24px;">
                <div class="shot-card-header">
                    <h3 class="shot-card-title">Permission Overview</h3>
                </div>
                <div class="shot-card-content">
                    <!-- Team Management Permissions -->
                    <div class="permission-category">
                        <div class="permission-category-header">
                            <ion-icon name="people" style="color: var(--shot-teal); font-size: 24px;"></ion-icon>
                            <h4 style="margin: 0;">Team Management</h4>
                        </div>
                        <div class="permission-grid">
                            <div class="permission-item">
                                <span>View Team Roster</span>
                                <ion-icon name="checkmark-circle" style="color: var(--shot-green);"></ion-icon>
                            </div>
                            <div class="permission-item">
                                <span>Add/Remove Players</span>
                                <ion-icon name="checkmark-circle" style="color: var(--shot-green);"></ion-icon>
                            </div>
                            <div class="permission-item">
                                <span>Edit Team Settings</span>
                                <ion-icon name="checkmark-circle" style="color: var(--shot-green);"></ion-icon>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Evaluation Permissions -->
                    <div class="permission-category">
                        <div class="permission-category-header">
                            <ion-icon name="analytics" style="color: var(--shot-purple); font-size: 24px;"></ion-icon>
                            <h4 style="margin: 0;">Evaluations</h4>
                        </div>
                        <div class="permission-grid">
                            <div class="permission-item">
                                <span>Create Evaluations</span>
                                <ion-icon name="checkmark-circle" style="color: var(--shot-green);"></ion-icon>
                            </div>
                            <div class="permission-item">
                                <span>View All Player Data</span>
                                <ion-icon name="checkmark-circle" style="color: var(--shot-green);"></ion-icon>
                            </div>
                            <div class="permission-item">
                                <span>Export Reports</span>
                                <ion-icon name="checkmark-circle" style="color: var(--shot-green);"></ion-icon>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Communication Permissions -->
                    <div class="permission-category">
                        <div class="permission-category-header">
                            <ion-icon name="chatbubbles" style="color: var(--shot-gold); font-size: 24px;"></ion-icon>
                            <h4 style="margin: 0;">Communication</h4>
                        </div>
                        <div class="permission-grid">
                            <div class="permission-item">
                                <span>Send Team Messages</span>
                                <ion-icon name="checkmark-circle" style="color: var(--shot-green);"></ion-icon>
                            </div>
                            <div class="permission-item">
                                <span>Contact Parents</span>
                                <ion-icon name="checkmark-circle" style="color: var(--shot-green);"></ion-icon>
                            </div>
                            <div class="permission-item">
                                <span>Broadcast Announcements</span>
                                <ion-icon name="close-circle" style="color: var(--shot-grey);"></ion-icon>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Team Members & Roles -->
            <div class="shot-card">
                <div class="shot-card-header">
                    <h3 class="shot-card-title">Team Members</h3>
                </div>
                <div class="shot-card-content">
                    <ion-segment value="all">
                        <ion-segment-button value="all">
                            <ion-label>All</ion-label>
                        </ion-segment-button>
                        <ion-segment-button value="coaches">
                            <ion-label>Coaches</ion-label>
                        </ion-segment-button>
                        <ion-segment-button value="players">
                            <ion-label>Players</ion-label>
                        </ion-segment-button>
                        <ion-segment-button value="parents">
                            <ion-label>Parents</ion-label>
                        </ion-segment-button>
                    </ion-segment>
                    
                    <div style="margin-top: 16px;">
                        <!-- Coach -->
                        <div class="user-list-item">
                            <img src="/avatar/SHOT avatar5.png" alt="User" class="user-avatar">
                            <div style="flex: 1;">
                                <h4 style="margin: 0;">Sarah Johnson</h4>
                                <div style="display: flex; gap: 8px; margin-top: 4px;">
                                    <div class="role-badge coach" style="font-size: 12px; padding: 4px 8px;">
                                        Assistant Coach
                                    </div>
                                </div>
                            </div>
                            <ion-button fill="clear" size="small">
                                <ion-icon name="ellipsis-vertical"></ion-icon>
                            </ion-button>
                        </div>
                        
                        <!-- Player with Parent -->
                        <div class="user-list-item">
                            <img src="/avatar/SHOT avatar1.png" alt="User" class="user-avatar">
                            <div style="flex: 1;">
                                <h4 style="margin: 0;">Alex Smith</h4>
                                <div style="display: flex; gap: 8px; margin-top: 4px;">
                                    <div class="role-badge player" style="font-size: 12px; padding: 4px 8px;">
                                        Player
                                    </div>
                                    <span style="font-size: 12px; color: rgba(255, 255, 255, 0.6);">
                                        Parent: John Smith ✓
                                    </span>
                                </div>
                            </div>
                            <ion-button fill="clear" size="small">
                                <ion-icon name="ellipsis-vertical"></ion-icon>
                            </ion-button>
                        </div>
                        
                        <!-- Parent -->
                        <div class="user-list-item">
                            <img src="/avatar/SHOT avatar6.png" alt="User" class="user-avatar">
                            <div style="flex: 1;">
                                <h4 style="margin: 0;">Emma Taylor</h4>
                                <div style="display: flex; gap: 8px; margin-top: 4px;">
                                    <div class="role-badge parent" style="font-size: 12px; padding: 4px 8px;">
                                        Parent
                                    </div>
                                    <span style="font-size: 12px; color: rgba(255, 255, 255, 0.6);">
                                        Child: Jordan Taylor
                                    </span>
                                </div>
                            </div>
                            <ion-button fill="clear" size="small">
                                <ion-icon name="ellipsis-vertical"></ion-icon>
                            </ion-button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Sync Status Bar -->
            <div class="sync-status-bar" id="syncBar" style="display: none;">
                <div style="display: flex; align-items: center; gap: 12px;">
                    <ion-spinner name="crescent" style="--color: var(--shot-teal);"></ion-spinner>
                    <span>Syncing role changes...</span>
                </div>
                <span style="font-size: 14px; color: rgba(255, 255, 255, 0.6);">2 of 3</span>
            </div>
        </ion-content>
    </ion-app>
    
    <script type="module" src="https://unpkg.com/@ionic/core@latest/dist/ionic/ionic.esm.js"></script>
    <script nomodule src="https://unpkg.com/@ionic/core@latest/dist/ionic/ionic.js"></script>
    <script>
        // Simulate role sync
        let syncTimer = null;
        
        function simulateRoleSync() {
            const syncBar = document.getElementById('syncBar');
            
            if (!navigator.onLine) {
                // Queue changes when offline
                console.log('Queuing role changes for sync...');
            } else {
                // Show sync progress
                syncBar.style.display = 'flex';
                
                clearTimeout(syncTimer);
                syncTimer = setTimeout(() => {
                    syncBar.style.display = 'none';
                    showNotification('Role changes synced successfully');
                }, 3000);
            }
        }
        
        function showNotification(message) {
            const toast = document.createElement('ion-toast');
            toast.message = message;
            toast.duration = 2000;
            toast.position = 'top';
            toast.color = 'success';
            
            document.body.appendChild(toast);
            toast.present();
        }
        
        // Handle permission toggles
        document.addEventListener('DOMContentLoaded', function() {
            const permissionItems = document.querySelectorAll('.permission-item');
            permissionItems.forEach(item => {
                item.addEventListener('click', function() {
                    const icon = this.querySelector('ion-icon');
                    if (icon.name === 'checkmark-circle') {
                        icon.name = 'close-circle';
                        icon.style.color = 'var(--shot-grey)';
                    } else {
                        icon.name = 'checkmark-circle';
                        icon.style.color = 'var(--shot-green)';
                    }
                    simulateRoleSync();
                });
            });
        });
        
        window.addEventListener('online', () => {
            console.log('Back online - syncing queued changes');
            simulateRoleSync();
        });
    </script>
</body>
</html>