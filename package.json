{"name": "shot", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "dev:with-version": "chmod +x ./scripts/set-version-dev.sh && ./scripts/set-version-dev.sh && vite", "build": "vite build", "build:with-version": "chmod +x ./scripts/build-with-version.sh && ./scripts/build-with-version.sh", "build:prod": "chmod +x ./scripts/build-with-version.sh && ./scripts/build-with-version.sh prod", "preview": "vite preview", "test.unit": "vitest", "lint": "eslint .", "lint:fix": "eslint . --fix", "supabase:start": "npx supabase start", "supabase:stop": "npx supabase stop", "functions:serve": "npx supabase functions serve", "functions:deploy": "npx supabase functions deploy", "serve-prod": "npm run build && serve -s dist", "test": "vitest run", "test:json": "vitest run --reporter=json --reporter=verbose > unit-test-results.txt 2>&1", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:home": "vitest src/pages/__tests__/Home.test.tsx", "test:all": "npm run test && npm run test:e2e", "lhci:mobile": "lhci autorun", "lhci:desktop": "lhci autorun --collect.settings.preset=desktop", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build -o dist/storybook", "build:all": "npm run build && npm run build-storybook", "dev:all": "npm run storybook & npm run dev", "types:generate": "node scripts/generate-types.js", "types:generate:local": "node scripts/generate-types.js --local", "types:generate:remote": "node scripts/generate-types.js --remote", "types:update-client": "echo '✅ Supabase client already configured with generated types'", "type-check": "tsc --noEmit"}, "dependencies": {"@capacitor/android": "^6.1.2", "@capacitor/app": "6.0.1", "@capacitor/camera": "^6.0.2", "@capacitor/core": "^6.1.2", "@capacitor/haptics": "6.0.1", "@capacitor/ios": "^6.1.2", "@capacitor/keyboard": "6.0.2", "@capacitor/status-bar": "6.0.1", "@ionic/pwa-elements": "^3.3.0", "@ionic/react": "^8.0.0", "@ionic/react-router": "^8.0.0", "@sentry/react": "^8.51.0", "@sentry/vite-plugin": "^3.1.0", "@stripe/react-stripe-js": "^3.9.0", "@stripe/stripe-js": "^4.10.0", "@supabase/supabase-js": "^2.55.0", "@tanstack/react-query": "^5.17.19", "@tanstack/react-query-devtools": "^5.17.19", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "@vimeo/player": "^2.24.0", "framer-motion": "^12.23.9", "ionicons": "^7.0.0", "lucide-react": "^0.517.0", "papaparse": "^5.5.2", "react": "^18.2.0", "react-cookie-consent": "^8.0.1", "react-dom": "^18.2.0", "react-markdown": "^9.0.1", "react-player": "^2.16.0", "react-router": "^5.3.4", "react-router-dom": "^5.3.4", "remark-gfm": "^4.0.0", "web-vitals": "^4.2.4"}, "devDependencies": {"@capacitor/cli": "^6.1.2", "@eslint/js": "^8.57.0", "@lhci/cli": "^0.13.x", "@playwright/test": "^1.42.1", "@storybook/addon-actions": "9.0.8", "@storybook/addon-docs": "9.0.17", "@storybook/addon-onboarding": "9.0.17", "@storybook/react-vite": "9.0.17", "@testing-library/dom": ">=7.21.4", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/node": "^20.11.24", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@vitejs/plugin-legacy": "^5.0.0", "@vitejs/plugin-react": "^4.0.1", "autoprefixer": "^10.4.20", "cypress": "^13.5.0", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-storybook": "9.0.17", "glob": "^11.0.3", "jsdom": "^22.1.0", "postcss": "^8.4.47", "prettier": "^3.2.5", "serve": "^14.2.1", "storybook": "9.0.17", "supabase": "^2.34.3", "tailwindcss": "^3.4.14", "terser": "^5.4.0", "typescript": "^5.1.6", "typescript-eslint": "^7.0.0", "vite": "~5.2.0", "vitest": "^0.34.6"}, "description": "An Ionic project"}