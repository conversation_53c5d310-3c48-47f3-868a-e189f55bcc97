-- Fix Twilio SMS Function to Properly Encode URL Parameters

-- Step 1: Create a URL-encode function
CREATE OR REPLACE FUNCTION url_encode(text) RETURNS text AS $$
DECLARE
  i int4;
  result text = '';
  c char(1);
  hex text = '0123456789ABCDEF';
BEGIN
  FOR i IN 1..length($1) LOOP
    c = substr($1, i, 1);
    IF c ~ '[a-zA-Z0-9]' THEN
      result = result || c;
    ELSIF c = ' ' THEN
      result = result || '%20';
    ELSE
      result = result || '%' || 
               substr(hex, (ascii(c) / 16)::int + 1, 1) || 
               substr(hex, (ascii(c) % 16) + 1, 1);
    END IF;
  END LOOP;
  RETURN result;
END;
$$ LANGUAGE plpgsql IMMUTABLE STRICT;

-- Step 2: Update the send_twilio_sms function to use proper URL encoding
CREATE OR REPLACE FUNCTION send_twilio_sms(
  phone_number TEXT,
  message_body TEXT,
  notification_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
  twilio_account_sid TEXT;
  twilio_auth_token TEXT;
  twilio_phone_number TEXT;
  auth TEXT;
  formatted_phone TEXT;
  response http_response;
  response_body JSONB;
  success BOOLEAN := FALSE;
  request_url TEXT;
  request_body TEXT;
BEGIN
  -- Get Twilio credentials from app_settings table
  SELECT value INTO twilio_account_sid FROM app_settings WHERE key = 'twilio_account_sid';
  SELECT value INTO twilio_auth_token FROM app_settings WHERE key = 'twilio_auth_token';
  SELECT value INTO twilio_phone_number FROM app_settings WHERE key = 'twilio_phone_number';

  -- Log function start with full details for debugging
  INSERT INTO debug_logs (function_name, message, data)
  VALUES ('send_twilio_sms', 'Starting SMS send function with full details', jsonb_build_object(
    'phone_number', phone_number,
    'message_body', message_body,
    'message_length', LENGTH(message_body),
    'notification_id', notification_id,
    'twilio_account_sid', twilio_account_sid,
    'twilio_auth_token_present', twilio_auth_token IS NOT NULL,
    'twilio_phone_number', twilio_phone_number,
    'timestamp', NOW()
  ));

  -- Check if Twilio credentials are configured
  IF twilio_account_sid IS NULL OR twilio_auth_token IS NULL OR twilio_phone_number IS NULL THEN
    INSERT INTO debug_logs (function_name, message, data)
    VALUES ('send_twilio_sms', 'Missing Twilio credentials', jsonb_build_object(
      'has_account_sid', twilio_account_sid IS NOT NULL,
      'has_auth_token', twilio_auth_token IS NOT NULL,
      'has_phone_number', twilio_phone_number IS NOT NULL
    ));
    
    -- Update notification record with failure
    UPDATE pre_evaluation_notifications
    SET 
      status = 'failed',
      failed_at = NOW(),
      failure_reason = 'Missing Twilio credentials in app_settings table'
    WHERE id = notification_id;
    
    RETURN FALSE;
  END IF;

  -- Format the phone number for Twilio (must start with +)
  formatted_phone := TRIM(phone_number);
  IF NOT starts_with(formatted_phone, '+') THEN
    -- If UK number without international prefix, add +44 and remove leading 0
    IF starts_with(formatted_phone, '0') THEN
      formatted_phone := '+44' || substring(formatted_phone, 2);
    ELSE
      formatted_phone := '+' || formatted_phone;
    END IF;
  END IF;

  -- Create Base64 auth string for Twilio
  auth := encode(convert_to(twilio_account_sid || ':' || twilio_auth_token, 'UTF8'), 'base64');
  
  -- Construct request URL
  request_url := format('https://api.twilio.com/2010-04-01/Accounts/%s/Messages.json', twilio_account_sid);
  
  -- Properly URL encode all parameters
  request_body := 'To=' || url_encode(formatted_phone) || 
                 '&From=' || url_encode(twilio_phone_number) || 
                 '&Body=' || url_encode(message_body);
  
  -- Log the request details
  INSERT INTO debug_logs (function_name, message, data)
  VALUES ('send_twilio_sms', 'Preparing Twilio API request', jsonb_build_object(
    'url', request_url,
    'to', formatted_phone,
    'from', twilio_phone_number,
    'body_length', LENGTH(message_body),
    'encoded_request_body', request_body
  ));

  BEGIN
    -- Make the HTTP request to Twilio API
    SELECT 
      * 
    INTO 
      response
    FROM 
      http((\n        'POST',
        request_url,
        ARRAY[
          http_header('Authorization', 'Basic ' || auth),
          http_header('Content-Type', 'application/x-www-form-urlencoded')
        ],
        'application/x-www-form-urlencoded',
        request_body
      ));

    -- Parse response body as JSON
    BEGIN
      response_body := response.content::JSONB;
    EXCEPTION WHEN OTHERS THEN
      response_body := jsonb_build_object('raw_response', response.content);
    END;

    -- Log response from Twilio
    INSERT INTO debug_logs (function_name, message, data)
    VALUES ('send_twilio_sms', format('Twilio API response: %s', response.status), jsonb_build_object(
      'status_code', response.status,
      'response', response_body,
      'phone_number', formatted_phone,
      'raw_response', response.content
    ));

    -- Check if the request was successful (2xx status code)
    IF response.status >= 200 AND response.status < 300 THEN
      success := TRUE;
      
      -- Update notification record with success
      UPDATE pre_evaluation_notifications
      SET 
        recipient_phone = formatted_phone,
        status = 'sent',
        sent_at = NOW(),
        metadata = COALESCE(metadata, '{}'::jsonb) || jsonb_build_object('twilio_sid', response_body->>'sid')
      WHERE id = notification_id;
      
      -- Update SMS queue if this came from there
      UPDATE sms_queue
      SET 
        status = 'sent',
        sent_at = NOW()
      WHERE pre_evaluation_id = (
        SELECT pre_evaluation_id 
        FROM pre_evaluation_notifications 
        WHERE id = notification_id
      );
    ELSE
      -- Update notification record with failure
      UPDATE pre_evaluation_notifications
      SET 
        recipient_phone = formatted_phone,
        status = 'failed',
        failed_at = NOW(),
        failure_reason = format('Twilio API Error: %s - %s', response.status, response_body->>'message'),
        metadata = COALESCE(metadata, '{}'::jsonb) || jsonb_build_object('error', response_body)
      WHERE id = notification_id;
      
      -- Update SMS queue if this came from there
      UPDATE sms_queue
      SET 
        status = 'failed',
        error_message = format('Twilio API Error: %s - %s', response.status, response_body->>'message'),
        retry_count = COALESCE(retry_count, 0) + 1
      WHERE pre_evaluation_id = (
        SELECT pre_evaluation_id 
        FROM pre_evaluation_notifications 
        WHERE id = notification_id
      );
    END IF;

  EXCEPTION WHEN OTHERS THEN
    -- Log any exception that occurs
    INSERT INTO debug_logs (function_name, message, data)
    VALUES ('send_twilio_sms', 'Exception sending SMS', jsonb_build_object(
      'error', SQLERRM,
      'phone_number', formatted_phone,
      'context', json_build_object(
        'notification_id', notification_id,
        'twilio_account_sid', twilio_account_sid,
        'twilio_phone_number', twilio_phone_number
      )
    ));
    
    -- Update notification record with failure
    UPDATE pre_evaluation_notifications
    SET 
      recipient_phone = formatted_phone,
      status = 'failed',
      failed_at = NOW(),
      failure_reason = format('Exception: %s', SQLERRM),
      metadata = COALESCE(metadata, '{}'::jsonb) || jsonb_build_object('error', SQLERRM)
    WHERE id = notification_id;
    
    -- Update SMS queue if this came from there
    UPDATE sms_queue
    SET 
      status = 'failed',
      error_message = format('Exception: %s', SQLERRM),
      retry_count = COALESCE(retry_count, 0) + 1
    WHERE pre_evaluation_id = (
      SELECT pre_evaluation_id 
      FROM pre_evaluation_notifications 
      WHERE id = notification_id
    );
  END;

  RETURN success;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Fix the pending SMS that failed
UPDATE sms_queue
SET status = 'pending', 
    retry_count = 0,
    error_message = NULL
WHERE id = '417e4a3f-d7ba-4a15-be7e-6fdff2de1201';

-- Step 4: Reset any stuck processing SMS
UPDATE sms_queue
SET status = 'pending',
    retry_count = 0,
    error_message = NULL
WHERE status = 'processing';

-- Step 5: Log the fix
INSERT INTO debug_logs (function_name, message, data)
VALUES ('sms_system_fix', 'Fixed Twilio SMS URL encoding issue', jsonb_build_object(
  'timestamp', NOW(),
  'fixed_sms_ids', ARRAY['417e4a3f-d7ba-4a15-be7e-6fdff2de1201']
));
