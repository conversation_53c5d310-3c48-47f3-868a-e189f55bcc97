-- Migration: Create Parent Onboarding Sessions Table
-- Description: Core table for tracking parent onboarding progress through the 3-minute flow
-- Phase: 2 - Parent Quick Onboarding System
-- Created: 2025-01-28

-- Create parent_onboarding_sessions table
CREATE TABLE IF NOT EXISTS parent_onboarding_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_token TEXT UNIQUE NOT NULL DEFAULT encode(gen_random_bytes(32), 'hex'),
    invite_code_id UUID REFERENCES invite_codes(id),
    
    -- Status tracking
    status VARCHAR(50) DEFAULT 'started'
        CHECK (status IN ('started', 'email_entered', 'account_created', 'child_added', 'completed', 'abandoned')),
    
    -- Parent data
    parent_email VARCHAR(255),
    parent_name VARCHAR(255),
    parent_phone VARCHAR(50),
    parent_profile_id UUID REFERENCES profiles(id),
    
    -- Child data
    child_name VARCHAR(255),
    child_date_of_birth DATE,
    child_profile_id UUID REFERENCES profiles(id),
    child_sport_head_id UUID REFERENCES sport_heads(id),
    
    -- Context data from invite
    inviting_coach_id UUID REFERENCES profiles(id),
    inviting_coach_sport_head_id UUID REFERENCES sport_heads(id),
    event_id UUID REFERENCES events(id),
    team_id UUID REFERENCES teams(team_id),
    sport VARCHAR(50),
    pre_evaluation_id UUID, -- Will reference pre_evaluations table when created
    
    -- Timing and tracking
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    abandoned_at TIMESTAMPTZ,
    total_duration_seconds INTEGER,
    
    -- Step tracking for analytics
    steps_completed JSONB DEFAULT '[]'::jsonb,
    step_timings JSONB DEFAULT '{}'::jsonb,
    /* Example step_timings:
    {
        "account_creation": 45,
        "email_verification": 20,
        "child_setup": 40,
        "completion": 10
    }
    */
    
    -- Device and browser info
    device_info JSONB DEFAULT '{}'::jsonb,
    /* Example device_info:
    {
        "user_agent": "Mozilla/5.0...",
        "platform": "iOS",
        "screen_size": "390x844",
        "browser": "Safari"
    }
    */
    
    -- A/B testing support
    variant_id VARCHAR(50) DEFAULT 'control',
    conversion_score DECIMAL(3,2),
    abandoned_at_step VARCHAR(50),
    abandonment_reason TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_onboarding_session_token ON parent_onboarding_sessions(session_token);
CREATE INDEX idx_onboarding_session_status ON parent_onboarding_sessions(status) 
    WHERE status NOT IN ('completed', 'abandoned');
CREATE INDEX idx_onboarding_parent_email ON parent_onboarding_sessions(parent_email) 
    WHERE parent_email IS NOT NULL;
CREATE INDEX idx_onboarding_conversion ON parent_onboarding_sessions(status, variant_id);
CREATE INDEX idx_onboarding_timing ON parent_onboarding_sessions(created_at) 
    WHERE status = 'completed';
CREATE INDEX idx_onboarding_coach ON parent_onboarding_sessions(inviting_coach_id);
CREATE INDEX idx_onboarding_team ON parent_onboarding_sessions(team_id);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_parent_onboarding_sessions_updated_at
    BEFORE UPDATE ON parent_onboarding_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add helpful comments
COMMENT ON TABLE parent_onboarding_sessions IS 'Tracks parent account creation through the 3-minute onboarding flow';
COMMENT ON COLUMN parent_onboarding_sessions.session_token IS 'Unique token used in onboarding URLs';
COMMENT ON COLUMN parent_onboarding_sessions.status IS 'Current step in the onboarding process';
COMMENT ON COLUMN parent_onboarding_sessions.step_timings IS 'Time spent on each step in seconds';
COMMENT ON COLUMN parent_onboarding_sessions.variant_id IS 'A/B test variant for conversion optimization';
COMMENT ON COLUMN parent_onboarding_sessions.conversion_score IS 'Calculated score for funnel analysis';

-- Enable Row Level Security
ALTER TABLE parent_onboarding_sessions ENABLE ROW LEVEL SECURITY;

-- RLS Policies will be created in a separate migration for clarity
