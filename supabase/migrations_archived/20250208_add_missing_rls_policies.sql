-- ABOUTME: Migration to add missing RLS policies for pre_evaluations, player_stats, and cart_sessions tables
-- This fixes 406 errors on the /perform page by allowing authenticated users to access their own data

-- Enable RLS on tables if not already enabled
ALTER TABLE pre_evaluations ENABLE ROW LEVEL SECURITY;
ALTER TABLE player_stats ENABLE ROW LEVEL SECURITY;
-- Note: cart_sessions is a view, we need to apply RLS to the underlying table
ALTER TABLE commerce.cart_sessions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if any (to avoid conflicts)
DROP POLICY IF EXISTS "Users can view their own pre_evaluations" ON pre_evaluations;
DROP POLICY IF EXISTS "Users can view pre_evaluations for their players" ON pre_evaluations;
DROP POLICY IF EXISTS "Users can create their own pre_evaluations" ON pre_evaluations;
DROP POLICY IF EXISTS "Users can update their own pre_evaluations" ON pre_evaluations;

DROP POLICY IF EXISTS "Users can view their own player_stats" ON player_stats;
DROP POLICY IF EXISTS "Users can view player_stats for their players" ON player_stats;
DROP POLICY IF EXISTS "Users can create their own player_stats" ON player_stats;
DROP POLICY IF EXISTS "Users can update their own player_stats" ON player_stats;

DROP POLICY IF EXISTS "Users can view their own cart_sessions" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Authenticated users can create cart_sessions" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can update their own cart_sessions" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can delete their own cart_sessions" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Anonymous users can view cart by session_id" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Anonymous users can create cart_sessions" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Anonymous users can update their cart by session_id" ON commerce.cart_sessions;

-- Policies for pre_evaluations table
CREATE POLICY "Users can view their own pre_evaluations"
ON pre_evaluations FOR SELECT
TO authenticated
USING (auth.uid() = player_id);

CREATE POLICY "Coaches can view pre_evaluations for their team players"
ON pre_evaluations FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM team_staff ts
    WHERE ts.user_id = auth.uid()
    AND ts.team_id = pre_evaluations.team_id
    AND ts.role IN ('coach', 'head_coach')
  )
);

CREATE POLICY "Users can create their own pre_evaluations"
ON pre_evaluations FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = player_id);

CREATE POLICY "Users can update their own pre_evaluations"
ON pre_evaluations FOR UPDATE
TO authenticated
USING (auth.uid() = player_id)
WITH CHECK (auth.uid() = player_id);

-- Policies for player_stats table
CREATE POLICY "Users can view their own player_stats"
ON player_stats FOR SELECT
TO authenticated
USING (auth.uid() = player_id);

CREATE POLICY "Coaches can view player_stats for their team players"
ON player_stats FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM team_rosters tr
    JOIN team_staff ts ON ts.team_id = tr.team_id
    WHERE tr.player_id = player_stats.player_id
    AND ts.user_id = auth.uid()
    AND ts.role IN ('coach', 'head_coach')
  )
);

CREATE POLICY "System can create player_stats"
ON player_stats FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = player_id);

CREATE POLICY "System can update player_stats"
ON player_stats FOR UPDATE
TO authenticated
USING (auth.uid() = player_id)
WITH CHECK (auth.uid() = player_id);

-- Policies for cart_sessions table (commerce schema)
CREATE POLICY "Users can view their own cart_sessions"
ON commerce.cart_sessions FOR SELECT
TO authenticated
USING (auth.uid() = profile_id);

CREATE POLICY "Authenticated users can create cart_sessions"
ON commerce.cart_sessions FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = profile_id);

CREATE POLICY "Users can update their own cart_sessions"
ON commerce.cart_sessions FOR UPDATE
TO authenticated
USING (auth.uid() = profile_id)
WITH CHECK (auth.uid() = profile_id);

CREATE POLICY "Users can delete their own cart_sessions"
ON commerce.cart_sessions FOR DELETE
TO authenticated
USING (auth.uid() = profile_id);

-- Also allow anonymous users to work with cart_sessions using session_id
CREATE POLICY "Anonymous users can view cart by session_id"
ON commerce.cart_sessions FOR SELECT
TO anon
USING (session_id IS NOT NULL);

CREATE POLICY "Anonymous users can create cart_sessions"
ON commerce.cart_sessions FOR INSERT
TO anon
WITH CHECK (session_id IS NOT NULL AND profile_id IS NULL);

CREATE POLICY "Anonymous users can update their cart by session_id"
ON commerce.cart_sessions FOR UPDATE
TO anon
USING (session_id IS NOT NULL AND profile_id IS NULL)
WITH CHECK (session_id IS NOT NULL);

-- Grant necessary permissions
GRANT SELECT ON pre_evaluations TO authenticated;
GRANT INSERT, UPDATE ON pre_evaluations TO authenticated;

GRANT SELECT ON player_stats TO authenticated;
GRANT INSERT, UPDATE ON player_stats TO authenticated;

-- Permissions are already granted on the commerce schema tables
-- The public views inherit these permissions