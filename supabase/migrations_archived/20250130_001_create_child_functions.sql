-- Migration: Create Child Management Functions
-- Description: Functions for creating children with SportHeads and finding similar children
-- Phase: 2 - Parent Quick Onboarding System
-- Created: 2025-01-30

-- Function to create child with SportHead
CREATE OR REPLACE FUNCTION create_child_with_sporthead(
    p_parent_id UUID,
    p_child_name TEXT,
    p_child_dob DATE,
    p_sport TEXT,
    p_emergency_contact JSON<PERSON>,
    p_team_id UUID DEFAULT NULL,
    p_session_id UUID DEFAULT NULL,
    p_allergies TEXT[] DEFAULT NULL,
    p_medical_conditions TEXT[] DEFAULT NULL,
    p_photography_allowed BOOLEAN DEFAULT TRUE
)
RETURNS TABLE (
    child_id UUID,
    sport_head_id UUID
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_child_id UUID;
    v_sport_head_id UUID;
    v_age INTEGER;
    v_user_auth_id UUID;
BEGIN
    -- Calculate age
    v_age := EXTRACT(YEAR FROM AGE(CURRENT_DATE, p_child_dob));
    
    -- Generate a UUID for auth.users (required for RLS)
    v_user_auth_id := uuid_generate_v4();
    
    -- Create auth.users entry for the child
    -- Note: In production, this might be handled differently
    -- For now, we create a placeholder that can't be used for login
    INSERT INTO auth.users (
        id,
        email,
        encrypted_password,
        email_confirmed_at,
        created_at,
        updated_at,
        raw_user_meta_data
    ) VALUES (
        v_user_auth_id,
        'child_' || v_user_auth_id || '@shot-app.internal',
        'PLACEHOLDER_NO_LOGIN',
        NOW(),
        NOW(),
        NOW(),
        jsonb_build_object(
            'is_child_account', true,
            'parent_id', p_parent_id,
            'created_via', 'parent_onboarding'
        )
    );
    
    -- Create child profile
    INSERT INTO profiles (
        id,
        full_name,
        date_of_birth,
        user_type,
        parent_id,
        is_minor,
        emergency_contact,
        allergies,
        medical_conditions,
        photography_allowed,
        has_completed_onboarding,
        onboarding_completed_at,
        onboarding_method,
        parent_consent_given,
        parent_consent_date,
        created_at,
        updated_at
    ) VALUES (
        v_user_auth_id,
        p_child_name,
        p_child_dob,
        'player',
        p_parent_id,
        v_age < 18,
        p_emergency_contact,
        COALESCE(p_allergies, ARRAY[]::TEXT[]),
        COALESCE(p_medical_conditions, ARRAY[]::TEXT[]),
        p_photography_allowed,
        TRUE,
        NOW(),
        'parent_quick_onboarding',
        TRUE,
        CURRENT_DATE,
        NOW(),
        NOW()
    ) RETURNING id INTO v_child_id;

    -- Create SportHead for the child
    INSERT INTO sport_heads (
        user_id,
        sport,
        display_name,
        role,
        is_primary,
        avatar_style,
        creation_date,
        sport_metadata
    ) VALUES (
        v_child_id,
        p_sport,
        p_child_name || '''s ' || INITCAP(p_sport) || ' Head',
        'player',
        TRUE,
        jsonb_build_object(
            'theme', CASE p_sport 
                WHEN 'football' THEN 'teal'
                WHEN 'basketball' THEN 'purple'
                WHEN 'tennis' THEN 'yellow'
                WHEN 'swimming' THEN 'blue'
                ELSE 'blue'
            END,
            'style', 'youthful',
            'accessories', ARRAY[]::TEXT[]
        ),
        NOW(),
        jsonb_build_object(
            'age_group', CASE 
                WHEN v_age < 8 THEN 'U8'
                WHEN v_age < 10 THEN 'U10'
                WHEN v_age < 12 THEN 'U12'
                WHEN v_age < 14 THEN 'U14'
                WHEN v_age < 16 THEN 'U16'
                WHEN v_age < 18 THEN 'U18'
                ELSE 'Senior'
            END,
            'created_by', 'parent_onboarding'
        )
    ) RETURNING id INTO v_sport_head_id;

    -- Update profile with primary sport head
    UPDATE profiles 
    SET primary_sport_head_id = v_sport_head_id
    WHERE id = v_child_id;

    -- If team_id provided, add to team
    IF p_team_id IS NOT NULL THEN
        INSERT INTO team_members (
            team_id,
            user_id,
            sport_head_id,
            role,
            joined_at,
            joined_via,
            parent_consent_given,
            parent_consent_date,
            onboarding_metadata
        ) VALUES (
            p_team_id,
            v_child_id,
            v_sport_head_id,
            'player',
            NOW(),
            'parent_onboarding',
            TRUE,
            CURRENT_DATE,
            jsonb_build_object(
                'session_id', p_session_id,
                'parent_id', p_parent_id
            )
        );
    END IF;

    -- Log activity
    INSERT INTO activities (
        user_id,
        activity_type,
        target_id,
        target_type,
        metadata,
        created_at
    ) VALUES (
        p_parent_id,
        'child_added',
        v_child_id,
        'profile',
        jsonb_build_object(
            'child_name', p_child_name,
            'sport', p_sport,
            'sport_head_id', v_sport_head_id,
            'method', 'quick_onboarding',
            'team_id', p_team_id
        ),
        NOW()
    );

    -- Update child detection cache
    INSERT INTO child_detection_cache (
        parent_id,
        search_key,
        child_id,
        sport_heads,
        confidence_score
    ) VALUES (
        p_parent_id,
        generate_child_search_key(p_child_name, p_child_dob),
        v_child_id,
        jsonb_build_array(
            jsonb_build_object(
                'id', v_sport_head_id,
                'sport', p_sport,
                'display_name', p_child_name || '''s ' || INITCAP(p_sport) || ' Head',
                'is_primary', true
            )
        ),
        1.0
    );

    RETURN QUERY SELECT v_child_id, v_sport_head_id;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error and re-raise
        RAISE LOG 'Error in create_child_with_sporthead: %', SQLERRM;
        RAISE;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION create_child_with_sporthead TO authenticated;

-- Function to find similar children
CREATE OR REPLACE FUNCTION find_similar_children(
    p_parent_id UUID,
    p_child_name TEXT,
    p_threshold DECIMAL DEFAULT 0.7
)
RETURNS TABLE (
    id UUID,
    name TEXT,
    date_of_birth DATE,
    similarity_score DECIMAL,
    sport_heads JSONB,
    teams JSONB
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- First check cache
    RETURN QUERY
    SELECT 
        c.child_id as id,
        p.full_name as name,
        p.date_of_birth,
        c.confidence_score as similarity_score,
        c.sport_heads,
        COALESCE(c.teams, '[]'::jsonb) as teams
    FROM child_detection_cache c
    JOIN profiles p ON p.id = c.child_id
    WHERE c.parent_id = p_parent_id
    AND c.stale = FALSE
    AND similarity(LOWER(p.full_name), LOWER(p_child_name)) > p_threshold
    ORDER BY similarity_score DESC
    LIMIT 5;
    
    -- If cache miss or not enough results, query directly
    IF NOT FOUND THEN
        RETURN QUERY
        WITH child_data AS (
            SELECT 
                c.id,
                c.full_name as name,
                c.date_of_birth,
                similarity(c.full_name, p_child_name) as similarity_score,
                COALESCE(
                    jsonb_agg(
                        DISTINCT jsonb_build_object(
                            'id', sh.id,
                            'sport', sh.sport,
                            'display_name', sh.display_name,
                            'is_primary', sh.is_primary
                        )
                    ) FILTER (WHERE sh.id IS NOT NULL),
                    '[]'::jsonb
                ) as sport_heads,
                COALESCE(
                    jsonb_agg(
                        DISTINCT jsonb_build_object(
                            'team_id', t.team_id,
                            'team_name', t.name,
                            'sport', t.sport
                        )
                    ) FILTER (WHERE t.team_id IS NOT NULL),
                    '[]'::jsonb
                ) as teams
            FROM profiles c
            LEFT JOIN sport_heads sh ON sh.user_id = c.id
            LEFT JOIN team_members tm ON tm.user_id = c.id
            LEFT JOIN teams t ON t.team_id = tm.team_id
            WHERE c.parent_id = p_parent_id
                AND c.user_type = 'player'
                AND (
                    similarity(c.full_name, p_child_name) > p_threshold
                    OR 
                    LOWER(c.full_name) LIKE '%' || LOWER(p_child_name) || '%'
                    OR
                    LOWER(p_child_name) LIKE '%' || LOWER(c.full_name) || '%'
                )
            GROUP BY c.id, c.full_name, c.date_of_birth
        )
        SELECT * FROM child_data
        ORDER BY similarity_score DESC
        LIMIT 5;
        
        -- Update cache with results
        INSERT INTO child_detection_cache (
            parent_id,
            search_key,
            child_id,
            sport_heads,
            teams,
            confidence_score
        )
        SELECT 
            p_parent_id,
            generate_child_search_key(cd.name, cd.date_of_birth),
            cd.id,
            cd.sport_heads,
            cd.teams,
            cd.similarity_score
        FROM child_data cd
        ON CONFLICT (parent_id, child_id) DO UPDATE
        SET 
            sport_heads = EXCLUDED.sport_heads,
            teams = EXCLUDED.teams,
            confidence_score = EXCLUDED.confidence_score,
            last_accessed = NOW(),
            access_count = child_detection_cache.access_count + 1,
            stale = FALSE;
    END IF;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION find_similar_children TO authenticated;

-- Add comment
COMMENT ON FUNCTION create_child_with_sporthead IS 'Creates a child profile with associated SportHead and optionally adds to team';
COMMENT ON FUNCTION find_similar_children IS 'Finds existing children for a parent using fuzzy name matching';
