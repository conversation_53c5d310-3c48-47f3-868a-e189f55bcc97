-- ABOUTME: Diagnose why cart_sessions isn't accessible via API
-- Run this to understand the exact state

-- 1. Check if the table exists in commerce schema
SELECT 
    'commerce.cart_sessions table' as checking,
    EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_schema = 'commerce' 
        AND table_name = 'cart_sessions'
    ) as exists;

-- 2. Check if the view exists in public schema  
SELECT 
    'public.cart_sessions view' as checking,
    EXISTS (
        SELECT 1 FROM information_schema.views 
        WHERE table_schema = 'public' 
        AND table_name = 'cart_sessions'
    ) as exists;

-- 3. Check permissions on the view
SELECT 
    grantee,
    privilege_type
FROM information_schema.table_privileges 
WHERE table_schema = 'public' 
AND table_name = 'cart_sessions'
ORDER BY grantee, privilege_type;

-- 4. Check if RLS is enabled
SELECT 
    schemaname,
    tablename,
    rowsecurity
FROM pg_tables 
WHERE schemaname IN ('public', 'commerce') 
AND tablename = 'cart_sessions';

-- 5. Test if anon role can see the table
SET ROLE anon;
SELECT count(*) FROM public.cart_sessions;
RESET ROLE;

-- 6. Check what tables PostgREST can see
SELECT 
    table_schema,
    table_name,
    table_type
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name LIKE '%cart%'
ORDER BY table_name;