-- ABOUTME: Check what actually exists in the database
-- This will show us all schemas and tables

-- 1. List all schemas
SELECT schema_name 
FROM information_schema.schemata 
WHERE schema_name NOT IN ('pg_catalog', 'information_schema', 'pg_toast')
ORDER BY schema_name;

-- 2. Check if commerce schema exists
SELECT EXISTS (
    SELECT 1 FROM information_schema.schemata 
    WHERE schema_name = 'commerce'
) as commerce_schema_exists;

-- 3. List all tables in public schema
SELECT table_name, table_type
FROM information_schema.tables
WHERE table_schema = 'public'
ORDER BY table_name;

-- 4. List all tables in commerce schema (if it exists)
SELECT table_name, table_type
FROM information_schema.tables
WHERE table_schema = 'commerce'
ORDER BY table_name;

-- 5. Search for any table with 'cart' in the name across all schemas
SELECT table_schema, table_name, table_type
FROM information_schema.tables
WHERE table_name LIKE '%cart%'
ORDER BY table_schema, table_name;