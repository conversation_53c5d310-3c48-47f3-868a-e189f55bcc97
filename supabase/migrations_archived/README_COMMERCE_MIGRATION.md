# Commerce Schema Migration Instructions

## Overview
The commerce schema migration (`20250201_001_create_commerce_schema.sql`) creates all necessary tables for the BigCommerce e-commerce integration.

## Tables Created
1. **bc_customer_mappings** - Links SHOT users to BigCommerce customers
2. **product_cache** - Local cache of products for performance
3. **cart_sessions** - Persistent shopping carts with 30-day expiration
4. **order_sync** - Synchronized BigCommerce orders
5. **drop_waitlists** - Queue system for limited edition releases
6. **guardian_settings** - Parental control configurations
7. **purchase_approvals** - Approval workflow for minor purchases
8. **loyalty_points** - Customer loyalty program balances
9. **point_transactions** - Loyalty point transaction history
10. **notification_queue** - Multi-channel notification delivery
11. **wishlist_items** - Saved products for later
12. **subscriptions** - Recurring subscription management

## How to Apply the Migration

### Option 1: Using Supabase CLI (Recommended)
```bash
# From the project root
cd /Users/<USER>/d/shotNew

# Apply the migration
supabase db push
```

### Option 2: Direct SQL Execution
1. Go to Supabase Dashboard
2. Navigate to SQL Editor
3. Copy contents of `20250201_001_create_commerce_schema.sql`
4. Execute the SQL

### Option 3: Using Database Connection
```bash
# Using the service role connection
psql "postgresql://postgres.ovfwiyqhubxeqvbrggbe:<EMAIL>:5432/postgres" -f supabase/migrations/20250201_001_create_commerce_schema.sql
```

## Verification Steps
After applying the migration, verify:

1. **Check Tables Exist**:
```sql
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE '%commerce%' 
OR table_name LIKE '%cart%' 
OR table_name LIKE '%order%'
OR table_name LIKE '%product%';
```

2. **Verify RLS Policies**:
```sql
SELECT tablename, policyname, permissive, roles, cmd 
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('bc_customer_mappings', 'product_cache', 'cart_sessions', 'order_sync');
```

3. **Check Indexes**:
```sql
SELECT tablename, indexname 
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename LIKE '%commerce%';
```

## Rollback Instructions
If needed, create a rollback migration:

```sql
-- Drop all commerce tables (CASCADE will remove dependencies)
DROP TABLE IF EXISTS public.subscriptions CASCADE;
DROP TABLE IF EXISTS public.wishlist_items CASCADE;
DROP TABLE IF EXISTS public.notification_queue CASCADE;
DROP TABLE IF EXISTS public.point_transactions CASCADE;
DROP TABLE IF EXISTS public.loyalty_points CASCADE;
DROP TABLE IF EXISTS public.purchase_approvals CASCADE;
DROP TABLE IF EXISTS public.guardian_settings CASCADE;
DROP TABLE IF EXISTS public.drop_waitlists CASCADE;
DROP TABLE IF EXISTS public.order_sync CASCADE;
DROP TABLE IF EXISTS public.cart_sessions CASCADE;
DROP TABLE IF EXISTS public.product_cache CASCADE;
DROP TABLE IF EXISTS public.bc_customer_mappings CASCADE;

-- Drop helper functions
DROP FUNCTION IF EXISTS clean_expired_cart_sessions();
DROP FUNCTION IF EXISTS update_loyalty_points(UUID, INTEGER, TEXT, TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS process_drop_queue(INTEGER, INTEGER);
```

## Next Steps
After applying the migration:
1. Test RLS policies with different user roles
2. Set up scheduled jobs for cart cleanup
3. Configure BigCommerce webhooks to populate data
4. Implement Edge Functions to interact with these tables