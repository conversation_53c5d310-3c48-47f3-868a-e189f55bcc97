-- Set up custom configuration parameters for cron jobs
-- These parameters are used by the content sync cron jobs

-- First, we need to create the custom settings if they don't exist
-- Note: These need to be set in postgresql.conf or via ALTER SYSTEM
-- For Supabase hosted instances, these should be set via environment variables

-- Create a function to set up the configuration
CREATE OR REPLACE FUNCTION setup_cron_config()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_supabase_url TEXT;
  v_service_role_key TEXT;
BEGIN
  -- Get the Supabase URL from the current database
  -- For Supabase hosted projects, this is typically: https://[project-ref].supabase.co
  v_supabase_url := 'https://ovfwiyqhubxeqvbrggbe.supabase.co';
  
  -- Get the service role key (this should be stored securely)
  -- In production, this should come from Vault or environment variables
  v_service_role_key := 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y';

  -- Store these in a settings table instead of using current_setting
  -- Create a settings table if it doesn't exist
  CREATE TABLE IF NOT EXISTS content.cron_settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
  );

  -- Insert or update the settings
  INSERT INTO content.cron_settings (key, value)
  VALUES 
    ('supabase_url', v_supabase_url),
    ('service_role_key', v_service_role_key)
  ON CONFLICT (key) 
  DO UPDATE SET 
    value = EXCLUDED.value,
    updated_at = NOW();

  -- Update the existing cron jobs to use the new approach
  -- First, unschedule the old jobs
  SELECT cron.unschedule('sync-pulse-content');
  SELECT cron.unschedule('retry-failed-content-syncs');

  -- Recreate the sync job with the correct URL
  SELECT cron.schedule(
    'sync-pulse-content',
    '*/15 * * * *', -- every 15 minutes
    $$
    SELECT net.http_post(
      url := (SELECT value FROM content.cron_settings WHERE key = 'supabase_url') || '/functions/v1/content-sync',
      headers := jsonb_build_object(
        'Authorization', 'Bearer ' || (SELECT value FROM content.cron_settings WHERE key = 'service_role_key'),
        'Content-Type', 'application/json'
      ),
      body := jsonb_build_object(
        'source', 'cron',
        'timestamp', now()
      )
    );
    $$
  );

  -- Recreate the retry job
  SELECT cron.schedule(
    'retry-failed-content-syncs',
    '*/5 * * * *', -- every 5 minutes
    'SELECT content.retry_failed_syncs();'
  );

  -- Grant necessary permissions
  GRANT SELECT ON content.cron_settings TO postgres;
  GRANT USAGE ON SCHEMA content TO postgres;

  RAISE NOTICE 'Cron configuration setup completed successfully';
END;
$$;

-- Execute the setup function
SELECT setup_cron_config();

-- Create an updated retry function that uses the settings table
CREATE OR REPLACE FUNCTION content.retry_failed_syncs()
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
  failed_sync RECORD;
  v_supabase_url TEXT;
  v_service_role_key TEXT;
BEGIN
  -- Get the configuration values
  SELECT value INTO v_supabase_url FROM content.cron_settings WHERE key = 'supabase_url';
  SELECT value INTO v_service_role_key FROM content.cron_settings WHERE key = 'service_role_key';

  -- Find failed syncs from the last hour
  FOR failed_sync IN 
    SELECT id, metadata
    FROM content.pulse_sync_log
    WHERE sync_status = 'failed'
      AND sync_started_at > NOW() - INTERVAL '1 hour'
      AND COALESCE((metadata->>'retry_count')::int, 0) < 3
  LOOP
    -- Trigger a retry
    PERFORM net.http_post(
      url := v_supabase_url || '/functions/v1/content-sync',
      headers := jsonb_build_object(
        'Authorization', 'Bearer ' || v_service_role_key,
        'Content-Type', 'application/json'
      ),
      body := jsonb_build_object(
        'source', 'retry',
        'retry_count', COALESCE((failed_sync.metadata->>'retry_count')::int, 0) + 1,
        'original_sync_id', failed_sync.id
      )
    );
  END LOOP;
END;
$$;

-- Add a helper function to check if cron jobs are active
CREATE OR REPLACE FUNCTION content.check_cron_status()
RETURNS TABLE (
  jobname TEXT,
  schedule TEXT,
  active BOOLEAN,
  last_run TIMESTAMP,
  next_run TIMESTAMP
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    j.jobname::TEXT,
    j.schedule::TEXT,
    j.active,
    j.jobname::TEXT as last_run_placeholder, -- cron doesn't track this by default
    cron.job_run_details(j.jobid, 1) as next_run_placeholder
  FROM cron.job j
  WHERE j.jobname LIKE '%pulse%' OR j.jobname LIKE '%content%';
END;
$$;

-- Add comment for documentation
COMMENT ON FUNCTION content.check_cron_status() IS 'Returns the status of content sync cron jobs';