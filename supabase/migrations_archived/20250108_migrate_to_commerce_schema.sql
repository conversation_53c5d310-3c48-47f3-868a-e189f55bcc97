-- ABOUTME: Migration to move all commerce tables from public to commerce schema
-- This migration moves existing commerce tables while preserving data and relationships

-- Create commerce schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS commerce;

-- Grant usage on commerce schema to authenticated and anon users
GRANT USAGE ON SCHEMA commerce TO authenticated, anon;

-- =====================================================
-- STEP 1: DISABLE TRIGGERS TEMPORARILY
-- =====================================================
ALTER TABLE public.cart_sessions DISABLE TRIGGER update_cart_sessions_updated_at;
ALTER TABLE public.product_cache DISABLE TRIGGER update_product_cache_updated_at;
ALTER TABLE public.bc_customer_mappings DISABLE TRIGGER update_bc_customer_mappings_updated_at;
ALTER TABLE public.order_sync DISABLE TRIGGER update_order_sync_updated_at;
ALTER TABLE public.drop_waitlists DISABLE TRIGGER update_drop_waitlists_updated_at;
ALTER TABLE public.guardian_settings DISABLE TRIGGER update_guardian_settings_updated_at;
ALTER TABLE public.purchase_approvals DISABLE TRIGGER update_purchase_approvals_updated_at;
ALTER TABLE public.loyalty_points DISABLE TRIGGER update_loyalty_points_updated_at;
ALTER TABLE public.point_transactions DISABLE TRIGGER update_point_transactions_updated_at;
ALTER TABLE public.notification_queue DISABLE TRIGGER update_notification_queue_updated_at;
ALTER TABLE public.wishlist_items DISABLE TRIGGER update_wishlist_items_updated_at;
ALTER TABLE public.subscriptions DISABLE TRIGGER update_subscriptions_updated_at;

-- =====================================================
-- STEP 2: MOVE TABLES TO COMMERCE SCHEMA
-- =====================================================
-- Move each table with ALTER TABLE ... SET SCHEMA
ALTER TABLE public.bc_customer_mappings SET SCHEMA commerce;
ALTER TABLE public.product_cache SET SCHEMA commerce;
ALTER TABLE public.cart_sessions SET SCHEMA commerce;
ALTER TABLE public.order_sync SET SCHEMA commerce;
ALTER TABLE public.drop_waitlists SET SCHEMA commerce;
ALTER TABLE public.guardian_settings SET SCHEMA commerce;
ALTER TABLE public.purchase_approvals SET SCHEMA commerce;
ALTER TABLE public.loyalty_points SET SCHEMA commerce;
ALTER TABLE public.point_transactions SET SCHEMA commerce;
ALTER TABLE public.notification_queue SET SCHEMA commerce;
ALTER TABLE public.wishlist_items SET SCHEMA commerce;
ALTER TABLE public.subscriptions SET SCHEMA commerce;

-- =====================================================
-- STEP 3: UPDATE FOREIGN KEY REFERENCES
-- =====================================================
-- Drop existing foreign key constraints that reference moved tables
ALTER TABLE commerce.point_transactions DROP CONSTRAINT IF EXISTS point_transactions_profile_id_fkey;
ALTER TABLE commerce.purchase_approvals DROP CONSTRAINT IF EXISTS purchase_approvals_cart_session_id_fkey;

-- Recreate foreign key constraints with correct schema references
ALTER TABLE commerce.point_transactions 
    ADD CONSTRAINT point_transactions_profile_id_fkey 
    FOREIGN KEY (profile_id) REFERENCES public.profiles(id) ON DELETE CASCADE;

ALTER TABLE commerce.purchase_approvals 
    ADD CONSTRAINT purchase_approvals_cart_session_id_fkey 
    FOREIGN KEY (cart_session_id) REFERENCES commerce.cart_sessions(id) ON DELETE SET NULL;

-- =====================================================
-- STEP 4: RECREATE TRIGGERS WITH COMMERCE SCHEMA
-- =====================================================
-- Update trigger function to be schema-aware
CREATE OR REPLACE FUNCTION commerce.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Recreate all triggers
CREATE TRIGGER update_cart_sessions_updated_at 
    BEFORE UPDATE ON commerce.cart_sessions 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

CREATE TRIGGER update_product_cache_updated_at 
    BEFORE UPDATE ON commerce.product_cache 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

CREATE TRIGGER update_bc_customer_mappings_updated_at 
    BEFORE UPDATE ON commerce.bc_customer_mappings 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

CREATE TRIGGER update_order_sync_updated_at 
    BEFORE UPDATE ON commerce.order_sync 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

CREATE TRIGGER update_drop_waitlists_updated_at 
    BEFORE UPDATE ON commerce.drop_waitlists 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

CREATE TRIGGER update_guardian_settings_updated_at 
    BEFORE UPDATE ON commerce.guardian_settings 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

CREATE TRIGGER update_purchase_approvals_updated_at 
    BEFORE UPDATE ON commerce.purchase_approvals 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

CREATE TRIGGER update_loyalty_points_updated_at 
    BEFORE UPDATE ON commerce.loyalty_points 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

CREATE TRIGGER update_point_transactions_updated_at 
    BEFORE UPDATE ON commerce.point_transactions 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

CREATE TRIGGER update_notification_queue_updated_at 
    BEFORE UPDATE ON commerce.notification_queue 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

CREATE TRIGGER update_wishlist_items_updated_at 
    BEFORE UPDATE ON commerce.wishlist_items 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at 
    BEFORE UPDATE ON commerce.subscriptions 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

-- =====================================================
-- STEP 5: UPDATE FUNCTIONS TO USE COMMERCE SCHEMA
-- =====================================================
-- Update the cart cleanup function
CREATE OR REPLACE FUNCTION commerce.clean_expired_cart_sessions()
RETURNS void AS $$
BEGIN
    DELETE FROM commerce.cart_sessions
    WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Update loyalty points functions
CREATE OR REPLACE FUNCTION commerce.get_loyalty_balance(p_profile_id UUID)
RETURNS INTEGER AS $$
BEGIN
    RETURN COALESCE(
        (SELECT current_balance FROM commerce.loyalty_points WHERE profile_id = p_profile_id),
        0
    );
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION commerce.add_loyalty_points(
    p_profile_id UUID,
    p_points INTEGER,
    p_transaction_type TEXT,
    p_description TEXT,
    p_metadata JSONB DEFAULT NULL
)
RETURNS commerce.point_transactions AS $$
DECLARE
    v_transaction commerce.point_transactions;
    v_new_balance INTEGER;
BEGIN
    -- Get current balance
    SELECT COALESCE(current_balance, 0) + p_points INTO v_new_balance
    FROM commerce.loyalty_points
    WHERE profile_id = p_profile_id;
    
    -- If no loyalty record exists, create one
    IF v_new_balance IS NULL THEN
        INSERT INTO commerce.loyalty_points (profile_id, current_balance, lifetime_earned)
        VALUES (p_profile_id, p_points, GREATEST(p_points, 0))
        RETURNING current_balance INTO v_new_balance;
    ELSE
        -- Update balance
        UPDATE commerce.loyalty_points
        SET current_balance = v_new_balance,
            lifetime_earned = lifetime_earned + GREATEST(p_points, 0),
            lifetime_redeemed = lifetime_redeemed + ABS(LEAST(p_points, 0))
        WHERE profile_id = p_profile_id;
    END IF;
    
    -- Create transaction record
    INSERT INTO commerce.point_transactions (
        profile_id,
        points,
        transaction_type,
        description,
        balance_after,
        metadata
    ) VALUES (
        p_profile_id,
        p_points,
        p_transaction_type,
        p_description,
        v_new_balance,
        p_metadata
    ) RETURNING * INTO v_transaction;
    
    RETURN v_transaction;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 6: UPDATE RLS POLICIES
-- =====================================================
-- Drop existing policies
DROP POLICY IF EXISTS "Users can manage own carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can view own BC mappings" ON commerce.bc_customer_mappings;
DROP POLICY IF EXISTS "Anyone can view visible products" ON commerce.product_cache;
DROP POLICY IF EXISTS "Users can view own orders" ON commerce.order_sync;
DROP POLICY IF EXISTS "Users can join waitlists" ON commerce.drop_waitlists;
DROP POLICY IF EXISTS "Users can view own guardian settings" ON commerce.guardian_settings;
DROP POLICY IF EXISTS "Guardians can manage minor settings" ON commerce.guardian_settings;
DROP POLICY IF EXISTS "Users can view own approvals" ON commerce.purchase_approvals;
DROP POLICY IF EXISTS "Guardians can manage approvals" ON commerce.purchase_approvals;
DROP POLICY IF EXISTS "Users can view own points" ON commerce.loyalty_points;
DROP POLICY IF EXISTS "Users can view own transactions" ON commerce.point_transactions;
DROP POLICY IF EXISTS "Users can view own notifications" ON commerce.notification_queue;
DROP POLICY IF EXISTS "Users can update own notifications" ON commerce.notification_queue;
DROP POLICY IF EXISTS "Users can manage own wishlist" ON commerce.wishlist_items;
DROP POLICY IF EXISTS "Users can view own subscriptions" ON commerce.subscriptions;
DROP POLICY IF EXISTS "Users can manage own subscriptions" ON commerce.subscriptions;

-- Recreate all RLS policies
-- Cart sessions
CREATE POLICY "Users can manage own carts" ON commerce.cart_sessions
    USING (
        profile_id = auth.uid() 
        OR (profile_id IS NULL AND session_id = current_setting('request.session.id', true))
    )
    WITH CHECK (
        profile_id = auth.uid() 
        OR (profile_id IS NULL AND session_id = current_setting('request.session.id', true))
    );

-- BC customer mappings
CREATE POLICY "Users can view own BC mappings" ON commerce.bc_customer_mappings
    FOR SELECT USING (profile_id = auth.uid());

-- Product cache (public read)
CREATE POLICY "Anyone can view visible products" ON commerce.product_cache
    FOR SELECT USING (is_visible = true);

-- Order sync
CREATE POLICY "Users can view own orders" ON commerce.order_sync
    FOR SELECT USING (profile_id = auth.uid());

-- Drop waitlists
CREATE POLICY "Users can join waitlists" ON commerce.drop_waitlists
    USING (profile_id = auth.uid())
    WITH CHECK (profile_id = auth.uid());

-- Guardian settings
CREATE POLICY "Users can view own guardian settings" ON commerce.guardian_settings
    FOR SELECT USING (
        minor_profile_id = auth.uid() 
        OR guardian_profile_id = auth.uid()
    );

CREATE POLICY "Guardians can manage minor settings" ON commerce.guardian_settings
    FOR ALL USING (guardian_profile_id = auth.uid());

-- Purchase approvals
CREATE POLICY "Users can view own approvals" ON commerce.purchase_approvals
    FOR SELECT USING (
        minor_profile_id = auth.uid() 
        OR guardian_profile_id = auth.uid()
    );

CREATE POLICY "Guardians can manage approvals" ON commerce.purchase_approvals
    FOR UPDATE USING (guardian_profile_id = auth.uid());

-- Loyalty points
CREATE POLICY "Users can view own points" ON commerce.loyalty_points
    FOR SELECT USING (profile_id = auth.uid());

-- Point transactions
CREATE POLICY "Users can view own transactions" ON commerce.point_transactions
    FOR SELECT USING (profile_id = auth.uid());

-- Notification queue
CREATE POLICY "Users can view own notifications" ON commerce.notification_queue
    FOR SELECT USING (profile_id = auth.uid());

CREATE POLICY "Users can update own notifications" ON commerce.notification_queue
    FOR UPDATE USING (profile_id = auth.uid());

-- Wishlist items
CREATE POLICY "Users can manage own wishlist" ON commerce.wishlist_items
    USING (profile_id = auth.uid())
    WITH CHECK (profile_id = auth.uid());

-- Subscriptions
CREATE POLICY "Users can view own subscriptions" ON commerce.subscriptions
    FOR SELECT USING (profile_id = auth.uid());

CREATE POLICY "Users can manage own subscriptions" ON commerce.subscriptions
    FOR UPDATE USING (profile_id = auth.uid());

-- =====================================================
-- STEP 7: GRANT PERMISSIONS
-- =====================================================
-- Grant permissions on all commerce tables
GRANT SELECT ON ALL TABLES IN SCHEMA commerce TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON commerce.cart_sessions TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON commerce.drop_waitlists TO authenticated;
GRANT INSERT, UPDATE, DELETE ON commerce.purchase_approvals TO authenticated;
GRANT INSERT, UPDATE, DELETE ON commerce.wishlist_items TO authenticated;
GRANT UPDATE ON commerce.notification_queue TO authenticated;
GRANT UPDATE ON commerce.subscriptions TO authenticated;

-- Grant sequence permissions
GRANT USAGE ON ALL SEQUENCES IN SCHEMA commerce TO anon, authenticated;

-- =====================================================
-- STEP 8: CREATE VIEWS FOR BACKWARD COMPATIBILITY (OPTIONAL)
-- =====================================================
-- If needed, create views in public schema to maintain compatibility
-- Uncomment if you need backward compatibility during transition

-- CREATE VIEW public.cart_sessions AS SELECT * FROM commerce.cart_sessions;
-- CREATE VIEW public.product_cache AS SELECT * FROM commerce.product_cache;
-- CREATE VIEW public.bc_customer_mappings AS SELECT * FROM commerce.bc_customer_mappings;
-- CREATE VIEW public.order_sync AS SELECT * FROM commerce.order_sync;

-- =====================================================
-- STEP 9: COMMENTS
-- =====================================================
COMMENT ON SCHEMA commerce IS 'E-commerce functionality for BigCommerce integration';
COMMENT ON TABLE commerce.cart_sessions IS 'Persistent shopping cart storage';
COMMENT ON TABLE commerce.product_cache IS 'Local cache of BigCommerce products';
COMMENT ON TABLE commerce.bc_customer_mappings IS 'Maps SHOT users to BigCommerce customers';
COMMENT ON TABLE commerce.order_sync IS 'Synchronized BigCommerce orders';
COMMENT ON TABLE commerce.drop_waitlists IS 'Queue management for limited edition releases';
COMMENT ON TABLE commerce.guardian_settings IS 'Parental control settings for purchases';
COMMENT ON TABLE commerce.purchase_approvals IS 'Approval workflow for minor purchases';
COMMENT ON TABLE commerce.loyalty_points IS 'Customer loyalty program balances';
COMMENT ON TABLE commerce.point_transactions IS 'Loyalty point transaction history';
COMMENT ON TABLE commerce.notification_queue IS 'Multi-channel notification delivery';
COMMENT ON TABLE commerce.wishlist_items IS 'Saved products for later';
COMMENT ON TABLE commerce.subscriptions IS 'Recurring subscription management';