-- ABOUTME: Create RPC functions to access cart_sessions if direct access fails
-- This provides an alternative way to interact with cart_sessions

-- Function to get cart sessions
CREATE OR REPLACE FUNCTION get_cart_session(p_session_id TEXT)
RETURNS TABLE (
    id UUID,
    session_id TEXT,
    profile_id UUID,
    items JSONB,
    subtotal DECIMAL(10,2),
    tax_total DECIMAL(10,2),
    shipping_total DECIMAL(10,2),
    discount_total DECIMAL(10,2),
    grand_total DECIMAL(10,2),
    coupon_codes TEXT[],
    shipping_address JSONB,
    billing_address JSONB,
    selected_shipping_option JSONB,
    notes TEXT,
    metadata JSONB,
    last_activity_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) 
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    RETURN QUERY
    SELECT * FROM commerce.cart_sessions
    WHERE cart_sessions.session_id = p_session_id;
END;
$$ LANGUAGE plpgsql;

-- Function to upsert cart session
CREATE OR REPLACE FUNCTION upsert_cart_session(
    p_session_id TEXT,
    p_profile_id UUID DEFAULT NULL,
    p_items JSONB DEFAULT '[]'::jsonb,
    p_subtotal DECIMAL(10,2) DEFAULT 0,
    p_tax_total DECIMAL(10,2) DEFAULT 0,
    p_shipping_total DECIMAL(10,2) DEFAULT 0,
    p_discount_total DECIMAL(10,2) DEFAULT 0,
    p_grand_total DECIMAL(10,2) DEFAULT 0,
    p_coupon_codes TEXT[] DEFAULT ARRAY[]::TEXT[],
    p_shipping_address JSONB DEFAULT NULL,
    p_billing_address JSONB DEFAULT NULL,
    p_selected_shipping_option JSONB DEFAULT NULL,
    p_notes TEXT DEFAULT NULL,
    p_metadata JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_cart_id UUID;
BEGIN
    INSERT INTO commerce.cart_sessions (
        session_id, profile_id, items, subtotal, tax_total,
        shipping_total, discount_total, grand_total, coupon_codes,
        shipping_address, billing_address, selected_shipping_option,
        notes, metadata, last_activity_at
    ) VALUES (
        p_session_id, p_profile_id, p_items, p_subtotal, p_tax_total,
        p_shipping_total, p_discount_total, p_grand_total, p_coupon_codes,
        p_shipping_address, p_billing_address, p_selected_shipping_option,
        p_notes, p_metadata, NOW()
    )
    ON CONFLICT (session_id) DO UPDATE SET
        profile_id = EXCLUDED.profile_id,
        items = EXCLUDED.items,
        subtotal = EXCLUDED.subtotal,
        tax_total = EXCLUDED.tax_total,
        shipping_total = EXCLUDED.shipping_total,
        discount_total = EXCLUDED.discount_total,
        grand_total = EXCLUDED.grand_total,
        coupon_codes = EXCLUDED.coupon_codes,
        shipping_address = EXCLUDED.shipping_address,
        billing_address = EXCLUDED.billing_address,
        selected_shipping_option = EXCLUDED.selected_shipping_option,
        notes = EXCLUDED.notes,
        metadata = EXCLUDED.metadata,
        last_activity_at = NOW()
    RETURNING id INTO v_cart_id;
    
    RETURN v_cart_id;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_cart_session(TEXT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION upsert_cart_session(TEXT, UUID, JSONB, DECIMAL, DECIMAL, DECIMAL, DECIMAL, DECIMAL, TEXT[], JSONB, JSONB, JSONB, TEXT, JSONB) TO anon, authenticated;