-- Create a helper function for the fallback sport head creation
-- This uses direct SQL to avoid potential issues with REST API calls

CREATE OR REPLACE FUNCTION insert_sport_head_fallback(
  p_user_id UUID,
  p_sport TEXT,
  p_display_name TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_sport_head_id UUID;
BEGIN
  -- Insert the sport head with minimal fields
  INSERT INTO sport_heads (
    id,
    user_id,
    sport,
    display_name,
    created_at
  ) VALUES (
    uuid_generate_v4(),
    p_user_id,
    p_sport,
    p_display_name,
    now()
  )
  RETURNING id INTO v_sport_head_id;
  
  -- Return the new ID
  RETURN v_sport_head_id;
END;
$$;

-- Create a helper function to get or create a sport head
CREATE OR REPLACE FUNCTION get_or_create_sport_head(
  p_user_id UUID,
  p_sport TEXT,
  p_display_name TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_sport_head_id UUID;
BEGIN
  -- First try to find an existing sport head
  SELECT id INTO v_sport_head_id
  FROM sport_heads
  WHERE user_id = p_user_id AND sport = p_sport
  LIMIT 1;
  
  -- If found, return it
  IF v_sport_head_id IS NOT NULL THEN
    RETURN v_sport_head_id;
  END IF;
  
  -- Otherwise create a new one
  INSERT INTO sport_heads (
    id,
    user_id,
    sport,
    display_name,
    created_at
  ) VALUES (
    uuid_generate_v4(),
    p_user_id,
    p_sport,
    p_display_name,
    now()
  )
  RETURNING id INTO v_sport_head_id;
  
  -- Return the new ID
  RETURN v_sport_head_id;
END;
$$;
