-- ABOUTME: Create commerce tables in public schema for Supabase v1 compatibility
-- This is a temporary solution until upgrading to Supabase v2

-- =====================================================
-- CART_SESSIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.cart_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id TEXT UNIQUE NOT NULL,
    profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    items JSONB DEFAULT '[]'::jsonb NOT NULL,
    subtotal DECIMAL(10,2) DEFAULT 0 NOT NULL,
    tax_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    shipping_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    discount_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    grand_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    coupon_codes TEXT[] DEFAULT ARRAY[]::TEXT[],
    shipping_address JSONB,
    billing_address JSONB,
    selected_shipping_option JSONB,
    notes TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days') NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_cart_sessions_session_id ON public.cart_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_cart_sessions_profile_id ON public.cart_sessions(profile_id);

-- =====================================================
-- PRODUCT_CACHE TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.product_cache (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    bc_product_id INTEGER NOT NULL UNIQUE,
    sku TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    sale_price DECIMAL(10, 2),
    cost_price DECIMAL(10, 2),
    retail_price DECIMAL(10, 2),
    weight DECIMAL(10, 2),
    categories JSONB DEFAULT '[]'::jsonb,
    brand_id INTEGER,
    brand_name TEXT,
    images JSONB DEFAULT '[]'::jsonb,
    variants JSONB DEFAULT '[]'::jsonb,
    custom_fields JSONB DEFAULT '{}'::jsonb,
    inventory_level INTEGER DEFAULT 0,
    inventory_tracking TEXT,
    is_visible BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    meta_keywords TEXT[],
    search_keywords TEXT,
    availability TEXT,
    condition TEXT DEFAULT 'new',
    cache_ttl_minutes INTEGER DEFAULT 5,
    cached_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_product_cache_bc_id ON public.product_cache(bc_product_id);

-- =====================================================
-- OTHER REQUIRED TABLES
-- =====================================================
CREATE TABLE IF NOT EXISTS public.guardian_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    child_profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    guardian_profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    purchase_approval_required BOOLEAN DEFAULT true,
    max_purchase_amount DECIMAL(10,2),
    allowed_categories INTEGER[],
    blocked_categories INTEGER[],
    notification_preferences JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(child_profile_id, guardian_profile_id)
);

CREATE TABLE IF NOT EXISTS public.purchase_approvals (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    child_profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    guardian_profile_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    cart_data JSONB NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'denied', 'expired')),
    guardian_notes TEXT,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responded_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '48 hours'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- UPDATE TRIGGERS
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers
CREATE TRIGGER update_cart_sessions_updated_at 
    BEFORE UPDATE ON public.cart_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_cache_updated_at 
    BEFORE UPDATE ON public.product_cache 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- RLS POLICIES
-- =====================================================
ALTER TABLE public.cart_sessions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own carts" ON public.cart_sessions
    FOR SELECT
    USING (auth.uid() = profile_id OR profile_id IS NULL);

CREATE POLICY "Users can create carts" ON public.cart_sessions
    FOR INSERT
    WITH CHECK (auth.uid() = profile_id OR profile_id IS NULL);

CREATE POLICY "Users can update own carts" ON public.cart_sessions
    FOR UPDATE
    USING (auth.uid() = profile_id OR profile_id IS NULL);

CREATE POLICY "Users can delete own carts" ON public.cart_sessions
    FOR DELETE
    USING (auth.uid() = profile_id OR profile_id IS NULL);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.cart_sessions TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.product_cache TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE ON public.guardian_settings TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.purchase_approvals TO authenticated;