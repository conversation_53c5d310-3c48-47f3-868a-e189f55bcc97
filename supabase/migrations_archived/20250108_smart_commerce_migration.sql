-- ABOUTME: Smart migration that checks actual database state and migrates accordingly
-- This migration will work regardless of whether tables exist in public or commerce schema

-- Create commerce schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS commerce;
GRANT USAGE ON SCHEMA commerce TO authenticated, anon;

-- =====================================================
-- HELPER FUNCTION TO CHECK IF TABLE EXISTS
-- =====================================================
CREATE OR REPLACE FUNCTION table_exists(schema_name text, table_name text) 
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_schema = schema_name 
        AND information_schema.tables.table_name = table_exists.table_name
    );
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CART_SESSIONS TABLE
-- =====================================================
DO $$
BEGIN
    -- Check if cart_sessions exists in public
    IF table_exists('public', 'cart_sessions') THEN
        -- Move it to commerce schema
        ALTER TABLE public.cart_sessions SET SCHEMA commerce;
        RAISE NOTICE 'Moved cart_sessions from public to commerce schema';
    -- Check if it exists in commerce already
    ELSIF table_exists('commerce', 'cart_sessions') THEN
        RAISE NOTICE 'cart_sessions already exists in commerce schema';
    ELSE
        -- Create it in commerce schema
        CREATE TABLE commerce.cart_sessions (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            session_id TEXT UNIQUE NOT NULL,
            profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
            items JSONB DEFAULT '[]'::jsonb,
            subtotal DECIMAL(10,2) DEFAULT 0,
            tax_total DECIMAL(10,2) DEFAULT 0,
            shipping_total DECIMAL(10,2) DEFAULT 0,
            discount_total DECIMAL(10,2) DEFAULT 0,
            grand_total DECIMAL(10,2) DEFAULT 0,
            coupon_codes TEXT[] DEFAULT ARRAY[]::TEXT[],
            shipping_address JSONB,
            billing_address JSONB,
            selected_shipping_option JSONB,
            notes TEXT,
            metadata JSONB,
            last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days'),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create indexes
        CREATE INDEX idx_cart_sessions_session_id ON commerce.cart_sessions(session_id);
        CREATE INDEX idx_cart_sessions_profile_id ON commerce.cart_sessions(profile_id);
        CREATE INDEX idx_cart_sessions_last_activity ON commerce.cart_sessions(last_activity_at);
        CREATE INDEX idx_cart_sessions_expires_at ON commerce.cart_sessions(expires_at);
        
        RAISE NOTICE 'Created cart_sessions in commerce schema';
    END IF;
END $$;

-- =====================================================
-- PRODUCT_CACHE TABLE
-- =====================================================
DO $$
BEGIN
    IF table_exists('public', 'product_cache') THEN
        ALTER TABLE public.product_cache SET SCHEMA commerce;
        RAISE NOTICE 'Moved product_cache from public to commerce schema';
    ELSIF NOT table_exists('commerce', 'product_cache') THEN
        CREATE TABLE commerce.product_cache (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            bc_product_id INTEGER NOT NULL UNIQUE,
            sku TEXT NOT NULL,
            name TEXT NOT NULL,
            description TEXT,
            price DECIMAL(10, 2) NOT NULL,
            sale_price DECIMAL(10, 2),
            cost_price DECIMAL(10, 2),
            retail_price DECIMAL(10, 2),
            weight DECIMAL(10, 2),
            categories JSONB DEFAULT '[]'::jsonb,
            brand_id INTEGER,
            brand_name TEXT,
            images JSONB DEFAULT '[]'::jsonb,
            variants JSONB DEFAULT '[]'::jsonb,
            custom_fields JSONB DEFAULT '{}'::jsonb,
            inventory_level INTEGER DEFAULT 0,
            inventory_tracking TEXT,
            is_visible BOOLEAN DEFAULT true,
            is_featured BOOLEAN DEFAULT false,
            sort_order INTEGER DEFAULT 0,
            meta_keywords TEXT[],
            search_keywords TEXT,
            availability TEXT,
            condition TEXT DEFAULT 'new',
            cache_ttl_minutes INTEGER DEFAULT 5,
            cached_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_product_cache_bc_id ON commerce.product_cache(bc_product_id);
        CREATE INDEX idx_product_cache_sku ON commerce.product_cache(sku);
        CREATE INDEX idx_product_cache_visible ON commerce.product_cache(is_visible);
        
        RAISE NOTICE 'Created product_cache in commerce schema';
    END IF;
END $$;

-- =====================================================
-- BC_CUSTOMER_MAPPINGS TABLE
-- =====================================================
DO $$
BEGIN
    IF table_exists('public', 'bc_customer_mappings') THEN
        ALTER TABLE public.bc_customer_mappings SET SCHEMA commerce;
        RAISE NOTICE 'Moved bc_customer_mappings from public to commerce schema';
    ELSIF NOT table_exists('commerce', 'bc_customer_mappings') THEN
        CREATE TABLE commerce.bc_customer_mappings (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
            bc_customer_id INTEGER NOT NULL UNIQUE,
            bc_email TEXT NOT NULL,
            sync_status TEXT DEFAULT 'active' CHECK (sync_status IN ('active', 'inactive', 'error')),
            last_sync_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            UNIQUE(profile_id)
        );
        
        CREATE INDEX idx_bc_customer_mappings_profile_id ON commerce.bc_customer_mappings(profile_id);
        CREATE INDEX idx_bc_customer_mappings_bc_customer_id ON commerce.bc_customer_mappings(bc_customer_id);
        
        RAISE NOTICE 'Created bc_customer_mappings in commerce schema';
    END IF;
END $$;

-- =====================================================
-- ORDER_SYNC TABLE
-- =====================================================
DO $$
BEGIN
    IF table_exists('public', 'order_sync') THEN
        ALTER TABLE public.order_sync SET SCHEMA commerce;
        RAISE NOTICE 'Moved order_sync from public to commerce schema';
    ELSIF NOT table_exists('commerce', 'order_sync') THEN
        CREATE TABLE commerce.order_sync (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            bc_order_id INTEGER NOT NULL UNIQUE,
            profile_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
            order_number TEXT NOT NULL,
            status TEXT NOT NULL,
            subtotal DECIMAL(10, 2) NOT NULL,
            tax_total DECIMAL(10, 2) DEFAULT 0,
            shipping_total DECIMAL(10, 2) DEFAULT 0,
            discount_total DECIMAL(10, 2) DEFAULT 0,
            grand_total DECIMAL(10, 2) NOT NULL,
            items_total INTEGER NOT NULL,
            payment_method TEXT,
            payment_status TEXT,
            shipping_method TEXT,
            tracking_numbers TEXT[],
            currency_code TEXT DEFAULT 'GBP',
            customer_message TEXT,
            staff_notes TEXT,
            order_source TEXT,
            bc_customer_id INTEGER,
            billing_address JSONB NOT NULL,
            shipping_address JSONB,
            products JSONB NOT NULL DEFAULT '[]'::jsonb,
            custom_status TEXT,
            metadata JSONB DEFAULT '{}'::jsonb,
            ordered_at TIMESTAMP WITH TIME ZONE NOT NULL,
            shipped_at TIMESTAMP WITH TIME ZONE,
            synced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_order_sync_bc_order_id ON commerce.order_sync(bc_order_id);
        CREATE INDEX idx_order_sync_profile_id ON commerce.order_sync(profile_id);
        CREATE INDEX idx_order_sync_order_number ON commerce.order_sync(order_number);
        CREATE INDEX idx_order_sync_status ON commerce.order_sync(status);
        
        RAISE NOTICE 'Created order_sync in commerce schema';
    END IF;
END $$;

-- Continue pattern for other tables...
-- Check and migrate/create: drop_waitlists, guardian_settings, purchase_approvals, 
-- loyalty_points, point_transactions, notification_queue, wishlist_items, subscriptions

-- =====================================================
-- UPDATE FUNCTION FOR TIMESTAMPS
-- =====================================================
CREATE OR REPLACE FUNCTION commerce.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ADD TRIGGERS FOR UPDATED_AT
-- =====================================================
DO $$
BEGIN
    -- Add trigger to cart_sessions if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger 
        WHERE tgname = 'update_cart_sessions_updated_at'
    ) THEN
        CREATE TRIGGER update_cart_sessions_updated_at 
            BEFORE UPDATE ON commerce.cart_sessions 
            FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();
    END IF;
    
    -- Add similar triggers for other tables...
END $$;

-- =====================================================
-- RLS POLICIES FOR CART_SESSIONS
-- =====================================================
ALTER TABLE commerce.cart_sessions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view own carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can create carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can update own carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can delete own carts" ON commerce.cart_sessions;

-- Create policies
CREATE POLICY "Users can view own carts" ON commerce.cart_sessions
    FOR SELECT
    USING (
        auth.uid() = profile_id 
        OR session_id = coalesce(current_setting('request.headers', true)::json->>'x-session-id', '')
    );

CREATE POLICY "Users can create carts" ON commerce.cart_sessions
    FOR INSERT
    WITH CHECK (
        auth.uid() = profile_id 
        OR profile_id IS NULL
    );

CREATE POLICY "Users can update own carts" ON commerce.cart_sessions
    FOR UPDATE
    USING (
        auth.uid() = profile_id 
        OR (profile_id IS NULL AND session_id = coalesce(current_setting('request.headers', true)::json->>'x-session-id', ''))
    );

CREATE POLICY "Users can delete own carts" ON commerce.cart_sessions
    FOR DELETE
    USING (
        auth.uid() = profile_id 
        OR (profile_id IS NULL AND session_id = coalesce(current_setting('request.headers', true)::json->>'x-session-id', ''))
    );

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================
GRANT SELECT ON ALL TABLES IN SCHEMA commerce TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON commerce.cart_sessions TO anon, authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA commerce TO anon, authenticated;

-- =====================================================
-- CLEANUP
-- =====================================================
DROP FUNCTION IF EXISTS table_exists(text, text);

-- =====================================================
-- SUMMARY
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE 'Commerce schema migration completed. Tables now in commerce schema:';
    RAISE NOTICE '- cart_sessions';
    RAISE NOTICE '- product_cache'; 
    RAISE NOTICE '- bc_customer_mappings';
    RAISE NOTICE '- order_sync';
    RAISE NOTICE 'Run additional migrations for other commerce tables as needed.';
END $$;