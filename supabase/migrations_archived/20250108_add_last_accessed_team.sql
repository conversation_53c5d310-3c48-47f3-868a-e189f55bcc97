-- ABOUTME: Migration to add last_accessed_team field to profiles table for coach team persistence
-- This allows coaches to return to their last viewed team when logging back in

-- Add last_accessed_team column to profiles table
ALTER TABLE profiles
ADD COLUMN IF NOT EXISTS last_accessed_team UUID REFERENCES teams(team_id) ON DELETE SET NULL;

-- Add index for performance when looking up last accessed team
CREATE INDEX IF NOT EXISTS idx_profiles_last_accessed_team ON profiles(last_accessed_team);

-- Add comment to explain the column
COMMENT ON COLUMN profiles.last_accessed_team IS 'The last team the user (coach) accessed in the perform area. Used to restore context on login.';

-- <PERSON>reate function to update last accessed team
CREATE OR REPLACE FUNCTION update_last_accessed_team(p_user_id UUID, p_team_id UUID)
RETURNS VOID AS $$
BEGIN
  -- Verify the user has access to this team as a coach
  IF EXISTS (
    SELECT 1 FROM team_coaches 
    WHERE user_id = p_user_id 
    AND team_id = p_team_id 
    AND is_active = true
  ) THEN
    -- Update the profile
    UPDATE profiles 
    SET last_accessed_team = p_team_id
    WHERE id = p_user_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION update_last_accessed_team(UUID, UUID) TO authenticated;

-- Create RLS policy for users to update their own last_accessed_team
CREATE POLICY "Users can update their own last_accessed_team" ON profiles
  FOR UPDATE
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);