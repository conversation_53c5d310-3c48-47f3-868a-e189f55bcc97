-- SMS System Fix Script

-- Step 1: Ensure the sms_queue table exists with all required fields
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'sms_queue') THEN
        CREATE TABLE sms_queue (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          phone_number TEXT NOT NULL,
          recipient_name TEXT,
          message_body TEXT NOT NULL,
          status VARCHAR(50) DEFAULT 'pending',
          notification_type VARCHAR(50),
          team_id UUID REFERENCES teams(team_id),
          event_id UUID REFERENCES events(id),
          pre_evaluation_id UUID REFERENCES pre_evaluations(id),
          context JSONB DEFAULT '{}',
          scheduled_for TIMESTAMPTZ DEFAULT NOW(),
          sent_at TIMESTAMPTZ,
          processed BOOLEAN DEFAULT FALSE,
          processed_at TIMESTAMPTZ,
          error_message TEXT,
          retry_count INTEGER DEFAULT 0,
          max_attempts INTEGER DEFAULT 3,
          last_error TEXT,
          failed_at TIMESTAMPTZ,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        -- Create indexes
        CREATE INDEX idx_sms_queue_status ON sms_queue(status);
        CREATE INDEX idx_sms_queue_notification_type ON sms_queue(notification_type);
        CREATE INDEX idx_sms_queue_scheduled ON sms_queue(scheduled_for) WHERE status = 'pending';
        CREATE INDEX idx_sms_queue_team ON sms_queue(team_id) WHERE team_id IS NOT NULL;
        CREATE INDEX idx_sms_queue_event ON sms_queue(event_id) WHERE event_id IS NOT NULL;
        CREATE INDEX idx_sms_queue_pre_evaluation ON sms_queue(pre_evaluation_id) WHERE pre_evaluation_id IS NOT NULL;
        
        RAISE NOTICE 'Created sms_queue table';
    ELSE
        -- Ensure all required columns exist
        BEGIN
            ALTER TABLE sms_queue ADD COLUMN IF NOT EXISTS phone_number TEXT NOT NULL DEFAULT '';
            ALTER TABLE sms_queue ADD COLUMN IF NOT EXISTS recipient_name TEXT;
            ALTER TABLE sms_queue ADD COLUMN IF NOT EXISTS message_body TEXT NOT NULL DEFAULT '';
            ALTER TABLE sms_queue ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT 'pending';
            ALTER TABLE sms_queue ADD COLUMN IF NOT EXISTS scheduled_for TIMESTAMPTZ DEFAULT NOW();
            ALTER TABLE sms_queue ADD COLUMN IF NOT EXISTS retry_count INTEGER DEFAULT 0;
            ALTER TABLE sms_queue ADD COLUMN IF NOT EXISTS max_attempts INTEGER DEFAULT 3;
            
            RAISE NOTICE 'Updated sms_queue table structure';
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error updating sms_queue table: %', SQLERRM;
        END;
    END IF;
END$$;

-- Step 2: Create or replace the get_pending_sms_v2 function for the edge function
CREATE OR REPLACE FUNCTION get_pending_sms_v2(
  p_limit INT DEFAULT 10
) RETURNS TABLE (
  id UUID,
  phone TEXT,
  message TEXT,
  created_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    sq.id,
    sq.phone_number AS phone,
    sq.message_body AS message,
    sq.created_at
  FROM 
    sms_queue sq
  WHERE 
    sq.status = 'pending'
    AND sq.scheduled_for <= NOW()
    AND sq.retry_count < sq.max_attempts
  ORDER BY 
    sq.scheduled_for
  LIMIT 
    p_limit;
END;
$$ LANGUAGE plpgsql;

-- Step 3: Create or replace the mark_sms_sent function
CREATE OR REPLACE FUNCTION mark_sms_sent(
  p_sms_id UUID,
  p_success BOOLEAN,
  p_error TEXT DEFAULT NULL
) RETURNS VOID AS $$
BEGIN
  IF p_success THEN
    UPDATE sms_queue
    SET 
      status = 'sent',
      sent_at = NOW(),
      processed = TRUE,
      processed_at = NOW()
    WHERE id = p_sms_id;
  ELSE
    UPDATE sms_queue
    SET 
      status = 'failed',
      error_message = p_error,
      retry_count = retry_count + 1,
      last_error = p_error,
      failed_at = NOW()
    WHERE id = p_sms_id;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Ensure we have a unified trigger function
CREATE OR REPLACE FUNCTION trigger_pre_evaluation_sms()
RETURNS TRIGGER AS $$
DECLARE
  v_phone_number TEXT;
  v_player_name TEXT;
  v_event_name TEXT;
  v_sms_id UUID;
BEGIN
  -- Log the function call
  RAISE NOTICE 'trigger_pre_evaluation_sms called for pre-evaluation ID: %', NEW.id;

  -- Get the player's phone number from profiles
  SELECT phone, full_name INTO v_phone_number, v_player_name
  FROM profiles
  WHERE id = NEW.player_id;

  -- Get event details
  SELECT event_name INTO v_event_name
  FROM events
  WHERE id = NEW.event_id;

  -- Log the phone number found
  RAISE NOTICE 'Found phone number: % for player: %', v_phone_number, v_player_name;

  -- If no phone number is found, use the default
  IF v_phone_number IS NULL OR v_phone_number = '' THEN
    RAISE NOTICE 'No phone number found, using default';
    v_phone_number := '+************'; -- Default phone number as fallback
  END IF;

  -- Insert a record into the sms_queue table
  INSERT INTO sms_queue (
    phone_number,
    message_body,
    status,
    pre_evaluation_id,
    recipient_name,
    event_id,
    team_id,
    notification_type,
    scheduled_for
  ) VALUES (
    v_phone_number,
    format('Hi %s, please complete your pre-assessment for %s. Click here: https://app.shot.com/pre-eval/%s', 
           v_player_name, 
           v_event_name,
           NEW.id),
    'pending',
    NEW.id,
    v_player_name,
    NEW.event_id,
    NEW.team_id,
    'pre_evaluation_sms',
    NOW()
  ) RETURNING id INTO v_sms_id;

  -- Log successful queue addition
  RAISE NOTICE 'SMS notification queued for phone: % with ID: %', v_phone_number, v_sms_id;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 5: Drop existing triggers to avoid duplication
DROP TRIGGER IF EXISTS pre_evaluation_notifications_trigger ON pre_evaluations;
DROP TRIGGER IF EXISTS pre_evaluation_sms_trigger ON pre_evaluations;

-- Step 6: Create a single trigger for SMS notifications
CREATE TRIGGER pre_evaluation_sms_trigger
AFTER INSERT ON pre_evaluations
FOR EACH ROW
EXECUTE FUNCTION trigger_pre_evaluation_sms();

-- Step 7: Create a test function
CREATE OR REPLACE FUNCTION test_sms_system(
  p_phone TEXT DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
  v_notification_id UUID;
  v_phone VARCHAR(50) := '+************'; -- Replace with your test phone number
BEGIN
  -- Use provided phone number if available
  IF p_phone IS NOT NULL THEN
    v_phone := p_phone;
  END IF;

  -- Create a test SMS
  INSERT INTO sms_queue (
    phone_number,
    recipient_name,
    message_body,
    notification_type,
    status,
    scheduled_for
  ) VALUES (
    v_phone,
    'Test User',
    'This is a test SMS from SHOT app sent at ' || to_char(now(), 'HH24:MI:SS'),
    'test',
    'pending',
    NOW()
  ) RETURNING id INTO v_notification_id;
  
  RETURN jsonb_build_object(
    'success', true,
    'message', 'Test SMS queued successfully',
    'sms_id', v_notification_id
  );
END;
$$ LANGUAGE plpgsql;