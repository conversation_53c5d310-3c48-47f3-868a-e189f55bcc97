-- Modified cron job setup for Supabase compatibility

-- Check if cron jobs already exist and schedule them if not
DO $$
BEGIN
    -- TASK P6.1: Schedule SMS processing job
    IF NOT EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'process-sms-queue') THEN
        PERFORM cron.schedule(
            'process-sms-queue',
            '* * * * *',  -- Every minute
            'SELECT * FROM process_pending_sms(20)'
        );
        RAISE NOTICE 'Scheduled process-sms-queue job';
    ELSE
        RAISE NOTICE 'process-sms-queue job already exists';
    END IF;

    -- TASK P6.2: Schedule retry job
    IF NOT EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'retry-failed-sms') THEN
        PERFORM cron.schedule(
            'retry-failed-sms',
            '*/15 * * * *',  -- Every 15 minutes
            'SELECT retry_failed_sms(3, ''24 hours''::INTERVAL)'
        );
        RAISE NOTICE 'Scheduled retry-failed-sms job';
    ELSE
        RAISE NOTICE 'retry-failed-sms job already exists';
    END IF;

    -- TASK P6.3: Schedule cleanup job
    IF NOT EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'cleanup-sms-queue') THEN
        PERFORM cron.schedule(
            'cleanup-sms-queue',
            '0 3 * * *',  -- 3 AM every day
            'SELECT cleanup_sms_queue(30)'
        );
        RAISE NOTICE 'Scheduled cleanup-sms-queue job';
    ELSE
        RAISE NOTICE 'cleanup-sms-queue job already exists';
    END IF;
END$$;
