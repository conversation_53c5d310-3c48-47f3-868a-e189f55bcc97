-- Create the missing views needed for PlayerEvaluationHistory component
-- These views aggregate player evaluation data for display in the UI

-- Drop views if they exist (to allow re-running)
DROP VIEW IF EXISTS player_evaluation_summary_by_event CASCADE;
DROP VIEW IF EXISTS player_evaluation_history_view CASCADE;

-- Create player_evaluation_summary_by_event view
-- This view summarizes evaluations by player and event
CREATE OR REPLACE VIEW player_evaluation_summary_by_event AS
SELECT 
    pe.player_id,
    pe.event_id,
    p.full_name as player_name,
    p.full_name as player_display_name,
    p.avatar_url as player_avatar,
    e.name as event_name,
    e.event_type,
    e.start_datetime::date as event_date,
    t.name as team_name,
    COUNT(DISTINCT pe.id) as evaluation_count,
    
    -- Average ratings
    ROUND(AVG(CASE WHEN pe.evaluator_id != pe.player_id THEN pe.rating END)::numeric, 2) as avg_coach_rating,
    ROUND(AVG(CASE WHEN pe.evaluator_id = pe.player_id THEN pe.rating END)::numeric, 2) as avg_self_rating,
    
    -- Category breakdown as JSONB
    jsonb_build_object(
        'Technical', jsonb_build_object(
            'coach_avg', ROUND(AVG(CASE WHEN pe.category = 'TECHNICAL' AND pe.evaluator_id != pe.player_id THEN pe.rating END)::numeric, 2),
            'self_avg', ROUND(AVG(CASE WHEN pe.category = 'TECHNICAL' AND pe.evaluator_id = pe.player_id THEN pe.rating END)::numeric, 2),
            'count', COUNT(CASE WHEN pe.category = 'TECHNICAL' THEN 1 END)
        ),
        'Physical', jsonb_build_object(
            'coach_avg', ROUND(AVG(CASE WHEN pe.category = 'PHYSICAL' AND pe.evaluator_id != pe.player_id THEN pe.rating END)::numeric, 2),
            'self_avg', ROUND(AVG(CASE WHEN pe.category = 'PHYSICAL' AND pe.evaluator_id = pe.player_id THEN pe.rating END)::numeric, 2),
            'count', COUNT(CASE WHEN pe.category = 'PHYSICAL' THEN 1 END)
        ),
        'Psychological', jsonb_build_object(
            'coach_avg', ROUND(AVG(CASE WHEN pe.category = 'PSYCHOLOGICAL' AND pe.evaluator_id != pe.player_id THEN pe.rating END)::numeric, 2),
            'self_avg', ROUND(AVG(CASE WHEN pe.category = 'PSYCHOLOGICAL' AND pe.evaluator_id = pe.player_id THEN pe.rating END)::numeric, 2),
            'count', COUNT(CASE WHEN pe.category = 'PSYCHOLOGICAL' THEN 1 END)
        ),
        'Social', jsonb_build_object(
            'coach_avg', ROUND(AVG(CASE WHEN pe.category = 'SOCIAL' AND pe.evaluator_id != pe.player_id THEN pe.rating END)::numeric, 2),
            'self_avg', ROUND(AVG(CASE WHEN pe.category = 'SOCIAL' AND pe.evaluator_id = pe.player_id THEN pe.rating END)::numeric, 2),
            'count', COUNT(CASE WHEN pe.category = 'SOCIAL' THEN 1 END)
        ),
        'Positional', jsonb_build_object(
            'coach_avg', ROUND(AVG(CASE WHEN pe.category = 'POSITIONAL' AND pe.evaluator_id != pe.player_id THEN pe.rating END)::numeric, 2),
            'self_avg', ROUND(AVG(CASE WHEN pe.category = 'POSITIONAL' AND pe.evaluator_id = pe.player_id THEN pe.rating END)::numeric, 2),
            'count', COUNT(CASE WHEN pe.category = 'POSITIONAL' THEN 1 END)
        )
    ) as category_breakdown
FROM player_evaluations pe
JOIN profiles p ON pe.player_id = p.id
LEFT JOIN events e ON pe.event_id = e.id
LEFT JOIN teams t ON pe.team_id = t.id
WHERE pe.event_id IS NOT NULL
GROUP BY 
    pe.player_id, 
    pe.event_id, 
    p.full_name, 
    p.avatar_url,
    e.name,
    e.event_type,
    e.start_datetime,
    t.name;

-- Create player_evaluation_history_view
-- This view provides overall statistics for a player across all evaluations
CREATE OR REPLACE VIEW player_evaluation_history_view AS
SELECT 
    pe.player_id,
    p.full_name as player_name,
    p.full_name as player_display_name,
    p.avatar_url as player_avatar,
    COUNT(DISTINCT pe.id) as total_evaluations,
    COUNT(DISTINCT pe.event_id) as total_events,
    ROUND(AVG(CASE WHEN pe.evaluator_id != pe.player_id THEN pe.rating END)::numeric, 2) as overall_avg_coach_rating,
    ROUND(AVG(CASE WHEN pe.evaluator_id = pe.player_id THEN pe.rating END)::numeric, 2) as overall_avg_self_rating,
    MAX(pe.evaluation_date) as latest_evaluation_date,
    MIN(pe.evaluation_date) as first_evaluation_date
FROM player_evaluations pe
JOIN profiles p ON pe.player_id = p.id
GROUP BY pe.player_id, p.full_name, p.avatar_url;

-- Grant permissions
GRANT SELECT ON player_evaluation_summary_by_event TO authenticated;
GRANT SELECT ON player_evaluation_history_view TO authenticated;

-- Add RLS policies (views inherit from base tables, but we can add explicit policies)
ALTER VIEW player_evaluation_summary_by_event OWNER TO postgres;
ALTER VIEW player_evaluation_history_view OWNER TO postgres;

-- Add comment for documentation
COMMENT ON VIEW player_evaluation_summary_by_event IS 'Aggregates player evaluations by event, including category breakdowns and average ratings';
COMMENT ON VIEW player_evaluation_history_view IS 'Provides overall evaluation statistics for a player across all events';