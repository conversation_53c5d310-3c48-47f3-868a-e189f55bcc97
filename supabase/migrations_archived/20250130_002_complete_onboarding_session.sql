-- Migration: Create Onboarding Completion Functions
-- Description: Functions to handle completion of parent onboarding sessions
-- Phase: 2 - Parent Quick Onboarding System
-- Created: 2025-01-30

-- Function to complete onboarding session
CREATE OR REPLACE FUNCTION complete_onboarding_session(
    p_session_token TEXT,
    p_parent_profile_id UUID,
    p_child_profile_id UUID,
    p_child_sport_head_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_session_id UUID;
    v_duration INTEGER;
    v_session_data RECORD;
BEGIN
    -- Get session data
    SELECT 
        id, 
        EXTRACT(EPOCH FROM (NOW() - started_at))::INTEGER as duration,
        status,
        invite_code_id,
        team_id,
        event_id,
        pre_evaluation_id
    INTO v_session_data
    FROM parent_onboarding_sessions
    WHERE session_token = p_session_token
    FOR UPDATE;

    IF v_session_data.id IS NULL THEN
        RAISE EXCEPTION 'Invalid session token';
    END IF;

    IF v_session_data.status = 'completed' THEN
        RAISE EXCEPTION 'Session already completed';
    END IF;

    -- Update session
    UPDATE parent_onboarding_sessions
    SET 
        status = 'completed',
        completed_at = NOW(),
        parent_profile_id = p_parent_profile_id,
        child_profile_id = p_child_profile_id,
        child_sport_head_id = p_child_sport_head_id,
        total_duration_seconds = v_session_data.duration,
        updated_at = NOW()
    WHERE id = v_session_data.id;

    -- Mark quick profile as converted if exists
    UPDATE quick_profiles
    SET 
        profile_created = TRUE,
        profile_id = p_parent_profile_id
    WHERE onboarding_session_id = v_session_data.id;

    -- Update invite code usage if applicable
    IF v_session_data.invite_code_id IS NOT NULL THEN
        UPDATE invite_codes
        SET 
            use_count = use_count + 1,
            total_redemptions = total_redemptions + 1,
            updated_at = NOW()
        WHERE id = v_session_data.invite_code_id;
        
        -- Record redemption
        INSERT INTO invite_code_redemptions (
            invite_code,
            redeemed_by,
            redemption_metadata
        )
        SELECT 
            code,
            p_parent_profile_id,
            jsonb_build_object(
                'child_id', p_child_profile_id,
                'sport_head_id', p_child_sport_head_id,
                'session_id', v_session_data.id,
                'method', 'parent_onboarding'
            )
        FROM invite_codes
        WHERE id = v_session_data.invite_code_id;
    END IF;

    -- If there's a pre-evaluation request, link it
    IF v_session_data.pre_evaluation_id IS NOT NULL THEN
        -- This will be implemented when pre_evaluations table exists
        -- UPDATE pre_evaluations 
        -- SET player_id = p_child_profile_id,
        --     sport_head_id = p_child_sport_head_id
        -- WHERE id = v_session_data.pre_evaluation_id;
        NULL; -- Placeholder
    END IF;

    -- Log completion analytics
    INSERT INTO onboarding_analytics (
        session_token,
        session_id,
        event_type,
        duration_seconds,
        metadata
    ) VALUES (
        p_session_token,
        v_session_data.id,
        'completed',
        v_session_data.duration,
        jsonb_build_object(
            'parent_id', p_parent_profile_id,
            'child_id', p_child_profile_id,
            'sport_head_id', p_child_sport_head_id
        )
    );

    -- Send completion notification (would integrate with notification system)
    -- For now, just log it
    INSERT INTO activities (
        user_id,
        activity_type,
        target_id,
        target_type,
        metadata
    ) VALUES (
        p_parent_profile_id,
        'profile_updated',
        p_parent_profile_id,
        'profile',
        jsonb_build_object(
            'action', 'onboarding_completed',
            'child_added', p_child_profile_id,
            'duration_seconds', v_session_data.duration
        )
    );

    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error
        RAISE LOG 'Error in complete_onboarding_session: %', SQLERRM;
        
        -- Record failure in analytics
        INSERT INTO onboarding_analytics (
            session_token,
            event_type,
            error_type,
            error_message,
            metadata
        ) VALUES (
            p_session_token,
            'error_shown',
            'completion_failed',
            SQLERRM,
            jsonb_build_object(
                'parent_id', p_parent_profile_id,
                'child_id', p_child_profile_id
            )
        );
        
        RAISE;
END;
$$;

-- Function to handle session abandonment
CREATE OR REPLACE FUNCTION abandon_onboarding_session(
    p_session_token TEXT,
    p_step VARCHAR(50),
    p_reason TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_session_id UUID;
    v_duration INTEGER;
BEGIN
    -- Update session
    UPDATE parent_onboarding_sessions
    SET 
        status = 'abandoned',
        abandoned_at = NOW(),
        abandoned_at_step = p_step,
        abandonment_reason = p_reason,
        total_duration_seconds = EXTRACT(EPOCH FROM (NOW() - started_at))::INTEGER,
        updated_at = NOW()
    WHERE session_token = p_session_token
    AND status NOT IN ('completed', 'abandoned')
    RETURNING id, total_duration_seconds INTO v_session_id, v_duration;

    IF v_session_id IS NULL THEN
        RETURN FALSE;
    END IF;

    -- Log abandonment analytics
    INSERT INTO onboarding_analytics (
        session_token,
        session_id,
        event_type,
        step_name,
        duration_seconds,
        metadata
    ) VALUES (
        p_session_token,
        v_session_id,
        'abandoned',
        p_step,
        v_duration,
        jsonb_build_object(
            'reason', p_reason,
            'step', p_step
        )
    );

    RETURN TRUE;
END;
$$;

-- Function to get or create onboarding session
CREATE OR REPLACE FUNCTION get_or_create_onboarding_session(
    p_invite_code TEXT DEFAULT NULL,
    p_coach_id UUID DEFAULT NULL,
    p_team_id UUID DEFAULT NULL,
    p_event_id UUID DEFAULT NULL
)
RETURNS TABLE (
    session_token TEXT,
    session_id UUID,
    status VARCHAR(50),
    existing_session BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_session RECORD;
    v_invite_data RECORD;
    v_new_token TEXT;
BEGIN
    -- If invite code provided, look up context
    IF p_invite_code IS NOT NULL THEN
        SELECT 
            id,
            owner_id,
            team_id
        INTO v_invite_data
        FROM invite_codes
        WHERE code = p_invite_code
        AND is_valid = TRUE
        AND (expires_at IS NULL OR expires_at > NOW());
        
        IF v_invite_data.id IS NULL THEN
            RAISE EXCEPTION 'Invalid or expired invite code';
        END IF;
        
        -- Use invite code data if not provided
        p_coach_id := COALESCE(p_coach_id, v_invite_data.owner_id);
        p_team_id := COALESCE(p_team_id, v_invite_data.team_id);
    END IF;

    -- Generate new token
    v_new_token := encode(gen_random_bytes(32), 'hex');

    -- Create new session
    INSERT INTO parent_onboarding_sessions (
        session_token,
        invite_code_id,
        inviting_coach_id,
        team_id,
        event_id,
        status,
        variant_id
    ) VALUES (
        v_new_token,
        v_invite_data.id,
        p_coach_id,
        p_team_id,
        p_event_id,
        'started',
        'control' -- Can be randomized for A/B testing
    )
    RETURNING 
        session_token,
        id as session_id,
        status,
        FALSE as existing_session
    INTO v_session;

    RETURN QUERY SELECT 
        v_session.session_token,
        v_session.session_id,
        v_session.status,
        v_session.existing_session;
END;
$$;

-- Helper function to validate session
CREATE OR REPLACE FUNCTION validate_onboarding_session(
    p_session_token TEXT
)
RETURNS TABLE (
    is_valid BOOLEAN,
    session_id UUID,
    status VARCHAR(50),
    started_at TIMESTAMPTZ,
    parent_email VARCHAR(255)
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        CASE 
            WHEN s.id IS NOT NULL AND s.status NOT IN ('completed', 'abandoned') THEN TRUE
            ELSE FALSE
        END as is_valid,
        s.id as session_id,
        s.status,
        s.started_at,
        s.parent_email
    FROM parent_onboarding_sessions s
    WHERE s.session_token = p_session_token;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION complete_onboarding_session TO authenticated, anon;
GRANT EXECUTE ON FUNCTION abandon_onboarding_session TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_or_create_onboarding_session TO authenticated, anon;
GRANT EXECUTE ON FUNCTION validate_onboarding_session TO authenticated, anon;

-- Add comments
COMMENT ON FUNCTION complete_onboarding_session IS 'Marks an onboarding session as complete and updates all related records';
COMMENT ON FUNCTION abandon_onboarding_session IS 'Records session abandonment for analytics';
COMMENT ON FUNCTION get_or_create_onboarding_session IS 'Creates a new onboarding session with optional context from invite code';
COMMENT ON FUNCTION validate_onboarding_session IS 'Checks if a session token is valid and returns session details';
