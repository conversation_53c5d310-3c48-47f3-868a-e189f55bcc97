-- ABOUTME: Comprehensive commerce schema setup ensuring all tables exist
-- This migration creates all required commerce tables if they don't already exist

-- Create commerce schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS commerce;

-- Grant permissions to the commerce schema
GRANT USAGE ON SCHEMA commerce TO authenticated, anon;
GRANT CREATE ON SCHEMA commerce TO authenticated;

-- =====================================================
-- CART_SESSIONS TABLE (Required for cart functionality)
-- =====================================================
CREATE TABLE IF NOT EXISTS commerce.cart_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id TEXT UNIQUE NOT NULL,
    profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    items JSONB DEFAULT '[]'::jsonb NOT NULL,
    subtotal DECIMAL(10,2) DEFAULT 0 NOT NULL,
    tax_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    shipping_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    discount_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    grand_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    coupon_codes TEXT[] DEFAULT ARRAY[]::TEXT[],
    shipping_address JSONB,
    billing_address JSONB,
    selected_shipping_option JSONB,
    notes TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days') NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for cart_sessions
CREATE INDEX IF NOT EXISTS idx_cart_sessions_session_id ON commerce.cart_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_cart_sessions_profile_id ON commerce.cart_sessions(profile_id);
CREATE INDEX IF NOT EXISTS idx_cart_sessions_last_activity ON commerce.cart_sessions(last_activity_at);
CREATE INDEX IF NOT EXISTS idx_cart_sessions_expires_at ON commerce.cart_sessions(expires_at);

-- =====================================================
-- PRODUCT_CACHE TABLE (For performance optimization)
-- =====================================================
CREATE TABLE IF NOT EXISTS commerce.product_cache (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    bc_product_id INTEGER NOT NULL UNIQUE,
    sku TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    sale_price DECIMAL(10, 2),
    cost_price DECIMAL(10, 2),
    retail_price DECIMAL(10, 2),
    weight DECIMAL(10, 2),
    categories JSONB DEFAULT '[]'::jsonb,
    brand_id INTEGER,
    brand_name TEXT,
    images JSONB DEFAULT '[]'::jsonb,
    variants JSONB DEFAULT '[]'::jsonb,
    custom_fields JSONB DEFAULT '{}'::jsonb,
    inventory_level INTEGER DEFAULT 0,
    inventory_tracking TEXT,
    is_visible BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    meta_keywords TEXT[],
    search_keywords TEXT,
    availability TEXT,
    condition TEXT DEFAULT 'new',
    cache_ttl_minutes INTEGER DEFAULT 5,
    cached_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for product_cache
CREATE INDEX IF NOT EXISTS idx_product_cache_bc_id ON commerce.product_cache(bc_product_id);
CREATE INDEX IF NOT EXISTS idx_product_cache_sku ON commerce.product_cache(sku);
CREATE INDEX IF NOT EXISTS idx_product_cache_visible ON commerce.product_cache(is_visible);
CREATE INDEX IF NOT EXISTS idx_product_cache_cached_at ON commerce.product_cache(cached_at);

-- =====================================================
-- GUARDIAN_SETTINGS TABLE (For purchase approvals)
-- =====================================================
CREATE TABLE IF NOT EXISTS commerce.guardian_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    child_profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    guardian_profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    purchase_approval_required BOOLEAN DEFAULT true,
    max_purchase_amount DECIMAL(10,2),
    allowed_categories INTEGER[],
    blocked_categories INTEGER[],
    notification_preferences JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(child_profile_id, guardian_profile_id)
);

-- Create indexes for guardian_settings
CREATE INDEX IF NOT EXISTS idx_guardian_settings_child ON commerce.guardian_settings(child_profile_id);
CREATE INDEX IF NOT EXISTS idx_guardian_settings_guardian ON commerce.guardian_settings(guardian_profile_id);

-- =====================================================
-- PURCHASE_APPROVALS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS commerce.purchase_approvals (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    child_profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    guardian_profile_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    cart_data JSONB NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'denied', 'expired')),
    guardian_notes TEXT,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responded_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '48 hours'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for purchase_approvals
CREATE INDEX IF NOT EXISTS idx_purchase_approvals_child ON commerce.purchase_approvals(child_profile_id);
CREATE INDEX IF NOT EXISTS idx_purchase_approvals_guardian ON commerce.purchase_approvals(guardian_profile_id);
CREATE INDEX IF NOT EXISTS idx_purchase_approvals_status ON commerce.purchase_approvals(status);
CREATE INDEX IF NOT EXISTS idx_purchase_approvals_expires ON commerce.purchase_approvals(expires_at);

-- =====================================================
-- BC_CUSTOMER_MAPPINGS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS commerce.bc_customer_mappings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    bc_customer_id INTEGER NOT NULL UNIQUE,
    bc_email TEXT NOT NULL,
    sync_status TEXT DEFAULT 'active' CHECK (sync_status IN ('active', 'inactive', 'error')),
    last_sync_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(profile_id)
);

-- Create indexes for bc_customer_mappings
CREATE INDEX IF NOT EXISTS idx_bc_customer_mappings_profile_id ON commerce.bc_customer_mappings(profile_id);
CREATE INDEX IF NOT EXISTS idx_bc_customer_mappings_bc_customer_id ON commerce.bc_customer_mappings(bc_customer_id);

-- =====================================================
-- ORDER_SYNC TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS commerce.order_sync (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    bc_order_id INTEGER NOT NULL UNIQUE,
    profile_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    order_number TEXT NOT NULL,
    status TEXT NOT NULL,
    subtotal DECIMAL(10, 2) NOT NULL,
    tax_total DECIMAL(10, 2) DEFAULT 0,
    shipping_total DECIMAL(10, 2) DEFAULT 0,
    discount_total DECIMAL(10, 2) DEFAULT 0,
    grand_total DECIMAL(10, 2) NOT NULL,
    items_total INTEGER NOT NULL,
    payment_method TEXT,
    payment_status TEXT,
    shipping_method TEXT,
    tracking_numbers TEXT[],
    currency_code TEXT DEFAULT 'GBP',
    customer_message TEXT,
    staff_notes TEXT,
    order_source TEXT,
    bc_customer_id INTEGER,
    billing_address JSONB NOT NULL,
    shipping_address JSONB,
    products JSONB NOT NULL DEFAULT '[]'::jsonb,
    custom_status TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    ordered_at TIMESTAMP WITH TIME ZONE NOT NULL,
    shipped_at TIMESTAMP WITH TIME ZONE,
    synced_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for order_sync
CREATE INDEX IF NOT EXISTS idx_order_sync_bc_order_id ON commerce.order_sync(bc_order_id);
CREATE INDEX IF NOT EXISTS idx_order_sync_profile_id ON commerce.order_sync(profile_id);
CREATE INDEX IF NOT EXISTS idx_order_sync_order_number ON commerce.order_sync(order_number);
CREATE INDEX IF NOT EXISTS idx_order_sync_status ON commerce.order_sync(status);
CREATE INDEX IF NOT EXISTS idx_order_sync_ordered_at ON commerce.order_sync(ordered_at);

-- =====================================================
-- UPDATE FUNCTION FOR TIMESTAMPS
-- =====================================================
CREATE OR REPLACE FUNCTION commerce.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ADD TRIGGERS FOR UPDATED_AT
-- =====================================================
-- Cart sessions
DROP TRIGGER IF EXISTS update_cart_sessions_updated_at ON commerce.cart_sessions;
CREATE TRIGGER update_cart_sessions_updated_at 
    BEFORE UPDATE ON commerce.cart_sessions 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

-- Product cache
DROP TRIGGER IF EXISTS update_product_cache_updated_at ON commerce.product_cache;
CREATE TRIGGER update_product_cache_updated_at 
    BEFORE UPDATE ON commerce.product_cache 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

-- Guardian settings
DROP TRIGGER IF EXISTS update_guardian_settings_updated_at ON commerce.guardian_settings;
CREATE TRIGGER update_guardian_settings_updated_at 
    BEFORE UPDATE ON commerce.guardian_settings 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

-- Purchase approvals
DROP TRIGGER IF EXISTS update_purchase_approvals_updated_at ON commerce.purchase_approvals;
CREATE TRIGGER update_purchase_approvals_updated_at 
    BEFORE UPDATE ON commerce.purchase_approvals 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

-- BC customer mappings
DROP TRIGGER IF EXISTS update_bc_customer_mappings_updated_at ON commerce.bc_customer_mappings;
CREATE TRIGGER update_bc_customer_mappings_updated_at 
    BEFORE UPDATE ON commerce.bc_customer_mappings 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

-- Order sync
DROP TRIGGER IF EXISTS update_order_sync_updated_at ON commerce.order_sync;
CREATE TRIGGER update_order_sync_updated_at 
    BEFORE UPDATE ON commerce.order_sync 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

-- =====================================================
-- RLS POLICIES FOR CART_SESSIONS
-- =====================================================
ALTER TABLE commerce.cart_sessions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view own carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can create carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can update own carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can delete own carts" ON commerce.cart_sessions;

-- Create policies
CREATE POLICY "Users can view own carts" ON commerce.cart_sessions
    FOR SELECT
    USING (
        auth.uid() = profile_id 
        OR profile_id IS NULL  -- Allow viewing guest carts by session
    );

CREATE POLICY "Users can create carts" ON commerce.cart_sessions
    FOR INSERT
    WITH CHECK (
        auth.uid() = profile_id 
        OR profile_id IS NULL  -- Allow creating guest carts
    );

CREATE POLICY "Users can update own carts" ON commerce.cart_sessions
    FOR UPDATE
    USING (
        auth.uid() = profile_id 
        OR profile_id IS NULL  -- Allow updating guest carts
    );

CREATE POLICY "Users can delete own carts" ON commerce.cart_sessions
    FOR DELETE
    USING (
        auth.uid() = profile_id 
        OR profile_id IS NULL  -- Allow deleting guest carts
    );

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================
-- Grant select on all tables
GRANT SELECT ON ALL TABLES IN SCHEMA commerce TO anon, authenticated;

-- Grant full access to cart_sessions for both anon and authenticated
GRANT INSERT, UPDATE, DELETE ON commerce.cart_sessions TO anon, authenticated;

-- Grant insert/update on purchase_approvals for authenticated users
GRANT INSERT, UPDATE ON commerce.purchase_approvals TO authenticated;

-- Grant usage on all sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA commerce TO anon, authenticated;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== Commerce Schema Setup Complete ===';
    RAISE NOTICE '';
    RAISE NOTICE 'Tables created/verified:';
    RAISE NOTICE '  ✓ commerce.cart_sessions - For cart persistence';
    RAISE NOTICE '  ✓ commerce.product_cache - For product caching';
    RAISE NOTICE '  ✓ commerce.guardian_settings - For purchase approvals';
    RAISE NOTICE '  ✓ commerce.purchase_approvals - For approval requests';
    RAISE NOTICE '  ✓ commerce.bc_customer_mappings - For BigCommerce sync';
    RAISE NOTICE '  ✓ commerce.order_sync - For order management';
    RAISE NOTICE '';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '  1. Run this migration in Supabase';
    RAISE NOTICE '  2. Test cart functionality';
    RAISE NOTICE '  3. Verify RLS policies are working';
    RAISE NOTICE '';
END $$;