-- Create views in public schema to access content schema tables for Supabase v1 compatibility

-- Drop existing views if they exist
DROP VIEW IF EXISTS public.pulse_articles CASCADE;
DROP VIEW IF EXISTS public.pulse_sync_log CASCADE;
DROP VIEW IF EXISTS public.api_keys CASCADE;
DROP VIEW IF EXISTS public.widget_configurations CASCADE;

-- <PERSON><PERSON> views that reference content schema tables
CREATE VIEW public.pulse_articles AS
SELECT * FROM content.pulse_articles;

CREATE VIEW public.pulse_sync_log AS
SELECT * FROM content.pulse_sync_log;

CREATE VIEW public.api_keys AS
SELECT * FROM content.api_keys;

CREATE VIEW public.widget_configurations AS
SELECT * FROM content.widget_configurations;

-- Create function wrappers in public schema
CREATE OR REPLACE FUNCTION public.check_sync_health()
RETURNS TABLE (
  alert_level TEXT,
  message TEXT,
  details JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY SELECT * FROM content.check_sync_health();
END;
$$;

CREATE OR REPLACE FUNCTION public.get_sync_stats(time_period INTERVAL DEFAULT INTERVAL '24 hours')
RETURNS TABLE (
  total_syncs BIGINT,
  successful_syncs BIGINT,
  failed_syncs BIGINT,
  success_rate NUMERIC,
  total_articles_synced BIGINT,
  avg_sync_duration_seconds NUMERIC,
  last_sync_at TIMESTAMP,
  last_sync_status TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY SELECT * FROM content.get_sync_stats(time_period);
END;
$$;

CREATE OR REPLACE FUNCTION public.get_latest_sync_status()
RETURNS TABLE (
  status TEXT,
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  articles_synced INTEGER,
  error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY SELECT * FROM content.get_latest_sync_status();
END;
$$;

CREATE OR REPLACE FUNCTION public.search_pulse_articles(search_query TEXT)
RETURNS SETOF content.pulse_articles
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY SELECT * FROM content.search_pulse_articles(search_query);
END;
$$;

-- Grant appropriate permissions
GRANT SELECT ON public.pulse_articles TO anon, authenticated;
GRANT SELECT ON public.pulse_sync_log TO authenticated;
GRANT SELECT ON public.widget_configurations TO anon, authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.check_sync_health() TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.get_sync_stats(INTERVAL) TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.get_latest_sync_status() TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.search_pulse_articles(TEXT) TO anon, authenticated;

-- Create triggers to handle INSERT/UPDATE/DELETE operations through views
-- For pulse_articles
CREATE OR REPLACE FUNCTION public.pulse_articles_insert()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO content.pulse_articles VALUES (NEW.*);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER pulse_articles_insert_trigger
INSTEAD OF INSERT ON public.pulse_articles
FOR EACH ROW EXECUTE FUNCTION public.pulse_articles_insert();

CREATE OR REPLACE FUNCTION public.pulse_articles_update()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE content.pulse_articles SET
    external_id = NEW.external_id,
    title = NEW.title,
    pillar = NEW.pillar,
    desk = NEW.desk,
    desk_emoji = NEW.desk_emoji,
    narrative = NEW.narrative,
    url = NEW.url,
    source = NEW.source,
    pub_date = NEW.pub_date,
    updated_at = NEW.updated_at,
    sync_status = NEW.sync_status,
    metadata = NEW.metadata,
    is_public = NEW.is_public,
    api_visibility = NEW.api_visibility
  WHERE id = OLD.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER pulse_articles_update_trigger
INSTEAD OF UPDATE ON public.pulse_articles
FOR EACH ROW EXECUTE FUNCTION public.pulse_articles_update();

CREATE OR REPLACE FUNCTION public.pulse_articles_delete()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM content.pulse_articles WHERE id = OLD.id;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER pulse_articles_delete_trigger
INSTEAD OF DELETE ON public.pulse_articles
FOR EACH ROW EXECUTE FUNCTION public.pulse_articles_delete();

-- Similar triggers for pulse_sync_log
CREATE OR REPLACE FUNCTION public.pulse_sync_log_insert()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO content.pulse_sync_log VALUES (NEW.*);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER pulse_sync_log_insert_trigger
INSTEAD OF INSERT ON public.pulse_sync_log
FOR EACH ROW EXECUTE FUNCTION public.pulse_sync_log_insert();

-- Add comment for documentation
COMMENT ON VIEW public.pulse_articles IS 'View to access content.pulse_articles for Supabase v1 compatibility';
COMMENT ON VIEW public.pulse_sync_log IS 'View to access content.pulse_sync_log for Supabase v1 compatibility';