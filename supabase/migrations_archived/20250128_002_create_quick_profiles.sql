-- Migration: Create Quick Profiles Table
-- Description: Temporary storage for parent data during quick onboarding before full account creation
-- Phase: 2 - Parent Quick Onboarding System
-- Created: 2025-01-28

-- Create quick_profiles table
CREATE TABLE IF NOT EXISTS quick_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    temporary_password TEXT,
    
    -- Minimal required data
    full_name VARCHAR(255) NOT NULL,
    relationship_to_child VARCHAR(50) DEFAULT 'parent',
    
    -- Conversion tracking
    profile_created BOOLEAN DEFAULT FALSE,
    profile_id UUID REFERENCES profiles(id),
    
    -- Deferred verification
    email_verification_token TEXT DEFAULT encode(gen_random_bytes(32), 'hex'),
    email_verified BOOLEAN DEFAULT FALSE,
    email_verified_at TIMESTAMPTZ,
    
    -- Session reference
    onboarding_session_id UUID REFERENCES parent_onboarding_sessions(id),
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ DEFAULT NOW() + INTERVAL '7 days'
);

-- <PERSON><PERSON> indexes
CREATE INDEX idx_quick_profiles_email ON quick_profiles(email);
CREATE INDEX idx_quick_profiles_token ON quick_profiles(email_verification_token);
CREATE INDEX idx_quick_profiles_expires ON quick_profiles(expires_at) 
    WHERE profile_created = FALSE;
CREATE INDEX idx_quick_profiles_session ON quick_profiles(onboarding_session_id);

-- Add comments
COMMENT ON TABLE quick_profiles IS 'Temporary storage for parent accounts during quick onboarding';
COMMENT ON COLUMN quick_profiles.email IS 'Parent email address - will be used for account creation';
COMMENT ON COLUMN quick_profiles.temporary_password IS 'Encrypted password stored temporarily until account creation';
COMMENT ON COLUMN quick_profiles.profile_created IS 'Flag indicating if the full profile has been created';
COMMENT ON COLUMN quick_profiles.email_verification_token IS 'Token for deferred email verification';
COMMENT ON COLUMN quick_profiles.expires_at IS 'Auto-cleanup after 7 days if not converted';

-- Create cleanup function for expired records
CREATE OR REPLACE FUNCTION cleanup_expired_quick_profiles()
RETURNS void AS $$
BEGIN
    DELETE FROM quick_profiles
    WHERE expires_at < NOW()
    AND profile_created = FALSE;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to clean up expired profiles (requires pg_cron extension)
-- This can be set up separately in Supabase dashboard or via API
-- SELECT cron.schedule('cleanup-quick-profiles', '0 2 * * *', 'SELECT cleanup_expired_quick_profiles()');

-- Enable Row Level Security
ALTER TABLE quick_profiles ENABLE ROW LEVEL SECURITY;
