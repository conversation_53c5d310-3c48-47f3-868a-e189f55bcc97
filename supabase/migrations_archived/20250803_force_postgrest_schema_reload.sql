-- ABOUTME: Forces PostgREST to reload its schema cache to recognize cart_sessions
-- This resolves 406 errors when PostgREST's cache is stale

-- Method 1: Send reload notification
NOTIFY pgrst, 'reload schema';
NOTIFY pgrst, 'reload config';

-- Method 2: Touch the schema to force detection
COMMENT ON TABLE commerce.cart_sessions IS 'Shopping cart sessions - updated at ' || now()::text;
COMMENT ON VIEW public.cart_sessions IS 'Public view of cart sessions - updated at ' || now()::text;

-- Method 3: Create a dummy function to trigger schema change detection
CREATE OR REPLACE FUNCTION public.postgrest_schema_reload_trigger()
RETURNS void
LANGUAGE sql
AS $$
  SELECT 1;
$$;

-- Drop it immediately
DROP FUNCTION public.postgrest_schema_reload_trigger();

-- Method 4: Grant permissions again (sometimes helps)
GRANT ALL ON public.cart_sessions TO anon;
GRANT ALL ON public.cart_sessions TO authenticated;
GRANT USAGE ON SCHEMA public TO anon;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Method 5: Ensure the view is properly defined
DROP VIEW IF EXISTS public.cart_sessions CASCADE;
CREATE VIEW public.cart_sessions AS
SELECT 
  id,
  session_id,
  profile_id,
  items,
  subtotal,
  tax_total,
  shipping_total,
  discount_total,
  grand_total,
  coupon_codes,
  shipping_address,
  billing_address,
  selected_shipping_option,
  notes,
  metadata,
  last_activity_at,
  expires_at,
  created_at,
  updated_at
FROM commerce.cart_sessions;

-- Re-grant permissions on the recreated view
GRANT ALL ON public.cart_sessions TO anon;
GRANT ALL ON public.cart_sessions TO authenticated;
GRANT ALL ON public.cart_sessions TO service_role;

-- Method 6: Create explicit RLS policies if they don't exist
DO $$
BEGIN
  -- Drop existing policies to recreate them
  DROP POLICY IF EXISTS "Enable all for anon" ON commerce.cart_sessions;
  DROP POLICY IF EXISTS "Enable all for authenticated" ON commerce.cart_sessions;
  
  -- Create new permissive policies
  CREATE POLICY "Enable all for anon" ON commerce.cart_sessions
    FOR ALL TO anon
    USING (true)
    WITH CHECK (true);
    
  CREATE POLICY "Enable all for authenticated" ON commerce.cart_sessions
    FOR ALL TO authenticated  
    USING (true)
    WITH CHECK (true);
END $$;

-- Final notification
NOTIFY pgrst, 'reload schema';