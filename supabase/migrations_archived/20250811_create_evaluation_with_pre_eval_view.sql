-- ============================================================================
-- EVALUATION WITH PRE-EVALUATION VIEW
-- ============================================================================
-- Creates a view that combines coach evaluations with player pre-evaluations
-- for the evaluation page to show both in different colors
-- ============================================================================

DROP VIEW IF EXISTS evaluation_with_pre_eval CASCADE;

CREATE OR REPLACE VIEW evaluation_with_pre_eval AS
WITH pre_eval_data AS (
  -- Extract pre-evaluation ratings from the responses JSONB
  SELECT 
    pe.event_id,
    pe.player_id,
    pe.team_id,
    pe.status as pre_eval_status,
    pe.completed_at as pre_eval_completed_at,
    pe.confidence_level,
    pe.mood_rating,
    pe.goals_text,
    pe.concerns_text,
    pe.responses,
    pe.questions_answered,
    pe.total_questions,
    pe.completion_percentage
  FROM pre_evaluations pe
  WHERE pe.status IN ('completed', 'submitted')
),
coach_eval_data AS (
  -- Get the latest coach evaluation for each player/event/category
  SELECT DISTINCT ON (player_id, event_id, category, area)
    player_id,
    event_id,
    team_id,
    category,
    area,
    rating as coach_rating,
    notes as coach_notes,
    evaluation_date as coach_eval_date,
    evaluator_id as coach_id
  FROM player_evaluations
  WHERE evaluator_id != player_id  -- Only coach evaluations
  ORDER BY player_id, event_id, category, area, evaluation_date DESC
)
SELECT 
  -- Event and player info
  e.id as event_id,
  e.name as event_name,
  e.start_datetime as event_date,
  p.id as player_id,
  p.full_name as player_name,
  p.avatar_url as player_avatar,
  ep.participant_position as player_position,
  
  -- Coach evaluation data
  ced.category,
  ced.area,
  ced.coach_rating,
  ced.coach_notes,
  ced.coach_eval_date,
  ced.coach_id,
  
  -- Pre-evaluation data
  ped.pre_eval_status,
  ped.pre_eval_completed_at,
  ped.confidence_level as pre_eval_confidence,
  ped.mood_rating as pre_eval_mood,
  ped.goals_text as pre_eval_goals,
  ped.concerns_text as pre_eval_concerns,
  ped.responses as pre_eval_responses,
  ped.completion_percentage as pre_eval_completion_percentage,
  
  -- Extract pre-evaluation rating from responses if available
  -- This assumes responses are stored in a JSONB format with category/area keys
  CASE 
    WHEN ped.responses IS NOT NULL AND ced.category IS NOT NULL AND ced.area IS NOT NULL
    THEN (ped.responses -> ced.category -> ced.area ->> 'rating')::INTEGER
    ELSE NULL
  END as pre_eval_rating,
  
  -- Evaluation status flags
  CASE WHEN ced.coach_rating IS NOT NULL THEN true ELSE false END as has_coach_eval,
  CASE WHEN ped.pre_eval_status = 'completed' THEN true ELSE false END as has_pre_eval,
  
  -- Display color hint (for UI)
  CASE 
    WHEN ced.coach_rating IS NOT NULL THEN 'green'  -- Coach evaluation exists
    WHEN ped.pre_eval_status = 'completed' THEN 'orange'  -- Only pre-evaluation exists
    ELSE 'gray'  -- No evaluation
  END as display_color

FROM events e
INNER JOIN event_participants ep ON e.id = ep.event_id
INNER JOIN profiles p ON ep.participant_id = p.id
LEFT JOIN pre_eval_data ped ON ped.event_id = e.id AND ped.player_id = p.id
LEFT JOIN coach_eval_data ced ON ced.event_id = e.id AND ced.player_id = p.id
WHERE ep.invitation_status = 'attended';

-- Grant permissions
GRANT SELECT ON evaluation_with_pre_eval TO authenticated;

-- Add comment for documentation
COMMENT ON VIEW evaluation_with_pre_eval IS 'Combines coach evaluations with player pre-evaluations for display on the evaluation page. Shows coach evaluations in green and pre-evaluations in orange.';

-- Create an index on pre_evaluations for better join performance
CREATE INDEX IF NOT EXISTS idx_pre_evaluations_event_player ON pre_evaluations(event_id, player_id);
CREATE INDEX IF NOT EXISTS idx_pre_evaluations_status ON pre_evaluations(status);