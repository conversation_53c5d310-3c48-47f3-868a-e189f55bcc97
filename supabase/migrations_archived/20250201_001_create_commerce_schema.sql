-- ABOUTME: Commerce schema for BigCommerce integration with SHOT app
-- Creates all necessary tables for e-commerce functionality including cart, orders, products, and advanced features

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- For text search

-- =====================================================
-- BIGCOMMERCE CUSTOMER MAPPING
-- =====================================================
-- Links SHOT users to BigCommerce customers
CREATE TABLE IF NOT EXISTS public.bc_customer_mappings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    bc_customer_id INTEGER NOT NULL UNIQUE,
    bc_email TEXT NOT NULL,
    sync_status TEXT DEFAULT 'active' CHECK (sync_status IN ('active', 'inactive', 'error')),
    last_sync_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(profile_id)
);

CREATE INDEX idx_bc_customer_mappings_profile_id ON public.bc_customer_mappings(profile_id);
CREATE INDEX idx_bc_customer_mappings_bc_customer_id ON public.bc_customer_mappings(bc_customer_id);

-- =====================================================
-- PRODUCT CACHE
-- =====================================================
-- Local cache of BigCommerce products for performance
CREATE TABLE IF NOT EXISTS public.product_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bc_product_id INTEGER NOT NULL UNIQUE,
    sku TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    sale_price DECIMAL(10, 2),
    cost_price DECIMAL(10, 2),
    retail_price DECIMAL(10, 2),
    weight DECIMAL(10, 2),
    categories JSONB DEFAULT '[]'::jsonb,
    brand_id INTEGER,
    brand_name TEXT,
    images JSONB DEFAULT '[]'::jsonb,
    variants JSONB DEFAULT '[]'::jsonb,
    custom_fields JSONB DEFAULT '{}'::jsonb,
    inventory_level INTEGER DEFAULT 0,
    inventory_tracking TEXT,
    is_visible BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    meta_keywords TEXT[],
    search_keywords TEXT,
    availability TEXT,
    condition TEXT DEFAULT 'new',
    cache_ttl_minutes INTEGER DEFAULT 5,
    cached_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_product_cache_bc_id ON public.product_cache(bc_product_id);
CREATE INDEX idx_product_cache_sku ON public.product_cache(sku);
CREATE INDEX idx_product_cache_visible ON public.product_cache(is_visible);
CREATE INDEX idx_product_cache_featured ON public.product_cache(is_featured);
CREATE INDEX idx_product_cache_search ON public.product_cache USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '') || ' ' || COALESCE(search_keywords, '')));

-- =====================================================
-- CART SESSIONS
-- =====================================================
-- Persistent shopping cart storage
CREATE TABLE IF NOT EXISTS public.cart_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL UNIQUE,
    bc_cart_id TEXT,
    items JSONB DEFAULT '[]'::jsonb,
    subtotal DECIMAL(10, 2) DEFAULT 0,
    tax_total DECIMAL(10, 2) DEFAULT 0,
    shipping_total DECIMAL(10, 2) DEFAULT 0,
    discount_total DECIMAL(10, 2) DEFAULT 0,
    grand_total DECIMAL(10, 2) DEFAULT 0,
    coupon_codes TEXT[],
    shipping_address JSONB,
    billing_address JSONB,
    selected_shipping_option JSONB,
    notes TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    expires_at TIMESTAMPTZ DEFAULT NOW() + INTERVAL '30 days',
    last_activity_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_cart_sessions_profile_id ON public.cart_sessions(profile_id);
CREATE INDEX idx_cart_sessions_session_id ON public.cart_sessions(session_id);
CREATE INDEX idx_cart_sessions_expires ON public.cart_sessions(expires_at);

-- =====================================================
-- ORDER SYNC
-- =====================================================
-- Synchronization of BigCommerce orders
CREATE TABLE IF NOT EXISTS public.order_sync (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bc_order_id INTEGER NOT NULL UNIQUE,
    profile_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    order_number TEXT NOT NULL,
    status TEXT NOT NULL,
    subtotal DECIMAL(10, 2) NOT NULL,
    tax_total DECIMAL(10, 2) DEFAULT 0,
    shipping_total DECIMAL(10, 2) DEFAULT 0,
    discount_total DECIMAL(10, 2) DEFAULT 0,
    grand_total DECIMAL(10, 2) NOT NULL,
    items_total INTEGER NOT NULL,
    payment_method TEXT,
    payment_status TEXT,
    shipping_method TEXT,
    tracking_numbers TEXT[],
    currency_code TEXT DEFAULT 'USD',
    customer_message TEXT,
    staff_notes TEXT,
    order_source TEXT,
    bc_customer_id INTEGER,
    billing_address JSONB NOT NULL,
    shipping_address JSONB,
    products JSONB NOT NULL DEFAULT '[]'::jsonb,
    custom_status TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    ordered_at TIMESTAMPTZ NOT NULL,
    shipped_at TIMESTAMPTZ,
    synced_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_order_sync_bc_order_id ON public.order_sync(bc_order_id);
CREATE INDEX idx_order_sync_profile_id ON public.order_sync(profile_id);
CREATE INDEX idx_order_sync_order_number ON public.order_sync(order_number);
CREATE INDEX idx_order_sync_status ON public.order_sync(status);
CREATE INDEX idx_order_sync_ordered_at ON public.order_sync(ordered_at DESC);

-- =====================================================
-- DROP WAITLISTS (Limited Edition Queue System)
-- =====================================================
-- Manages queues for limited edition product releases
CREATE TABLE IF NOT EXISTS public.drop_waitlists (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    bc_product_id INTEGER NOT NULL,
    profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    queue_position INTEGER NOT NULL,
    tier TEXT DEFAULT 'general' CHECK (tier IN ('vip', 'early_access', 'general')),
    status TEXT DEFAULT 'waiting' CHECK (status IN ('waiting', 'notified', 'purchased', 'expired', 'cancelled')),
    reservation_token UUID UNIQUE,
    reservation_expires_at TIMESTAMPTZ,
    max_quantity INTEGER DEFAULT 1,
    metadata JSONB DEFAULT '{}'::jsonb,
    joined_at TIMESTAMPTZ DEFAULT NOW(),
    notified_at TIMESTAMPTZ,
    purchased_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(bc_product_id, profile_id)
);

CREATE INDEX idx_drop_waitlists_product ON public.drop_waitlists(bc_product_id);
CREATE INDEX idx_drop_waitlists_profile ON public.drop_waitlists(profile_id);
CREATE INDEX idx_drop_waitlists_status ON public.drop_waitlists(status);
CREATE INDEX idx_drop_waitlists_queue ON public.drop_waitlists(bc_product_id, tier, queue_position);
CREATE INDEX idx_drop_waitlists_reservation ON public.drop_waitlists(reservation_token) WHERE reservation_token IS NOT NULL;

-- =====================================================
-- GUARDIAN SETTINGS
-- =====================================================
-- Parental control settings for minor purchases
CREATE TABLE IF NOT EXISTS public.guardian_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    child_profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    guardian_profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    spending_limit_daily DECIMAL(10, 2),
    spending_limit_weekly DECIMAL(10, 2),
    spending_limit_monthly DECIMAL(10, 2),
    spending_limit_per_item DECIMAL(10, 2),
    require_approval_over DECIMAL(10, 2),
    allowed_categories TEXT[],
    blocked_categories TEXT[],
    notification_channels TEXT[] DEFAULT ARRAY['push', 'email'],
    auto_approve_under DECIMAL(10, 2),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(child_profile_id, guardian_profile_id)
);

CREATE INDEX idx_guardian_settings_child ON public.guardian_settings(child_profile_id);
CREATE INDEX idx_guardian_settings_guardian ON public.guardian_settings(guardian_profile_id);
CREATE INDEX idx_guardian_settings_active ON public.guardian_settings(is_active);

-- =====================================================
-- PURCHASE APPROVALS
-- =====================================================
-- Tracks approval requests and decisions
CREATE TABLE IF NOT EXISTS public.purchase_approvals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    child_profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    guardian_profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    cart_session_id UUID REFERENCES public.cart_sessions(id) ON DELETE SET NULL,
    requested_total DECIMAL(10, 2) NOT NULL,
    items_summary JSONB NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'denied', 'expired', 'cancelled')),
    approval_token UUID DEFAULT uuid_generate_v4() UNIQUE,
    denial_reason TEXT,
    approved_by UUID REFERENCES public.profiles(id),
    coach_override BOOLEAN DEFAULT false,
    notification_sent_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ DEFAULT NOW() + INTERVAL '24 hours',
    requested_at TIMESTAMPTZ DEFAULT NOW(),
    decided_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_purchase_approvals_child ON public.purchase_approvals(child_profile_id);
CREATE INDEX idx_purchase_approvals_guardian ON public.purchase_approvals(guardian_profile_id);
CREATE INDEX idx_purchase_approvals_status ON public.purchase_approvals(status);
CREATE INDEX idx_purchase_approvals_token ON public.purchase_approvals(approval_token);
CREATE INDEX idx_purchase_approvals_expires ON public.purchase_approvals(expires_at);

-- =====================================================
-- LOYALTY POINTS
-- =====================================================
-- Customer loyalty program points tracking
CREATE TABLE IF NOT EXISTS public.loyalty_points (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    current_balance INTEGER NOT NULL DEFAULT 0,
    lifetime_earned INTEGER NOT NULL DEFAULT 0,
    lifetime_spent INTEGER NOT NULL DEFAULT 0,
    tier TEXT DEFAULT 'bronze' CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum')),
    tier_progress INTEGER DEFAULT 0,
    next_tier_threshold INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(profile_id)
);

CREATE INDEX idx_loyalty_points_profile ON public.loyalty_points(profile_id);
CREATE INDEX idx_loyalty_points_tier ON public.loyalty_points(tier);

-- =====================================================
-- POINT TRANSACTIONS
-- =====================================================
-- Loyalty point transaction history
CREATE TABLE IF NOT EXISTS public.point_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('earned', 'spent', 'expired', 'adjusted')),
    points_amount INTEGER NOT NULL,
    balance_after INTEGER NOT NULL,
    reason TEXT NOT NULL,
    reference_type TEXT,
    reference_id TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_point_transactions_profile ON public.point_transactions(profile_id);
CREATE INDEX idx_point_transactions_type ON public.point_transactions(transaction_type);
CREATE INDEX idx_point_transactions_created ON public.point_transactions(created_at DESC);

-- =====================================================
-- NOTIFICATION QUEUE
-- =====================================================
-- Multi-channel notification delivery queue
CREATE TABLE IF NOT EXISTS public.notification_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    channel TEXT NOT NULL CHECK (channel IN ('push', 'email', 'sms', 'in_app')),
    type TEXT NOT NULL,
    subject TEXT,
    body TEXT NOT NULL,
    data JSONB DEFAULT '{}'::jsonb,
    priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'sending', 'sent', 'failed', 'cancelled')),
    scheduled_for TIMESTAMPTZ DEFAULT NOW(),
    sent_at TIMESTAMPTZ,
    failed_at TIMESTAMPTZ,
    failure_reason TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE INDEX idx_notification_queue_profile ON public.notification_queue(profile_id);
CREATE INDEX idx_notification_queue_status ON public.notification_queue(status);
CREATE INDEX idx_notification_queue_scheduled ON public.notification_queue(scheduled_for) WHERE status = 'pending';
CREATE INDEX idx_notification_queue_channel ON public.notification_queue(channel);

-- =====================================================
-- WISHLIST ITEMS
-- =====================================================
-- User saved products for later purchase
CREATE TABLE IF NOT EXISTS public.wishlist_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    bc_product_id INTEGER NOT NULL,
    variant_id INTEGER,
    notes TEXT,
    priority INTEGER DEFAULT 0,
    notify_on_sale BOOLEAN DEFAULT true,
    notify_on_restock BOOLEAN DEFAULT true,
    added_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(profile_id, bc_product_id, variant_id)
);

CREATE INDEX idx_wishlist_items_profile ON public.wishlist_items(profile_id);
CREATE INDEX idx_wishlist_items_product ON public.wishlist_items(bc_product_id);

-- =====================================================
-- SUBSCRIPTIONS
-- =====================================================
-- Recurring subscription management
CREATE TABLE IF NOT EXISTS public.subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    bc_subscription_id INTEGER UNIQUE,
    stripe_subscription_id TEXT UNIQUE,
    product_id INTEGER NOT NULL,
    plan_name TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('active', 'paused', 'cancelled', 'expired', 'past_due')),
    current_period_start TIMESTAMPTZ NOT NULL,
    current_period_end TIMESTAMPTZ NOT NULL,
    cancel_at_period_end BOOLEAN DEFAULT false,
    cancelled_at TIMESTAMPTZ,
    pause_collection JSONB,
    billing_cycle_anchor INTEGER,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(profile_id, product_id)
);

CREATE INDEX idx_subscriptions_profile ON public.subscriptions(profile_id);
CREATE INDEX idx_subscriptions_status ON public.subscriptions(status);
CREATE INDEX idx_subscriptions_stripe ON public.subscriptions(stripe_subscription_id);

-- =====================================================
-- TRIGGERS FOR UPDATED_AT
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at triggers to all tables
CREATE TRIGGER update_bc_customer_mappings_updated_at BEFORE UPDATE ON public.bc_customer_mappings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_cache_updated_at BEFORE UPDATE ON public.product_cache
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cart_sessions_updated_at BEFORE UPDATE ON public.cart_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_order_sync_updated_at BEFORE UPDATE ON public.order_sync
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_drop_waitlists_updated_at BEFORE UPDATE ON public.drop_waitlists
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_guardian_settings_updated_at BEFORE UPDATE ON public.guardian_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_purchase_approvals_updated_at BEFORE UPDATE ON public.purchase_approvals
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_loyalty_points_updated_at BEFORE UPDATE ON public.loyalty_points
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_queue_updated_at BEFORE UPDATE ON public.notification_queue
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON public.subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- RLS POLICIES
-- =====================================================
-- Enable RLS on all tables
ALTER TABLE public.bc_customer_mappings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_cache ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cart_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_sync ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.drop_waitlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.guardian_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.purchase_approvals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.loyalty_points ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.point_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.wishlist_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- Customer mappings - users can only see their own
CREATE POLICY "Users can view own customer mapping" ON public.bc_customer_mappings
    FOR SELECT USING (auth.uid() = profile_id);

-- Product cache - public read for all authenticated users
CREATE POLICY "Authenticated users can view products" ON public.product_cache
    FOR ALL USING (auth.role() = 'authenticated');

-- Cart sessions - users can manage their own carts
CREATE POLICY "Users can manage own carts" ON public.cart_sessions
    FOR ALL USING (auth.uid() = profile_id OR profile_id IS NULL);

-- Orders - users can view their own orders
CREATE POLICY "Users can view own orders" ON public.order_sync
    FOR SELECT USING (auth.uid() = profile_id);

-- Drop waitlists - users can manage their own waitlist entries
CREATE POLICY "Users can manage own waitlist entries" ON public.drop_waitlists
    FOR ALL USING (auth.uid() = profile_id);

-- Guardian settings - guardians and children can view, only guardians can modify
CREATE POLICY "View guardian settings" ON public.guardian_settings
    FOR SELECT USING (auth.uid() IN (child_profile_id, guardian_profile_id));

CREATE POLICY "Guardians can manage settings" ON public.guardian_settings
    FOR ALL USING (auth.uid() = guardian_profile_id);

-- Purchase approvals - involved parties can view
CREATE POLICY "View purchase approvals" ON public.purchase_approvals
    FOR SELECT USING (auth.uid() IN (child_profile_id, guardian_profile_id, approved_by));

CREATE POLICY "Create purchase approvals" ON public.purchase_approvals
    FOR INSERT WITH CHECK (auth.uid() = child_profile_id);

CREATE POLICY "Update purchase approvals" ON public.purchase_approvals
    FOR UPDATE USING (auth.uid() IN (guardian_profile_id, approved_by));

-- Loyalty points - users can view their own
CREATE POLICY "Users can view own loyalty points" ON public.loyalty_points
    FOR SELECT USING (auth.uid() = profile_id);

-- Point transactions - users can view their own
CREATE POLICY "Users can view own point transactions" ON public.point_transactions
    FOR SELECT USING (auth.uid() = profile_id);

-- Notifications - users can view their own
CREATE POLICY "Users can view own notifications" ON public.notification_queue
    FOR SELECT USING (auth.uid() = profile_id);

-- Wishlist - users can manage their own
CREATE POLICY "Users can manage own wishlist" ON public.wishlist_items
    FOR ALL USING (auth.uid() = profile_id);

-- Subscriptions - users can view their own
CREATE POLICY "Users can view own subscriptions" ON public.subscriptions
    FOR SELECT USING (auth.uid() = profile_id);

-- =====================================================
-- HELPER FUNCTIONS
-- =====================================================

-- Function to clean expired cart sessions
CREATE OR REPLACE FUNCTION clean_expired_cart_sessions()
RETURNS void AS $$
BEGIN
    DELETE FROM public.cart_sessions 
    WHERE expires_at < NOW() 
    AND profile_id IS NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update loyalty points
CREATE OR REPLACE FUNCTION update_loyalty_points(
    p_profile_id UUID,
    p_points INTEGER,
    p_type TEXT,
    p_reason TEXT,
    p_reference_type TEXT DEFAULT NULL,
    p_reference_id TEXT DEFAULT NULL
)
RETURNS void AS $$
DECLARE
    v_current_balance INTEGER;
    v_new_balance INTEGER;
BEGIN
    -- Get current balance or create new record
    INSERT INTO public.loyalty_points (profile_id, current_balance)
    VALUES (p_profile_id, 0)
    ON CONFLICT (profile_id) DO NOTHING;
    
    SELECT current_balance INTO v_current_balance
    FROM public.loyalty_points
    WHERE profile_id = p_profile_id
    FOR UPDATE;
    
    -- Calculate new balance
    IF p_type = 'spent' THEN
        v_new_balance := v_current_balance - ABS(p_points);
    ELSE
        v_new_balance := v_current_balance + ABS(p_points);
    END IF;
    
    -- Update balance
    UPDATE public.loyalty_points
    SET current_balance = v_new_balance,
        lifetime_earned = CASE WHEN p_type = 'earned' THEN lifetime_earned + ABS(p_points) ELSE lifetime_earned END,
        lifetime_spent = CASE WHEN p_type = 'spent' THEN lifetime_spent + ABS(p_points) ELSE lifetime_spent END
    WHERE profile_id = p_profile_id;
    
    -- Record transaction
    INSERT INTO public.point_transactions (
        profile_id, transaction_type, points_amount, 
        balance_after, reason, reference_type, reference_id
    ) VALUES (
        p_profile_id, p_type, p_points, 
        v_new_balance, p_reason, p_reference_type, p_reference_id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to process drop queue when inventory becomes available
CREATE OR REPLACE FUNCTION process_drop_queue(p_product_id INTEGER, p_available_quantity INTEGER)
RETURNS TABLE(profile_id UUID, reservation_token UUID) AS $$
DECLARE
    v_allocated INTEGER := 0;
    v_reservation_duration INTERVAL := '10 minutes';
BEGIN
    RETURN QUERY
    WITH allocated AS (
        SELECT 
            dw.profile_id,
            dw.id,
            dw.max_quantity,
            SUM(dw.max_quantity) OVER (ORDER BY dw.tier DESC, dw.queue_position) as running_total
        FROM public.drop_waitlists dw
        WHERE dw.bc_product_id = p_product_id
        AND dw.status = 'waiting'
        ORDER BY dw.tier DESC, dw.queue_position
    )
    UPDATE public.drop_waitlists dw
    SET 
        status = 'notified',
        reservation_token = uuid_generate_v4(),
        reservation_expires_at = NOW() + v_reservation_duration,
        notified_at = NOW()
    FROM allocated a
    WHERE dw.id = a.id
    AND a.running_total <= p_available_quantity
    RETURNING dw.profile_id, dw.reservation_token;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- COMMENTS FOR DOCUMENTATION
-- =====================================================
COMMENT ON TABLE public.bc_customer_mappings IS 'Maps SHOT profiles to BigCommerce customer accounts';
COMMENT ON TABLE public.product_cache IS 'Local cache of BigCommerce products for performance';
COMMENT ON TABLE public.cart_sessions IS 'Persistent shopping cart storage with 30-day expiration';
COMMENT ON TABLE public.order_sync IS 'Synchronized copy of BigCommerce orders for local queries';
COMMENT ON TABLE public.drop_waitlists IS 'Queue management for limited edition product releases';
COMMENT ON TABLE public.guardian_settings IS 'Parental controls for minor user purchases';
COMMENT ON TABLE public.purchase_approvals IS 'Approval workflow for purchases requiring guardian consent';
COMMENT ON TABLE public.loyalty_points IS 'Customer loyalty program point balances';
COMMENT ON TABLE public.point_transactions IS 'Audit trail of loyalty point changes';
COMMENT ON TABLE public.notification_queue IS 'Multi-channel notification delivery system';
COMMENT ON TABLE public.wishlist_items IS 'Saved products for later purchase';
COMMENT ON TABLE public.subscriptions IS 'Recurring subscription management for memberships';