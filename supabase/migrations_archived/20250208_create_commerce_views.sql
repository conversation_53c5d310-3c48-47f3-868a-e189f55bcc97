-- ABOUTME: Create public views for commerce schema tables (Supabase v1 compatibility)
-- This allows Supabase v1 to access commerce schema tables through public views

-- First ensure commerce schema exists
CREATE SCHEMA IF NOT EXISTS commerce;
GRANT USAGE ON SCHEMA commerce TO authenticated, anon;

-- =====================================================
-- CREATE TABLES IN COMMERCE SCHEMA
-- =====================================================

-- Cart sessions table
CREATE TABLE IF NOT EXISTS commerce.cart_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id TEXT UNIQUE NOT NULL,
    profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    items JSONB DEFAULT '[]'::jsonb NOT NULL,
    subtotal DECIMAL(10,2) DEFAULT 0 NOT NULL,
    tax_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    shipping_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    discount_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    grand_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    coupon_codes TEXT[] DEFAULT ARRAY[]::TEXT[],
    shipping_address JSONB,
    billing_address JSONB,
    selected_shipping_option JSONB,
    notes TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days') NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Product cache table
CREATE TABLE IF NOT EXISTS commerce.product_cache (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    bc_product_id INTEGER NOT NULL UNIQUE,
    sku TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    sale_price DECIMAL(10, 2),
    cost_price DECIMAL(10, 2),
    retail_price DECIMAL(10, 2),
    weight DECIMAL(10, 2),
    categories JSONB DEFAULT '[]'::jsonb,
    brand_id INTEGER,
    brand_name TEXT,
    images JSONB DEFAULT '[]'::jsonb,
    variants JSONB DEFAULT '[]'::jsonb,
    custom_fields JSONB DEFAULT '{}'::jsonb,
    inventory_level INTEGER DEFAULT 0,
    inventory_tracking TEXT,
    is_visible BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    meta_keywords TEXT[],
    search_keywords TEXT,
    availability TEXT,
    condition TEXT DEFAULT 'new',
    cache_ttl_minutes INTEGER DEFAULT 5,
    cached_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Guardian settings table
CREATE TABLE IF NOT EXISTS commerce.guardian_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    child_profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    guardian_profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    purchase_approval_required BOOLEAN DEFAULT true,
    max_purchase_amount DECIMAL(10,2),
    allowed_categories INTEGER[],
    blocked_categories INTEGER[],
    notification_preferences JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(child_profile_id, guardian_profile_id)
);

-- Purchase approvals table
CREATE TABLE IF NOT EXISTS commerce.purchase_approvals (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    child_profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    guardian_profile_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    cart_data JSONB NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'denied', 'expired')),
    guardian_notes TEXT,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responded_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '48 hours'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- CREATE INDEXES
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_cart_sessions_session_id ON commerce.cart_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_cart_sessions_profile_id ON commerce.cart_sessions(profile_id);
CREATE INDEX IF NOT EXISTS idx_product_cache_bc_id ON commerce.product_cache(bc_product_id);

-- =====================================================
-- CREATE PUBLIC VIEWS (FOR SUPABASE V1 COMPATIBILITY)
-- =====================================================

-- Drop existing views if they exist
DROP VIEW IF EXISTS public.cart_sessions CASCADE;
DROP VIEW IF EXISTS public.product_cache CASCADE;
DROP VIEW IF EXISTS public.guardian_settings CASCADE;
DROP VIEW IF EXISTS public.purchase_approvals CASCADE;

-- Create views in public schema that reference commerce schema tables
CREATE VIEW public.cart_sessions AS
SELECT * FROM commerce.cart_sessions;

CREATE VIEW public.product_cache AS
SELECT * FROM commerce.product_cache;

CREATE VIEW public.guardian_settings AS
SELECT * FROM commerce.guardian_settings;

CREATE VIEW public.purchase_approvals AS
SELECT * FROM commerce.purchase_approvals;

-- =====================================================
-- GRANT PERMISSIONS ON VIEWS
-- =====================================================
GRANT SELECT ON public.cart_sessions TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON public.cart_sessions TO anon, authenticated;

GRANT SELECT ON public.product_cache TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON public.product_cache TO anon, authenticated;

GRANT SELECT ON public.guardian_settings TO authenticated;
GRANT INSERT, UPDATE ON public.guardian_settings TO authenticated;

GRANT SELECT ON public.purchase_approvals TO authenticated;
GRANT INSERT, UPDATE ON public.purchase_approvals TO authenticated;

-- =====================================================
-- RLS POLICIES (Apply to actual tables, not views)
-- =====================================================
ALTER TABLE commerce.cart_sessions ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can view own carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can create carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can update own carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can delete own carts" ON commerce.cart_sessions;

CREATE POLICY "Users can view own carts" ON commerce.cart_sessions
    FOR SELECT
    USING (auth.uid() = profile_id OR profile_id IS NULL);

CREATE POLICY "Users can create carts" ON commerce.cart_sessions
    FOR INSERT
    WITH CHECK (auth.uid() = profile_id OR profile_id IS NULL);

CREATE POLICY "Users can update own carts" ON commerce.cart_sessions
    FOR UPDATE
    USING (auth.uid() = profile_id OR profile_id IS NULL);

CREATE POLICY "Users can delete own carts" ON commerce.cart_sessions
    FOR DELETE
    USING (auth.uid() = profile_id OR profile_id IS NULL);

-- =====================================================
-- CREATE TRIGGERS
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers to actual tables
DROP TRIGGER IF EXISTS update_cart_sessions_updated_at ON commerce.cart_sessions;
CREATE TRIGGER update_cart_sessions_updated_at 
    BEFORE UPDATE ON commerce.cart_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_product_cache_updated_at ON commerce.product_cache;
CREATE TRIGGER update_product_cache_updated_at 
    BEFORE UPDATE ON commerce.product_cache 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- CREATE INSTEAD OF TRIGGERS FOR VIEWS
-- =====================================================
-- These triggers allow INSERT/UPDATE/DELETE operations on the public views
-- to be redirected to the actual tables in the commerce schema

-- CART_SESSIONS triggers
CREATE OR REPLACE FUNCTION public.cart_sessions_insert()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO commerce.cart_sessions VALUES (NEW.*);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER cart_sessions_insert_trigger
INSTEAD OF INSERT ON public.cart_sessions
FOR EACH ROW EXECUTE FUNCTION public.cart_sessions_insert();

CREATE OR REPLACE FUNCTION public.cart_sessions_update()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE commerce.cart_sessions SET
    session_id = NEW.session_id,
    profile_id = NEW.profile_id,
    items = NEW.items,
    subtotal = NEW.subtotal,
    tax_total = NEW.tax_total,
    shipping_total = NEW.shipping_total,
    discount_total = NEW.discount_total,
    grand_total = NEW.grand_total,
    coupon_codes = NEW.coupon_codes,
    shipping_address = NEW.shipping_address,
    billing_address = NEW.billing_address,
    selected_shipping_option = NEW.selected_shipping_option,
    notes = NEW.notes,
    metadata = NEW.metadata,
    last_activity_at = NEW.last_activity_at,
    expires_at = NEW.expires_at,
    updated_at = NEW.updated_at
  WHERE id = OLD.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER cart_sessions_update_trigger
INSTEAD OF UPDATE ON public.cart_sessions
FOR EACH ROW EXECUTE FUNCTION public.cart_sessions_update();

CREATE OR REPLACE FUNCTION public.cart_sessions_delete()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM commerce.cart_sessions WHERE id = OLD.id;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER cart_sessions_delete_trigger
INSTEAD OF DELETE ON public.cart_sessions
FOR EACH ROW EXECUTE FUNCTION public.cart_sessions_delete();

-- PRODUCT_CACHE triggers
CREATE OR REPLACE FUNCTION public.product_cache_insert()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO commerce.product_cache VALUES (NEW.*);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER product_cache_insert_trigger
INSTEAD OF INSERT ON public.product_cache
FOR EACH ROW EXECUTE FUNCTION public.product_cache_insert();

CREATE OR REPLACE FUNCTION public.product_cache_update()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE commerce.product_cache SET
    bc_product_id = NEW.bc_product_id,
    sku = NEW.sku,
    name = NEW.name,
    description = NEW.description,
    price = NEW.price,
    sale_price = NEW.sale_price,
    cost_price = NEW.cost_price,
    retail_price = NEW.retail_price,
    weight = NEW.weight,
    categories = NEW.categories,
    brand_id = NEW.brand_id,
    brand_name = NEW.brand_name,
    images = NEW.images,
    variants = NEW.variants,
    custom_fields = NEW.custom_fields,
    inventory_level = NEW.inventory_level,
    inventory_tracking = NEW.inventory_tracking,
    is_visible = NEW.is_visible,
    is_featured = NEW.is_featured,
    sort_order = NEW.sort_order,
    meta_keywords = NEW.meta_keywords,
    search_keywords = NEW.search_keywords,
    availability = NEW.availability,
    condition = NEW.condition,
    cache_ttl_minutes = NEW.cache_ttl_minutes,
    cached_at = NEW.cached_at,
    updated_at = NEW.updated_at
  WHERE id = OLD.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER product_cache_update_trigger
INSTEAD OF UPDATE ON public.product_cache
FOR EACH ROW EXECUTE FUNCTION public.product_cache_update();

CREATE OR REPLACE FUNCTION public.product_cache_delete()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM commerce.product_cache WHERE id = OLD.id;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER product_cache_delete_trigger
INSTEAD OF DELETE ON public.product_cache
FOR EACH ROW EXECUTE FUNCTION public.product_cache_delete();

-- GUARDIAN_SETTINGS triggers
CREATE OR REPLACE FUNCTION public.guardian_settings_insert()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO commerce.guardian_settings VALUES (NEW.*);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER guardian_settings_insert_trigger
INSTEAD OF INSERT ON public.guardian_settings
FOR EACH ROW EXECUTE FUNCTION public.guardian_settings_insert();

CREATE OR REPLACE FUNCTION public.guardian_settings_update()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE commerce.guardian_settings SET
    child_profile_id = NEW.child_profile_id,
    guardian_profile_id = NEW.guardian_profile_id,
    purchase_approval_required = NEW.purchase_approval_required,
    max_purchase_amount = NEW.max_purchase_amount,
    allowed_categories = NEW.allowed_categories,
    blocked_categories = NEW.blocked_categories,
    notification_preferences = NEW.notification_preferences,
    updated_at = NEW.updated_at
  WHERE id = OLD.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER guardian_settings_update_trigger
INSTEAD OF UPDATE ON public.guardian_settings
FOR EACH ROW EXECUTE FUNCTION public.guardian_settings_update();

CREATE OR REPLACE FUNCTION public.guardian_settings_delete()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM commerce.guardian_settings WHERE id = OLD.id;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER guardian_settings_delete_trigger
INSTEAD OF DELETE ON public.guardian_settings
FOR EACH ROW EXECUTE FUNCTION public.guardian_settings_delete();

-- PURCHASE_APPROVALS triggers
CREATE OR REPLACE FUNCTION public.purchase_approvals_insert()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO commerce.purchase_approvals VALUES (NEW.*);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER purchase_approvals_insert_trigger
INSTEAD OF INSERT ON public.purchase_approvals
FOR EACH ROW EXECUTE FUNCTION public.purchase_approvals_insert();

CREATE OR REPLACE FUNCTION public.purchase_approvals_update()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE commerce.purchase_approvals SET
    child_profile_id = NEW.child_profile_id,
    guardian_profile_id = NEW.guardian_profile_id,
    cart_data = NEW.cart_data,
    total_amount = NEW.total_amount,
    status = NEW.status,
    guardian_notes = NEW.guardian_notes,
    requested_at = NEW.requested_at,
    responded_at = NEW.responded_at,
    expires_at = NEW.expires_at,
    updated_at = NEW.updated_at
  WHERE id = OLD.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER purchase_approvals_update_trigger
INSTEAD OF UPDATE ON public.purchase_approvals
FOR EACH ROW EXECUTE FUNCTION public.purchase_approvals_update();

CREATE OR REPLACE FUNCTION public.purchase_approvals_delete()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM commerce.purchase_approvals WHERE id = OLD.id;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER purchase_approvals_delete_trigger
INSTEAD OF DELETE ON public.purchase_approvals
FOR EACH ROW EXECUTE FUNCTION public.purchase_approvals_delete();

-- =====================================================
-- VERIFICATION
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== Commerce Schema Setup Complete ===';
    RAISE NOTICE '';
    RAISE NOTICE 'Tables created in commerce schema:';
    RAISE NOTICE '  ✓ commerce.cart_sessions';
    RAISE NOTICE '  ✓ commerce.product_cache';
    RAISE NOTICE '  ✓ commerce.guardian_settings';
    RAISE NOTICE '  ✓ commerce.purchase_approvals';
    RAISE NOTICE '';
    RAISE NOTICE 'Public views created for Supabase v1:';
    RAISE NOTICE '  ✓ public.cart_sessions → commerce.cart_sessions';
    RAISE NOTICE '  ✓ public.product_cache → commerce.product_cache';
    RAISE NOTICE '  ✓ public.guardian_settings → commerce.guardian_settings';
    RAISE NOTICE '  ✓ public.purchase_approvals → commerce.purchase_approvals';
    RAISE NOTICE '';
    RAISE NOTICE 'Your app can now use:';
    RAISE NOTICE '  supabase.from("cart_sessions") - accesses the public view';
    RAISE NOTICE '  Which automatically reads/writes to commerce.cart_sessions';
    RAISE NOTICE '';
END $$;