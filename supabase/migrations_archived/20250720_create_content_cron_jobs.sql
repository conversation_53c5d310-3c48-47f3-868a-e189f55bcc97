-- Enable pg_cron extension if not already enabled
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Grant usage on cron schema to postgres role
GRANT USAGE ON SCHEMA cron TO postgres;

-- Create cron job for content sync (every 15 minutes)
SELECT cron.schedule(
  'sync-pulse-content', -- job name
  '*/15 * * * *', -- every 15 minutes
  $$
  SELECT net.http_post(
    url := current_setting('app.supabase_url') || '/functions/v1/content-sync',
    headers := jsonb_build_object(
      'Authorization', 'Bearer ' || current_setting('app.service_role_key'),
      'Content-Type', 'application/json'
    ),
    body := jsonb_build_object(
      'source', 'cron',
      'timestamp', now()
    )
  );
  $$
);

-- Create a function to manage sync retries
CREATE OR REPLACE FUNCTION content.retry_failed_syncs()
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
  failed_sync RECORD;
BEGIN
  -- Find failed syncs from the last hour
  FOR failed_sync IN 
    SELECT id, metadata
    FROM content.pulse_sync_log
    WHERE sync_status = 'failed'
      AND sync_started_at > NOW() - INTERVAL '1 hour'
      AND (metadata->>'retry_count')::int < 3
  LOOP
    -- Trigger a retry
    PERFORM net.http_post(
      url := current_setting('app.supabase_url') || '/functions/v1/content-sync',
      headers := jsonb_build_object(
        'Authorization', 'Bearer ' || current_setting('app.service_role_key'),
        'Content-Type', 'application/json'
      ),
      body := jsonb_build_object(
        'source', 'retry',
        'retry_count', COALESCE((failed_sync.metadata->>'retry_count')::int, 0) + 1,
        'original_sync_id', failed_sync.id
      )
    );
  END LOOP;
END;
$$;

-- Create cron job for retry logic (every 5 minutes)
SELECT cron.schedule(
  'retry-failed-content-syncs',
  '*/5 * * * *', -- every 5 minutes
  'SELECT content.retry_failed_syncs();'
);

-- Create monitoring views
CREATE OR REPLACE VIEW content.sync_health AS
SELECT 
  DATE_TRUNC('hour', sync_started_at) as hour,
  COUNT(*) as total_syncs,
  COUNT(*) FILTER (WHERE sync_status = 'completed') as successful_syncs,
  COUNT(*) FILTER (WHERE sync_status = 'failed') as failed_syncs,
  AVG(EXTRACT(EPOCH FROM (sync_completed_at - sync_started_at))) as avg_duration_seconds,
  SUM(articles_created) as total_articles_created,
  SUM(articles_updated) as total_articles_updated
FROM content.pulse_sync_log
WHERE sync_started_at > NOW() - INTERVAL '24 hours'
GROUP BY DATE_TRUNC('hour', sync_started_at)
ORDER BY hour DESC;

-- Create alert function for sync failures
CREATE OR REPLACE FUNCTION content.check_sync_health()
RETURNS TABLE (
  alert_level TEXT,
  message TEXT,
  details JSONB
)
LANGUAGE plpgsql
AS $$
BEGIN
  -- Check for no syncs in the last hour
  IF NOT EXISTS (
    SELECT 1 FROM content.pulse_sync_log 
    WHERE sync_started_at > NOW() - INTERVAL '1 hour'
  ) THEN
    RETURN QUERY
    SELECT 
      'critical'::TEXT,
      'No syncs in the last hour'::TEXT,
      jsonb_build_object(
        'last_sync', (SELECT MAX(sync_started_at) FROM content.pulse_sync_log)
      );
  END IF;

  -- Check for high failure rate
  IF EXISTS (
    SELECT 1
    FROM content.sync_health
    WHERE hour = DATE_TRUNC('hour', NOW())
      AND failed_syncs > successful_syncs
  ) THEN
    RETURN QUERY
    SELECT 
      'warning'::TEXT,
      'High failure rate in current hour'::TEXT,
      jsonb_build_object(
        'stats', (
          SELECT jsonb_build_object(
            'successful', successful_syncs,
            'failed', failed_syncs
          )
          FROM content.sync_health
          WHERE hour = DATE_TRUNC('hour', NOW())
        )
      );
  END IF;

  -- All good
  RETURN QUERY
  SELECT 
    'ok'::TEXT,
    'Sync health is good'::TEXT,
    jsonb_build_object(
      'last_sync', (SELECT MAX(sync_started_at) FROM content.pulse_sync_log),
      'success_rate', (
        SELECT 
          ROUND((COUNT(*) FILTER (WHERE sync_status = 'completed')::numeric / COUNT(*) * 100), 2)
        FROM content.pulse_sync_log
        WHERE sync_started_at > NOW() - INTERVAL '1 hour'
      )
    );
END;
$$;

-- Create function to get sync statistics
CREATE OR REPLACE FUNCTION content.get_sync_stats(time_period INTERVAL DEFAULT INTERVAL '24 hours')
RETURNS TABLE (
  total_syncs BIGINT,
  successful_syncs BIGINT,
  failed_syncs BIGINT,
  success_rate NUMERIC,
  total_articles_synced BIGINT,
  avg_sync_duration_seconds NUMERIC,
  last_sync_at TIMESTAMP,
  last_sync_status TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*)::BIGINT as total_syncs,
    COUNT(*) FILTER (WHERE sync_status = 'completed')::BIGINT as successful_syncs,
    COUNT(*) FILTER (WHERE sync_status = 'failed')::BIGINT as failed_syncs,
    ROUND((COUNT(*) FILTER (WHERE sync_status = 'completed')::numeric / NULLIF(COUNT(*), 0) * 100), 2) as success_rate,
    (SUM(articles_created) + SUM(articles_updated))::BIGINT as total_articles_synced,
    ROUND(AVG(EXTRACT(EPOCH FROM (sync_completed_at - sync_started_at)))::numeric, 2) as avg_sync_duration_seconds,
    MAX(sync_started_at) as last_sync_at,
    (SELECT sync_status FROM content.pulse_sync_log ORDER BY sync_started_at DESC LIMIT 1) as last_sync_status
  FROM content.pulse_sync_log
  WHERE sync_started_at > NOW() - time_period;
END;
$$;

-- Add comments for documentation
COMMENT ON FUNCTION content.retry_failed_syncs() IS 'Retries failed content syncs up to 3 times';
COMMENT ON FUNCTION content.check_sync_health() IS 'Checks sync health and returns alerts if issues detected';
COMMENT ON FUNCTION content.get_sync_stats(INTERVAL) IS 'Returns sync statistics for the specified time period';
COMMENT ON VIEW content.sync_health IS 'Hourly sync health metrics for monitoring';