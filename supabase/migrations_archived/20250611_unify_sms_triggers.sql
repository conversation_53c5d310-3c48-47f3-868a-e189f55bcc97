-- SMS Notification System Unification

-- Step 1: Create a backup of the trigger_pre_evaluation_sms function
DO $$
BEGIN
  -- Log the migration start
  INSERT INTO debug_logs (function_name, message, data)
  VALUES ('sms_unification_migration', 'Starting SMS trigger unification', jsonb_build_object(
    'timestamp', NOW()
  ));
END$$;

-- Step 2: Drop the redundant trigger (pre_evaluation_notifications_trigger)
DROP TRIGGER IF EXISTS pre_evaluation_notifications_trigger ON pre_evaluations;

-- Step 3: Update the trigger_pre_evaluation_sms function to use api_version='v2'
CREATE OR REPLACE FUNCTION trigger_pre_evaluation_sms()
RETURNS TRIGGER AS $$
DECLARE
  v_phone_number TEXT;
  v_player_name TEXT;
  v_event_name TEXT;
  v_sms_id UUID;
BEGIN
  -- Log the function call
  INSERT INTO debug_logs (function_name, message, data)
  VALUES ('trigger_pre_evaluation_sms', 'Trigger fired', jsonb_build_object(
    'pre_eval_id', NEW.id,
    'player_id', NEW.player_id,
    'event_id', NEW.event_id,
    'team_id', NEW.team_id,
    'status', NEW.status,
    'timestamp', NOW()
  ));

  -- Get the player's phone number from profiles
  SELECT phone, full_name INTO v_phone_number, v_player_name
  FROM profiles
  WHERE id = NEW.player_id;

  -- Log the player info retrieved
  INSERT INTO debug_logs (function_name, message, data)
  VALUES ('trigger_pre_evaluation_sms', 'Player info retrieved', jsonb_build_object(
    'player_name', v_player_name,
    'phone_number', v_phone_number
  ));

  -- Get event details
  SELECT event_name INTO v_event_name
  FROM events
  WHERE id = NEW.event_id;

  -- Log the event info retrieved
  INSERT INTO debug_logs (function_name, message, data)
  VALUES ('trigger_pre_evaluation_sms', 'Event info retrieved', jsonb_build_object(
    'event_name', v_event_name
  ));

  -- If no phone number is found, use the default
  IF v_phone_number IS NULL OR v_phone_number = '' THEN
    INSERT INTO debug_logs (function_name, message, data)
    VALUES ('trigger_pre_evaluation_sms', 'Using default phone number', jsonb_build_object(
      'default_phone', '07507940322'
    ));
    
    -- Format UK number to international format
    v_phone_number := '+447507940322'; -- Default phone number as fallback
  ELSE
    -- Format UK number if it starts with 0
    IF v_phone_number LIKE '0%' THEN
      v_phone_number := '+44' || SUBSTRING(v_phone_number FROM 2);
      
      INSERT INTO debug_logs (function_name, message, data)
      VALUES ('trigger_pre_evaluation_sms', 'Formatted UK phone number', jsonb_build_object(
        'original', SUBSTRING(v_phone_number FROM 2),
        'formatted', v_phone_number
      ));
    ELSE
      -- Ensure number has + prefix if international
      IF v_phone_number NOT LIKE '+%' THEN
        v_phone_number := '+' || v_phone_number;
        
        INSERT INTO debug_logs (function_name, message, data)
        VALUES ('trigger_pre_evaluation_sms', 'Added + prefix to number', jsonb_build_object(
          'formatted', v_phone_number
        ));
      END IF;
    END IF;
  END IF;

  -- Insert a record into the sms_queue table with api_version='v2'
  INSERT INTO sms_queue (
    phone_number,
    message_body,
    status,
    pre_evaluation_id,
    recipient_name,
    event_id,
    team_id,
    notification_type,
    api_version,
    created_at
  ) VALUES (
    v_phone_number,
    format('Hi %s, please complete your pre-assessment for %s. Click here: https://app.shot.com/pre-eval/%s', 
           v_player_name, 
           v_event_name,
           NEW.id),
    'pending',
    NEW.id,
    v_player_name,
    NEW.event_id,
    NEW.team_id,
    'pre_evaluation_sms',
    'v2',
    NOW()
  ) RETURNING id INTO v_sms_id;

  -- Log successful queue addition
  INSERT INTO debug_logs (function_name, message, data)
  VALUES ('trigger_pre_evaluation_sms', 'Created SMS notification record', jsonb_build_object(
    'notification_id', v_sms_id,
    'phone_number', v_phone_number,
    'pre_evaluation_id', NEW.id
  ));

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Update any pending SMS to use api_version='v2'
UPDATE sms_queue
SET api_version = 'v2'
WHERE status = 'pending' 
  AND (api_version IS NULL OR api_version = 'v1');

-- Step 5: Log migration completion
INSERT INTO debug_logs (function_name, message, data)
VALUES ('sms_unification_migration', 'SMS trigger unification completed', jsonb_build_object(
  'timestamp', NOW()
));

-- Step 6: Add a script to reset any stuck SMS in 'processing' status
UPDATE sms_queue
SET status = 'pending',
    api_version = 'v2',
    retry_count = COALESCE(retry_count, 0) + 1,
    error_message = COALESCE(error_message, '') || ' (Auto-reset from stuck processing state)',
    updated_at = NOW()
WHERE status = 'processing'
  AND updated_at < NOW() - INTERVAL '30 minutes';

-- Log completion of the stuck SMS reset
INSERT INTO debug_logs (function_name, message, data)
VALUES ('sms_unification_migration', 'Reset stuck processing SMS', jsonb_build_object(
  'timestamp', NOW()
));
