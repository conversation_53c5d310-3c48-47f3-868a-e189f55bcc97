-- Function to send SMS notifications for pre-evaluations using player's phone number
CREATE OR REPLACE FUNCTION trigger_pre_evaluation_sms()
RETURNS TRIGGER AS $$
DECLARE
  v_phone_number TEXT;
  v_player_name TEXT;
  v_event_name TEXT;
BEGIN
  -- Log the function call
  RAISE NOTICE 'trigger_pre_evaluation_sms called for pre-evaluation ID: %', NEW.id;

  -- Get the player's phone number from profiles
  SELECT phone, full_name INTO v_phone_number, v_player_name
  FROM profiles
  WHERE id = NEW.player_id;

  -- Get event details
  SELECT event_name INTO v_event_name
  FROM events
  WHERE id = NEW.event_id;

  -- Log the phone number found
  RAISE NOTICE 'Found phone number: % for player: %', v_phone_number, v_player_name;

  -- If no phone number is found, use the default
  IF v_phone_number IS NULL OR v_phone_number = '' THEN
    RAISE NOTICE 'No phone number found, using default';
    v_phone_number := '+447507940322'; -- Default phone number as fallback
  END IF;

  -- Insert a record into the sms_queue table
  INSERT INTO sms_queue (
    phone_number,
    message_body,
    status,
    pre_evaluation_id,
    recipient_name,
    created_at
  ) VALUES (
    v_phone_number,
    format('Hi %s, please complete your pre-assessment for %s. Click here: https://app.shot.com/pre-eval/%s', 
           v_player_name, 
           v_event_name,
           NEW.id),
    'pending',
    NEW.id,
    v_player_name,
    NOW()
  );

  -- Log successful queue addition
  RAISE NOTICE 'SMS notification queued for phone: %', v_phone_number;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create SMS queue table if it doesn't exist
CREATE TABLE IF NOT EXISTS sms_queue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone_number TEXT NOT NULL,
  message_body TEXT NOT NULL,
  status VARCHAR(50) DEFAULT 'pending',
  sent_at TIMESTAMPTZ,
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  pre_evaluation_id UUID REFERENCES pre_evaluations(id),
  recipient_name TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index on status for efficient querying
CREATE INDEX IF NOT EXISTS idx_sms_queue_status ON sms_queue(status);

-- Drop the existing trigger if it exists
DROP TRIGGER IF EXISTS pre_evaluation_sms_trigger ON pre_evaluations;

-- Create the trigger
CREATE TRIGGER pre_evaluation_sms_trigger
AFTER INSERT ON pre_evaluations
FOR EACH ROW
EXECUTE FUNCTION trigger_pre_evaluation_sms();

-- Log that the migration is complete
DO $$
BEGIN
  RAISE NOTICE 'Migration completed: Added phone number support to pre-evaluation SMS trigger';
END $$;
