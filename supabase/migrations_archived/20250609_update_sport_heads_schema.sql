-- Update sport_heads table to match the strategic schema
-- Add missing columns from the strategic schema
ALTER TABLE sport_heads
  ADD COLUMN IF NOT EXISTS position VARCHAR(50),
  ADD COLUMN IF NOT EXISTS level INTEGER DEFAULT 1 CHECK (level BETWEEN 1 AND 100),
  ADD COLUMN IF NOT EXISTS experience_points INTEGER DEFAULT 0 CHECK (experience_points >= 0),
  ADD COLUMN IF NOT EXISTS prestige_level INTEGER DEFAULT 0,
  ADD COLUMN IF NOT EXISTS total_evaluations INTEGER DEFAULT 0,
  ADD COLUMN IF NOT EXISTS achievements JSONB DEFAULT '[]',
  ADD COLUMN IF NOT EXISTS achievement_points INTEGER DEFAULT 0,
  ADD COLUMN IF NOT EXISTS streak_days INTEGER DEFAULT 0,
  ADD COLUMN IF NOT EXISTS longest_streak INTEGER DEFAULT 0,
  ADD COLUMN IF NOT EXISTS last_activity_date DATE,
  ADD COLUMN IF NOT EXISTS total_active_days INTEGER DEFAULT 0,
  ADD COLUMN IF NOT EXISTS average_self_rating DECIMAL(3,2) CHECK (average_self_rating >= 1 AND average_self_rating <= 5),
  ADD COLUMN IF NOT EXISTS average_coach_rating DECIMAL(3,2) CHECK (average_coach_rating >= 1 AND average_coach_rating <= 5),
  ADD COLUMN IF NOT EXISTS improvement_trend DECIMAL(5,2),
  ADD COLUMN IF NOT EXISTS peak_rating DECIMAL(3,2),
  ADD COLUMN IF NOT EXISTS peak_rating_date DATE,
  ADD COLUMN IF NOT EXISTS unlocked_themes JSONB DEFAULT '[]',
  ADD COLUMN IF NOT EXISTS unlocked_badges JSONB DEFAULT '[]',
  ADD COLUMN IF NOT EXISTS equipped_theme VARCHAR(50) DEFAULT 'default',
  ADD COLUMN IF NOT EXISTS equipped_badges UUID[] DEFAULT '{}',
  ADD COLUMN IF NOT EXISTS evaluation_velocity DECIMAL(5,2),
  ADD COLUMN IF NOT EXISTS response_rate DECIMAL(5,2),
  ADD COLUMN IF NOT EXISTS avg_response_time INTEGER,
  ADD COLUMN IF NOT EXISTS nft_metadata_generated BOOLEAN DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS token_id VARCHAR(255),
  ADD COLUMN IF NOT EXISTS rarity_score INTEGER DEFAULT 0;

-- Add computed column for rating_gap
-- First, check if the column exists
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT FROM information_schema.columns 
                 WHERE table_name = 'sport_heads' AND column_name = 'rating_gap') THEN
    -- If both average_coach_rating and average_self_rating are not null, add the generated column
    ALTER TABLE sport_heads
    ADD COLUMN rating_gap DECIMAL(3,2) GENERATED ALWAYS AS (
      CASE 
        WHEN average_coach_rating IS NOT NULL AND average_self_rating IS NOT NULL 
        THEN average_coach_rating - average_self_rating
        ELSE NULL
      END
    ) STORED;
  END IF;
END $$;

-- Handle the creation_date column - don't try to rename if created_at already exists
DO $$ 
BEGIN
  -- Check if creation_date exists and created_at exists
  IF EXISTS (SELECT FROM information_schema.columns 
             WHERE table_name = 'sport_heads' AND column_name = 'creation_date') 
     AND EXISTS (SELECT FROM information_schema.columns 
             WHERE table_name = 'sport_heads' AND column_name = 'created_at') THEN
    
    -- Update created_at with creation_date values where created_at is null
    EXECUTE '
      UPDATE sport_heads 
      SET created_at = creation_date 
      WHERE created_at IS NULL AND creation_date IS NOT NULL';
    
    -- We could drop creation_date here if desired, but that's risky without thorough testing
    -- Commented out for safety - uncomment if you want to drop the column
    -- EXECUTE 'ALTER TABLE sport_heads DROP COLUMN creation_date';
    
  -- If only creation_date exists but not created_at
  ELSIF EXISTS (SELECT FROM information_schema.columns 
                WHERE table_name = 'sport_heads' AND column_name = 'creation_date') 
        AND NOT EXISTS (SELECT FROM information_schema.columns 
                WHERE table_name = 'sport_heads' AND column_name = 'created_at') THEN
    
    -- In this case, we can safely rename
    EXECUTE 'ALTER TABLE sport_heads RENAME COLUMN creation_date TO created_at';
  END IF;
END $$;

-- Create missing indexes
CREATE INDEX IF NOT EXISTS idx_sport_heads_level ON sport_heads(level DESC);
CREATE INDEX IF NOT EXISTS idx_sport_heads_streak ON sport_heads(streak_days DESC) 
    WHERE streak_days > 0;
CREATE INDEX IF NOT EXISTS idx_sport_heads_achievements ON sport_heads USING gin(achievements);

-- Create the activity streak trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_activity_streak()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.last_activity_date != OLD.last_activity_date OR 
       OLD.last_activity_date IS NULL THEN
        
        IF OLD.last_activity_date = CURRENT_DATE - INTERVAL '1 day' THEN
            NEW.streak_days := OLD.streak_days + 1;
            NEW.longest_streak := GREATEST(NEW.streak_days, OLD.longest_streak);
        ELSIF OLD.last_activity_date < CURRENT_DATE - INTERVAL '1 day' THEN
            NEW.streak_days := 1;
        END IF;
        
        NEW.total_active_days := OLD.total_active_days + 1;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the trigger if it exists and recreate it
DROP TRIGGER IF EXISTS update_streak_on_activity ON sport_heads;

CREATE TRIGGER update_streak_on_activity
    BEFORE UPDATE OF last_activity_date ON sport_heads
    FOR EACH ROW
    EXECUTE FUNCTION update_activity_streak();
