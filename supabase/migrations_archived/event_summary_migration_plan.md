# Event Summary View Migration Plan

## Current State
The `event_comprehensive_summary` view is used in 4 key areas:
1. **CoachPerform.tsx** - Coach dashboard
2. **TeamFlat.tsx** - Team page 
3. **EventManagement.tsx** - Admin event management
4. **EventSummaryService.ts** - Service layer

## Issues with Current View
1. Calculates "post_eval" stats for a feature that doesn't exist
2. Missing actual coach evaluation counts
3. Calculates percentages in the database (should be in frontend)
4. Returns incorrect data (e.g., 13 evaluations for 5 attendees)

## Migration Strategy

### Phase 1: Create New View
1. Deploy the new `event_summary` view alongside the existing one
2. The new view provides:
   - Raw counts only (no percentages)
   - Actual coach evaluation data from `player_evaluations` table
   - Correct attendance counts

### Phase 2: Update Components
Update each component to use the new view:

1. **TeamFlat.tsx**
   ```typescript
   // Change from:
   .from('event_comprehensive_summary')
   // To:
   .from('event_summary')
   
   // Calculate percentages in component:
   preEvaluationPercentage: Math.round((summary.pre_eval_completed_count / summary.pre_eval_total_count) * 100) || 0
   ```

2. **EventManagement.tsx**
   - Similar changes

3. **CoachPerform.tsx**
   - Similar changes

4. **EventSummaryService.ts**
   - Update all methods to use new view
   - Add percentage calculations in the service layer

### Phase 3: Remove Old View
Once all components are updated and tested:
```sql
DROP VIEW IF EXISTS event_comprehensive_summary;
```

## Key Differences

| Old View | New View |
|----------|----------|
| `post_eval_completed_players` | `coach_eval_completed_count` |
| `post_eval_completion_percentage` | (calculate in frontend) |
| `attended_count` | `attended_count` (fixed calculation) |
| `pre_eval_completion_percentage` | (calculate in frontend) |

## Testing Checklist
- [ ] TeamFlat shows correct event stats
- [ ] EventManagement displays all events properly
- [ ] CoachPerform dashboard works correctly
- [ ] EventSummaryService returns expected data
- [ ] Evaluation percentages display correctly
- [ ] No "260%" or similar impossible values