-- Migration: Create Onboarding Analytics Table
-- Description: Detailed analytics tracking for parent onboarding flow optimization
-- Phase: 2 - Parent Quick Onboarding System
-- Created: 2025-01-28

-- Create onboarding_analytics table
CREATE TABLE IF NOT EXISTS onboarding_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_token TEXT NOT NULL,
    session_id UUID REFERENCES parent_onboarding_sessions(id),
    
    -- Event tracking
    event_type VARCHAR(50) NOT NULL 
        CHECK (event_type IN (
            'step_started', 
            'step_completed', 
            'field_focused', 
            'field_blurred', 
            'field_changed',
            'validation_error',
            'error_shown', 
            'help_clicked', 
            'back_clicked',
            'link_copied',
            'browser_back',
            'page_refresh',
            'session_restored',
            'abandoned', 
            'completed'
        )),
    
    -- Context data
    step_name VARCHAR(50),
    field_name VARCHAR(50),
    field_value TEXT, -- Sanitized, no PII
    error_type VARCHAR(100),
    error_message TEXT,
    duration_seconds INTEGER,
    
    -- Additional metadata
    metadata JSONB DEFAULT '{}'::jsonb,
    /* Example metadata:
    {
        "previous_step": "account",
        "validation_attempts": 3,
        "autocomplete_used": true,
        "keyboard_type": "virtual",
        "connection_speed": "4g"
    }
    */
    
    -- Client info
    client_timestamp TIMESTAMPTZ,
    timezone VARCHAR(50),
    
    -- Server timestamp
    timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for analytics queries
CREATE INDEX idx_onboarding_analytics_session ON onboarding_analytics(session_token);
CREATE INDEX idx_onboarding_analytics_session_id ON onboarding_analytics(session_id);
CREATE INDEX idx_onboarding_analytics_timestamp ON onboarding_analytics(timestamp);
CREATE INDEX idx_onboarding_analytics_event_type ON onboarding_analytics(event_type);
CREATE INDEX idx_onboarding_analytics_step ON onboarding_analytics(step_name);
CREATE INDEX idx_onboarding_analytics_errors ON onboarding_analytics(error_type) 
    WHERE error_type IS NOT NULL;

-- Partial index for abandoned sessions
CREATE INDEX idx_onboarding_analytics_abandoned ON onboarding_analytics(session_token, timestamp)
    WHERE event_type = 'abandoned';

-- Create materialized view for funnel analysis
CREATE MATERIALIZED VIEW onboarding_funnel_stats AS
WITH session_steps AS (
    SELECT 
        s.id,
        s.variant_id,
        s.started_at::date as date,
        s.status,
        s.total_duration_seconds,
        COUNT(DISTINCT a.step_name) as steps_visited,
        COUNT(CASE WHEN a.event_type = 'validation_error' THEN 1 END) as error_count,
        COUNT(CASE WHEN a.event_type = 'help_clicked' THEN 1 END) as help_clicks
    FROM parent_onboarding_sessions s
    LEFT JOIN onboarding_analytics a ON s.session_token = a.session_token
    GROUP BY s.id, s.variant_id, s.started_at::date, s.status, s.total_duration_seconds
)
SELECT 
    date,
    variant_id,
    COUNT(*) as total_sessions,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_sessions,
    COUNT(CASE WHEN status = 'abandoned' THEN 1 END) as abandoned_sessions,
    ROUND(COUNT(CASE WHEN status = 'completed' THEN 1 END)::numeric / COUNT(*)::numeric * 100, 2) as completion_rate,
    AVG(CASE WHEN status = 'completed' THEN total_duration_seconds END) as avg_completion_time,
    AVG(steps_visited) as avg_steps_visited,
    AVG(error_count) as avg_errors_per_session,
    AVG(help_clicks) as avg_help_clicks
FROM session_steps
GROUP BY date, variant_id
ORDER BY date DESC, variant_id;

-- Create index on materialized view
CREATE INDEX idx_funnel_stats_date ON onboarding_funnel_stats(date);

-- Function to refresh funnel stats
CREATE OR REPLACE FUNCTION refresh_onboarding_funnel_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY onboarding_funnel_stats;
END;
$$ LANGUAGE plpgsql;

-- Add comments
COMMENT ON TABLE onboarding_analytics IS 'Detailed event tracking for parent onboarding flow optimization';
COMMENT ON COLUMN onboarding_analytics.event_type IS 'Type of user interaction or system event';
COMMENT ON COLUMN onboarding_analytics.field_value IS 'Sanitized field value - no PII stored';
COMMENT ON COLUMN onboarding_analytics.metadata IS 'Additional context about the event';
COMMENT ON MATERIALIZED VIEW onboarding_funnel_stats IS 'Aggregated funnel metrics for conversion analysis';

-- Enable Row Level Security
ALTER TABLE onboarding_analytics ENABLE ROW LEVEL SECURITY;

-- Note: RLS policies will be restrictive - analytics data should only be accessible to admins
