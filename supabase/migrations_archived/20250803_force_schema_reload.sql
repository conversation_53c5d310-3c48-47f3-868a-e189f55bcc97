-- ABOUTME: Force PostgREST to reload schema by notifying it
-- This triggers PostgREST to reload its schema cache

-- Method 1: Send a notification to reload the schema
NOTIFY pgrst, 'reload schema';

-- Method 2: Alternative notification
NOTIFY ddl_command_end;

-- Method 3: Touch a system catalog to trigger reload
-- This creates a harmless comment that forces a schema change event
DO $$
BEGIN
    EXECUTE 'COMMENT ON SCHEMA public IS ''Schema for public data - refreshed at ' || NOW()::text || '''';
END $$;

-- Verify cart_sessions is accessible
DO $$
BEGIN
    -- Check if the view exists
    IF EXISTS (
        SELECT 1 
        FROM information_schema.views 
        WHERE table_schema = 'public' 
        AND table_name = 'cart_sessions'
    ) THEN
        RAISE NOTICE 'public.cart_sessions view exists ✓';
    ELSE
        RAISE WARNING 'public.cart_sessions view NOT FOUND!';
    END IF;
    
    -- Check if commerce.cart_sessions exists
    IF EXISTS (
        SELECT 1 
        FROM information_schema.tables 
        WHERE table_schema = 'commerce' 
        AND table_name = 'cart_sessions'
    ) THEN
        RAISE NOTICE 'commerce.cart_sessions table exists ✓';
    ELSE
        RAISE WARNING 'commerce.cart_sessions table NOT FOUND!';
    END IF;
END $$;