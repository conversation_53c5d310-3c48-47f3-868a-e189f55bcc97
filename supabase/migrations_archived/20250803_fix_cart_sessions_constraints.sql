-- ABOUTME: Fix cart_sessions table constraints and ensure public access
-- This migration fixes the unique constraint on session_id and ensures proper public access

-- First, ensure the commerce schema and table exist
CREATE SCHEMA IF NOT EXISTS commerce;

-- Create cart_sessions table if it doesn't exist
CREATE TABLE IF NOT EXISTS commerce.cart_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id TEXT NOT NULL,
    profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    items JSONB DEFAULT '[]'::jsonb NOT NULL,
    subtotal DECIMAL(10,2) DEFAULT 0 NOT NULL,
    tax_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    shipping_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    discount_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    grand_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    coupon_codes TEXT[] DEFAULT ARRAY[]::TEXT[],
    shipping_address JSONB,
    billing_address JSONB,
    selected_shipping_option JSONB,
    notes TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days') NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Add unique constraint on session_id if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'cart_sessions_session_id_key' 
        AND conrelid = 'commerce.cart_sessions'::regclass
    ) THEN
        ALTER TABLE commerce.cart_sessions ADD CONSTRAINT cart_sessions_session_id_key UNIQUE (session_id);
    END IF;
END $$;

-- Create indexes if they don't exist
CREATE INDEX IF NOT EXISTS idx_cart_sessions_session_id ON commerce.cart_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_cart_sessions_profile_id ON commerce.cart_sessions(profile_id);
CREATE INDEX IF NOT EXISTS idx_cart_sessions_last_activity ON commerce.cart_sessions(last_activity_at);
CREATE INDEX IF NOT EXISTS idx_cart_sessions_expires_at ON commerce.cart_sessions(expires_at);

-- Drop and recreate the public view to ensure it exists
DROP VIEW IF EXISTS public.cart_sessions CASCADE;
CREATE VIEW public.cart_sessions AS
SELECT * FROM commerce.cart_sessions;

-- Grant permissions
GRANT USAGE ON SCHEMA commerce TO authenticated, anon, service_role;
GRANT SELECT ON commerce.cart_sessions TO anon, authenticated, service_role;
GRANT INSERT, UPDATE, DELETE ON commerce.cart_sessions TO anon, authenticated, service_role;
GRANT SELECT ON public.cart_sessions TO anon, authenticated, service_role;
GRANT INSERT, UPDATE, DELETE ON public.cart_sessions TO anon, authenticated, service_role;

-- Enable RLS
ALTER TABLE commerce.cart_sessions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view own carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can create carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can update own carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can delete own carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Service role has full access" ON commerce.cart_sessions;

-- Create policies
CREATE POLICY "Users can view own carts" ON commerce.cart_sessions
    FOR SELECT
    USING (
        auth.uid() = profile_id 
        OR profile_id IS NULL  -- Allow viewing guest carts by session
        OR auth.role() = 'service_role'
    );

CREATE POLICY "Users can create carts" ON commerce.cart_sessions
    FOR INSERT
    WITH CHECK (
        auth.uid() = profile_id 
        OR profile_id IS NULL  -- Allow creating guest carts
        OR auth.role() = 'service_role'
    );

CREATE POLICY "Users can update own carts" ON commerce.cart_sessions
    FOR UPDATE
    USING (
        auth.uid() = profile_id 
        OR profile_id IS NULL  -- Allow updating guest carts
        OR auth.role() = 'service_role'
    );

CREATE POLICY "Users can delete own carts" ON commerce.cart_sessions
    FOR DELETE
    USING (
        auth.uid() = profile_id 
        OR profile_id IS NULL  -- Allow deleting guest carts
        OR auth.role() = 'service_role'
    );

CREATE POLICY "Service role has full access" ON commerce.cart_sessions
    FOR ALL
    USING (auth.role() = 'service_role');

-- Create INSTEAD OF triggers for the view
CREATE OR REPLACE FUNCTION public.cart_sessions_insert()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO commerce.cart_sessions VALUES (NEW.*);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS cart_sessions_insert_trigger ON public.cart_sessions;
CREATE TRIGGER cart_sessions_insert_trigger
INSTEAD OF INSERT ON public.cart_sessions
FOR EACH ROW EXECUTE FUNCTION public.cart_sessions_insert();

CREATE OR REPLACE FUNCTION public.cart_sessions_update()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE commerce.cart_sessions SET
    session_id = NEW.session_id,
    profile_id = NEW.profile_id,
    items = NEW.items,
    subtotal = NEW.subtotal,
    tax_total = NEW.tax_total,
    shipping_total = NEW.shipping_total,
    discount_total = NEW.discount_total,
    grand_total = NEW.grand_total,
    coupon_codes = NEW.coupon_codes,
    shipping_address = NEW.shipping_address,
    billing_address = NEW.billing_address,
    selected_shipping_option = NEW.selected_shipping_option,
    notes = NEW.notes,
    metadata = NEW.metadata,
    last_activity_at = NEW.last_activity_at,
    expires_at = NEW.expires_at,
    updated_at = NOW()
  WHERE id = OLD.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS cart_sessions_update_trigger ON public.cart_sessions;
CREATE TRIGGER cart_sessions_update_trigger
INSTEAD OF UPDATE ON public.cart_sessions
FOR EACH ROW EXECUTE FUNCTION public.cart_sessions_update();

CREATE OR REPLACE FUNCTION public.cart_sessions_delete()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM commerce.cart_sessions WHERE id = OLD.id;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS cart_sessions_delete_trigger ON public.cart_sessions;
CREATE TRIGGER cart_sessions_delete_trigger
INSTEAD OF DELETE ON public.cart_sessions
FOR EACH ROW EXECUTE FUNCTION public.cart_sessions_delete();

-- Add update trigger for updated_at
CREATE OR REPLACE FUNCTION commerce.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_cart_sessions_updated_at ON commerce.cart_sessions;
CREATE TRIGGER update_cart_sessions_updated_at 
    BEFORE UPDATE ON commerce.cart_sessions 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

-- Verification
DO $$
BEGIN
    RAISE NOTICE '';
    RAISE NOTICE '=== Cart Sessions Fix Complete ===';
    RAISE NOTICE '';
    RAISE NOTICE 'Fixed:';
    RAISE NOTICE '  ✓ Added unique constraint on session_id';
    RAISE NOTICE '  ✓ Created/updated public view';
    RAISE NOTICE '  ✓ Set proper permissions for anon, authenticated, and service_role';
    RAISE NOTICE '  ✓ Added RLS policies including service role access';
    RAISE NOTICE '  ✓ Created INSTEAD OF triggers for view operations';
    RAISE NOTICE '';
    RAISE NOTICE 'Please run this migration in Supabase to fix the cart_sessions errors.';
    RAISE NOTICE '';
END $$;