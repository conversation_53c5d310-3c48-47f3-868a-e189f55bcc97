-- =========================================================
-- SHOT Pre-Evaluation Security Functions
-- =========================================================
-- This file contains all the security definer functions needed
-- to bypass RLS policies for pre-evaluation creation
-- =========================================================

-- Function to get or create a sport head (bypassing RLS)
CREATE OR REPLACE FUNCTION get_or_create_sport_head(
  p_user_id UUID,
  p_sport TEXT,
  p_display_name TEXT
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_sport_head_id UUID;
BEGIN
  -- First try to find an existing sport head
  SELECT id INTO v_sport_head_id
  FROM sport_heads
  WHERE user_id = p_user_id AND sport = p_sport
  LIMIT 1;
  
  -- If found, return it
  IF v_sport_head_id IS NOT NULL THEN
    RETURN v_sport_head_id;
  END IF;
  
  -- Otherwise create a new one
  INSERT INTO sport_heads (
    id,
    user_id,
    sport,
    display_name,
    created_at,
    updated_at,
    is_primary,
    level,
    experience_points
  ) VALUES (
    gen_random_uuid(),
    p_user_id,
    p_sport,
    p_display_name,
    now(),
    now(),
    true,
    1,
    0
  )
  RETURNING id INTO v_sport_head_id;
  
  -- Return the new ID
  RETURN v_sport_head_id;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_or_create_sport_head TO authenticated;

-- Function to create pre-evaluations (bypassing RLS)
CREATE OR REPLACE FUNCTION create_basic_pre_evaluations(
  p_event_id UUID,
  p_player_ids UUID[],
  p_team_id UUID,
  p_framework_version TEXT,
  p_week_number INTEGER,
  p_expires_at TIMESTAMPTZ
)
RETURNS SETOF UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_player_id UUID;
  v_pre_eval_id UUID;
  v_sport_head_id UUID;
BEGIN
  -- Loop through each player
  FOREACH v_player_id IN ARRAY p_player_ids
  LOOP
    -- Try to get or create sport head
    SELECT get_or_create_sport_head(
      v_player_id, 
      (SELECT sport_type FROM teams WHERE team_id = p_team_id), 
      (SELECT full_name FROM profiles WHERE id = v_player_id)
    ) INTO v_sport_head_id;
    
    -- Insert pre-evaluation for this player
    INSERT INTO pre_evaluations (
      id,
      event_id,
      player_id,
      sport_head_id,
      team_id,
      framework_version,
      week_number,
      expires_at,
      status,
      request_method,
      points_earned,
      created_at,
      responses
    ) VALUES (
      gen_random_uuid(),
      p_event_id,
      v_player_id,
      v_sport_head_id,
      p_team_id,
      p_framework_version,
      p_week_number,
      p_expires_at,
      'pending',
      'coach_initiated',
      30,
      now(),
      '{"questions":[],"summary":{}}'
    )
    RETURNING id INTO v_pre_eval_id;
    
    -- Return this ID to the result set
    RETURN NEXT v_pre_eval_id;
  END LOOP;
  
  RETURN;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION create_basic_pre_evaluations TO authenticated;

-- Function to create notifications (bypassing RLS)
CREATE OR REPLACE FUNCTION create_pre_evaluation_notifications(
  p_pre_evaluation_ids UUID[],
  p_recipient_ids UUID[],
  p_subject TEXT,
  p_body TEXT,
  p_action_url_prefix TEXT
)
RETURNS SETOF UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_pre_eval_id UUID;
  v_recipient_id UUID;
  v_notification_id UUID;
  i INTEGER;
BEGIN
  -- Loop through each pre-evaluation
  FOR i IN 1..array_length(p_pre_evaluation_ids, 1) LOOP
    v_pre_eval_id := p_pre_evaluation_ids[i];
    v_recipient_id := p_recipient_ids[i];
    
    -- Insert notification
    INSERT INTO pre_evaluation_notifications (
      id,
      pre_evaluation_id,
      notification_type,
      channel,
      recipient_id,
      subject,
      body,
      action_url,
      scheduled_for,
      status,
      created_at
    ) VALUES (
      gen_random_uuid(),
      v_pre_eval_id,
      'initial',
      'push',
      v_recipient_id,
      p_subject,
      p_body,
      p_action_url_prefix || v_pre_eval_id,
      now(),
      'pending',
      now()
    )
    RETURNING id INTO v_notification_id;
    
    -- Return this ID to the result set
    RETURN NEXT v_notification_id;
  END LOOP;
  
  RETURN;
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION create_pre_evaluation_notifications TO authenticated;
