-- ABOUTME: Complete fix for cart_sessions - recreate with proper structure
-- This migration ensures cart_sessions works correctly with all constraints and defaults

-- First, drop the existing view if it exists
DROP VIEW IF EXISTS public.cart_sessions CASCADE;

-- Ensure commerce schema exists
CREATE SCHEMA IF NOT EXISTS commerce;
GRANT USAGE ON SCHEMA commerce TO authenticated, anon, service_role;

-- Drop and recreate the cart_sessions table in commerce schema with proper defaults
DROP TABLE IF EXISTS commerce.cart_sessions CASCADE;

CREATE TABLE commerce.cart_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    session_id TEXT UNIQUE NOT NULL,
    profile_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    items JSONB DEFAULT '[]'::jsonb NOT NULL,
    subtotal DECIMAL(10,2) DEFAULT 0 NOT NULL,
    tax_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    shipping_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    discount_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    grand_total DECIMAL(10,2) DEFAULT 0 NOT NULL,
    coupon_codes TEXT[] DEFAULT ARRAY[]::TEXT[],
    shipping_address JSONB,
    billing_address JSONB,
    selected_shipping_option JSONB,
    notes TEXT,
    metadata JSONB DEFAULT '{}'::jsonb,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days') NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes
CREATE INDEX idx_cart_sessions_session_id ON commerce.cart_sessions(session_id);
CREATE INDEX idx_cart_sessions_profile_id ON commerce.cart_sessions(profile_id);
CREATE INDEX idx_cart_sessions_last_activity ON commerce.cart_sessions(last_activity_at);
CREATE INDEX idx_cart_sessions_expires_at ON commerce.cart_sessions(expires_at);

-- Create the public view
CREATE VIEW public.cart_sessions AS
SELECT * FROM commerce.cart_sessions;

-- Grant permissions on the actual table
GRANT SELECT, INSERT, UPDATE, DELETE ON commerce.cart_sessions TO anon, authenticated, service_role;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.cart_sessions TO anon, authenticated, service_role;

-- Enable RLS on the actual table
ALTER TABLE commerce.cart_sessions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view own carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can create carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can update own carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can delete own carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Service role has full access" ON commerce.cart_sessions;

-- Create RLS policies
CREATE POLICY "Users can view own carts" ON commerce.cart_sessions
    FOR SELECT
    USING (
        auth.uid() = profile_id 
        OR profile_id IS NULL
        OR auth.role() = 'service_role'
    );

CREATE POLICY "Users can create carts" ON commerce.cart_sessions
    FOR INSERT
    WITH CHECK (
        auth.uid() = profile_id 
        OR profile_id IS NULL
        OR auth.role() = 'service_role'
    );

CREATE POLICY "Users can update own carts" ON commerce.cart_sessions
    FOR UPDATE
    USING (
        auth.uid() = profile_id 
        OR profile_id IS NULL
        OR auth.role() = 'service_role'
    );

CREATE POLICY "Users can delete own carts" ON commerce.cart_sessions
    FOR DELETE
    USING (
        auth.uid() = profile_id 
        OR profile_id IS NULL
        OR auth.role() = 'service_role'
    );

-- Create update trigger for updated_at
CREATE OR REPLACE FUNCTION commerce.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_cart_sessions_updated_at ON commerce.cart_sessions;
CREATE TRIGGER update_cart_sessions_updated_at 
    BEFORE UPDATE ON commerce.cart_sessions 
    FOR EACH ROW EXECUTE FUNCTION commerce.update_updated_at_column();

-- Create INSTEAD OF triggers for the view
CREATE OR REPLACE FUNCTION public.cart_sessions_insert()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO commerce.cart_sessions (
    id,
    session_id,
    profile_id,
    items,
    subtotal,
    tax_total,
    shipping_total,
    discount_total,
    grand_total,
    coupon_codes,
    shipping_address,
    billing_address,
    selected_shipping_option,
    notes,
    metadata,
    last_activity_at,
    expires_at,
    created_at,
    updated_at
  ) VALUES (
    COALESCE(NEW.id, gen_random_uuid()),
    NEW.session_id,
    NEW.profile_id,
    COALESCE(NEW.items, '[]'::jsonb),
    COALESCE(NEW.subtotal, 0),
    COALESCE(NEW.tax_total, 0),
    COALESCE(NEW.shipping_total, 0),
    COALESCE(NEW.discount_total, 0),
    COALESCE(NEW.grand_total, 0),
    COALESCE(NEW.coupon_codes, ARRAY[]::TEXT[]),
    NEW.shipping_address,
    NEW.billing_address,
    NEW.selected_shipping_option,
    NEW.notes,
    COALESCE(NEW.metadata, '{}'::jsonb),
    COALESCE(NEW.last_activity_at, NOW()),
    COALESCE(NEW.expires_at, NOW() + INTERVAL '30 days'),
    COALESCE(NEW.created_at, NOW()),
    COALESCE(NEW.updated_at, NOW())
  )
  RETURNING * INTO NEW;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS cart_sessions_insert_trigger ON public.cart_sessions;
CREATE TRIGGER cart_sessions_insert_trigger
INSTEAD OF INSERT ON public.cart_sessions
FOR EACH ROW EXECUTE FUNCTION public.cart_sessions_insert();

CREATE OR REPLACE FUNCTION public.cart_sessions_update()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE commerce.cart_sessions SET
    session_id = NEW.session_id,
    profile_id = NEW.profile_id,
    items = NEW.items,
    subtotal = NEW.subtotal,
    tax_total = NEW.tax_total,
    shipping_total = NEW.shipping_total,
    discount_total = NEW.discount_total,
    grand_total = NEW.grand_total,
    coupon_codes = NEW.coupon_codes,
    shipping_address = NEW.shipping_address,
    billing_address = NEW.billing_address,
    selected_shipping_option = NEW.selected_shipping_option,
    notes = NEW.notes,
    metadata = NEW.metadata,
    last_activity_at = NEW.last_activity_at,
    expires_at = NEW.expires_at,
    updated_at = NOW()
  WHERE id = OLD.id
  RETURNING * INTO NEW;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS cart_sessions_update_trigger ON public.cart_sessions;
CREATE TRIGGER cart_sessions_update_trigger
INSTEAD OF UPDATE ON public.cart_sessions
FOR EACH ROW EXECUTE FUNCTION public.cart_sessions_update();

CREATE OR REPLACE FUNCTION public.cart_sessions_delete()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM commerce.cart_sessions WHERE id = OLD.id;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

DROP TRIGGER IF EXISTS cart_sessions_delete_trigger ON public.cart_sessions;
CREATE TRIGGER cart_sessions_delete_trigger
INSTEAD OF DELETE ON public.cart_sessions
FOR EACH ROW EXECUTE FUNCTION public.cart_sessions_delete();

-- Test the setup
DO $$
DECLARE
    test_id UUID;
    test_session_id TEXT;
BEGIN
    test_session_id := 'test_' || extract(epoch from now())::text;
    
    -- Test insert
    INSERT INTO public.cart_sessions (session_id) 
    VALUES (test_session_id)
    RETURNING id INTO test_id;
    
    RAISE NOTICE 'Test insert successful, ID: %', test_id;
    
    -- Test upsert
    INSERT INTO public.cart_sessions (session_id, subtotal, grand_total) 
    VALUES (test_session_id, 10, 10)
    ON CONFLICT (session_id) 
    DO UPDATE SET subtotal = 20, grand_total = 20;
    
    RAISE NOTICE 'Test upsert successful';
    
    -- Cleanup
    DELETE FROM public.cart_sessions WHERE id = test_id;
    RAISE NOTICE 'Test cleanup successful';
    
    RAISE NOTICE '';
    RAISE NOTICE '✅ cart_sessions is now properly configured!';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Test failed: %', SQLERRM;
END $$;