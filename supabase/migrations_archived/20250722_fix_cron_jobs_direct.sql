-- Fix the cron jobs by using direct URLs instead of configuration parameters
-- This is a simpler approach that works with Supabase hosted instances

-- First, unschedule existing jobs if they exist
DO $$
BEGIN
  -- Try to unschedule, ignore errors if jobs don't exist
  PERFORM cron.unschedule('sync-pulse-content');
  PERFORM cron.unschedule('retry-failed-content-syncs');
EXCEPTION WHEN OTHERS THEN
  -- Ignore errors if jobs don't exist
  NULL;
END $$;

-- Create the main sync job with direct URL
SELECT cron.schedule(
  'sync-pulse-content',
  '*/15 * * * *', -- every 15 minutes
  $$
  SELECT net.http_post(
    url := 'https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/content-sync-v2',
    headers := jsonb_build_object(
      'Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y',
      'Content-Type', 'application/json'
    ),
    body := jsonb_build_object(
      'source', 'cron',
      'timestamp', now()
    )
  );
  $$
);

-- Update the retry function to use direct URLs
CREATE OR REPLACE FUNCTION content.retry_failed_syncs()
RETURNS void
LANGUAGE plpgsql
AS $$
DECLARE
  failed_sync RECORD;
BEGIN
  -- Find failed syncs from the last hour
  FOR failed_sync IN 
    SELECT id, metadata
    FROM content.pulse_sync_log
    WHERE sync_status = 'failed'
      AND sync_started_at > NOW() - INTERVAL '1 hour'
      AND COALESCE((metadata->>'retry_count')::int, 0) < 3
  LOOP
    -- Trigger a retry with direct URL
    PERFORM net.http_post(
      url := 'https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/content-sync-v2',
      headers := jsonb_build_object(
        'Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y',
        'Content-Type', 'application/json'
      ),
      body := jsonb_build_object(
        'source', 'retry',
        'retry_count', COALESCE((failed_sync.metadata->>'retry_count')::int, 0) + 1,
        'original_sync_id', failed_sync.id
      )
    );
  END LOOP;
END;
$$;

-- Recreate the retry cron job
SELECT cron.schedule(
  'retry-failed-content-syncs',
  '*/5 * * * *', -- every 5 minutes
  'SELECT content.retry_failed_syncs();'
);

-- Create a function to manually trigger sync (useful for testing)
CREATE OR REPLACE FUNCTION content.manual_sync()
RETURNS json
LANGUAGE plpgsql
AS $$
DECLARE
  result json;
BEGIN
  SELECT net.http_post(
    url := 'https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/content-sync-v2',
    headers := jsonb_build_object(
      'Authorization', 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTcyODU1MTEyMCwiZXhwIjoyMDQ0MTI3MTIwfQ.i1QjqGoYAEfp-_DA5-wj-Rn8iWwfmvq0gwsJox1-z7Y',
      'Content-Type', 'application/json'
    ),
    body := jsonb_build_object(
      'source', 'manual',
      'timestamp', now()
    )
  ) INTO result;
  
  RETURN result;
END;
$$;

-- Create a view to check cron job status
CREATE OR REPLACE VIEW content.cron_job_status AS
SELECT 
  jobname,
  schedule,
  active,
  command
FROM cron.job
WHERE jobname IN ('sync-pulse-content', 'retry-failed-content-syncs')
ORDER BY jobname;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION content.manual_sync() TO authenticated;
GRANT SELECT ON content.cron_job_status TO authenticated;

-- Add helpful comments
COMMENT ON FUNCTION content.manual_sync() IS 'Manually trigger a Pulse content sync';
COMMENT ON VIEW content.cron_job_status IS 'View the status of Pulse content sync cron jobs';