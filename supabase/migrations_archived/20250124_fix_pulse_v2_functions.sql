-- ABOUTME: Fix ambiguous column reference in get_pulse_articles_with_stats function
-- This migration fixes the article_id ambiguity error in the function

-- Drop and recreate the function with proper column alias
DROP FUNCTION IF EXISTS content.get_pulse_articles_with_stats();
DROP FUNCTION IF EXISTS public.get_pulse_articles_with_stats();

-- Recreate the function in content schema with fixed column alias
CREATE OR REPLACE FUNCTION content.get_pulse_articles_with_stats()
RETURNS TABLE (
  article_id UUID,
  title TEXT,
  pillar TEXT,
  desk TEXT,
  desk_emoji TEXT,
  narrative TEXT,
  url TEXT,
  source TEXT,
  pub_date TIMESTAMP,
  entities JSONB,
  social_post TEXT,
  story_type JSONB,
  image_url TEXT,
  like_count BIGINT,
  bookmark_count BIGINT,
  share_count BIGINT,
  user_liked BOOLEAN,
  user_bookmarked BOOLEAN,
  user_shared BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pa.id AS article_id,  -- Fixed: explicitly alias as article_id
    pa.title,
    pa.pillar,
    pa.desk,
    pa.desk_emoji,
    pa.narrative,
    pa.url,
    pa.source,
    pa.pub_date,
    pa.entities,
    pa.social_post,
    pa.story_type,
    pa.image_url,
    COUNT(DISTINCT CASE WHEN pi.action_type = 'like' THEN pi.profile_id END)::BIGINT as like_count,
    COUNT(DISTINCT CASE WHEN pi.action_type = 'bookmark' THEN pi.profile_id END)::BIGINT as bookmark_count,
    COUNT(DISTINCT CASE WHEN pi.action_type = 'share' THEN pi.profile_id END)::BIGINT as share_count,
    EXISTS(
      SELECT 1 FROM content.pulse_interactions pi2 
      WHERE pi2.article_id = pa.id 
      AND pi2.profile_id = auth.uid() 
      AND pi2.action_type = 'like'
    ) as user_liked,
    EXISTS(
      SELECT 1 FROM content.pulse_interactions pi2 
      WHERE pi2.article_id = pa.id 
      AND pi2.profile_id = auth.uid() 
      AND pi2.action_type = 'bookmark'
    ) as user_bookmarked,
    EXISTS(
      SELECT 1 FROM content.pulse_interactions pi2 
      WHERE pi2.article_id = pa.id 
      AND pi2.profile_id = auth.uid() 
      AND pi2.action_type = 'share'
    ) as user_shared
  FROM content.pulse_articles pa
  LEFT JOIN content.pulse_interactions pi ON pa.id = pi.article_id
  WHERE pa.is_public = true
  GROUP BY pa.id
  ORDER BY pa.pub_date DESC;
END;
$$;

-- Recreate the public wrapper function
CREATE OR REPLACE FUNCTION public.get_pulse_articles_with_stats()
RETURNS TABLE (
  article_id UUID,
  title TEXT,
  pillar TEXT,
  desk TEXT,
  desk_emoji TEXT,
  narrative TEXT,
  url TEXT,
  source TEXT,
  pub_date TIMESTAMP,
  entities JSONB,
  social_post TEXT,
  story_type JSONB,
  image_url TEXT,
  like_count BIGINT,
  bookmark_count BIGINT,
  share_count BIGINT,
  user_liked BOOLEAN,
  user_bookmarked BOOLEAN,
  user_shared BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY SELECT * FROM content.get_pulse_articles_with_stats();
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_pulse_articles_with_stats() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION content.get_pulse_articles_with_stats() TO anon, authenticated;