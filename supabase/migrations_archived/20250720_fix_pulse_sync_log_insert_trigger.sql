-- Fix the pulse_sync_log insert trigger to properly handle ID generation

-- Drop the existing trigger function
DROP FUNCTION IF EXISTS public.pulse_sync_log_insert() CASCADE;

-- Create the corrected trigger function that properly handles default values
CREATE OR REPLACE FUNCTION public.pulse_sync_log_insert()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert into content.pulse_sync_log, letting the database handle defaults
  INSERT INTO content.pulse_sync_log (
    id,
    sync_started_at,
    sync_completed_at,
    articles_fetched,
    articles_created,
    articles_updated,
    error_message,
    sync_status,
    metadata
  ) VALUES (
    COALESCE(NEW.id, gen_random_uuid()),
    COALESCE(NEW.sync_started_at, NOW()),
    NEW.sync_completed_at,
    COALESCE(NEW.articles_fetched, 0),
    COALESCE(NEW.articles_created, 0),
    COALESCE(NEW.articles_updated, 0),
    NEW.error_message,
    NEW.sync_status,
    COALESCE(NEW.metadata, '{}'::jsonb)
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger
CREATE TRIGGER pulse_sync_log_insert_trigger
INSTEAD OF INSERT ON public.pulse_sync_log
FOR EACH ROW EXECUTE FUNCTION public.pulse_sync_log_insert();

-- Also fix the pulse_articles insert trigger for consistency
DROP FUNCTION IF EXISTS public.pulse_articles_insert() CASCADE;

CREATE OR REPLACE FUNCTION public.pulse_articles_insert()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert into content.pulse_articles, letting the database handle defaults
  INSERT INTO content.pulse_articles (
    id,
    external_id,
    title,
    pillar,
    desk,
    desk_emoji,
    narrative,
    url,
    source,
    pub_date,
    created_at,
    updated_at,
    sync_status,
    metadata,
    is_public,
    api_visibility
  ) VALUES (
    COALESCE(NEW.id, gen_random_uuid()),
    NEW.external_id,
    NEW.title,
    NEW.pillar,
    NEW.desk,
    NEW.desk_emoji,
    NEW.narrative,
    NEW.url,
    NEW.source,
    NEW.pub_date,
    COALESCE(NEW.created_at, NOW()),
    COALESCE(NEW.updated_at, NOW()),
    COALESCE(NEW.sync_status, 'active'),
    COALESCE(NEW.metadata, '{}'::jsonb),
    COALESCE(NEW.is_public, true),
    COALESCE(NEW.api_visibility, ARRAY['public']::text[])
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger
CREATE TRIGGER pulse_articles_insert_trigger
INSTEAD OF INSERT ON public.pulse_articles
FOR EACH ROW EXECUTE FUNCTION public.pulse_articles_insert();