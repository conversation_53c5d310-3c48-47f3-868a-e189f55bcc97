-- ABOUTME: Fix pulse_interactions trigger to properly generate ID
-- The INSTEAD OF INSERT trigger needs to handle ID generation

-- Drop existing trigger and function
DROP TRIGGER IF EXISTS pulse_interactions_insert_trigger ON public.pulse_interactions;
DROP FUNCTION IF EXISTS public.pulse_interactions_insert();

-- Recreate the function with proper ID handling
CREATE OR REPLACE FUNCTION public.pulse_interactions_insert()
RETURNS TRIGGER AS $$
BEGIN
  -- If ID is not provided, generate one
  IF NEW.id IS NULL THEN
    NEW.id := gen_random_uuid();
  END IF;
  
  -- Insert into the content schema table
  INSERT INTO content.pulse_interactions (id, profile_id, article_id, action_type, created_at)
  VALUES (NEW.id, NEW.profile_id, NEW.article_id, NEW.action_type, COALESCE(NEW.created_at, NOW()));
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Recreate the trigger
CREATE TRIGGER pulse_interactions_insert_trigger
INSTEAD OF INSERT ON public.pulse_interactions
FOR EACH ROW EXECUTE FUNCTION public.pulse_interactions_insert();

-- Also fix the pulse_follows trigger
DROP TRIGGER IF EXISTS pulse_follows_insert_trigger ON public.pulse_follows;
DROP FUNCTION IF EXISTS public.pulse_follows_insert();

CREATE OR REPLACE FUNCTION public.pulse_follows_insert()
RETURNS TRIGGER AS $$
BEGIN
  -- If ID is not provided, generate one
  IF NEW.id IS NULL THEN
    NEW.id := gen_random_uuid();
  END IF;
  
  -- Insert into the content schema table
  INSERT INTO content.pulse_follows (id, profile_id, tag_name, tag_type, created_at)
  VALUES (NEW.id, NEW.profile_id, NEW.tag_name, COALESCE(NEW.tag_type, 'general'), COALESCE(NEW.created_at, NOW()));
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER pulse_follows_insert_trigger
INSTEAD OF INSERT ON public.pulse_follows
FOR EACH ROW EXECUTE FUNCTION public.pulse_follows_insert();