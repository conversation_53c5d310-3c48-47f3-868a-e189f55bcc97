-- Migration: Enhance Profiles Table for Parent Onboarding
-- Description: Add child-specific fields and onboarding tracking to profiles table
-- Phase: 2 - Parent Quick Onboarding System
-- Created: 2025-01-29

-- Add columns to profiles table for Phase 2
ALTER TABLE profiles
    -- SportHead reference
    ADD COLUMN IF NOT EXISTS primary_sport_head_id UUID REFERENCES sport_heads(id),
    
    -- Onboarding tracking
    ADD COLUMN IF NOT EXISTS has_completed_onboarding BOOLEAN DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS onboarding_completed_at TIMESTAMPTZ,
    ADD COLUMN IF NOT EXISTS onboarding_method VARCHAR(50),
    ADD COLUMN IF NOT EXISTS requires_profile_completion BOOLEAN DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS profile_completion_reminder_sent BOOLEAN DEFAULT FALSE,
    
    -- Child-specific fields (migrated from children table)
    ADD COLUMN IF NOT EXISTS allergies TEXT[] DEFAULT ARRAY[]::TEXT[],
    ADD COLUMN IF NOT EXISTS medical_conditions TEXT[] DEFAULT ARRAY[]::TEXT[],
    ADD COLUMN IF NOT EXISTS emergency_contact JSON<PERSON>,
    ADD COLUMN IF NOT EXISTS photography_allowed BOOLEAN DEFAULT TRUE,
    ADD COLUMN IF NOT EXISTS parent_consent_given BOOLEAN DEFAULT FALSE,
    ADD COLUMN IF NOT EXISTS parent_consent_date DATE,
    ADD COLUMN IF NOT EXISTS parent_consent_version VARCHAR(20),
    
    -- Additional child safety fields
    ADD COLUMN IF NOT EXISTS special_instructions TEXT,
    ADD COLUMN IF NOT EXISTS dietary_restrictions TEXT[],
    ADD COLUMN IF NOT EXISTS emergency_medical_info JSONB;

-- Add check constraint for emergency contact structure
ALTER TABLE profiles 
    ADD CONSTRAINT emergency_contact_structure 
    CHECK (
        emergency_contact IS NULL OR (
            emergency_contact ? 'name' AND 
            emergency_contact ? 'phone' AND 
            emergency_contact ? 'relationship'
        )
    );

-- Create indexes for new columns
CREATE INDEX IF NOT EXISTS idx_profiles_primary_sport_head 
    ON profiles(primary_sport_head_id);
CREATE INDEX IF NOT EXISTS idx_profiles_parent_id 
    ON profiles(parent_id) 
    WHERE parent_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_profiles_onboarding_status 
    ON profiles(has_completed_onboarding) 
    WHERE has_completed_onboarding = FALSE;
CREATE INDEX IF NOT EXISTS idx_profiles_requires_completion 
    ON profiles(requires_profile_completion) 
    WHERE requires_profile_completion = TRUE;

-- Add comments for new columns
COMMENT ON COLUMN profiles.primary_sport_head_id IS 'Primary SportHead for this user - required for all non-parent users';
COMMENT ON COLUMN profiles.has_completed_onboarding IS 'Whether user has completed initial onboarding flow';
COMMENT ON COLUMN profiles.onboarding_method IS 'How the user was onboarded (parent_quick, coach_invite, self_signup, etc)';
COMMENT ON COLUMN profiles.allergies IS 'List of known allergies for child safety';
COMMENT ON COLUMN profiles.medical_conditions IS 'List of medical conditions coaches should be aware of';
COMMENT ON COLUMN profiles.emergency_contact IS 'JSON object with name, phone, and relationship fields';
COMMENT ON COLUMN profiles.parent_consent_given IS 'Whether parent has given consent for child participation';
COMMENT ON COLUMN profiles.parent_consent_version IS 'Version of terms/consent form agreed to';

-- Function to migrate data from children table if it exists
CREATE OR REPLACE FUNCTION migrate_children_to_profiles()
RETURNS void AS $$
DECLARE
    v_children_exists BOOLEAN;
BEGIN
    -- Check if children table exists
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'children'
    ) INTO v_children_exists;
    
    IF v_children_exists THEN
        -- Update existing child profiles with data from children table
        UPDATE profiles p
        SET 
            allergies = c.allergies,
            medical_conditions = c.medical_conditions,
            emergency_contact = c.emergency_contact,
            photography_allowed = c.photography_allowed,
            updated_at = NOW()
        FROM children c
        WHERE c.profile_id = p.parent_id
        AND p.full_name = c.name
        AND p.date_of_birth = c.date_of_birth;
        
        -- Log migration
        RAISE NOTICE 'Migrated % child records to profiles', (SELECT COUNT(*) FROM children);
    ELSE
        RAISE NOTICE 'Children table does not exist, skipping migration';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Run migration if needed
SELECT migrate_children_to_profiles();

-- Function to ensure profile has primary SportHead
CREATE OR REPLACE FUNCTION ensure_primary_sporthead()
RETURNS TRIGGER AS $$
BEGIN
    -- Only enforce for non-parent, non-admin users
    IF NEW.user_type NOT IN ('parent', 'admin') AND NEW.primary_sport_head_id IS NULL THEN
        -- Try to find existing primary SportHead
        SELECT id INTO NEW.primary_sport_head_id
        FROM sport_heads
        WHERE user_id = NEW.id
        AND is_primary = TRUE
        LIMIT 1;
        
        -- If still null and this is a player, we'll create one in app logic
        -- Don't block the update here
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to ensure SportHead consistency
CREATE TRIGGER ensure_profile_sporthead
    BEFORE INSERT OR UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION ensure_primary_sporthead();

-- Update existing profiles to set primary_sport_head_id
UPDATE profiles p
SET primary_sport_head_id = (
    SELECT id 
    FROM sport_heads sh 
    WHERE sh.user_id = p.id 
    AND sh.is_primary = TRUE
    LIMIT 1
)
WHERE p.primary_sport_head_id IS NULL
AND p.user_type NOT IN ('parent', 'admin');

-- Set onboarding status for existing users
UPDATE profiles
SET 
    has_completed_onboarding = TRUE,
    onboarding_method = 'legacy_user'
WHERE created_at < '2025-01-28'
AND has_completed_onboarding IS FALSE;
