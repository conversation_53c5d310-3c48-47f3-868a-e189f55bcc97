-- Migration: Create Child Detection Cache Table
-- Description: Cache for smart child detection to avoid duplicate profiles
-- Phase: 2 - Parent Quick Onboarding System
-- Created: 2025-01-28

-- Install fuzzy string matching extension if not exists
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Create child_detection_cache table
CREATE TABLE IF NOT EXISTS child_detection_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    parent_id UUID REFERENCES profiles(id),
    search_key VARCHAR(255), -- normalized name + dob for quick lookup
    child_id UUID REFERENCES profiles(id),
    sport_heads JSONB DEFAULT '[]'::jsonb, -- Array of sport head info
    teams JSONB DEFAULT '[]'::jsonb, -- Array of team memberships
    confidence_score DECIMAL(3,2),
    match_reasons TEXT[],
    
    -- Cache management
    last_accessed TIMESTAMPTZ DEFAULT NOW(),
    access_count INTEGER DEFAULT 1,
    stale BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ DEFAULT NOW() + INTERVAL '30 days'
);

-- Create indexes for performance
CREATE INDEX idx_child_detection_search ON child_detection_cache(parent_id, search_key);
CREATE INDEX idx_child_detection_child ON child_detection_cache(child_id);
CREATE INDEX idx_child_detection_expires ON child_detection_cache(expires_at);
CREATE INDEX idx_child_detection_stale ON child_detection_cache(stale) 
    WHERE stale = TRUE;

-- Create trigram index for fuzzy matching on search_key
CREATE INDEX idx_child_detection_search_trgm ON child_detection_cache 
    USING gin (search_key gin_trgm_ops);

-- Add comments
COMMENT ON TABLE child_detection_cache IS 'Cache for smart child detection during parent onboarding';
COMMENT ON COLUMN child_detection_cache.search_key IS 'Normalized combination of child name and DOB for quick lookups';
COMMENT ON COLUMN child_detection_cache.confidence_score IS 'Similarity score from fuzzy matching (0.0 to 1.0)';
COMMENT ON COLUMN child_detection_cache.match_reasons IS 'Array of reasons why this was considered a match';
COMMENT ON COLUMN child_detection_cache.stale IS 'Flag to indicate cache needs refresh';

-- Function to generate search key
CREATE OR REPLACE FUNCTION generate_child_search_key(
    p_name TEXT,
    p_dob DATE
) RETURNS TEXT AS $$
BEGIN
    -- Normalize name: lowercase, remove special chars, trim spaces
    RETURN LOWER(TRIM(regexp_replace(p_name, '[^a-zA-Z0-9\s]', '', 'g'))) || 
           '_' || 
           TO_CHAR(p_dob, 'YYYY-MM-DD');
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to invalidate cache when child data changes
CREATE OR REPLACE FUNCTION invalidate_child_cache()
RETURNS TRIGGER AS $$
BEGIN
    -- Mark cache entries as stale when child profile changes
    UPDATE child_detection_cache
    SET stale = TRUE
    WHERE child_id = NEW.id
    OR (OLD IS NOT NULL AND child_id = OLD.id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to invalidate cache on profile changes
CREATE TRIGGER invalidate_cache_on_profile_change
    AFTER UPDATE OF full_name, date_of_birth ON profiles
    FOR EACH ROW
    WHEN (NEW.user_type = 'player')
    EXECUTE FUNCTION invalidate_child_cache();

-- Create trigger to invalidate cache on sport head changes
CREATE TRIGGER invalidate_cache_on_sporthead_change
    AFTER INSERT OR UPDATE OR DELETE ON sport_heads
    FOR EACH ROW
    EXECUTE FUNCTION invalidate_child_cache();

-- Cleanup function for expired cache entries
CREATE OR REPLACE FUNCTION cleanup_expired_child_cache()
RETURNS void AS $$
BEGIN
    DELETE FROM child_detection_cache
    WHERE expires_at < NOW()
    OR (stale = TRUE AND last_accessed < NOW() - INTERVAL '7 days');
END;
$$ LANGUAGE plpgsql;

-- Enable Row Level Security
ALTER TABLE child_detection_cache ENABLE ROW LEVEL SECURITY;
