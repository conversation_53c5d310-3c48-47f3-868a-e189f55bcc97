-- Create dedicated content schema for Pulse and future content services
CREATE SCHEMA IF NOT EXISTS content;

-- Set search path to include content schema
ALTER DATABASE postgres SET search_path TO public, content;

-- Pulse articles table with full-text search support
CREATE TABLE content.pulse_articles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  external_id TEXT UNIQUE,
  title TEXT NOT NULL,
  pillar TEXT NOT NULL CHECK (pillar IN ('TAKE YOUR SHOT', 'OWN IT', 'MAKE IMPACT', 'OTHER')),
  desk TEXT NOT NULL,
  desk_emoji TEXT,
  narrative TEXT NOT NULL,
  url TEXT NOT NULL,
  source TEXT,
  pub_date TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  sync_status TEXT DEFAULT 'active' CHECK (sync_status IN ('active', 'archived', 'error')),
  metadata JSONB DEFAULT '{}',
  is_public BOOLEAN DEFAULT true,
  api_visibility TEXT[] DEFAULT ARRAY['public']
);

-- Sync log for tracking API calls and debugging
CREATE TABLE content.pulse_sync_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sync_started_at TIMESTAMP NOT NULL DEFAULT NOW(),
  sync_completed_at TIMESTAMP,
  articles_fetched INTEGER DEFAULT 0,
  articles_created INTEGER DEFAULT 0,
  articles_updated INTEGER DEFAULT 0,
  error_message TEXT,
  sync_status TEXT CHECK (sync_status IN ('running', 'completed', 'failed')),
  metadata JSONB DEFAULT '{}'
);

-- API Keys table for external service authentication (future use)
CREATE TABLE content.api_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  key_hash TEXT NOT NULL UNIQUE,
  service_name TEXT NOT NULL,
  description TEXT,
  permissions JSONB DEFAULT '["read"]',
  rate_limit_per_hour INTEGER DEFAULT 1000,
  created_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP,
  last_used_at TIMESTAMP,
  is_active BOOLEAN DEFAULT true,
  metadata JSONB DEFAULT '{}'
);

-- Widget configurations (future use)
CREATE TABLE content.widget_configurations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  widget_key TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  default_config JSONB NOT NULL,
  allowed_domains TEXT[],
  is_public BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_content_pulse_title_search ON content.pulse_articles 
  USING gin(to_tsvector('english', title));
CREATE INDEX idx_content_pulse_narrative_search ON content.pulse_articles 
  USING gin(to_tsvector('english', narrative));
CREATE INDEX idx_content_pulse_pillar ON content.pulse_articles(pillar);
CREATE INDEX idx_content_pulse_pub_date ON content.pulse_articles(pub_date DESC);
CREATE INDEX idx_content_pulse_created_at ON content.pulse_articles(created_at DESC);
CREATE INDEX idx_content_pulse_desk ON content.pulse_articles(desk);
CREATE INDEX idx_content_pulse_external_id ON content.pulse_articles(external_id);

-- Enable RLS on content tables
ALTER TABLE content.pulse_articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE content.pulse_sync_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE content.api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE content.widget_configurations ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Public can read pulse articles
CREATE POLICY "Public can read pulse articles" ON content.pulse_articles
  FOR SELECT USING (is_public = true);

-- Only service role can modify articles
CREATE POLICY "Service role can manage pulse articles" ON content.pulse_articles
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Sync log only for service role
CREATE POLICY "Service role can manage sync logs" ON content.pulse_sync_log
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Admins can view sync logs
CREATE POLICY "Admins can view sync logs" ON content.pulse_sync_log
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND 'superadmin' = ANY(profiles.privileges)
    )
  );

-- API keys management for service role only
CREATE POLICY "Service role can manage API keys" ON content.api_keys
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Widget configurations public read
CREATE POLICY "Public can read widget configurations" ON content.widget_configurations
  FOR SELECT USING (is_public = true);

CREATE POLICY "Service role can manage widget configurations" ON content.widget_configurations
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Helper functions

-- Function to search pulse articles
CREATE OR REPLACE FUNCTION content.search_pulse_articles(search_query TEXT)
RETURNS SETOF content.pulse_articles
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM content.pulse_articles
  WHERE 
    to_tsvector('english', title || ' ' || narrative) @@ plainto_tsquery('english', search_query)
    AND is_public = true
  ORDER BY 
    ts_rank(to_tsvector('english', title || ' ' || narrative), plainto_tsquery('english', search_query)) DESC,
    pub_date DESC;
END;
$$;

-- Function to get latest sync status
CREATE OR REPLACE FUNCTION content.get_latest_sync_status()
RETURNS TABLE (
  status TEXT,
  started_at TIMESTAMP,
  completed_at TIMESTAMP,
  articles_synced INTEGER,
  error_message TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    sync_status,
    sync_started_at,
    sync_completed_at,
    COALESCE(articles_created, 0) + COALESCE(articles_updated, 0),
    error_message
  FROM content.pulse_sync_log
  ORDER BY sync_started_at DESC
  LIMIT 1;
END;
$$;

-- Add comments for documentation
COMMENT ON SCHEMA content IS 'Content management system for Pulse and other content types';
COMMENT ON TABLE content.pulse_articles IS 'Stores Pulse feed articles synced from Google Sheets';
COMMENT ON TABLE content.pulse_sync_log IS 'Tracks sync operations for monitoring and debugging';
COMMENT ON TABLE content.api_keys IS 'API keys for external service authentication';
COMMENT ON TABLE content.widget_configurations IS 'Configuration for embeddable widgets';