-- ABOUTME: Query to check what commerce tables actually exist in the database
-- Run this query first to see the current state before creating migration

-- Check all tables in public schema that might be commerce-related
SELECT 
    schemaname,
    tablename 
FROM pg_tables 
WHERE schemaname IN ('public', 'commerce')
AND (
    tablename LIKE '%cart%' 
    OR tablename LIKE '%product%' 
    OR tablename LIKE '%order%' 
    OR tablename LIKE '%bc_%'
    OR tablename LIKE '%commerce%'
    OR tablename LIKE '%shop%'
    OR tablename LIKE '%customer%'
    OR tablename LIKE '%loyalty%'
    OR tablename LIKE '%subscription%'
    OR tablename LIKE '%wishlist%'
    OR tablename LIKE '%drop%'
    OR tablename LIKE '%guardian%'
    OR tablename LIKE '%notification%'
    OR tablename LIKE '%stripe%'
)
ORDER BY schemaname, tablename;

-- Also check if commerce schema exists
SELECT schema_name 
FROM information_schema.schemata 
WHERE schema_name = 'commerce';

-- Check columns of any existing cart_sessions table
SELECT 
    table_schema,
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns
WHERE table_name = 'cart_sessions'
ORDER BY ordinal_position;