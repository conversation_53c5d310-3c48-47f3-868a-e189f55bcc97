-- ABOUTME: Force PostgREST to recognize cart_sessions
-- Multiple methods to trigger schema reload

-- Method 1: Alter the view to force a schema change event
DROP VIEW IF EXISTS public.cart_sessions CASCADE;

-- Recreate the view
CREATE VIEW public.cart_sessions AS
SELECT * FROM commerce.cart_sessions;

-- Method 2: Grant permissions again (this often triggers reload)
GRANT SELECT, INSERT, UPDATE, DELETE ON public.cart_sessions TO anon, authenticated;
GRANT USAGE ON SCHEMA public TO anon, authenticated;

-- Method 3: Create a dummy function then drop it (forces schema change)
CREATE OR REPLACE FUNCTION public._force_schema_reload()
RETURNS void AS $$
BEGIN
    -- Do nothing, just force a schema change
END;
$$ LANGUAGE plpgsql;

DROP FUNCTION public._force_schema_reload();

-- Method 4: Recreate the INSTEAD OF triggers
-- Drop existing triggers
DROP TRIGGER IF EXISTS cart_sessions_insert_trigger ON public.cart_sessions;
DROP TRIGGER IF EXISTS cart_sessions_update_trigger ON public.cart_sessions;
DROP TRIGGER IF EXISTS cart_sessions_delete_trigger ON public.cart_sessions;

-- Recreate insert trigger
CREATE OR REPLACE FUNCTION public.cart_sessions_insert()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO commerce.cart_sessions VALUES (NEW.*)
  ON CONFLICT (session_id) DO UPDATE SET
    profile_id = EXCLUDED.profile_id,
    items = EXCLUDED.items,
    subtotal = EXCLUDED.subtotal,
    tax_total = EXCLUDED.tax_total,
    shipping_total = EXCLUDED.shipping_total,
    discount_total = EXCLUDED.discount_total,
    grand_total = EXCLUDED.grand_total,
    coupon_codes = EXCLUDED.coupon_codes,
    shipping_address = EXCLUDED.shipping_address,
    billing_address = EXCLUDED.billing_address,
    selected_shipping_option = EXCLUDED.selected_shipping_option,
    notes = EXCLUDED.notes,
    metadata = EXCLUDED.metadata,
    last_activity_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER cart_sessions_insert_trigger
INSTEAD OF INSERT ON public.cart_sessions
FOR EACH ROW EXECUTE FUNCTION public.cart_sessions_insert();

-- Update trigger
CREATE OR REPLACE FUNCTION public.cart_sessions_update()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE commerce.cart_sessions SET
    session_id = NEW.session_id,
    profile_id = NEW.profile_id,
    items = NEW.items,
    subtotal = NEW.subtotal,
    tax_total = NEW.tax_total,
    shipping_total = NEW.shipping_total,
    discount_total = NEW.discount_total,
    grand_total = NEW.grand_total,
    coupon_codes = NEW.coupon_codes,
    shipping_address = NEW.shipping_address,
    billing_address = NEW.billing_address,
    selected_shipping_option = NEW.selected_shipping_option,
    notes = NEW.notes,
    metadata = NEW.metadata,
    last_activity_at = NEW.last_activity_at,
    expires_at = NEW.expires_at,
    updated_at = NOW()
  WHERE id = OLD.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER cart_sessions_update_trigger
INSTEAD OF UPDATE ON public.cart_sessions
FOR EACH ROW EXECUTE FUNCTION public.cart_sessions_update();

-- Delete trigger
CREATE OR REPLACE FUNCTION public.cart_sessions_delete()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM commerce.cart_sessions WHERE id = OLD.id;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER cart_sessions_delete_trigger
INSTEAD OF DELETE ON public.cart_sessions
FOR EACH ROW EXECUTE FUNCTION public.cart_sessions_delete();

-- Method 5: Send notifications
NOTIFY pgrst, 'reload schema';
NOTIFY pgrst, 'reload config';

-- Verify it worked by testing access
DO $$
DECLARE
    v_count INTEGER;
BEGIN
    -- Test as anon role
    SET LOCAL ROLE anon;
    SELECT COUNT(*) INTO v_count FROM public.cart_sessions;
    RESET ROLE;
    
    RAISE NOTICE 'Anon role can access cart_sessions: YES (% rows)', v_count;
EXCEPTION
    WHEN OTHERS THEN
        RESET ROLE;
        RAISE NOTICE 'Anon role can access cart_sessions: NO - %', SQLERRM;
END $$;