-- Fix division by zero error in check_sync_health function

CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION content.check_sync_health()
RETURNS TABLE (
  alert_level TEXT,
  message TEXT,
  details JSONB
)
LANGUAGE plpgsql
AS $$
BEGIN
  -- Check for no syncs in the last hour
  IF NOT EXISTS (
    SELECT 1 FROM content.pulse_sync_log 
    WHERE sync_started_at > NOW() - INTERVAL '1 hour'
  ) THEN
    RETURN QUERY
    SELECT 
      'critical'::TEXT,
      'No syncs in the last hour'::TEXT,
      jsonb_build_object(
        'last_sync', (SELECT MAX(sync_started_at) FROM content.pulse_sync_log)
      );
  END IF;

  -- Check for high failure rate
  IF EXISTS (
    SELECT 1
    FROM content.sync_health
    WHERE hour = DATE_TRUNC('hour', NOW())
      AND failed_syncs > successful_syncs
  ) THEN
    RETURN QUERY
    SELECT 
      'warning'::TEXT,
      'High failure rate in current hour'::TEXT,
      jsonb_build_object(
        'stats', (
          SELECT jsonb_build_object(
            'successful', successful_syncs,
            'failed', failed_syncs
          )
          FROM content.sync_health
          WHERE hour = DATE_TRUNC('hour', NOW())
        )
      );
  END IF;

  -- All good
  RETURN QUERY
  SELECT 
    'ok'::TEXT,
    'Sync health is good'::TEXT,
    jsonb_build_object(
      'last_sync', (SELECT MAX(sync_started_at) FROM content.pulse_sync_log),
      'success_rate', (
        SELECT 
          CASE 
            WHEN COUNT(*) = 0 THEN 0
            ELSE ROUND((COUNT(*) FILTER (WHERE sync_status = 'completed')::numeric / COUNT(*) * 100), 2)
          END
        FROM content.pulse_sync_log
        WHERE sync_started_at > NOW() - INTERVAL '1 hour'
      )
    );
END;
$$;

-- Also update the public wrapper function
CREATE OR REPLACE FUNCTION public.check_sync_health()
RETURNS TABLE (
  alert_level TEXT,
  message TEXT,
  details JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY SELECT * FROM content.check_sync_health();
END;
$$;