-- Update sport_types table with position configurations for each sport

-- Football positions with groupings
UPDATE sport_types
SET sport_positions = '{
  "groups": [
    {
      "id": "goalkeepers",
      "name": "Goalkeepers",
      "order": 1,
      "positions": ["goalkeeper"]
    },
    {
      "id": "defenders",
      "name": "Defenders",
      "order": 2,
      "positions": ["defender", "center_back", "full_back"]
    },
    {
      "id": "midfielders",
      "name": "Midfielders",
      "order": 3,
      "positions": ["midfielder", "defensive_midfielder", "attacking_midfielder", "winger"]
    },
    {
      "id": "forwards",
      "name": "Forwards",
      "order": 4,
      "positions": ["forward", "striker"]
    }
  ],
  "positions": [
    { "value": "", "label": "No Position", "group": null },
    { "value": "goalkeeper", "label": "Goalkeeper", "group": "goalkeepers" },
    { "value": "defender", "label": "Defender", "group": "defenders" },
    { "value": "center_back", "label": "Center Back", "group": "defenders" },
    { "value": "full_back", "label": "Full Back", "group": "defenders" },
    { "value": "midfielder", "label": "Midfielder", "group": "midfielders" },
    { "value": "defensive_midfielder", "label": "Defensive Midfielder", "group": "midfielders" },
    { "value": "attacking_midfielder", "label": "Attacking Midfielder", "group": "midfielders" },
    { "value": "winger", "label": "Winger", "group": "midfielders" },
    { "value": "forward", "label": "Forward", "group": "forwards" },
    { "value": "striker", "label": "Striker", "group": "forwards" }
  ]
}'::jsonb
WHERE sport_code = 'football';

-- Basketball positions (example for another sport)
UPDATE sport_types
SET sport_positions = '{
  "groups": [
    {
      "id": "guards",
      "name": "Guards",
      "order": 1,
      "positions": ["point_guard", "shooting_guard"]
    },
    {
      "id": "forwards",
      "name": "Forwards",
      "order": 2,
      "positions": ["small_forward", "power_forward"]
    },
    {
      "id": "centers",
      "name": "Centers",
      "order": 3,
      "positions": ["center"]
    }
  ],
  "positions": [
    { "value": "", "label": "No Position", "group": null },
    { "value": "point_guard", "label": "Point Guard", "group": "guards" },
    { "value": "shooting_guard", "label": "Shooting Guard", "group": "guards" },
    { "value": "small_forward", "label": "Small Forward", "group": "forwards" },
    { "value": "power_forward", "label": "Power Forward", "group": "forwards" },
    { "value": "center", "label": "Center", "group": "centers" }
  ]
}'::jsonb
WHERE sport_code = 'basketball';

-- Boxing weight classes (different structure for individual sports)
UPDATE sport_types
SET sport_positions = '{
  "groups": [],
  "positions": [
    { "value": "", "label": "No Weight Class", "group": null },
    { "value": "flyweight", "label": "Flyweight", "group": null },
    { "value": "bantamweight", "label": "Bantamweight", "group": null },
    { "value": "featherweight", "label": "Featherweight", "group": null },
    { "value": "lightweight", "label": "Lightweight", "group": null },
    { "value": "welterweight", "label": "Welterweight", "group": null },
    { "value": "middleweight", "label": "Middleweight", "group": null },
    { "value": "light_heavyweight", "label": "Light Heavyweight", "group": null },
    { "value": "heavyweight", "label": "Heavyweight", "group": null }
  ]
}'::jsonb
WHERE sport_code = 'boxing';