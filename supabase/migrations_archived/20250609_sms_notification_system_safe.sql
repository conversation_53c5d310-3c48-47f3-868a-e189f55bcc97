-- Safer approach - check tables first and only recreate if needed

-- SMS Notification System Implementation

-- ============================================================
-- Phase 0: Pre-requisites and Environment Setup
-- ============================================================

-- Extension Verification
DO $$
BEGIN
    -- TASK P0.1: Verify pg_cron extension is available
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_cron') THEN
        CREATE EXTENSION pg_cron;
        RAISE NOTICE 'Created pg_cron extension';
    ELSE
        RAISE NOTICE 'pg_cron extension already exists';
    END IF;

    -- TASK P0.3: Verify http extension is available
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'http') THEN
        CREATE EXTENSION http;
        RAISE NOTICE 'Created http extension';
    ELSE
        RAISE NOTICE 'http extension already exists';
    END IF;
END$$;

-- Environment Configuration
DO $$
BEGIN
    -- TASK P0.5: Set Twilio environment variables if not already set
    BEGIN
        PERFORM current_setting('app.settings.twilio_account_sid', true);
        PERFORM current_setting('app.settings.twilio_auth_token', true);
        PERFORM current_setting('app.settings.twilio_phone_number', true);
        RAISE NOTICE 'Twilio environment variables already set';
    EXCEPTION WHEN OTHERS THEN
        -- TASK P0.6: Set configuration values
        ALTER DATABASE postgres SET app.settings.twilio_account_sid TO '**********************************';
        ALTER DATABASE postgres SET app.settings.twilio_auth_token TO '4789d5d975f4f181f5e9cc9c7ceaece3';
        ALTER DATABASE postgres SET app.settings.twilio_phone_number TO '+************';
        ALTER DATABASE postgres SET app.settings.sms_edge_function_url TO 'https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/send-sms';
        
        RAISE NOTICE 'Set Twilio environment variables';
    END;
END$$;

-- ============================================================
-- Phase 1: Database Schema Implementation
-- ============================================================

-- Safely check if tables exist and create only if needed
DO $$
DECLARE
    notification_templates_exists BOOLEAN;
    sms_queue_exists BOOLEAN;
BEGIN
    -- Check if tables exist
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'notification_templates'
    ) INTO notification_templates_exists;
    
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'sms_queue'
    ) INTO sms_queue_exists;
    
    -- Create notification_templates if it doesn't exist
    IF NOT notification_templates_exists THEN
        RAISE NOTICE 'Creating notification_templates table';
        
        -- TASK P1.1: Create notification_templates table
        CREATE TABLE notification_templates (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            notification_type VARCHAR(50) NOT NULL UNIQUE,
            name VARCHAR(100) NOT NULL,
            template_body TEXT NOT NULL,
            description TEXT,
            variables JSONB DEFAULT '[]',
            category VARCHAR(50) DEFAULT 'system',
            is_active BOOLEAN DEFAULT TRUE,
            created_by UUID REFERENCES profiles(id),
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        -- Create indexes
        CREATE INDEX idx_notification_templates_type ON notification_templates(notification_type);
        CREATE INDEX idx_notification_templates_active ON notification_templates(is_active);
    ELSE
        RAISE NOTICE 'notification_templates table already exists';
    END IF;
    
    -- Create sms_queue if it doesn't exist
    IF NOT sms_queue_exists THEN
        RAISE NOTICE 'Creating sms_queue table';
        
        -- TASK P1.3: Create sms_queue table
        CREATE TABLE sms_queue (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          phone_number TEXT NOT NULL,
          recipient_name TEXT,
          message_body TEXT NOT NULL,
          status VARCHAR(50) DEFAULT 'pending',
          notification_type VARCHAR(50),
          team_id UUID REFERENCES teams(team_id),
          event_id UUID REFERENCES events(id),
          pre_evaluation_id UUID REFERENCES pre_evaluations(id),
          context JSONB DEFAULT '{}',
          scheduled_for TIMESTAMPTZ DEFAULT NOW(),
          sent_at TIMESTAMPTZ,
          processed BOOLEAN DEFAULT FALSE,
          processed_at TIMESTAMPTZ,
          error_message TEXT,
          retry_count INTEGER DEFAULT 0,
          max_attempts INTEGER DEFAULT 3,
          last_error TEXT,
          failed_at TIMESTAMPTZ,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        -- Create indexes
        CREATE INDEX idx_sms_queue_status ON sms_queue(status);
        CREATE INDEX idx_sms_queue_notification_type ON sms_queue(notification_type);
        CREATE INDEX idx_sms_queue_scheduled ON sms_queue(scheduled_for) WHERE status = 'pending';
        CREATE INDEX idx_sms_queue_team ON sms_queue(team_id) WHERE team_id IS NOT NULL;
        CREATE INDEX idx_sms_queue_event ON sms_queue(event_id) WHERE event_id IS NOT NULL;
        CREATE INDEX idx_sms_queue_pre_evaluation ON sms_queue(pre_evaluation_id) WHERE pre_evaluation_id IS NOT NULL;
    ELSE
        RAISE NOTICE 'sms_queue table already exists';
    END IF;
END$$;

-- TASK P1.2: Insert default SMS templates
INSERT INTO notification_templates 
  (notification_type, name, template_body, description, variables, category) 
VALUES
  ('pre_evaluation_sms', 
   'Pre-Evaluation SMS Request', 
   'Hi {{recipient_name}}, please complete your pre-assessment for {{event_name}}. Click here: https://app.shot.com/pre-eval/{{pre_evaluation_id}}',
   'SMS sent when a pre-evaluation is requested', 
   jsonb_build_array('recipient_name', 'event_name', 'pre_evaluation_id'),
   'player_notifications'),
   
  ('parent_notification_sms', 
   'Parent SMS Notification', 
   'Hi {{parent_name}}, {{player_name}} has a new pre-assessment to complete for {{event_name}}. Please ensure it is completed before {{due_date}}.',
   'SMS sent to parents when their child receives a pre-evaluation', 
   jsonb_build_array('parent_name', 'player_name', 'event_name', 'due_date'),
   'parent_notifications')
ON CONFLICT (notification_type) DO UPDATE
SET 
  template_body = EXCLUDED.template_body,
  variables = EXCLUDED.variables,
  updated_at = NOW();

-- ============================================================
-- Phase 2: Core Functions Implementation
-- ============================================================

-- TASK P2.1: Create template formatting function
CREATE OR REPLACE FUNCTION format_notification_message(
  p_notification_type VARCHAR,
  p_variables JSONB
) RETURNS TEXT AS $$
DECLARE
  v_template TEXT;
  v_result TEXT;
  v_var_name TEXT;
  v_var_value TEXT;
BEGIN
  -- Get the template
  SELECT template_body INTO v_template
  FROM notification_templates
  WHERE notification_type = p_notification_type
  AND is_active = TRUE;
  
  IF v_template IS NULL THEN
    RAISE EXCEPTION 'No active template found for notification type: %', p_notification_type;
  END IF;
  
  -- Start with the template
  v_result := v_template;
  
  -- Replace each variable
  FOR v_var_name, v_var_value IN SELECT * FROM jsonb_each_text(p_variables)
  LOOP
    v_result := replace(v_result, '{{' || v_var_name || '}}', COALESCE(v_var_value, ''));
  END LOOP;
  
  RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- TASK P2.2: Create player SMS generation function
CREATE OR REPLACE FUNCTION create_pre_evaluation_sms(
  p_pre_evaluation_id UUID
) RETURNS UUID AS $$
DECLARE
  v_player_id UUID;
  v_event_id UUID;
  v_team_id UUID;
  v_phone VARCHAR(50);
  v_player_name VARCHAR(255);
  v_event_name VARCHAR(255);
  v_notification_id UUID;
  v_message TEXT;
  v_expires_at TIMESTAMPTZ;
  v_due_date TEXT;
BEGIN
  -- Get pre-evaluation details
  SELECT 
    pe.player_id, 
    pe.event_id, 
    pe.team_id,
    p.phone,
    p.full_name,
    e.event_name,
    pe.expires_at
  INTO 
    v_player_id, 
    v_event_id, 
    v_team_id,
    v_phone,
    v_player_name,
    v_event_name,
    v_expires_at
  FROM pre_evaluations pe
  JOIN profiles p ON p.id = pe.player_id
  JOIN events e ON e.id = pe.event_id
  WHERE pe.id = p_pre_evaluation_id;
  
  -- Format due date for display
  v_due_date := to_char(v_expires_at, 'FMDay, FMMonth DD');
  
  -- Check if phone number exists
  IF v_phone IS NULL OR v_phone = '' THEN
    -- Default phone number as fallback
    v_phone := '+************'; -- This should be removed in production and proper error handling added
    RAISE NOTICE 'Using default phone number for player %', v_player_name;
  END IF;
  
  -- Format the message using the template
  v_message := format_notification_message(
    'pre_evaluation_sms',
    jsonb_build_object(
      'recipient_name', v_player_name,
      'event_name', v_event_name,
      'pre_evaluation_id', p_pre_evaluation_id,
      'due_date', v_due_date
    )
  );
  
  -- Create the notification
  INSERT INTO sms_queue (
    phone_number,
    recipient_name,
    message_body,
    notification_type,
    event_id,
    team_id,
    pre_evaluation_id,
    status,
    context,
    scheduled_for
  ) VALUES (
    v_phone,
    v_player_name,
    v_message,
    'pre_evaluation_sms',
    v_event_id,
    v_team_id,
    p_pre_evaluation_id,
    'pending',
    jsonb_build_object(
      'event_name', v_event_name,
      'pre_evaluation_id', p_pre_evaluation_id,
      'expires_at', v_expires_at
    ),
    NOW() -- Schedule for immediate delivery
  ) RETURNING id INTO v_notification_id;
  
  RETURN v_notification_id;
END;
$$ LANGUAGE plpgsql;

-- TASK P2.3: Create parent SMS generation function
CREATE OR REPLACE FUNCTION create_parent_pre_evaluation_sms(
  p_pre_evaluation_id UUID
) RETURNS UUID AS $$
DECLARE
  v_player_id UUID;
  v_parent_id UUID;
  v_parent_phone VARCHAR(50);
  v_parent_name VARCHAR(255);
  v_player_name VARCHAR(255);
  v_event_id UUID;
  v_event_name VARCHAR(255);
  v_team_id UUID;
  v_notification_id UUID;
  v_message TEXT;
  v_expires_at TIMESTAMPTZ;
  v_due_date TEXT;
BEGIN
  -- Get player and parent details
  SELECT 
    pe.player_id, 
    p.parent_id,
    pp.phone,
    pp.full_name,
    p.full_name,
    pe.event_id,
    e.event_name,
    pe.team_id,
    pe.expires_at
  INTO 
    v_player_id,
    v_parent_id,
    v_parent_phone,
    v_parent_name,
    v_player_name,
    v_event_id,
    v_event_name,
    v_team_id,
    v_expires_at
  FROM pre_evaluations pe
  JOIN profiles p ON p.id = pe.player_id
  LEFT JOIN profiles pp ON pp.id = p.parent_id
  JOIN events e ON e.id = pe.event_id
  WHERE pe.id = p_pre_evaluation_id
  AND p.parent_id IS NOT NULL;
  
  -- If no parent found, exit
  IF v_parent_id IS NULL THEN
    RAISE NOTICE 'No parent found for player %', v_player_name;
    RETURN NULL;
  END IF;
  
  -- If no phone number, exit
  IF v_parent_phone IS NULL OR v_parent_phone = '' THEN
    RAISE NOTICE 'No phone number found for parent %', v_parent_name;
    RETURN NULL;
  END IF;
  
  -- Format due date for display
  v_due_date := to_char(v_expires_at, 'FMDay, FMMonth DD');
  
  -- Format the message using the template
  v_message := format_notification_message(
    'parent_notification_sms',
    jsonb_build_object(
      'parent_name', v_parent_name,
      'player_name', v_player_name,
      'event_name', v_event_name,
      'due_date', v_due_date
    )
  );
  
  -- Create the notification
  INSERT INTO sms_queue (
    phone_number,
    recipient_name,
    message_body,
    notification_type,
    event_id,
    team_id,
    pre_evaluation_id,
    status,
    context,
    scheduled_for
  ) VALUES (
    v_parent_phone,
    v_parent_name,
    v_message,
    'parent_notification_sms',
    v_event_id,
    v_team_id,
    p_pre_evaluation_id,
    'pending',
    jsonb_build_object(
      'event_name', v_event_name,
      'pre_evaluation_id', p_pre_evaluation_id,
      'player_id', v_player_id,
      'player_name', v_player_name,
      'parent_id', v_parent_id,
      'expires_at', v_expires_at
    ),
    NOW() + INTERVAL '5 minutes' -- Slight delay after player notification
  ) RETURNING id INTO v_notification_id;
  
  RETURN v_notification_id;
END;
$$ LANGUAGE plpgsql;

-- TASK P2.4: Create trigger function
CREATE OR REPLACE FUNCTION trigger_pre_evaluation_notifications()
RETURNS TRIGGER AS $$
DECLARE
  v_sms_notification_id UUID;
  v_parent_sms_notification_id UUID;
BEGIN
  -- Log the function call
  RAISE NOTICE 'trigger_pre_evaluation_notifications called for pre-evaluation ID: %', NEW.id;

  -- Create player SMS notification
  v_sms_notification_id := create_pre_evaluation_sms(NEW.id);
  RAISE NOTICE 'Created SMS notification ID: %', v_sms_notification_id;
  
  -- Create parent SMS notification if applicable
  v_parent_sms_notification_id := create_parent_pre_evaluation_sms(NEW.id);
  RAISE NOTICE 'Created parent SMS notification ID: %', v_parent_sms_notification_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ============================================================
-- Phase 3: Processing Functions Implementation
-- ============================================================

-- TASK P3.1: Create edge function caller
CREATE OR REPLACE FUNCTION call_send_sms_edge_function(
  p_notification_id UUID,
  p_edge_function_url TEXT DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  v_result JSONB;
  v_url TEXT;
  v_service_key TEXT;
BEGIN
  -- Get edge function URL from settings or use default
  IF p_edge_function_url IS NULL THEN
    -- Try to get from settings, fall back to default
    BEGIN
      v_url := current_setting('app.settings.sms_edge_function_url');
    EXCEPTION WHEN OTHERS THEN
      v_url := 'https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/send-sms';
    END;
  ELSE
    v_url := p_edge_function_url;
  END IF;
  
  -- Get service key from settings
  BEGIN
    v_service_key := current_setting('app.settings.service_key');
  EXCEPTION WHEN OTHERS THEN
    -- Fall back to anon key if no service key is set
    BEGIN
      v_service_key := current_setting('app.settings.anon_key');
    EXCEPTION WHEN OTHERS THEN
      v_service_key := 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im92ZndpeXFodWJ4ZXF2YnJnZ2JlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mjg1NTExMjAsImV4cCI6MjA0NDEyNzEyMH0.fUqHSW87vqKLBt2c4l-JHoR-smcnSGGI8PEATd8nnrU';
    END;
  END;
  
  -- Make HTTP request to edge function
  SELECT content::jsonb INTO v_result
  FROM http((
    'POST',
    v_url,
    ARRAY[
      ('Content-Type', 'application/json'),
      ('Authorization', 'Bearer ' || v_service_key)
    ],
    NULL,
    jsonb_build_object('notification_id', p_notification_id)::TEXT
  )::http_request);
  
  RETURN v_result;
EXCEPTION WHEN OTHERS THEN
  -- Log error and return error object
  RAISE NOTICE 'Error calling edge function: %', SQLERRM;
  RETURN jsonb_build_object(
    'success', false,
    'error', SQLERRM,
    'notification_id', p_notification_id
  );
END;
$$ LANGUAGE plpgsql;

-- TASK P3.2: Create batch processor
CREATE OR REPLACE FUNCTION process_pending_sms(
  p_batch_size INTEGER DEFAULT 10
) RETURNS TABLE(
  notification_id UUID,
  success BOOLEAN,
  error TEXT
) AS $$
DECLARE
  v_notification RECORD;
  v_result JSONB;
  v_success BOOLEAN;
  v_error TEXT;
BEGIN
  -- Process each pending notification
  FOR v_notification IN
    SELECT id
    FROM sms_queue
    WHERE status = 'pending'
    AND scheduled_for <= NOW()
    AND retry_count < max_attempts
    ORDER BY scheduled_for
    LIMIT p_batch_size
  LOOP
    BEGIN
      -- Update status to processing
      UPDATE sms_queue
      SET status = 'processing'
      WHERE id = v_notification.id;
      
      -- Call the edge function
      v_result := call_send_sms_edge_function(v_notification.id);
      
      -- Extract result
      v_success := (v_result->>'success')::BOOLEAN;
      v_error := v_result->>'error';
      
      -- Update notification status based on result
      IF v_success THEN
        UPDATE sms_queue
        SET 
          status = 'sent',
          sent_at = NOW(),
          processed = TRUE,
          processed_at = NOW()
        WHERE id = v_notification.id;
      ELSE
        UPDATE sms_queue
        SET 
          status = 'failed',
          error_message = v_error,
          retry_count = retry_count + 1,
          last_error = v_error,
          failed_at = NOW()
        WHERE id = v_notification.id;
      END IF;
      
      -- Return result
      notification_id := v_notification.id;
      success := v_success;
      error := v_error;
      RETURN NEXT;
    EXCEPTION WHEN OTHERS THEN
      -- Handle exceptions
      UPDATE sms_queue
      SET 
        status = 'failed',
        error_message = SQLERRM,
        retry_count = retry_count + 1,
        last_error = SQLERRM,
        failed_at = NOW()
      WHERE id = v_notification.id;
      
      notification_id := v_notification.id;
      success := FALSE;
      error := SQLERRM;
      RETURN NEXT;
    END;
  END LOOP;
  
  RETURN;
END;
$$ LANGUAGE plpgsql;

-- TASK P3.3: Create retry function
CREATE OR REPLACE FUNCTION retry_failed_sms(
  p_max_retries INTEGER DEFAULT 3,
  p_retry_window INTERVAL DEFAULT INTERVAL '24 hours'
) RETURNS INTEGER AS $$
DECLARE
  v_retried_count INTEGER := 0;
BEGIN
  -- Update failed SMS notifications to pending
  WITH retried AS (
    UPDATE sms_queue
    SET 
      status = 'pending',
      scheduled_for = NOW() + (retry_count * INTERVAL '5 minutes'), -- Exponential backoff
      error_message = error_message || ' (Retry attempt: ' || (retry_count + 1) || ')',
      updated_at = NOW()
    WHERE 
      status = 'failed'
      AND retry_count < p_max_retries
      AND failed_at > NOW() - p_retry_window
    RETURNING id
  )
  SELECT COUNT(*) INTO v_retried_count FROM retried;
  
  RETURN v_retried_count;
END;
$$ LANGUAGE plpgsql;

-- TASK P3.4: Create cleanup function
CREATE OR REPLACE FUNCTION cleanup_sms_queue(
  p_days_to_keep INTEGER DEFAULT 30
) RETURNS INTEGER AS $$
DECLARE
  v_deleted_count INTEGER;
BEGIN
  -- Delete old SMS queue entries
  WITH deleted AS (
    DELETE FROM sms_queue
    WHERE created_at < NOW() - (p_days_to_keep || ' days')::INTERVAL
    RETURNING id
  )
  SELECT COUNT(*) INTO v_deleted_count FROM deleted;
  
  RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ============================================================
-- Phase 4: Monitoring Functions
-- ============================================================

-- TASK P4.1: Create job status check function
CREATE OR REPLACE FUNCTION check_sms_job_status()
RETURNS TABLE (
  job_name TEXT,
  last_run TIMESTAMPTZ,
  next_run TIMESTAMPTZ,
  status TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    j.jobname,
    j.last_run,
    j.next_run,
    CASE 
      WHEN j.last_run IS NULL THEN 'Pending first run'
      WHEN j.last_run < NOW() - INTERVAL '5 minutes' AND j.jobname = 'process-sms-queue' THEN 'Warning: May be delayed'
      WHEN j.last_run < NOW() - INTERVAL '30 minutes' AND j.jobname = 'retry-failed-sms' THEN 'Warning: May be delayed'
      WHEN j.last_run < NOW() - INTERVAL '25 hours' AND j.jobname = 'cleanup-sms-queue' THEN 'Warning: May be delayed'
      ELSE 'OK'
    END AS status
  FROM cron.job j
  WHERE j.jobname IN ('process-sms-queue', 'retry-failed-sms', 'cleanup-sms-queue');
END;
$$ LANGUAGE plpgsql;

-- TASK P4.2: Create queue stats function
CREATE OR REPLACE FUNCTION get_sms_queue_stats()
RETURNS JSONB AS $$
DECLARE
  v_pending INTEGER;
  v_processing INTEGER;
  v_sent INTEGER;
  v_failed INTEGER;
  v_total INTEGER;
  v_last_24h INTEGER;
  v_success_rate DECIMAL;
BEGIN
  -- Get counts by status
  SELECT 
    COUNT(*) FILTER (WHERE status = 'pending') AS pending,
    COUNT(*) FILTER (WHERE status = 'processing') AS processing,
    COUNT(*) FILTER (WHERE status = 'sent') AS sent,
    COUNT(*) FILTER (WHERE status = 'failed') AS failed,
    COUNT(*) AS total,
    COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '24 hours') AS last_24h
  INTO 
    v_pending, v_processing, v_sent, v_failed, v_total, v_last_24h
  FROM sms_queue;
  
  -- Calculate success rate
  IF (v_sent + v_failed) > 0 THEN
    v_success_rate := (v_sent::DECIMAL / (v_sent + v_failed)) * 100;
  ELSE
    v_success_rate := NULL;
  END IF;
  
  -- Return JSON with stats
  RETURN jsonb_build_object(
    'pending', v_pending,
    'processing', v_processing,
    'sent', v_sent,
    'failed', v_failed,
    'total', v_total,
    'last_24h', v_last_24h,
    'success_rate', v_success_rate,
    'timestamp', NOW()
  );
END;
$$ LANGUAGE plpgsql;

-- TASK P4.3: Create test function
CREATE OR REPLACE FUNCTION test_sms_system(
  p_phone TEXT DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
  v_notification_id UUID;
  v_result JSONB;
  v_phone VARCHAR(50) := '+************'; -- Replace with your test phone number
BEGIN
  -- Use provided phone number if available
  IF p_phone IS NOT NULL THEN
    v_phone := p_phone;
  END IF;

  -- Create a test SMS
  INSERT INTO sms_queue (
    phone_number,
    recipient_name,
    message_body,
    notification_type,
    status,
    context,
    scheduled_for
  ) VALUES (
    v_phone,
    'Test User',
    'This is a test SMS from SHOT app sent at ' || to_char(now(), 'HH24:MI:SS'),
    'test',
    'pending',
    jsonb_build_object('test', true),
    NOW()
  ) RETURNING id INTO v_notification_id;
  
  -- Process the SMS
  SELECT jsonb_agg(
    jsonb_build_object(
      'notification_id', notification_id,
      'success', success,
      'error', error
    )
  )
  INTO v_result
  FROM process_pending_sms(1);
  
  RETURN jsonb_build_object(
    'created_notification', v_notification_id,
    'processing_result', v_result
  );
END;
$$ LANGUAGE plpgsql;

-- ============================================================
-- Phase 5: Trigger Implementation
-- ============================================================

-- TASK P5.1: Check for existing triggers
DO $$
DECLARE
    existing_trigger_name TEXT;
BEGIN
    SELECT tgname INTO existing_trigger_name
    FROM pg_trigger 
    JOIN pg_class ON pg_trigger.tgrelid = pg_class.oid 
    WHERE pg_class.relname = 'pre_evaluations'
    AND tgname = 'pre_evaluation_notifications_trigger'
    LIMIT 1;
    
    IF FOUND THEN
        RAISE NOTICE 'Trigger "pre_evaluation_notifications_trigger" already exists, dropping...';
        -- TASK P5.2: Drop existing trigger if needed
        EXECUTE 'DROP TRIGGER IF EXISTS pre_evaluation_notifications_trigger ON pre_evaluations';
    END IF;
END$$;

-- TASK P5.3: Create new trigger
CREATE TRIGGER pre_evaluation_notifications_trigger
AFTER INSERT ON pre_evaluations
FOR EACH ROW
EXECUTE FUNCTION trigger_pre_evaluation_notifications();

-- ============================================================
-- Phase 6: pg_cron Job Setup
-- ============================================================

-- Check if cron jobs already exist and schedule them if not
DO $$
BEGIN
    -- TASK P6.1: Schedule SMS processing job
    IF NOT EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'process-sms-queue') THEN
        PERFORM cron.schedule(
            'process-sms-queue',
            '* * * * *',  -- Every minute
            'SELECT * FROM process_pending_sms(20)'
        );
        RAISE NOTICE 'Scheduled process-sms-queue job';
    ELSE
        RAISE NOTICE 'process-sms-queue job already exists';
    END IF;

    -- TASK P6.2: Schedule retry job
    IF NOT EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'retry-failed-sms') THEN
        PERFORM cron.schedule(
            'retry-failed-sms',
            '*/15 * * * *',  -- Every 15 minutes
            'SELECT retry_failed_sms(3, ''24 hours''::INTERVAL)'
        );
        RAISE NOTICE 'Scheduled retry-failed-sms job';
    ELSE
        RAISE NOTICE 'retry-failed-sms job already exists';
    END IF;

    -- TASK P6.3: Schedule cleanup job
    IF NOT EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'cleanup-sms-queue') THEN
        PERFORM cron.schedule(
            'cleanup-sms-queue',
            '0 3 * * *',  -- 3 AM every day
            'SELECT cleanup_sms_queue(30)'
        );
        RAISE NOTICE 'Scheduled cleanup-sms-queue job';
    ELSE
        RAISE NOTICE 'cleanup-sms-queue job already exists';
    END IF;
END$$;
