-- Migration: Fix event_summary view with proper evaluation state tracking
-- Date: 2025-01-12
-- Purpose: 
-- 1. Fix pre-evaluation counting to only include completed ones
-- 2. Fix coach evaluation counting to exclude drafts
-- 3. Track coach evaluations as: needs attendance -> draft -> completed

DROP VIEW IF EXISTS event_summary;

CREATE OR REPLACE VIEW event_summary AS
SELECT 
  -- Basic event information
  e.id as event_id,
  e.name as event_name,
  e.event_type,
  e.status as event_status,
  e.start_datetime,
  e.end_datetime,
  e.location_name,
  e.location_address,
  
  -- Team and club information
  e.team_id,
  t.team_name,
  t.sport_type,
  t.age_group,
  e.club_id,
  c.club_name,
  
  -- Event settings
  e.is_pre_session_evaluation,
  
  -- Attendance counts (from event_participants)
  COUNT(DISTINCT CASE 
    WHEN ep.role = 'player' 
    THEN ep.user_id 
  END) as invited_count,
  
  COUNT(DISTINCT CASE 
    WHEN ep.role = 'player' AND ep.invitation_status = 'attended' 
    THEN ep.user_id 
  END) as attended_count,
  
  -- Pre-evaluation counts (player self-assessments before event)
  -- Only count completed pre-evaluations
  COUNT(DISTINCT CASE 
    WHEN pe.id IS NOT NULL 
    AND ep.role = 'player' 
    AND pe.status = 'completed'
    THEN pe.player_id 
  END) as pre_eval_completed_count,
  
  -- Total players who should complete pre-evaluation (all invited players when enabled)
  CASE 
    WHEN e.is_pre_session_evaluation = true 
    THEN COUNT(DISTINCT CASE WHEN ep.role = 'player' THEN ep.user_id END)
    ELSE 0
  END as pre_eval_total_count,
  
  -- Coach evaluation counts (main feature - coaches evaluating players)
  -- Only count non-draft evaluations for attended players
  COUNT(DISTINCT CASE 
    WHEN eval.id IS NOT NULL 
    AND ep.invitation_status = 'attended' 
    AND ep.role = 'player'
    AND eval.evaluation_status != 'draft'
    THEN eval.player_id 
  END) as coach_eval_completed_count,
  
  -- Count coach evaluations that have been started (draft status) for attended players
  COUNT(DISTINCT CASE 
    WHEN eval.id IS NOT NULL 
    AND ep.invitation_status = 'attended' 
    AND ep.role = 'player'
    AND eval.evaluation_status = 'draft'
    THEN eval.player_id 
  END) as coach_eval_draft_count,
  
  -- Event metadata
  e.created_at,
  e.updated_at

FROM events e
LEFT JOIN teams t ON e.team_id = t.team_id
LEFT JOIN clubs c ON e.club_id = c.club_id
LEFT JOIN event_participants ep ON e.id = ep.event_id
LEFT JOIN pre_evaluations pe ON e.id = pe.event_id AND pe.player_id = ep.user_id
LEFT JOIN player_evaluations eval ON e.id = eval.event_id AND eval.player_id = ep.user_id

GROUP BY 
  e.id,
  e.name,
  e.event_type,
  e.status,
  e.start_datetime,
  e.end_datetime,
  e.location_name,
  e.location_address,
  e.team_id,
  t.team_name,
  t.sport_type,
  t.age_group,
  e.club_id,
  c.club_name,
  e.is_pre_session_evaluation,
  e.created_at,
  e.updated_at;

-- Add comment explaining the view
COMMENT ON VIEW event_summary IS 'Provides raw event statistics without calculated percentages. 
Pre-evaluations: Only counts completed (status = completed)
Coach evaluations: Only counts non-draft evaluations for attended players
The frontend should show attendance needs to be set when invited_count > attended_count for past events';

-- Grant appropriate permissions
GRANT SELECT ON event_summary TO authenticated;
GRANT SELECT ON event_summary TO service_role;