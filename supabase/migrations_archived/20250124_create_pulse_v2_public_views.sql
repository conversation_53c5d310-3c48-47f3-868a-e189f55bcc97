-- ABOUTME: Create public schema views for Pulse v2 content tables for Supabase v1 compatibility
-- This migration creates views and function wrappers to access content schema objects from public schema

-- Drop existing views if they exist
DROP VIEW IF EXISTS public.pulse_interactions CASCADE;
DROP VIEW IF EXISTS public.pulse_follows CASCADE;
DROP VIEW IF EXISTS public.sp_transactions CASCADE;

-- <PERSON><PERSON> views that reference content schema tables
CREATE VIEW public.pulse_interactions AS
SELECT * FROM content.pulse_interactions;

CREATE VIEW public.pulse_follows AS
SELECT * FROM content.pulse_follows;

CREATE VIEW public.sp_transactions AS
SELECT * FROM content.sp_transactions;

-- Create function wrapper for get_pulse_articles_with_stats
CREATE OR REPLACE FUNCTION public.get_pulse_articles_with_stats()
RETURNS TABLE (
  article_id UUID,
  title TEXT,
  pillar TEXT,
  desk TEXT,
  desk_emoji TEXT,
  narrative TEXT,
  url TEXT,
  source TEXT,
  pub_date TIMESTAMP,
  entities JSONB,
  social_post TEXT,
  story_type JSONB,
  image_url TEXT,
  like_count BIGINT,
  bookmark_count BIGINT,
  share_count BIGINT,
  user_liked BOOLEAN,
  user_bookmarked BOOLEAN,
  user_shared BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY SELECT * FROM content.get_pulse_articles_with_stats();
END;
$$;

-- Create function wrapper for award_sp_points
CREATE OR REPLACE FUNCTION public.award_sp_points(
  p_profile_id UUID,
  p_points INTEGER,
  p_action_type TEXT,
  p_reference_id UUID,
  p_description TEXT DEFAULT NULL
)
RETURNS content.sp_transactions
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_transaction content.sp_transactions;
BEGIN
  -- Call the content schema function
  SELECT * INTO v_transaction FROM content.award_sp_points(
    p_profile_id,
    p_points,
    p_action_type,
    p_reference_id,
    p_description
  );
  RETURN v_transaction;
END;
$$;

-- Create function wrapper for get_sp_balance
CREATE OR REPLACE FUNCTION public.get_sp_balance(p_profile_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN content.get_sp_balance(p_profile_id);
END;
$$;

-- Grant appropriate permissions
GRANT SELECT ON public.pulse_interactions TO authenticated;
GRANT SELECT ON public.pulse_follows TO authenticated;
GRANT SELECT ON public.sp_transactions TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.get_pulse_articles_with_stats() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION public.award_sp_points(UUID, INTEGER, TEXT, UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_sp_balance(UUID) TO authenticated;

-- Create triggers to handle INSERT/UPDATE/DELETE operations through views

-- For pulse_interactions
CREATE OR REPLACE FUNCTION public.pulse_interactions_insert()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO content.pulse_interactions VALUES (NEW.*);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER pulse_interactions_insert_trigger
INSTEAD OF INSERT ON public.pulse_interactions
FOR EACH ROW EXECUTE FUNCTION public.pulse_interactions_insert();

CREATE OR REPLACE FUNCTION public.pulse_interactions_delete()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM content.pulse_interactions 
  WHERE profile_id = OLD.profile_id 
    AND article_id = OLD.article_id 
    AND action_type = OLD.action_type;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER pulse_interactions_delete_trigger
INSTEAD OF DELETE ON public.pulse_interactions
FOR EACH ROW EXECUTE FUNCTION public.pulse_interactions_delete();

-- For pulse_follows
CREATE OR REPLACE FUNCTION public.pulse_follows_insert()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO content.pulse_follows VALUES (NEW.*);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER pulse_follows_insert_trigger
INSTEAD OF INSERT ON public.pulse_follows
FOR EACH ROW EXECUTE FUNCTION public.pulse_follows_insert();

CREATE OR REPLACE FUNCTION public.pulse_follows_delete()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM content.pulse_follows 
  WHERE profile_id = OLD.profile_id 
    AND tag_name = OLD.tag_name;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER pulse_follows_delete_trigger
INSTEAD OF DELETE ON public.pulse_follows
FOR EACH ROW EXECUTE FUNCTION public.pulse_follows_delete();

-- For sp_transactions (read-only from public schema)
-- No insert/update/delete triggers as SP transactions should only be created via the award_sp_points function

-- Update pulse_articles view to include new columns
DROP VIEW IF EXISTS public.pulse_articles CASCADE;

CREATE VIEW public.pulse_articles AS
SELECT * FROM content.pulse_articles;

-- Recreate triggers for pulse_articles with new columns
CREATE OR REPLACE FUNCTION public.pulse_articles_update()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE content.pulse_articles SET
    external_id = NEW.external_id,
    title = NEW.title,
    pillar = NEW.pillar,
    desk = NEW.desk,
    desk_emoji = NEW.desk_emoji,
    narrative = NEW.narrative,
    url = NEW.url,
    source = NEW.source,
    pub_date = NEW.pub_date,
    updated_at = NEW.updated_at,
    sync_status = NEW.sync_status,
    metadata = NEW.metadata,
    is_public = NEW.is_public,
    api_visibility = NEW.api_visibility,
    entities = NEW.entities,
    social_post = NEW.social_post,
    story_type = NEW.story_type,
    image_url = NEW.image_url
  WHERE id = OLD.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER pulse_articles_update_trigger
INSTEAD OF UPDATE ON public.pulse_articles
FOR EACH ROW EXECUTE FUNCTION public.pulse_articles_update();

-- Re-grant permissions on updated pulse_articles view
GRANT SELECT ON public.pulse_articles TO anon, authenticated;

-- Add comments for documentation
COMMENT ON VIEW public.pulse_interactions IS 'View to access content.pulse_interactions for Supabase v1 compatibility';
COMMENT ON VIEW public.pulse_follows IS 'View to access content.pulse_follows for Supabase v1 compatibility';
COMMENT ON VIEW public.sp_transactions IS 'View to access content.sp_transactions for Supabase v1 compatibility';
COMMENT ON FUNCTION public.get_pulse_articles_with_stats() IS 'Wrapper for content.get_pulse_articles_with_stats() for Supabase v1 compatibility';
COMMENT ON FUNCTION public.award_sp_points(UUID, INTEGER, TEXT, UUID, TEXT) IS 'Wrapper for content.award_sp_points() for Supabase v1 compatibility';
COMMENT ON FUNCTION public.get_sp_balance(UUID) IS 'Wrapper for content.get_sp_balance() for Supabase v1 compatibility';