-- ABOUTME: Adds the missing unique constraint on session_id column
-- This fixes the "ON CONFLICT" errors when upserting cart sessions

-- First check if the constraint already exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM pg_constraint 
        WHERE conname = 'cart_sessions_session_id_unique' 
        AND conrelid = 'commerce.cart_sessions'::regclass
    ) THEN
        -- Add unique constraint on session_id
        ALTER TABLE commerce.cart_sessions 
        ADD CONSTRAINT cart_sessions_session_id_unique UNIQUE (session_id);
        
        RAISE NOTICE 'Added unique constraint on session_id';
    ELSE
        RAISE NOTICE 'Unique constraint on session_id already exists';
    END IF;
END $$;

-- Verify the constraint was created
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as definition
FROM pg_constraint
WHERE conrelid = 'commerce.cart_sessions'::regclass
AND conname = 'cart_sessions_session_id_unique';

-- Force PostgREST to reload schema
NOTIFY pgrst, 'reload schema';