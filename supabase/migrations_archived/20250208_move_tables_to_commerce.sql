-- ABOUTME: Move existing commerce tables from public to commerce schema
-- Only run this if tables exist in public schema

-- Create commerce schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS commerce;
GRANT USAGE ON SCHEMA commerce TO authenticated, anon;

-- Move tables from public to commerce (if they exist)
DO $$
BEGIN
    -- Cart sessions
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'cart_sessions') THEN
        ALTER TABLE public.cart_sessions SET SCHEMA commerce;
        RAISE NOTICE 'Moved cart_sessions from public to commerce';
    END IF;

    -- Product cache
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'product_cache') THEN
        ALTER TABLE public.product_cache SET SCHEMA commerce;
        RAISE NOTICE 'Moved product_cache from public to commerce';
    END IF;

    -- BC customer mappings
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'bc_customer_mappings') THEN
        ALTER TABLE public.bc_customer_mappings SET SCHEMA commerce;
        RAISE NOTICE 'Moved bc_customer_mappings from public to commerce';
    END IF;

    -- Order sync
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'order_sync') THEN
        ALTER TABLE public.order_sync SET SCHEMA commerce;
        RAISE NOTICE 'Moved order_sync from public to commerce';
    END IF;

    -- Guardian settings
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'guardian_settings') THEN
        ALTER TABLE public.guardian_settings SET SCHEMA commerce;
        RAISE NOTICE 'Moved guardian_settings from public to commerce';
    END IF;

    -- Purchase approvals
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'purchase_approvals') THEN
        ALTER TABLE public.purchase_approvals SET SCHEMA commerce;
        RAISE NOTICE 'Moved purchase_approvals from public to commerce';
    END IF;

    -- Drop waitlists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'drop_waitlists') THEN
        ALTER TABLE public.drop_waitlists SET SCHEMA commerce;
        RAISE NOTICE 'Moved drop_waitlists from public to commerce';
    END IF;

    -- Loyalty points
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'loyalty_points') THEN
        ALTER TABLE public.loyalty_points SET SCHEMA commerce;
        RAISE NOTICE 'Moved loyalty_points from public to commerce';
    END IF;

    -- Point transactions
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'point_transactions') THEN
        ALTER TABLE public.point_transactions SET SCHEMA commerce;
        RAISE NOTICE 'Moved point_transactions from public to commerce';
    END IF;

    -- Notification queue
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'notification_queue') THEN
        ALTER TABLE public.notification_queue SET SCHEMA commerce;
        RAISE NOTICE 'Moved notification_queue from public to commerce';
    END IF;

    -- Wishlist items
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'wishlist_items') THEN
        ALTER TABLE public.wishlist_items SET SCHEMA commerce;
        RAISE NOTICE 'Moved wishlist_items from public to commerce';
    END IF;

    -- Subscriptions
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'subscriptions') THEN
        ALTER TABLE public.subscriptions SET SCHEMA commerce;
        RAISE NOTICE 'Moved subscriptions from public to commerce';
    END IF;
END $$;

-- Grant permissions
GRANT SELECT ON ALL TABLES IN SCHEMA commerce TO anon, authenticated;
GRANT INSERT, UPDATE, DELETE ON commerce.cart_sessions TO anon, authenticated;
GRANT INSERT, UPDATE ON commerce.purchase_approvals TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA commerce TO anon, authenticated;

-- Enable RLS on cart_sessions
ALTER TABLE commerce.cart_sessions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for cart_sessions
DROP POLICY IF EXISTS "Users can view own carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can create carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can update own carts" ON commerce.cart_sessions;
DROP POLICY IF EXISTS "Users can delete own carts" ON commerce.cart_sessions;

CREATE POLICY "Users can view own carts" ON commerce.cart_sessions
    FOR SELECT
    USING (auth.uid() = profile_id OR profile_id IS NULL);

CREATE POLICY "Users can create carts" ON commerce.cart_sessions
    FOR INSERT
    WITH CHECK (auth.uid() = profile_id OR profile_id IS NULL);

CREATE POLICY "Users can update own carts" ON commerce.cart_sessions
    FOR UPDATE
    USING (auth.uid() = profile_id OR profile_id IS NULL);

CREATE POLICY "Users can delete own carts" ON commerce.cart_sessions
    FOR DELETE
    USING (auth.uid() = profile_id OR profile_id IS NULL);