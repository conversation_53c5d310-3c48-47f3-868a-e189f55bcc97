-- Create drops table for managing limited edition product releases
CREATE TABLE IF NOT EXISTS drops (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  bc_product_id INTEGER NOT NULL, -- BigCommerce product ID
  drop_name VARCHAR(255) NOT NULL,
  drop_date TIMESTAMPTZ NOT NULL,
  total_quantity INTEGER NOT NULL,
  max_per_customer INTEGER NOT NULL DEFAULT 2,
  is_active BOOLEAN NOT NULL DEFAULT false,
  sold_count INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create drop_purchases table to track user purchases per drop
CREATE TABLE IF NOT EXISTS drop_purchases (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  drop_id UUID NOT NULL REFERENCES drops(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  order_id VARCHAR(255) NOT NULL, -- BigCommerce order ID
  quantity INTEGER NOT NULL,
  purchased_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  
  -- Composite unique to prevent duplicate order records
  UNIQUE(drop_id, user_id, order_id)
);

-- Create indexes for better query performance
CREATE INDEX idx_drops_drop_date ON drops(drop_date);
CREATE INDEX idx_drops_is_active ON drops(is_active);
CREATE INDEX idx_drops_bc_product_id ON drops(bc_product_id);
CREATE INDEX idx_drop_purchases_drop_id ON drop_purchases(drop_id);
CREATE INDEX idx_drop_purchases_user_id ON drop_purchases(user_id);

-- Create updated_at trigger for drops table
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_drops_updated_at BEFORE UPDATE ON drops
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add RLS policies
ALTER TABLE drops ENABLE ROW LEVEL SECURITY;
ALTER TABLE drop_purchases ENABLE ROW LEVEL SECURITY;

-- Drops table policies
-- Everyone can view active drops
CREATE POLICY "Public can view active drops" ON drops
  FOR SELECT
  USING (is_active = true);

-- Admins can manage all drops
CREATE POLICY "Admins can manage drops" ON drops
  FOR ALL
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.system_role = 'admin'
    )
  );

-- Drop purchases policies
-- Users can view their own purchases
CREATE POLICY "Users can view own drop purchases" ON drop_purchases
  FOR SELECT
  USING (user_id = auth.uid());

-- System can insert purchases (through service role)
-- Note: Actual purchase recording should be done through service role
-- to enforce business logic and prevent manipulation

-- Admins can view all purchases
CREATE POLICY "Admins can view all drop purchases" ON drop_purchases
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.system_role = 'admin'
    )
  );

-- ========== DROP ACTIVATION SYSTEM ==========

-- Function to check and activate drops that have reached their launch time
CREATE OR REPLACE FUNCTION activate_drops()
RETURNS void AS $$
DECLARE
  drop_record RECORD;
BEGIN
  -- Find drops that should be active but aren't
  FOR drop_record IN 
    SELECT * FROM drops 
    WHERE drop_date <= NOW() 
    AND is_active = false
  LOOP
    -- Update drop status
    UPDATE drops 
    SET is_active = true, 
        updated_at = NOW()
    WHERE id = drop_record.id;
    
    -- Log activation
    RAISE NOTICE 'Activated drop: % at %', drop_record.drop_name, NOW();
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to sync inventory from BigCommerce after order
CREATE OR REPLACE FUNCTION sync_drop_inventory(
  p_product_id INTEGER,
  p_current_stock INTEGER
)
RETURNS void AS $$
DECLARE
  drop_record RECORD;
  sold_count INTEGER;
BEGIN
  -- Find active drops for this product
  FOR drop_record IN 
    SELECT * FROM drops 
    WHERE bc_product_id = p_product_id 
    AND is_active = true
  LOOP
    -- Calculate sold count
    sold_count := drop_record.total_quantity - p_current_stock;
    
    -- Update drop sold count
    UPDATE drops 
    SET sold_count = GREATEST(0, sold_count),
        updated_at = NOW()
    WHERE id = drop_record.id;
  END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to check if user can purchase from a drop
CREATE OR REPLACE FUNCTION check_drop_purchase_limit(
  p_drop_id UUID,
  p_user_id UUID,
  p_quantity INTEGER
)
RETURNS BOOLEAN AS $$
DECLARE
  drop_limit INTEGER;
  current_purchases INTEGER;
BEGIN
  -- Get drop limit
  SELECT max_per_customer INTO drop_limit
  FROM drops
  WHERE id = p_drop_id;
  
  -- Get current user purchases
  SELECT COALESCE(SUM(quantity), 0) INTO current_purchases
  FROM drop_purchases
  WHERE drop_id = p_drop_id
  AND user_id = p_user_id;
  
  -- Check if purchase would exceed limit
  RETURN (current_purchases + p_quantity) <= drop_limit;
END;
$$ LANGUAGE plpgsql;

-- ========== SCHEDULED JOBS ==========

-- Create a scheduled job to activate drops (requires pg_cron extension)
-- Note: You'll need to enable pg_cron in Supabase dashboard
-- Then run this in SQL editor:
-- SELECT cron.schedule(
--   'activate-drops',
--   '* * * * *', -- Every minute
--   'SELECT activate_drops();'
-- );

-- ========== USEFUL VIEWS ==========

-- View for drop analytics
CREATE OR REPLACE VIEW drop_analytics AS
SELECT 
  d.id,
  d.drop_name,
  d.bc_product_id,
  d.drop_date,
  d.total_quantity,
  d.sold_count,
  d.max_per_customer,
  d.is_active,
  ROUND((d.sold_count::NUMERIC / d.total_quantity) * 100, 2) as sell_through_rate,
  COUNT(DISTINCT dp.user_id) as unique_buyers,
  COUNT(dp.id) as total_orders,
  AVG(dp.quantity) as avg_quantity_per_order
FROM drops d
LEFT JOIN drop_purchases dp ON d.id = dp.drop_id
GROUP BY d.id;

-- Grant access to analytics view
GRANT SELECT ON drop_analytics TO authenticated;

-- ========== HELPER FUNCTIONS FOR ADMIN ==========

-- Function to manually activate a drop (for testing)
CREATE OR REPLACE FUNCTION manually_activate_drop(p_drop_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE drops 
  SET is_active = true, 
      updated_at = NOW()
  WHERE id = p_drop_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get drop status with inventory
CREATE OR REPLACE FUNCTION get_drop_status(p_drop_id UUID)
RETURNS TABLE (
  drop_id UUID,
  drop_name VARCHAR,
  is_live BOOLEAN,
  can_purchase BOOLEAN,
  remaining_stock INTEGER,
  seconds_until_launch INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id as drop_id,
    d.drop_name,
    d.is_active as is_live,
    (d.is_active AND (d.total_quantity - d.sold_count) > 0) as can_purchase,
    (d.total_quantity - d.sold_count) as remaining_stock,
    CASE 
      WHEN d.drop_date > NOW() THEN 
        EXTRACT(EPOCH FROM (d.drop_date - NOW()))::INTEGER
      ELSE 0
    END as seconds_until_launch
  FROM drops d
  WHERE d.id = p_drop_id;
END;
$$ LANGUAGE plpgsql;

-- Quick create drop procedure for SQL Editor
CREATE OR REPLACE PROCEDURE create_drop_quick(
  p_product_id INTEGER,
  p_drop_name VARCHAR,
  p_drop_date TIMESTAMP,
  p_total_quantity INTEGER,
  p_max_per_customer INTEGER DEFAULT 2
)
LANGUAGE plpgsql
AS $$
BEGIN
  INSERT INTO drops (
    bc_product_id,
    drop_name,
    drop_date,
    total_quantity,
    max_per_customer,
    is_active,
    sold_count
  ) VALUES (
    p_product_id,
    p_drop_name,
    p_drop_date,
    p_total_quantity,
    p_max_per_customer,
    false,
    0
  );
END;
$$;