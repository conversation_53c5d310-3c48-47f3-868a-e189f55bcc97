-- Migration: Create RLS Policies for Parent Onboarding
-- Description: Row Level Security policies for Phase 2 tables
-- Phase: 2 - Parent Quick Onboarding System
-- Created: 2025-01-31

-- ============================================
-- Parent Onboarding Sessions Policies
-- ============================================

-- Public can create onboarding sessions (for invite links)
CREATE POLICY "Public can create onboarding sessions"
    ON parent_onboarding_sessions FOR INSERT
    TO anon, authenticated
    WITH CHECK (true);

-- Anyone can view their own sessions (by token during onboarding)
CREATE POLICY "View own sessions by token"
    ON parent_onboarding_sessions FOR SELECT
    TO anon, authenticated
    USING (true); -- Session token acts as authentication

-- Users can view sessions they're involved in
CREATE POLICY "Users view their sessions"
    ON parent_onboarding_sessions FOR SELECT
    TO authenticated
    USING (
        parent_profile_id = auth.uid()
        OR
        inviting_coach_id = auth.uid()
    );

-- Sessions can be updated during onboarding
CREATE POLICY "Update active sessions"
    ON parent_onboarding_sessions FOR UPDATE
    TO anon, authenticated
    USING (status NOT IN ('completed', 'abandoned'))
    WITH CHECK (status NOT IN ('completed', 'abandoned'));

-- Coaches can view sessions they initiated
CREATE POLICY "Coaches view initiated sessions"
    ON parent_onboarding_sessions FOR SELECT
    TO authenticated
    USING (
        inviting_coach_id = auth.uid()
        OR
        inviting_coach_sport_head_id IN (
            SELECT id FROM sport_heads WHERE user_id = auth.uid()
        )
    );

-- ============================================
-- Quick Profiles Policies
-- ============================================

-- Public can create quick profiles
CREATE POLICY "Public create quick profiles"
    ON quick_profiles FOR INSERT
    TO anon, authenticated
    WITH CHECK (true);

-- Users can view their own quick profile
CREATE POLICY "View own quick profile"
    ON quick_profiles FOR SELECT
    TO authenticated
    USING (
        profile_id = auth.uid()
        OR
        email = (SELECT email FROM auth.users WHERE id = auth.uid())
    );

-- Quick profiles can be updated by anyone with the token
CREATE POLICY "Update quick profiles"
    ON quick_profiles FOR UPDATE
    TO anon, authenticated
    USING (profile_created = FALSE)
    WITH CHECK (true);

-- ============================================
-- Child Detection Cache Policies
-- ============================================

-- Parents can manage their own cache entries
CREATE POLICY "Parents manage own cache"
    ON child_detection_cache FOR ALL
    TO authenticated
    USING (parent_id = auth.uid())
    WITH CHECK (parent_id = auth.uid());

-- System can clean up stale entries
CREATE POLICY "System cleanup cache"
    ON child_detection_cache FOR DELETE
    TO service_role
    USING (
        expires_at < NOW() 
        OR 
        (stale = TRUE AND last_accessed < NOW() - INTERVAL '7 days')
    );

-- ============================================
-- Onboarding Analytics Policies
-- ============================================

-- Only service role can insert analytics (from edge functions)
CREATE POLICY "Service role insert analytics"
    ON onboarding_analytics FOR INSERT
    TO anon, authenticated, service_role
    WITH CHECK (true);

-- Only admins can view analytics
CREATE POLICY "Admins view analytics"
    ON onboarding_analytics FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid()
            AND user_type = 'admin'
        )
    );

-- No one can update or delete analytics
-- (No policies = no access)

-- ============================================
-- Enhanced Profiles Policies (for new columns)
-- ============================================

-- Parents can view their children's full profiles
CREATE POLICY "Parents view children profiles"
    ON profiles FOR SELECT
    TO authenticated
    USING (
        id = auth.uid()
        OR
        parent_id = auth.uid()
    );

-- Parents can update their children's profiles
CREATE POLICY "Parents update children"
    ON profiles FOR UPDATE
    TO authenticated
    USING (parent_id = auth.uid())
    WITH CHECK (parent_id = auth.uid());

-- Users can update their own emergency info
CREATE POLICY "Users update own emergency info"
    ON profiles FOR UPDATE
    TO authenticated
    USING (id = auth.uid())
    WITH CHECK (id = auth.uid());

-- Coaches can view emergency info for their team members
CREATE POLICY "Coaches view team emergency info"
    ON profiles FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM team_members tm1
            JOIN team_members tm2 ON tm1.team_id = tm2.team_id
            WHERE tm1.user_id = auth.uid()
            AND tm1.role IN ('coach', 'assistant_coach')
            AND tm2.user_id = profiles.id
        )
    );

-- ============================================
-- Functions Security
-- ============================================

-- Revoke default execute permissions and grant specifically
REVOKE ALL ON ALL FUNCTIONS IN SCHEMA public FROM public;

-- Grant execute on specific functions to appropriate roles
GRANT EXECUTE ON FUNCTION create_child_with_sporthead TO authenticated;
GRANT EXECUTE ON FUNCTION find_similar_children TO authenticated;
GRANT EXECUTE ON FUNCTION complete_onboarding_session TO authenticated, anon;
GRANT EXECUTE ON FUNCTION abandon_onboarding_session TO authenticated, anon;
GRANT EXECUTE ON FUNCTION get_or_create_onboarding_session TO authenticated, anon;
GRANT EXECUTE ON FUNCTION validate_onboarding_session TO authenticated, anon;
GRANT EXECUTE ON FUNCTION generate_child_search_key TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_expired_quick_profiles TO service_role;
GRANT EXECUTE ON FUNCTION cleanup_expired_child_cache TO service_role;
GRANT EXECUTE ON FUNCTION refresh_onboarding_funnel_stats TO service_role;

-- ============================================
-- Additional Security Measures
-- ============================================

-- Create function to check if user is a coach
CREATE OR REPLACE FUNCTION is_coach(p_user_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM profiles p
        JOIN sport_heads sh ON sh.user_id = p.id
        WHERE p.id = p_user_id
        AND (
            p.user_type = 'coach' 
            OR 
            sh.role IN ('coach', 'assistant_coach')
        )
    );
END;
$$;

-- Create function to check if user is parent of child
CREATE OR REPLACE FUNCTION is_parent_of(p_parent_id UUID, p_child_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM profiles
        WHERE id = p_child_id
        AND parent_id = p_parent_id
    );
END;
$$;

-- Grant execute on helper functions
GRANT EXECUTE ON FUNCTION is_coach TO authenticated;
GRANT EXECUTE ON FUNCTION is_parent_of TO authenticated;

-- ============================================
-- Audit Log for Sensitive Operations
-- ============================================

-- Create audit log table for parent onboarding
CREATE TABLE IF NOT EXISTS parent_onboarding_audit (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID REFERENCES parent_onboarding_sessions(id),
    action VARCHAR(50) NOT NULL,
    actor_id UUID,
    actor_type VARCHAR(20),
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Index for audit queries
CREATE INDEX idx_onboarding_audit_session ON parent_onboarding_audit(session_id);
CREATE INDEX idx_onboarding_audit_created ON parent_onboarding_audit(created_at);

-- Enable RLS on audit table
ALTER TABLE parent_onboarding_audit ENABLE ROW LEVEL SECURITY;

-- Only admins can view audit logs
CREATE POLICY "Admins view audit logs"
    ON parent_onboarding_audit FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid()
            AND user_type = 'admin'
        )
    );

-- Only service role can insert audit logs
CREATE POLICY "Service role insert audit"
    ON parent_onboarding_audit FOR INSERT
    TO service_role
    WITH CHECK (true);

COMMENT ON TABLE parent_onboarding_audit IS 'Audit trail for parent onboarding operations';
