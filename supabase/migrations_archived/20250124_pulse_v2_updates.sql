-- ABOUTME: Migration to add Pulse v2 features including entities, interactions, following, and SP tracking
-- This migration extends the existing pulse_articles table and adds new tables for social features

-- Add new columns to pulse_articles for v2 features
ALTER TABLE content.pulse_articles 
ADD COLUMN IF NOT EXISTS entities JSONB,
ADD COLUMN IF NOT EXISTS social_post TEXT,
ADD COLUMN IF NOT EXISTS story_type JSONB,
ADD COLUMN IF NOT EXISTS image_url TEXT;

-- Create table for user interactions (likes, bookmarks, shares)
CREATE TABLE IF NOT EXISTS content.pulse_interactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  article_id UUID REFERENCES content.pulse_articles(id) ON DELETE CASCADE,
  action_type TEXT NOT NULL CHECK (action_type IN ('like', 'bookmark', 'share')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(profile_id, article_id, action_type)
);

-- Create table for following tags
CREATE TABLE IF NOT EXISTS content.pulse_follows (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  tag_name TEXT NOT NULL,
  tag_type TEXT DEFAULT 'general',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(profile_id, tag_name)
);

-- Create table for SP transactions
CREATE TABLE IF NOT EXISTS content.sp_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  profile_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  points INTEGER NOT NULL,
  action_type TEXT NOT NULL,
  reference_id UUID, -- article_id or other reference
  reference_type TEXT DEFAULT 'pulse_article',
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_pulse_interactions_profile ON content.pulse_interactions(profile_id);
CREATE INDEX IF NOT EXISTS idx_pulse_interactions_article ON content.pulse_interactions(article_id);
CREATE INDEX IF NOT EXISTS idx_pulse_interactions_action ON content.pulse_interactions(action_type);
CREATE INDEX IF NOT EXISTS idx_pulse_follows_profile ON content.pulse_follows(profile_id);
CREATE INDEX IF NOT EXISTS idx_pulse_follows_tag ON content.pulse_follows(tag_name);
CREATE INDEX IF NOT EXISTS idx_sp_transactions_profile ON content.sp_transactions(profile_id);
CREATE INDEX IF NOT EXISTS idx_sp_transactions_created ON content.sp_transactions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_pulse_articles_entities ON content.pulse_articles USING GIN(entities);
CREATE INDEX IF NOT EXISTS idx_pulse_articles_story_type ON content.pulse_articles USING GIN(story_type);

-- Enable RLS on new tables
ALTER TABLE content.pulse_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE content.pulse_follows ENABLE ROW LEVEL SECURITY;
ALTER TABLE content.sp_transactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for pulse_interactions
CREATE POLICY "Users can manage their own interactions" ON content.pulse_interactions
  FOR ALL USING (auth.uid() = profile_id);

CREATE POLICY "Public can view interaction counts" ON content.pulse_interactions
  FOR SELECT USING (true);

-- RLS Policies for pulse_follows
CREATE POLICY "Users can manage their own follows" ON content.pulse_follows
  FOR ALL USING (auth.uid() = profile_id);

CREATE POLICY "Users can view their own follows" ON content.pulse_follows
  FOR SELECT USING (auth.uid() = profile_id);

-- RLS Policies for sp_transactions
CREATE POLICY "Users can view their own SP transactions" ON content.sp_transactions
  FOR SELECT USING (auth.uid() = profile_id);

CREATE POLICY "Service role can create SP transactions" ON content.sp_transactions
  FOR INSERT WITH CHECK (auth.jwt() ->> 'role' = 'service_role');

-- Helper function to get article with interaction counts
CREATE OR REPLACE FUNCTION content.get_pulse_articles_with_stats()
RETURNS TABLE (
  article_id UUID,
  title TEXT,
  pillar TEXT,
  desk TEXT,
  desk_emoji TEXT,
  narrative TEXT,
  url TEXT,
  source TEXT,
  pub_date TIMESTAMP,
  entities JSONB,
  social_post TEXT,
  story_type JSONB,
  image_url TEXT,
  like_count BIGINT,
  bookmark_count BIGINT,
  share_count BIGINT,
  user_liked BOOLEAN,
  user_bookmarked BOOLEAN,
  user_shared BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pa.id,
    pa.title,
    pa.pillar,
    pa.desk,
    pa.desk_emoji,
    pa.narrative,
    pa.url,
    pa.source,
    pa.pub_date,
    pa.entities,
    pa.social_post,
    pa.story_type,
    pa.image_url,
    COUNT(DISTINCT CASE WHEN pi.action_type = 'like' THEN pi.profile_id END) as like_count,
    COUNT(DISTINCT CASE WHEN pi.action_type = 'bookmark' THEN pi.profile_id END) as bookmark_count,
    COUNT(DISTINCT CASE WHEN pi.action_type = 'share' THEN pi.profile_id END) as share_count,
    EXISTS(SELECT 1 FROM content.pulse_interactions WHERE article_id = pa.id AND profile_id = auth.uid() AND action_type = 'like') as user_liked,
    EXISTS(SELECT 1 FROM content.pulse_interactions WHERE article_id = pa.id AND profile_id = auth.uid() AND action_type = 'bookmark') as user_bookmarked,
    EXISTS(SELECT 1 FROM content.pulse_interactions WHERE article_id = pa.id AND profile_id = auth.uid() AND action_type = 'share') as user_shared
  FROM content.pulse_articles pa
  LEFT JOIN content.pulse_interactions pi ON pa.id = pi.article_id
  WHERE pa.is_public = true
  GROUP BY pa.id
  ORDER BY pa.pub_date DESC;
END;
$$;

-- Function to award SP points
CREATE OR REPLACE FUNCTION content.award_sp_points(
  p_profile_id UUID,
  p_points INTEGER,
  p_action_type TEXT,
  p_reference_id UUID,
  p_description TEXT DEFAULT NULL
)
RETURNS content.sp_transactions
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_transaction content.sp_transactions;
BEGIN
  -- Check if user has already been awarded points for this action
  IF EXISTS (
    SELECT 1 FROM content.sp_transactions 
    WHERE profile_id = p_profile_id 
    AND action_type = p_action_type 
    AND reference_id = p_reference_id
  ) THEN
    RETURN NULL;
  END IF;

  -- Insert new transaction
  INSERT INTO content.sp_transactions (
    profile_id, points, action_type, reference_id, description
  ) VALUES (
    p_profile_id, p_points, p_action_type, p_reference_id, p_description
  ) RETURNING * INTO v_transaction;

  -- Update user's total SP (assuming there's a field in profiles table)
  UPDATE profiles 
  SET sp_balance = COALESCE(sp_balance, 0) + p_points
  WHERE id = p_profile_id;

  RETURN v_transaction;
END;
$$;

-- Function to get user's SP balance
CREATE OR REPLACE FUNCTION content.get_sp_balance(p_profile_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN COALESCE((
    SELECT SUM(points) 
    FROM content.sp_transactions 
    WHERE profile_id = p_profile_id
  ), 0);
END;
$$;

-- Add SP balance column to profiles if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'profiles' 
    AND column_name = 'sp_balance'
  ) THEN
    ALTER TABLE profiles ADD COLUMN sp_balance INTEGER DEFAULT 0;
  END IF;
END $$;

-- Comments for documentation
COMMENT ON TABLE content.pulse_interactions IS 'Tracks user interactions with pulse articles (likes, bookmarks, shares)';
COMMENT ON TABLE content.pulse_follows IS 'Tracks which tags/entities users are following';
COMMENT ON TABLE content.sp_transactions IS 'Records all SP (Shot Points) transactions for gamification';
COMMENT ON COLUMN content.pulse_articles.entities IS 'JSON array of entities mentioned in the article (players, teams, brands, etc.)';
COMMENT ON COLUMN content.pulse_articles.social_post IS 'Pre-formatted text for social sharing';
COMMENT ON COLUMN content.pulse_articles.story_type IS 'Category and type information for filtering';
COMMENT ON COLUMN content.pulse_articles.image_url IS 'Featured image URL for the article card';