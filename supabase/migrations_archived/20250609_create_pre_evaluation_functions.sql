-- Function to insert pre-evaluations with elevated privileges
CREATE OR REPLACE FUNCTION insert_pre_evaluation_with_admin(
  p_event_id UUID,
  p_player_id UUID,
  p_sport_head_id UUID,
  p_team_id UUID,
  p_framework_version TEXT,
  p_week_number INTEGER,
  p_expires_at TIMESTAMPTZ,
  p_request_method TEXT DEFAULT 'coach_initiated',
  p_points_earned INTEGER DEFAULT 30
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER -- This makes the function run with the privileges of the creator
AS $$
DECLARE
  v_pre_eval_id UUID;
BEGIN
  -- Insert the pre-evaluation with elevated privileges
  INSERT INTO pre_evaluations (
    id,
    event_id,
    player_id,
    sport_head_id,
    team_id,
    framework_version,
    week_number,
    expires_at,
    status,
    request_method,
    points_earned,
    created_at,
    responses
  ) VALUES (
    gen_random_uuid(),
    p_event_id,
    p_player_id,
    p_sport_head_id,
    p_team_id,
    p_framework_version,
    p_week_number,
    p_expires_at,
    'pending',
    p_request_method,
    p_points_earned,
    now(),
    '{"questions":[],"summary":{}}'
  )
  RETURNING id INTO v_pre_eval_id;
  
  -- Return the new ID
  RETURN v_pre_eval_id;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION insert_pre_evaluation_with_admin TO authenticated;

-- Function to create basic pre-evaluations without sport_heads
CREATE OR REPLACE FUNCTION create_basic_pre_evaluations(
  p_event_id UUID,
  p_player_ids UUID[],
  p_team_id UUID,
  p_framework_version TEXT,
  p_week_number INTEGER,
  p_expires_at TIMESTAMPTZ
)
RETURNS SETOF UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_player_id UUID;
  v_pre_eval_id UUID;
BEGIN
  -- Loop through each player
  FOREACH v_player_id IN ARRAY p_player_ids
  LOOP
    -- Insert pre-evaluation for this player
    INSERT INTO pre_evaluations (
      id,
      event_id,
      player_id,
      team_id,
      framework_version,
      week_number,
      expires_at,
      status,
      request_method,
      points_earned,
      created_at,
      responses
    ) VALUES (
      gen_random_uuid(),
      p_event_id,
      v_player_id,
      p_team_id,
      p_framework_version,
      p_week_number,
      p_expires_at,
      'pending',
      'coach_initiated',
      30,
      now(),
      '{"questions":[],"summary":{}}'
    )
    RETURNING id INTO v_pre_eval_id;
    
    -- Return this ID to the result set
    RETURN NEXT v_pre_eval_id;
  END LOOP;
  
  RETURN;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION create_basic_pre_evaluations TO authenticated;

-- Function to create a single pre-evaluation with a sport head
CREATE OR REPLACE FUNCTION create_single_pre_evaluation(
  p_event_id UUID,
  p_player_id UUID,
  p_sport_head_id UUID,
  p_team_id UUID,
  p_framework_version TEXT,
  p_week_number INTEGER,
  p_expires_at TIMESTAMPTZ
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_pre_eval_id UUID;
BEGIN
  -- Insert pre-evaluation with the provided sport_head_id
  INSERT INTO pre_evaluations (
    id,
    event_id,
    player_id,
    sport_head_id,
    team_id,
    framework_version,
    week_number,
    expires_at,
    status,
    request_method,
    points_earned,
    created_at,
    responses
  ) VALUES (
    gen_random_uuid(),
    p_event_id,
    p_player_id,
    p_sport_head_id,
    p_team_id,
    p_framework_version,
    p_week_number,
    p_expires_at,
    'pending',
    'coach_initiated',
    30,
    now(),
    '{"questions":[],"summary":{}}'
  )
  RETURNING id INTO v_pre_eval_id;
  
  -- Return the new ID
  RETURN v_pre_eval_id;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION create_single_pre_evaluation TO authenticated;
