-- Migration: Fix RLS Security Issues
-- Generated from RLS Security Report Analysis
-- Date: 2024-12-20

-- ============================================================================
-- PART 1: Fix Tables with RLS Enabled but No Policies
-- ============================================================================

-- Fix pre_evaluation_audit_log table
-- This table should allow authenticated users to read audit logs they have access to
CREATE POLICY "pre_evaluation_audit_log_select" ON "public"."pre_evaluation_audit_log"
AS PERMISSIVE FOR SELECT
TO authenticated
USING (
  -- Allow users to see audit logs for pre-evaluations they are involved in
  auth.uid() IN (
    SELECT player_id FROM pre_evaluations WHERE id = pre_evaluation_id
    UNION
    SELECT sport_head_id FROM pre_evaluations WHERE id = pre_evaluation_id
  )
);

-- Allow system/admin users to insert audit logs
CREATE POLICY "pre_evaluation_audit_log_insert" ON "public"."pre_evaluation_audit_log"
AS PERMISSIVE FOR INSERT
TO authenticated
WITH CHECK (
  -- Only allow inserts if user has access to the pre-evaluation
  auth.uid() IN (
    SELECT player_id FROM pre_evaluations WHERE id = pre_evaluation_id
    UNION
    SELECT sport_head_id FROM pre_evaluations WHERE id = pre_evaluation_id
  )
);

-- Fix team_seasons table
-- This table should allow team members and coaches to access team season data
CREATE POLICY "team_seasons_select" ON "public"."team_seasons"
AS PERMISSIVE FOR SELECT
TO authenticated
USING (
  -- Allow access if user is a team member, coach, or club admin
  team_id IN (
    SELECT team_id FROM team_members WHERE user_id = auth.uid()
    UNION
    SELECT team_id FROM team_coaches WHERE user_id = auth.uid()
    UNION
    SELECT t.team_id FROM teams t
    JOIN clubs c ON t.club_id = c.club_id
    JOIN club_administrators ca ON c.club_id = ca.club_id
    WHERE ca.user_id = auth.uid()
  )
);

CREATE POLICY "team_seasons_insert" ON "public"."team_seasons"
AS PERMISSIVE FOR INSERT
TO authenticated
WITH CHECK (
  -- Only coaches and club admins can create team seasons
  team_id IN (
    SELECT team_id FROM team_coaches WHERE user_id = auth.uid()
    UNION
    SELECT t.team_id FROM teams t
    JOIN clubs c ON t.club_id = c.club_id
    JOIN club_administrators ca ON c.club_id = ca.club_id
    WHERE ca.user_id = auth.uid()
  )
);

CREATE POLICY "team_seasons_update" ON "public"."team_seasons"
AS PERMISSIVE FOR UPDATE
TO authenticated
USING (
  -- Only coaches and club admins can update team seasons
  team_id IN (
    SELECT team_id FROM team_coaches WHERE user_id = auth.uid()
    UNION
    SELECT t.team_id FROM teams t
    JOIN clubs c ON t.club_id = c.club_id
    JOIN club_administrators ca ON c.club_id = ca.club_id
    WHERE ca.user_id = auth.uid()
  )
)
WITH CHECK (
  team_id IN (
    SELECT team_id FROM team_coaches WHERE user_id = auth.uid()
    UNION
    SELECT t.team_id FROM teams t
    JOIN clubs c ON t.club_id = c.club_id
    JOIN club_administrators ca ON c.club_id = ca.club_id
    WHERE ca.user_id = auth.uid()
  )
);

CREATE POLICY "team_seasons_delete" ON "public"."team_seasons"
AS PERMISSIVE FOR DELETE
TO authenticated
USING (
  -- Only club admins can delete team seasons
  team_id IN (
    SELECT t.team_id FROM teams t
    JOIN clubs c ON t.club_id = c.club_id
    JOIN club_administrators ca ON c.club_id = ca.club_id
    WHERE ca.user_id = auth.uid()
  )
);

-- ============================================================================
-- PART 2: Enable RLS on Critical Tables Currently Without Protection
-- ============================================================================

-- Enable RLS on club_administrators table
ALTER TABLE "public"."club_administrators" ENABLE ROW LEVEL SECURITY;

-- Drop existing policy if it exists
DROP POLICY IF EXISTS "club_administrators_select" ON "public"."club_administrators";

CREATE POLICY "club_administrators_select" ON "public"."club_administrators"
AS PERMISSIVE FOR SELECT
TO authenticated
USING (
  -- Users can see club admins for clubs they're associated with
  club_id IN (
    SELECT club_id FROM club_administrators WHERE user_id = auth.uid()
    UNION
    SELECT t.club_id FROM teams t
    JOIN team_members tm ON t.team_id = tm.team_id
    WHERE tm.user_id = auth.uid()
    UNION
    SELECT t.club_id FROM teams t
    JOIN team_coaches tc ON t.team_id = tc.team_id
    WHERE tc.user_id = auth.uid()
  )
);

CREATE POLICY "club_administrators_insert" ON "public"."club_administrators"
AS PERMISSIVE FOR INSERT
TO authenticated
WITH CHECK (
  -- Only existing club admins can add new admins
  club_id IN (
    SELECT club_id FROM club_administrators WHERE user_id = auth.uid()
  )
);

CREATE POLICY "club_administrators_update" ON "public"."club_administrators"
AS PERMISSIVE FOR UPDATE
TO authenticated
USING (
  -- Only existing club admins can update admin records
  club_id IN (
    SELECT club_id FROM club_administrators WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  club_id IN (
    SELECT club_id FROM club_administrators WHERE user_id = auth.uid()
  )
);

CREATE POLICY "club_administrators_delete" ON "public"."club_administrators"
AS PERMISSIVE FOR DELETE
TO authenticated
USING (
  -- Only existing club admins can remove admin records (but not themselves)
  club_id IN (
    SELECT club_id FROM club_administrators WHERE user_id = auth.uid()
  )
  AND user_id != auth.uid() -- Prevent self-removal
);

-- Enable RLS on club_coaches table
ALTER TABLE "public"."club_coaches" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "club_coaches_select" ON "public"."club_coaches"
AS PERMISSIVE FOR SELECT
TO authenticated
USING (
  -- Users can see coaches for clubs they're associated with
  club_id IN (
    SELECT club_id FROM club_administrators WHERE user_id = auth.uid()
    UNION
    SELECT club_id FROM club_coaches WHERE user_id = auth.uid()
    UNION
    SELECT c.club_id FROM clubs c
    JOIN teams t ON c.club_id = t.club_id
    JOIN team_members tm ON t.team_id = tm.team_id
    WHERE tm.user_id = auth.uid()
  )
  OR user_id = auth.uid() -- Coaches can see their own record
);

CREATE POLICY "club_coaches_insert" ON "public"."club_coaches"
AS PERMISSIVE FOR INSERT
TO authenticated
WITH CHECK (
  -- Only club admins can add coaches
  club_id IN (
    SELECT club_id FROM club_administrators WHERE user_id = auth.uid()
  )
);

CREATE POLICY "club_coaches_update" ON "public"."club_coaches"
AS PERMISSIVE FOR UPDATE
TO authenticated
USING (
  -- Club admins or the coach themselves can update
  club_id IN (
    SELECT club_id FROM club_administrators WHERE user_id = auth.uid()
  )
  OR user_id = auth.uid()
)
WITH CHECK (
  club_id IN (
    SELECT club_id FROM club_administrators WHERE user_id = auth.uid()
  )
  OR user_id = auth.uid()
);

CREATE POLICY "club_coaches_delete" ON "public"."club_coaches"
AS PERMISSIVE FOR DELETE
TO authenticated
USING (
  -- Only club admins can remove coaches
  club_id IN (
    SELECT club_id FROM club_administrators WHERE user_id = auth.uid()
  )
);

-- Enable RLS on clubs table
ALTER TABLE "public"."clubs" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "clubs_select" ON "public"."clubs"
AS PERMISSIVE FOR SELECT
TO authenticated
USING (
  -- Users can see clubs they're associated with
  club_id IN (
    SELECT club_id FROM club_administrators WHERE user_id = auth.uid()
    UNION
    SELECT club_id FROM club_coaches WHERE user_id = auth.uid()
    UNION
    SELECT c.club_id FROM clubs c
    JOIN teams t ON c.club_id = t.club_id
    JOIN team_members tm ON t.team_id = tm.team_id
    WHERE tm.user_id = auth.uid()
  )
);

-- Allow anonymous users to see basic club information for registration
CREATE POLICY "clubs_public_select" ON "public"."clubs"
AS PERMISSIVE FOR SELECT
TO anon
USING (
  verification_status = 'approved' AND is_active = true
);

CREATE POLICY "clubs_insert" ON "public"."clubs"
AS PERMISSIVE FOR INSERT
TO authenticated
WITH CHECK (true); -- Any authenticated user can create a club

CREATE POLICY "clubs_update" ON "public"."clubs"
AS PERMISSIVE FOR UPDATE
TO authenticated
USING (
  -- Only club admins can update club information
  club_id IN (
    SELECT club_id FROM club_administrators WHERE user_id = auth.uid()
  )
)
WITH CHECK (
  club_id IN (
    SELECT club_id FROM club_administrators WHERE user_id = auth.uid()
  )
);

CREATE POLICY "clubs_delete" ON "public"."clubs"
AS PERMISSIVE FOR DELETE
TO authenticated
USING (
  -- Only club admins can delete clubs
  club_id IN (
    SELECT club_id FROM club_administrators WHERE user_id = auth.uid()
  )
);

-- Enable RLS on team_coaches table
ALTER TABLE "public"."team_coaches" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "team_coaches_select" ON "public"."team_coaches"
AS PERMISSIVE FOR SELECT
TO authenticated
USING (
  -- Users can see team coaches for teams they're associated with
  team_id IN (
    SELECT team_id FROM team_members WHERE user_id = auth.uid()
    UNION
    SELECT team_id FROM team_coaches WHERE user_id = auth.uid()
    UNION
    SELECT t.team_id FROM teams t
    JOIN clubs c ON t.club_id = c.club_id
    JOIN club_administrators ca ON c.club_id = ca.club_id
    WHERE ca.user_id = auth.uid()
  )
  OR user_id = auth.uid() -- Coaches can see their own assignments
);

CREATE POLICY "team_coaches_insert" ON "public"."team_coaches"
AS PERMISSIVE FOR INSERT
TO authenticated
WITH CHECK (
  -- Only club admins can assign coaches to teams
  team_id IN (
    SELECT t.team_id FROM teams t
    JOIN clubs c ON t.club_id = c.club_id
    JOIN club_administrators ca ON c.club_id = ca.club_id
    WHERE ca.user_id = auth.uid()
  )
);

CREATE POLICY "team_coaches_update" ON "public"."team_coaches"
AS PERMISSIVE FOR UPDATE
TO authenticated
USING (
  -- Only club admins can update coach assignments
  team_id IN (
    SELECT t.team_id FROM teams t
    JOIN clubs c ON t.club_id = c.club_id
    JOIN club_administrators ca ON c.club_id = ca.club_id
    WHERE ca.user_id = auth.uid()
  )
)
WITH CHECK (
  team_id IN (
    SELECT t.team_id FROM teams t
    JOIN clubs c ON t.club_id = c.club_id
    JOIN club_administrators ca ON c.club_id = ca.club_id
    WHERE ca.user_id = auth.uid()
  )
);

CREATE POLICY "team_coaches_delete" ON "public"."team_coaches"
AS PERMISSIVE FOR DELETE
TO authenticated
USING (
  -- Only club admins can remove coach assignments
  team_id IN (
    SELECT t.team_id FROM teams t
    JOIN clubs c ON t.club_id = c.club_id
    JOIN club_administrators ca ON c.club_id = ca.club_id
    WHERE ca.user_id = auth.uid()
  )
);

-- Enable RLS on team_members table
ALTER TABLE "public"."team_members" ENABLE ROW LEVEL SECURITY;

-- Drop existing policy if it exists
DROP POLICY IF EXISTS "team_members_select" ON "public"."team_members";

CREATE POLICY "team_members_select" ON "public"."team_members"
AS PERMISSIVE FOR SELECT
TO authenticated
USING (
  -- Users can see team members for teams they're associated with
  team_id IN (
    SELECT team_id FROM team_members WHERE user_id = auth.uid()
    UNION
    SELECT team_id FROM team_coaches WHERE user_id = auth.uid()
    UNION
    SELECT t.team_id FROM teams t
    JOIN clubs c ON t.club_id = c.club_id
    JOIN club_administrators ca ON c.club_id = ca.club_id
    WHERE ca.user_id = auth.uid()
  )
  OR user_id = auth.uid() -- Users can see their own membership
);

-- Drop existing policy if it exists
DROP POLICY IF EXISTS "team_members_insert" ON "public"."team_members";

CREATE POLICY "team_members_insert" ON "public"."team_members"
AS PERMISSIVE FOR INSERT
TO authenticated
WITH CHECK (
  -- Team coaches and club admins can add members
  team_id IN (
    SELECT team_id FROM team_coaches WHERE user_id = auth.uid()
    UNION
    SELECT t.team_id FROM teams t
    JOIN clubs c ON t.club_id = c.club_id
    JOIN club_administrators ca ON c.club_id = ca.club_id
    WHERE ca.user_id = auth.uid()
  )
  OR user_id = auth.uid() -- Users can join teams themselves (if allowed by team settings)
);

-- Drop existing policy if it exists
DROP POLICY IF EXISTS "team_members_update" ON "public"."team_members";

CREATE POLICY "team_members_update" ON "public"."team_members"
AS PERMISSIVE FOR UPDATE
TO authenticated
USING (
  -- Team coaches, club admins, or the member themselves can update
  team_id IN (
    SELECT team_id FROM team_coaches WHERE user_id = auth.uid()
    UNION
    SELECT t.team_id FROM teams t
    JOIN clubs c ON t.club_id = c.club_id
    JOIN club_administrators ca ON c.club_id = ca.club_id
    WHERE ca.user_id = auth.uid()
  )
  OR user_id = auth.uid()
)
WITH CHECK (
  team_id IN (
    SELECT team_id FROM team_coaches WHERE user_id = auth.uid()
    UNION
    SELECT t.team_id FROM teams t
    JOIN clubs c ON t.club_id = c.club_id
    JOIN club_administrators ca ON c.club_id = ca.club_id
    WHERE ca.user_id = auth.uid()
  )
  OR user_id = auth.uid()
);

-- Drop existing policy if it exists
DROP POLICY IF EXISTS "team_members_delete" ON "public"."team_members";

CREATE POLICY "team_members_delete" ON "public"."team_members"
AS PERMISSIVE FOR DELETE
TO authenticated
USING (
  -- Team coaches, club admins, or the member themselves can remove membership
  team_id IN (
    SELECT team_id FROM team_coaches WHERE user_id = auth.uid()
    UNION
    SELECT t.team_id FROM teams t
    JOIN clubs c ON t.club_id = c.club_id
    JOIN club_administrators ca ON c.club_id = ca.club_id
    WHERE ca.user_id = auth.uid()
  )
  OR user_id = auth.uid()
);

-- ============================================================================
-- PART 3: Additional Security Improvements for Medium-Risk Tables
-- ============================================================================

-- Enable RLS on player_evaluation_history table
ALTER TABLE "public"."player_evaluation_history" ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "player_evaluation_history_select" ON "public"."player_evaluation_history";

CREATE POLICY "player_evaluation_history_select" ON "public"."player_evaluation_history"
AS PERMISSIVE FOR SELECT
TO authenticated
USING (
  -- Users can see evaluation history for players they have access to
  evaluation_id IN (
    SELECT pe.id FROM player_evaluations pe
    WHERE pe.player_id IN (
      SELECT p.id FROM profiles p WHERE p.parent_id = auth.uid()
      UNION
      SELECT tm.user_id FROM team_members tm
      JOIN team_coaches tc ON tm.team_id = tc.team_id
      WHERE tc.user_id = auth.uid()
      UNION
      SELECT tm.user_id FROM team_members tm
      JOIN teams t ON tm.team_id = t.team_id
      JOIN clubs cl ON t.club_id = cl.club_id
      JOIN club_administrators ca ON cl.club_id = ca.club_id
      WHERE ca.user_id = auth.uid()
    )
  )
);

-- Drop existing policy if it exists
DROP POLICY IF EXISTS "player_evaluation_history_insert" ON "public"."player_evaluation_history";

CREATE POLICY "player_evaluation_history_insert" ON "public"."player_evaluation_history"
AS PERMISSIVE FOR INSERT
TO authenticated
WITH CHECK (
  -- Only coaches and club admins can create evaluation history
  evaluation_id IN (
    SELECT pe.id FROM player_evaluations pe
    WHERE pe.player_id IN (
      SELECT tm.user_id FROM team_members tm
      JOIN team_coaches tc ON tm.team_id = tc.team_id
      WHERE tc.user_id = auth.uid()
      UNION
      SELECT tm.user_id FROM team_members tm
      JOIN teams t ON tm.team_id = t.team_id
      JOIN clubs cl ON t.club_id = cl.club_id
      JOIN club_administrators ca ON cl.club_id = ca.club_id
      WHERE ca.user_id = auth.uid()
    )
  )
);

-- ============================================================================
-- PART 4: Comments and Documentation
-- ============================================================================

-- Add comments to document the security model
COMMENT ON POLICY "pre_evaluation_audit_log_select" ON "public"."pre_evaluation_audit_log" IS 
'Allows authenticated users to view audit logs for evaluations they created or are involved in';

COMMENT ON POLICY "team_seasons_select" ON "public"."team_seasons" IS 
'Allows team members, coaches, and club admins to view team season data';

COMMENT ON POLICY "club_administrators_select" ON "public"."club_administrators" IS 
'Allows users to see club administrators for clubs they are associated with';

COMMENT ON POLICY "clubs_public_select" ON "public"."clubs" IS 
'Allows anonymous users to view public club information for registration purposes';

COMMENT ON POLICY "team_members_select" ON "public"."team_members" IS 
'Allows users to see team members for teams they are associated with or their own membership';

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

-- This migration addresses the following security issues:
-- 1. Fixed 2 tables with RLS enabled but no policies
-- 2. Enabled RLS on 5 critical tables that were unprotected
-- 3. Added comprehensive policies for proper access control
-- 4. Maintained principle of least privilege
-- 5. Ensured users can only access data they should have access to
--
-- Next steps:
-- 1. Test the migration in a development environment
-- 2. Verify that existing application functionality still works
-- 3. Monitor for any performance impacts
-- 4. Consider adding indexes on commonly queried columns in policy conditions