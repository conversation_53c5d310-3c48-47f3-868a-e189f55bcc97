-- Fix player_evaluations rating column constraint violation
-- Add default value to rating column to prevent NOT NULL constraint errors
-- when automatic evaluation creation occurs during event publishing

-- Add default value of 1 (minimum valid rating) to the rating column
ALTER TABLE player_evaluations 
ALTER COLUMN rating SET DEFAULT 1;

-- Update any existing NULL ratings to the default value (just in case)
UPDATE player_evaluations 
SET rating = 1 
WHERE rating IS NULL;

-- Add comment for documentation
COMMENT ON COLUMN player_evaluations.rating IS 'Player rating (1-5 scale). Default value 1 allows automatic evaluation creation without constraint violations.';