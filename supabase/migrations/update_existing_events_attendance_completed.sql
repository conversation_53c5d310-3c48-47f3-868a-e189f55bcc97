-- Update existing events to set attendance_completed = true where attendance has been marked
-- This is based on having event_participants with 'attended' status

UPDATE events 
SET attendance_completed = TRUE
WHERE id IN (
  SELECT DISTINCT event_id 
  FROM event_participants 
  WHERE invitation_status = 'attended'
)
AND attendance_completed = FALSE;

-- Add comment about the update
COMMENT ON COLUMN events.attendance_completed IS 'Indicates whether attendance has been marked for this event. When true, coaches can skip directly to evaluation page. Updated retroactively for events with attended participants.';