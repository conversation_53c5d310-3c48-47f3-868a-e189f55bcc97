-- Migration: Add attendance_completed field to event_summary view
-- Date: 2025-01-13
-- Purpose: Include attendance_completed flag in event_summary so <PERSON><PERSON><PERSON> can determine if attendance is already set

DROP VIEW IF EXISTS event_summary;

CREATE OR REPLACE VIEW event_summary AS
SELECT 
  -- Basic event information
  e.id as event_id,
  e.name as event_name,
  e.event_type,
  e.status as event_status,
  e.start_datetime,
  e.end_datetime,
  e.location_name,
  e.location_address,
  
  -- Team and club information
  e.team_id,
  t.team_name,
  t.sport_type,
  t.age_group,
  e.club_id,
  c.club_name,
  
  -- Event settings
  e.is_pre_session_evaluation,
  e.attendance_completed,  -- ADD THIS FIELD
  
  -- Attendance counts (from event_participants)
  COUNT(DISTINCT CASE 
    WHEN ep.role = 'player' 
    THEN ep.user_id 
  END) as invited_count,
  
  COUNT(DISTINCT CASE 
    WHEN ep.role = 'player' AND ep.invitation_status = 'attended' 
    THEN ep.user_id 
  END) as attended_count,
  
  -- Pre-evaluation counts (player self-assessments before event)
  -- Only count completed pre-evaluations
  COUNT(DISTINCT CASE 
    WHEN pe.id IS NOT NULL 
    AND ep.role = 'player' 
    AND pe.status = 'completed'
    THEN pe.player_id 
  END) as pre_eval_completed_count,
  
  -- Total players who should complete pre-evaluation (all invited players when enabled)
  CASE 
    WHEN e.is_pre_session_evaluation = true 
    THEN COUNT(DISTINCT CASE WHEN ep.role = 'player' THEN ep.user_id END)
    ELSE 0
  END as pre_eval_total_count,
  
  -- Coach evaluation counts (main feature - coaches evaluating players)
  -- Only count non-draft evaluations for attended players
  COUNT(DISTINCT CASE 
    WHEN ce.id IS NOT NULL 
    AND ep.invitation_status = 'attended' 
    AND ep.role = 'player'
    AND ce.evaluation_status != 'draft'
    THEN ce.player_id 
  END) as coach_eval_completed_count,
  
  -- Count coach evaluations that have been started (draft status) for attended players
  COUNT(DISTINCT CASE 
    WHEN ce.id IS NOT NULL 
    AND ep.invitation_status = 'attended' 
    AND ep.role = 'player'
    AND ce.evaluation_status = 'draft'
    THEN ce.player_id 
  END) as coach_eval_draft_count,
  
  -- Timestamps
  e.created_at,
  e.updated_at
FROM events e
LEFT JOIN teams t ON e.team_id = t.team_id
LEFT JOIN clubs c ON e.club_id = c.club_id
LEFT JOIN event_participants ep ON e.id = ep.event_id
LEFT JOIN pre_evaluations pe ON e.id = pe.event_id AND pe.player_id = ep.user_id
LEFT JOIN player_evaluations ce ON e.id = ce.event_id AND ce.player_id = ep.user_id

GROUP BY 
  e.id,
  e.name,
  e.event_type,
  e.status,
  e.start_datetime,
  e.end_datetime,
  e.location_name,
  e.location_address,
  e.team_id,
  t.team_name,
  t.sport_type,
  t.age_group,
  e.club_id,
  c.club_name,
  e.is_pre_session_evaluation,
  e.attendance_completed,
  e.created_at,
  e.updated_at;

-- Grant appropriate permissions
GRANT SELECT ON event_summary TO authenticated;
GRANT SELECT ON event_summary TO service_role;