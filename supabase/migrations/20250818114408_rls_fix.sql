-- ULTIMATE RLS FIX - This migration runs last and completely overrides all previous RLS policies
-- Uses a very high timestamp to ensure it runs after all other migrations

-- Step 1: Completely disable <PERSON><PERSON> and drop all policies
ALTER TABLE club_administrators DISABLE ROW LEVEL SECURITY;
ALTER TABLE teams DISABLE ROW LEVEL SECURITY;
ALTER TABLE team_coaches DISABLE ROW LEVEL SECURITY;
ALTER TABLE team_members DISABLE ROW LEVEL SECURITY;

-- Step 2: Drop ALL existing policies using a more comprehensive approach
DO $$ 
DECLARE
    pol_record RECORD;
    table_name TEXT;
BEGIN
    -- List of tables to clean
    FOR table_name IN VALUES ('club_administrators'), ('teams'), ('team_coaches'), ('team_members')
    LOOP
        -- Drop all policies for this table
        FOR pol_record IN 
            SELECT policyname FROM pg_policies WHERE tablename = table_name
        LOOP
            BEGIN
                EXECUTE format('DROP POLICY IF EXISTS %I ON %I', pol_record.policyname, table_name);
            EXCEPTION WHEN OTHERS THEN
                -- Continue if policy doesn't exist or can't be dropped
                NULL;
            END;
        END LOOP;
    END LOOP;
END $$;

-- Step 3: Create completely new, simple policies without any circular references

-- Club Administrators - Base level (no dependencies on other tables)
ALTER TABLE club_administrators ENABLE ROW LEVEL SECURITY;

-- Simple policy: users can only see/manage their own admin records
CREATE POLICY "club_admin_own_records" ON club_administrators
    FOR ALL USING (auth.uid() = user_id);

-- Teams - Only depends on direct user ownership
ALTER TABLE teams ENABLE ROW LEVEL SECURITY;

-- Simple policy: only team creators can see/manage their teams
CREATE POLICY "teams_creator_only" ON teams
    FOR ALL USING (auth.uid() = created_by);

-- Team Coaches - Simple ownership model
ALTER TABLE team_coaches ENABLE ROW LEVEL SECURITY;

-- Simple policy: coaches can see their own assignments, team creators can manage
CREATE POLICY "team_coaches_simple" ON team_coaches
    FOR SELECT USING (
        auth.uid() = user_id OR 
        EXISTS (SELECT 1 FROM teams WHERE team_id = team_coaches.team_id AND created_by = auth.uid())
    );

CREATE POLICY "team_coaches_manage" ON team_coaches
    FOR INSERT WITH CHECK (
        EXISTS (SELECT 1 FROM teams WHERE team_id = team_coaches.team_id AND created_by = auth.uid())
    );

CREATE POLICY "team_coaches_update" ON team_coaches
    FOR UPDATE USING (
        EXISTS (SELECT 1 FROM teams WHERE team_id = team_coaches.team_id AND created_by = auth.uid())
    );

CREATE POLICY "team_coaches_delete" ON team_coaches
    FOR DELETE USING (
        EXISTS (SELECT 1 FROM teams WHERE team_id = team_coaches.team_id AND created_by = auth.uid())
    );

-- Team Members - Simple ownership model
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;

-- Simple policy: members can see their own memberships, team creators can manage
CREATE POLICY "team_members_simple" ON team_members
    FOR SELECT USING (
        auth.uid() = user_id OR 
        EXISTS (SELECT 1 FROM teams WHERE team_id = team_members.team_id AND created_by = auth.uid())
    );

CREATE POLICY "team_members_manage" ON team_members
    FOR INSERT WITH CHECK (
        EXISTS (SELECT 1 FROM teams WHERE team_id = team_members.team_id AND created_by = auth.uid())
    );

CREATE POLICY "team_members_update" ON team_members
    FOR UPDATE USING (
        EXISTS (SELECT 1 FROM teams WHERE team_id = team_members.team_id AND created_by = auth.uid())
    );

CREATE POLICY "team_members_delete" ON team_members
    FOR DELETE USING (
        EXISTS (SELECT 1 FROM teams WHERE team_id = team_members.team_id AND created_by = auth.uid())
    );

-- Add helpful comments
COMMENT ON POLICY "club_admin_own_records" ON club_administrators IS 'Users can only access their own admin records';
COMMENT ON POLICY "teams_creator_only" ON teams IS 'Only team creators can access their teams';
COMMENT ON POLICY "team_coaches_simple" ON team_coaches IS 'Coaches see own assignments, creators manage all';
COMMENT ON POLICY "team_members_simple" ON team_members IS 'Members see own memberships, creators manage all';

-- Final verification: ensure no circular dependencies exist
-- This query should return 0 rows if successful
DO $$
BEGIN
    RAISE NOTICE 'RLS policies have been completely reset with no circular dependencies';
END $$;