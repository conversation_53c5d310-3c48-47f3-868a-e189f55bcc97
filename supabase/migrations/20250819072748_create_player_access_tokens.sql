-- Create player access tokens table for secure evaluation access
-- This table stores temporary tokens that allow players to access evaluations without authentication

CREATE TABLE IF NOT EXISTS public.player_access_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  token TEXT UNIQUE NOT NULL,
  event_id UUID NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
  player_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  evaluation_type TEXT NOT NULL CHECK (evaluation_type IN ('pre', 'post')),
  expires_at TIMESTAMPTZ NOT NULL,
  created_by UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  used_at TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT TRUE,
  
  -- Ensure one active token per player/event/type combination
  CONSTRAINT unique_active_token UNIQUE (event_id, player_id, evaluation_type, is_active)
);

-- Create indexes for performance
CREATE INDEX idx_player_access_tokens_token ON public.player_access_tokens(token);
CREATE INDEX idx_player_access_tokens_event_player ON public.player_access_tokens(event_id, player_id);
CREATE INDEX idx_player_access_tokens_expires_at ON public.player_access_tokens(expires_at);

-- Enable RLS
ALTER TABLE public.player_access_tokens ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Coaches can create tokens for their team's events
CREATE POLICY "Coaches can create access tokens" ON public.player_access_tokens
  FOR INSERT TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.events e
      JOIN public.team_members tm ON e.team_id = tm.team_id
      WHERE e.id = event_id
      AND tm.user_id = auth.uid()
      AND tm.role IN ('coach', 'manager')
    )
  );

-- Coaches can view tokens for their team's events
CREATE POLICY "Coaches can view access tokens" ON public.player_access_tokens
  FOR SELECT TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.events e
      JOIN public.team_members tm ON e.team_id = tm.team_id
      WHERE e.id = event_id
      AND tm.user_id = auth.uid()
      AND tm.role IN ('coach', 'manager')
    )
  );

-- Coaches can update tokens for their team's events
CREATE POLICY "Coaches can update access tokens" ON public.player_access_tokens
  FOR UPDATE TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.events e
      JOIN public.team_members tm ON e.team_id = tm.team_id
      WHERE e.id = event_id
      AND tm.user_id = auth.uid()
      AND tm.role IN ('coach', 'manager')
    )
  );

-- Anonymous users can validate tokens (for player access)
CREATE POLICY "Anonymous can validate tokens" ON public.player_access_tokens
  FOR SELECT TO anon
  USING (
    is_active = TRUE
    AND expires_at > NOW()
  );

-- Function to generate secure tokens
CREATE OR REPLACE FUNCTION generate_player_access_token()
RETURNS TEXT AS $$
BEGIN
  RETURN 'eval_' || encode(gen_random_bytes(32), 'base64url');
END;
$$ LANGUAGE plpgsql;

-- Function to cleanup expired tokens
CREATE OR REPLACE FUNCTION cleanup_expired_tokens()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM public.player_access_tokens
  WHERE expires_at < NOW() - INTERVAL '7 days';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to cleanup expired tokens (runs daily)
-- Note: This requires pg_cron extension to be enabled
-- SELECT cron.schedule('cleanup-expired-tokens', '0 2 * * *', 'SELECT cleanup_expired_tokens();');

COMMENT ON TABLE public.player_access_tokens IS 'Stores temporary access tokens for players to access evaluations without authentication';
COMMENT ON COLUMN public.player_access_tokens.token IS 'Unique secure token for player access';
COMMENT ON COLUMN public.player_access_tokens.evaluation_type IS 'Type of evaluation: pre or post';
COMMENT ON COLUMN public.player_access_tokens.used_at IS 'Timestamp when token was first used';
COMMENT ON COLUMN public.player_access_tokens.is_active IS 'Whether token is currently active';