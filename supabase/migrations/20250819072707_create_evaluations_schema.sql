-- Create Evaluations Schema for Assess System
-- This creates a parallel evaluation system without affecting existing functionality

-- Create the evaluations schema
CREATE SCHEMA IF NOT EXISTS evaluations;

-- Grant appropriate permissions
GRANT USAGE ON SCHEMA evaluations TO anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA evaluations TO service_role;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA evaluations TO authenticated;

-- Set search path to create tables in evaluations schema
SET search_path TO evaluations;

-- Copy evaluation criteria to new schema for reference integrity
-- First create the table structure
CREATE TABLE evaluation_criteria (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    position character varying(50) NOT NULL,
    category character varying(50) NOT NULL,
    area character varying(100) NOT NULL,
    evaluation_focus text NOT NULL,
    question text NOT NULL,
    week_number integer NOT NULL,
    framework_date date,
    framework_version character varying(20) DEFAULT 'SHOT-2025'::character varying NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    sport_type character varying(50) DEFAULT 'football'::character varying NOT NULL,
    question_pre text,
    question_post text,
    answers_pre jsonb,
    answers_post jsonb,
    CONSTRAINT evaluation_criteria_week_number_check CHECK ((week_number >= 1) AND (week_number <= 52))
);

-- Add primary key
ALTER TABLE evaluation_criteria ADD CONSTRAINT evaluation_criteria_pkey PRIMARY KEY (id);

-- Insert data from the original table (if any exists)
INSERT INTO evaluation_criteria 
SELECT * FROM public.evaluation_criteria
WHERE EXISTS (SELECT 1 FROM public.evaluation_criteria LIMIT 1);
CREATE INDEX idx_criteria_week ON evaluation_criteria(week_number);
CREATE INDEX idx_criteria_position ON evaluation_criteria(position);
CREATE INDEX idx_criteria_category ON evaluation_criteria(category);

-- Main event evaluations table
CREATE TABLE event_evaluations (
    -- Identity
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id uuid NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
    player_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    team_id uuid NOT NULL REFERENCES public.teams(team_id) ON DELETE CASCADE,
    
    -- Criteria reference
    criteria_id uuid NOT NULL REFERENCES evaluations.evaluation_criteria(id),
    category text NOT NULL CHECK (category IN ('TECHNICAL', 'PHYSICAL', 'PSYCHOLOGICAL', 'SOCIAL')),
    area text NOT NULL,
    position text NOT NULL,
    
    -- Player position (frozen at creation)
    player_position text NOT NULL,
    
    -- Questions (frozen at creation)
    question_pre text,
    question_coach text NOT NULL,
    question_post text,
    
    -- Answer options (frozen)
    answers_pre jsonb,
    answers_post jsonb,
    
    -- Scores with decimal support
    pre_score numeric(3,1) CHECK (pre_score BETWEEN 1.0 AND 10.0),
    coach_score numeric(3,1) CHECK (coach_score BETWEEN 1.0 AND 10.0),
    post_score numeric(3,1) CHECK (post_score BETWEEN 1.0 AND 10.0),
    
    -- Timestamps for each evaluation type
    pre_submitted_at timestamp with time zone,
    pre_submitted_by uuid REFERENCES public.profiles(id),
    coach_submitted_at timestamp with time zone,
    coach_submitted_by uuid REFERENCES public.profiles(id),
    post_submitted_at timestamp with time zone,
    post_submitted_by uuid REFERENCES public.profiles(id),
    
    -- Metadata
    week_number integer NOT NULL,
    framework_version text DEFAULT 'SHOT-2025',
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    -- Tracking
    pre_reminder_sent_at timestamp with time zone,
    post_reminder_sent_at timestamp with time zone,
    
    -- Unique constraint
    CONSTRAINT unique_player_event_criteria UNIQUE (event_id, player_id, criteria_id)
);

-- Create indexes for performance
CREATE INDEX idx_eval_event ON event_evaluations(event_id);
CREATE INDEX idx_eval_player ON event_evaluations(player_id);
CREATE INDEX idx_eval_team ON event_evaluations(team_id);
CREATE INDEX idx_eval_category ON event_evaluations(category);
CREATE INDEX idx_eval_coach_pending ON event_evaluations(event_id, coach_score) 
    WHERE coach_score IS NULL;
CREATE INDEX idx_eval_pre_pending ON event_evaluations(event_id, player_id, pre_score) 
    WHERE pre_score IS NULL;

-- Function to create evaluations for an event
CREATE OR REPLACE FUNCTION evaluations.create_event_evaluations(
    p_event_id uuid,
    p_team_id uuid DEFAULT NULL
)
RETURNS void AS $$
DECLARE
    v_event record;
    v_member record;
    v_criteria record;
    v_week integer;
    v_team_id uuid;
BEGIN
    -- Get event details
    SELECT e.*, e.team_id as event_team_id
    INTO v_event 
    FROM public.events e
    WHERE e.id = p_event_id;
    
    -- Use provided team_id or get from event
    v_team_id := COALESCE(p_team_id, v_event.event_team_id);
    
    v_week := EXTRACT(WEEK FROM v_event.start_datetime);
    
    -- For each active team member
    FOR v_member IN
        SELECT tm.user_id, tm.position
        FROM public.team_members tm
        WHERE tm.team_id = v_team_id
        AND tm.status = 'active'
    LOOP
        -- Get all applicable criteria
        FOR v_criteria IN
            SELECT 
                ec.*
            FROM evaluations.evaluation_criteria ec
            WHERE ec.week_number = v_week
            AND ec.framework_version = 'SHOT-2025'
            AND (
                ec.position = 'All' 
                OR ec.position = COALESCE(v_member.position, 'All')
            )
        LOOP
            INSERT INTO evaluations.event_evaluations (
                event_id, player_id, team_id,
                criteria_id, category, area, position,
                player_position,
                question_pre, question_coach, question_post,
                answers_pre, answers_post,
                week_number, framework_version
            ) VALUES (
                p_event_id, v_member.user_id, v_team_id,
                v_criteria.id, v_criteria.category, v_criteria.area, v_criteria.position,
                COALESCE(v_member.position, 'All'),
                v_criteria.question_pre, v_criteria.question, v_criteria.question_post,
                v_criteria.answers_pre, v_criteria.answers_post,
                v_week, 'SHOT-2025'
            )
            ON CONFLICT (event_id, player_id, criteria_id) DO NOTHING;
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Enable RLS on event_evaluations
ALTER TABLE evaluations.event_evaluations ENABLE ROW LEVEL SECURITY;

-- Players can view and update their own pre/post evaluations
CREATE POLICY "Players can view own evaluations" ON evaluations.event_evaluations
    FOR SELECT TO authenticated
    USING (player_id = auth.uid());

CREATE POLICY "Players can update own pre evaluations" ON evaluations.event_evaluations
    FOR UPDATE TO authenticated
    USING (player_id = auth.uid())
    WITH CHECK (player_id = auth.uid());

-- Coaches can view and update evaluations for their teams
CREATE POLICY "Coaches can view team evaluations" ON evaluations.event_evaluations
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.team_members tm
            WHERE tm.team_id = event_evaluations.team_id
            AND tm.user_id = auth.uid()
            AND tm.role IN ('coach', 'owner')
        )
    );

CREATE POLICY "Coaches can update team evaluations" ON evaluations.event_evaluations
    FOR UPDATE TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.team_members tm
            WHERE tm.team_id = event_evaluations.team_id
            AND tm.user_id = auth.uid()
            AND tm.role IN ('coach', 'owner')
        )
    );

-- Service role has full access
CREATE POLICY "Service role has full access" ON evaluations.event_evaluations
    FOR ALL TO service_role
    USING (true);

-- Add updated_at trigger
CREATE OR REPLACE FUNCTION evaluations.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_event_evaluations_updated_at
    BEFORE UPDATE ON evaluations.event_evaluations
    FOR EACH ROW
    EXECUTE FUNCTION evaluations.update_updated_at_column();

-- Reset search path
SET search_path TO public;

-- Add assess-related columns to existing events table
ALTER TABLE public.events ADD COLUMN IF NOT EXISTS assess_enabled boolean DEFAULT false;
ALTER TABLE public.events ADD COLUMN IF NOT EXISTS assess_status text DEFAULT 'draft' CHECK (assess_status IN ('draft', 'published', 'completed'));
ALTER TABLE public.events ADD COLUMN IF NOT EXISTS assess_published_at timestamp with time zone;
ALTER TABLE public.events ADD COLUMN IF NOT EXISTS assess_pre_deadline timestamp with time zone;
ALTER TABLE public.events ADD COLUMN IF NOT EXISTS assess_post_deadline timestamp with time zone;

-- Create index for assess queries
CREATE INDEX IF NOT EXISTS idx_events_assess_enabled ON public.events(assess_enabled) WHERE assess_enabled = true;
CREATE INDEX IF NOT EXISTS idx_events_assess_status ON public.events(assess_status);