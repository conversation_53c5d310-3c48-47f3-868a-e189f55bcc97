# Analysis: TeamFlat.tsx Evaluation Data Loading

## Overview
After analyzing the code, I've identified the key differences in how TeamFlat.tsx loads and calculates evaluation data compared to the evaluation page.

## Data Loading Approach

### TeamFlat.tsx Data Loading
TeamFlat.tsx uses the `event_summary` view to fetch event data with pre-calculated statistics:

```typescript
// Lines 296-303: Loading past events
const { data: pastEventSummaries } = await supabase
  .from('event_summary')
  .select('*')
  .eq('team_id', teamId)
  .gte('start_datetime', sixtyDaysAgo.toISOString())
  .lt('start_datetime', now.toISOString())
  .order('start_datetime', { ascending: false })
  .limit(10);
```

### Key Fields from event_summary View
The view provides these evaluation-related fields:
- `attended_count`: Number of players who attended
- `coach_eval_completed_count`: Number of completed coach evaluations
- `coach_eval_draft_count`: Number of draft evaluations
- `pre_eval_completed_count`: Number of completed pre-evaluations
- `pre_eval_total_count`: Total number of pre-evaluations

### Data Transformation (Lines 311-345)
Team<PERSON>lat transforms the view data into event objects with these properties:
```typescript
{
  // Coach evaluation data
  coachEvaluationTotal: summary.attended_count || 0,
  coachEvaluationCompleted: summary.coach_eval_completed_count || 0,
  coachEvaluationDraftCount: summary.coach_eval_draft_count || 0,
  
  // Pre-evaluation data
  preEvaluationTotal: summary.pre_eval_total_count || 0,
  preEvaluationCompleted: summary.pre_eval_completed_count || 0,
  
  // Attendance data
  attendedCount: summary.attended_count || 0,
  attendanceCompleted: summary.attendance_completed || false
}
```

## Key Differences from Evaluation Page

### 1. **Data Source**
- **TeamFlat**: Uses pre-aggregated `event_summary` view
- **Evaluation Page**: Loads raw data from multiple tables (`event_participants`, `player_evaluations`, `pre_evaluations`)

### 2. **Calculation Method**
- **TeamFlat**: Relies on database-calculated counts from the view
- **Evaluation Page**: Calculates statistics client-side after loading raw data

### 3. **Performance**
- **TeamFlat**: Single query to get all statistics (efficient)
- **Evaluation Page**: Multiple queries and client-side processing (less efficient)

### 4. **Coach Evaluation Calculation**
The key insight is that `coachEvaluationTotal` uses `attended_count` as the denominator:
```typescript
coachEvaluationTotal: summary.attended_count || 0
```

This means the percentage is calculated as:
```
completion_percentage = (coach_eval_completed_count / attended_count) * 100
```

## Potential Issues

1. **Attendance Tracking**: If `attended_count` is not properly updated when attendance is taken, the evaluation percentages will be incorrect.

2. **View Synchronization**: The `event_summary` view must be properly maintained and synchronized with the underlying tables.

3. **Draft Evaluations**: The view includes `coach_eval_draft_count` but this might not be used in percentage calculations.

## Recommendations

1. **Verify View Definition**: Check the `event_summary` view definition to ensure it's calculating counts correctly.

2. **Ensure Attendance Updates**: Verify that `attended_count` is updated when attendance is marked in the event.

3. **Consistent Data Loading**: Consider using the `EventSummaryService` consistently across both pages for uniform data.

4. **Debug Logging**: The code includes debug logging (lines 313-321) which can help verify the data being loaded.

## Component Flow

The ShadowEventCard component receives the data and displays it:
```typescript
<ShadowEventCard
  // Coach evaluation data for past events
  evaluationsCompleted={(event as any).coachEvaluationCompleted}
  evaluationsTotal={(event as any).coachEvaluationTotal}
  coachEvaluationDraftCount={(event as any).coachEvaluationDraftCount}
  // ... other props
/>
```

The component will then calculate and display the percentage internally based on these values.