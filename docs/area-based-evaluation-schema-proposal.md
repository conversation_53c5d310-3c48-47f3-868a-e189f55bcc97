# Area-Based Evaluation Schema Proposal

## Core Concept
Instead of storing evaluations as complete sets or individual questions, store **one row per evaluation area** (Technical, Physical, Psychological, Social, Positional). This aligns perfectly with how evaluations are displayed and processed.

## Proposed Schema

### Single `event_evaluations` Table
```sql
CREATE TABLE event_evaluations (
    -- Identity
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id uuid NOT NULL REFERENCES events(id),
    player_id uuid NOT NULL REFERENCES profiles(id),
    team_id uuid NOT NULL REFERENCES teams(id),
    
    -- Area being evaluated
    area text NOT NULL CHECK (area IN ('technical', 'physical', 'psychological', 'social', 'positional')),
    
    -- Position at time of evaluation (frozen)
    player_position text NOT NULL,
    
    -- Question and criteria (frozen at creation)
    question_text text NOT NULL,
    question_criteria jsonb, -- Full criteria details including answers/descriptions
    criteria_id uuid, -- Optional link to evaluation_criteria for reference
    
    -- Scores
    pre_score integer CHECK (pre_score BETWEEN 1 AND 10),
    pre_submitted_at timestamp,
    pre_submitted_by uuid REFERENCES profiles(id),
    
    coach_score integer CHECK (coach_score BETWEEN 1 AND 10),
    coach_submitted_at timestamp,
    coach_submitted_by uuid REFERENCES profiles(id),
    
    post_score integer CHECK (post_score BETWEEN 1 AND 10), -- For future post-event evaluations
    post_submitted_at timestamp,
    post_submitted_by uuid REFERENCES profiles(id),
    
    -- Metadata
    week_number integer NOT NULL,
    created_at timestamp DEFAULT now(),
    updated_at timestamp DEFAULT now(),
    
    -- Status tracking
    pre_reminder_sent_at timestamp,
    
    -- Constraints
    CONSTRAINT unique_player_event_area UNIQUE (event_id, player_id, area)
);

-- Indexes for performance
CREATE INDEX idx_event_evaluations_event ON event_evaluations(event_id);
CREATE INDEX idx_event_evaluations_player ON event_evaluations(player_id);
CREATE INDEX idx_event_evaluations_team ON event_evaluations(team_id);
CREATE INDEX idx_event_evaluations_area ON event_evaluations(area);
CREATE INDEX idx_event_evaluations_week ON event_evaluations(week_number);
```

## Example Data

For a single player in an event, there would be exactly 5 rows:

```sql
-- Player: John Smith (Striker) - Event: Training Session Week 15
INSERT INTO event_evaluations (event_id, player_id, area, player_position, question_text, pre_score, coach_score) VALUES
('event123', 'player456', 'technical', 'STRIKER', 'How would you rate your finishing ability?', 7, 8),
('event123', 'player456', 'physical', 'STRIKER', 'How would you rate your speed and agility?', 8, 7),
('event123', 'player456', 'psychological', 'STRIKER', 'How well do you handle pressure situations?', 6, 7),
('event123', 'player456', 'social', 'STRIKER', 'How well do you communicate with teammates?', 9, 9),
('event123', 'player456', 'positional', 'STRIKER', 'How effective is your movement in the box?', 7, 8);
```

## Benefits

### 1. **Natural Data Structure**
- Matches how evaluations are displayed (by area)
- Easy to query specific areas across players
- Simple to show completion status per area

### 2. **Flexible Scoring**
- Pre, coach, and post scores in same row
- Easy to compare self-assessment vs coach assessment
- Can add peer evaluations as additional columns

### 3. **Efficient Queries**
```sql
-- Get all evaluations for a player in an event
SELECT * FROM event_evaluations 
WHERE event_id = ? AND player_id = ?
ORDER BY area;

-- Get team average for technical skills
SELECT AVG(coach_score) FROM event_evaluations
WHERE team_id = ? AND area = 'technical';

-- Find players who need to complete pre-evaluations
SELECT DISTINCT player_id FROM event_evaluations
WHERE event_id = ? AND pre_score IS NULL;

-- Compare self vs coach assessment
SELECT area, pre_score, coach_score, (coach_score - pre_score) as difference
FROM event_evaluations
WHERE event_id = ? AND player_id = ?;
```

### 4. **Clear Status Tracking**
```sql
-- Evaluation completion status
SELECT 
    player_id,
    COUNT(*) as total_areas,
    COUNT(pre_score) as pre_completed,
    COUNT(coach_score) as coach_completed
FROM event_evaluations
WHERE event_id = ?
GROUP BY player_id;
```

## Creation Process

### When Event is Created
```sql
-- Function to create evaluation rows when event is scheduled
CREATE OR REPLACE FUNCTION create_event_evaluations(p_event_id uuid) 
RETURNS void AS $$
DECLARE
    v_event record;
    v_player record;
    v_criteria record;
    v_week integer;
BEGIN
    -- Get event details
    SELECT * INTO v_event FROM events WHERE id = p_event_id;
    v_week := EXTRACT(WEEK FROM v_event.date);
    
    -- For each player in the team
    FOR v_player IN 
        SELECT tm.user_id, tm.position, tm.team_id
        FROM team_members tm
        WHERE tm.team_id = v_event.team_id
        AND tm.status = 'active'
    LOOP
        -- Get the 5 questions for this player (4 standard + 1 positional)
        FOR v_criteria IN
            SELECT DISTINCT ON (category)
                category,
                question_pre as question_text,
                jsonb_build_object(
                    'question_post', question,
                    'answers_pre', answers_pre,
                    'answers_post', answers_post,
                    'area', area
                ) as criteria_data,
                id as criteria_id
            FROM evaluation_criteria
            WHERE week_number = v_week
            AND (position = 'All' OR position = map_position_to_evaluation(v_player.position))
            ORDER BY category, 
                CASE WHEN position != 'All' THEN 0 ELSE 1 END -- Prefer position-specific
        LOOP
            INSERT INTO event_evaluations (
                event_id, player_id, team_id, area, 
                player_position, question_text, question_criteria, 
                criteria_id, week_number
            ) VALUES (
                p_event_id, v_player.user_id, v_player.team_id,
                LOWER(v_criteria.category), -- technical, physical, etc.
                map_position_to_evaluation(v_player.position),
                v_criteria.question_text,
                v_criteria.criteria_data,
                v_criteria.criteria_id,
                v_week
            );
        END LOOP;
        
        -- Add the positional question (5th area)
        INSERT INTO event_evaluations (
            event_id, player_id, team_id, area,
            player_position, question_text, question_criteria,
            week_number
        )
        SELECT 
            p_event_id, v_player.user_id, v_player.team_id, 'positional',
            map_position_to_evaluation(v_player.position),
            question_pre,
            jsonb_build_object('question_post', question, 'answers_pre', answers_pre),
            v_week
        FROM evaluation_criteria
        WHERE week_number = v_week
        AND position = map_position_to_evaluation(v_player.position)
        AND category = 'TECHNICAL' -- Positional questions often categorized as technical
        LIMIT 1;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

## Migration from Current System

```sql
-- Migrate existing pre-evaluations and player_evaluations
INSERT INTO event_evaluations (
    event_id, player_id, team_id, area, player_position,
    question_text, pre_score, coach_score, week_number
)
SELECT 
    pe.event_id,
    pe.player_id,
    pe.team_id,
    'technical' as area,
    COALESCE(pe.player_position, tm.position) as player_position,
    'Technical skills assessment' as question_text,
    pe.response_technical as pre_score,
    (SELECT AVG(score) FROM player_evaluations 
     WHERE player_id = pe.player_id 
     AND event_id = pe.event_id 
     AND category = 'TECHNICAL') as coach_score,
    EXTRACT(WEEK FROM e.date) as week_number
FROM pre_evaluations pe
JOIN events e ON e.id = pe.event_id
LEFT JOIN team_members tm ON tm.user_id = pe.player_id AND tm.team_id = pe.team_id
UNION ALL
-- Repeat for physical, psychological, social areas...
```

## Comparison to Current System

| Aspect | Current System | Area-Based Proposal |
|--------|---------------|-------------------|
| **Tables** | 2 tables (pre_evaluations, player_evaluations) | 1 table (event_evaluations) |
| **Rows per player/event** | Variable (1-20+) | Exactly 5 |
| **Question storage** | Duplicated in each evaluation | Stored once per area |
| **Position tracking** | 3 different places | One place per row |
| **Query complexity** | Multiple joins, aggregations | Simple queries |
| **Pre/Coach link** | Through pre_evaluation_id | Same row |
| **Partial completion** | Hard to track | COUNT(pre_score) |

## UI Integration Benefits

```typescript
// Fetching player evaluation is straightforward
const getPlayerEvaluation = async (eventId: string, playerId: string) => {
  const rows = await supabase
    .from('event_evaluations')
    .select('*')
    .eq('event_id', eventId)
    .eq('player_id', playerId)
    .order('area');
    
  // Returns exactly 5 rows, one per area
  return {
    technical: rows.find(r => r.area === 'technical'),
    physical: rows.find(r => r.area === 'physical'),
    psychological: rows.find(r => r.area === 'psychological'),
    social: rows.find(r => r.area === 'social'),
    positional: rows.find(r => r.area === 'positional')
  };
};

// Saving is also simple
const savePreEvaluation = async (eventId: string, playerId: string, area: string, score: number) => {
  await supabase
    .from('event_evaluations')
    .update({ 
      pre_score: score, 
      pre_submitted_at: new Date(),
      pre_submitted_by: currentUser.id 
    })
    .eq('event_id', eventId)
    .eq('player_id', playerId)
    .eq('area', area);
};
```

## Conclusion

This area-based approach:
1. **Simplifies queries** - Each player has exactly 5 rows per event
2. **Matches UI needs** - Data structure aligns with display requirements
3. **Reduces complexity** - Single table instead of two
4. **Improves performance** - Predictable row count, efficient indexes
5. **Enables easy comparison** - Pre/coach scores in same row
6. **Supports future expansion** - Can add post, peer evaluations as columns

The structure is intuitive, efficient, and directly maps to how coaches and players interact with evaluations.