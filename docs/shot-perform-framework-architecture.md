# SHOT Perform Framework Architecture Overview

## Executive Summary

The SHOT Perform Framework is a comprehensive player evaluation system that tracks development across 5 key areas throughout a 36-week season. The framework uses position-specific questions to provide tailored assessments for different player roles, enabling coaches to track individual progress and identify areas for improvement.

## Framework Structure

### The 5-Question Model
1. **Technical** - Skills and technique specific to the sport
2. **Physical** - Fitness, strength, and physical capabilities
3. **Psychological** - Mental aspects, decision-making, and game intelligence
4. **Social** - Teamwork, communication, and interpersonal skills
5. **Positional** - Position-specific skills (e.g., goalkeeper-specific, striker-specific)

### Position Categories
- **GOALKEEPER** - Specialized goalkeeper questions
- **CENTRE BACK** - Defensive positioning and aerial ability
- **FULL BACK** - Width play and defensive/attacking balance
- **MIDFIELD** - Game control and distribution
- **STRIKER** - Finishing and movement in the box
- **WINGER** - Pace, dribbling, and crossing
- **All** - General questions applicable to all positions

## Database Architecture

### Current Structure

```mermaid
graph TD
    TM[team_members] -->|position| PE[pre_evaluations]
    PE -->|pre_evaluation_id| PEV[player_evaluations]
    EC[evaluation_criteria] -->|position match| PEV
    E[events] -->|event_id| PE
    E -->|event_id| PEV
    
    TM -->|stores player position| DB1[(position varchar)]
    PE -->|copies position at request time| DB2[(player_position varchar)]
    PEV -->|stores evaluation position| DB3[(position varchar)]
```

### Key Tables

#### 1. **team_members**
- Stores player's assigned position in the team
- Position can be updated by coaches
- Links players to teams

#### 2. **evaluation_criteria**
- Contains all evaluation questions
- Week-based progression (1-36)
- Position-specific questions marked with position value
- Questions for "All" apply to every player

#### 3. **pre_evaluations**
- Tracks evaluation requests sent to players
- Stores player's position at time of request
- Contains self-assessment responses
- Links to event and team

#### 4. **player_evaluations**
- Stores actual evaluation scores (1-10 scale)
- Contains both self-assessments and coach evaluations
- Position stored to match with criteria
- Grouped by category and area

### Position Mapping Function
```sql
-- map_position_to_evaluation function converts various formats:
goalkeeper, gk → GOALKEEPER
defender, defense, centre back → CENTRE BACK
midfielder, midfield → MIDFIELD
forward, striker → STRIKER
winger → WINGER
(empty/null) → All
```

## Application Architecture

### Core Services

#### PlayerEvaluationService.ts
- **Purpose**: Central service for all evaluation operations
- **Key Methods**:
  - `getEvaluationCriteria()` - Fetches position-specific questions
  - `createEvaluationSession()` - Creates new evaluation session
  - `saveEvaluation()` - Persists evaluation scores
  - `mapPositionForEvaluation()` - Standardizes position names

#### PreEvaluationService.ts
- **Purpose**: Manages pre-evaluation requests
- **Key Methods**:
  - `createPreEvaluationRequest()` - Sends evaluation requests to players
  - `getPreEvaluationStats()` - Tracks completion rates
  - `sendReminders()` - Manages reminder notifications

### UI Components

#### Coach Components
1. **PreEvaluationRequestButton.tsx**
   - Located on event details page
   - Triggers pre-evaluation requests to all team members
   - Shows completion statistics

2. **EventEvaluation.tsx**
   - Main coach evaluation interface
   - Displays player cards with rating controls
   - Shows pre-evaluation responses for comparison
   - Handles position changes during evaluation

#### Player Components
1. **PlayerSelfEvaluationForm.tsx**
   - Self-assessment interface for players
   - Displays position-specific questions
   - 1-10 rating scale with descriptions
   - Saves responses to pre_evaluations table

### Shadow DOM Components
- **ShadowPlayerEvaluation** - Reusable evaluation card component
- **ShadowRatingScale** - Consistent rating interface
- **ShadowPositionBadge** - Position display component

## Process Flows

### Player Flow

```mermaid
sequenceDiagram
    participant Player
    participant App
    participant DB
    participant Coach
    
    Coach->>DB: Create event
    Coach->>DB: Send pre-evaluation request
    DB->>App: Pre-evaluation notification
    App->>Player: Display notification
    Player->>App: Open self-evaluation form
    App->>DB: Fetch position-specific questions
    DB->>App: Return criteria (4 categories + positional)
    App->>Player: Display questions
    Player->>App: Submit ratings (1-10)
    App->>DB: Save self-assessment
    DB->>Coach: Update completion stats
```

### Coach Flow

```mermaid
sequenceDiagram
    participant Coach
    participant App
    participant DB
    participant Players
    
    Coach->>App: Navigate to event
    Coach->>App: Click "Send Pre-Evaluation"
    App->>DB: Create pre_evaluation records
    DB->>Players: Trigger notifications
    Coach->>App: View completion stats
    Note over Coach,App: After event occurs
    Coach->>App: Open evaluation interface
    App->>DB: Fetch player list with positions
    App->>DB: Fetch pre-evaluation responses
    App->>Coach: Display evaluation cards
    Coach->>App: Rate each player (1-10)
    Coach->>App: Optionally change position
    App->>DB: Save evaluations
    App->>DB: Update player progress
```

## Current Implementation Issues

### 1. Database Complexity
- **Problem**: Two separate tables (pre_evaluations and player_evaluations) with overlapping data
- **Impact**: Complex queries, data redundancy, maintenance overhead

### 2. Position Synchronization
- **Problem**: Position stored in 3 places with potential mismatches
- **Impact**: Wrong questions displayed if position changes between pre-evaluation and evaluation

### 3. Missing Positional Category
- **Problem**: UI expects 5 categories but backend provides 4
- **Impact**: Incomplete evaluations, UI/data mismatch

### 4. Evaluation Counting
- **Problem**: Players only counted as "evaluated" if all 4 categories rated
- **Impact**: Misleading statistics, partial evaluations not recognized

## Recommended Simplification

### Unified Evaluation Table
Merge pre_evaluations and player_evaluations into a single `evaluations` table:

```sql
CREATE TABLE evaluations (
    id uuid PRIMARY KEY,
    player_id uuid NOT NULL,
    team_id uuid NOT NULL,
    event_id uuid NOT NULL,
    position varchar(50), -- Snapshot at evaluation time
    type varchar(20), -- 'self' or 'coach'
    status varchar(20), -- 'pending', 'completed'
    
    -- Scores (NULL until completed)
    technical_score integer,
    physical_score integer,
    psychological_score integer,
    social_score integer,
    
    -- Metadata
    created_at timestamp,
    completed_at timestamp,
    created_by uuid,
    
    -- Link self to coach evaluation
    related_evaluation_id uuid
);
```

### Benefits
1. **Simpler queries** - One table to join instead of two
2. **Clear relationships** - Self and coach evaluations linked directly
3. **Position consistency** - Single source of truth for position
4. **Flexible status** - Easy to track partial completions
5. **Extensible** - Easy to add new evaluation types

## File Structure

```
src/
├── services/
│   ├── PlayerEvaluationService.ts    # Core evaluation logic
│   └── PreEvaluationService.ts       # Pre-evaluation management
├── pages/section/Coach/
│   ├── events/
│   │   └── EventEvaluation.tsx       # Coach evaluation UI
│   └── components/
│       └── PreEvaluationRequestButton.tsx
├── pages/section/Player/
│   └── PlayerSelfEvaluationForm.tsx  # Player self-assessment
├── components/shadow/
│   ├── ShadowPlayerEvaluation.tsx    # Evaluation card
│   └── ShadowRatingScale.tsx         # Rating component
└── types/
    └── evaluation.types.ts            # TypeScript definitions

supabase/
├── migrations/
│   └── 20250813_fix_positions_and_pre_evaluations.sql
└── functions/
    └── map_position_to_evaluation.sql
```

## Security Considerations

### Row Level Security (RLS)
- Players can only view/edit their own evaluations
- Coaches can evaluate players in their teams
- Pre-evaluation requests restricted to team coaches
- Evaluation history protected from tampering

### Data Integrity
- Positions snapshot at evaluation time
- Audit trail for all changes
- Soft deletes to preserve history
- Validation of rating ranges (1-10)

## Future Enhancements

1. **Advanced Analytics**
   - Position-specific performance trends
   - Team-wide strength/weakness analysis
   - Predictive development tracking

2. **Customizable Frameworks**
   - Sport-specific question sets
   - Club-customized criteria
   - Age-group appropriate evaluations

3. **Integration Points**
   - Export to coaching platforms
   - Video analysis linking
   - Training plan generation

## Conclusion

The SHOT Perform Framework provides a comprehensive evaluation system with position-specific assessments. While the current implementation works, significant simplification is possible by merging the evaluation tables and standardizing position handling. This would reduce complexity while maintaining all current functionality.