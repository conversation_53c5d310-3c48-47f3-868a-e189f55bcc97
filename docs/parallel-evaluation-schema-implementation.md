# Parallel Evaluation Schema Implementation Plan

## Overview
Create a new `evaluations` schema to house the redesigned evaluation system while keeping the existing `public` schema tables intact. This allows parallel development and testing without disrupting the current functionality.

## Schema Design

### 1. Create New Schema
```sql
-- Create the evaluations schema
CREATE SCHEMA IF NOT EXISTS evaluations;

-- Grant appropriate permissions
GRANT USAGE ON SCHEMA evaluations TO anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA evaluations TO service_role;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA evaluations TO authenticated;
```

### 2. New Table Structure
```sql
-- Set search path to create tables in evaluations schema
SET search_path TO evaluations;

-- Main event evaluations table
CREATE TABLE event_evaluations (
    -- Identity
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id uuid NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
    player_id uuid NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    team_id uuid NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
    
    -- Criteria reference
    criteria_id uuid NOT NULL, -- We'll copy evaluation_criteria to this schema
    category text NOT NULL CHECK (category IN ('TECHNICAL', 'PHYSICAL', 'PSYCHOLOGICAL', 'SOCIAL')),
    area text NOT NULL,
    position text NOT NULL,
    
    -- Player position (frozen at creation)
    player_position text NOT NULL,
    
    -- Questions (frozen at creation)
    question_pre text,
    question_coach text NOT NULL,
    question_post text,
    
    -- Answer options (frozen)
    answers_pre jsonb,
    answers_post jsonb,
    
    -- Scores with decimal support
    pre_score numeric(3,1) CHECK (pre_score BETWEEN 1.0 AND 10.0),
    coach_score numeric(3,1) CHECK (coach_score BETWEEN 1.0 AND 10.0),
    post_score numeric(3,1) CHECK (post_score BETWEEN 1.0 AND 10.0),
    
    -- Timestamps for each evaluation type
    pre_submitted_at timestamp with time zone,
    pre_submitted_by uuid REFERENCES public.profiles(id),
    coach_submitted_at timestamp with time zone,
    coach_submitted_by uuid REFERENCES public.profiles(id),
    post_submitted_at timestamp with time zone,
    post_submitted_by uuid REFERENCES public.profiles(id),
    
    -- Metadata
    week_number integer NOT NULL,
    framework_version text DEFAULT 'SHOT-2025',
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    -- Tracking
    pre_reminder_sent_at timestamp with time zone,
    post_reminder_sent_at timestamp with time zone,
    
    -- Unique constraint
    CONSTRAINT unique_player_event_criteria UNIQUE (event_id, player_id, criteria_id)
);

-- Create indexes
CREATE INDEX idx_eval_event ON event_evaluations(event_id);
CREATE INDEX idx_eval_player ON event_evaluations(player_id);
CREATE INDEX idx_eval_team ON event_evaluations(team_id);
CREATE INDEX idx_eval_category ON event_evaluations(category);
CREATE INDEX idx_eval_coach_pending ON event_evaluations(event_id, coach_score) 
    WHERE coach_score IS NULL;
CREATE INDEX idx_eval_pre_pending ON event_evaluations(event_id, player_id, pre_score) 
    WHERE pre_score IS NULL;

-- Copy evaluation criteria to new schema for reference integrity
CREATE TABLE evaluation_criteria AS 
SELECT * FROM public.evaluation_criteria;

-- Add primary key and indexes
ALTER TABLE evaluation_criteria ADD PRIMARY KEY (id);
CREATE INDEX idx_criteria_week ON evaluation_criteria(week_number);
CREATE INDEX idx_criteria_position ON evaluation_criteria(position);
CREATE INDEX idx_criteria_category ON evaluation_criteria(category);
```

### 3. Helper Functions
```sql
-- Function to create evaluations for an event
CREATE OR REPLACE FUNCTION evaluations.create_event_evaluations(
    p_event_id uuid,
    p_team_id uuid DEFAULT NULL
)
RETURNS void AS $$
DECLARE
    v_event record;
    v_member record;
    v_criteria record;
    v_week integer;
    v_team_id uuid;
BEGIN
    -- Get event details
    SELECT e.*, e.team_id as event_team_id
    INTO v_event 
    FROM public.events e
    WHERE e.id = p_event_id;
    
    -- Use provided team_id or get from event
    v_team_id := COALESCE(p_team_id, v_event.event_team_id);
    
    v_week := EXTRACT(WEEK FROM v_event.start_datetime);
    
    -- For each active team member
    FOR v_member IN
        SELECT tm.user_id, tm.position
        FROM public.team_members tm
        WHERE tm.team_id = v_team_id
        AND tm.status = 'active'
    LOOP
        -- Get all applicable criteria
        FOR v_criteria IN
            SELECT 
                ec.*
            FROM evaluations.evaluation_criteria ec
            WHERE ec.week_number = v_week
            AND ec.framework_version = 'SHOT-2025'
            AND (
                ec.position = 'All' 
                OR ec.position = public.map_position_to_evaluation(v_member.position)
            )
        LOOP
            INSERT INTO evaluations.event_evaluations (
                event_id, player_id, team_id,
                criteria_id, category, area, position,
                player_position,
                question_pre, question_coach, question_post,
                answers_pre, answers_post,
                week_number, framework_version
            ) VALUES (
                p_event_id, v_member.user_id, v_team_id,
                v_criteria.id, v_criteria.category, v_criteria.area, v_criteria.position,
                COALESCE(public.map_position_to_evaluation(v_member.position), 'All'),
                v_criteria.question_pre, v_criteria.question, v_criteria.question_post,
                v_criteria.answers_pre, v_criteria.answers_post,
                v_week, 'SHOT-2025'
            )
            ON CONFLICT (event_id, player_id, criteria_id) DO NOTHING;
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- RLS Policies
ALTER TABLE evaluations.event_evaluations ENABLE ROW LEVEL SECURITY;

-- Players can view and update their own pre/post evaluations
CREATE POLICY "Players can view own evaluations" ON evaluations.event_evaluations
    FOR SELECT TO authenticated
    USING (player_id = auth.uid());

CREATE POLICY "Players can update own pre evaluations" ON evaluations.event_evaluations
    FOR UPDATE TO authenticated
    USING (player_id = auth.uid())
    WITH CHECK (player_id = auth.uid());

-- Coaches can view and update evaluations for their teams
CREATE POLICY "Coaches can view team evaluations" ON evaluations.event_evaluations
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.team_members tm
            WHERE tm.team_id = event_evaluations.team_id
            AND tm.user_id = auth.uid()
            AND tm.role IN ('coach', 'owner')
        )
    );

CREATE POLICY "Coaches can update team evaluations" ON evaluations.event_evaluations
    FOR UPDATE TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.team_members tm
            WHERE tm.team_id = event_evaluations.team_id
            AND tm.user_id = auth.uid()
            AND tm.role IN ('coach', 'owner')
        )
    );

-- Service role has full access
CREATE POLICY "Service role has full access" ON evaluations.event_evaluations
    FOR ALL TO service_role
    USING (true);
```

## Implementation Strategy

### Phase 1: Database Setup (Immediate)
1. Create the new schema and tables
2. Copy evaluation_criteria data
3. Set up RLS policies
4. Create helper functions

### Phase 2: Service Layer (Week 1)
Create parallel services that use the new schema:

```typescript
// src/services/NewEvaluationService.ts
export class NewEvaluationService {
  private readonly schema = 'evaluations';
  
  async createEventEvaluations(eventId: string, teamId?: string) {
    const { error } = await supabase.rpc('create_event_evaluations', {
      p_event_id: eventId,
      p_team_id: teamId
    }, {
      schema: 'evaluations'
    });
    
    if (error) throw error;
  }
  
  async getEventEvaluations(eventId: string, playerId?: string) {
    let query = supabase
      .from('event_evaluations')
      .select('*')
      .eq('event_id', eventId);
    
    if (playerId) {
      query = query.eq('player_id', playerId);
    }
    
    const { data, error } = await query.schema('evaluations');
    if (error) throw error;
    
    return data;
  }
  
  async savePreEvaluation(evaluationId: string, score: number) {
    const { error } = await supabase
      .from('event_evaluations')
      .update({
        pre_score: score,
        pre_submitted_at: new Date().toISOString(),
        pre_submitted_by: (await supabase.auth.getUser()).data.user?.id
      })
      .eq('id', evaluationId)
      .schema('evaluations');
    
    if (error) throw error;
  }
  
  async saveCoachEvaluation(evaluationId: string, score: number) {
    const { error } = await supabase
      .from('event_evaluations')
      .update({
        coach_score: score,
        coach_submitted_at: new Date().toISOString(),
        coach_submitted_by: (await supabase.auth.getUser()).data.user?.id
      })
      .eq('id', evaluationId)
      .schema('evaluations');
    
    if (error) throw error;
  }
}

export const newEvaluationService = new NewEvaluationService();
```

### Phase 3: Feature Flag UI (Week 2)
Add a feature flag to toggle between old and new systems:

```typescript
// Add to settings or environment config
const USE_NEW_EVALUATION_SYSTEM = process.env.REACT_APP_USE_NEW_EVALUATIONS === 'true';

// In components
if (USE_NEW_EVALUATION_SYSTEM) {
  // Use newEvaluationService
  const evaluations = await newEvaluationService.getEventEvaluations(eventId);
} else {
  // Use existing PlayerEvaluationService
  const evaluations = await playerEvaluationService.getEvaluationCriteria(...);
}
```

### Phase 4: Testing (Week 3)
1. Create test events using new system
2. Compare data structure and query performance
3. Verify all evaluation flows work correctly
4. Test edge cases and error handling

### Phase 5: Migration Tools (When Ready)
Create tools to migrate data from old to new system:

```sql
-- Migration function (when ready to switch)
CREATE OR REPLACE FUNCTION evaluations.migrate_from_old_system(
    p_event_id uuid DEFAULT NULL
)
RETURNS void AS $$
BEGIN
    -- Migration logic here
    -- Can be run for specific events or all events
END;
$$ LANGUAGE plpgsql;
```

## Benefits of This Approach

1. **Zero Risk** - Old system continues working unchanged
2. **Easy Testing** - Switch between systems with a flag
3. **Gradual Migration** - Move one event at a time if needed
4. **Performance Testing** - Compare query speeds directly
5. **Clean Separation** - New code doesn't touch old code
6. **Easy Rollback** - Just switch the feature flag

## Query Examples

### Old System Query
```sql
-- Complex joins across pre_evaluations and player_evaluations
SELECT ... FROM public.pre_evaluations pe
LEFT JOIN public.player_evaluations pev ON ...
WHERE ...
```

### New System Query
```sql
-- Simple, direct query
SELECT * FROM evaluations.event_evaluations
WHERE event_id = ? AND player_id = ?
ORDER BY category, area;
```

## Next Steps

1. **Create the schema** - I can generate the full SQL script
2. **Build parallel service** - New service class using the evaluations schema
3. **Add feature flag** - Environment variable to toggle systems
4. **Create test UI** - Simple interface to test new system
5. **Compare results** - Side-by-side comparison tool

This approach gives you complete flexibility to test and refine the new system without any risk to your current functionality!