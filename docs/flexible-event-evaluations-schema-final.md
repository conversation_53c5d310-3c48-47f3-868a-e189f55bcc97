# Flexible Event Evaluations Schema - Final Proposal

## Core Principle
Store **one row per evaluation criteria** that applies to a player at the time of the event. This preserves the exact questions, answer options, and maintains complete historical accuracy while supporting variable criteria counts.

## Schema Design

```sql
CREATE TABLE event_evaluations (
    -- Identity
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id uuid NOT NULL REFERENCES events(id),
    player_id uuid NOT NULL REFERENCES profiles(id),
    team_id uuid NOT NULL REFERENCES teams(id),
    
    -- Criteria reference and categorization
    criteria_id uuid REFERENCES evaluation_criteria(id), -- Link to source criteria
    category text NOT NULL, -- 'TECHNICAL', 'PHYSICAL', 'PSYCHOLOGICAL', 'SOCIAL'
    area text NOT NULL, -- More specific area within category
    position text NOT NULL, -- Position this criteria applies to (or 'All')
    
    -- Player position at time of evaluation (frozen)
    player_position text NOT NULL,
    
    -- Questions (all frozen at creation time)
    question_pre text, -- Pre-evaluation question shown to player
    question_coach text NOT NULL, -- Coach evaluation question
    question_post text, -- Post-evaluation question (if applicable)
    
    -- Answer options (frozen at creation time)
    answers_pre jsonb, -- {"1": "Poor", "5": "Average", "10": "Excellent"}
    answers_post jsonb, -- Answer options for post evaluation
    
    -- Evaluation scores
    pre_score integer CHECK (pre_score BETWEEN 1 AND 10),
    coach_score integer CHECK (coach_score BETWEEN 1 AND 10),
    post_score integer CHECK (post_score BETWEEN 1 AND 10),
    
    -- Timestamps for each evaluation
    pre_submitted_at timestamp,
    pre_submitted_by uuid REFERENCES profiles(id),
    
    coach_submitted_at timestamp,
    coach_submitted_by uuid REFERENCES profiles(id),
    
    post_submitted_at timestamp,
    post_submitted_by uuid REFERENCES profiles(id),
    
    -- Metadata
    week_number integer NOT NULL,
    framework_version text DEFAULT 'SHOT-2025',
    created_at timestamp DEFAULT now(),
    updated_at timestamp DEFAULT now(),
    
    -- Tracking
    pre_reminder_sent_at timestamp,
    post_reminder_sent_at timestamp,
    
    -- Constraints
    CONSTRAINT unique_player_event_criteria UNIQUE (event_id, player_id, criteria_id)
);

-- Indexes for performance
CREATE INDEX idx_event_evaluations_event ON event_evaluations(event_id);
CREATE INDEX idx_event_evaluations_player ON event_evaluations(player_id);
CREATE INDEX idx_event_evaluations_category ON event_evaluations(category);
CREATE INDEX idx_event_evaluations_coach_pending ON event_evaluations(event_id, coach_score) 
    WHERE coach_score IS NULL;
CREATE INDEX idx_event_evaluations_pre_pending ON event_evaluations(event_id, player_id, pre_score) 
    WHERE pre_score IS NULL;
```

## Example Data Structure

For a striker in week 15, there might be 7-10 rows depending on the framework:

```sql
-- Example rows for one player in one event
INSERT INTO event_evaluations (
    event_id, player_id, category, area, 
    player_position, question_pre, question_coach, question_post,
    answers_pre, pre_score, coach_score,
    pre_submitted_at, coach_submitted_at
) VALUES 
-- Technical category (2-3 criteria)
('event123', 'player456', 'TECHNICAL', 'First Touch', 
 'STRIKER', 'How confident are you with your first touch?', 
 'Rate the player''s first touch ability',
 'How do you feel your first touch performed today?',
 '{"1": "Not confident", "5": "Somewhat confident", "10": "Very confident"}',
 7, 8, '2024-01-15 09:00:00', '2024-01-15 16:30:00'),
 
('event123', 'player456', 'TECHNICAL', 'Finishing', 
 'STRIKER', 'Rate your finishing ability in the box',
 'Rate the player''s finishing effectiveness',
 'How effective was your finishing today?',
 '{"1": "Poor", "5": "Average", "10": "Clinical"}',
 6, 7, '2024-01-15 09:00:00', '2024-01-15 16:30:00'),

-- Physical category (2-3 criteria) 
('event123', 'player456', 'PHYSICAL', 'Speed',
 'STRIKER', 'How would you rate your speed and acceleration?',
 'Rate the player''s speed and acceleration',
 'How did your speed impact the game today?',
 '{"1": "Slow", "5": "Average pace", "10": "Very fast"}',
 8, 8, '2024-01-15 09:00:00', '2024-01-15 16:30:00'),

-- Psychological category (1-2 criteria)
('event123', 'player456', 'PSYCHOLOGICAL', 'Decision Making',
 'STRIKER', 'How good are your decisions in the final third?',
 'Rate the player''s decision making in attacking situations',
 'How were your decisions in key moments today?',
 '{"1": "Poor choices", "5": "Mixed decisions", "10": "Excellent choices"}',
 7, 6, '2024-01-15 09:00:00', '2024-01-15 16:30:00'),

-- Social category (1-2 criteria)
('event123', 'player456', 'SOCIAL', 'Communication',
 'STRIKER', 'How well do you communicate with teammates?',
 'Rate the player''s communication on the pitch',
 'How was your communication during the match?',
 '{"1": "Very quiet", "5": "Some communication", "10": "Excellent leader"}',
 8, 9, '2024-01-15 09:00:00', '2024-01-15 16:30:00'),

-- Position-specific criteria
('event123', 'player456', 'TECHNICAL', 'Movement in Box',
 'STRIKER', 'How effective is your movement to create space?',
 'Rate the player''s movement and positioning in the box',
 'How was your movement in the box today?',
 '{"1": "Static", "5": "Some good runs", "10": "Constant threat"}',
 7, 8, '2024-01-15 09:00:00', '2024-01-15 16:30:00');
```

## Key Features

### 1. **Variable Criteria Count**
- Number of rows determined by evaluation_criteria matches
- Supports framework changes over time
- Different positions can have different numbers of criteria

### 2. **Complete Question Preservation**
```sql
-- All three question variants stored
question_pre    -- "How confident are you with your first touch?"
question_coach  -- "Rate the player's first touch ability"  
question_post   -- "How do you feel your first touch performed today?"

-- Answer options preserved
answers_pre     -- {"1": "Not confident", "10": "Very confident"}
answers_post    -- {"1": "Needs work", "10": "Exceeded expectations"}
```

### 3. **Individual Timestamps**
- `pre_submitted_at` - When player completed self-assessment
- `coach_submitted_at` - When coach evaluated this specific criteria
- `post_submitted_at` - When player completed reflection
- Allows partial completion tracking

### 4. **Flexible Queries**

```sql
-- Get all criteria for a player's evaluation
SELECT * FROM event_evaluations
WHERE event_id = ? AND player_id = ?
ORDER BY 
    CASE category 
        WHEN 'TECHNICAL' THEN 1
        WHEN 'PHYSICAL' THEN 2
        WHEN 'PSYCHOLOGICAL' THEN 3
        WHEN 'SOCIAL' THEN 4
    END,
    area;

-- Check pre-evaluation completion
SELECT 
    COUNT(*) as total_criteria,
    COUNT(pre_score) as completed_criteria,
    MIN(pre_submitted_at) as started_at,
    MAX(pre_submitted_at) as completed_at
FROM event_evaluations
WHERE event_id = ? AND player_id = ?;

-- Get coach's pending evaluations
SELECT DISTINCT player_id, COUNT(*) as pending_count
FROM event_evaluations
WHERE event_id = ? AND coach_score IS NULL
GROUP BY player_id;

-- Category averages for a player
SELECT 
    category,
    AVG(pre_score) as avg_self_assessment,
    AVG(coach_score) as avg_coach_score,
    AVG(coach_score - pre_score) as avg_difference
FROM event_evaluations
WHERE event_id = ? AND player_id = ?
GROUP BY category;
```

## Creation Process

```sql
CREATE OR REPLACE FUNCTION create_event_evaluations(p_event_id uuid)
RETURNS void AS $$
DECLARE
    v_event record;
    v_member record;
    v_criteria record;
    v_week integer;
BEGIN
    -- Get event details
    SELECT e.*, t.sport_type 
    INTO v_event 
    FROM events e
    JOIN teams t ON t.id = e.team_id
    WHERE e.id = p_event_id;
    
    v_week := EXTRACT(WEEK FROM v_event.date);
    
    -- For each active team member
    FOR v_member IN
        SELECT user_id, position, team_id
        FROM team_members
        WHERE team_id = v_event.team_id
        AND status = 'active'
    LOOP
        -- Get all applicable criteria for this player's position and week
        FOR v_criteria IN
            SELECT 
                ec.id,
                ec.category,
                ec.area,
                ec.position,
                ec.question_pre,
                ec.question as question_coach,
                ec.question_post,
                ec.answers_pre,
                ec.answers_post
            FROM evaluation_criteria ec
            WHERE ec.week_number = v_week
            AND ec.framework = 'SHOT-2025'
            AND ec.sport_type = v_event.sport_type
            AND (
                ec.position = 'All' 
                OR ec.position = map_position_to_evaluation(v_member.position)
            )
            ORDER BY ec.category, ec.area
        LOOP
            INSERT INTO event_evaluations (
                event_id, player_id, team_id,
                criteria_id, category, area, position,
                player_position,
                question_pre, question_coach, question_post,
                answers_pre, answers_post,
                week_number, framework_version
            ) VALUES (
                p_event_id, v_member.user_id, v_member.team_id,
                v_criteria.id, v_criteria.category, v_criteria.area, v_criteria.position,
                COALESCE(map_position_to_evaluation(v_member.position), 'All'),
                v_criteria.question_pre, v_criteria.question_coach, v_criteria.question_post,
                v_criteria.answers_pre, v_criteria.answers_post,
                v_week, 'SHOT-2025'
            );
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

## Benefits Over Current System

| Aspect | Current System | This Proposal |
|--------|---------------|---------------|
| **Tables** | 2 (pre_evaluations, player_evaluations) | 1 (event_evaluations) |
| **Rows per player/event** | Variable and unpredictable | Matches evaluation_criteria count |
| **Question preservation** | Partial | Complete (pre, coach, post) |
| **Answer options** | Sometimes lost | Always preserved |
| **Timestamps** | Event-level only | Per-criteria timestamps |
| **Partial completion** | Hard to track | Simple NULL checks |
| **Historical accuracy** | Questions can change | Frozen at event time |

## Migration Path

```sql
-- 1. Create new table
CREATE TABLE event_evaluations (...);

-- 2. Migrate existing data
INSERT INTO event_evaluations (...)
SELECT ... FROM pre_evaluations pe
JOIN player_evaluations pev ON ...;

-- 3. Update application code
-- 4. Deprecate old tables after verification
```

## Conclusion

This design:
1. **Preserves complete history** - All questions and answers frozen at event time
2. **Handles variable criteria** - Supports any number of evaluation criteria
3. **Tracks individual progress** - Timestamp per criteria per evaluation type
4. **Simplifies queries** - Single table with clear structure
5. **Supports framework evolution** - Old events keep their original questions
6. **Enables rich analytics** - Easy to analyze by category, position, or time