# SHOT Points (SP) Documentation

## Overview
SHOT Points (SP) are the reward system within the SHOT app that incentivizes user engagement and participation. These points are earned through various activities and can be used to unlock features, badges, and other rewards.

## Current Implementation Status

### Database Structure

#### 1. **sport_heads** Table
The main table storing user points and progression data:
- `experience_points` (integer, default: 0) - Total XP earned by the user
- `achievement_points` (integer, default: 0) - Points earned from achievements
- `level` (integer, default: 1) - Current user level
- `prestige_level` (integer, default: 0) - Prestige level after max level

#### 2. **player_xp_transactions** Table
Tracks all XP transactions:
- `player_id` (uuid) - Reference to the player
- `xp_amount` (integer) - Amount of XP earned
- `source_type` (varchar) - Type of activity that earned XP
- `source_id` (uuid) - Reference to the specific activity
- `description` (text) - Human-readable description
- `created_at` (timestamp) - When the XP was earned

#### 3. **pre_evaluations** Table
Stores pre-evaluation data with points:
- `points_earned` (integer, default: 0) - Points earned from this evaluation
- `points_claimed` (boolean, default: false) - Whether points have been claimed

## Points Earning System

### Current Sources of XP

1. **Pre-Training Evaluations**
   - **Amount**: 50 XP per completed evaluation
   - **Source Type**: `pre_evaluation`
   - **Description**: "Completed pre-training evaluation"
   - **Trigger**: When a player completes a pre-evaluation form

### Planned Sources (Not Yet Implemented)
Based on the database structure, the following sources are planned:

1. **Coach Evaluations**
   - Points for receiving evaluations from coaches
   - Points based on improvement trends

2. **Training Attendance**
   - Points for attending training sessions
   - Bonus points for attendance streaks

3. **Achievement Unlocks**
   - One-time points for reaching milestones
   - Special achievements for exceptional performance

4. **Daily Activity**
   - Points for daily logins
   - Streak bonuses for consecutive days

## Points Calculation Logic

### Level Progression
- Users start at Level 1 with 0 XP
- Level progression follows a standard curve (details to be implemented)
- After reaching max level, users can prestige

### Achievement Points
- Separate from XP, achievement points are earned from specific milestones
- Stored in `achievement_points` field in sport_heads table

## Integration with UserContext

### Current State
The UserContext currently has hardcoded SHOT points values. To integrate properly:

1. **Fetch User's Sport Head**
   - Get the primary sport head for the user
   - Access `experience_points` and `achievement_points`

2. **Calculate Total SHOT Points**
   - Total SP = experience_points + achievement_points

3. **Update UserContext**
   - Add fields for:
     - `shotPoints`: Total SHOT points
     - `experiencePoints`: XP portion
     - `achievementPoints`: Achievement portion
     - `level`: Current level
     - `prestigeLevel`: Prestige level

### Implementation Steps

1. **Modify UserContext.tsx**:
   ```typescript
   interface UserProfile {
     // ... existing fields
     shotPoints?: number;
     experiencePoints?: number;
     achievementPoints?: number;
     level?: number;
     prestigeLevel?: number;
   }
   ```

2. **Fetch Sport Head Data**:
   - Query the sport_heads table for the user's primary sport head
   - Include points data in the profile fetch

3. **Real-time Updates**:
   - Subscribe to changes in player_xp_transactions
   - Update context when new XP is earned

## Future Enhancements

1. **Points Redemption System**
   - Exchange points for:
     - Premium features
     - Custom badges
     - Theme unlocks
     - Physical rewards

2. **Leaderboards**
   - Global rankings
   - Team-specific leaderboards
   - Weekly/monthly competitions

3. **Points Multipliers**
   - Double XP events
   - Team bonuses
   - Premium membership multipliers

4. **Social Features**
   - Gift points to teammates
   - Team challenges
   - Collaborative goals

## Technical Notes

- All points calculations should be done server-side to prevent tampering
- Use database triggers or Edge Functions for automatic point allocation
- Implement rate limiting to prevent abuse
- Consider implementing a points audit log for transparency

## Security Considerations

1. **Server-side Validation**
   - Never trust client-side point calculations
   - Validate all point-earning activities server-side

2. **Rate Limiting**
   - Implement daily caps on certain activities
   - Monitor for suspicious activity patterns

3. **Audit Trail**
   - Keep detailed logs of all point transactions
   - Enable rollback capabilities for fraudulent activities