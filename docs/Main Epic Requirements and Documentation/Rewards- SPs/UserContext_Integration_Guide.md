# UserContext SHOT Points Integration Guide

## Implementation Example

This guide shows how to modify the UserContext to fetch and store real SHOT points from the database instead of using hardcoded values.

### 1. Update UserContext Interface

```typescript
// In UserContext.tsx

interface UserProfile {
    id?: string;
    full_name?: string;
    website?: string;
    avatar_url?: string;
    email?: string;
    date_of_birth?: string;
    marketing_email?: boolean;
    marketing_notifications?: boolean;
    privileges?: string[];
    // Add SHOT Points fields
    shotPoints?: number;        // Total SHOT points (XP + Achievement)
    experiencePoints?: number;   // XP from activities
    achievementPoints?: number;  // Points from achievements
    level?: number;             // Current level
    prestigeLevel?: number;     // Prestige level
    sportHeadId?: string;       // Primary sport head ID
}

interface UserContextType {
    // ... existing fields
    profile: UserProfile | null;
    user: any;
    setProfile: React.Dispatch<React.SetStateAction<UserProfile | null>>;
    updateUserInContext: (updatedProfile: UserProfile) => void;
    fetchUserProfile: () => Promise<void>;
    refreshUserData: () => Promise<void>;
    loading: boolean;
    isEmailVerified: boolean;
    // Add SHOT Points specific methods
    refreshShotPoints: () => Promise<void>;
    addXpTransaction: (xpAmount: number, sourceType: string, sourceId?: string) => Promise<void>;
}
```

### 2. Fetch Sport Head Data with Profile

```typescript
// Modified fetchUserProfile function

const fetchUserProfile = useCallback(async (forceRefresh = false) => {
    try {
        console.log('Fetching user profile...', forceRefresh ? '(forced refresh)' : '');
        
        const currentUser = supabase.auth.user();
        
        if (!currentUser) {
            console.log('No authenticated user found');
            setUser(null);
            setProfile(null);
            setLoading(false);
            return;
        }

        setUser(currentUser);

        // Fetch profile with sport head data
        const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select(`
                *,
                primary_sport_head:sport_heads!primary_sport_head_id (
                    id,
                    experience_points,
                    achievement_points,
                    level,
                    prestige_level
                )
            `)
            .eq('id', currentUser.id)
            .maybeSingle();

        if (profileError) {
            console.error('Error fetching profile:', profileError);
            return;
        }

        if (profileData) {
            // Calculate total SHOT points
            const sportHead = profileData.primary_sport_head;
            const shotPoints = sportHead ? 
                (sportHead.experience_points || 0) + (sportHead.achievement_points || 0) : 
                0;

            const enrichedProfile = {
                ...profileData,
                shotPoints,
                experiencePoints: sportHead?.experience_points || 0,
                achievementPoints: sportHead?.achievement_points || 0,
                level: sportHead?.level || 1,
                prestigeLevel: sportHead?.prestige_level || 0,
                sportHeadId: sportHead?.id
            };

            console.log('Profile data loaded with SHOT points:', enrichedProfile);
            setProfile(enrichedProfile);
        } else {
            // If no profile exists, create minimal profile
            setProfile({
                id: currentUser.id,
                email: currentUser.email,
                full_name: null,
                avatar_url: null,
                shotPoints: 0,
                experiencePoints: 0,
                achievementPoints: 0,
                level: 1,
                prestigeLevel: 0
            });
        }
    } catch (error: any) {
        console.error('Error in fetchUserProfile:', error.message);
    } finally {
        setLoading(false);
    }
}, []);
```

### 3. Add Method to Refresh SHOT Points

```typescript
// Add this method to fetch latest SHOT points
const refreshShotPoints = useCallback(async () => {
    if (!user?.id || !profile?.sportHeadId) {
        console.log('No user or sport head ID available');
        return;
    }

    try {
        const { data: sportHeadData, error } = await supabase
            .from('sport_heads')
            .select('experience_points, achievement_points, level, prestige_level')
            .eq('id', profile.sportHeadId)
            .single();

        if (error) {
            console.error('Error fetching SHOT points:', error);
            return;
        }

        if (sportHeadData) {
            const shotPoints = (sportHeadData.experience_points || 0) + 
                             (sportHeadData.achievement_points || 0);

            setProfile(prev => ({
                ...prev!,
                shotPoints,
                experiencePoints: sportHeadData.experience_points || 0,
                achievementPoints: sportHeadData.achievement_points || 0,
                level: sportHeadData.level || 1,
                prestigeLevel: sportHeadData.prestige_level || 0
            }));

            console.log('SHOT points refreshed:', shotPoints);
        }
    } catch (error) {
        console.error('Error refreshing SHOT points:', error);
    }
}, [user?.id, profile?.sportHeadId]);
```

### 4. Add XP Transaction Method

```typescript
// Method to add XP when user completes activities
const addXpTransaction = useCallback(async (
    xpAmount: number, 
    sourceType: string, 
    sourceId?: string,
    description?: string
) => {
    if (!user?.id) {
        console.error('No user ID available for XP transaction');
        return;
    }

    try {
        // Add XP transaction
        const { error: transactionError } = await supabase
            .from('player_xp_transactions')
            .insert({
                player_id: user.id,
                xp_amount: xpAmount,
                source_type: sourceType,
                source_id: sourceId,
                description: description || `Earned ${xpAmount} XP from ${sourceType}`
            });

        if (transactionError) {
            console.error('Error adding XP transaction:', transactionError);
            return;
        }

        // Update sport head XP
        if (profile?.sportHeadId) {
            const { error: updateError } = await supabase
                .rpc('increment', {
                    table_name: 'sport_heads',
                    row_id: profile.sportHeadId,
                    column_name: 'experience_points',
                    increment_value: xpAmount
                });

            if (updateError) {
                console.error('Error updating sport head XP:', updateError);
                return;
            }
        }

        // Refresh points in context
        await refreshShotPoints();
        
        console.log(`Added ${xpAmount} XP for ${sourceType}`);
    } catch (error) {
        console.error('Error in addXpTransaction:', error);
    }
}, [user?.id, profile?.sportHeadId, refreshShotPoints]);
```

### 5. Set Up Real-time Subscription

```typescript
// Add this to the useEffect that sets up listeners
useEffect(() => {
    if (!profile?.sportHeadId) return;

    // Subscribe to sport head changes
    const sportHeadSubscription = supabase
        .from(`sport_heads:id=eq.${profile.sportHeadId}`)
        .on('UPDATE', payload => {
            console.log('Sport head updated:', payload);
            refreshShotPoints();
        })
        .subscribe();

    // Subscribe to XP transactions
    const xpSubscription = supabase
        .from(`player_xp_transactions:player_id=eq.${user?.id}`)
        .on('INSERT', payload => {
            console.log('New XP transaction:', payload);
            refreshShotPoints();
        })
        .subscribe();

    return () => {
        supabase.removeSubscription(sportHeadSubscription);
        supabase.removeSubscription(xpSubscription);
    };
}, [profile?.sportHeadId, user?.id, refreshShotPoints]);
```

### 6. Update Provider Value

```typescript
return (
    <UserContext.Provider value={{ 
        profile, 
        user,
        setProfile, 
        fetchUserProfile, 
        refreshUserData,
        updateUserInContext,
        loading,
        isEmailVerified,
        getProfileAvatar,
        getFilterIcon,
        getQRCodeIcon,
        // Add SHOT Points methods
        refreshShotPoints,
        addXpTransaction
    }}>
        {children}
    </UserContext.Provider>
);
```

## Usage Example

```typescript
// In a component that awards XP
import { useUserContext } from '../contexts/UserContext';

function PreEvaluationForm() {
    const { addXpTransaction } = useUserContext();

    const handleSubmit = async (evaluationData) => {
        try {
            // Submit evaluation...
            const { data: evaluation } = await submitEvaluation(evaluationData);

            // Award XP for completing evaluation
            await addXpTransaction(
                50,                    // XP amount
                'pre_evaluation',      // Source type
                evaluation.id,         // Source ID
                'Completed pre-training evaluation'
            );

            // Show success message
            showToast('Evaluation submitted! You earned 50 XP!');
        } catch (error) {
            console.error('Error submitting evaluation:', error);
        }
    };

    return (
        // ... form JSX
    );
}
```

```typescript
// Display SHOT points in a component
import { useUserContext } from '../contexts/UserContext';

function UserProfileHeader() {
    const { profile } = useUserContext();

    return (
        <div className="profile-header">
            <h2>{profile?.full_name}</h2>
            <div className="shot-points">
                <span className="sp-icon">💎</span>
                <span className="sp-value">{profile?.shotPoints || 0} SP</span>
                <span className="level">Level {profile?.level || 1}</span>
            </div>
        </div>
    );
}
```

## Notes

1. **Database Function Required**: The `increment` RPC function needs to be created in Supabase:
```sql
CREATE OR REPLACE FUNCTION increment(table_name text, row_id uuid, column_name text, increment_value int)
RETURNS void AS $$
BEGIN
  EXECUTE format('UPDATE %I SET %I = %I + $1 WHERE id = $2', table_name, column_name, column_name)
  USING increment_value, row_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

2. **Error Handling**: Add proper error handling and user feedback for failed XP transactions.

3. **Caching**: Consider implementing a local cache for SHOT points to reduce database queries.

4. **Performance**: For frequently updating points, consider batching updates or using debouncing.

5. **Security**: Ensure all XP additions are validated server-side to prevent cheating.