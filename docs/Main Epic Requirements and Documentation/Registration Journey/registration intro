When a user comes to the app without an invite code tehy create a basic SHOT member account (supabase authentication + verification via email. At this point we store Name, email, location (TBC), Nickname, phonenumber)
  Once Verified they enter the app and are defaulted as a user of type 'member' and we show them generic 'member' content on Clubhouse, Locker and Pulse tabs.  
  
  DYNAMIC PERFORM PAGE
  The Perform tab shows information regarding improving yourself ['Just for me' BUTTON LINK] or joining a club/team ['Join a club' BUTTON LINK] or coach link ['Coach' BUTTON LINK]
  On first entry this is dynamic and has specific messaging to sell the benefits of SHOT more holistically

  JUST FOR ME
  If the member selects the 'Just for me' button they are able to create an Instant Session [An event with current timestamp and of type 'instant event'] which then enables them to do their own evaluations on themselves and will follow links to improvement drills etc.

  JOIN A CLUB
  If the member selects the 'Join a club' button they are asked for an invite code then whether they are a player or a guardian and their DOB. 
  If over 18 they return back to the Perform page with their first/next pre-evaluation ready to do (CHECK THIS WITH LIAM)
  If under 18 a modal appears with a 'parent invite code' section, which allows the under 18 player to auto-generate an invite code and share via whatsapp or any other means the url or QR code of the event.

  The parent receives the link to register (with embedded invite code) and once they have registered they land on the PERFORM section of the app with their son/daughter's pre-evaluation ready to be completed plus information on the team they have joined and additional information (alergies etc, marketing preferences etc) they will need to provide.

  COACH
  If the member selects 'Coach' button from the perform page they are able to enter the club name,team name, Sport type (sport_id) and Date of Birth in a free form manner that is sent to SHOT superadmins for verification
  Once the SHOT superadmins have verified the club and created the club in the background the Coach is notified (somehow) and they are able to see their club in the perform section. They are then able to create their own team within the Perform area.

