# User Onboarding Journey Format v2 - Sports App

## Overview: Universal Member Registration

All users start with the same basic registration process, becoming SHOT 'members' before specializing their journey through the dynamic Perform page.

## Core Registration Flow (All Users)

```mermaid
flowchart TD
    Start([New User Arrives]) --> Register[Basic Registration<br/>- Name<br/>- Email<br/>- Location TBD<br/>- Nickname<br/>- Phone Number]
    
    Register --> CreateAuth[Create Supabase Auth Account]
    CreateAuth --> SendVerify[Send Email Verification]
    SendVerify --> AwaitVerify{Email Verified?}
    
    AwaitVerify -->|No| ResendOption[Resend Email Option]
    AwaitVerify -->|Yes| CreateProfile[Create Profile<br/>Default type: 'member']
    
    ResendOption --> SendVerify
    
    CreateProfile --> MainApp[Enter Main App<br/>Access as Basic Member]
    
    MainApp --> ViewContent[View Generic Content:<br/>- Clubhouse<br/>- Locker<br/>- Pulse<br/>- Perform Dynamic]
    
    ViewContent --> PerformPage[Dynamic Perform Page<br/>Marketing Content +<br/>Three Action Buttons]
    
    PerformPage --> UserChoice{User Selects Path}
    
    UserChoice -->|Just for Me| SoloPath[Solo Member Path]
    UserChoice -->|Join a Club| TeamPath[Team Member Path]
    UserChoice -->|Coach| CoachPath[Coach Path]
    
    style Start fill:#e1f5fe
    style CreateProfile fill:#e8f5e9
    style PerformPage fill:#fff3e0
```

### Data Flow - Basic Registration

```sql
-- Step 1: Create in auth.users (Supabase Auth)
-- Returns user_id

-- Step 2: Create basic member profile
INSERT INTO profiles (
    id, full_name, nickname, email, phone,
    email_verified, onboarding_completed,
    registration_metadata
) VALUES (
    auth_user_id, ?, ?, ?, ?,
    false, false,
    jsonb_build_object(
        'user_type', 'member',
        'registration_date', now(),
        'location', 'selected_location'
    )
)

-- Step 3: After email verification
UPDATE profiles 
SET email_verified = true,
    updated_at = now()
WHERE id = ?
```

---

## 1. JUST FOR ME PATH (Solo Member)

### Visual Flow Diagram

```mermaid
flowchart TD
    Start([Member on Perform Page]) --> ClickJustForMe[Click Just for Me Button]
    
    ClickJustForMe --> EnableFeatures[Enable Solo Features]
    
    EnableFeatures --> UpdateProfile[Update Profile<br/>Set solo_mode = true]
    
    UpdateProfile --> ShowSoloUI[Show Solo UI:<br/>- Create Instant Session<br/>- Self-Evaluation Tools<br/>- Personal Progress]
    
    ShowSoloUI --> CreateInstant{Create Instant Session?}
    
    CreateInstant -->|Yes| InstantSession[Create Event<br/>- Type: instant<br/>- Timestamp: now<br/>- Sport: selected]
    
    InstantSession --> PreEval[Immediate Pre-Evaluation<br/>Self-Assessment]
    
    PreEval --> DoActivity[Perform Activity<br/>Access Drill Links]
    
    DoActivity --> PostEval[Immediate Post-Evaluation<br/>Self-Assessment]
    
    PostEval --> ViewProgress[View Personal Progress<br/>Track Improvements]
    
    CreateInstant -->|Later| StayOnPerform[Stay on Perform Page<br/>Can Create Anytime]
    
    style Start fill:#e1f5fe
    style ShowSoloUI fill:#c8e6c9
    style ViewProgress fill:#e8f5e9
```

### Data Flow - Just for Me

```sql
-- Enable solo mode
UPDATE profiles 
SET context_preferences = jsonb_set(
    context_preferences,
    '{perform_mode}',
    '"solo"'
),
sport_affiliations = jsonb_build_array(
    jsonb_build_object(
        'type', 'individual',
        'sport', 'selected_sport',
        'mode', 'just_for_me'
    )
)
WHERE id = ?

-- Create instant session
INSERT INTO events (
    title, event_type, sport_type,
    created_by, start_time, end_time,
    is_instant_event
) VALUES (
    'Personal Training', 
    'instant', 
    ?, 
    ?,
    now(), 
    now(),
    true
)

-- Create self-evaluation
INSERT INTO player_evaluations (
    player_id, evaluator_id, event_id,
    evaluation_type, category, area, rating
) VALUES (?, ?, ?, 'self', ?, ?, ?)
```

---

## 2. JOIN A CLUB PATH (Team Member)

### Visual Flow Diagram

```mermaid
flowchart TD
    Start([Member on Perform Page]) --> ClickJoinClub[Click Join a Club Button]
    
    ClickJoinClub --> EnterCode[Enter Invite Code]
    EnterCode --> ValidateCode{Valid Code?}
    
    ValidateCode -->|Invalid| ShowError[Show Error<br/>Try Again]
    ValidateCode -->|Valid| SelectRole[Select Role:<br/>- Player<br/>- Guardian]
    
    ShowError --> EnterCode
    
    SelectRole --> EnterDOB[Enter Date of Birth]
    
    EnterDOB --> CheckAge{Age Check}
    
    CheckAge -->|18+| Adult[Adult Player Flow]
    CheckAge -->|Under 18| Minor[Minor Player Flow]
    
    Adult --> JoinTeamDirect[Join Team<br/>Add to team_members]
    
    Minor --> GenerateParentCode[Auto-Generate<br/>Parent Invite Code]
    
    GenerateParentCode --> ShowShareModal[Modal: Share Parent Code<br/>- WhatsApp<br/>- Copy URL<br/>- QR Code]
    
    ShowShareModal --> ParentRegisters[Parent Registers<br/>with Embedded Code]
    
    ParentRegisters --> ParentSession[Create parent_onboarding_session]
    
    ParentSession --> ParentLands[Parent Lands on Perform<br/>Shows Child Pre-Evaluation<br/>+ Team Info<br/>+ Additional Info Forms]
    
    ParentLands --> ParentCompletes[Parent Completes:<br/>- Child's Evaluation<br/>- Allergies<br/>- Marketing Preferences<br/>- Consent]
    
    ParentCompletes --> ChildLinked[Link Child to Parent<br/>Update parent_id]
    
    ChildLinked --> ChildCanAccess[Child Can Access Team]
    
    JoinTeamDirect --> BackToPerform[Return to Perform<br/>Show First Pre-Evaluation]
    ChildCanAccess --> BackToPerform
    
    style Start fill:#e1f5fe
    style CheckAge fill:#fff3e0
    style Minor fill:#ffebee
    style ParentLands fill:#ffe0b2
    style BackToPerform fill:#c8e6c9
```

### Data Flow - Join a Club

#### Adult Player (18+)
```sql
-- Validate invite code
SELECT * FROM invite_codes 
WHERE code = ? AND is_valid = true

-- Create team join request
INSERT INTO team_join_requests (
    team_id, player_id, invite_code_id,
    requested_by, status
) VALUES (?, ?, ?, ?, 'approved')

-- Add to team immediately
INSERT INTO team_members (
    team_id, user_id, role, status
) VALUES (?, ?, 'player', 'active')

-- Update profile
UPDATE profiles 
SET context_preferences = jsonb_set(
    context_preferences,
    '{perform_mode}',
    '"team"'
)
WHERE id = ?
```

#### Minor Player (Under 18)
```sql
-- Generate parent invite code
INSERT INTO invite_codes (
    code, code_type, team_id, 
    created_by, metadata, is_valid
) VALUES (
    generate_unique_code(),
    'parent_verification',
    ?,
    child_id,
    jsonb_build_object(
        'child_id', child_id,
        'child_name', child_name,
        'team_id', team_id,
        'invite_code_used', original_code
    ),
    true
)

-- Create parent onboarding session
INSERT INTO parent_onboarding_sessions (
    session_token, child_name, 
    child_date_of_birth, child_profile_id,
    status
) VALUES (
    ?, ?, ?, ?,
    'awaiting_parent'
)

-- Parent registration creates link
UPDATE profiles 
SET parent_id = parent_profile_id
WHERE id = child_id

-- Update parent onboarding session
UPDATE parent_onboarding_sessions
SET parent_profile_id = ?,
    parent_email = ?,
    status = 'completed',
    completed_at = now()
WHERE child_profile_id = ?
```

---

## 3. COACH PATH

### Visual Flow Diagram

```mermaid
flowchart TD
    Start([Member on Perform Page]) --> ClickCoach[Click Coach Button]
    
    ClickCoach --> CoachForm[Coach Registration Form:<br/>- Club Name<br/>- Team Name<br/>- Sport Type<br/>- Date of Birth]
    
    CoachForm --> CheckCoachAge{Age 18+?}
    
    CheckCoachAge -->|No| RejectCoach[Cannot be Coach<br/>Must be 18+]
    CheckCoachAge -->|Yes| SubmitForm[Submit to SHOT Admin]
    
    RejectCoach --> BackToPerform[Back to Perform Page]
    
    SubmitForm --> CreateRequest[Create Verification Request<br/>Status: pending]
    
    CreateRequest --> NotifyAdmin[Notify SHOT Superadmins]
    
    NotifyAdmin --> AdminReview{Admin Reviews}
    
    AdminReview -->|Verify Club Exists| CheckClub[Check Existing Clubs]
    AdminReview -->|Create New Club| CreateClub[Create Club Record]
    AdminReview -->|Reject| RejectRequest[Reject with Reason]
    
    CheckClub --> ApproveCoach
    CreateClub --> ApproveCoach[Approve Coach Request]
    
    ApproveCoach --> NotifyCoach[Notify Coach<br/>Email/SMS/In-App]
    
    NotifyCoach --> UpdateProfile[Update Profile<br/>privileges += coach]
    
    UpdateProfile --> CoachPerform[Coach Sees Club<br/>in Perform Section]
    
    CoachPerform --> CreateTeam[Can Create Team<br/>Within Club]
    
    CreateTeam --> ManageTeam[Manage Team:<br/>- Generate Invite Codes<br/>- Create Events<br/>- View Roster]
    
    style Start fill:#e1f5fe
    style CheckCoachAge fill:#fff3e0
    style RejectCoach fill:#ffcdd2
    style AdminReview fill:#fff9c4
    style CoachPerform fill:#c8e6c9
```

### Data Flow - Coach Path

```sql
-- Submit coach verification request
INSERT INTO coach_verification_requests (
    user_id, club_name, team_name,
    sport_type, date_of_birth,
    status, submitted_at
) VALUES (
    ?, ?, ?, ?, ?,
    'pending', now()
)

-- Notify admins via sms_queue or notification system
INSERT INTO admin_notifications (
    type, priority, data,
    created_at
) VALUES (
    'coach_verification',
    'normal',
    jsonb_build_object(
        'user_id', ?,
        'club_name', ?,
        'team_name', ?
    ),
    now()
)

-- Admin approves and creates/links club
INSERT INTO clubs (
    club_name, sport_type, 
    created_by, verification_status
) VALUES (?, ?, admin_id, 'approved')

-- Grant coach privileges
UPDATE profiles 
SET privileges = array_append(privileges, 'coach'),
    context_preferences = jsonb_set(
        context_preferences,
        '{perform_mode}',
        '"coach"'
    )
WHERE id = coach_id

-- Create team under club
INSERT INTO teams (
    team_name, club_id, created_by,
    sport_type, status
) VALUES (?, ?, coach_id, ?, 'active')

-- Add coach to team
INSERT INTO team_members (
    team_id, user_id, role, status
) VALUES (?, coach_id, 'coach', 'active')
```

---

## 4. DYNAMIC PERFORM PAGE

### Page State Logic

```mermaid
stateDiagram-v2
    [*] --> MemberDefault: Email Verified
    
    MemberDefault --> JustForMe: Click Just for Me
    MemberDefault --> JoinClub: Click Join a Club
    MemberDefault --> Coach: Click Coach
    
    JustForMe --> SoloMember: Enable Solo Mode
    JoinClub --> TeamMember: Join Team Success
    Coach --> PendingCoach: Submit Verification
    
    PendingCoach --> ActiveCoach: Admin Approved
    PendingCoach --> MemberDefault: Admin Rejected
    
    SoloMember --> HybridMember: Later Joins Team
    TeamMember --> HybridMember: Adds Solo Training
    
    note right of MemberDefault
        Shows marketing content
        Three action buttons
        Generic member view
    end note
    
    note right of SoloMember
        Personal training
        Self-evaluations
        Can still join teams
    end note
    
    note right of TeamMember
        Team schedule
        Coach evaluations
        Can add solo training
    end note
    
    note right of ActiveCoach
        Team management
        Create events
        View evaluations
    end note
```

### Perform Page Content by State

```sql
-- Determine what to show on Perform page
SELECT 
    CASE
        WHEN 'coach' = ANY(privileges) THEN 'coach_dashboard'
        WHEN EXISTS (
            SELECT 1 FROM team_members 
            WHERE user_id = ? AND status = 'active'
        ) THEN 'team_member_view'
        WHEN context_preferences->>'perform_mode' = 'solo' THEN 'solo_training_view'
        ELSE 'marketing_with_buttons'
    END as perform_view_type
FROM profiles
WHERE id = ?
```

---

## 5. PARENT ONBOARDING DETAIL

### Parent Registration from Child Link

```mermaid
sequenceDiagram
    participant Child
    participant System
    participant Parent
    
    Child->>System: Under 18, wants to join team
    System->>System: Generate parent invite code
    System->>Child: Show share modal
    Child->>Parent: Share link/QR via WhatsApp
    
    Parent->>System: Click link - code embedded
    System->>Parent: Registration page
    Parent->>System: Complete registration
    System->>System: Create parent profile
    System->>System: Link to child via parent_id
    
    System->>Parent: Land on Perform page
    Note over Parent: Sees:<br/>1. Child pre-evaluation<br/>2. Team info<br/>3. Additional info forms
    
    Parent->>System: Complete evaluation
    Parent->>System: Add allergies, preferences
    Parent->>System: Give consent
    
    System->>Child: Unlock team access
    System->>Parent: Show child's team dashboard
```

### Parent Landing Experience

```sql
-- Parent lands after registration with child's code
SELECT 
    c.full_name as child_name,
    t.team_name,
    t.sport_type,
    pe.* -- pre-evaluation details
FROM profiles c
JOIN team_members tm ON c.id = tm.user_id
JOIN teams t ON tm.team_id = t.team_id
LEFT JOIN pre_evaluations pe ON pe.player_id = c.id
WHERE c.parent_id = parent_id
AND pe.status = 'pending'
ORDER BY pe.created_at DESC
LIMIT 1

-- Show additional info forms needed
SELECT 
    'allergies' as form_type,
    'Please provide allergy information' as prompt
WHERE NOT EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = child_id 
    AND allergies IS NOT NULL
)
UNION
SELECT 
    'emergency_contact',
    'Emergency contact required'
WHERE NOT EXISTS (
    SELECT 1 FROM profiles 
    WHERE id = child_id 
    AND emergency_contact IS NOT NULL
)
```

---

## 6. KEY IMPLEMENTATION NOTES

### Database Schema Requirements

```sql
-- New/Modified tables needed

-- Coach verification requests
CREATE TABLE coach_verification_requests (
    id uuid PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id uuid REFERENCES profiles(id),
    club_name text NOT NULL,
    team_name text NOT NULL,
    sport_type text NOT NULL,
    date_of_birth date NOT NULL,
    status varchar DEFAULT 'pending',
    reviewed_by uuid REFERENCES profiles(id),
    reviewed_at timestamp,
    rejection_reason text,
    submitted_at timestamp DEFAULT now(),
    created_at timestamp DEFAULT now()
);

-- Track user journey state
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS
    perform_journey_state jsonb DEFAULT '{
        "current_mode": "member",
        "modes_activated": ["member"],
        "last_action": null
    }';

-- Parent invite codes distinct from team codes
ALTER TABLE invite_codes ADD CONSTRAINT check_code_type 
CHECK (code_type IN (
    'team_join', 
    'parent_verification', 
    'personal', 
    'event'
));
```

### Critical Business Rules

1. **Universal Start**: Everyone begins as 'member' type
2. **Age Gates**: 
   - Coaches must be 18+
   - Players under 18 need parent verification
3. **Dynamic Perform**: Content changes based on user actions
4. **Parent Experience**: Land directly on child's evaluation
5. **Manual Verification**: Coaches require admin approval

### Security Considerations

- Parent codes are single-use and child-specific
- Parent can only see/edit their own children
- Coach verification prevents unauthorized team creation
- Age verification required for role assignment

---

## 7. TESTING SCENARIOS

### Happy Path Tests
1. **Solo Adult**: Register → Verify → Just for Me → Create Instant Session
2. **Adult Team Player**: Register → Verify → Join Club → Enter Code → Join Team
3. **Minor Team Player**: Register → Verify → Join Club → Generate Parent Code → Parent Completes
4. **Coach**: Register → Verify → Coach → Submit → Admin Approves → Create Team

### Edge Cases
1. **Age Boundary**: Exactly 18 years old on coach registration
2. **Parent Multiple Children**: Parent with existing account adds second child
3. **Role Switching**: Solo member later joins team
4. **Rejected Coach**: What happens after admin rejection
5. **Expired Parent Code**: Child generates code but parent delays

---

## 8. METRICS & MONITORING

### Conversion Funnel
```sql
-- Track perform page actions
INSERT INTO activity_log (
    user_id, action, metadata
) VALUES (?, 'perform_button_clicked', 
    jsonb_build_object(
        'button', 'just_for_me|join_club|coach',
        'timestamp', now()
    )
);

-- Monitor parent conversion
SELECT 
    COUNT(*) FILTER (WHERE status = 'started') as codes_generated,
    COUNT(*) FILTER (WHERE status = 'completed') as parents_registered,
    COUNT(*) FILTER (WHERE completed_at IS NOT NULL) as evaluations_done
FROM parent_onboarding_sessions
WHERE created_at > now() - interval '30 days';
```

---

*Document Version: 2.0 - Updated for universal member registration with dynamic Perform page*