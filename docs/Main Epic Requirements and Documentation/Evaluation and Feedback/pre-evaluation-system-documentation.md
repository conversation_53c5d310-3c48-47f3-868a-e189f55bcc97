# Pre-Evaluation System Documentation

**Last Updated:** 2025-01-10T17:15:00Z

## Change Log
- 2025-01-10 17:15: Updated Perform page to show all outstanding pre-evaluations for future events instead of just one
- 2025-01-10 17:30: Added expandable team progress to pre-evaluation cards with member completion status

## Overview

The pre-evaluation system in SHOT allows coaches to request self-assessments from players before training sessions or events. Players receive SMS notifications with a link to complete their pre-evaluation, which helps coaches prepare personalized training sessions.

## System Architecture

### Main Components

1. **ShadowPreEvaluationSMS** (`/src/components/shadow/ShadowPreEvaluationSMS.tsx`)
   - Shadow DOM modal for requesting pre-evaluations via SMS
   - Handles event publishing status validation
   - Integrates with PreEvaluationRequestButton

2. **PreEvaluationRequestButton** (`/src/pages/section/Coach/components/PreEvaluationRequestButton.tsx`)
   - Core component for sending pre-evaluation requests
   - Manages player selection and notification sending
   - Handles framework template selection
   - Creates pre-evaluation records in database

3. **PlayerSelfEvaluationForm** (`/src/pages/section/Coach/components/PlayerSelfEvaluation/PlayerSelfEvaluationForm.tsx`)
   - The form players see when clicking SMS link
   - Works both within app and as standalone page (for SMS access)
   - Displays event countdown timer
   - Shows category-based evaluation questions
   - Saves player ratings and feedback

4. **ShadowPreEvaluationCard** (`/src/components/shadow/ShadowPreEvaluationCard.tsx`)
   - Display card showing pre-evaluation status
   - Shows urgency levels based on time remaining
   - Includes team completion progress
   - Animated based on urgency

5. **PreEvaluationService** (`/src/services/PreEvaluationService.ts`)
   - Service layer for pre-evaluation operations
   - Handles statistics calculation
   - Manages pre-evaluation status checks

6. **Updated: ShadowPreEvaluationCard with Team Members**
   - Now supports expandable team progress
   - Shows all team members and their completion status
   - Highlights current player in the list
   - Click to expand/collapse team member list

## Database Schema

### pre_evaluations table
- `id`: UUID primary key
- `event_id`: Reference to event
- `player_id`: Reference to player
- `team_id`: Reference to team
- `framework_version`: Evaluation framework being used
- `week_number`: Week in the training program
- `status`: pending | in_progress | completed | submitted
- `viewed`: Boolean
- `viewed_at`: Timestamp
- `questions_answered`: Number of completed questions
- `total_questions`: Total questions in evaluation
- `completion_percentage`: Generated column (calculated)

### player_evaluations table
- Links to pre_evaluations via `pre_evaluation_id`
- Stores individual question responses
- `player_rating`: Player's self-assessment (1-5)
- `player_submitted_at`: Submission timestamp

## User Flow

### Coach Flow
1. Coach navigates to team/event page
2. Clicks "Request Pre-Evaluations" button
3. Modal opens showing:
   - Event details
   - Framework information
   - Player selection list
4. Coach selects players and sends requests
5. System creates pre-evaluation records
6. SMS notifications sent to players

### Player Flow (SMS Access)
1. Player receives SMS with evaluation link
2. Clicks link (format: `/evaluation/pre/{id}`)
3. Sees branded SHOT page (no login required)
4. Views:
   - Event countdown timer
   - Welcome message from coach
   - Category-based questions
5. Rates themselves using sliders (1-5)
6. Submits to earn SP (SHOT Points)
7. Sees success message

### Player Flow (In-App)
1. Player sees all pending pre-evaluation cards on Perform page
   - Shows all outstanding pre-evaluations for future events
   - Each card displays event name, date, and team progress
2. Clicks "Complete Evaluation" on any card
3. Same form as SMS but with app header/footer
4. After submission, returns to Perform page

## Key Features

### Framework Integration
- Uses SHOT-2025 framework by default
- Supports custom frameworks per event
- Falls back gracefully if no framework available
- Questions adapt based on:
  - Sport type
  - Player position
  - Week number
  - Categories (Technical, Physical, Psychological, Social)

### Notification System
- SMS notifications via Supabase RPC functions
- Configurable notification channels (SMS, email, push)
- Retry mechanism for failed notifications
- Status tracking per player

### Security & Access
- Anonymous access for SMS links (no login required)
- Supabase RLS policies handle permissions
- Pre-evaluations linked to authenticated players
- Unique IDs prevent unauthorized access

### UI/UX Features
- Shadow DOM components for consistent styling
- SHOT brand colors and fonts
- Responsive design for mobile
- Urgency indicators (color-coded borders)
- Progress tracking and team completion stats
- Animated buttons and interactions
- **NEW: Expandable team progress showing all team members**
- **NEW: Multiple pre-evaluation cards display**
- **NEW: Live countdown timers with seconds**

## Technical Implementation

### Routes
```typescript
// Anonymous access (SMS links)
<Route exact path="/evaluation/pre/:preEvaluationId" component={PlayerSelfEvaluationForm} />

// Legacy test format
<Route exact path="/pre-evaluation-test.html?id={id}" component={PlayerSelfEvaluationForm} />
```

### RPC Functions Used
- `create_basic_pre_evaluations`: Creates pre-evaluation records
- `create_pre_evaluation_notifications`: Sends SMS notifications
- `update_event_notification_settings`: Updates notification preferences

### Error Handling
- Graceful fallbacks for missing frameworks
- Retry mechanism for failed notifications
- Clear error messages for users
- Logging for debugging

## Future Enhancements
1. Email and push notification support
2. Bulk operations for multiple events
3. Analytics dashboard for completion rates
4. Custom question templates
5. Integration with training recommendations

## Troubleshooting

### Common Issues
1. **"Event Not Published" Error**
   - Event must be published before sending pre-evaluations
   - Check event status in database

2. **Players Not Receiving SMS**
   - Verify phone numbers in profiles
   - Check notification service status
   - Review RPC function logs

3. **Framework Not Loading**
   - Ensure framework exists in evaluation_framework_metadata
   - Check framework_version matches
   - Verify sport_type alignment

### Debug Information
- Check browser console for errors
- Review Supabase logs for RPC failures
- Verify pre_evaluations table records
- Check notification_queue for SMS status
