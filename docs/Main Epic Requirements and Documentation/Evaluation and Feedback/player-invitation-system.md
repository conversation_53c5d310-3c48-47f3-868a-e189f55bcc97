# Player Invitation System Documentation

## Table of Contents
1. [Overview](#overview)
2. [Invitation Flow](#invitation-flow)
3. [Key Functions](#key-functions)
4. [Database Operations](#database-operations)
5. [User Interface](#user-interface)
6. [Pre-Evaluation Process](#pre-evaluation-process)
7. [Technical Implementation](#technical-implementation)

## Overview

The SHOT app uses a multi-step process to invite players to events and evaluations. The system handles two distinct invitation types:

1. **Event Participation Invitations** - Inviting players to attend an event
2. **Pre-Evaluation Requests** - Sending self-assessment requests to participants

## Invitation Flow

### Step 1: Create Event
Coach creates an event with optional pre-evaluation enabled:
```typescript
// Event creation includes:
{
  name: "Training Session",
  start_datetime: "2025-01-20T18:00:00",
  is_pre_session_evaluation: true,  // Enable pre-evaluations
  sport_framework: "SHOT-2025"       // Evaluation framework
}
```

### Step 2: Add Participants
Coach navigates to the invite page and selects players to invite.

### Step 3: Send Pre-Evaluation Requests (Optional)
If pre-evaluations are enabled, coach sends assessment requests via SMS.

## Key Functions

### 1. Adding Event Participants

**Service**: `EventParticipantService`  
**Method**: `addMultipleParticipants()`

```typescript
async addMultipleParticipants(
  eventId: string, 
  userIds: string[], 
  role: 'player' | 'coach' | 'observer' = 'player'
): Promise<EventParticipant[]>
```

**Process**:
1. Validates event ID and user IDs
2. Creates participant records with:
   - Unique `participant_id` (UUID)
   - `invitation_status`: 'invited'
   - `role`: Always 'player' for team members
   - Timestamps for tracking
3. Checks for existing participants to avoid duplicates
4. Inserts new participants into database
5. Returns created participant records

**Database Operations**:
```sql
-- Check existing participants
SELECT user_id FROM event_participants 
WHERE event_id = ? AND user_id IN (?)

-- Insert new participants
INSERT INTO event_participants (
  participant_id, event_id, user_id, role, 
  invitation_status, invited_at, created_at, updated_at
) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
```

### 2. Creating Pre-Evaluations

**Database Function**: `create_basic_pre_evaluations`  
**Called via**: RPC from frontend

```sql
-- This function is called to create pre-evaluation records
-- It generates evaluation questions based on:
-- - Event's sport framework (e.g., SHOT-2025)
-- - Week number in the program
-- - Player positions
-- - TPPS categories (Technical, Physical, Psychological, Social)
```

**Process**:
1. Creates `pre_evaluations` record for each participant
2. Triggers `generate_player_evaluations_for_pre_evaluation()`
3. Creates 4-5 evaluation questions per player
4. Sets status to 'pending'

### 3. Sending Evaluation Notifications

**Service**: `selfEvaluationService`  
**Method**: `sendPreEvaluationReminder()`

```typescript
export const sendPreEvaluationReminder = async (
  preEvaluationIds: string[],
  playerIds: string[],
  eventName: string
): Promise<boolean>
```

**Process**:
1. Calls database RPC `create_pre_evaluation_notifications`
2. Creates SMS notification records
3. Includes personalized evaluation link
4. Queues messages for sending

## Database Operations

### Tables Involved

#### 1. `event_participants`
Stores who is invited to each event:
```sql
{
  participant_id: UUID (PK)
  event_id: UUID (FK)
  user_id: UUID (FK)
  role: 'player' | 'coach' | 'observer'
  invitation_status: enum
  invited_at: timestamp
  responded_at: timestamp
  attendance_confirmed_at: timestamp
}
```

#### 2. `pre_evaluations`
Stores evaluation requests:
```sql
{
  id: UUID (PK)
  event_id: UUID (FK)
  player_id: UUID (FK)
  team_id: UUID (FK)
  status: 'pending' | 'started' | 'completed'
  requested_at: timestamp
  expires_at: timestamp
}
```

#### 3. `pre_evaluation_notifications`
Tracks notification delivery:
```sql
{
  id: UUID (PK)
  pre_evaluation_id: UUID (FK)
  notification_type: 'initial' | 'reminder'
  channel: 'sms' | 'email' | 'push'
  status: 'pending' | 'sent' | 'delivered' | 'failed'
  sent_at: timestamp
}
```

## User Interface

### Event Invites Page (`EventInvites.tsx`)

**URL**: `/coach/club/{clubId}/team/{teamId}/event/{eventId}/invite`

**Features**:
1. **Team Member List**
   - Shows all team members not yet invited
   - Filters: Active/Inactive/All
   - Search by name or position
   - Shows member status (active/inactive)

2. **Selection Controls**
   - Checkbox for each player
   - "Select All" button
   - "Clear" button
   - Selected count display

3. **Already Invited Section**
   - Shows participants already added
   - Status badges (invited/confirmed/declined)
   - Cannot be re-invited

4. **Send Invites Button**
   - Shows count of selected players
   - Confirmation dialog
   - Success/error toasts

### Pre-Evaluation Management

**Component**: `PlayerSelfEvaluationStatus`

**Features**:
1. Shows completion progress
2. "Send Evaluations" button (initial send)
3. "Send Reminders" button (follow-up)
4. Player list with completion status

## Pre-Evaluation Process

### 1. Initial Setup
When coach enables pre-evaluations for an event:
```typescript
eventDetails.is_pre_session_evaluation = true
eventDetails.sport_framework = "SHOT-2025"
```

### 2. Creating Evaluations
Coach clicks "Send Pre-Evaluation Requests":
1. System calls `create_basic_pre_evaluations` RPC
2. Creates pre-evaluation records for all participants
3. Generates personalized questions based on:
   - Player position
   - Current week in program
   - Sport type

### 3. Notification Flow
```mermaid
sequenceDiagram
    Coach->>System: Click "Send Evaluations"
    System->>Database: Create pre_evaluation records
    Database->>Database: Trigger generates questions
    System->>SMS Service: Queue notifications
    SMS Service->>Player: Send SMS with link
    Player->>App: Click evaluation link
    App->>Player: Show pre-assessment form
```

### 4. Tracking Progress
- Real-time updates every 10 seconds
- Progress shown as percentage
- Status indicators:
  - Not started (gray)
  - In progress (yellow)
  - Completed (green)

## Technical Implementation

### Service Layer Architecture

```typescript
// EventParticipantService - Manages event invitations
class EventParticipantService {
  // Add players to event
  addMultipleParticipants(eventId, userIds, role)
  
  // Update invitation status
  updateParticipantStatus(participantId, status)
  
  // Get participant list
  getEventParticipants(eventId)
}

// selfEvaluationService - Manages evaluations
{
  // Create evaluation requests
  createPreEvaluations(eventId, playerIds)
  
  // Send notifications
  sendPreEvaluationReminder(preEvaluationIds, playerIds, eventName)
  
  // Track progress
  getPreEvaluationStats(eventId, teamId)
}
```

### State Management

```typescript
// Event Details Page State
const [participants, setParticipants] = useState<any[]>([]);
const [preEvaluationStats, setPreEvaluationStats] = useState({
  total: 0,
  pending: 0,
  inProgress: 0,
  completed: 0
});

// Auto-refresh for live updates
useEffect(() => {
  const interval = setInterval(() => {
    refreshPreEvaluationStats();
  }, 10000); // Every 10 seconds
}, []);
```

### Error Handling

1. **Duplicate Prevention**
   - Check existing participants before adding
   - Filter out already-invited players

2. **Validation**
   - Require event ID and user IDs
   - Validate user roles
   - Check team membership

3. **User Feedback**
   - Success toasts for completed actions
   - Error messages for failures
   - Loading states during operations

## Best Practices

### 1. Invitation Management
- Always check for existing participants
- Use bulk operations for performance
- Provide clear status indicators

### 2. Pre-Evaluation Timing
- Send evaluations 24-48 hours before event
- Send reminders 4-6 hours before
- Set expiration times appropriately

### 3. User Experience
- Show real-time progress updates
- Provide clear call-to-action buttons
- Use consistent status colors/icons

### 4. Data Integrity
- Use database triggers for related records
- Maintain status history
- Log all state changes

## Troubleshooting

### Common Issues

1. **Players Not Receiving Invitations**
   - Check SMS service status
   - Verify phone numbers in profiles
   - Check notification queue

2. **Duplicate Participants**
   - Database checks prevent this
   - UI filters out already-invited

3. **Pre-Evaluations Not Created**
   - Verify event has `is_pre_session_evaluation = true`
   - Check participant count > 0
   - Ensure framework is set

### Debug Points

```typescript
// Check participant addition
console.log('Adding participants:', { eventId, userIds, role });

// Verify pre-evaluation creation
console.log('Pre-evaluation stats:', preEvaluationStats);

// Monitor notification status
console.log('Notification sent:', { playerId, status });
```

---

*Last Updated: January 2025*  
*Version: 1.0*