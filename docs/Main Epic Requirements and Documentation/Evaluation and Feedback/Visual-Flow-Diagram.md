# Pre-Evaluation Visual Flow

## Player Journey

```mermaid
graph TD
    Start[Player Joins Team] --> Check{New Member?}
    Check -->|Yes < 7 days| ShowPrompt[Show Pre-Eval Prompt<br/>Top of /perform page]
    Check -->|No >= 7 days| ShowLower[Show Pre-Eval Prompt<br/>Below action cards]
    
    ShowPrompt --> Click[Click Start Pre-Evaluation]
    ShowLower --> Click
    
    Click --> Create[Create Record<br/>Status: pending]
    Create --> Form[Load Form<br/>/evaluation/pre/{id}]
    
    Form --> Step1[Step 1: Player Info<br/>- Skill Level<br/>- Experience<br/>- Previous Teams<br/>- Positions<br/>- Fitness]
    
    Step1 --> Save1{Save Draft?}
    Save1 -->|Yes| Draft1[Status: started]
    Save1 -->|No| Step2
    Draft1 --> Step2
    
    Step2[Step 2: Goals<br/>- What to achieve<br/>- Concerns]
    
    Step2 --> Save2{Save Draft?}
    Save2 -->|Yes| Draft2[Update responses]
    Save2 -->|No| Step3
    Draft2 --> Step3
    
    Step3[Step 3: Availability<br/>- Training days<br/>- Injuries<br/>- Commitments]
    
    Step3 --> Save3{Save Draft?}
    Save3 -->|Yes| Draft3[Update responses]
    Save3 -->|No| Step4
    Draft3 --> Step4
    
    Step4[Step 4: Review<br/>Summary of all info]
    
    Step4 --> Submit[Submit Evaluation]
    Submit --> Update[Status: submitted<br/>completed_at: now]
    Update --> Success[Success Page]
    Success --> Notify[Notify Coaches]
    
    style ShowPrompt fill:#0d9488,color:#fff
    style Click fill:#0d9488,color:#fff
    style Submit fill:#0d9488,color:#fff
    style Success fill:#10b981,color:#fff
```

## Coach Journey

```mermaid
graph TD
    NewEval[New Pre-Evaluation Submitted] --> Notification[Coach Receives Notification]
    
    Notification --> Dashboard[Coach Dashboard<br/>Shows pending count]
    Dashboard --> List[Pre-Evaluations List<br/>/coach/pre-evaluations]
    
    List --> Filter{Apply Filters}
    Filter --> TeamFilter[Filter by Team]
    Filter --> StatusFilter[Filter by Status<br/>- All<br/>- Pending<br/>- Viewed]
    
    TeamFilter --> ShowList[Display Evaluations]
    StatusFilter --> ShowList
    
    ShowList --> Click[Click Evaluation]
    Click --> Detail[Evaluation Detail Page<br/>/coach/pre-evaluations/{id}]
    
    Detail --> View[View Player Info<br/>- Skills<br/>- Goals<br/>- Availability<br/>- Concerns]
    
    View --> MarkViewed[Auto-mark as Viewed]
    MarkViewed --> AddNotes[Add Coach Notes]
    
    AddNotes --> Save[Save & Mark Actioned]
    Save --> UpdateStatus[Status: actioned<br/>actioned_at: now]
    
    UpdateStatus --> BackToList[Return to List]
    
    List --> Insights[View Team Insights]
    Insights --> Analytics[Analytics Dashboard<br/>- Avg Skill Level<br/>- Best Training Days<br/>- Injury Count<br/>- Skill Distribution]
    
    style Notification fill:#eab308,color:#000
    style Dashboard fill:#8b5cf6,color:#fff
    style Save fill:#0d9488,color:#fff
    style Analytics fill:#3b82f6,color:#fff
```

## Data Flow

```mermaid
graph LR
    subgraph Player Side
        Form[Pre-Eval Form] --> API1[Create/Update API]
    end
    
    subgraph Database
        API1 --> Table[(pre_evaluations)]
        Table --> Trigger[on_submitted trigger]
        Trigger --> Notif[(notifications)]
    end
    
    subgraph Coach Side
        Notif --> CoachUI[Coach Interface]
        Table --> CoachUI
        CoachUI --> API2[Update API]
        API2 --> Table
    end
    
    style Table fill:#1f2937,color:#fff
    style Notif fill:#1f2937,color:#fff
```

## Component Architecture

```
src/
├── pages/
│   ├── perform/
│   │   ├── MemberPerformV2.tsx (contains prompt)
│   │   └── components/
│   │       └── PreEvaluationPrompt.tsx ✅
│   │
│   ├── evaluation/
│   │   └── pre/
│   │       ├── PreEvaluationForm.tsx ❌ (needs creation)
│   │       └── PreEvaluationSuccess.tsx ❌ (needs creation)
│   │
│   └── section/
│       └── Coach/
│           └── components/
│               └── PreEvaluations/
│                   ├── PreEvaluationsList.tsx ❌
│                   ├── PreEvaluationDetail.tsx ❌
│                   └── TeamInsights.tsx ❌
│
└── hooks/
    └── usePreEvaluation.ts ❌
```

## Status Workflow

```mermaid
stateDiagram-v2
    [*] --> pending: Created
    pending --> started: Player opens form
    started --> started: Save draft
    started --> submitted: Submit form
    submitted --> viewed: Coach views
    viewed --> actioned: Coach adds notes
    actioned --> [*]
    
    note right of pending: Initial state
    note right of started: In progress
    note right of submitted: Completed by player
    note right of viewed: Seen by coach
    note right of actioned: Processed by coach
```

## UI Components Used

### Shadow DOM Components (Your Design System)
- `ShadowActionCard` - For the prompt on /perform page
- `ShadowHeaderSubHeader` - For page headers
- `ShadowNotificationModal` - For success/error messages

### Form Elements
- Range sliders for skill/fitness levels
- Text inputs for experience/positions
- Textareas for goals/concerns
- Checkboxes for availability
- Standard buttons with Tailwind styling

### Icons (Lucide React)
- `ClipboardCheck` - Pre-evaluation icon
- `ChevronRight` - Navigation
- `Save` - Save draft
- `Send` - Submit
- `User` - Player avatar
- `Calendar` - Date info
- `TrendingUp` - Skills
- `AlertCircle` - Warnings
- `Target` - Goals
- `Clock` - Availability
