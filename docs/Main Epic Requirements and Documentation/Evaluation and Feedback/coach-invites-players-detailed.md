# Coach Invites Players - Detailed Flow Documentation

## Table of Contents
1. [Overview](#overview)
2. [When Players Are Invited](#when-players-are-invited)
3. [How Players Are Invited](#how-players-are-invited)
4. [Automatic vs Manual Process](#automatic-vs-manual-process)
5. [Technical Implementation](#technical-implementation)
6. [User Experience Flow](#user-experience-flow)
7. [Edge Cases and Considerations](#edge-cases-and-considerations)

## Overview

The process of inviting players to events in the SHOT app is **semi-automatic**. When a coach creates an event, they can select players during the creation process, and those players are automatically added as participants. However, this is not a fully automatic process - the coach must explicitly choose which players to invite.

## When Players Are Invited

### 1. **During Event Creation** (Primary Method)
- **Timing**: As part of the event creation flow
- **Location**: Create Event page (`/coach/club/{clubId}/team/{teamId}/create-event`)
- **Selection**: Coach chooses attendees using the `PlayerAttendanceSelector` component

### 2. **After Event Creation** (Secondary Method)
- **Timing**: Any time after the event is created
- **Location**: Event Invites page (`/coach/club/{clubId}/team/{teamId}/event/{eventId}/invite`)
- **Purpose**: Add players who were missed during initial creation

### 3. **Never Automatic**
- Players are NEVER automatically invited based on team membership
- No scheduled or triggered invitations
- Coach must always make an explicit selection

## How Players Are Invited

### During Event Creation Flow

```mermaid
flowchart TD
    A[Coach clicks Create Event] --> B[Event Creation Form]
    B --> C[PlayerAttendanceSelector Component]
    C --> D{Coach Selects Option}
    D -->|All Players| E[All team members selected]
    D -->|Active Only| F[Only active members selected]
    D -->|Choose| G[Manual selection modal]
    E --> H[Coach fills other details]
    F --> H
    G --> H
    H --> I[Coach clicks Create Event]
    I --> J[EventService.createEvent called]
    J --> K[Event created in database]
    K --> L[EventParticipantService.addMultipleParticipants]
    L --> M[Participant records created]
    M --> N[Players are now invited]
```

### Key Component: PlayerAttendanceSelector

The `PlayerAttendanceSelector` component provides three options:

1. **"All Players"** (Default)
   - Automatically selects every team member
   - Includes both active and inactive players
   - This is the default selection on initial load

2. **"Active Only"**
   - Selects only players with `status: 'active'`
   - Filters out inactive/suspended players

3. **"Choose"**
   - Opens a modal for manual selection
   - Coach can search and select specific players
   - Provides checkboxes for individual selection

### Code Implementation

```typescript
// In CreateEvent.tsx
const CreateEvent: React.FC = () => {
  // State for selected attendees
  const [selectedAttendees, setSelectedAttendees] = useState<string[]>([]);
  
  // PlayerAttendanceSelector manages the selection
  <PlayerAttendanceSelector
    teamId={teamId}
    selectedPlayers={selectedAttendees}
    onChange={(playerIds) => {
      setSelectedAttendees(playerIds);
    }}
  />
  
  // When creating event, attendees are passed to service
  const eventData = {
    name: title,
    start_datetime: startDateTime,
    // ... other fields
  };
  
  // This is where the invitation happens!
  const newEvent = await EventService.createEvent(eventData, selectedAttendees);
};
```

## Automatic vs Manual Process

### What IS Automatic:

1. **Default Selection**: 
   - On initial load, "All Players" is selected by default
   - This means all team members are pre-selected
   - Coach can change this before creating the event

2. **Participant Record Creation**:
   - Once coach clicks "Create Event", the system automatically:
   - Creates the event record
   - Creates `event_participants` records for all selected players
   - Sets their status to "invited"

3. **Database Triggers**:
   - `trigger_update_event_participant_count` updates participant counts
   - Status tracking triggers maintain history

### What is NOT Automatic:

1. **Player Selection**:
   - Coach must be present and click "Create Event"
   - No background jobs or scheduled invitations
   - No rule-based automatic invitations

2. **Notifications**:
   - Creating participant records does NOT send notifications
   - SMS/Email notifications require separate action
   - Pre-evaluation requests are a separate step

3. **Future Events**:
   - No recurring event invitations
   - Each event requires manual player selection

## Technical Implementation

### 1. Initial Player Loading

```typescript
// In PlayerAttendanceSelector.tsx
useEffect(() => {
  const loadTeamMembers = async () => {
    const members = await teamMemberService.getTeamMembers(teamId);
    const players = members.map(member => ({
      user_id: member.user_id,
      full_name: member.profiles?.full_name || 'Unknown Player',
      is_active: member.status === 'active',
      // ... other fields
    }));
    
    setAllPlayers(players);
    
    // Auto-select all players on initial load
    if (!initialLoadComplete && selectedOption === 'all') {
      const allPlayerIds = players.map(player => player.user_id);
      setSelectedIds(allPlayerIds);
      onChange(allPlayerIds); // Notify parent component
      setInitialLoadComplete(true);
    }
  };
  
  loadTeamMembers();
}, [teamId]);
```

### 2. Event Creation with Attendees

```typescript
// In EventService.ts
async createEvent(eventData, attendees?: string[]): Promise<Event> {
  // 1. Create the event
  const { data: event, error } = await supabase
    .from('events')
    .insert([eventToInsert])
    .select()
    .single();
    
  // 2. Add attendees if provided
  if (attendees && attendees.length > 0 && event.id) {
    await EventParticipantService.addMultipleParticipants(
      event.id,
      attendees,
      'player' // Default role
    );
  }
  
  return event;
}
```

### 3. Adding Participants

```typescript
// In EventParticipantService.ts
async addMultipleParticipants(eventId, userIds, role = 'player') {
  // Create participant records
  const participants = userIds.map(userId => ({
    participant_id: uuidv4(),
    event_id: eventId,
    user_id: userId,
    role,
    invitation_status: 'invited',
    invited_at: now,
    created_at: now,
    updated_at: now
  }));
  
  // Insert into database
  const { data, error } = await supabase
    .from('event_participants')
    .insert(participants)
    .select();
    
  return data;
}
```

## User Experience Flow

### Coach Perspective

1. **Navigate to Team Page**
   - Click "Create Event" button

2. **See Event Creation Form**
   - Player attendance selector shows "All Players" selected
   - Number shows (e.g., "15 Players Selected")

3. **Can Change Selection**
   - Click "Active Only" to filter
   - Click "Choose" to manually select
   - Search and select specific players

4. **Complete Form**
   - Fill in event details
   - Optionally enable pre-evaluations

5. **Submit**
   - Click "Create Event"
   - See success message
   - Redirect to event details

### Player Perspective

1. **Initial Invitation**
   - No immediate notification
   - Status in database is "invited"

2. **View in App**
   - Event appears in their upcoming events
   - Can see invitation status

3. **Respond to Invitation**
   - Can confirm/decline attendance
   - Status updates in database

4. **Receive Notifications** (if coach sends them)
   - SMS for pre-evaluations
   - Reminders before event

## Edge Cases and Considerations

### 1. **Team with No Players**
- UI shows "No players in team"
- Event can still be created without participants
- Players can be added later

### 2. **Inactive Players**
- Included in "All Players" selection
- Excluded from "Active Only"
- Shown with "(Inactive)" label

### 3. **Large Teams**
- All players loaded at once (no pagination)
- Search functionality helps find specific players
- Performance considerations for 50+ players

### 4. **Failed Participant Addition**
- Event is still created successfully
- Error logged but not shown to user
- Coach can add players later via invite page

### 5. **Duplicate Prevention**
- System checks for existing participants
- Prevents adding same player twice
- Silently skips duplicates

## Best Practices

### For Developers

1. **Always Load Team Members First**
   - Ensure team data is available before showing selector
   - Handle loading states appropriately

2. **Validate Selections**
   - Check for empty arrays
   - Validate user IDs before submission

3. **Handle Errors Gracefully**
   - Event creation should succeed even if participant addition fails
   - Log errors for debugging

### For Coaches

1. **Review Default Selection**
   - "All Players" is selected by default
   - Change if you don't want everyone invited

2. **Use Active Filter**
   - For regular training, use "Active Only"
   - For special events, might want "All Players"

3. **Manual Selection for Specific Groups**
   - Use "Choose" for position-specific training
   - Search functionality helps with large teams

## Summary

The player invitation process is:
- **Semi-automatic**: Default selection but requires coach action
- **Flexible**: Three selection modes for different needs
- **Immediate**: Happens during event creation
- **Database-driven**: Creates participant records, not notifications
- **Two-stage**: Invitation records first, notifications separately

This design balances automation with coach control, ensuring that coaches maintain authority over who attends their events while minimizing the manual work required.

---

*Last Updated: January 2025*  
*Version: 1.0*