# SHOT Evaluation System - Technical Findings and Implementation Details

## Table of Contents
1. [Overview](#overview)
2. [Database Architecture](#database-architecture)
3. [Evaluation Flow Process](#evaluation-flow-process)
4. [Event and Evaluation Integration](#event-and-evaluation-integration)
5. [Completion Tracking](#completion-tracking)
6. [Technical Implementation Details](#technical-implementation-details)
7. [Known Issues and Bugs](#known-issues-and-bugs)
8. [Cross-References](#cross-references)

## Overview

Based on investigation of the codebase and database, the SHOT evaluation system is a comprehensive player assessment framework that integrates with events and training sessions. This document details the technical implementation findings.

## Database Architecture

### Core Tables

#### 1. **Events Table**
```sql
events
├── id (UUID)
├── name (required)
├── start_datetime (required)
├── hosted_by (required)
├── team_id
├── club_id
├── event_type ('training' | 'match' | 'assessment')
├── status ('draft' | 'published' | 'cancelled' | 'completed')
├── is_pre_session_evaluation (boolean)
├── sport_framework (e.g., 'SHOT-2025')
├── current_participants (number)
└── max_participants (number)
```

#### 2. **Event Participants**
```sql
event_participants
├── id
├── event_id
├── participant_id (player UUID)
├── role
├── status
└── attendance_status
```

#### 3. **Pre-Evaluations**
```sql
pre_evaluations
├── id
├── event_id
├── player_id
├── sport_head_id
├── team_id
├── template_id
├── framework_version
├── status ('not_started' | 'started' | 'completed')
├── requested_at
├── started_at
├── completed_at
├── completion_percentage
└── evaluation_status
```

#### 4. **Evaluation Sessions**
```sql
evaluation_sessions
├── id
├── team_id
├── event_id
├── evaluator_id
├── session_date
├── framework_version
├── is_completed
└── notes
```

#### 5. **Player Evaluations**
```sql
player_evaluations
├── evaluation_id
├── player_id
├── evaluated_by
├── criteria_id
├── rating (1-5)
├── overall_rating
└── created_at
```

### Summary Views

#### 1. **event_evaluation_summary**
Aggregates evaluation data per event:
- Total participants
- Attended participants
- Evaluated participants
- Completion percentage
- Average ratings

#### 2. **player_pre_evaluation_summary**
Tracks pre-evaluation completion status per player/event

#### 3. **player_evaluation_history_view**
Historical evaluation data for trend analysis

## Evaluation Flow Process

### 1. Event Creation with Evaluations

```javascript
// EventService.createEvent()
1. Create event with is_pre_session_evaluation = true
2. Add participants via EventParticipantService
3. Triggers fire to update participant counts
```

### 2. Pre-Evaluation Generation

**Current Implementation:**
- Pre-evaluations are NOT automatically created when participants are added
- Must be explicitly requested by coach
- Uses `create_basic_pre_evaluations` RPC function

**Expected Flow:**
```
Event Created → Participants Added → Coach Requests Pre-Evaluations → 
Pre-Evaluation Records Created → Players Complete → Training → 
Coach Evaluates → Post-Session Reflection
```

### 3. Database Triggers

#### Key Triggers on `event_participants`:
1. **`trigger_update_event_participant_count`**
   - Updates `current_participants` count on events table
   - Fires on INSERT, UPDATE, DELETE

2. **`trigger_update_evaluation_completion_participants`**
   - Updates evaluation completion tracking
   - Manages evaluation status changes

3. **`participant_status_tracking_trigger`**
   - Tracks participant status changes
   - Maintains audit trail

## Event and Evaluation Integration

### Event Types Supporting Evaluations
1. **Training Sessions** - Primary use case
2. **Matches** - Can have pre/post evaluations
3. **Assessments** - Dedicated evaluation events

### Evaluation Framework
- **SHOT-2025**: 36-week progressive framework
- **Categories**: Technical, Physical, Psychological, Social
- **Position-Specific**: Additional questions based on player position

### Evaluation Types per Skill
1. **Pre-Session**: "How confident are you at..."
2. **Coach Observation**: "Did the player demonstrate..."
3. **Post-Session**: "How well did you perform..."

## Completion Tracking

### How Completion is Calculated

Based on `event_evaluation_summary` view:
```sql
completion_percentage = (evaluated_participants / total_participants) * 100
```

### Status Tracking
- **not_started**: No evaluations begun
- **in_progress**: Some evaluations completed
- **completed**: All evaluations finished

### Important Finding
The 29% completion issue discovered is a **UI bug**, not a database issue:
- Database correctly shows 0% completion for new events
- No pre-evaluations exist yet
- UI may be caching or miscalculating

## Technical Implementation Details

### Service Layer

#### EventService (`/services/EventService.ts`)
- Handles event CRUD operations
- Manages participant addition
- Validates required fields
- Generates unique slugs

#### EventParticipantService
- Manages event-participant relationships
- Handles bulk participant operations
- Triggers evaluation generation

### Authentication Integration
- Uses Supabase Auth for user identification
- RLS policies enforce access control
- `created_by` field tracks event creator

### Data Validation
```javascript
// Required fields for event creation
- name (non-empty string)
- start_datetime (valid timestamp)
- hosted_by (organization/club)
- status (enum validation)
```

## Known Issues and Bugs

### 1. UI Completion Percentage Bug
**Issue**: New events show 29% completion when they should show 0%
**Root Cause**: Frontend calculation error
**Database Status**: Correctly shows 0%
**Fix**: Check UI component calculating completion

### 2. Pre-Evaluation Auto-Generation
**Issue**: Pre-evaluations not automatically created
**Expected**: Should create when `is_pre_session_evaluation = true`
**Workaround**: Coach must manually request evaluations

### 3. Missing Email Field
**Issue**: Profiles table lacks email field
**Impact**: Coach management shows empty emails
**Fix Applied**: Set email to empty string in service layer

## Cross-References

### Related Documentation
1. **[evaluation-system-architecture.md](./evaluation-system-architecture.md)**
   - Comprehensive system architecture
   - Database schema details
   - Implementation roadmap

2. **[evaluation-system-summary.md](./evaluation-system-summary.md)**
   - High-level overview
   - Key features
   - User flows

3. **[Pre-Evaluation-Design.md](./Pre-Evaluation-Design.md)**
   - UI/UX design specifications
   - User journey maps

4. **[self-improvement-evaluation-flow.md](./self-improvement-evaluation-flow.md)**
   - Personal training features
   - Individual progress tracking

### Key Differences from Documentation
1. **Auto-generation**: Documentation suggests automatic pre-evaluation creation, but implementation requires manual request
2. **Completion Tracking**: More complex than documented, involves multiple views and calculations
3. **Email Integration**: Documentation assumes email availability, but profiles lack email field

### Integration Points
1. **Events → Pre-Evaluations**: One-to-many relationship
2. **Pre-Evaluations → Player Evaluations**: Triggered generation
3. **Evaluation Sessions → Player Evaluations**: Coach assessment container
4. **Event Participants → Evaluation Tracking**: Participant count drives completion

## Recommendations

### Immediate Fixes
1. Debug UI completion percentage calculation
2. Implement auto-generation of pre-evaluations
3. Add proper error handling for missing user authentication

### Future Enhancements
1. Add email field to profiles or link to auth.users
2. Implement evaluation reminder system
3. Add bulk evaluation management tools
4. Create evaluation analytics dashboard

### Performance Considerations
1. Index foreign keys in evaluation tables
2. Optimize summary view calculations
3. Implement caching for framework metadata
4. Consider pagination for large team evaluations

---

*Document Version: 1.0*  
*Created: January 9, 2025*  
*Based on: Code investigation and database analysis*  
*Author: Technical Investigation*
