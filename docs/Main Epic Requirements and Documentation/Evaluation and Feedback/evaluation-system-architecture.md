# SHOT Evaluation System Architecture

## Table of Contents
1. [Overview](#overview)
2. [Current System Architecture](#current-system-architecture)
3. [Evaluation Types](#evaluation-types)
4. [Database Schema](#database-schema)
5. [Evaluation Flow](#evaluation-flow)
6. [Proposed: Self-Improvement Evaluations](#proposed-self-improvement-evaluations)
7. [Implementation Roadmap](#implementation-roadmap)

## Overview

The SHOT evaluation system is a comprehensive framework for assessing player development across four key areas: Technical, Physical, Psychological, and Social skills. The system supports pre-session self-assessments, post-session reflections, and coach evaluations.

### Key Features
- 36-week progressive evaluation framework
- Position-specific questions
- Pre and post-session self-assessments
- Coach evaluations
- Progress tracking and analytics

## Current System Architecture

### Core Components

```
┌─────────────────────────────────────────────────────────────┐
│                     SHOT-2025 Framework                      │
│  ┌─────────────┬─────────────┬──────────────┬────────────┐ │
│  │  TECHNICAL  │  PHYSICAL   │ PSYCHOLOGICAL│   SOCIAL   │ │
│  └─────────────┴─────────────┴──────────────┴────────────┘ │
└─────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                    Evaluation Criteria                       │
│  • Base Questions (All positions)                           │
│  • Position-specific Questions                              │
│  • Week-based Progression (1-36)                           │
└─────────────────────────────────────────────────────────────┘
                               │
                ┌──────────────┴──────────────┐
                ▼                             ▼
┌───────────────────────────┐   ┌───────────────────────────┐
│    Pre-Evaluations        │   │   Player Evaluations      │
│  • Self-assessment        │   │  • Coach ratings          │
│  • Before session         │   │  • Player self-ratings    │
│  • Confidence-based       │   │  • Performance-based      │
└───────────────────────────┘   └───────────────────────────┘
```

### Database Tables

#### 1. `evaluation_framework_metadata`
- Stores framework definitions (SHOT-2025)
- Defines categories, positions, and duration

#### 2. `evaluation_criteria`
- Contains all evaluation questions
- Three question types per skill:
  - `question`: Coach evaluation
  - `question_pre`: Pre-session self-assessment
  - `question_post`: Post-session reflection
- Organized by week, category, and position

#### 3. `pre_evaluations`
- Tracks evaluation requests
- Links to events or training sessions
- Manages completion status

#### 4. `player_evaluations`
- Stores actual evaluation responses
- Contains both coach ratings and player self-ratings
- Links to pre-evaluations

#### 5. `event_comprehensive_summary` (View)
- Comprehensive view providing aggregated event statistics
- Includes participant counts, pre-evaluation progress, and post-evaluation progress
- Handles edge cases like division by zero for percentage calculations
- Single source of truth for event dashboard displays

## Evaluation Types

### 1. Pre-Session Self-Assessment
**Purpose**: Players assess their confidence level before training
- **Scale**: 1-5 confidence scale
- **Focus**: "How good are you at..."
- **Example**: "How good are you at using both feet when dribbling?"

### 2. Post-Session Self-Reflection
**Purpose**: Players reflect on actual performance
- **Scale**: 1-5 performance scale
- **Focus**: "How well did I..."
- **Example**: "How well did I use both feet while dribbling?"

### 3. Coach Evaluation
**Purpose**: Objective assessment by coaches
- **Scale**: 1-5 observation scale
- **Focus**: "Did the player..."
- **Example**: "Did the player try to use both feet while dribbling under control?"

## Database Schema

### Key Relationships

```sql
events (1) ──────────────> (n) pre_evaluations
   │                              │
   │                              │ (trigger: generate_player_evaluations)
   │                              ▼
   │                         (n) player_evaluations
   │                              │
   └──────────────────────────────┘
   
evaluation_criteria ──────> player_evaluations
(provides questions)        (stores responses)

event_comprehensive_summary (VIEW)
   │
   ├── events (base table)
   ├── event_participants (participant stats)
   ├── pre_evaluations (pre-eval progress)
   └── player_evaluations (post-eval progress)
```

### Event Comprehensive Summary View

The `event_comprehensive_summary` view provides a unified interface for event statistics:

```sql
CREATE VIEW event_comprehensive_summary AS
SELECT 
    -- Event Basic Info
    e.id AS event_id,
    e.name AS event_name,
    e.event_type,
    e.start_datetime,
    e.team_id,
    e.is_pre_session_evaluation,
    
    -- Participant Statistics
    COUNT(DISTINCT ep.participant_id) AS total_participants,
    COUNT(DISTINCT CASE WHEN ep.invitation_status = 'confirmed' THEN ep.participant_id END) AS confirmed_count,
    COUNT(DISTINCT CASE WHEN ep.invitation_status = 'attended' THEN ep.participant_id END) AS attended_count,
    
    -- Pre-Evaluation Statistics (Before Event)
    COUNT(DISTINCT pre.id) AS pre_eval_total,
    COUNT(DISTINCT CASE WHEN pre.status = 'completed' THEN pre.id END) AS pre_eval_completed,
    CASE 
        WHEN COUNT(DISTINCT pre.id) = 0 THEN 0
        ELSE ROUND(100.0 * COUNT(DISTINCT CASE WHEN pre.status = 'completed' THEN pre.id END) / COUNT(DISTINCT pre.id), 0)
    END AS pre_eval_completion_percentage,
    
    -- Post-Evaluation Statistics (After Event - Coach Evaluations)
    COUNT(DISTINCT pe.player_id) AS post_eval_completed_players,
    ROUND(AVG(pe.rating), 2) AS post_eval_avg_rating,
    
    -- Combined Evaluation Status
    CASE
        WHEN e.is_pre_session_evaluation AND COUNT(DISTINCT pre.id) = 0 THEN 'pre_eval_not_created'
        WHEN e.is_pre_session_evaluation AND COUNT(DISTINCT CASE WHEN pre.status = 'completed' THEN pre.id END) = 0 THEN 'pre_eval_not_started'
        WHEN e.is_pre_session_evaluation AND COUNT(DISTINCT CASE WHEN pre.status = 'completed' THEN pre.id END) < COUNT(DISTINCT pre.id) THEN 'pre_eval_in_progress'
        WHEN e.is_pre_session_evaluation AND COUNT(DISTINCT CASE WHEN pre.status = 'completed' THEN pre.id END) = COUNT(DISTINCT pre.id) THEN 'pre_eval_completed'
        ELSE 'unknown'
    END AS overall_evaluation_status
    
FROM events e
LEFT JOIN event_participants ep ON e.id = ep.event_id
LEFT JOIN pre_evaluations pre ON e.id = pre.event_id
LEFT JOIN player_evaluations pe ON e.id = pe.event_id
GROUP BY e.id;
```

**Key Features:**
- Handles division by zero in percentage calculations
- Provides pre-evaluation completion status even when no pre-evaluations exist
- Single query for all event statistics
- Distinguishes between "not created" and "not started" states

### Triggers and Functions

1. **`trigger_generate_player_evaluations`**
   - Fires on `pre_evaluations` INSERT
   - Calls `generate_player_evaluations_for_pre_evaluation()`
   - Creates 4-5 evaluation records per player

2. **`create_basic_pre_evaluations`**
   - RPC function for creating pre-evaluations
   - Used by coaches to request evaluations
   - Handles bulk creation for teams

3. **`EventSummaryService`**
   - TypeScript service for accessing event_comprehensive_summary view
   - Provides methods for fetching event statistics
   - Handles null/undefined cases gracefully
   - Used by UI components to display accurate progress percentages

## Evaluation Flow

### Current Team-Based Flow

1. **Event Creation**
   - Coach creates training/match event
   - Assigns to team

2. **Pre-Evaluation Request**
   - Coach sends evaluation requests (SMS)
   - System creates `pre_evaluations` records
   - Trigger generates questions

3. **Player Self-Assessment**
   - Player accesses via link
   - Completes pre-session questions
   - Saves confidence ratings

4. **Training Session**
   - Physical training occurs

5. **Post-Session Evaluation**
   - Coach evaluates players
   - Players complete self-reflection
   - Data saved to `player_evaluations`

6. **Analysis**
   - Compare pre/post assessments
   - Track progress over time

## Proposed: Self-Improvement Evaluations

### Concept
Enable players to track progress from individual training sessions outside of team context.

### Use Cases
1. **Solo Training**: Individual practice sessions
2. **Friend Training**: Informal sessions with friends
3. **Parent/Child**: Family training sessions
4. **Personal Coach**: 1-on-1 coaching outside team

### Architecture Extensions

#### New Tables

```sql
-- Individual training sessions
CREATE TABLE personal_training_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    player_id UUID REFERENCES auth.users(id),
    session_name VARCHAR(255) NOT NULL,
    session_type VARCHAR(50), -- 'solo', 'friend', 'coach', 'parent'
    partner_name VARCHAR(255), -- For non-solo sessions
    location VARCHAR(255),
    start_time TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER,
    notes TEXT,
    weather_conditions VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Skills practiced in personal sessions
CREATE TABLE personal_session_skills (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES personal_training_sessions(id),
    category VARCHAR(50), -- TECHNICAL, PHYSICAL, etc.
    skill_name VARCHAR(255),
    duration_minutes INTEGER,
    intensity_level INTEGER CHECK (intensity_level BETWEEN 1 AND 5),
    notes TEXT
);

-- Personal evaluations (simplified)
CREATE TABLE personal_evaluations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES personal_training_sessions(id),
    criteria_id UUID REFERENCES evaluation_criteria(id),
    pre_rating INTEGER CHECK (pre_rating BETWEEN 1 AND 5),
    post_rating INTEGER CHECK (post_rating BETWEEN 1 AND 5),
    improvement_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Personal progress tracking
CREATE TABLE personal_progress_goals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    player_id UUID REFERENCES auth.users(id),
    category VARCHAR(50),
    skill_area VARCHAR(255),
    current_level INTEGER CHECK (current_level BETWEEN 1 AND 5),
    target_level INTEGER CHECK (target_level BETWEEN 1 AND 5),
    target_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    achieved_at TIMESTAMP WITH TIME ZONE
);
```

#### New Components

1. **Personal Training Dashboard**
   ```typescript
   interface PersonalTrainingDashboard {
     recentSessions: PersonalTrainingSession[];
     skillProgress: SkillProgressChart[];
     upcomingGoals: PersonalProgressGoal[];
     suggestedDrills: Drill[];
   }
   ```

2. **Quick Session Logger**
   - Mobile-optimized interface
   - Quick skill selection
   - Timer integration
   - Pre/post ratings

3. **Skill Library**
   - Categorized skill drills
   - Video demonstrations
   - Difficulty levels
   - Equipment requirements

### Implementation Features

#### 1. Simplified Evaluation Flow
```
Start Session → Select Skills → Timer → Quick Pre-Rating → Train → Quick Post-Rating → Save
```

#### 2. Flexible Skill Selection
- Quick picks from recent skills
- Browse by category
- Search functionality
- Custom skill addition

#### 3. Progress Visualization
- Personal best tracking
- Improvement trends
- Skill radar charts
- Achievement badges

#### 4. Social Features (Optional)
- Share progress with coach
- Compare with friends (opt-in)
- Challenge system
- Training buddy matching

### API Endpoints

```typescript
// Personal Training Sessions
POST   /api/personal-training/sessions
GET    /api/personal-training/sessions
PUT    /api/personal-training/sessions/:id
DELETE /api/personal-training/sessions/:id

// Personal Evaluations
POST   /api/personal-training/evaluate
GET    /api/personal-training/progress/:category
GET    /api/personal-training/history

// Goals
POST   /api/personal-training/goals
PUT    /api/personal-training/goals/:id
GET    /api/personal-training/goals/active
```

### RLS Policies

```sql
-- Players can only manage their own data
CREATE POLICY "Players manage own sessions" ON personal_training_sessions
    FOR ALL USING (auth.uid() = player_id);

CREATE POLICY "Players manage own evaluations" ON personal_evaluations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM personal_training_sessions pts
            WHERE pts.id = personal_evaluations.session_id
            AND pts.player_id = auth.uid()
        )
    );
```

## Implementation Roadmap

### Phase 1: Foundation (Weeks 1-2)
- [ ] Create database tables
- [ ] Implement RLS policies
- [ ] Create basic API endpoints
- [ ] Build session creation UI

### Phase 2: Evaluation Flow (Weeks 3-4)
- [ ] Quick evaluation interface
- [ ] Timer integration
- [ ] Skill selection UI
- [ ] Data persistence

### Phase 3: Progress Tracking (Weeks 5-6)
- [ ] Progress charts
- [ ] Goal setting UI
- [ ] Achievement system
- [ ] Historical views

### Phase 4: Enhancements (Weeks 7-8)
- [ ] Drill library
- [ ] Video integration
- [ ] Social features
- [ ] Coach sharing

### Phase 5: Polish (Weeks 9-10)
- [ ] Performance optimization
- [ ] Offline support
- [ ] Push notifications
- [ ] User testing

## Technical Considerations

### 1. Offline Support
- Cache evaluation criteria locally
- Queue evaluations for sync
- Conflict resolution

### 2. Mobile Optimization
- Touch-friendly UI
- Reduced data usage
- Quick actions

### 3. Integration Points
- Link personal progress to team evaluations
- Share insights with coaches
- Export data for analysis

### 4. Privacy Controls
- Data sharing preferences
- Anonymous comparisons
- Parental controls for minors

## Troubleshooting

### Common Issues

#### Pre-Evaluation Showing Incorrect Percentage

**Symptom**: Pre-evaluation card shows incorrect percentage (e.g., 29%) when it should show 0%

**Causes**:
1. **Missing teamProgress prop**: Component not receiving progress data from parent
2. **Division by zero**: When total = 0, calculation results in NaN
3. **Data not loaded**: Async data fetch hasn't completed
4. **Cached values**: Old data being displayed

**Solution**:
1. Ensure `EventSummaryService.getEventSummary()` is called to fetch data
2. Pass `teamProgress` prop with `{ completed: 0, total: 0 }` for events without pre-evaluations
3. Handle NaN cases explicitly in percentage calculations
4. Add console logging to trace data flow

**Debug Code**:
```javascript
// In PlayerPerformSection.tsx
const eventSummary = await EventSummaryService.getEventSummary(eventId);
console.log('[Debug] Event Summary:', eventSummary);

const progress = {
  completed: eventSummary?.pre_eval_completed || 0,
  total: eventSummary?.pre_eval_total || 0
};
console.log('[Debug] Team Progress:', progress);

// In ShadowPreEvaluationCard.tsx
let completionPercentage = 0;
if (teamProgress && teamProgress.total > 0) {
  completionPercentage = Math.round((teamProgress.completed / teamProgress.total) * 100);
}
console.log('[Debug] Completion Percentage:', completionPercentage);
```

#### Pre-Evaluations Not Created

**Symptom**: Event has participants but no pre-evaluations

**Cause**: Pre-evaluations must be explicitly created by coach action

**Solution**: Coach needs to send pre-evaluation requests from event details page

## Success Metrics

1. **Adoption**
   - % of players using personal training
   - Sessions per week per player

2. **Engagement**
   - Completion rate of evaluations
   - Goal achievement rate

3. **Improvement**
   - Skill progression over time
   - Correlation with team performance

4. **Retention**
   - Continued usage over months
   - Feature satisfaction scores

---

*Document Version: 1.0*  
*Last Updated: January 2025*  
*Author: SHOT Development Team*
