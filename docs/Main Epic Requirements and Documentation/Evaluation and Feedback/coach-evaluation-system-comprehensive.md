# Coach Evaluation System - Comprehensive Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Architecture](#architecture)
3. [Data Flow](#data-flow)
4. [Components](#components)
5. [Database Structure](#database-structure)
6. [User Flows](#user-flows)
7. [Implementation Details](#implementation-details)
8. [Integration Points](#integration-points)
9. [Known Issues](#known-issues)
10. [Future Enhancements](#future-enhancements)

## System Overview

The SHOT Coach Evaluation System is a comprehensive framework for assessing player development across four key dimensions: Technical, Physical, Psychological, and Social (TPPS). It uses the SHOT-2025 framework, a 36-week progressive evaluation program designed to track and improve player performance systematically.

### Key Features
- **Multi-dimensional Assessment**: Evaluates players across TPPS categories
- **Progressive Framework**: 36-week structured program with weekly progression
- **Position-Specific**: Tailored questions based on player positions
- **Three-Phase Evaluation**: Pre-session confidence, coach observation, post-session reflection
- **Real-time Tracking**: Live progress monitoring during events
- **Historical Analysis**: Tracks improvement over time

### Evaluation Types
1. **Pre-Session Self-Assessment**: Players rate their confidence (1-5) before training
2. **Coach Observation**: Coaches evaluate performance during the session
3. **Post-Session Reflection**: Players self-reflect on their actual performance

## Architecture

### High-Level System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Frontend (React/Vite)                     │
├─────────────────────────────────────────────────────────────────┤
│  Coach Interface          │  Player Interface    │  Admin Tools  │
│  - Event Management       │  - Self-Assessment   │  - Framework  │
│  - Player Evaluation      │  - View Feedback     │  - Analytics  │
│  - Progress Tracking      │  - History View      │  - Reports    │
└─────────────────────────┬────────────────────┬─────────────────┘
                          │                    │
                          ▼                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Service Layer (TypeScript)                    │
├─────────────────────────────────────────────────────────────────┤
│  EventService       │  EvaluationService  │  NotificationService│
│  - Create Events    │  - CRUD Operations  │  - SMS Integration  │
│  - Manage Participants│ - Progress Calc   │  - Email Alerts     │
└─────────────────────┬────────────────────┬─────────────────────┘
                      │                    │
                      ▼                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Database (Supabase/PostgreSQL)                │
├─────────────────────────────────────────────────────────────────┤
│  Core Tables:           │  Views:           │  Functions:       │
│  - events               │  - event_summary  │  - RPC calls      │
│  - pre_evaluations      │  - player_stats   │  - Triggers       │
│  - player_evaluations   │  - progress_view  │  - Aggregations   │
└─────────────────────────────────────────────────────────────────┘
```

### Technology Stack
- **Frontend**: React with Vite, Tailwind CSS, Shadow DOM components
- **Backend**: Supabase (PostgreSQL) with Row Level Security
- **Authentication**: Supabase Auth
- **Icons**: Lucide React
- **State Management**: React Context API
- **Routing**: React Router

## Data Flow

### Complete Evaluation Flow

```mermaid
sequenceDiagram
    participant Coach
    participant System
    participant Database
    participant Player
    participant SMS/Email

    Coach->>System: Create Training Event
    System->>Database: Insert event record
    Database-->>System: Event created
    
    Coach->>System: Add Participants
    System->>Database: Insert event_participants
    Database-->>System: Participants added
    Database->>Database: Trigger updates count
    
    Coach->>System: Request Pre-Evaluations
    System->>Database: Call create_basic_pre_evaluations()
    Database->>Database: Generate evaluation questions
    Database-->>System: Pre-evaluations created
    
    System->>SMS/Email: Send evaluation links
    SMS/Email->>Player: Receive notification
    
    Player->>System: Open evaluation link
    System->>Database: Track link opening
    Player->>System: Complete pre-assessment
    System->>Database: Save confidence ratings
    
    Note over Coach,Player: Training Session Occurs
    
    Coach->>System: Open evaluation interface
    System->>Database: Fetch players & criteria
    Coach->>System: Rate each player
    System->>Database: Save coach ratings
    
    Player->>System: Complete post-reflection
    System->>Database: Save performance ratings
    
    System->>Database: Calculate completion %
    Database-->>System: Return statistics
    System->>Coach: Show progress dashboard
```

## Components

### Coach Components

#### 1. **CoachPlayerEvaluation.tsx**
Main evaluation interface for coaches
```typescript
interface Props {
  eventId?: string;
  teamId?: string;
}

// Features:
- Player grid selection
- 5-point rating scale for each category
- Focus area selection
- Notes and observations
- Progress tracking
- Navigation between players
```

#### 2. **EventManagement Components**
```
/src/pages/section/Coach/
├── events/
│   ├── EventDetails.tsx       // Event overview
│   ├── PlayerEvaluation.tsx   // Evaluation interface
│   └── EventParticipants.tsx  // Participant management
```

#### 3. **Progress Tracking Components**
- **ShadowPreEvaluationCard**: Shows pre-evaluation completion status
- **EventSummaryCard**: Displays overall event statistics
- **PlayerProgressChart**: Visualizes improvement over time

### Player Components

#### 1. **PreEvaluationForm**
Self-assessment interface
```typescript
// Key features:
- Question-by-question flow
- Confidence rating (1-5)
- Progress indicator
- Auto-save functionality
```

#### 2. **PostEvaluationReflection**
Post-session self-reflection
```typescript
// Key features:
- Performance rating (1-5)
- Comparison with pre-assessment
- Personal notes
- Improvement tracking
```

### Shared Components

#### 1. **Shadow DOM Components**
All components use the Shadow DOM design system:
```typescript
- ShadowButton
- ShadowCard
- ShadowInfoCard
- ShadowStatCard
- ShadowHeaderSubHeader
- ShadowNotificationModal
```

#### 2. **Rating Components**
```typescript
- RatingScale (1-5 interactive scale)
- SkillCategoryCard
- FocusAreaSelector
```

## Database Structure

### Core Tables

#### 1. **events**
```sql
events {
  id: UUID (PK)
  name: string
  start_datetime: timestamp
  team_id: UUID (FK)
  event_type: enum('training', 'match', 'assessment')
  is_pre_session_evaluation: boolean
  sport_framework: string (default: 'SHOT-2025')
  status: enum('draft', 'published', 'completed')
}
```

#### 2. **pre_evaluations**
```sql
pre_evaluations {
  id: UUID (PK)
  event_id: UUID (FK)
  player_id: UUID (FK)
  team_id: UUID (FK)
  status: enum('not_started', 'started', 'completed')
  requested_at: timestamp
  completed_at: timestamp
  completion_percentage: integer
}
```

#### 3. **player_evaluations**
```sql
player_evaluations {
  id: UUID (PK)
  player_id: UUID (FK)
  evaluator_id: UUID (FK)
  event_id: UUID (FK)
  category: string (TECHNICAL/PHYSICAL/PSYCHOLOGICAL/SOCIAL)
  area: string (specific skill area)
  rating: integer (1-5)
  player_rating: integer (1-5, self-assessment)
  notes: text
  evaluation_status: enum('draft', 'submitted', 'viewed')
  pre_evaluation_id: UUID (FK)
}
```

#### 4. **evaluation_criteria**
```sql
evaluation_criteria {
  id: UUID (PK)
  framework_version: string
  week_number: integer
  category: string
  area: string
  position: string
  question: text (coach question)
  question_pre: text (pre-session question)
  question_post: text (post-session question)
}
```

### Important Views

#### 1. **event_comprehensive_summary**
Provides aggregated statistics for events:
```sql
- event details
- participant counts
- pre-evaluation completion %
- post-evaluation progress
- average ratings
```

#### 2. **player_evaluation_history_view**
Historical evaluation data for trend analysis

### Database Triggers

1. **trigger_generate_player_evaluations**
   - Fires on pre_evaluation INSERT
   - Creates evaluation records for each criteria

2. **trigger_update_event_participant_count**
   - Updates participant count on events table
   - Maintains accurate statistics

## User Flows

### Coach Flow

```
1. Event Creation
   Coach Dashboard → Create Event → Set Details → Save
   
2. Participant Management
   Event Details → Add Participants → Select Players → Confirm
   
3. Pre-Evaluation Request
   Event Details → Send Evaluations → Choose Recipients → Send SMS/Email
   
4. Player Evaluation
   Event Details → Evaluate Players → Select Player → Rate Categories → Add Notes → Save
   
5. Progress Monitoring
   Event Dashboard → View Summary → Check Completion → Export Results
```

### Player Flow

```
1. Receive Notification
   SMS/Email → Click Link → Authenticate
   
2. Pre-Session Assessment
   Welcome Screen → Start Assessment → Rate Confidence → Submit
   
3. Post-Session Reflection
   Notification → Open Form → Rate Performance → Add Notes → Submit
   
4. View Feedback
   Player Dashboard → Evaluation History → View Details → Track Progress
```

## Implementation Details

### Service Layer

#### EventService
```typescript
class EventService {
  // Core methods
  static async createEvent(eventData: CreateEventInput): Promise<Event>
  static async addParticipants(eventId: string, participantIds: string[]): Promise<void>
  static async getEventSummary(eventId: string): Promise<EventSummary>
  static async updateEventStatus(eventId: string, status: EventStatus): Promise<void>
}
```

#### EvaluationService
```typescript
class EvaluationService {
  // Pre-evaluation methods
  static async createPreEvaluations(eventId: string, playerIds: string[]): Promise<void>
  static async getPreEvaluationStatus(eventId: string): Promise<PreEvalStatus>
  
  // Player evaluation methods
  static async saveCoachEvaluation(data: CoachEvaluationInput): Promise<void>
  static async savePlayerSelfAssessment(data: PlayerAssessmentInput): Promise<void>
  static async getEvaluationHistory(playerId: string): Promise<EvaluationHistory[]>
}
```

### State Management

#### UserContext
Manages user role and permissions:
```typescript
interface UserContextType {
  user: User | null;
  currentRole: UserRole;
  isLoading: boolean;
  switchRole: (role: UserRole) => void;
}
```

#### EvaluationContext (if implemented)
```typescript
interface EvaluationContextType {
  currentEvent: Event | null;
  evaluations: Map<string, PlayerEvaluation>;
  saveEvaluation: (playerId: string, data: EvaluationData) => Promise<void>;
  syncStatus: 'idle' | 'syncing' | 'synced' | 'error';
}
```

### Routing Structure

```
/events/:eventId
  ├── /details              // Event overview
  ├── /participants         // Manage participants
  ├── /evaluate            // Coach evaluation interface
  ├── /evaluate/:playerId  // Individual player evaluation
  └── /summary             // Event summary and analytics

/evaluation/pre/:id        // Player pre-assessment
/evaluation/post/:id       // Player post-reflection
/evaluation/history        // Player evaluation history
```

## Integration Points

### 1. SMS Integration
- **Service**: Twilio (via Supabase Edge Functions)
- **Triggers**: Pre-evaluation requests, reminders
- **Format**: Short links with authentication tokens

### 2. Authentication
- **Provider**: Supabase Auth
- **Methods**: Email/password, magic links
- **Session**: JWT tokens with role claims

### 3. Real-time Updates
- **Technology**: Supabase Realtime
- **Use cases**: Live progress tracking, concurrent evaluations

### 4. Export/Reporting
- **Formats**: CSV, PDF reports
- **Data**: Individual evaluations, team summaries, progress trends

## Known Issues

### 1. Pre-Evaluation Auto-Generation
**Issue**: Pre-evaluations not automatically created when event has `is_pre_session_evaluation = true`  
**Current Workaround**: Coach must manually request evaluations  
**Fix**: Implement trigger on event_participants INSERT

### 2. UI Completion Percentage
**Issue**: Shows incorrect percentage (29%) for new events  
**Root Cause**: Frontend caching or calculation error  
**Fix**: Ensure fresh data fetch and proper null handling

### 3. Missing Email Integration
**Issue**: Profiles table lacks email field  
**Impact**: Cannot send email notifications  
**Workaround**: SMS-only notifications

### 4. Performance with Large Teams
**Issue**: Evaluation interface slows with 30+ players  
**Cause**: Loading all evaluations at once  
**Fix**: Implement pagination or virtual scrolling

## Future Enhancements

### 1. AI-Powered Insights
- Pattern recognition in evaluation data
- Suggested focus areas based on trends
- Predictive performance modeling

### 2. Video Integration
- Link evaluation moments to video clips
- Visual skill demonstrations
- Progress compilation videos

### 3. Parent Portal
- View child's progress
- Receive evaluation summaries
- Communication with coaches

### 4. Advanced Analytics
- Team-wide skill heatmaps
- Position-specific benchmarking
- Comparative analysis with age groups

### 5. Mobile App
- Native evaluation interface
- Offline support with sync
- Push notifications

### 6. Integration Enhancements
- Calendar sync for training sessions
- Wearable device data integration
- Third-party coaching tool APIs

## Appendix

### Evaluation Categories Detail

#### TECHNICAL
- Ball Control
- Passing Accuracy
- Shooting Technique
- Dribbling Skills
- First Touch

#### PHYSICAL
- Speed
- Stamina
- Strength
- Agility
- Balance

#### PSYCHOLOGICAL
- Decision Making
- Awareness
- Confidence
- Focus
- Resilience

#### SOCIAL
- Communication
- Teamwork
- Leadership
- Respect
- Sportsmanship

### Position-Specific Considerations
- **Goalkeeper**: Additional focus on positioning, reflexes, distribution
- **Defender**: Emphasis on marking, tackling, aerial ability
- **Midfielder**: Balance across all categories, creativity
- **Forward**: Finishing, movement, creating space

---

*Last Updated: January 2025*  
*Version: 1.0*  
*Next Review: Q2 2025*