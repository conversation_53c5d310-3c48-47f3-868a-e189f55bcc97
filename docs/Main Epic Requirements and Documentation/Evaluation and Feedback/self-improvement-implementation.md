# Self-Improvement Evaluation - Implementation Guide

## Quick Start Implementation

This guide provides a practical approach to implementing self-improvement evaluations for individual training sessions.

## MVP Features (Phase 1)

### 1. Database Schema (Minimal)

```sql
-- Simplified personal sessions table
CREATE TABLE personal_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    player_id UUID REFERENCES auth.users(id) NOT NULL,
    session_name VARCHAR(255) NOT NULL,
    session_type VARCHAR(50) DEFAULT 'solo', -- 'solo', 'friend', 'parent'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    total_duration_minutes INTEGER
);

-- Link sessions to existing evaluation system
CREATE TABLE personal_session_evaluations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES personal_sessions(id) ON DELETE CASCADE,
    evaluation_criteria_id UUID REFERENCES evaluation_criteria(id),
    pre_rating INTEGER CHECK (pre_rating BETWEEN 1 AND 5),
    post_rating INTEGER CHECK (post_rating BETWEEN 1 AND 5),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_personal_sessions_player ON personal_sessions(player_id);
CREATE INDEX idx_personal_evaluations_session ON personal_session_evaluations(session_id);
```

### 2. RLS Policies

```sql
-- Enable RLS
ALTER TABLE personal_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE personal_session_evaluations ENABLE ROW LEVEL SECURITY;

-- Policies
CREATE POLICY "Users manage own sessions" ON personal_sessions
    FOR ALL USING (auth.uid() = player_id);

CREATE POLICY "Users manage own evaluations" ON personal_session_evaluations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM personal_sessions ps
            WHERE ps.id = personal_session_evaluations.session_id
            AND ps.player_id = auth.uid()
        )
    );
```

### 3. React Component Structure

```typescript
// types/personal-training.ts
export interface PersonalSession {
  id: string;
  player_id: string;
  session_name: string;
  session_type: 'solo' | 'friend' | 'parent';
  created_at: string;
  completed_at?: string;
  total_duration_minutes?: number;
}

export interface PersonalEvaluation {
  id: string;
  session_id: string;
  evaluation_criteria_id: string;
  pre_rating?: number;
  post_rating?: number;
  notes?: string;
  // Joined data
  criteria?: {
    category: string;
    area: string;
    question_pre: string;
    question_post: string;
  };
}
```

### 4. Core Components

#### PersonalTrainingButton.tsx
```tsx
import React from 'react';
import { useHistory } from 'react-router-dom';
import { Target } from 'lucide-react';

export const PersonalTrainingButton: React.FC = () => {
  const history = useHistory();
  
  const handleStartTraining = () => {
    history.push('/personal-training/new');
  };
  
  return (
    <button
      onClick={handleStartTraining}
      className="flex items-center gap-2 px-4 py-2 bg-shot-teal text-white rounded-lg"
    >
      <Target size={20} />
      <span>Personal Training</span>
    </button>
  );
};
```

#### PersonalSessionSetup.tsx
```tsx
import React, { useState } from 'react';
import { supabase } from '../../../supabaseClient';
import { useUserContext } from '../../../contexts/UserContext';

export const PersonalSessionSetup: React.FC = () => {
  const { user } = useUserContext();
  const [sessionName, setSessionName] = useState('');
  const [sessionType, setSessionType] = useState<'solo' | 'friend' | 'parent'>('solo');
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  
  // Quick skill selection based on recent evaluations
  const quickSkills = [
    { id: 'tech_ball', name: 'Ball Control', category: 'TECHNICAL' },
    { id: 'phys_balance', name: 'Balance', category: 'PHYSICAL' },
    { id: 'psyc_aware', name: 'Awareness', category: 'PSYCHOLOGICAL' },
    { id: 'soc_comm', name: 'Communication', category: 'SOCIAL' }
  ];
  
  const handleCreateSession = async () => {
    try {
      // Create session
      const { data: session, error } = await supabase
        .from('personal_sessions')
        .insert({
          player_id: user?.id,
          session_name: sessionName || `Training ${new Date().toLocaleDateString()}`,
          session_type: sessionType
        })
        .select()
        .single();
        
      if (error) throw error;
      
      // Get evaluation criteria for selected skills
      const { data: criteria } = await supabase
        .from('evaluation_criteria')
        .select('id, category, area, question_pre')
        .eq('framework_version', 'SHOT-2025')
        .eq('week_number', 1) // Start with week 1 questions
        .in('category', selectedSkills);
        
      // Navigate to pre-evaluation
      history.push(`/personal-training/${session.id}/pre-evaluate`, {
        criteria,
        session
      });
    } catch (error) {
      console.error('Error creating session:', error);
    }
  };
  
  return (
    <div className="max-w-md mx-auto p-4">
      <h2 className="text-2xl font-bold mb-4">Start Personal Training</h2>
      
      {/* Session Name */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">
          Session Name (optional)
        </label>
        <input
          type="text"
          value={sessionName}
          onChange={(e) => setSessionName(e.target.value)}
          placeholder="e.g., Morning Practice"
          className="w-full px-3 py-2 border rounded-lg"
        />
      </div>
      
      {/* Session Type */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">
          Training With
        </label>
        <div className="flex gap-2">
          {['solo', 'friend', 'parent'].map((type) => (
            <button
              key={type}
              onClick={() => setSessionType(type as any)}
              className={`px-4 py-2 rounded-lg ${
                sessionType === type
                  ? 'bg-shot-teal text-white'
                  : 'bg-gray-200'
              }`}
            >
              {type.charAt(0).toUpperCase() + type.slice(1)}
            </button>
          ))}
        </div>
      </div>
      
      {/* Quick Skill Selection */}
      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">
          What will you practice?
        </label>
        <div className="grid grid-cols-2 gap-2">
          {quickSkills.map((skill) => (
            <button
              key={skill.id}
              onClick={() => {
                setSelectedSkills(prev =>
                  prev.includes(skill.category)
                    ? prev.filter(s => s !== skill.category)
                    : [...prev, skill.category]
                );
              }}
              className={`p-3 rounded-lg border ${
                selectedSkills.includes(skill.category)
                  ? 'border-shot-teal bg-shot-teal/10'
                  : 'border-gray-300'
              }`}
            >
              {skill.name}
            </button>
          ))}
        </div>
      </div>
      
      <button
        onClick={handleCreateSession}
        disabled={selectedSkills.length === 0}
        className="w-full py-3 bg-shot-teal text-white rounded-lg disabled:opacity-50"
      >
        Start Training
      </button>
    </div>
  );
};
```

#### QuickEvaluationFlow.tsx
```tsx
import React, { useState } from 'react';

interface QuickEvaluationProps {
  criteria: any[];
  onComplete: (ratings: Record<string, number>) => void;
  type: 'pre' | 'post';
}

export const QuickEvaluation: React.FC<QuickEvaluationProps> = ({
  criteria,
  onComplete,
  type
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [ratings, setRatings] = useState<Record<string, number>>({});
  
  const currentQuestion = criteria[currentIndex];
  const question = type === 'pre' 
    ? currentQuestion.question_pre 
    : currentQuestion.question_post;
    
  const handleRating = (rating: number) => {
    setRatings({
      ...ratings,
      [currentQuestion.id]: rating
    });
    
    if (currentIndex < criteria.length - 1) {
      setCurrentIndex(currentIndex + 1);
    } else {
      onComplete(ratings);
    }
  };
  
  return (
    <div className="max-w-md mx-auto p-4">
      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-500 mb-2">
          <span>Question {currentIndex + 1} of {criteria.length}</span>
          <span>{currentQuestion.category}</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-shot-teal h-2 rounded-full transition-all"
            style={{ width: `${((currentIndex + 1) / criteria.length) * 100}%` }}
          />
        </div>
      </div>
      
      <h3 className="text-lg font-medium mb-6">{question}</h3>
      
      <div className="flex justify-between gap-2">
        {[1, 2, 3, 4, 5].map((rating) => (
          <button
            key={rating}
            onClick={() => handleRating(rating)}
            className="flex-1 py-4 rounded-lg bg-gray-100 hover:bg-shot-teal hover:text-white transition-colors"
          >
            {rating}
          </button>
        ))}
      </div>
      
      {type === 'pre' && (
        <div className="mt-4 text-sm text-gray-600">
          <div>1 = I really struggle with this</div>
          <div>5 = I'm really good at this</div>
        </div>
      )}
    </div>
  );
};
```

### 5. Routes

```tsx
// AppRoutes.tsx additions
<Route exact path="/personal-training" component={PersonalTrainingDashboard} />
<Route exact path="/personal-training/new" component={PersonalSessionSetup} />
<Route exact path="/personal-training/:sessionId/pre-evaluate" component={PreTrainingEvaluation} />
<Route exact path="/personal-training/:sessionId/active" component={ActiveTrainingSession} />
<Route exact path="/personal-training/:sessionId/post-evaluate" component={PostTrainingEvaluation} />
<Route exact path="/personal-training/:sessionId/summary" component={SessionSummary} />
```

### 6. Service Functions

```typescript
// services/PersonalTrainingService.ts
export class PersonalTrainingService {
  static async createSession(
    playerId: string,
    sessionName: string,
    sessionType: 'solo' | 'friend' | 'parent'
  ) {
    const { data, error } = await supabase
      .from('personal_sessions')
      .insert({
        player_id: playerId,
        session_name: sessionName,
        session_type: sessionType
      })
      .select()
      .single();
      
    if (error) throw error;
    return data;
  }
  
  static async savePreEvaluations(
    sessionId: string,
    ratings: Record<string, number>
  ) {
    const evaluations = Object.entries(ratings).map(([criteriaId, rating]) => ({
      session_id: sessionId,
      evaluation_criteria_id: criteriaId,
      pre_rating: rating
    }));
    
    const { error } = await supabase
      .from('personal_session_evaluations')
      .insert(evaluations);
      
    if (error) throw error;
  }
  
  static async savePostEvaluations(
    sessionId: string,
    ratings: Record<string, number>
  ) {
    const updates = Object.entries(ratings).map(([criteriaId, rating]) => ({
      session_id: sessionId,
      evaluation_criteria_id: criteriaId,
      post_rating: rating
    }));
    
    // Update existing records
    for (const update of updates) {
      await supabase
        .from('personal_session_evaluations')
        .update({ post_rating: update.post_rating })
        .match({
          session_id: update.session_id,
          evaluation_criteria_id: update.evaluation_criteria_id
        });
    }
  }
  
  static async completeSession(sessionId: string, duration: number) {
    const { error } = await supabase
      .from('personal_sessions')
      .update({
        completed_at: new Date().toISOString(),
        total_duration_minutes: duration
      })
      .eq('id', sessionId);
      
    if (error) throw error;
  }
}
```

## Next Steps

1. **Analytics Dashboard**
   - Progress charts
   - Skill improvement tracking
   - Training frequency

2. **Social Features**
   - Share progress with coach
   - Training buddy system
   - Challenges

3. **Content Library**
   - Drill videos
   - Training plans
   - Skill progressions

4. **Gamification**
   - Achievements
   - Streaks
   - Level progression

## Testing Checklist

- [ ] User can create personal session
- [ ] Pre-evaluation questions display correctly
- [ ] Timer works during training
- [ ] Post-evaluation saves properly
- [ ] Session summary shows improvement
- [ ] Data persists correctly
- [ ] RLS policies work as expected
- [ ] Mobile UI is responsive
