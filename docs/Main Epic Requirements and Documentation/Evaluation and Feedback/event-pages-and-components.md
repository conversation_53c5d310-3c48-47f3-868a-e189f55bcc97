# Event Pages and Component States Documentation

## Table of Contents
1. [Overview](#overview)
2. [Main Pages](#main-pages)
3. [Event Lifecycle States](#event-lifecycle-states)
4. [Page-by-Page Breakdown](#page-by-page-breakdown)
5. [Component Usage Matrix](#component-usage-matrix)
6. [State Transitions](#state-transitions)
7. [Role-Based Views](#role-based-views)

## Overview

The SHOT app's event system consists of multiple pages that guide coaches and players through the complete evaluation lifecycle. Each page serves a specific purpose and contains components designed for different states of an event.

### Key Pages in the Event Flow
1. **Event List** - View all events
2. **Event Details** - Central hub for event management
3. **Attendance Tracking** - Mark who attended
4. **Pre-Evaluation Management** - Send evaluation requests
5. **Player Evaluations** - Coach rates players
6. **Player Self-Assessment** - Players complete evaluations

## Main Pages

### Coach Pages
```
/coach/club/{clubId}/team/{teamId}/events              - Event list
/coach/club/{clubId}/team/{teamId}/event/{eventId}     - Event details
/coach/club/{clubId}/team/{teamId}/event/{eventId}/attendance     - Track attendance
/coach/club/{clubId}/team/{teamId}/event/{eventId}/invite        - Add participants
/coach/club/{clubId}/team/{teamId}/event/{eventId}/pre-evaluation-notifications - Send pre-eval requests
/coach/club/{clubId}/team/{teamId}/event/{eventId}/evaluations   - Evaluate players
```

### Player Pages
```
/perform                        - Player dashboard
/evaluation/pre/{id}           - Pre-session self-assessment
/evaluation/post/{id}          - Post-session reflection
/evaluation/history            - View past evaluations
```

## Event Lifecycle States

### 1. **Event Created**
- Status: `draft` or `published`
- No participants yet
- Pre-evaluations not requested

### 2. **Participants Added**
- Players invited to event
- Status: `invited`
- Can send pre-evaluation requests

### 3. **Pre-Evaluations Sent**
- SMS/Email notifications sent
- Players can complete assessments
- Track completion progress

### 4. **Event Day**
- Track attendance
- Mark who showed up
- Status changes to `attended`

### 5. **Post-Event**
- Coach evaluates players
- Players complete self-reflection
- View analytics and progress

## Page-by-Page Breakdown

### 1. Event Details Page (`EventDetails.tsx`)

**Purpose**: Central hub for managing an event

**Key Components**:
```typescript
// Header Section
- Event type icon (training/match/assessment)
- Event name with pre-assessment badge
- Date, time, location details

// Pre-Evaluation Section (if enabled)
- PlayerSelfEvaluationStatus
  - Shows completion percentage
  - "Send Evaluations" button
  - List of pending/completed players
  
- ShadowTeamPreEvaluationProgress
  - Team-wide completion progress
  - Visual progress bar
  - Player avatars showing status

// Participants Section
- Participant list with status badges
- Add players button
- Status history viewer

// Action Buttons Grid
- Track Attendance (purple)
- Send Pre-Evaluation Requests (teal) - if enabled
- Team button (outline)
- Delete button (red outline)

// Coach Evaluation Status (past events only)
- Progress percentage
- "View Player Evaluations" button
```

**State Indicators**:
```typescript
// Event States
eventDetails.status: 'draft' | 'published' | 'completed'
eventDetails.is_pre_session_evaluation: boolean

// Participant States
participant.invitation_status: 'invited' | 'confirmed' | 'declined' | 'attended'

// Pre-Evaluation States
preEvaluation.status: 'not_started' | 'started' | 'completed'
```

### 2. Player Dashboard (`PlayerPerformSection.tsx`)

**Purpose**: Player's view of upcoming evaluations and performance

**Key Components**:
```typescript
// Recently Completed Section
- DismissibleCard wrapper
  - ShadowTeamPreEvaluationProgress
  - Shows for 12 hours after completion

// Action Cards Grid
- ShadowPreEvaluationCard (if pending evaluation)
  - Event name and time
  - Team progress
  - XP reward indicator
  - Countdown timer
  - "Start Evaluation" button
  
- ShadowMatchCard
  - Next match details
  - Set availability option

- ShadowInfoCard
  - Training tips
  - Skill improvement suggestions
```

**State Management**:
```typescript
// Automatic refresh every 10 seconds
useEffect(() => {
  const interval = setInterval(() => {
    checkPendingPreEvaluations();
  }, 10000);
}, []);

// States tracked
pendingPreEvaluation: any | null
completedPreEvaluations: any[]
teamProgress: { completed: number; total: number }
currentStreak: number
```

### 3. Attendance Tracking Page (`EventAttendance.tsx`)

**Purpose**: Mark which players attended the event

**Key Components**:
```typescript
// Attendance Tracker
- PlayerAttendanceSelector
  - Toggle switches for each player
  - Bulk actions (Mark all present/absent)
  - Status indicators

// Summary Stats
- Total invited
- Confirmed count
- Actually attended
- No-shows

// Actions
- Save attendance
- Send reminders to absent players
```

### 4. Pre-Evaluation Notifications Page

**Purpose**: Send evaluation requests to players

**Key Components**:
```typescript
// Player Selection
- Checkbox list of all participants
- Select all/none options
- Filter by status

// Message Preview
- SMS template preview
- Customizable message
- Evaluation link preview

// Send Options
- Send immediately
- Schedule for later
- Reminder settings
```

### 5. Coach Player Evaluation Page (`CoachPlayerEvaluation.tsx`)

**Purpose**: Coach evaluates player performance

**Key Components**:
```typescript
// Player Grid
- Player cards with:
  - Name and number
  - Position
  - Completion indicator
  - Last evaluation score

// Evaluation Form
- Rating categories (TPPS + Positional)
  - 5-point scale buttons
  - Category descriptions
  
- Focus Areas selector
  - Skill chips to select
  - Quick add common areas
  
- Notes textarea
  - Observations
  - Recommendations

// Navigation
- Previous/Next player buttons
- Save & Continue
- Progress indicator
```

### 6. Player Self-Assessment Page

**Purpose**: Players rate their own performance

**Key Components**:
```typescript
// Pre-Session Assessment
- Welcome message
- Question-by-question flow
- 5-point confidence scale
- Progress bar
- Skip option

// Post-Session Reflection
- Performance rating
- Comparison with pre-assessment
- Improvement notes
- Submit button
```

## Component Usage Matrix

| Component | Event Details | Player Dashboard | Attendance | Evaluations |
|-----------|--------------|------------------|------------|-------------|
| **ShadowPreEvaluationCard** | ❌ | ✅ | ❌ | ❌ |
| **PlayerSelfEvaluationStatus** | ✅ | ❌ | ❌ | ❌ |
| **ShadowTeamPreEvaluationProgress** | ✅ | ✅ | ❌ | ❌ |
| **ParticipantList** | ✅ | ❌ | ✅ | ❌ |
| **RatingScale** | ❌ | ❌ | ❌ | ✅ |
| **FocusAreaSelector** | ❌ | ❌ | ❌ | ✅ |
| **ShadowButton** | ✅ | ✅ | ✅ | ✅ |
| **ShadowStatCard** | ✅ | ❌ | ✅ | ✅ |
| **EventStatusBadge** | ✅ | ❌ | ✅ | ❌ |

## State Transitions

### Event State Flow
```mermaid
stateDiagram-v2
    [*] --> Created: Create Event
    Created --> ParticipantsAdded: Add Players
    ParticipantsAdded --> PreEvalRequested: Send Pre-Evaluations
    PreEvalRequested --> PreEvalInProgress: Players Start
    PreEvalInProgress --> PreEvalCompleted: Players Submit
    PreEvalCompleted --> EventDay: Event Date Arrives
    EventDay --> AttendanceTracked: Mark Attendance
    AttendanceTracked --> CoachEvaluating: Coach Rates Players
    CoachEvaluating --> PostEvaluation: Players Reflect
    PostEvaluation --> Completed: All Done
    Completed --> [*]
```

### Component State Changes

#### ShadowPreEvaluationCard States
```typescript
// Initial State
{
  showCard: true,
  isLoading: false,
  timeRemaining: "2 days",
  teamProgress: { completed: 0, total: 10 }
}

// In Progress
{
  showCard: true,
  isLoading: false,
  timeRemaining: "5 hours",
  teamProgress: { completed: 6, total: 10 }
}

// Completed
{
  showCard: false, // Card hidden after completion
}
```

#### PlayerSelfEvaluationStatus States
```typescript
// No Pre-Evaluations
{
  showSection: false
}

// Pre-Evaluations Created
{
  showSection: true,
  totalPlayers: 15,
  sentCount: 0,
  completedCount: 0,
  showSendButton: true
}

// In Progress
{
  showSection: true,
  totalPlayers: 15,
  sentCount: 15,
  completedCount: 8,
  showSendButton: false,
  showResendButton: true
}
```

## Role-Based Views

### Coach View Features
1. **Full Event Management**
   - Create/edit/delete events
   - Manage participants
   - Send evaluation requests
   - Track attendance
   - Evaluate players

2. **Analytics Access**
   - Team progress tracking
   - Individual player trends
   - Completion statistics
   - Export capabilities

3. **Communication Tools**
   - SMS/Email notifications
   - Bulk messaging
   - Reminder scheduling

### Player View Features
1. **Limited Event Access**
   - View upcoming events
   - See team progress
   - Complete self-assessments

2. **Personal Dashboard**
   - Pending evaluations
   - Performance history
   - Improvement tracking
   - XP/Streak gamification

3. **Notifications**
   - Push notifications
   - In-app alerts
   - SMS reminders

### Parent View Features (Future)
1. **Child Monitoring**
   - View child's evaluations
   - See attendance records
   - Track improvement

2. **Communication**
   - Receive coach updates
   - Event notifications
   - Progress reports

## Best Practices

### Component Selection
1. **Use Shadow Components**: All UI should use the Shadow design system
2. **Role-Specific Components**: Use `RoleSpecificContent` wrapper
3. **Loading States**: Always show loading indicators
4. **Error Handling**: Graceful error messages with retry options

### State Management
1. **Real-time Updates**: Use intervals for pending states
2. **Optimistic Updates**: Update UI immediately, sync later
3. **Cache Management**: Store frequently accessed data
4. **Progress Persistence**: Save progress in case of interruption

### Navigation
1. **Breadcrumbs**: Clear path back to team/events
2. **Quick Actions**: Prominent buttons for common tasks
3. **Context Preservation**: Maintain filters/selections
4. **Deep Linking**: Support direct URLs to specific states

---

*Last Updated: January 2025*  
*Version: 1.0*