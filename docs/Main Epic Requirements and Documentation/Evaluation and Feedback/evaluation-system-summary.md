# SHOT Evaluation System - Summary

## Overview
The SHOT evaluation system provides comprehensive player assessment across Technical, Physical, Psychological, and Social dimensions using a 36-week progressive framework.

## Current System

### Core Components
1. **SHOT-2025 Framework**: 36-week program with 4 categories
2. **Three Question Types per Skill**:
   - Pre-session self-assessment (confidence)
   - Coach evaluation (observation)
   - Post-session self-reflection (performance)
3. **Position-Specific Questions**: Additional questions based on player position
4. **Automated Generation**: Triggers create evaluations when pre-evaluation is requested

### Key Tables
- `evaluation_framework_metadata`: Framework definitions
- `evaluation_criteria`: All questions and rating scales
- `pre_evaluations`: Evaluation requests and status
- `player_evaluations`: Actual ratings and responses

### Current Flow
1. Coach creates event → 2. Requests evaluations (SMS) → 3. Players self-assess → 4. Training → 5. Coach evaluates → 6. Players reflect

## Proposed: Self-Improvement Evaluations

### Purpose
Enable players to track progress from individual practice sessions outside team context.

### Use Cases
- Solo practice
- Training with friends
- Parent-child sessions
- Personal coach sessions

### New Components

#### Database
```sql
personal_sessions         -- Individual training sessions
personal_session_skills   -- Skills practiced
personal_evaluations     -- Simplified pre/post ratings
personal_progress_goals  -- Personal improvement targets
```

#### UI Flow
1. **Quick Start**: Select training type and skills
2. **Pre-Rating**: Rate confidence (1-5)
3. **Active Training**: Timer and skill tracking
4. **Post-Rating**: Rate performance (1-5)
5. **Summary**: View improvement and save notes

#### Key Features
- Reuses existing evaluation questions
- Simplified rating flow
- Progress visualization
- Optional coach sharing
- Offline support

### Integration
- Links to team evaluations
- Shared skill progression
- Coach visibility (opt-in)
- Unified progress dashboard

## Implementation Priority

### Phase 1 (MVP)
- Basic session creation
- Pre/post evaluations
- Simple progress tracking

### Phase 2
- Analytics dashboard
- Goal setting
- Achievement system

### Phase 3
- Drill library
- Social features
- Coach integration

## Benefits
1. **For Players**: Track personal improvement, stay motivated between team sessions
2. **For Coaches**: See player dedication, identify self-starters
3. **For Platform**: Increased engagement, more data points, retention

## Technical Considerations
- Mobile-first design
- Offline capability
- Privacy controls
- Performance optimization

---

*This extends the existing evaluation system to support individual player development while maintaining consistency with team-based assessments.*
