# Team Joining User Flows and Scenarios

## Overview
This document details all the user flows and scenarios for joining teams in the SHOT platform, including edge cases and special considerations.

## User Flows

### Flow 1: Brand New User with Invite Code

```mermaid
graph TD
    A[Player receives invite code from coach] -->|Via SMS/Email/In-person| B[Player visits SHOT website/app]
    B --> C{Has account?}
    C -->|No| D[Click Register]
    D --> E[Enter invite code during registration]
    E --> F[Complete registration form]
    F --> G[Submit registration]
    G --> H[Email verification sent]
    H --> I[Player verifies email]
    I --> J[System processes pending invite code]
    J --> K[Player automatically added to team]
    K --> L[Redirect to team dashboard]
```

**Key Points:**
- Invite code entered during registration is stored in `pending_profiles` table
- Upon email verification, trigger processes the code
- Player privilege automatically added
- Team membership created with 'active' status

**Technical Implementation:**
```typescript
// Registration with invite code
const registrationData = {
  email: '<EMAIL>',
  password: 'secure-password',
  fullName: 'John Player',
  inviteCode: 'SHOT-FB-A3B-C4D',
  registration_metadata: {
    registration_flow: 'invited',
    invite_code: 'SHOT-FB-A3B-C4D'
  }
};
```

### Flow 2: Existing User Joins Team

```mermaid
graph TD
    A[Existing player receives invite code] --> B[Player logs into SHOT app]
    B --> C[Navigate to Join Team section]
    C --> D[Enter invite code]
    D --> E[System validates code]
    E --> F{Code valid?}
    F -->|Yes| G[Check if already member]
    G --> H{Already member?}
    H -->|No| I[Add to team immediately]
    H -->|Yes| J[Show already member message]
    I --> K[Update invite code usage]
    K --> L[Redirect to team page]
    F -->|No| M[Show error message]
```

**Key Points:**
- Immediate team membership (no approval needed)
- Real-time validation feedback
- Usage tracking for analytics

### Flow 3: QR Code Team Joining

```mermaid
graph TD
    A[Coach displays QR code] --> B[Player scans with camera]
    B --> C[Opens URL in browser]
    C --> D{User logged in?}
    D -->|Yes| E[Auto-process invite code]
    D -->|No| F[Redirect to login/register]
    F --> G[User completes auth]
    G --> E
    E --> H[Join team]
    H --> I[Show success message]
```

**URL Format:**
```
https://app.shot.com/join?code=SHOT-FB-A3B-C4D
https://app.shot.com/join?code=SHOT-FB-A3B-C4D&team=team-uuid
```

### Flow 4: Bulk Team Joining (Future Feature)

```mermaid
graph TD
    A[Coach uploads player list CSV] --> B[System generates unique codes]
    B --> C[Send invites via email/SMS]
    C --> D[Players receive personalized links]
    D --> E[Each player follows individual flow]
    E --> F[Coach tracks join status]
```

## Special Scenarios

### Scenario 1: Minor Player (Under 16) Joining

**Current Implementation:**
- Age calculated from date_of_birth
- `is_minor` flag set in profile
- Parent approval may be required (future feature)

**Flow:**
1. Minor enters invite code
2. System detects age < 16
3. Currently: Same flow as adult (joins immediately)
4. Future: Parent approval request sent

### Scenario 2: Expired or Invalid Code

**User Experience:**
```
Player enters: "SHOT-FB-OLD-CODE"

System checks:
1. Code exists? ❌ → "Invalid invite code"
2. Code valid? ❌ → "This code is no longer active"
3. Code expired? ❌ → "This invite code has expired"
4. Usage limit? ❌ → "This code has reached its usage limit"
```

**Error Messages:**
```typescript
const errorMessages = {
  INVALID_CODE: "The invite code you entered is not valid. Please check and try again.",
  EXPIRED_CODE: "This invite code has expired. Please request a new code from your coach.",
  USAGE_LIMIT: "This invite code has reached its maximum number of uses.",
  ALREADY_MEMBER: "Great news! You're already a member of this team.",
  SYSTEM_ERROR: "Something went wrong. Please try again later."
};
```

### Scenario 3: Coach Managing Multiple Codes

**Use Cases:**
1. **Tryouts**: Time-limited code for tryout period
2. **Seasonal**: Different codes for different seasons
3. **Position-specific**: Codes for specific positions needed

**Management Interface:**
- View all codes with status
- See usage statistics
- Deactivate/reactivate codes
- Add notes for tracking

### Scenario 4: Re-joining After Leaving

**Current Behavior:**
- Player who left team can use new invite code
- Previous membership history maintained
- New membership record created

**Database Handling:**
```sql
-- Check for previous membership
SELECT * FROM team_members 
WHERE user_id = ? AND team_id = ? 
AND status = 'inactive';

-- Create new active membership
INSERT INTO team_members (user_id, team_id, status, joined_at)
VALUES (?, ?, 'active', NOW());
```

## Edge Cases and Handling

### Edge Case 1: Network Issues During Join
- Frontend stores attempt in local storage
- Retry mechanism on connection restore
- Idempotent operations prevent duplicates

### Edge Case 2: Multiple Simultaneous Joins
- Database constraints prevent duplicate memberships
- Unique constraint on (user_id, team_id, status='active')

### Edge Case 3: Code Shared Publicly
- Usage limits prevent abuse
- Coach can deactivate compromised codes
- Audit trail tracks all usage

### Edge Case 4: Wrong Team Joined by Mistake
- Currently: Coach must remove player
- Future: Grace period for self-removal
- Future: Confirmation step for critical teams

## Analytics and Tracking

### Metrics Tracked

```typescript
interface InviteCodeAnalytics {
  code: string;
  created_at: Date;
  total_views: number;      // How many times code was entered
  successful_joins: number; // Successful team joins
  failed_attempts: number;  // Failed validation attempts
  conversion_rate: number;  // Success rate percentage
  average_time_to_join: number; // From code creation to join
  referral_source: string; // How player got the code
}
```

### Coach Dashboard Insights
1. **Code Performance**
   - Most successful codes
   - Conversion rates by code
   - Time to join analysis

2. **Player Journey**
   - Where players drop off
   - Common error patterns
   - Success factors

3. **Team Growth**
   - Join rate over time
   - Source of new players
   - Retention after joining

## Best Practices

### For Coaches

1. **Code Creation**
   ```
   ✅ DO:
   - Use descriptive notes
   - Set appropriate expiry
   - Create event-specific codes
   
   ❌ DON'T:
   - Share codes on public social media
   - Create codes with unlimited uses
   - Forget to deactivate old codes
   ```

2. **Code Distribution**
   ```
   ✅ DO:
   - Share via secure channels
   - Include instructions
   - Follow up on usage
   
   ❌ DON'T:
   - Post codes publicly
   - Use same code for different purposes
   - Ignore failed join attempts
   ```

### For Players

1. **Joining Process**
   ```
   ✅ DO:
   - Enter code exactly as given
   - Complete profile before joining
   - Contact coach if issues
   
   ❌ DON'T:
   - Share codes without permission
   - Try to guess codes
   - Create duplicate accounts
   ```

## Future Enhancements

### Planned Features

1. **Smart Invites**
   - Position-specific codes
   - Skill-level requirements
   - Automatic team assignment

2. **Social Features**
   - Friend referral bonuses
   - Team achievement unlocks
   - Join with friends option

3. **Advanced Analytics**
   - Predictive join likelihood
   - Optimal code distribution times
   - Retention prediction

### Technical Roadmap

```
Q1 2025: Basic invite system ✅
Q2 2025: QR codes and bulk invites
Q3 2025: Parent approval flow
Q4 2025: Advanced analytics and AI recommendations
```

---

*Document Version: 1.0*  
*Last Updated: January 2025*  
*Author: SHOT Development Team*
