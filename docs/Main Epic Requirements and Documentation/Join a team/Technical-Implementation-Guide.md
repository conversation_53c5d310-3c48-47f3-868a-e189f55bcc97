# Team Joining Technical Implementation Guide

## System Architecture

### 1. Database Design

#### Core Tables Relationship
```
teams
  ├── team_members (players belonging to teams)
  ├── team_coaches (coaches managing teams)
  └── invite_codes (codes for joining teams)
      └── team_join_requests (audit trail)
```

#### Key Relationships
- **One Team** → Many Invite Codes
- **One Team** → Many Team Members
- **One User** → Many Team Memberships
- **One Invite Code** → Many Join Requests

### 2. Invite Code Generation Algorithm

```typescript
// Format: PREFIX-SPORT-XXX-YYY
// Example: SHOT-FB-A3B-C4D

function generateInviteCode(teamId: string, sportType?: string): string {
  const prefix = 'SHOT';
  const sport = sportType ? sportType.substring(0, 2).toUpperCase() : 'TM';
  const random1 = generateRandomString(3);
  const random2 = generateRandomString(3);
  
  return `${prefix}-${sport}-${random1}-${random2}`;
}

function generateRandomString(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
```

### 3. Complete Join Flow Sequence Diagram

```
Player                    Frontend                  Supabase              Database
  |                          |                          |                     |
  |-- Enter Invite Code -->  |                          |                     |
  |                          |-- Validate Code -------> |                     |
  |                          |                          |-- Check Code ----->  |
  |                          |                          |<-- Code Valid -----  |
  |                          |<-- Validation Result --- |                     |
  |                          |                          |                     |
  |                          |-- Process Join --------> |                     |
  |                          |                          |-- Check Member --->  |
  |                          |                          |<-- Not Member -----  |
  |                          |                          |                     |
  |                          |                          |-- Add to Team ---->  |
  |                          |                          |-- Update Code ---->  |
  |                          |                          |-- Create Request ->  |
  |                          |                          |                     |
  |<-- Success/Redirect ---  |<-- Join Complete ------ |<-- Confirmed ------  |
```

### 4. React Component Structure

```typescript
// Main components involved in team joining

src/
├── components/
│   ├── InviteStep.tsx              // Registration step for invite code
│   ├── ProfileCompletionGuard.tsx  // Ensures profile complete before team access
│   └── RegistrationContainerV2.tsx // Main registration flow
├── hooks/
│   └── useInviteProcessing.ts      // Hook for invite code processing
├── services/
│   ├── InviteCodeService.ts        // Core invite code logic
│   └── EnhancedInviteCodeService.ts // Extended features for coaches
└── pages/
    ├── Registration.tsx             // Registration page
    ├── InvitedRegistrationPage.tsx  // Invite-specific entry point
    └── section/
        └── Coach/
            └── supporting/
                └── ClubManagement/
                    └── TeamAddPlayer.tsx // Coach invite management
```

### 5. State Management

#### Local Storage Usage
```typescript
// Pending invite codes stored during registration
localStorage.setItem('pendingInviteCode', inviteCode);
localStorage.setItem('pendingTeamId', teamId);

// Retrieved after email verification
const pendingCode = localStorage.getItem('pendingInviteCode');
```

#### React State Flow
```typescript
interface InviteProcessingState {
  processing: boolean;
  result: InviteProcessingResult | null;
  error: string | null;
}

interface InviteProcessingResult {
  success: boolean;
  message: string;
  team_id?: string;
  error?: string;
}
```

### 6. Security Implementation

#### Row Level Security (RLS) Policies

```sql
-- Team Members: Users can view their own memberships
CREATE POLICY "Users can view own team memberships" ON team_members
  FOR SELECT USING (auth.uid() = user_id);

-- Coaches can manage team members
CREATE POLICY "Coaches can manage team members" ON team_members
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM team_coaches tc
      WHERE tc.team_id = team_members.team_id
      AND tc.user_id = auth.uid()
      AND tc.status = 'active'
    )
  );

-- Invite codes visible to team coaches only
CREATE POLICY "Coaches can view team invite codes" ON invite_codes
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM team_coaches tc
      WHERE tc.team_id = invite_codes.team_id
      AND tc.user_id = auth.uid()
      AND tc.status = 'active'
    )
  );
```

#### Validation Function
```sql
CREATE OR REPLACE FUNCTION validate_invite_code(
  p_code TEXT,
  p_user_id UUID
) RETURNS TABLE (
  valid BOOLEAN,
  reason TEXT,
  team_id UUID,
  team_name TEXT
) AS $$
BEGIN
  -- Check if code exists and is valid
  IF NOT EXISTS (
    SELECT 1 FROM invite_codes 
    WHERE code = p_code AND is_valid = true
  ) THEN
    RETURN QUERY SELECT FALSE, 'Invalid code', NULL::UUID, NULL::TEXT;
    RETURN;
  END IF;
  
  -- Check expiry
  IF EXISTS (
    SELECT 1 FROM invite_codes 
    WHERE code = p_code 
    AND expires_at IS NOT NULL 
    AND expires_at < NOW()
  ) THEN
    RETURN QUERY SELECT FALSE, 'Code expired', NULL::UUID, NULL::TEXT;
    RETURN;
  END IF;
  
  -- Check usage limit
  IF EXISTS (
    SELECT 1 FROM invite_codes 
    WHERE code = p_code 
    AND max_uses IS NOT NULL 
    AND use_count >= max_uses
  ) THEN
    RETURN QUERY SELECT FALSE, 'Usage limit reached', NULL::UUID, NULL::TEXT;
    RETURN;
  END IF;
  
  -- Return valid result with team info
  RETURN QUERY 
  SELECT 
    TRUE,
    'Valid',
    ic.team_id,
    t.team_name
  FROM invite_codes ic
  JOIN teams t ON t.team_id = ic.team_id
  WHERE ic.code = p_code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 7. API Integration Examples

#### Creating an Invite Code
```typescript
async function createInviteCode(teamId: string, options?: {
  maxUses?: number;
  expiryDays?: number;
  notes?: string;
}) {
  const { data, error } = await supabase.rpc('create_team_invite_code', {
    p_team_id: teamId,
    p_created_by: currentUserId,
    p_max_uses: options?.maxUses || 10,
    p_expires_days: options?.expiryDays || 30,
    p_notes: options?.notes || null
  });
  
  if (error) throw error;
  return data;
}
```

#### Processing Join Request
```typescript
async function joinTeamWithCode(inviteCode: string): Promise<JoinResult> {
  try {
    // Get current user
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.user) {
      throw new Error('User not authenticated');
    }
    
    // Process invite code
    const { data, error } = await supabase.rpc('process_team_join', {
      p_invite_code: inviteCode.toUpperCase(),
      p_user_id: session.user.id
    });
    
    if (error) {
      return { success: false, error: error.message };
    }
    
    return {
      success: true,
      teamId: data.team_id,
      teamName: data.team_name
    };
  } catch (error) {
    console.error('Join team error:', error);
    return { success: false, error: 'Failed to join team' };
  }
}
```

### 8. Error Handling Patterns

```typescript
// Comprehensive error handling in the service layer
export const processInviteCode = async (
  code: string, 
  userId: string
): Promise<ProcessResult> => {
  try {
    // Input validation
    if (!code || !userId) {
      return {
        success: false,
        error: 'INVALID_INPUT',
        message: 'Code and user ID are required'
      };
    }
    
    // Normalize code
    const normalizedCode = code.trim().toUpperCase();
    
    // Validate code format
    if (!isValidCodeFormat(normalizedCode)) {
      return {
        success: false,
        error: 'INVALID_FORMAT',
        message: 'Invalid code format'
      };
    }
    
    // Process the code
    const result = await supabase.rpc('validate_and_process_invite_code', {
      p_invite_code: normalizedCode,
      p_user_id: userId
    });
    
    // Handle specific error cases
    if (result.error) {
      switch (result.error.code) {
        case 'EXPIRED':
          return {
            success: false,
            error: 'CODE_EXPIRED',
            message: 'This invite code has expired'
          };
        case 'LIMIT_REACHED':
          return {
            success: false,
            error: 'USAGE_LIMIT',
            message: 'This code has reached its usage limit'
          };
        case 'ALREADY_MEMBER':
          return {
            success: false,
            error: 'DUPLICATE_MEMBER',
            message: 'You are already a member of this team'
          };
        default:
          throw result.error;
      }
    }
    
    return {
      success: true,
      teamId: result.data.team_id,
      teamName: result.data.team_name,
      message: 'Successfully joined team!'
    };
    
  } catch (error) {
    console.error('Process invite code error:', error);
    return {
      success: false,
      error: 'SYSTEM_ERROR',
      message: 'An unexpected error occurred. Please try again.'
    };
  }
};
```

### 9. Testing Strategies

#### Unit Tests
```typescript
describe('InviteCodeService', () => {
  describe('validateCodeFormat', () => {
    it('should accept valid code formats', () => {
      expect(validateCodeFormat('SHOT-FB-A3B-C4D')).toBe(true);
      expect(validateCodeFormat('USR-12-ABC-DEF')).toBe(true);
    });
    
    it('should reject invalid formats', () => {
      expect(validateCodeFormat('INVALID')).toBe(false);
      expect(validateCodeFormat('SHOT-TOOLONG-ABC-DEF')).toBe(false);
    });
  });
  
  describe('processInviteCode', () => {
    it('should handle expired codes gracefully', async () => {
      const result = await processInviteCode('EXPIRED-CODE', 'user-id');
      expect(result.success).toBe(false);
      expect(result.error).toBe('CODE_EXPIRED');
    });
  });
});
```

#### Integration Tests
```typescript
describe('Team Join Flow', () => {
  it('should complete full join flow for new user', async () => {
    // 1. Create invite code
    const code = await createTestInviteCode();
    
    // 2. Register new user with code
    const user = await registerUserWithCode(code);
    
    // 3. Verify team membership
    const membership = await getTeamMembership(user.id, testTeamId);
    expect(membership).toBeDefined();
    expect(membership.status).toBe('active');
    
    // 4. Verify code usage updated
    const updatedCode = await getInviteCode(code);
    expect(updatedCode.use_count).toBe(1);
  });
});
```

### 10. Performance Considerations

#### Database Indexes
```sql
-- Optimize invite code lookups
CREATE INDEX idx_invite_codes_code ON invite_codes(code) WHERE is_valid = true;

-- Optimize team member queries
CREATE INDEX idx_team_members_user_team ON team_members(user_id, team_id) 
  WHERE status = 'active';

-- Optimize join request queries
CREATE INDEX idx_team_join_requests_status ON team_join_requests(status, created_at) 
  WHERE status = 'pending';
```

#### Caching Strategy
```typescript
// Cache team data after successful join
const cacheTeamData = async (teamId: string, teamData: TeamData) => {
  const cacheKey = `team_${teamId}`;
  const cacheExpiry = 3600; // 1 hour
  
  await redis.setex(cacheKey, cacheExpiry, JSON.stringify(teamData));
};

// Check cache before database query
const getTeamData = async (teamId: string): Promise<TeamData> => {
  const cached = await redis.get(`team_${teamId}`);
  if (cached) {
    return JSON.parse(cached);
  }
  
  const data = await fetchTeamFromDatabase(teamId);
  await cacheTeamData(teamId, data);
  return data;
};
```

---

*Document Version: 1.0*  
*Last Updated: January 2025*  
*Author: SHOT Development Team*
