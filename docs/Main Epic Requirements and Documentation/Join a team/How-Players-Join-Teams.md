# How Players Join Teams in SHOT

## Overview
The SHOT platform supports multiple methods for players to join teams, with a primary focus on invite codes and planned future features for team discovery and open join requests. The system is designed to give coaches control over team membership while making it easy for players to join.

## Current Implementation

### 1. Invite Code System

The primary method for joining teams is through **invite codes** - unique alphanumeric codes that coaches generate and share with prospective players.

#### Database Schema

**Key Tables:**
- `invite_codes` - Stores all invite codes with their configuration
- `team_members` - Records of players who belong to teams
- `team_join_requests` - Tracks join requests for audit purposes
- `profiles` - User profile information
- `teams` - Team information

**Invite Codes Table Structure:**
```sql
invite_codes {
  id: uuid (primary key)
  code: text (unique, e.g., "ABC-123-XYZ")
  team_id: uuid (references teams)
  created_by: uuid (coach who created it)
  is_valid: boolean (active/inactive status)
  expires_at: timestamp (optional expiry date)
  max_uses: integer (usage limit)
  use_count: integer (current usage count)
  code_type: varchar (default: 'team_join')
  personal_code: boolean (for personal invites)
  sport_context: text
  analytics_data: jsonb (tracking data)
}
```

#### Invite Code Generation Flow

1. **Coach Creates Invite Code**
   - Coach navigates to Team > Add Players
   - Configures code settings:
     - Expiry duration (days)
     - Maximum uses
     - Optional notes
   - System generates unique code (e.g., "SHOT-FB-A3B-C4D")

2. **Code Sharing Methods**
   - Direct code sharing (text)
   - QR code generation
   - Shareable URL: `https://app.shot.com/join?code=XXX`

#### Player Join Flow

1. **Registration with Invite Code**
   - New users can register with an invite code
   - Code validated during registration
   - User automatically added to team upon email verification

2. **Existing User with Invite Code**
   - User enters code in app
   - Code validated
   - User added to team immediately

### 2. Key Functions and Services

#### Backend Functions (Supabase)

**`validate_and_process_invite_code`**
```sql
-- Validates invite code and returns team information
-- Updates usage count
-- Returns success/failure with reason
```

**`process_invited_user_team_join`**
```sql
-- Triggered after profile creation
-- Checks for invite code in registration metadata
-- Automatically adds user to team
-- Updates invite code usage
```

**`register_invited_user`**
```sql
-- Special registration function for invited users
-- Creates auth user with pre-verified email
-- Creates profile with team association
```

#### Frontend Services

**InviteCodeService.ts**
```typescript
// Main service for invite code processing
processInviteCodeAfterVerification(inviteCode, userId)
checkPendingInviteCode()
clearPendingInviteCode()
```

**EnhancedInviteCodeService.ts**
```typescript
// Extended functionality for coaches
createTeamInviteCode(teamId, userId, options)
getTeamInviteCodes(teamId)
toggleInviteCodeStatus(codeId, status)
```

### 3. User Experience Flows

#### Flow A: New User Registration with Invite Code

1. Player receives invite code from coach
2. Navigates to registration page
3. Enters invite code during registration
4. Completes registration form
5. Receives email verification
6. Upon verification, automatically joined to team
7. Redirected to team dashboard

#### Flow B: Existing User Joins Team

1. Player receives invite code
2. Opens app and navigates to "Join Team"
3. Enters invite code
4. System validates code
5. Player immediately added to team
6. Success message and redirect to team

#### Flow C: QR Code Scanning

1. Coach displays QR code (contains join URL)
2. Player scans with phone camera
3. Opens URL in browser/app
4. If not logged in, prompted to register/login
5. Invite code automatically processed
6. Player joined to team

### 4. Security and Validation

#### Code Validation Checks
- Code exists and matches format
- Code is marked as valid (`is_valid = true`)
- Code hasn't expired (`expires_at` check)
- Usage limit not exceeded (`use_count < max_uses`)
- User not already a team member

#### Row Level Security (RLS)
- Team members can view their own membership
- Coaches can manage team memberships
- Invite codes visible only to team coaches
- Join requests visible to coaches and requesting player

### 5. Special Considerations

#### Minor Players (Under 16)
- Registration requires parent/guardian approval
- Team joining may require additional parental consent
- System tracks `is_minor` status based on date of birth

#### Multiple Team Membership
- Players can belong to multiple teams
- Each team membership tracked separately
- Different roles possible per team (player, captain, etc.)

### 6. Current UI Components

#### Coach Side
- **TeamAddPlayer.tsx**: Main interface for managing invite codes
  - Generate new codes
  - View existing codes
  - Toggle code status
  - Display QR codes
  - Copy shareable links

#### Player Side
- **Registration.tsx**: Handles registration with invite codes
- **InvitedRegistrationPage.tsx**: Redirect handler for invite URLs
- **JoinTeam.tsx**: Placeholder for future team discovery

### 7. Planned Features (Not Yet Implemented)

#### Team Discovery
- Browse available teams by:
  - Sport type
  - Location
  - Age group
  - Skill level
- Request to join without invite code

#### Join Requests
- Players can request to join teams
- Coaches receive notifications
- Approval/rejection workflow
- Automatic acceptance rules

#### Social Features
- Friend invitations
- Team recommendations
- Player referrals

## Technical Implementation Details

### API Endpoints (via Supabase RPC)

```typescript
// Create invite code
supabase.rpc('create_team_invite_code', {
  p_team_id: teamId,
  p_created_by: userId,
  p_max_uses: 10,
  p_expires_days: 30
})

// Validate and use invite code
supabase.rpc('validate_and_process_invite_code', {
  p_invite_code: code,
  p_user_id: userId,
  p_user_email: email
})

// Get team invite codes (for coaches)
supabase
  .from('invite_codes')
  .select('*')
  .eq('team_id', teamId)
  .order('created_at', { ascending: false })
```

### State Management

The system uses React hooks and context for state management:
- `useInviteProcessing()` - Handles invite code processing
- Local storage for pending invites
- Session-based authentication state

### Error Handling

Common error scenarios and responses:
- Invalid code: "Invalid invite code"
- Expired code: "This invite code has expired"
- Usage limit: "This invite code has reached its usage limit"
- Already member: "You're already a member of this team"

## Best Practices for Coaches

1. **Code Management**
   - Set appropriate expiry dates
   - Use meaningful notes for tracking
   - Deactivate unused codes
   - Monitor usage statistics

2. **Security**
   - Don't share codes publicly
   - Use limited-use codes for tryouts
   - Deactivate codes after events

3. **Player Experience**
   - Share clear instructions with code
   - Provide QR codes for in-person events
   - Follow up with players who haven't joined

## Future Enhancements

1. **Bulk Invitations**
   - CSV import for mass invites
   - Email/SMS integration
   - Tracking and reminders

2. **Advanced Permissions**
   - Role-based invites (player, coach, manager)
   - Conditional acceptance (skill requirements)
   - Trial period memberships

3. **Analytics**
   - Conversion tracking
   - Source attribution
   - Player retention metrics

## Troubleshooting Common Issues

### Player Can't Join Team
1. Verify code is correct (case-sensitive)
2. Check code hasn't expired
3. Confirm usage limit not exceeded
4. Ensure player not already a member

### Code Not Working
1. Coach should verify code is active
2. Check expiry date
3. Review usage count
4. Generate new code if needed

### Registration Issues
1. Ensure valid email address
2. Check spam folder for verification
3. Try alternative registration flow
4. Contact support if persistent

---

*Last Updated: January 2025*
*Version: 1.0*
