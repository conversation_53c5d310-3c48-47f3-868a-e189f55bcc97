# Role Debug Mode Documentation

## Overview
The SHOT app includes a sophisticated debug mode that helps developers and superadmins visualize role-specific components and behaviors. When enabled, it adds visual indicators to components showing which user role they're designed for.

## Key Features
- **Visual Role Indicators**: Colored borders and labels on components
- **Non-Intrusive**: Debug UI only appears when explicitly enabled
- **Role-Specific Colors**: Each role has a distinct color scheme
- **Easy Toggle**: Simple on/off switch in admin tools
- **Session-Based**: Settings persist during the session but don't affect other users

## How It Works

### 1. Storage Mechanism
Debug mode state is stored in `sessionStorage` under the key `'superadmin_session_flags'`:

```javascript
{
  "debug_mode": true,  // Main toggle for debug mode
  // other flags can be added here
}
```

### 2. Core Utilities (`/src/utils/debugMode.ts`)

#### Check if Debug Mode is Enabled
```typescript
export const isDebugMode = (): boolean => {
  try {
    const flags = sessionStorage.getItem('superadmin_session_flags');
    if (!flags) return false;
    const parsedFlags = JSON.parse(flags);
    return parsedFlags.debug_mode === true;
  } catch (e) {
    return false;
  }
};
```

#### Role-Specific Debug Styles
The `getDebugStyles()` function provides CSS styles for each role:

| Role | Color | Hex | Label |
|------|-------|-----|-------|
| Coach/Superadmin | Orange | #FF6F3C | COACH |
| Player | Blue | #296DFF | PLAYER |
| Parent | Green | #00BB00 | PARENT |
| Member/Admin | Purple | #9333EA | MEMBER/ADMIN |

### 3. Component Implementation

Components check for debug mode and apply visual indicators directly:

```tsx
// Example from CoachPerformSection.tsx
const CoachPerformSection: React.FC = () => {
  const debugEnabled = isDebugMode();
  
  return (
    <div 
      className={`relative ${debugEnabled ? 'border-2 border-orange-500 rounded-lg' : ''}`}
      style={debugEnabled ? { boxShadow: 'inset 0 0 20px rgba(255, 111, 60, 0.2)' } : {}}
    >
      {/* Component content */}
      
      {debugEnabled && (
        <div className="absolute top-2 right-2 text-xs text-orange-500 bg-black/85 px-2 py-1 rounded border border-orange-500/40 font-bold">
          COACH
        </div>
      )}
    </div>
  );
};
```

### 4. Visual Indicators

When debug mode is enabled, components display:
- **Colored Border**: 2px border in role-specific color
- **Inset Shadow**: Subtle glow effect matching the role color
- **Role Label**: Small badge in top-right corner showing the role name
- **Non-Layout-Affecting**: All debug elements use absolute positioning or shadows to avoid affecting layout

## Usage Instructions

### Enabling Debug Mode

1. **Via Admin Tools**:
   - Navigate to Admin Tools (must have superadmin privileges)
   - Find "Debug Mode" toggle
   - Switch it ON
   - Debug borders and labels become visible immediately

2. **Via Browser Console**:
   ```javascript
   // Enable debug mode
   sessionStorage.setItem('superadmin_session_flags', JSON.stringify({ debug_mode: true }));
   
   // Disable debug mode
   sessionStorage.setItem('superadmin_session_flags', JSON.stringify({ debug_mode: false }));
   ```

### Testing Different Roles

1. Enable debug mode
2. Use the sliding navigation to change your current role
3. Navigate to different pages to see role-specific components
4. Each component will show its intended audience with colored borders and labels

### Debug Mode Visual Guide

```
┌─────────────────────────────────────┐
│ ┌─────────────────────────┐ COACH  │  ← Role label
│ │                         │         │
│ │   Component Content     │         │  ← Orange border & glow
│ │                         │         │     for coach components
│ └─────────────────────────┘         │
└─────────────────────────────────────┘
```

## Best Practices

### For Developers

1. **Import the utility**: Always use `isDebugMode()` from `debugMode.ts`
   ```typescript
   import { isDebugMode } from '../../../utils/debugMode';
   ```

2. **Consistent implementation**: Follow the pattern of conditional classes and absolute-positioned labels

3. **Performance**: Debug checks are lightweight and don't impact performance

4. **Testing**: Always test components with debug mode both ON and OFF

### For Component Authors

When creating new role-specific components:

1. Import the debug utility
2. Check debug status at component initialization
3. Add conditional border/shadow styling
4. Include absolute-positioned role label
5. Use the appropriate role color from the table above

Example template:
```tsx
const MyRoleComponent: React.FC = () => {
  const debugEnabled = isDebugMode();
  const roleColor = '#FF6F3C'; // Use appropriate color
  const roleLabel = 'COACH';   // Use appropriate label
  
  return (
    <div 
      className={`relative ${debugEnabled ? 'border-2 border-[color] rounded-lg' : ''}`}
      style={debugEnabled ? { 
        borderColor: roleColor,
        boxShadow: `inset 0 0 20px ${roleColor}40` 
      } : {}}
    >
      {/* Your component content */}
      
      {debugEnabled && (
        <div 
          className="absolute top-2 right-2 text-xs bg-black/85 px-2 py-1 rounded font-bold"
          style={{ 
            color: roleColor,
            border: `1px solid ${roleColor}66`
          }}
        >
          {roleLabel}
        </div>
      )}
    </div>
  );
};
```

## Troubleshooting

### Debug Mode Not Showing
1. Check if you have superadmin privileges
2. Verify sessionStorage has the correct flag
3. Refresh the page after enabling
4. Check browser console for errors

### Visual Indicators Interfering with Layout
- All debug elements should use absolute positioning
- Borders use existing padding/margins
- If layout shifts occur, check component implementation

### Role Colors Not Matching
- Refer to the color table above
- Ensure you're using the exact hex values
- Check that opacity values match (0.2 for shadows, 0.4 for borders)

## Future Enhancements

Potential improvements to consider:
- Role override without changing actual privileges
- Debug panel with component hierarchy
- Performance metrics in debug mode
- Automated screenshot capture for different roles
- A/B testing framework integration

## Related Files
- `/src/utils/debugMode.ts` - Core debug utilities
- `/src/components/v2/*/` - Role-specific component implementations
- Admin Tools section - Debug mode toggle UI