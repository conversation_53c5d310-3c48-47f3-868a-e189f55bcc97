ARCHITECTURAL ASSUMPTIONS & INTEGRATIONS
PERFORM FRAMEWORK DATA STRATEGY
Option A: Static Configuration (Recommended for V1)
// Static JSON configs stored in app/database
const footballFramework = {
  sport: "football",
  evaluationQuestions: {
    physical: {
      pre: "How ready is your body for football training?",
      post: "How did you perform physically during training?",
      coach: "How did [player] perform physically?"
    },
    technical: {
      pre: "How confident are you with your football skills right now?", 
      post: "How did your technical skills perform?",
      coach: "Rate [player]'s technical execution"
    }
    // ... etc
  },
  drillLibrary: [
    {
      id: "fb_tech_001",
      name: "Cone Weaving - Ball Control",
      quadrant: "technical",
      difficulty: ["beginner", "intermediate", "advanced"],
      equipment: ["5 cones", "1 ball"],
      instructions: ["Set up 5 cones 2 meters apart", "Dribble through using inside of both feet"]
    }
    // ... etc
  ],
  monthlyFocus: {
    "january": { physical: "endurance", technical: "passing", tactical: "positioning", mental: "confidence" }
    // ... etc
  }
}

Option B: Google Apps Script Backend (If content needs frequent updates)
// Fetch from Google Sheets via Apps Script API
const getPerformFramework = async (sport) => {
  const response = await fetch('https://script.google.com/macros/s/[script-id]/exec?action=getFramework&sport=' + sport);
  return response.json();
}

Recommendation: Start with static configs in database. Much simpler for v1, easier to test, faster performance.
PULSE CONTENT FEED STRATEGY
Recommended: Google Apps Script + Simple CMS
// Pulse content fetched from Google Apps Script
const getPulseContent = async (filters) => {
  const response = await fetch('https://script.google.com/macros/s/[script-id]/exec', {
    method: 'POST',
    body: JSON.stringify({
      action: 'getPulseContent',
      filters: filters, // {type: 'my-teams', sport: 'football'}
      userId: currentUser.id
    })
  });
  return response.json();
}

// Google Apps Script can manage:
// - SHOT news and announcements
// - Community highlights  
// - Achievement celebrations
// - Drop announcements
// - Coach tips and insights

Benefits:
Non-technical team can manage content
Quick content updates without app deployment
Built-in caching via Google's infrastructure
Simple to implement filtering logic
LOCKER SHOPPING STRATEGY
Recommended: Shopify Integration with Native UX
// Native app UI that calls Shopify APIs
const getProducts = async () => {
  const response = await fetch('https://[your-shop].myshopify.com/api/2023-10/graphql.json', {
    method: 'POST',
    headers: {
      'X-Shopify-Storefront-Access-Token': '[token]',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      query: `
        query getProducts {
          products(first: 20) {
            edges {
              node {
                id title description
                images(first: 1) { edges { node { url } } }
                variants(first: 1) { edges { node { price } } }
              }
            }
          }
        }
      `
    })
  });
  return response.json();
}

Native Shopping Experience:
Shopify handles: inventory, payment, fulfillment, tax calculation
SHOT app handles: product browsing, cart UI, user experience
Seamless handoff to Shopify checkout when ready to pay
Order status syncs back to SHOT for "My Items" display
Benefits:
No custom e-commerce development
Professional payment processing
Inventory management handled
Order fulfillment systems ready
VAT/tax compliance built-in
DEVELOPMENT IMPACT SUMMARY
What This Architecture Means for Your Devs:
PERFORM Framework (Static Config Approach):
Pros:
Much faster development (no CMS to build)
Better performance (no external API calls)
Easier testing and debugging
Full control over data structure
Cons:
Framework updates require app deployment
Less flexibility for rapid content changes
Dev Timeline Impact: Saves 3-4 weeks vs building custom CMS
PULSE Feed (Google Apps Script):
Pros:
Content team can manage without dev involvement
No custom backend for content management
Built-in caching and performance
Quick content updates
Cons:
Slight dependency on Google's infrastructure
Learning curve for content team
Dev Timeline Impact: Saves 2-3 weeks vs building custom content management
LOCKER Shopping (Shopify Integration):
Pros:
No e-commerce development required
Professional payment/shipping/tax handling
No PCI compliance requirements
Inventory management included
Cons:
Monthly Shopify fees
Less control over checkout experience
Dependency on Shopify APIs
Dev Timeline Impact: Saves 6-8 weeks vs building custom e-commerce
TOTAL DEVELOPMENT TIME SAVED: 11-15 WEEKS
This architectural approach allows your team to focus on the core SHOT experience (user management, evaluations, team dynamics) rather than building generic systems that already exist.
FOUNDATIONAL PRINCIPLES
Data Ownership (Non-Negotiable)
Player ALWAYS owns their performance data
Coaches get temporary read/write access while player is on their team
Players can transfer teams and retain ALL historical data
Players can share dashboard with multiple coaches/adults
When player leaves team, coach loses ALL access immediately
Evaluation Color System
1-4 scores: Teal color (needs improvement)
5-8 scores: Purple color (developing)
9-10 scores: Gold color (excelling)
Evaluation Timing Windows
Pre-evaluation: 48 hours before event start
Post-evaluation: Player has 48 hours after event end
Coach feedback: Additional 48 hours after player post-evaluation to edit/comment/approve
EPIC 1: APP INSTALLATION & FIRST LAUNCH
User Story 1.1: App Discovery & Installation
As a potential SHOT user
I want to discover and install the SHOT app
So that I can begin my performance journey
Acceptance Criteria:
[ ] App Store Listing optimized with SHOT branding and clear value proposition
[ ] Google Play Store listing with same branding and messaging
[ ] Progressive Web App installable via browser at app.shotclubhouse.com
[ ] App icon clearly recognizable and follows platform guidelines
[ ] App permissions clearly explained during installation
[ ] Offline capability for core features explained in store listing
Test Scenarios:
Feature: App Installation

Scenario: Install from iOS App Store
  Given I search for "SHOT Clubhouse" in App Store
  When I find the official SHOT app
  Then I should see clear app description and screenshots
  And I should see SHOT branding and logo
  When I tap "Install"
  Then the app should download and install successfully
  And I should be able to launch it from home screen

Scenario: Install as PWA from browser
  Given I visit app.shotclubhouse.com on mobile browser
  When the page loads
  Then I should see an "Install App" prompt
  When I tap "Install"
  Then the app should install as a native-feeling app
  And I should be able to access it from home screen
  And it should work offline for core features

Scenario: App permissions on first launch
  Given I have installed the app
  When I launch it for the first time
  Then I should see permission requests for:
    - Notifications (for training reminders)
    - Camera (for uploading training videos)
    - Location (for finding local clubs)
  And each permission should explain why it's needed
  And I should be able to continue even if I deny permissions

EPIC 2: USER REGISTRATION & ONBOARDING
User Story 2.1: Initial Registration
As a new user
I want to create my SHOT account quickly
So that I can start using the platform
Acceptance Criteria:
[ ] Landing screen with compelling SHOT messaging and clear CTA
[ ] Registration form captures essential information only
[ ] Email verification required before app access
[ ] Input validation with helpful error messages
[ ] Password requirements clearly stated if using password auth
[ ] Social login options (Apple, Google) if available
[ ] Terms and privacy acceptance required
[ ] Age verification for compliance with data protection
Functional Specification:
Registration Form Fields:
Required Fields:
├── First Name (text, max 50 chars, letters only)
├── Last Name (text, max 50 chars, letters only)  
├── Email (email format validation, unique check)
├── Phone Number (international format, +44 validation for UK)
├── Nickname (text, max 30 chars, alphanumeric + spaces)
├── Date of Birth (date picker, age calculation)
└── Location (dropdown of major UK cities)

Optional Fields:
├── Referral Code (if user was referred)
└── Marketing Preferences (email/SMS consent)

Validation Rules:
├── Email must be unique in system
├── Phone must be valid UK mobile format
├── Age must be 13+ for account creation
├── All required fields must be completed
└── Terms & Privacy must be accepted

Test Scenarios:
Feature: User Registration

Scenario: Successful registration with valid data
  Given I am on the SHOT app landing screen
  When I tap "Get Started"
  And I complete the registration form with:
    | Field       | Value                    |
    | First Name  | Jamie                    |
    | Last Name   | Smith                    |
    | Email       | <EMAIL>  |
    | Phone       | +************            |
    | Nickname    | JSmith                   |
    | DOB         | 1995-03-15              |
    | Location    | Manchester               |
  And I accept terms and privacy policy
  And I tap "Create Account"
  Then I should see "Verification email <NAME_EMAIL>"
  And a verification email should be sent
  And my account should be created with status "unverified"

Scenario: Registration fails with invalid email
  Given I am completing registration
  When I enter email "invalid-email"
  And I try to continue
  Then I should see error "Please enter a valid email address"
  And the form should not submit
  And the email field should be highlighted in red

Scenario: Registration fails with under-13 age
  Given I am completing registration
  When I enter date of birth "2015-01-01" (age 8)
  And I try to submit
  Then I should see error "You must be at least 13 years old to create an account"
  And the form should not submit
  And I should see information about parental supervision options

Scenario: Registration fails with duplicate email
  Given a user already exists with email "<EMAIL>"
  When I try to register with the same email
  Then I should see error "An account with this email already exists"
  And I should see "Forgot password?" link
  And I should see "Try a different email" option

User Story 2.2: Email Verification
As a registered user
I want to verify my email address
So that I can access my SHOT account
Acceptance Criteria:
[ ] Verification email sent immediately after registration
[ ] Email template branded with SHOT design
[ ] Verification link expires after 24 hours
[ ] Resend option available if email not received
[ ] Clear instructions in email about next steps
[ ] Mobile-friendly email template
[ ] Successful verification redirects to app with confirmation
Test Scenarios:
Feature: Email Verification

Scenario: Successful email verification
  Given I have registered with email "<EMAIL>"
  When I receive the verification email
  And I click the verification link
  Then I should be redirected to the app
  And I should see "Email verified successfully!"
  And I should be logged into the app
  And my account status should be "verified"

Scenario: Resend verification email
  Given I registered but haven't received the verification email
  When I tap "Didn't receive email?"
  And I tap "Resend verification email"
  Then a new verification email should be sent
  And I should see "Verification email <NAME_EMAIL>"
  And the new email should contain a fresh verification link

Scenario: Expired verification link
  Given I received a verification email 25 hours ago
  When I click the expired verification link
  Then I should see "This verification link has expired"
  And I should see option to "Request new verification email"
  When I request a new email
  Then a fresh verification link should be sent

EPIC 3: SPORT HEAD CREATION & PROFILE SETUP
User Story 3.1: Sport Head Avatar Creation
As a verified user
I want to create my unique Sport Head avatar
So that I have a personalized identity in SHOT
Acceptance Criteria:
[ ] Avatar creation wizard with step-by-step customization
[ ] Diverse options for skin tone, hair, accessories
[ ] Sport selection influences available gear/accessories
[ ] Preview functionality to see avatar in different contexts
[ ] Save and continue option at each step
[ ] Avatar appears consistently throughout app
[ ] Edit options available later in profile settings
Functional Specification:
Avatar Customization Steps:
Step 1: Base Appearance
├── Skin Tone (8 diverse options)
├── Gender Presentation (Various options)
└── Face Shape (5 options)

Step 2: Hair & Head
├── Hairstyles (20+ options covering diverse textures/styles)
├── Hair Colors (Natural and fun colors)
├── Facial Hair (if applicable, 10+ styles)
└── Headwear (Caps, headbands, nothing)

Step 3: Sport Selection
├── Primary Sport (Football, Boxing, Basketball, Tennis, etc.)
├── Influences available gear and backgrounds
└── Can be changed later

Step 4: Accessories & Gear
├── Jerseys/Tops (Based on sport selection)
├── Accessories (Gloves, wristbands, chains)
├── Background (Stadium, gym, park, etc.)
└── Special unlockables (based on achievements)

Preview Features:
├── 360° avatar rotation
├── Different poses (action, standing, celebrating)
├── Context previews (in team, on field, in app)
└── Animation preview (basic movement)

Test Scenarios:
Feature: Sport Head Avatar Creation

Scenario: Complete avatar creation process
  Given I have verified my email and entered the app
  When I see "Create Your Sport Head" welcome screen
  And I tap "Let's Go"
  Then I should see Step 1: Base Appearance
  When I select skin tone and face shape
  And I tap "Next"
  Then I should see Step 2: Hair & Head options
  When I select hairstyle and color
  And I tap "Next"
  Then I should see Step 3: Sport Selection
  When I select "Football" as my primary sport
  And I tap "Next"
  Then I should see Step 4: Football-specific gear options
  When I customize my jersey and accessories
  And I tap "Create Avatar"
  Then I should see my completed Sport Head
  And I should see "Welcome to SHOT, [nickname]!"

Scenario: Preview avatar in different contexts
  Given I am customizing my Sport Head
  When I make changes to hairstyle
  Then I should see live preview updates
  When I tap "Preview in Action"
  Then I should see my avatar in different poses
  And I should see my avatar in team context
  And I should see my avatar on different backgrounds

Scenario: Save progress and continue later
  Given I am halfway through avatar creation
  When I tap "Save Progress"
  Then my current selections should be saved
  When I close the app and return later
  Then I should be able to continue from where I left off
  And all my previous selections should be preserved

User Story 3.2: Initial Profile Setup
As a user with a created Sport Head
I want to set up my initial profile preferences
So that I get relevant content and features
Acceptance Criteria:
[ ] Sport preferences beyond primary sport
[ ] Skill level self-assessment
[ ] Goals and motivation selection
[ ] Notification preferences setup
[ ] Privacy settings with age-appropriate defaults
[ ] Location-based club and content suggestions
[ ] Interests beyond sport (style, music, etc.)
Test Scenarios:
Feature: Profile Setup

Scenario: Complete initial profile setup
  Given I have created my Sport Head avatar
  When I proceed to profile setup
  Then I should see "Tell us more about yourself"
  When I select multiple sports I'm interested in
  And I choose my skill level as "Intermediate"
  And I select goals: "Improve fitness" and "Join a team"
  And I set notification preferences
  And I confirm privacy settings
  And I tap "Complete Setup"
  Then I should be taken to the main app
  And I should see personalized content recommendations
  And my profile should be 100% complete

EPIC 4: MAIN APP NAVIGATION & TABS
User Story 4.1: Bottom Tab Navigation
As a SHOT user
I want to navigate easily between app sections
So that I can access all features efficiently
Acceptance Criteria:
[ ] Five persistent navigation elements visible at all times: Clubhouse | Perform | AI Hub (central button) | Locker | Pulse
[ ] Active tab indicator clearly shows current section (excluding AI Hub)
[ ] Tab icons intuitive and consistent with SHOT branding
[ ] Central AI Hub button is distinct and easily accessible
[ ] Badge notifications on tabs when action required
[ ] Fast switching between tabs with no loading delays
[ ] Tab content remembers scroll position when switching
Test Scenarios:
Feature: App Navigation

Scenario: Navigate between all tabs
  Given I am logged into the SHOT app
  Then I should see 5 navigation elements at the bottom: Clubhouse, Perform, AI Hub, Locker, Pulse
  When I tap the Clubhouse tab
  Then I should see the Clubhouse content
  And the Clubhouse tab should be highlighted/active
  When I tap the AI Hub button
  Then the AI Hub full-screen interface should appear
  When I tap the Locker tab
  Then I should see the Locker content
  And the Locker tab should be highlighted/active

Scenario: Tab badges for notifications
  Given I have a pending evaluation
  When I view the bottom tabs
  Then I should see a red badge on the Perform tab
  When I complete the required action
  Then the badge should disappear

EPIC 5: DYNAMIC CLUBHOUSE CONTENT
User Story 5.1: Member Clubhouse Experience
As a basic member (no team affiliation)
I want to see engaging SHOT community content
So that I understand the platform value and feel motivated to engage
Acceptance Criteria:
[ ] Story Highlights section with interactive, color-coded stories
[ ] Hero section with rotating SHOT highlights and community wins
[ ] Latest drops preview with "Get early access" messaging
[ ] Success stories from community members (anonymized)
[ ] Impact tracker showing collective community contributions
[ ] Call-to-action encouraging path selection in Perform
[ ] No team-specific content visible
[ ] Fresh content updates regularly
[ ] Engagement prompts to drive interaction
Functional Specification:
Member Clubhouse Layout:
Header Section:
├── Welcome message: "What's good, [nickname]?"
├── Sport Head avatar (tap to access profile)
└── Notification bell (with badge count)

Story Highlights Row:
└── Interactive, horizontally scrollable story rings (See Epic 17)

Hero Carousel:
├── Rotating slides (5-7 slides, auto-advance every 5 seconds):
│   ├── Latest community achievements
│   ├── New drop announcements
│   ├── Success story highlights
│   ├── Impact milestones reached
│   └── Upcoming community events
└── Manual navigation dots

Quick Stats Panel:
├── "Community Impact This Month"
├── Hours of coaching funded: [dynamic number]
├── Equipment donated: [dynamic number]
└── Lives changed: [dynamic number]

Content Feed:
├── Recent community highlights
├── Drop previews with "Members get 24hr early access"
├── Achievement celebrations (anonymized)
├── Coach tips and insights
└── Local club spotlights

Call-to-Action Card:
├── "Ready to take your shot?"
├── "Choose your development path in Perform"
└── Prominent button: "Get Started"

Test Scenarios:
Feature: Member Clubhouse Content

Scenario: Member sees general community content
  Given I am logged in as a basic member with no team
  When I view the Clubhouse tab
  Then I should see "What's good, [my nickname]?"
  And I should see the Story Highlights row
  And I should see the hero carousel with community highlights
  And I should see community impact statistics
  And I should see recent achievement celebrations
  And I should see latest drop previews
  And I should NOT see any team-specific content
  And I should see "Ready to take your shot?" CTA

Scenario: Hero carousel functionality
  Given I am viewing the Clubhouse as a member
  When I see the hero carousel
  Then it should auto-advance every 5 seconds
  When I swipe or tap navigation dots
  Then I should be able to manually navigate slides
  When I tap on a carousel item
  Then I should be taken to relevant detail page

Scenario: Impact statistics are dynamic
  Given I am viewing community impact stats
  When community members complete impact actions
  Then the statistics should update regularly
  And I should see realistic, growing numbers
  And the stats should inspire participation

User Story 5.2: Athlete Clubhouse Experience
As an athlete (team member)
I want to see my team updates and personal progress
So that I stay connected with my team and motivated by achievements
Acceptance Criteria:
[ ] Team feed showing recent team activities and announcements
[ ] Coach messages directed at team or individual
[ ] Personal achievements and milestone celebrations
[ ] Teammate highlights and shout-outs
[ ] Upcoming events with quick RSVP functionality
[ ] Performance streak display (training consistency, improvements)
[ ] Team leaderboard with friendly competition metrics
[ ] Quick actions for common tasks (RSVP, evaluation reminder)
Functional Specification:
Athlete Clubhouse Layout:
Header Section:
├── Welcome: "Time to shine, [nickname]!"
├── Team badge and name
├── Performance streak indicator
└── Quick actions menu

Team Updates Feed:
├── Recent team announcements from coach
├── Match results and highlights
├── Training session summaries
├── Team achievement celebrations
└── Teammate milestone shout-outs

Personal Progress Card:
├── Latest evaluation scores with color coding
├── Recent improvements highlighted
├── Streak counter (consecutive training, evaluations)
├── Next milestone progress bar
└── Coach feedback summary

Upcoming Events:
├── Next training session with countdown
├── Upcoming matches with opposition
├── Team social events
├── RSVP status and quick toggle
└── Event details and location

Team Leaderboard:
├── Weekly/monthly performance metrics
├── Most improved players
├── Training attendance leaders
├── Positive attitude awards
└── "View full leaderboard" link

Test Scenarios:
Feature: Athlete Clubhouse Content

Scenario: Athlete sees team-specific content
  Given I am logged in as an athlete on "United FC Under 16s"
  When I view the Clubhouse tab
  Then I should see "Time to shine, [my nickname]!"
  And I should see my team badge "United FC Under 16s"
  And I should see recent team announcements
  And I should see my personal progress summary
  And I should see upcoming team events
  And I should see team leaderboard
  And I should NOT see general member content

Scenario: Quick RSVP for team events
  Given I see "Training: Wednesday 6pm" in upcoming events
  When I tap the RSVP toggle
  Then it should switch to "You're going ✓"
  And my attendance should be recorded
  And my coach should see my RSVP status
  When I tap it again
  Then it should switch to "Can't make it ✗"
  And my status should update accordingly

Scenario: Performance streak display
  Given I have attended 5 consecutive training sessions
  When I view my Clubhouse
  Then I should see "5-session streak! 🔥"
  And the streak should be prominently displayed
  When I complete another session
  Then the streak should update to "6-session streak!"
  And I should see encouragement to keep it going

User Story 5.3: Coach Clubhouse Experience
As a coach
I want to see my team overview and coaching resources
So that I can manage my team effectively and improve my coaching
Acceptance Criteria:
[ ] Team performance summary with key metrics and trends
[ ] Player highlights showing recent improvements and concerns
[ ] Coaching resources and tips from SHOT community
[ ] Event management quick links (create session, view RSVPs)
[ ] Parent communication summary and outstanding messages
[ ] Coach community posts and shared best practices
[ ] Administrative alerts (pending evaluations, approvals needed)
[ ] Team development progress tracking
Test Scenarios:
Feature: Coach Clubhouse Content

Scenario: Coach sees team management overview
  Given I am logged in as a coach for "United FC Under 16s"
  When I view the Clubhouse tab
  Then I should see "Morning Coach [name], squad's ready!"
  And I should see my team performance summary
  And I should see recent player achievements and concerns
  And I should see coaching resources relevant to football
  And I should see quick links to create events
  And I should see any pending parent communications
  And I should see administrative alerts if any

Scenario: Quick event creation from Clubhouse
  Given I am viewing my coach Clubhouse
  When I see "Create Training Session" quick action
  And I tap it
  Then I should see the event creation form
  And it should be pre-populated with my team details
  And I should be able to quickly create a session

User Story 5.4: Guardian Clubhouse Experience
As a parent/guardian
I want to see my child's progress and team information
So that I can support their development and stay informed
Acceptance Criteria:
[ ] Child progress summary with recent evaluations and improvements
[ ] Team communications from coaches and club administrators
[ ] Upcoming events requiring approval or attendance
[ ] Parent resources about youth sports development and safety
[ ] Child selector if guardian has multiple children
[ ] Safety information and reporting options
[ ] Communication thread with child's coach
[ ] Achievement celebrations and milestone recognition
Test Scenarios:
Feature: Guardian Clubhouse Content

Scenario: Guardian sees child-focused content
  Given I am logged in as a guardian linked to "Jamie Smith"
  When I view the Clubhouse tab
  Then I should see "Jamie's doing great! Here's the latest"
  And I should see Jamie's recent progress highlights
  And I should see communications from Jamie's coach
  And I should see upcoming events requiring my approval
  And I should see youth sports development resources
  And I should see safeguarding information clearly displayed

Scenario: Multi-child guardian experience
  Given I am a guardian linked to "Jamie Smith" and "Alex Smith"
  When I view my Clubhouse tab
  Then I should see a child selector at the top
  And it should default to the most recently active child
  When I tap on "Alex Smith"
  Then all content should update to show Alex's information
  And I should see Alex's team communications
  And I should see Alex's upcoming events

EPIC 6: PERFORM TAB - DYNAMIC CONTENT BY USER TYPE
User Story 6.1: Member Perform View (Path Selection)
As a basic member
I want to see clear development paths available
So that I can choose how to engage with SHOT's performance system
Acceptance Criteria:
[ ] Compelling hero message that changes based on time of day
[ ] Three distinct pathway cards with clear value propositions
[ ] Success metrics showing outcomes from each path
[ ] Testimonials from users who've succeeded in each path
[ ] No performance data displayed (since no path chosen)
[ ] Clear next steps for each pathway option
Functional Specification:
Path Selection Interface:
Hero Section:
├── Dynamic greeting based on time:
│   ├── Morning: "Morning [nickname], ready to take your shot?"
│   ├── Afternoon: "What's good [nickname], time to level up?"
│   └── Evening: "Evening [nickname], let's make moves!"
├── Motivational subtitle
└── SHOT performance logo

Path Cards (Grid Layout):
├── Just for Me Card:
│   ├── Icon: Individual training symbol
│   ├── Title: "Just for Me"
│   ├── Subtitle: "Solo training, instant improvement"
│   ├── Benefits:
│   │   ├── "✓ Start training immediately"
│   │   ├── "✓ AI-powered drill suggestions"
│   │   ├── "✓ Track progress over time"
│   │   └── "✓ No team required"
│   ├── Success metric: "Average 23% improvement in 4 weeks"
│   ├── CTA: "Start Now"
│   └── Color: Teal theme

├── Join a Club Card:
│   ├── Icon: Team/club shield symbol
│   ├── Title: "Join a Club"
│   ├── Subtitle: "Team training, coach guidance"
│   ├── Benefits:
│   │   ├── "✓ Professional coach feedback"
│   │   ├── "✓ Team performance tracking"
│   │   ├── "✓ Match day evaluations"
│   │   └── "✓ Compare with teammates"
│   ├── Success metric: "87% see improvement with coach feedback"
│   ├── CTA: "Enter Invite Code"
│   └── Color: Purple theme

└── Coach Card:
    ├── Icon: Whistle/coaching symbol
    ├── Title: "Coach"
    ├── Subtitle: "Lead a team, develop athletes"
    ├── Benefits:
    │   ├── "✓ Team roster management"
    │   ├── "✓ Player evaluation tools"
    │   ├── "✓ Performance analytics"
    │   └── "✓ Event scheduling"
    ├── Success metric: "Coaches report 34% better team performance"
    ├── CTA: "Get Verified"
    └── Color: Gold theme

Footer:
├── "Not sure which path fits you?"
├── "Take our 2-minute quiz" (optional feature)
└── "Contact support for guidance"

Test Scenarios:
Feature: Member Path Selection

Scenario: Member sees three clear pathways
  Given I am a basic member with no path selected
  When I navigate to the Perform tab
  Then I should see a dynamic greeting with my nickname
  And I should see 3 pathway cards: "Just for Me", "Join a Club", "Coach"
  And each card should show clear benefits and success metrics
  And each card should have a distinct color theme
  And I should NOT see any performance data or history
  And all CTA buttons should be clearly visible and tappable

Scenario: Dynamic greeting changes by time
  Given I am viewing the Perform tab at 8am
  Then I should see "Morning [nickname], ready to take your shot?"
  Given I am viewing the Perform tab at 2pm
  Then I should see "What's good [nickname], time to level up?"
  Given I am viewing the Perform tab at 8pm
  Then I should see "Evening [nickname], let's make moves!"

Scenario: Path selection triggers appropriate flow
  Given I am viewing the path selection screen
  When I tap "Start Now" on the Just for Me card
  Then I should be taken to the instant session creation flow
  When I tap "Enter Invite Code" on the Join a Club card
  Then I should see the invite code entry modal
  When I tap "Get Verified" on the Coach card
  Then I should see the coach verification form

User Story 6.2: Athlete Perform View (Training Dashboard)
As an athlete
I want to see my performance dashboard and training progress
So that I can track development and improve systematically
Acceptance Criteria:
[ ] Current evaluation status clearly displayed (pending/completed)
[ ] Performance trends across four quadrants with color coding (See Epic 19)
[ ] Next training session information and preparation tips
[ ] Improvement areas highlighted based on recent evaluations
[ ] Achievement progress toward badges and milestones
[ ] Coach feedback on recent performances clearly visible
[ ] Training history with searchable/filterable sessions
[ ] Quick actions for common tasks (start evaluation, view feedback)
Functional Specification:
Athlete Dashboard Layout:
Header Section:
├── Welcome: "Let's go [nickname]!"
├── Current streak: "[X] days training streak 🔥"
├── Overall progress indicator
└── Quick action buttons

Evaluation Status Card:
├── Urgent evaluations (red background):
│   ├── "Pre-evaluation required - Training: Wed 6pm"
│   ├── Countdown: "12 hours remaining"
│   └── "Complete Now" button
├── Pending evaluations (amber background):
│   ├── "Post-evaluation available - Match: Sat"
│   ├── Deadline: "36 hours remaining"
│   └── "Complete Evaluation" button
└── Recent completions (green background):
    ├── "Evaluation complete - Training: Mon"
    ├── "Awaiting coach feedback"
    └── "View Results" button

Performance Overview:
├── Four-Quadrant Chart (See Epic 19 for spec)
├── "View 12-week trends" link
└── Areas for improvement highlighted

Next Session Card:
├── "Coming up: Training - Wednesday 6pm"
├── Location: "Training Ground A"
├── Focus: "Passing accuracy and first touch"
├── Preparation tips from coach
├── "Mark attendance" toggle
└── "Get directions" link

Recent Coach Feedback:
├── Latest feedback from coach
├── Specific areas to work on
├── Positive highlights
├── "View all feedback" link
└── "Ask coach a question" (if available)

Training History:
├── Recent sessions (last 10)
├── Filter by: All/Training/Matches/Solo
├── Search functionality
├── Performance summary for each session
└── "View full history" link

Test Scenarios:
Feature: Athlete Performance Dashboard

Scenario: Athlete sees current evaluation status
  Given I am an athlete with a training session tomorrow
  When I view my Perform dashboard
  Then I should see "Pre-evaluation required - Training: Wed 6pm"
  And I should see countdown "23 hours remaining"
  And the card should have red/urgent styling
  When I tap "Complete Now"
  Then I should be taken to the pre-evaluation form

Scenario: Performance trends with color coding
  Given I have completed multiple evaluations
  When I view my performance overview
  Then I should see my latest scores for all four quadrants
  And scores 1-4 should be displayed in teal
  And scores 5-8 should be displayed in purple
  And scores 9-10 should be displayed in gold
  And I should see trend arrows (↗️ improving, ↘️ declining, → stable)

Scenario: Training history is searchable
  Given I have completed 20+ training sessions
  When I view my training history
  Then I should see the 10 most recent sessions
  When I tap "View full history"
  Then I should see all my sessions
  When I search for "passing"
  Then I should see only sessions that mention "passing"
  When I filter by "Matches"
  Then I should see only match evaluations

User Story 6.3: Coach Perform View (Team Management)
As a coach
I want to manage my team's performance and development
So that I can track progress and provide effective coaching
Acceptance Criteria:
[ ] Team roster with player status indicators and quick access
[ ] Pending evaluations requiring coach input clearly highlighted
[ ] Event management tools (create, edit, view RSVPs, manage attendance)
[ ] Player development tracking with individual and team trends
[ ] Communication tools to message team or individuals
[ ] Performance analytics showing team improvements and areas of concern
[ ] Resource library with drills and session plans
[ ] Administrative tasks (approvals, roster changes) prominent
Test Scenarios:
Feature: Coach Team Management

Scenario: Coach sees team roster with status indicators
  Given I am a coach for "United FC Under 16s" with 18 players
  When I view my Perform dashboard
  Then I should see my team roster organized by position
  And each player should show:
    - Name and age
    - Latest evaluation status
    - Performance indicator (improving ↗️, steady →, needs attention ⚠️)
    - Days since last evaluation
  And I should see total team statistics

Scenario: Manage pending evaluations
  Given 5 players have completed post-evaluations awaiting my feedback
  When I view pending evaluations section
  Then I should see "5 evaluations need your feedback"
  And I should see each player listed with:
    - Session details
    - Player's self-evaluation scores
    - Time remaining to add feedback
  When I tap on a player's evaluation
  Then I should see the full evaluation interface
  And I should be able to add my scores and comments

Scenario: Create team training session
  Given I want to create a new training session
  When I tap "Create Event"
  And I select "Training Session"
  And I fill in:
    | Field      | Value                |
    | Title      | Passing Practice     |
    | Date       | 2024-07-17          |
    | Start Time | 18:00               |
    | End Time   | 19:30               |
    | Location   | Training Ground A    |
  And I tap "Create Session"
  Then the event should be created
  And all team members should receive notifications
  And pre-evaluation windows should be activated
  And the event should appear in my event management list

User Story 6.4: Guardian Perform View (Child Oversight)
As a guardian
I want to monitor and support my child's training progress
So that I can be appropriately involved in their development
Acceptance Criteria:
[ ] Child's performance summary with privacy-appropriate level of detail
[ ] Evaluation oversight with ability to assist or approve evaluations
[ ] Event approvals for training sessions, matches, and other activities
[ ] Coach communication thread for development discussions
[ ] Progress milestones and achievement celebrations
[ ] Support resources for helping with training at home
[ ] Safety monitoring with ability to report concerns
[ ] Multi-child management if guardian has multiple children
Test Scenarios:
Feature: Guardian Child Oversight

Scenario: Guardian sees child's performance overview
  Given I am a guardian linked to "Jamie Smith" (age 15)
  When I view the Perform tab
  Then I should see "Jamie's Performance Dashboard"
  And I should see Jamie's recent evaluation summary
  And I should see upcoming events requiring my approval
  And I should see communication thread with Jamie's coach
  And I should see Jamie's achievement highlights
  And I should see options to support Jamie's training

Scenario: Approve child's event attendance
  Given Jamie has an upcoming training session
  When I see "Training: Wednesday 6pm - Approval Required"
  And I tap "Review Event"
  Then I should see full event details
  And I should see other attending players (if appropriate)
  When I tap "Approve Attendance"
  Then Jamie should be marked as attending
  And I should receive confirmation
  And Jamie's coach should see confirmed attendance

Scenario: Assist with child's evaluation
  Given Jamie has a pending pre-evaluation
  When I see "Help Jamie complete pre-evaluation"
  And I tap "Assist with Evaluation"
  Then I should see the evaluation form
  And I should see guidance on how to help appropriately
  When we complete the evaluation together
  Then it should be marked as "completed with guardian assistance"
  And the coach should see this information

EPIC 7: INSTANT SESSION WORKFLOW ("JUST FOR ME" PATH)
User Story 7.1: Sport & Context Selection
As a member choosing "Just for Me"
I want to quickly set up a personalized training session
So that I can start improving immediately
Acceptance Criteria:
[ ] Sport selection from available options in SHOT
[ ] Training context options (Solo, Pair, Group)
[ ] Location type selection (Home, Park, Gym, Field)
[ ] Session duration preference (15min, 30min, 45min, 60min)
[ ] Focus area selection from four quadrants
[ ] Instant session creation without delays
[ ] Drill recommendations based on selections (See Epic 24)
Functional Specification:
Instant Session Setup Interface:
Header:
├── "Let's create your training session"
├── Progress indicator (Step 1 of 4)
└── Back to path selection

Step 1: Sport Selection
├── Available sports grid:
│   ├── Football ⚽
│   ├── Basketball 🏀
│   ├── Boxing 🥊
│   ├── Tennis 🎾
│   ├── Rugby 🏉
│   └── [More sports...]
├── Search/filter sports
└── "Continue" button (enabled when sport selected)

Step 2: Training Context
├── Solo: "Just me training alone"
├── Pair: "Training with one other person"
├── Group: "Training with 2+ people"
└── Context affects drill recommendations

Step 3: Location & Duration
├── Location options:
│   ├── Home (Limited space, basic equipment)
│   ├── Park (Open space, minimal equipment)
│   ├── Gym (Indoor, equipment available)
│   └── Field (Full space, sport-specific setup)
├── Duration slider: 15-60 minutes
└── Equipment available checkbox

Step 4: Focus Area (Optional)
├── "What would you like to work on today?"
├── Physical: "Fitness, strength, speed"
├── Technical: "Skills, technique, ball control"
├── Tactical: "Game understanding, decision making"
├── Mental: "Confidence, focus, pressure handling"
├── "Surprise me" option for AI selection
└── "Create Session" button

Session Creation Confirmation:
├── "Your session is ready!"
├── Session summary display
├── Estimated improvement potential
├── "Start Pre-Evaluation" button
└── "Modify session" option

Test Scenarios:
Feature: Instant Session Creation

Scenario: Create football session with framework data
  Given I have selected "Just for Me" path
  When I select "Football" as my sport
  Then the system should load football framework configuration
  And I should see football-specific context options
  When I complete session setup
  Then drill recommendations should come from football drill library
  And evaluation questions should be football-specific
  And monthly focus should align with current month's football framework

Scenario: Drill recommendations adapt to skill level
  Given I am creating a football technical session
  And my previous technical evaluations average 3/10 (teal level)
  When I create the session
  Then I should see "Beginner" difficulty drills from the framework
  When my technical average improves to 8/10 (purple level)
  And I create another session
  Then I should see "Intermediate" difficulty drills

Scenario: Framework data loads efficiently  
  Given I am creating any instant session
  When I select my sport
  Then framework data should load within 1 second
  And all drill instructions should be complete
  And evaluation questions should be contextually relevant

User Story 7.2: Pre-Evaluation Process
As a user starting an instant session
I want to evaluate my current state honestly
So that I can track improvement accurately
Acceptance Criteria:
[ ] Four-quadrant evaluation with contextual questions
[ ] Slider interface for 1-10 scoring with visual feedback
[ ] Color coding (1-4 teal, 5-8 purple, 9-10 gold) immediate
[ ] Progress indicator showing completion status
[ ] Helpful guidance on how to rate honestly
[ ] Session-specific questions based on focus area
[ ] Save progress ability if user needs to pause
Functional Specification:
Pre-Evaluation Interface:
Header:
├── "How are you feeling right now?"
├── Session context: "30-min Football - Technical Focus"
├── Progress: "3/4 areas rated"
└── Save & continue later option

Evaluation Sliders:
├── Physical Quadrant:
│   ├── Label: "Physical (Energy, Fitness)"
│   ├── Session-specific question: "How ready is your body for football training?"
│   ├── Slider: 1-10 with live color coding
│   ├── Current value display: "7/10"
│   ├── Color background: Purple (for 7)
│   └── Helper text: "Be honest - this helps track improvement"

├── Technical Quadrant:
│   ├── Label: "Technical (Skills, Technique)"
│   ├── Session-specific question: "How confident are you with your football skills right now?"
│   ├── Slider: 1-10 with live color coding
│   ├── Current value display: "5/10"
│   ├── Color background: Purple (for 5)
│   └── Helper text: "Think about ball control, passing, shooting"

├── Tactical Quadrant:
│   ├── Label: "Tactical (Game Understanding)"
│   ├── Session-specific question: "How well do you understand football tactics today?"
│   ├── Slider: 1-10 with live color coding
│   ├── Current value display: "6/10"
│   ├── Color background: Purple (for 6)
│   └── Helper text: "Consider positioning, decision-making, game awareness"

└── Mental Quadrant:
    ├── Label: "Mental (Confidence, Focus)"
    ├── Session-specific question: "How mentally ready are you for training?"
    ├── Slider: 1-10 with live color coding
    ├── Current value display: "8/10"
    ├── Color background: Purple (for 8)
    └── Helper text: "Think about motivation, concentration, confidence"

Submit Section:
├── Completion indicator: "4/4 areas rated ✓"
├── Overall readiness: "Good to go! 💪"
├── "Start Training" button (enabled when all rated)
└── "I need to pause" option (saves progress)

Guidance Panel:
├── "Rating Guide"
├── 1-4: "Need significant work" (Teal)
├── 5-8: "Developing well" (Purple)
├── 9-10: "Excellent level" (Gold)
└── "Be honest - only you will see these scores"

Test Scenarios:
Feature: Pre-Evaluation Process

Scenario: Complete pre-evaluation with color coding
  Given I have created an instant session
  When I see the pre-evaluation screen
  Then I should see 4 evaluation sliders for Physical, Technical, Tactical, Mental
  When I set Physical slider to 3
  Then I should see teal color background for that quadrant
  When I set Technical slider to 7
  Then I should see purple color background for that quadrant
  When I set Tactical slider to 9
  Then I should see gold color background for that quadrant
  When I set Mental slider to 6
  Then I should see purple color background for that quadrant
  And I should see "4/4 areas rated ✓"
  And "Start Training" button should be enabled

Scenario: Session-specific evaluation questions
  Given I created a boxing session with mental focus
  When I view the pre-evaluation
  Then Physical should ask "How ready is your body for boxing training?"
  And Technical should ask "How confident are you with your boxing technique?"
  And Tactical should ask "How well do you understand boxing strategy?"
  And Mental should ask "How mentally tough are you feeling today?"

Scenario: Save progress and continue later
  Given I am halfway through pre-evaluation
  When I tap "I need to pause"
  Then my current ratings should be saved
  And I should return to the main app
  When I return to Perform tab later
  Then I should see "Continue paused evaluation"
  When I tap to continue
  Then my previous ratings should be restored
  And I should be able to complete remaining sliders

User Story 7.3: Training Session Execution
As a user who completed pre-evaluation
I want to follow a structured training session
So that I can improve systematically
Acceptance Criteria:
[ ] Drill sequence based on session parameters and evaluation (See Epic 24)
[ ] Step-by-step instructions with visual aids
[ ] Timer functionality for timed drills
[ ] Progress tracking through the session
[ ] Difficulty adaptation based on pre-evaluation scores
[ ] Video examples for complex drills (if available)
[ ] Session modification options if needed
[ ] Completion confirmation before post-evaluation
Functional Specification:
Training Session Interface:
Session Header:
├── Session title: "Football Technical Training"
├── Total duration: "30 minutes"
├── Current drill: "2 of 5"
├── Session timer: "12:34 elapsed"
└── Emergency stop button

Current Drill Card:
├── Drill name: "Cone Weaving - Ball Control"
├── Difficulty: "Intermediate" (based on pre-evaluation)
├── Duration: "5 minutes"
├── Equipment needed: "5 cones, 1 ball"
├── Instructions:
│   ├── "Set up 5 cones 2 meters apart"
│   ├── "Dribble through using inside of both feet"
│   ├── "Focus on close ball control"
│   └── "Complete 10 runs total"
├── Visual aid: Diagram or video thumbnail
├── Drill timer: "2:15 remaining"
└── "Mark as complete" button

Drill Navigation:
├── Previous drill (if applicable)
├── Skip drill option (with warning)
├── Modify difficulty (Easier/Harder)
└── Next drill preview

Session Progress:
├── Completed drills: ✓ ✓ 
├── Current drill: ⏱️
├── Remaining drills: ○ ○
├── Overall progress: 40%
└── Estimated completion: "17 minutes remaining"

Quick Actions:
├── "Pause session" (saves progress)
├── "Need help?" (instructions/tips)
├── "Report issue" (drill unclear/impossible)
└── "End session early" (with confirmation)

Session Completion:
├── "Excellent work! 💪"
├── All drills completed summary
├── Total session time: "28:45"
├── "How did that feel?" quick rating
├── "Start Post-Evaluation" button
└── "Review session details"

Test Scenarios:
Feature: Training Session Execution

Scenario: Follow complete drill sequence
  Given I have completed pre-evaluation with scores [7,5,8,6]
  When I tap "Start Training"
  Then I should see the first drill "Warm-up - Dynamic stretching"
  And I should see clear instructions and duration
  When I complete the drill and tap "Mark as complete"
  Then I should move to drill 2 of 5
  And the progress indicator should update
  When I complete all 5 drills
  Then I should see "Session complete! Excellent work!"
  And I should see total session time
  And I should see "Start Post-Evaluation" button

Scenario: Drill difficulty adapts to pre-evaluation
  Given my pre-evaluation Technical score was 3 (teal)
  When I view technical drills
  Then I should see "Beginner" difficulty drills
  And instructions should be more detailed
  Given my pre-evaluation Technical score was 9 (gold)
  When I view technical drills
  Then I should see "Advanced" difficulty drills
  And drills should be more challenging

Scenario: Pause and resume session
  Given I am in the middle of drill 3
  When I tap "Pause session"
  Then my progress should be saved
  And I should return to main app
  When I return to Perform tab
  Then I should see "Resume paused session"
  When I tap to resume
  Then I should continue from drill 3
  And session timer should continue from where I paused

User Story 7.4: Post-Evaluation & Results
As a user who completed training
I want to evaluate my performance and see improvement
So that I can track progress and stay motivated
Acceptance Criteria:
[ ] Post-evaluation using same four quadrants as pre-evaluation
[ ] Comparison display showing before/after scores
[ ] Improvement calculation and visual representation
[ ] Areas of progress highlighted in gold/green
[ ] Areas needing work highlighted appropriately
[ ] Achievement unlocks if milestones reached (See Epic 18)
[ ] Next session recommendations based on results
[ ] Session data saved to player's permanent record
Test Scenarios:
Feature: Post-Evaluation and Results

Scenario: Complete post-evaluation with improvement tracking
  Given I completed a technical-focused football session
  And my pre-evaluation scores were [Physical: 7, Technical: 5, Tactical: 8, Mental: 6]
  When I start post-evaluation
  Then I should see "How do you feel after training?"
  When I rate post-session as [Physical: 6, Technical: 7, Tactical: 8, Mental: 7]
  And I submit the evaluation
  Then I should see comparison:
    - Physical: 7→6 (slight decrease, normal after training)
    - Technical: 5→7 (+2 improvement, highlighted in gold)
    - Tactical: 8→8 (maintained, stable)
    - Mental: 6→7 (+1 improvement, highlighted in green)
  And I should see "Great job! You improved in 2 areas!"
  And I should see next session recommendations

Scenario: Achievement unlock after session
  Given this was my 5th consecutive solo session
  When I complete post-evaluation
  Then I should see "Achievement Unlocked: Consistency Champion!"
  And I should see badge added to my profile
  And I should earn appropriate SP points
  And achievement should appear in my timeline

Scenario: Session data permanently saved
  Given I complete a full instant session cycle
  When the session is finished
  Then the session should appear in my training history
  And it should show as "Instant Session - Football Technical"
  And it should display pre/post evaluation scores
  And it should show improvement metrics
  And it should be accessible from my Perform dashboard
  And coach should NOT have access to this data (solo session)

EPIC 8: CLUB JOINING WORKFLOW
User Story 8.1: Invite Code Entry & Validation
As a member wanting to join a team
I want to enter my club's invite code easily
So that I can connect with my team
Acceptance Criteria:
[ ] Clean invite code interface with clear instructions
[ ] Real-time validation as user types
[ ] Error handling for invalid/expired codes
[ ] Team information display when valid code entered
[ ] Code format assistance (auto-uppercase, remove spaces)
[ ] Manual entry and QR code scanning options
[ ] Multiple attempt handling with helpful messages
Test Scenarios:
Feature: Invite Code Entry

Scenario: Valid invite code shows team information
  Given a team exists with invite code "UNITED2024"
  When I tap "Enter Invite Code" on Join a Club card
  Then I should see invite code entry screen
  When I type "united2024"
  Then the code should auto-convert to "UNITED2024"
  When I finish typing
  Then I should see "✓ Valid code"
  And I should see team information: "United FC Under 16s"
  And I should see club information: "United Football Club"
  And I should see sport: "Football"
  And the "Continue" button should be enabled

Scenario: Invalid invite code shows helpful error
  Given no team exists with invite code "INVALID123"
  When I enter "INVALID123"
  Then I should see "✗ Invalid invite code"
  And I should see "Check with your coach for the correct code"
  And the "Continue" button should remain disabled
  When I enter a valid code after invalid attempt
  Then the error should clear immediately
  And valid team information should display

User Story 8.2: Role Selection & Age Verification
As a user with valid invite code
I want to specify my role and verify my age
So that I get appropriate access and protections
Acceptance Criteria:
[ ] Role selection with clear explanations (Player/Guardian)
[ ] Date of birth entry with age calculation
[ ] Age-appropriate flow routing (18+ vs under-18)
[ ] Guardian role verification (must be 18+)
[ ] Clear next steps based on age and role
[ ] Privacy information appropriate for age group
Test Scenarios:
Feature: Role Selection and Age Verification

Scenario: Adult player (18+) gets immediate access
  Given I have entered a valid invite code for "United FC Under 16s"
  When I see role selection options
  And I select "I'm a Player"
  And I enter date of birth "1995-06-15" (age 28)
  And I tap "Join Team"
  Then my member type should update to "athlete"
  And I should be immediately added to the team roster
  And I should be redirected to my athlete Perform dashboard
  And coach should see me in their team roster
  And I should see my first evaluation opportunity

Scenario: Under 18 player requires parent approval
  Given I have entered a valid invite code
  When I select "I'm a Player"
  And I enter date of birth "2008-06-15" (age 15)
  And I tap "Join Team"
  Then I should see "Parent Approval Required" screen
  And I should NOT gain immediate team access
  And my status should be "pending_approval"
  And a parent invite should be generated
  And I should see sharing options for parent invite

Scenario: Guardian role requires adult age
  Given I have entered a valid invite code
  When I select "I'm a Parent/Guardian"
  And I enter date of birth "2010-01-01" (age 13)
  And I tap "Continue"
  Then I should see error "Guardians must be 18 or older"
  And I should not be able to proceed
  When I change date of birth to "1985-01-01" (age 38)
  Then the error should clear
  And I should be able to continue to guardian setup

User Story 8.3: Under-18 Parent Approval Process
As an under-18 player
I want to easily get my parent's approval
So that I can join my team safely
Acceptance Criteria:
[ ] Parent invite generation with unique codes
[ ] Multiple sharing options (WhatsApp, email, SMS, copy link, QR code)
[ ] Clear messaging explaining the approval process
[ ] Status tracking while waiting for approval
[ ] Notification system when approval granted
[ ] Secure links that can't be spoofed or bypassed
Functional Specification:
Parent Approval Interface:
Header:
├── "Parent Approval Required"
├── Team info: "To join United FC Under 16s"
└── Security message: "This keeps you safe"

Explanation:
├── "You need a parent or guardian to approve your team access"
├── "This is required for all players under 18"
├── "Once approved, you'll get full team access"
└── "Your parent will also be able to support your progress"

Parent Invite Information:
├── "Share this with your parent/guardian:"
├── Unique invite code: "PAR-XYZ789ABC"
├── Generated link: "shot.app/parent-approve/PAR-XYZ789ABC"
└── QR code image

Sharing Options:
├── WhatsApp Button:
│   ├── Icon: WhatsApp logo
│   ├── Text: "Share via WhatsApp"
│   └── Action: Opens WhatsApp with pre-filled message
├── SMS Button:
│   ├── Icon: SMS symbol
│   ├── Text: "Send Text Message"
│   └── Action: Opens SMS with pre-filled message
├── Email Button:
│   ├── Icon: Email symbol
│   ├── Text: "Send Email"
│   └── Action: Opens email with pre-filled content
├── Copy Link Button:
│   ├── Icon: Link symbol
│   ├── Text: "Copy Link"
│   ├── Action: Copies URL to clipboard
│   └── Feedback: "Link copied!" toast
└── QR Code:
    ├── Scannable QR code image
    ├── Caption: "Let your parent scan this"
    └── "Save QR code to photos" option

Waiting Status:
├── "Waiting for approval..."
├── Status indicator: Pending ⏳
├── "We'll notify you when your parent approves"
├── "This usually takes a few minutes"
└── "Having trouble? Contact support"

Pre-filled Message Template:
"Hi! I'd like to join United FC Under 16s on SHOT for my football training.

Please approve my access by clicking this link:
shot.app/parent-approve/PAR-XYZ789ABC

This is safe and secure. You'll be able to support my training progress.

Thanks!
[Player Name]"

Test Scenarios:
Feature: Under-18 Parent Approval

Scenario: Generate parent approval request
  Given I am a 15-year-old player trying to join a team
  When age verification determines I need parent approval
  Then a unique parent invite code should be generated
  And I should see the parent approval screen
  And I should see 5 sharing options: WhatsApp, SMS, Email, Copy, QR
  And each option should be clearly labeled and working
  And I should see my pending status clearly

Scenario: WhatsApp sharing works correctly
  Given I am on the parent approval screen
  When I tap "Share via WhatsApp"
  Then WhatsApp should open with pre-filled message containing:
    - My name as the sender
    - Team name "United FC Under 16s"
    - Approval link with my unique code
    - Explanation of what SHOT is
    - Safety reassurance for parent
  And the message should be ready to send

Scenario: QR code generation and sharing
  Given I am on the parent approval screen
  When I see the QR code
  Then it should contain the approval URL with my unique code
  When I tap "Save QR code to photos"
  Then the QR code should be saved to my device photos
  When someone scans the QR code
  Then it should open the parent approval page with correct parameters

Scenario: Status tracking while waiting
  Given I have shared the parent approval request
  When I return to the app
  Then I should see my current status as "Pending parent approval"
  And I should see when the request was sent
  And I should see options to resend or contact support
  And I should NOT have access to team features yet

User Story 8.4: Guardian Registration & Child Approval
As a parent/guardian
I want to approve my child's team access safely
So that I can support their development while ensuring their safety
Acceptance Criteria:
[ ] Guardian registration via parent invite link
[ ] Child information verification and additional details
[ ] Safety and privacy information clearly presented
[ ] Consent mechanisms for data sharing and communication
[ ] Emergency contact and medical information collection
[ ] Approval action that immediately activates child's access
[ ] Guardian dashboard setup with child management tools
Test Scenarios:
Feature: Guardian Registration and Approval

Scenario: Guardian approves child's team access
  Given my child has sent me a team approval link
  When I click the parent approval link
  Then I should see "Your child wants to join United FC Under 16s"
  And I should see child's name and the team details
  When I tap "Create Guardian Account"
  And I complete registration with my details
  Then I should see child approval form
  When I complete required information:
    | Field                  | Value                    |
    | Emergency Contact Name | Sarah Smith             |
    | Emergency Contact Phone| +************           |
    | Relationship           | Mother                  |
    | Medical Conditions     | Mild asthma             |
    | Allergies              | None                    |
  And I check "I consent to coach communication about my child's progress"
  And I check "I consent to my child's performance data being tracked"
  And I tap "Approve Team Access"
  Then my child should gain immediate team access
  And I should see my guardian dashboard with child's information
  And coach should see child approved in team roster

Scenario: Guardian sets up multi-child management
  Given I have approved one child "Jamie"
  When I receive another approval request for child "Alex"
  And I follow the approval process for Alex
  Then I should see both children in my guardian dashboard
  And I should be able to switch between Jamie and Alex views
  And each child should show their respective team information
  And I should manage approvals for each child separately

EPIC 9: COACH VERIFICATION & TEAM MANAGEMENT
User Story 9.1: Coach Registration & Verification Submission
As a potential coach
I want to get verified to manage my team
So that I can use SHOT's coaching tools safely and effectively
Acceptance Criteria:
[ ] Verification form capturing essential coaching information
[ ] Club and team information with validation
[ ] Age verification ensuring coaches are 18+
[ ] Additional information field for qualifications/experience
[ ] Clear expectations about verification timeline
[ ] Status tracking for submitted applications
[ ] Email confirmation of submission
Test Scenarios:
Feature: Coach Verification Process

Scenario: Submit coach verification request
  Given I click "Get Verified" from the Coach pathway
  When I see the coach verification form
  And I complete all required fields:
    | Field       | Value                    |
    | Club Name   | United Football Club     |
    | Team Name   | Under 16s Boys          |
    | Sport       | Football                |
    | DOB         | 1985-03-20 (age 38)    |
    | Additional  | FA Level 2 qualified    |
  And I tap "Submit for Verification"
  Then I should see "Verification request submitted"
  And I should see "Expect response within 24-48 hours"
  And my status should be "pending_verification"
  And SHOT admin should receive notification of new request
  And I should receive email confirmation

Scenario: Under-18 cannot apply as head coach
  Given I am on the coach verification form
  When I enter date of birth "2008-01-01" (age 15)
  And I try to submit
  Then I should see error "Head coaches must be 18 or older"
  And the form should not submit
  And I should see information about assistant coach options

User Story 9.2: Admin Verification Process
As a SHOT admin
I want to review and approve coach applications
So that only legitimate coaches can manage teams
Acceptance Criteria:
[ ] Admin dashboard showing pending verifications
[ ] Application review interface with all submitted information
[ ] Club validation and creation if needed
[ ] Approval/rejection actions with reason codes
[ ] Coach notification system for decisions
[ ] Team creation workflow upon approval
[ ] Invite code generation for approved teams
Test Scenarios:
Feature: Admin Coach Verification

Scenario: Admin approves coach application
  Given a coach has submitted verification for "United FC Under 16s"
  When I view the admin verification dashboard
  Then I should see the pending application with all details
  When I click "Review Application"
  Then I should see full coach information and club details
  When I click "Approve"
  And I confirm club "United Football Club" exists or create it
  And I create team "Under 16s Boys" within the club
  Then the coach should receive approval notification
  And the coach should gain access to team management tools
  And 20 unique invite codes should be generated for the team
  And the team should appear in the system directory

Scenario: Admin rejects coach application
  Given I am reviewing a coach application
  When I click "Reject"
  And I select reason "Insufficient information provided"
  And I add note "Please provide coaching qualifications"
  And I confirm rejection
  Then the coach should receive rejection email with reason
  And they should be able to resubmit with additional information

User Story 9.3: Team Setup & Player Recruitment
As an approved coach
I want to set up my team and recruit players
So that I can start coaching with SHOT tools
Acceptance Criteria:
[ ] Team dashboard access upon approval
[ ] Invite code management with sharing tools
[ ] Team roster building as players join
[ ] Player status tracking (invited, joined, active)
[ ] Team customization options (logo, colors, description)
[ ] Bulk invite capabilities for quick team building
Test Scenarios:
Feature: Coach Team Setup

Scenario: Access team dashboard after approval
  Given my coach verification has been approved
  When I log into the app and go to Perform
  Then I should see my team dashboard for "United FC Under 16s"
  And I should see "Add Players" section
  And I should see 20 available invite codes
  And I should see team customization options
  And I should see event creation tools

Scenario: Share invite codes with multiple options
  Given I am viewing my team dashboard
  When I click "Share Invite Codes"
  Then I should see sharing options for each code
  When I click "Share via WhatsApp" for code "UNITED2024"
  Then WhatsApp should open with message:
    "Join United FC Under 16s on SHOT! 
    Use invite code: UNITED2024
    Download SHOT Clubhouse and enter this code to join our team."
  When I click "Copy Code"
  Then "UNITED2024" should be copied to clipboard
  And I should see "Code copied!" confirmation

Scenario: Track team building progress
  Given I have shared invite codes with potential players
  When players start using the codes to join
  Then I should see them appear in my team roster
  And each player should show:
    - Name and age
    - Join date and time
    - Status (joined, pending approval if under-18)
    - Invite code used
  And I should see total player count update in real-time

EPIC 10: EVALUATION SYSTEM & TIMING WINDOWS
User Story 10.1: Pre-Event Evaluation (48-Hour Window)
As an athlete with upcoming team event
I want to complete my pre-evaluation within the window
So that I can prepare mentally and help my coach plan
Acceptance Criteria:
[ ] Evaluation window opens exactly 48 hours before event start
[ ] Notification system alerts when window opens
[ ] Countdown display showing time remaining
[ ] Event context shown in evaluation (training vs match)
[ ] Window enforcement prevents early or late submissions
[ ] Visual indicators for completion status
[ ] Reminder notifications as deadline approaches
Functional Specification:
Pre-Evaluation Timing:
Event: Training Session - Wednesday 6:00 PM
├── Created: Monday 10:00 AM
├── Pre-evaluation window opens: Monday 6:00 PM (48 hours before)
├── Pre-evaluation deadline: Wednesday 6:00 PM (event start)
├── Event duration: Wednesday 6:00 PM - 7:30 PM
└── Post-evaluation window: Wednesday 7:30 PM - Friday 7:30 PM

Notification Schedule:
├── Window opens: "Your pre-evaluation for Wednesday training is now available"
├── 24 hours remaining: "Don't forget: Pre-evaluation due tomorrow at 6pm"
├── 4 hours remaining: "Complete your pre-evaluation - deadline 6pm today"
├── 1 hour remaining: "Last chance: Pre-evaluation due in 1 hour"
└── Window closed: "Pre-evaluation window has closed"

Status Indicators:
├── Not available: Gray circle ○ "Opens in X hours"
├── Available: Blue circle ◉ "Complete by [deadline]"
├── Completed: Green checkmark ✅ "Completed [timestamp]"
├── Overdue: Red warning ⚠️ "Missed deadline"
└── Closed: Gray X ✗ "Window closed"

Test Scenarios:
Feature: Pre-Event Evaluation Timing

Scenario: Pre-evaluation window opens correctly
  Given a coach creates training for "Wednesday 6:00 PM"
  And the event is created on "Monday 10:00 AM"
  When the time reaches "Monday 6:00 PM" (48 hours before)
  Then all team players should receive notification
  And players should see "Pre-evaluation available" in their dashboard
  And the evaluation should show countdown "48 hours remaining"
  When players access the evaluation
  Then they should see event context: "Training: Wednesday 6pm"
  And they should see the four-quadrant evaluation form

Scenario: Evaluation window enforcement
  Given a training session is scheduled for "Wednesday 6:00 PM"
  When I try to submit evaluation before "Monday 6:00 PM"
  Then I should see "Evaluation not yet available - opens in X hours"
  When I try to submit evaluation after "Wednesday 6:00 PM"
  Then I should see "Pre-evaluation window has closed"
  And I should NOT be able to submit
  When I submit within the valid window
  Then the evaluation should be accepted and saved

Scenario: Multiple events with overlapping windows
  Given I have training "Tuesday 6pm" and match "Thursday 7pm"
  When both pre-evaluation windows are open
  Then I should see both evaluations clearly separated
  And each should show the specific event details
  And I should be able to complete them independently
  And deadlines should be tracked separately

User Story 10.2: Post-Event Evaluation (48-Hour Window)
As an athlete after team event
I want to evaluate my performance while it's fresh
So that I can reflect honestly and improve
Acceptance Criteria:
[ ] Evaluation window opens immediately when event ends
[ ] 48-hour deadline for completion
[ ] Event reflection questions specific to activity
[ ] Performance comparison with pre-evaluation
[ ] Coach visibility of completed evaluations
[ ] Reminder system for approaching deadline
Test Scenarios:
Feature: Post-Event Evaluation

Scenario: Post-evaluation window opens after event
  Given I attended training "Wednesday 6:00-7:30 PM"
  When the event ends at "Wednesday 7:30 PM"
  Then I should immediately receive notification
  And I should see "Post-evaluation available" in dashboard
  And the deadline should show "Friday 7:30 PM" (48 hours later)
  When I access the post-evaluation
  Then I should see "How did you perform in training?"
  And I should see my pre-evaluation scores for comparison
  And questions should reflect the training focus

Scenario: Coach sees completed post-evaluations
  Given 5 players have completed their post-evaluations
  When I view my coach dashboard
  Then I should see "5 post-evaluations ready for review"
  And I should see each player's evaluation status
  When I click on a player's evaluation
  Then I should see their pre/post scores
  And I should be able to add my coach feedback
  And I should see 48-hour window to add my input

User Story 10.3: Coach Feedback Phase (Additional 48 Hours)
As a coach
I want to review and comment on player evaluations
So that I can provide valuable feedback and track development
Acceptance Criteria:
[ ] 48-hour window after player post-evaluation
[ ] Coach evaluation interface with player's scores visible
[ ] Coaching ratings for each quadrant
[ ] Written feedback requirements
[ ] Development recommendations for next session
[ ] Approval/sign-off mechanism
[ ] Player notification when coach feedback complete
Functional Specification:
Coach Feedback Interface:
Player Evaluation Review:
├── Player: "Jamie Smith - Training: Wednesday 6pm"
├── Event details: "Passing practice (Technical focus)"
├── Deadline: "Sunday 7:30 PM" (48 hours after player post-evaluation)
└── Status: "Awaiting your feedback"

Player's Self-Evaluation:
├── Pre-session scores: [Physical: 7, Technical: 5, Tactical: 8, Mental: 6]
├── Post-session scores: [Physical: 6, Technical: 7, Tactical: 8, Mental: 7]
├── Player notes: "Felt really good about my passing today"
└── Areas of focus: "Technical - passing accuracy"

Coach Evaluation Section:
├── Coach ratings (1-10 for each quadrant):
│   ├── Physical: [slider] "How did Jamie perform physically?"
│   ├── Technical: [slider] "Rate Jamie's technical execution"
│   ├── Tactical: [slider] "How was Jamie's tactical understanding?"
│   └── Mental: [slider] "Rate Jamie's mental approach"
├── Required written feedback:
│   ├── "What did Jamie do well?" [text area, required]
│   ├── "Areas for improvement?" [text area, required]
│   └── "Focus for next session?" [text area, required]
├── Overall session rating: [1-10 slider]
└── Development status: [Improving/Stable/Needs attention]

Actions:
├── "Save as draft" (can edit later)
├── "Submit feedback" (final, notifies player)
└── "Request parent meeting" (if concerns)

Completion Confirmation:
├── "Feedback submitted to Jamie ✓"
├── "Jamie will receive notification"
├── "Added to development timeline"
└── "Next evaluation: [upcoming event]"

Test Scenarios:
Feature: Coach Feedback Process

Scenario: Coach completes evaluation feedback
  Given Jamie completed post-evaluation for Wednesday training
  When I view pending evaluations in my coach dashboard
  Then I should see "Jamie Smith - awaiting feedback"
  And I should see deadline "48 hours remaining"
  When I click to review Jamie's evaluation
  Then I should see Jamie's pre/post scores
  And I should see Jamie's self-reflection comments
  When I add my coach ratings and written feedback
  And I submit the feedback
  Then Jamie should receive notification
  And the evaluation should be marked "complete"
  And it should appear in Jamie's development timeline

Scenario: Coach feedback deadline enforcement
  Given Jamie's post-evaluation was submitted Friday 7:30 PM
  When the deadline passes Sunday 7:30 PM without coach feedback
  Then the evaluation should auto-complete with player scores only
  And I should see "Feedback deadline missed" status
  And Jamie should see "Coach feedback not provided"
  And admin should be notified of missed coach feedback

Scenario: Coach can edit draft feedback before deadline
  Given I started feedback for Jamie but saved as draft
  When I return within the 48-hour window
  Then I should see "Draft feedback" status
  And I should be able to edit my previous inputs
  When I make changes and submit
  Then Jamie should receive the updated feedback
  And the timestamp should reflect final submission time

EPIC 11: PULSE TAB - FILTERING & FOLLOWING SYSTEM
User Story 11.1: Content Feed Filtering
As a SHOT user
I want to filter the Pulse feed by relevance
So that I see content that interests me most
Acceptance Criteria:
[ ] Filter tabs across top: All | My Teams | My Sport | Following
[ ] Content from Google Apps Script with smart filtering
[ ] Content organized by access levels and pillars (See Epic 20)
[ ] Local caching for instant filter application
[ ] Persistent preferences remembering user choices
[ ] Clear content indicators showing filter reasons
[ ] Refresh functionality for latest content from script
Technical Implementation:
// Pulse content fetched from Google Apps Script with user context
const loadPulseContent = async (filter = 'all') => {
  const userContext = {
    userId: currentUser.id,
    teams: currentUser.teams, // ["united-fc-u16s"]
    sports: currentUser.sports, // ["football"]
    following: currentUser.following, // ["coach-mike", "city-academy"],
    accessLevel: currentUser.accessLevel // PULSE, CIRCLE, etc.
  };

  const response = await fetch('https://script.google.com/macros/s/[script-id]/exec', {
    method: 'POST',
    body: JSON.stringify({
      action: 'getPulseContent',
      filter: filter,
      context: userContext
    })
  });
  
  const content = await response.json();
  
  // Cache locally for instant filtering
  localStorage.setItem(`pulse_${filter}`, JSON.stringify(content));
  return content;
}

Test Scenarios:
Feature: Pulse Feed with Google Apps Script Backend

Scenario: Filter content loads from script
  Given I am on the Pulse tab
  When I tap "My Teams" filter
  Then the app should call Google Apps Script with my team context
  And I should see content relevant to "United FC Under 16s"
  And content should be cached locally for instant reloading
  When I tap "My Sport" 
  Then I should see football-specific content from the script
  And content should include coaching tips, news, achievements

Scenario: Script provides contextual content
  Given I am a football player in Manchester
  When I view "All" content
  Then the script should return content prioritized by:
    - My team's recent activities
    - Local Manchester football news  
    - Football-specific coaching content
    - Achievement celebrations from similar players
  And content should feel relevant and engaging

Scenario: Content updates without app deployment
  Given SHOT admin wants to add breaking news
  When they update the Google Apps Script content
  Then users should see new content within 5 minutes
  And no app update should be required
  And content should maintain consistent formatting

User Story 11.2: Following System
As a SHOT user
I want to follow teams, coaches, and other users
So that I see their updates in my personalized feed
Acceptance Criteria:
[ ] Follow buttons on profiles, team pages, coach pages
[ ] Following count visible on user profile
[ ] Followers count on public profiles
[ ] Following feed showing posts from followed accounts
[ ] Unfollow option easily accessible
[ ] Suggested follows based on interests and connections
Test Scenarios:
Feature: Following System

Scenario: Follow a team and see their content
  Given I view "Manchester City Academy" team page
  When I tap the "Follow" button
  Then the button should change to "Following ✓"
  And Manchester City content should appear in my Following feed
  And my following count should increase by 1
  When I navigate to Pulse and select "Following"
  Then I should see posts from Manchester City Academy
  And posts should show "Following Manchester City Academy"

Scenario: Unfollow a team
  Given I am following "Manchester City Academy"
  When I visit their team page
  And I tap "Following ✓"
  Then I should see unfollow confirmation
  When I confirm unfollow
  Then the button should change back to "Follow"
  And their posts should no longer appear in my Following feed
  And my following count should decrease by 1

EPIC 12: LOCKER TAB - SHOPPING & INVENTORY
User Story 12.1: Browse & Purchase Items via Shopify Integration
As a SHOT user
I want to browse and purchase SHOT merchandise seamlessly
So that I can express my identity and support the community
Acceptance Criteria:
[ ] Product catalog pulled from Shopify with high-quality images
[ ] Native browsing experience before Shopify handoff (See Epic 22)
[ ] Native cart management with item count and review
[ ] Size, color, variant selection using Shopify product data
[ ] Real-time pricing including discounts from Shopify
[ ] Stock status synced with Shopify inventory
[ ] Seamless checkout handoff to Shopify
[ ] Order tracking synced back to SHOT app
Technical Implementation:
// Native SHOT UI calling Shopify Storefront API
const loadProducts = async () => {
  const query = `
    query getProducts($first: Int!) {
      products(first: $first, sortKey: CREATED_AT, reverse: true) {
        edges {
          node {
            id title description handle
            featuredImage { url }
            variants(first: 5) {
              edges {
                node {
                  id title price
                  availableForSale
                  selectedOptions { name value }
                }
              }
            }
            tags
          }
        }
      }
    }
  `;

  const response = await fetch(`https://shot-store.myshopify.com/api/2023-10/graphql.json`, {
    method: 'POST',
    headers: {
      'X-Shopify-Storefront-Access-Token': process.env.SHOPIFY_STOREFRONT_TOKEN,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ query, variables: { first: 20 } })
  });

  return response.json();
}

// Seamless checkout handoff
const proceedToCheckout = (cartItems) => {
  const checkoutUrl = `https://shot-store.myshopify.com/cart/${cartItems.map(item => 
    `${item.variantId}:${item.quantity}`
  ).join(',')}`;
  
  // Open Shopify checkout in webview or browser
  window.open(checkoutUrl, '_blank');
}

Test Scenarios:
Feature: Shopify-Integrated Shopping Experience

Scenario: Browse products with native feel
  Given I am on the Locker tab
  When the app loads products from Shopify
  Then I should see SHOT-branded product catalog
  And products should display with high-quality images
  And I should see real-time pricing from Shopify
  And the experience should feel native, not like a web store
  When I tap on "SHOT Performance Cap"
  Then I should see product details from Shopify
  And I should see available colors and sizes
  And stock status should be accurate

Scenario: Add to cart and checkout seamlessly
  Given I am viewing a product
  When I select "Black" color and "Large" size
  And I tap "Add to Cart"
  Then the item should be added to my local cart
  And I should see cart icon update with item count
  When I have multiple items and tap "Checkout"
  Then I should be handed off to Shopify checkout
  And the checkout should be pre-populated with my items
  And I should be able to complete purchase with Shopify's secure payment

Scenario: Order tracking integrates back to SHOT
  Given I completed a purchase through Shopify checkout
  When my order ships
  Then I should receive notification in SHOT app
  And I should see order status in "My Orders" section
  When items arrive and I confirm delivery
  Then purchased items should appear in "My Items" section
  And items should be available for avatar customization if applicable

Scenario: Age-appropriate purchase flow
  Given I am a 15-year-old user
  When I try to purchase an item
  Then I should see "Guardian approval required"
  And guardian should receive notification with item details
  When guardian approves the purchase
  Then I should be able to complete Shopify checkout
  And guardian should receive order confirmation

EPIC 13: ADMIN MANAGEMENT SYSTEM
User Story 13.1: Coach Verification Management
As a SHOT admin
I want to efficiently review coach applications
So that only legitimate coaches can manage teams
Acceptance Criteria:
[ ] Admin dashboard with pending verifications queue
[ ] Application details view with all submitted information
[ ] Club creation/validation workflow
[ ] Approval/rejection actions with audit trail
[ ] Bulk actions for processing multiple applications
[ ] Communication templates for applicant responses
Test Scenarios:
Feature: Admin Coach Verification

Scenario: Process coach verification efficiently
  Given 10 coach applications are pending
  When I view the admin verification dashboard
  Then I should see all applications with key details
  And I should see application date and waiting time
  When I click on an application
  Then I should see full coach details and club information
  When I approve the application
  Then the coach should be notified within 5 minutes
  And the team should be created with invite codes generated

Scenario: Handle club creation during approval
  Given a coach applies for "New United FC"
  When I review the application
  And I see the club doesn't exist in our system
  Then I should have option to "Create New Club"
  When I create the club with details
  Then the club should be added to our directory
  And the coach should be linked as the first team admin

User Story 13.2: Perform Framework Management
As a SHOT admin
I want to manage sport-specific performance frameworks
So that users get relevant training content
Acceptance Criteria:
[ ] Framework editor for each sport
[ ] Monthly focus assignment by quadrant
[ ] Drill library management (See Epic 24)
[ ] Evaluation questions customization
[ ] Framework preview before publishing
[ ] Version control for framework changes
Test Scenarios:
Feature: Perform Framework Management

Scenario: Update football framework for December
  Given I am managing the Football framework
  When I select "December" focus period
  And I set quadrant focuses:
    | Quadrant  | Focus Area           |
    | Physical  | Agility and Speed    |
    | Technical | Shooting Accuracy    |
    | Tactical  | Attacking Patterns   |
    | Mental    | Match Confidence     |
  And I save the framework
  Then all football users should see updated December content
  And instant sessions should use the new framework
  And existing evaluations should remain unchanged

User Story 13.3: User & Data Management
As a SHOT admin
I want to manage user accounts and data
So that I can handle edge cases and maintain platform integrity
Acceptance Criteria:
[ ] User search functionality across all accounts
[ ] Profile editing capabilities with change logging
[ ] Team transfers with data ownership protection
[ ] Account suspension/deletion tools
[ ] Data export for GDPR compliance
[ ] Impact Measurement Tools for community metrics (See Epic 21)
[ ] Audit logging for all admin actions
Test Scenarios:
Feature: Admin User Management

Scenario: Transfer player between teams
  Given Jamie is currently on "United FC Under 16s"
  And "City Academy U16s" wants to recruit Jamie
  When I process the team transfer
  Then Coach A (United FC) should lose access to Jamie's data
  And Coach B (City Academy) should gain access from transfer date
  And Jamie should retain all historical evaluation data
  And the transfer should be logged with timestamp and reason

Scenario: Handle guardian change for under-18 player
  Given Alex (age 15) has Guardian A managing their account
  When custody changes to Guardian B
  And I receive proper documentation
  Then Guardian A access should be revoked immediately
  And Guardian B should be approved and linked
  And Alex's data and team membership should remain intact
  And all changes should be logged for audit

EPIC 14: DATA OWNERSHIP & PRIVACY
User Story 14.1: Player Data Ownership Enforcement
As a player
I want to own my performance data permanently
So that I control who has access and can take it with me
Acceptance Criteria:
[ ] Database-level enforcement of player data ownership
[ ] Coach access control based on current team membership
[ ] Automatic access revocation when player leaves team
[ ] Data portability allowing players to share with new coaches
[ ] Audit trails for all data access and changes
[ ] Player dashboard sharing controls
Test Scenarios:
Feature: Data Ownership Enforcement

Scenario: Player retains data when leaving team
  Given Jamie has 6 months of evaluation data with United FC
  When Jamie leaves United FC to join City Academy
  Then United FC coach loses ALL access to Jamie's historical data
  And Jamie retains complete access to all evaluation history
  And City Academy coach only sees evaluations from join date forward
  And Jamie can share historical summary with new coach if desired

Scenario: Player controls data sharing
  Given Jamie owns their performance data
  When Jamie wants to share progress with a personal trainer
  Then Jamie should be able to generate a shareable dashboard link
  And the link should show performance trends and achievements
  And Jamie should be able to revoke access at any time
  And the personal trainer should NOT be able to edit any data

User Story 14.2: Guardian Privacy Controls
As a guardian
I want to control my child's data privacy
So that I can ensure appropriate protection and access
Acceptance Criteria:
[ ] Guardian approval for all data sharing beyond coach
[ ] Privacy controls appropriate for child's age
[ ] Communication management with team and coaches
[ ] Data retention policies clearly explained
[ ] Access revocation tools for guardian use
[ ] Account transition planning for when child turns 18
Test Scenarios:
Feature: Guardian Privacy Controls

Scenario: Guardian manages child's data sharing
  Given I am guardian for 15-year-old Jamie
  When a sports scientist requests access to Jamie's data
  Then I should receive approval request with clear explanation
  And I should see exactly what data would be shared
  When I approve with time-limited access
  Then the scientist should receive defined data for specified period
  And I should be able to revoke access at any time

Scenario: Account transition at 18th birthday
  Given Jamie turns 18 next month
  When Jamie's 18th birthday passes
  Then Jamie should receive notification about account control options
  And Jamie should be able to maintain or revoke my guardian access
  And Jamie should gain full control over data sharing decisions
  And I should only retain access if Jamie explicitly grants it

EPIC 15: NOTIFICATIONS & COMMUNICATION
User Story 15.1: Smart Notification System
As a SHOT user
I want to receive relevant notifications at appropriate times
So that I stay engaged without being overwhelmed
Acceptance Criteria:
[ ] Evaluation reminders based on timing windows
[ ] Achievement notifications for milestones and progress
[ ] Team communications from coaches and club admins
[ ] Event notifications for training and matches
[ ] Parent approvals for under-18 activities
[ ] AI-driven notifications for personalized suggestions (See Epic 16)
[ ] Customizable preferences for notification types and timing
[ ] Smart scheduling avoiding late night/early morning unless urgent
Test Scenarios:
Feature: Smart Notifications

Scenario: Evaluation reminder sequence
  Given I have a training session in 36 hours
  When the pre-evaluation window opens (48 hours before)
  Then I should receive notification "Pre-evaluation now available"
  When 24 hours remain until deadline
  Then I should receive reminder "Don't forget: Pre-evaluation due tomorrow"
  When 4 hours remain
  Then I should receive reminder "Complete pre-evaluation - 4 hours left"
  But notifications should not be sent between 10pm-7am unless I opt in

Scenario: AI-driven notification
  Given I have not trained in 5 days
  And my focus area is 'Technical'
  Then I should receive a notification from ALEX
  Saying "Ready to work on your technical skills? I've got a drill for you."
  And the notification should deeplink to the AI Hub or a suggested drill

Scenario: Achievement notifications
  Given I complete my 5th consecutive training session
  When the session evaluation is submitted
  Then I should receive "Achievement Unlocked: Consistency Champion!"
  And the notification should include badge visual
  And it should link to my achievement page
  And it should suggest sharing the achievement

User Story 15.2: Coach-Team Communication
As a coach
I want to communicate effectively with my team and parents
So that everyone stays informed and engaged
Acceptance Criteria:
[ ] Team announcements broadcast to all players and guardians
[ ] Individual messaging with players (with guardian visibility for under-18)
[ ] Parent communication for administrative and development topics
[ ] Event notifications with RSVP functionality
[ ] Urgent messaging capabilities for time-sensitive information
[ ] Message templates for common communications
Test Scenarios:
Feature: Coach Communication

Scenario: Send team announcement
  Given I want to inform my team about training changes
  When I create a team announcement
  And I write "Tomorrow's training moved to 7pm due to weather"
  And I send to "All team members and guardians"
  Then all 18 players should receive the notification
  And all linked guardians should receive the same message
  And the message should appear in team feed
  And players should be able to acknowledge receipt

Scenario: Individual player feedback with guardian visibility
  Given I want to discuss Jamie's development with their guardian
  When I send individual message to Jamie (age 15)
  Then Jamie should receive the message
  And Jamie's guardian should automatically receive copy
  And the message should be marked "Guardian copied"
  And guardian should be able to respond in the thread

🤖 EPIC 16: AI HUB & ASSISTANT SYSTEM
User Story 16.1: ALEX AI Assistant Interface
As a SHOT user
I want to interact with ALEX, the SHOT AI assistant
So that I can get personalized help and navigate the app efficiently
Acceptance Criteria:
[ ] Full-screen AI chat interface accessible from central nav button
[ ] Persistent chat history within session
[ ] Quick action prompts for common tasks
[ ] Natural language processing for user queries
[ ] Contextual responses based on user data and app state
[ ] App navigation triggers from AI conversations
[ ] Typing indicators and response delays for natural feel
Functional Specification:
AI Hub Interface Layout:
Header Section:
├── AI Name: "ALEX" (branded in purple)
├── Close button (X icon)
└── Context indicator showing user's current sport/team

Chat History Area:
├── Auto-scroll to bottom for new messages
├── Message bubbles:
│   ├── User messages: Right-aligned, purple background
│   ├── AI messages: Left-aligned, gray background
│   └── Timestamp display for sessions
├── Chat bubble styling with rounded corners
└── Scrollable container

Quick Actions Grid (2x2):
├── "When is my next match?" → Contextual fixture response
├── "Show me my rewards" → Navigate to rewards section
├── "Help me improve my game" → Trigger improvement flow
└── "What drills should I do?" → AI drill recommendations

Chat Input Section:
├── Text input field with placeholder "Ask me anything..."
├── Send button
├── Auto-focus on modal open
└── Enter key submission support

AI Response Logic:
const aiResponseEngine = {
  contextualData: {
    userProfile: "currentUser",
    activeSport: "appState.activeSport",
    currentTeam: "appState.currentTeamId",
    upcomingFixtures: "userData.fixtures",
    spBalance: "currentUser.sp"
  },
  responsePatterns: {
    nextMatch: "Parse fixtures for upcoming events",
    rewards: "Direct navigation to rewards section",
    improvement: "Trigger improvement flow",
    drills: "Contextual drill recommendations",
    fallback: "Helpful error message with suggestions"
  },
  navigationTriggers: {
    rewards: "() => navigateTo('my-rewards')",
    improvement: "() => startFullImprovementFlow()",
    perform: "() => navigateTo('perform')",
    pulse: "() => navigateTo('pulse')"
  }
};

Test Scenarios:
Feature: ALEX AI Assistant

Scenario: Access AI Hub from navigation
  Given I am on any page in the SHOT app
  When I tap the central AI button in bottom navigation
  Then I should see the full-screen AI chat interface
  And I should see "Hi [my name], I'm Alex. How can I help you improve today?"
  And I should see 4 quick action buttons
  And the chat input should be focused and ready

Scenario: Quick action navigation
  Given I am in the AI Hub
  When I tap "Show me my rewards"
  Then the AI Hub should close
  And I should navigate to the rewards page
  And the transition should be smooth

Scenario: Contextual fixture response
  Given I have an upcoming training session tomorrow
  When I ask "When is my next match?"
  Then ALEX should respond with specific event details
  And include date, time, opponent, and location
  And the response should be formatted clearly

Scenario: Fallback response handling
  Given I am chatting with ALEX
  When I ask "What's the weather like?"
  Then ALEX should respond with a helpful fallback message
  And suggest relevant alternatives I can ask about
  And maintain a helpful, encouraging tone

📱 EPIC 17: STORY HIGHLIGHTS SYSTEM
User Story 17.1: Interactive Story Highlights Interface
As a SHOT user
I want to browse story highlights with engaging visuals
So that I can quickly access featured content and updates
Acceptance Criteria:
[ ] Gradient ring highlights with color coding by story type
[ ] Horizontal scrollable story row with smooth scrolling
[ ] Story type indicators (drop, event, music, star, impact)
[ ] Touch interactions with hover effects and scaling
[ ] Navigation controls for desktop users
[ ] Story detail modals with full content display
Functional Specification:
Story Highlight Visual Design:
Story Ring Gradients:
Purple (Drop): linear-gradient(45deg, var(--shot-gold), var(--shot-purple))
Teal (Event): linear-gradient(45deg, var(--shot-teal), #15a187)
Red (Music): linear-gradient(45deg, var(--shot-gold), var(--shot-red))
Gold (Star): linear-gradient(45deg, #FFD700, var(--shot-gold))
Impact: linear-gradient(45deg, var(--shot-teal), var(--shot-purple))
Interaction States:
Default: scale(1)
Hover: scale(1.05) with transition
Active: Ring glow effect
Visited: Opacity adjustment
Story Data Structure:
const storyHighlight = {
  id: "unique_story_id",
  type: "drop|event|music|star|impact",
  label: "Display text (max 15 chars)",
  color: "ring gradient identifier",
  contentTitle: "Modal header text",
  contentBody: "Full story content with formatting",
  metadata: {
    created: "timestamp",
    priority: "number",
    targetAudience: ["member", "athlete", "coach"]
  }
};

Test Scenarios:
Feature: Story Highlights Interface

Scenario: Story highlights display and interaction
  Given I am viewing the Clubhouse
  When I see the story highlights section
  Then I should see a horizontal row of story circles
  And each circle should have appropriate gradient coloring
  And labels should be clearly readable within circles
  When I tap on a story highlight
  Then a full-screen modal should appear with story content

Scenario: Story navigation and scrolling
  Given there are more than 4 story highlights
  When I view the story highlights row
  Then I should be able to scroll horizontally through all stories
  And on desktop, I should see navigation arrow buttons
  When I click the navigation arrows
  Then the story row should scroll smoothly left or right

Scenario: Story modal interaction
  Given I have opened a story detail modal
  When I view the story content
  Then I should see the full title and content
  And I should see navigation arrows to adjacent stories
  When I tap outside the modal content
  Then the modal should close and return to Clubhouse

💰 EPIC 18: COMPREHENSIVE SP (SHOT POINTS) ECONOMY
User Story 18.1: SP Earning System
As a SHOT user
I want to earn SP through various activities
So that I can unlock rewards and track my engagement
Acceptance Criteria:
[ ] Multiple earning methods with different SP values
[ ] Real-time SP updates in header display
[ ] Earning notifications with reason explanations
[ ] Activity tracking for earning history
[ ] Progressive rewards for consistent engagement
Functional Specification:
SP Earning Matrix:
const spEarningSystem = {
  activities: {
    drillCompletion: 150,
    matchEvaluation: 100,
    trainingAttendance: 75,
    socialLike: 10,
    achievementUnlock: 250,
    eventAttendance: 200,
    profileCompletion: 100,
    weeklyGoal: 150
  },
  multipliers: {
    weekendBonus: 1.5,
    streakBonus: 1.2,
    newUserBonus: 2.0
  },
  limits: {
    dailyMaxFromSocial: 100
  }
};

User Story 18.2: SP Spending & Rewards System
As a SHOT user
I want to spend my SP on valuable rewards
So that I can unlock exclusive experiences and items
Acceptance Criteria:
[ ] Tiered reward system from 500 SP to 25,000 SP
[ ] Reward categories (discounts, merchandise, experiences, entries)
[ ] Availability status (available, locked, claimed, expired)
[ ] SP requirement display with affordability indicators
[ ] Claiming process with confirmation and fulfillment
Functional Specification:
Reward Tier Structure:
const rewardTiers = {
  bronze: {
    range: "500 - 2,000 SP",
    rewards: ["Partner discounts (15% off)", "Contest entries", "Digital wallpapers"]
  },
  silver: {
    range: "2,000 - 5,000 SP",
    rewards: ["SHOT merchandise vouchers (£20)", "Early access to drops", "Virtual coaching sessions"]
  },
  gold: {
    range: "5,000 - 15,000 SP",
    rewards: ["Free SHOT merchandise", "VIP event access", "Professional coaching sessions"]
  },
  platinum: {
    range: "15,000+ SP",
    rewards: ["Training camp scholarships", "Professional athlete meetups", "Exclusive SHOT experiences"]
  }
};

Test Scenarios:
Feature: SP Rewards System

Scenario: Claiming available reward
  Given I have 2,500 SP
  And there is a £20 merchandise voucher for 2,000 SP
  When I view the reward and tap "Claim"
  Then my SP should decrease to 500
  And the reward status should change to "Claimed"
  And I should receive confirmation with fulfillment details

Scenario: Locked reward indication
  Given I have 1,500 SP
  And there is a reward requiring 2,000 SP
  When I view the reward
  Then I should see "500 SP needed" indicator
  And the claim button should be disabled

📊 EPIC 19: ADVANCED DATA VISUALIZATION
User Story 19.1: Performance Corner Charts
As a SHOT user
I want to see my performance data visualized clearly
So that I can track improvement across all performance corners
Acceptance Criteria:
[ ] 12-month trending charts for performance history
[ ] Corner-specific color coding consistent throughout app
[ ] Interactive chart elements with hover states and data points
[ ] Responsive design optimized for mobile devices
[ ] Chart.js integration with proper error handling
Functional Specification:
Performance Corner Color System:
const cornerColorSystem = {
  technical: "#296DFF",
  physical: "#2E8B57",
  psychological: "#FF6F3C",
  social: "#FF5D73",
  positional: "#A95CFF"
};

Chart Configuration (Chart.js):
const performanceChartConfig = {
  type: "line",
  responsive: true,
  maintainAspectRatio: false,
  datasets: {
    borderWidth: 2,
    pointRadius: 0,
    pointHoverRadius: 4,
    tension: 0.4,
    fill: false
  },
  scales: {
    y: { beginAtZero: false, max: 10, min: 1 },
    x: { grid: { display: false } }
  },
  plugins: { legend: { display: false } }
};

Test Scenarios:
Feature: Performance Data Visualization

Scenario: Chart renders with user data
  Given I have 12 months of evaluation data
  When I view my Perform dashboard
  Then I should see a line chart with my performance trends
  And each corner should have its designated color
  And the chart should show last 12 months of data

Scenario: Chart responsiveness on mobile
  Given I am viewing the chart on a mobile device
  When the chart loads
  Then it should fit properly within the container
  And maintain readability of labels and lines

🎯 EPIC 20: PULSE CONTENT ARCHITECTURE
User Story 20.1: Content Access Levels & Pillar System
As a SHOT user
I want to access content appropriate to my engagement level
So that I receive relevant, valuable content experiences
Acceptance Criteria:
[ ] Four access levels (PULSE, CIRCLE, COMMUNITY, IMPACT)
[ ] Three pillar framework (TAKE YOUR SHOT, OWN IT, MAKE IMPACT)
[ ] Content filtering by access level and pillar
[ ] Visual indicators for content access requirements
[ ] Graduated content experience encouraging progression
Functional Specification:
Content Access Level System:
const contentAccessLevels = {
  PULSE: { description: "Open access content for all users" },
  CIRCLE: { description: "Exclusive content for engaged members" },
  COMMUNITY: { description: "Community-driven content and local stories" },
  IMPACT: { description: "Social impact and charitable initiative content" }
};

Pillar Framework:
const pillarFramework = {
  "TAKE YOUR SHOT": { philosophy: "Courage, opportunity, and seizing moments" },
  "OWN IT": { philosophy: "Personal responsibility, authenticity, and self-improvement" },
  "MAKE IMPACT": { philosophy: "Community contribution and positive change" }
};

Test Scenarios:
Feature: Content Access Levels

Scenario: PULSE content available to all users
  Given I am any type of SHOT user
  When I view the Pulse feed
  Then I should see content marked "PULSE"
  And these should be accessible without restrictions

Scenario: CIRCLE content for engaged members
  Given I am an engaged SHOT member (high SP or activity)
  When I view content marked "CIRCLE"
  Then I should see exclusive behind-scenes content

🏆 EPIC 21: IMPACT MEASUREMENT & DISPLAY
User Story 21.1: Community Impact Tracking
As a SHOT community member
I want to see the real impact SHOT is making
So that I feel connected to a meaningful movement
Acceptance Criteria:
[ ] Quantified impact metrics (people coached, money raised, equipment donated)
[ ] Community achievement stories with visual documentation
[ ] Partnership impact showcase with institutional collaborations
[ ] Real-time impact updates showing growing community effect
Functional Specification:
Impact Category Structure:
const impactCategories = {
  communityWins: { focus: "Local team achievements and grassroots success" },
  partnerships: { focus: "Institutional collaborations and systematic change" },
  givingBack: { focus: "Charitable contributions and community support" },
  coachingReach: { focus: "Direct coaching impact and participation growth" }
};

Impact Metrics Dashboard:
const impactMetrics = {
  realTimeCounters: {
    totalCoached: "500+ in past 12 months",
    weeklyParticipants: "250+ children every week",
    moneyRaised: "£450 for community defibrillator"
  }
};

Test Scenarios:
Feature: Impact Measurement Display

Scenario: Community wins showcase
  Given SHOT has supported local teams
  When I view the Impact section
  Then I should see specific team achievements
  And each should show team name, achievement, and SHOT connection

Scenario: Real-time metrics display
  Given SHOT has ongoing coaching programs
  When I view coaching reach metrics
  Then I should see current participant numbers
  And weekly participation rates

🛍️ EPIC 22: SHOPPING CART & COMMERCE UX
User Story 22.1: Shopping Cart Management
As a SHOT user
I want to manage my cart before Shopify checkout
So that I can review items and make changes easily
Acceptance Criteria:
[ ] Header cart icon with item count badge
[ ] Add to cart functionality from product displays
[ ] Cart state persistence across app sessions
[ ] Stock status integration with availability warnings
[ ] Pre-checkout cart review before Shopify handoff
Functional Specification:
Cart State Management:
const cartSystem = {
  storage: "sessionStorage for cart persistence",
  cartItem: {
    id: "product_variant_id",
    name: "product_name",
    price: "decimal_price",
    quantity: "integer_quantity",
    image: "product_image_url"
  },
  cartOperations: {
    addItem: "Add product variant to cart",
    removeItem: "Remove item from cart",
    updateQuantity: "Modify item quantity"
  }
};

Test Scenarios:
Feature: Shopping Cart Management

Scenario: Add item to cart
  Given I am viewing a product in the Locker
  When I tap the "+" button on a product
  Then the item should be added to my cart
  And the header cart badge should update to show item count

Scenario: Cart persistence across sessions
  Given I have items in my cart
  When I close and reopen the SHOT app
  Then my cart should retain all items
  And I should be able to proceed with checkout

👤 EPIC 23: PROFILE SWITCHING & MULTI-ROLE
User Story 23.1: Profile Switching Interface
As a SHOT user with multiple roles
I want to switch between different contexts easily
So that I can access appropriate content and features
Acceptance Criteria:
[ ] Profile switcher modal with circular progress visualization for SP
[ ] Achievement showcase with visual badge display
[ ] Context switching between sports and teams
[ ] Role-based content adaptation throughout app
Functional Specification:
Profile Switcher Design:
SP Progress Visualization: Circular progress ring around avatar showing progress to next reward.
Achievement Display: Horizontal scrollable row of unlocked achievement badges.
Context Switching: UI elements to change active sport, team, or role.
Test Scenarios:
Feature: Profile Switching Interface

Scenario: Profile switcher access and display
  Given I am logged into SHOT
  When I tap on my avatar in the header
  Then the profile switcher should appear
  And show my circular SP progress indicator
  And display my achievements in a scrollable row

Scenario: Role-based content filtering
  Given I switch from Member to Athlete role
  When I view the Clubhouse
  Then I should see team-specific content instead of general community

📈 EPIC 24: DRILL FRAMEWORK & CONTENT SYSTEM
User Story 24.1: Intelligent Drill Selection System
As a SHOT user creating a drill session
I want to the system to recommend the perfect drill for my context
So that I get personalized, effective training
Acceptance Criteria:
[ ] Multi-criteria drill matching (participants, environment, focus areas)
[ ] Drill adaptation logic for non-perfect matches
[ ] Comprehensive drill database with detailed metadata
[ ] Progressive difficulty based on user level and history
[ ] Smart fallback options when perfect matches aren't available
Functional Specification:
Drill Selection Algorithm:
const drillSelectionSystem = {
  matchingCriteria: {
    primary: { participants: "solo, pair, group", environment: "indoor, outdoor" },
    secondary: { focusAreas: "technical, physical, etc.", difficulty: "beginner, etc." }
  },
  adaptationLogic: {
    environmentAdaptation: "Modify outdoor drills for indoor space",
    equipmentSubstitution: "Suggest alternatives for missing equipment"
  }
};

Test Scenarios:
Feature: Intelligent Drill Selection

Scenario: Perfect match drill selection
  Given I select "Pair", "Outdoor", and "Technical" focus
  When the system searches for drills
  Then it should find drills that exactly match all criteria
  And return the best-rated drill for my experience level

Scenario: Drill adaptation for imperfect match
  Given I select "Solo", "Indoor" but only outdoor drills exist
  When the system processes my request
  Then it should select an outdoor drill
  And provide modification instructions for indoor use

COMPLETE DEFINITION OF DONE
Technical Requirements ✅
Backend Systems:
[ ] User authentication (Supabase) with email verification
[ ] Role-based access control enforcing data ownership principles
[ ] Evaluation timing system with 48-hour windows enforced
[ ] Notification system with smart scheduling
[ ] File upload capabilities for avatar and training media
[ ] AI Chat System - ALEX assistant with contextual responses
[ ] SP Economy Engine - Points calculation, earning/spending tracking
[ ] Multi-role User System - Role switching, context management
External Service Integrations:
[ ] Perform Framework Data - Static JSON configs stored in database (v1)
[ ] Pulse Content Feed - Google Apps Script backend with content filtering
[ ] Locker Shopping - Shopify Storefront API integration with native UX
[ ] Payment processing - Shopify checkout with order status sync
[ ] Content Management - Google Apps Script for pulse, admin dashboard for core
[ ] Admin dashboard with all management functions
[ ] API rate limiting and security measures
[ ] Database backup and recovery procedures
[ ] GDPR compliance tools and data export capabilities
Frontend Application:
[ ] Progressive Web App installable on all devices
[ ] Responsive design working perfectly on mobile, tablet, desktop
[ ] Offline functionality for core features
[ ] Real-time updates for notifications and team changes
[ ] Accessibility compliance (WCAG 2.1 AA)
[ ] Performance optimization (loading times under 2 seconds)
[ ] Cross-browser compatibility (Chrome, Safari, Firefox, Edge)
[ ] App store deployment (iOS and Android native wrappers)
[ ] Interactive Story System - Swipeable highlights with modals
[ ] Advanced Data Visualization - Corner-coded charts with Chart.js
Data & Security:
[ ] Data encryption at rest and in transit
[ ] Player data ownership enforced at database level
[ ] Age verification and appropriate protections
[ ] Parent approval system working securely
[ ] Audit logging for all sensitive operations
[ ] Privacy controls appropriate for different age groups
[ ] Secure sharing mechanisms for data access
[ ] Regular security audits and penetration testing
User Experience Requirements ✅
Onboarding:
[ ] App installation smooth on all platforms
[ ] Registration process completed in under 3 minutes
[ ] Email verification working reliably
[ ] Sport Head creation engaging and fun
[ ] Path selection clear and motivating
[ ] First session can be started within 5 minutes of choosing path
Core Flows:
[ ] Instant sessions working end-to-end (selection → evaluation → training → results)
[ ] Club joining working for all scenarios (adult, under-18, guardian)
[ ] Coach verification efficient and transparent
[ ] Team management intuitive for coaches
[ ] Parent oversight comprehensive and reassuring
[ ] Evaluation system working within timing windows
[ ] Data sharing secure and controlled
[ ] AI Integration - Contextual AI responses and navigation triggers
Content & Engagement:
[ ] Dynamic content appropriate for each user type
[ ] Pulse filtering instant and accurate
[ ] Following system working smoothly
[ ] Shopping experience professional and secure
[ ] Achievement system motivating and visible
[ ] Progress tracking clear and encouraging
[ ] Content Experience - Access levels, pillars, and impact storytelling
Quality Assurance ✅
Testing Coverage:
[ ] Unit tests for all critical functions (>80% coverage)
[ ] Integration tests for user journeys
[ ] End-to-end tests for complete workflows
[ ] Performance testing under load
[ ] Security testing for vulnerabilities
[ ] Accessibility testing with screen readers
[ ] Mobile device testing on real devices
[ ] Cross-platform testing (iOS, Android, Web)
[ ] AI System Testing - Contextual responses, fallbacks, and triggers
User Acceptance Testing:
[ ] All Gherkin scenarios passing in test environment
[ ] Real user testing with target demographics
[ ] Coach feedback on team management tools
[ ] Parent approval of safety and privacy measures
[ ] Admin testing of management functions
[ ] Performance benchmarks met consistently
[ ] Error handling graceful and helpful
[ ] Edge case handling working properly
Launch Readiness ✅
Infrastructure:
[ ] Production environment stable and monitored
[ ] CDN deployment for global performance
[ ] Database scaling ready for user growth
[ ] Backup systems tested and verified
[ ] Monitoring alerts configured for critical issues
[ ] Support systems ready for user inquiries
[ ] Documentation complete for all features
[ ] Training materials prepared for support team
Legal & Compliance:
[ ] Terms of service approved by legal team
[ ] Privacy policy GDPR and COPPA compliant
[ ] Age verification legally compliant
[ ] Data processing agreements in place
[ ] App store approvals received
[ ] Payment processing compliance verified
[ ] Insurance coverage appropriate for user data
[ ] Safeguarding policies implemented and monitored
Business Operations:
[ ] Coach verification team trained and ready
[ ] Customer support staffed with SHOT-trained team
[ ] Content moderation systems and human reviewers ready
[ ] Payment processing tested and verified
[ ] Merchant fulfillment ready for orders
[ ] Marketing materials aligned with app features
[ ] Launch communications prepared for users
[ ] Success metrics defined and tracking implemented
[ ] Content Operations - Editorial team trained on new architecture
[ ] AI Operations - Response training and monitoring plan in place

