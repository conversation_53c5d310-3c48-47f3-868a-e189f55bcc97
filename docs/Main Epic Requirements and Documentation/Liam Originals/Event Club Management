<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHOT Club Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        /* Custom scrollbar for a sleeker look */
        .hide-scrollbar::-webkit-scrollbar { display: none; }
        .hide-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }
        .fade-in { animation: fadeIn 0.5s ease-in-out; }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        details > summary { list-style: none; }
        details > summary::-webkit-details-marker { display: none; }
        .tab-active {
            border-bottom: 2px solid #22d3ee; /* cyan-400 */
            color: white;
        }
        .details-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        details[open] .details-content {
            max-height: 4000px; /* Adjust as needed for more content */
        }
        /* Custom toggle switch style */
        #coachModeToggle:checked + .block { background-color: #22d3ee; }
        #coachModeToggle:checked + .block + .dot { transform: translateX(1.5rem); }
    </style>
</head>
<body class="bg-gray-900 text-white antialiased">

    <!-- App Wrapper -->
    <div id="app-container" class="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">

        <!-- App Header -->
        <header class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-cyan-400">SHOT</h1>
                <p class="text-gray-400">Club & Term Management</p>
            </div>
            <div class="flex items-center space-x-4">
                 <div id="user-id-display" class="text-right"></div>
                <label for="coachModeToggle" class="flex items-center cursor-pointer">
                    <span class="mr-3 text-sm font-medium">Coach Mode</span>
                    <div class="relative">
                        <input type="checkbox" id="coachModeToggle" class="sr-only">
                        <div class="block bg-gray-600 w-14 h-8 rounded-full"></div>
                        <div class="dot absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition"></div>
                    </div>
                </label>
            </div>
        </header>

        <!-- Main Content Area -->
        <main id="main-content" class="fade-in">
            <!-- Dynamic content will be rendered here -->
        </main>

    </div>

    <!-- Modal Container -->
    <div id="modal-container" class="fixed inset-0 z-50 pointer-events-none"></div>

    <!-- Firebase and App Logic -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-app.js";
        import { getAuth, signInAnonymously, onAuthStateChanged, signInWithCustomToken } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-auth.js";
        import { getFirestore, collection, doc, addDoc, getDocs, onSnapshot, query, where, serverTimestamp, setDoc, getDoc, deleteDoc, updateDoc, arrayUnion, arrayRemove, writeBatch, runTransaction } from "https://www.gstatic.com/firebasejs/11.6.1/firebase-firestore.js";

        // --- STATE MANAGEMENT ---
        const appState = {
            isCoachMode: false,
            userId: null,
            authReady: false,
            termsData: [],
            myBookings: [],
            venues: [],
            discounts: [],
            currentParentView: 'my-bookings',
            currentCoachView: 'terms', 
        };

        // --- DOM ELEMENTS ---
        const mainContent = document.getElementById('main-content');
        const modalContainer = document.getElementById('modal-container');
        const coachModeToggle = document.getElementById('coachModeToggle');
        const userIdDisplay = document.getElementById('user-id-display');

        // --- FIREBASE SETUP ---
        let db, auth;
        const appId = typeof __app_id !== 'undefined' ? __app_id : 'shot-club-dev';
        const firebaseConfig = typeof __firebase_config !== 'undefined' ? JSON.parse(__firebase_config) : { apiKey: "your-fallback-api-key", authDomain: "your-fallback-auth-domain", projectId: "your-fallback-project-id" };

        try {
            const app = initializeApp(firebaseConfig);
            db = getFirestore(app);
            auth = getAuth(app);
        } catch (e) {
            console.error("Firebase initialization failed:", e);
            mainContent.innerHTML = `<div class="bg-red-800 p-4 rounded-lg text-center">Could not connect to the application services. Please try again later.</div>`;
        }
        
        // --- AUTHENTICATION ---
        onAuthStateChanged(auth, async user => {
            if (user && !appState.authReady) {
                appState.userId = user.uid;
                appState.authReady = true;
                console.log("Authentication successful. User ID:", appState.userId);
                
                // Seed data on first load for this app instance
                await seedDemoData();

                renderApp();
            }
        });
        
        async function authenticateUser() {
            try {
                if (typeof __initial_auth_token !== 'undefined' && __initial_auth_token) {
                    await signInWithCustomToken(auth, __initial_auth_token);
                } else {
                    await signInAnonymously(auth);
                }
            } catch (error) {
                console.error("Authentication Error: ", error);
            }
        }

        // --- CORE RENDER LOGIC ---
        function renderApp() {
            if (!appState.authReady) {
                mainContent.innerHTML = `<div class="text-center text-gray-400">Authenticating...</div>`;
                return;
            }
            mainContent.innerHTML = '';

            const userRole = appState.isCoachMode ? 'Coach' : 'Parent';
            userIdDisplay.innerHTML = `
                <p class="text-xs text-gray-500">UID: ${appState.userId.substring(0,8)}...</p>
                <p class="text-xs font-bold text-cyan-400">${userRole} View</p>
            `;

            if (appState.isCoachMode) {
                renderCoachView();
            } else {
                renderParentView();
            }
        }

        // --- PARENT VIEW ---
        function renderParentView() {
            const parentViewHTML = `
                <div class="mb-6">
                    <div class="flex border-b border-gray-700 mb-4">
                        <button id="my-bookings-btn" class="py-2 px-4 font-semibold transition" onclick="window.switchParentView('my-bookings')">My Bookings</button>
                        <button id="all-classes-btn" class="py-2 px-4 font-semibold transition" onclick="window.switchParentView('all-terms')">All Terms & Camps</button>
                    </div>
                    <div id="parent-view-content"></div>
                </div>
            `;
            mainContent.innerHTML = parentViewHTML;
            switchParentView(appState.currentParentView);
        }

        window.switchParentView = (view) => {
            appState.currentParentView = view;
            const myBookingsBtn = document.getElementById('my-bookings-btn');
            const allClassBtn = document.getElementById('all-classes-btn');
            const contentEl = document.getElementById('parent-view-content');

            myBookingsBtn.className = 'py-2 px-4 font-semibold transition text-gray-400';
            allClassBtn.className = 'py-2 px-4 font-semibold transition text-gray-400';
            
            if (view === 'my-bookings') {
                myBookingsBtn.classList.remove('text-gray-400');
                myBookingsBtn.classList.add('tab-active');
                contentEl.innerHTML = `<div class="text-center p-8"><span class="text-cyan-400">Loading your bookings...</span></div>`;
                listenForMyBookings();
            } else {
                allClassBtn.classList.remove('text-gray-400');
                allClassBtn.classList.add('tab-active');
                contentEl.innerHTML = `<div id="terms-list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"><div class="text-center p-8 col-span-full"><span class="text-cyan-400">Loading all terms...</span></div></div>`;
                listenForAllTerms();
            }
        }
        
        function listenForMyBookings() {
            const regsQuery = query(collection(db, `/artifacts/${appId}/public/data/registrations`), where("parentId", "==", appState.userId));
            onSnapshot(regsQuery, async (regSnapshot) => {
                const contentEl = document.getElementById('parent-view-content');
                if (!contentEl) return;
                if (regSnapshot.empty) {
                    contentEl.innerHTML = `<div class="bg-gray-800 rounded-lg p-8 text-center">
                        <h3 class="text-xl font-bold">You haven't booked any terms yet.</h3>
                        <p class="text-gray-400 mt-2">Switch to the "All Terms & Camps" tab to find an activity for your child.</p>
                    </div>`;
                    return;
                }

                const bookingPromises = regSnapshot.docs.map(async (regDoc) => {
                    const registration = { id: regDoc.id, ...regDoc.data() };
                    const termDoc = await getDoc(doc(db, `/artifacts/${appId}/public/data/terms`, registration.termId));
                    if (!termDoc.exists()) return null;
                    const term = { id: termDoc.id, ...termDoc.data() };
                    
                    const schedulesQuery = query(collection(db, `/artifacts/${appId}/public/data/schedules`), where("termId", "==", registration.termId));
                    const scheduleSnapshot = await getDocs(schedulesQuery);
                    term.schedule = scheduleSnapshot.docs.map(d => ({id: d.id, ...d.data()}));

                    return { registration, term };
                });

                const bookings = (await Promise.all(bookingPromises)).filter(b => b !== null);
                appState.myBookings = bookings;
                renderMyBookings(bookings);
            });
        }

        function renderMyBookings(bookings) {
            const contentEl = document.getElementById('parent-view-content');
            contentEl.innerHTML = `<div class="space-y-6">${bookings.map(({ registration, term }) => {
                const paymentStatus = registration.paymentStatus || 'unpaid';
                const paymentColor = paymentStatus === 'paid' ? 'text-green-400' : 'text-yellow-400';
                const paymentText = paymentStatus === 'paid' ? 'Paid' : 'Payment Due';
                const finalPrice = registration.finalPrice || term.price;
                const priceText = term.paymentType === 'subscription' ? `£${finalPrice}/month` : `£${finalPrice}`;
                const bookingType = registration.bookingType === 'trial' ? '<span class="text-xs bg-purple-600 text-white font-bold uppercase px-2 py-1 rounded-full">Trial</span>' : '';

                const scheduleHTML = term.schedule && term.schedule.length > 0
                    ? `<div class="mt-4 pt-4 border-t border-gray-700">
                            <h4 class="font-semibold text-sm mb-2 text-gray-300">Schedule (${term.schedule.length} sessions)</h4>
                            <ul class="space-y-1 text-xs text-gray-400 max-h-32 overflow-y-auto hide-scrollbar">
                                ${term.schedule.map(item => {
                                    const date = new Date(item.date).toLocaleDateString('en-GB', { weekday: 'short', day: 'numeric', month: 'short' });
                                    return `<li class="flex justify-between"><span>${item.title}</span> <span>${date} at ${item.startTime}</span></li>`;
                                }).join('')}
                            </ul>
                        </div>`
                    : '';
                
                const availabilityHTML = `
                    <div class="mt-4 pt-4 border-t border-gray-700">
                        <h4 class="font-semibold text-sm mb-2 text-gray-300">Availability Requests</h4>
                        <div id="availability-requests-${registration.id}" class="space-y-2 text-sm">
                           <p class="text-gray-500">Loading requests...</p>
                        </div>
                    </div>
                `;
                
                renderBookingAvailabilityRequests(registration, term);

                return `
                    <div class="bg-gray-800 rounded-lg shadow-lg p-6 flex flex-col justify-between fade-in">
                        <div>
                            <div class="flex justify-between items-start">
                                <h3 class="text-xl font-bold text-white">${term.name}</h3>
                                ${bookingType}
                            </div>
                            <p class="text-sm text-gray-300">For: <span class="font-semibold text-white">${registration.childName}</span></p>
                            <p class="text-cyan-400 font-medium">${term.venueName}</p>
                            ${scheduleHTML}
                            ${availabilityHTML}
                        </div>
                        <div class="mt-6">
                            <div class="flex justify-between items-center mb-4">
                               <span class="text-lg font-bold ${paymentColor}">${paymentText}</span>
                               <span class="text-lg font-bold text-white">${priceText}</span>
                            </div>
                            ${paymentStatus === 'unpaid' ? `<button onclick="window.handlePayment('${registration.id}')" class="w-full text-center bg-green-600 hover:bg-green-700 text-white font-bold py-3 rounded-lg transition duration-200">Pay Now</button>` : ''}
                        </div>
                    </div>
                `;
            }).join('')}</div>`;
        }
        
        async function renderBookingAvailabilityRequests(registration, term) {
            const containerId = `availability-requests-${registration.id}`;
            const reqQuery = query(collection(db, `/artifacts/${appId}/public/data/availability_requests`), where("termId", "==", term.id));
            
            onSnapshot(reqQuery, async (reqSnapshot) => {
                const container = document.getElementById(containerId);
                if (!container) return;

                if (reqSnapshot.empty) {
                    container.innerHTML = '<p class="text-gray-500 text-xs">No availability requests for this term yet.</p>';
                    return;
                }

                const requests = reqSnapshot.docs.map(d => ({ id: d.id, ...d.data() }));

                const respQuery = query(collection(db, `/artifacts/${appId}/public/data/availability_responses`), where("registrationId", "==", registration.id));
                const respSnapshot = await getDocs(respQuery);
                const responses = respSnapshot.docs.map(d => d.data());

                let html = '';
                for (const request of requests) {
                    const scheduleItem = term.schedule.find(s => s.id === request.scheduleId);
                    if (!scheduleItem) continue;

                    const existingResponse = responses.find(r => r.requestId === request.id);
                    const date = new Date(scheduleItem.date).toLocaleDateString('en-GB', { weekday: 'short', day: 'numeric', month: 'short' });

                    html += `<div class="bg-gray-700 p-3 rounded-md">`;
                    html += `<p class="font-semibold text-white">${scheduleItem.title} - ${date}</p>`;
                    if (request.message) {
                        html += `<p class="text-gray-300 italic text-xs mt-1">"${request.message}"</p>`;
                    }
                    
                    if (existingResponse) {
                        const statusClass = existingResponse.status === 'available' ? 'text-green-400' : 'text-red-400';
                        html += `<p class="mt-2 font-bold text-xs">Your response: <span class="${statusClass} uppercase">${existingResponse.status}</span></p>`;
                    } else {
                        html += `
                            <div class="flex gap-2 mt-2">
                                <button onclick="window.handleAvailabilityResponse('${request.id}', '${registration.id}', 'available')" class="flex-1 bg-green-600 hover:bg-green-700 text-white font-bold py-1 px-2 text-xs rounded-md transition">✅ Available</button>
                                <button onclick="window.handleAvailabilityResponse('${request.id}', '${registration.id}', 'unavailable')" class="flex-1 bg-red-600 hover:bg-red-700 text-white font-bold py-1 px-2 text-xs rounded-md transition">❌ Unavailable</button>
                            </div>
                        `;
                    }
                    html += `</div>`;
                }
                container.innerHTML = html;
            });
        }
        
        function listenForAllTerms() {
            const termsCollection = collection(db, `/artifacts/${appId}/public/data/terms`);
            onSnapshot(termsCollection, (snapshot) => {
                const promises = snapshot.docs.map(async termDoc => {
                    const term = { id: termDoc.id, ...termDoc.data() };
                    const registrationsQuery = query(collection(db, `/artifacts/${appId}/public/data/registrations`), where("termId", "==", term.id));
                    const regSnapshot = await getDocs(registrationsQuery);
                    term.spotsLeft = term.capacity - regSnapshot.size;
                    
                    const waitlistQuery = query(collection(db, `/artifacts/${appId}/public/data/waitlist`), where("termId", "==", term.id));
                    const waitlistSnapshot = await getDocs(waitlistQuery);
                    term.waitlistCount = waitlistSnapshot.size;
                    
                    return term;
                });

                Promise.all(promises).then(terms => {
                    appState.termsData = terms;
                    renderTermCards(terms);
                });
            });
        }

        function renderTermCards(terms) {
            const termsList = document.getElementById('terms-list');
            if (!termsList) return;
            if (terms.length === 0) {
                termsList.innerHTML = `<div class="bg-gray-800 rounded-lg p-8 text-center col-span-full">
                    <h3 class="text-xl font-bold">No Activities Available</h3>
                    <p class="text-gray-400 mt-2">The club administrator has not created any terms or camps yet. Please check back later.</p>
                </div>`;
                return;
            }
            
            termsList.innerHTML = terms.map(term => {
                const spotsLeft = term.spotsLeft;
                let spotsColor = 'text-green-400';
                let spotsText = `${spotsLeft} Spots Left`;
                let isFull = false;

                if (spotsLeft <= 0) {
                    spotsColor = 'text-red-500';
                    spotsText = 'Full';
                    isFull = true;
                } else if (spotsLeft <= 5) {
                    spotsColor = 'text-yellow-400';
                }
                const startDate = new Date(term.startDate).toLocaleDateString('en-GB', { day: 'numeric', month: 'short' });
                const endDate = new Date(term.endDate).toLocaleDateString('en-GB', { day: 'numeric', month: 'short' });
                const priceText = term.paymentType === 'subscription' ? `£${term.price}/month` : `£${term.price}`;

                return `
                    <div class="bg-gray-800 rounded-lg shadow-lg p-6 flex flex-col justify-between fade-in">
                        <div>
                            <h3 class="text-xl font-bold text-white">${term.name}</h3>
                            <p class="text-cyan-400 font-medium">${term.venueName}</p>
                            <p class="text-gray-400 text-sm mt-1">${startDate} - ${endDate}</p>
                            <p class="text-gray-300 mt-2 text-sm">${term.description}</p>
                        </div>
                        <div class="mt-6">
                            <div class="flex justify-between items-center mb-4">
                                <span class="text-lg font-bold ${spotsColor}">${spotsText}</span>
                                <span class="text-lg font-bold text-white">${priceText}</span>
                            </div>
                            <button onclick="window.showBookingModal('${term.id}')" class="w-full text-center bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-3 rounded-lg transition duration-200">
                                ${isFull ? 'Join Waitlist' : 'Book Now'}
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        }

        // --- COACH VIEW ---
        function renderCoachView() {
            const coachViewHTML = `
                <div class="mb-6">
                    <div class="flex border-b border-gray-700 mb-4">
                        <button id="coach-terms-btn" class="py-2 px-4 font-semibold transition" onclick="window.switchCoachView('terms')">Terms & Camps</button>
                        <button id="coach-setup-btn" class="py-2 px-4 font-semibold transition" onclick="window.switchCoachView('setup')">System Setup</button>
                    </div>
                    <div id="coach-view-content"></div>
                </div>
            `;
            mainContent.innerHTML = coachViewHTML;
            switchCoachView(appState.currentCoachView);
        }

        window.switchCoachView = (view) => {
            appState.currentCoachView = view;
            const termsBtn = document.getElementById('coach-terms-btn');
            const setupBtn = document.getElementById('coach-setup-btn');
            const contentEl = document.getElementById('coach-view-content');

            termsBtn.className = 'py-2 px-4 font-semibold transition text-gray-400';
            setupBtn.className = 'py-2 px-4 font-semibold transition text-gray-400';

            if (view === 'terms') {
                termsBtn.classList.remove('text-gray-400');
                termsBtn.classList.add('tab-active');
                contentEl.innerHTML = `
                    <div class="flex justify-between items-center mb-6">
                        <div><h2 class="text-2xl font-bold">My Terms & Camps</h2><p class="text-gray-400">Manage your activities, view registrations, and schedules.</p></div>
                        <button onclick="window.showCreateTermModal()" class="bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-2 px-4 rounded-lg transition duration-200">+ Create Activity</button>
                    </div>
                    <div id="coach-terms-list" class="space-y-4"><div class="text-center p-8"><span class="text-cyan-400">Loading your activities...</span></div></div>`;
                listenForCoachTerms();
            } else { // System Setup View
                setupBtn.classList.remove('text-gray-400');
                setupBtn.classList.add('tab-active');
                contentEl.innerHTML = `
                    <div>
                        <div class="flex justify-between items-center mb-6">
                            <div><h2 class="text-2xl font-bold">System Setup</h2><p class="text-gray-400">Manage your club's core information.</p></div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Venues Column -->
                            <div>
                                <div class="flex justify-between items-center mb-3">
                                    <h3 class="text-xl font-semibold">Venues</h3>
                                    <button onclick="window.showCreateVenueModal()" class="bg-gray-700 hover:bg-gray-600 text-white text-sm font-bold py-1 px-3 rounded-lg transition">+ Add Venue</button>
                                </div>
                                <div id="venues-list" class="space-y-2 bg-gray-800 p-4 rounded-lg"></div>
                            </div>
                            <!-- Coaches Column -->
                            <div>
                                <div class="flex justify-between items-center mb-3">
                                    <h3 class="text-xl font-semibold">Coaches / Staff</h3>
                                    <button onclick="window.showCreateCoachModal()" class="bg-gray-700 hover:bg-gray-600 text-white text-sm font-bold py-1 px-3 rounded-lg transition">+ Add Coach</button>
                                </div>
                                <div id="coaches-list" class="space-y-2 bg-gray-800 p-4 rounded-lg"></div>
                            </div>
                             <!-- Discounts Column -->
                            <div>
                                <div class="flex justify-between items-center mb-3">
                                    <h3 class="text-xl font-semibold">Discount Codes</h3>
                                    <button onclick="window.showCreateDiscountModal()" class="bg-gray-700 hover:bg-gray-600 text-white text-sm font-bold py-1 px-3 rounded-lg transition">+ Add Code</button>
                                </div>
                                <div id="discounts-list" class="space-y-2 bg-gray-800 p-4 rounded-lg"></div>
                            </div>
                        </div>
                    </div>
                `;
                listenForVenues();
                listenForCoaches();
                listenForDiscounts();
            }
        }

        function listenForCoachTerms() {
            // In demo mode, show all terms. In a real app, this would be filtered by coachId.
            const q = query(collection(db, `/artifacts/${appId}/public/data/terms`));
            onSnapshot(q, async (snapshot) => {
                const termsListEl = document.getElementById('coach-terms-list');
                if (!termsListEl) return;

                if (snapshot.empty) {
                    termsListEl.innerHTML = `<div class="bg-gray-800 rounded-lg p-8 text-center">
                        <h3 class="text-xl font-bold">No activities have been created yet.</h3>
                        <p class="text-gray-400 mt-2">Click the "+ Create Activity" button to get started.</p>
                    </div>`;
                    return;
                }
                
                const termPromises = snapshot.docs.map(async doc => {
                    const term = { id: doc.id, ...doc.data() };
                    const regsQuery = query(collection(db, `/artifacts/${appId}/public/data/registrations`), where("termId", "==", term.id));
                    const regSnapshot = await getDocs(regsQuery);
                    term.registrations = regSnapshot.docs.map(d => ({id: d.id, ...d.data()}));
                    
                    const schedQuery = query(collection(db, `/artifacts/${appId}/public/data/schedules`), where("termId", "==", term.id));
                    const schedSnapshot = await getDocs(schedQuery);
                    term.schedule = schedSnapshot.docs.map(d => ({id: d.id, ...d.data()}));

                    return term;
                });

                const termsWithData = await Promise.all(termPromises);

                termsListEl.innerHTML = termsWithData.map(term => {
                    const spotsFilled = term.registrations.length;
                    const revenue = term.registrations.filter(r => r.paymentStatus === 'paid').reduce((sum, r) => sum + (r.finalPrice || term.price || 0), 0);
                    return `
                        <div class="bg-gray-800 p-4 rounded-lg shadow-md">
                            <details>
                                <summary class="font-bold text-lg cursor-pointer flex justify-between items-center">
                                    <span>${term.name}</span>
                                    <div class="text-right text-sm">
                                        <p class="text-white">${spotsFilled} / ${term.capacity} Registered</p>
                                        <p class="text-green-400">£${revenue.toFixed(2)} Collected</p>
                                    </div>
                                </summary>
                                <div class="details-content mt-4 pt-4 border-t border-gray-700">
                                    <div class="flex border-b border-gray-700 mb-4 overflow-x-auto hide-scrollbar">
                                        <button class="coach-term-tab py-2 px-4 font-semibold text-gray-400 flex-shrink-0" onclick="renderCoachTermTab(event, '${term.id}', 'registrations')">Registrations</button>
                                        <button class="coach-term-tab py-2 px-4 font-semibold text-gray-400 flex-shrink-0" onclick="renderCoachTermTab(event, '${term.id}', 'waitlist')">Waitlist</button>
                                        <button class="coach-term-tab py-2 px-4 font-semibold text-gray-400 flex-shrink-0" onclick="renderCoachTermTab(event, '${term.id}', 'availability')">Availability</button>
                                        <button class="coach-term-tab py-2 px-4 font-semibold text-gray-400 flex-shrink-0" onclick="renderCoachTermTab(event, '${term.id}', 'attendance')">Attendance</button>
                                        <button class="coach-term-tab py-2 px-4 font-semibold text-gray-400 flex-shrink-0" onclick="renderCoachTermTab(event, '${term.id}', 'finance')">Finance</button>
                                    </div>
                                    <div id="coach-term-content-${term.id}"></div>
                                    <div class="mt-4 text-right">
                                        <button onclick="window.handleDeleteTerm('${term.id}')" class="bg-red-600 hover:bg-red-700 text-white text-xs font-bold py-1 px-3 rounded">Delete Term</button>
                                    </div>
                                </div>
                            </details>
                        </div>
                    `;
                }).join('');
            });
        }
        
        window.renderCoachTermTab = async (e, termId, tab) => {
            const button = e.target;
            const parent = button.parentElement;
            parent.querySelectorAll('.coach-term-tab').forEach(btn => btn.classList.remove('tab-active'));
            button.classList.add('tab-active');

            const contentEl = document.getElementById(`coach-term-content-${termId}`);
            contentEl.innerHTML = `<div class="text-center p-4"><span class="text-cyan-400">Loading...</span></div>`;

            const termDoc = await getDoc(doc(db, `/artifacts/${appId}/public/data/terms`, termId));
            const term = {id: termDoc.id, ...termDoc.data()};
            
            const regsQuery = query(collection(db, `/artifacts/${appId}/public/data/registrations`), where("termId", "==", termId));
            const regsSnapshot = await getDocs(regsQuery);
            const registrations = regsSnapshot.docs.map(d => ({id: d.id, ...d.data()}));

            const schedQuery = query(collection(db, `/artifacts/${appId}/public/data/schedules`), where("termId", "==", termId));
            const schedSnapshot = await getDocs(schedQuery);
            const schedule = schedSnapshot.docs.map(d => ({id: d.id, ...d.data()}));

            if (tab === 'registrations') {
                if (registrations.length === 0) {
                    contentEl.innerHTML = '<p class="text-gray-400">No registrations yet.</p>';
                } else {
                    contentEl.innerHTML = `<ul class="space-y-2">${registrations.map(d => {
                        const bookingType = d.bookingType === 'trial' ? '<span class="text-xs bg-purple-600 text-white font-bold uppercase px-2 py-1 rounded-full ml-2">Trial</span>' : '';
                        return `<li class="bg-gray-700 p-2 rounded-md">
                                    <button class="w-full text-left" onclick="window.showRegistrationDetails('${d.id}')">
                                        <div class="flex justify-between items-center">
                                            <span>${d.childName} (${d.parentName}) ${bookingType}</span> 
                                        </div>
                                    </button>
                                </li>`
                    }).join('')}</ul>`;
                }
            } else if (tab === 'waitlist') {
                const waitlistQuery = query(collection(db, `/artifacts/${appId}/public/data/waitlist`), where("termId", "==", termId));
                onSnapshot(waitlistQuery, async (waitlistSnapshot) => {
                    const waitlist = waitlistSnapshot.docs.map(d => ({id: d.id, ...d.data()}));
                    const spotsLeft = term.capacity - registrations.length;
                    
                    if (waitlist.length === 0) {
                        contentEl.innerHTML = '<p class="text-gray-400">Waitlist is empty.</p>';
                    } else {
                        const canPromote = spotsLeft > 0;
                        contentEl.innerHTML = `
                        <p class="text-sm text-gray-300 mb-3">There are <span class="font-bold text-white">${waitlist.length}</span> on the waitlist. 
                           <span class="font-bold ${canPromote ? 'text-green-400' : 'text-red-500'}">${spotsLeft}</span> spots currently available.
                        </p>
                        <ul class="space-y-2">${waitlist.map(w => `
                            <li class="flex justify-between items-center bg-gray-700 p-2 rounded-md">
                                <span>${w.childName} (${w.parentName})</span>
                                ${canPromote ? `<button onclick="window.handlePromoteFromWaitlist('${w.id}')" class="text-xs bg-green-600 hover:bg-green-700 px-2 py-1 rounded-md font-bold transition">Promote</button>` : `<span class="text-xs text-gray-400">Term Full</span>`}
                            </li>
                        `).join('')}</ul>`;
                    }
                });
            } else if (tab === 'availability') {
                renderCoachAvailabilityView(contentEl, term, registrations, schedule);
            } else if (tab === 'attendance') {
                if (schedule.length === 0) {
                    contentEl.innerHTML = '<p class="text-gray-400">No schedule generated for this term yet.</p>';
                } else {
                    contentEl.innerHTML = `<div class="space-y-3">${schedule.map(item => {
                        const date = new Date(item.date).toLocaleDateString('en-GB', { weekday: 'short', day: 'numeric', month: 'short' });
                        const attendees = item.attendees || [];
                        return `
                            <div class="bg-gray-700 p-3 rounded-lg">
                                <p class="font-semibold flex justify-between"><span>${item.title}</span> <span>${date} at ${item.startTime}</span></p>
                                <div class="mt-2 text-xs grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
                                    ${registrations.length > 0 ? registrations.map(reg => `
                                        <label class="flex items-center space-x-2 bg-gray-600 p-2 rounded-md">
                                            <input type="checkbox" onchange="window.handleAttendance(event, '${item.id}', '${reg.id}')" ${attendees.includes(reg.id) ? 'checked' : ''} class="h-4 w-4 rounded border-gray-400 bg-gray-700 text-cyan-500 focus:ring-cyan-600">
                                            <span>${reg.childName}</span>
                                        </label>
                                    `).join('') : '<p class="text-gray-400 col-span-full">No children registered to take attendance.</p>'}
                                </div>
                            </div>
                        `;
                    }).join('')}</div>`;
                }
            } else if (tab === 'finance') {
                const paidRegistrations = registrations.filter(r => r.paymentStatus === 'paid');
                const unpaidRegistrations = registrations.filter(r => r.paymentStatus !== 'paid');
                const totalRevenue = paidRegistrations.reduce((sum, r) => sum + (r.finalPrice || term.price), 0);
                const outstandingRevenue = unpaidRegistrations.reduce((sum, r) => sum + (r.finalPrice || term.price), 0);
                contentEl.innerHTML = `
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between p-3 bg-gray-700 rounded-md"><span>Total Potential Revenue:</span> <span class="font-bold text-white">£${(totalRevenue + outstandingRevenue).toFixed(2)}</span></div>
                        <div class="flex justify-between p-3 bg-gray-700 rounded-md"><span>Collected:</span> <span class="font-bold text-green-400">£${totalRevenue.toFixed(2)}</span></div>
                        <div class="flex justify-between p-3 bg-gray-700 rounded-md"><span>Outstanding:</span> <span class="font-bold text-yellow-400">£${outstandingRevenue.toFixed(2)}</span></div>
                    </div>
                `;
            }
        }

        async function renderCoachAvailabilityView(container, term, registrations, schedule) {
            const reqQuery = query(collection(db, `/artifacts/${appId}/public/data/availability_requests`), where("termId", "==", term.id));
            onSnapshot(reqQuery, async (reqSnapshot) => {
                const requests = reqSnapshot.docs.map(d => ({id: d.id, ...d.data()}));
                
                let html = '<div class="space-y-4">';
                for (const item of schedule) {
                    const date = new Date(item.date).toLocaleDateString('en-GB', { weekday: 'short', day: 'numeric', month: 'short' });
                    const request = requests.find(r => r.scheduleId === item.id);

                    html += `<div class="bg-gray-700 p-3 rounded-lg">`;
                    html += `<div class="flex justify-between items-center">`;
                    html += `<p class="font-semibold">${item.title} - ${date}</p>`;
                    if (request) {
                         html += `<button onclick="window.viewAvailabilityDetails('${request.id}')" class="bg-blue-600 hover:bg-blue-700 text-white text-xs font-bold py-1 px-3 rounded">View Details</button>`;
                    } else {
                        html += `<button onclick="window.showRequestAvailabilityModal('${item.id}', '${term.id}')" class="bg-cyan-500 hover:bg-cyan-600 text-white text-xs font-bold py-1 px-3 rounded">+ Request Availability</button>`;
                    }
                    html += `</div>`;
                    if (request) {
                        const respQuery = query(collection(db, `/artifacts/${appId}/public/data/availability_responses`), where("requestId", "==", request.id));
                        const respSnapshot = await getDocs(respQuery);
                        html += `<p class="text-xs text-gray-400 mt-2">Status: ${respSnapshot.size} / ${registrations.length} responded. Deadline: ${new Date(request.deadline).toLocaleString('en-GB')}</p>`;
                    }
                    html += `</div>`;
                }
                html += '</div>';
                container.innerHTML = html;
            });
        }

        window.viewAvailabilityDetails = async (requestId) => {
            const reqDoc = await getDoc(doc(db, `/artifacts/${appId}/public/data/availability_requests`, requestId));
            const request = reqDoc.data();
            
            const regsQuery = query(collection(db, `/artifacts/${appId}/public/data/registrations`), where("termId", "==", request.termId));
            const regsSnapshot = await getDocs(regsQuery);
            const registrations = regsSnapshot.docs.map(d => ({id: d.id, ...d.data()}));

            const respQuery = query(collection(db, `/artifacts/${appId}/public/data/availability_responses`), where("requestId", "==", requestId));
            const respSnapshot = await getDocs(respQuery);
            const responses = respSnapshot.docs.map(d => ({id: d.id, ...d.data()}));

            const available = responses.filter(r => r.status === 'available');
            const unavailable = responses.filter(r => r.status === 'unavailable');
            const pending = registrations.filter(reg => !responses.find(r => r.registrationId === reg.id));

            const listHtml = (title, list, colorClass) => `
                <div>
                    <h4 class="font-bold text-sm ${colorClass}">${title} (${list.length})</h4>
                    <ul class="text-xs text-gray-300 list-disc list-inside mt-1">
                        ${list.length > 0 ? list.map(item => `<li>${item.childName}</li>`).join('') : '<li>None</li>'}
                    </ul>
                </div>
            `;

            const modalContent = `
                <h2 class="text-2xl font-bold mb-4 text-white">Availability Details</h2>
                <div class="space-y-4">
                    ${listHtml('✅ Available', available.map(r => registrations.find(reg => reg.id === r.registrationId)), 'text-green-400')}
                    ${listHtml('❌ Unavailable', unavailable.map(r => registrations.find(reg => reg.id === r.registrationId)), 'text-red-400')}
                    ${listHtml('⏳ Pending', pending, 'text-yellow-400')}
                </div>
            `;
            showModal(modalContent);
        }
        
        function listenForVenues() {
            const venuesQuery = query(collection(db, `/artifacts/${appId}/public/data/venues`));
            onSnapshot(venuesQuery, (snapshot) => {
                appState.venues = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const venuesListEl = document.getElementById('venues-list');
                if (!venuesListEl) return;
                if(snapshot.empty) {
                    venuesListEl.innerHTML = `<p class="text-sm text-gray-400">No venues created yet.</p>`;
                    return;
                }
                venuesListEl.innerHTML = snapshot.docs.map(doc => {
                    const venue = doc.data();
                    return `<div class="bg-gray-700 p-2 rounded-md text-sm">${venue.name} - ${venue.postcode}</div>`;
                }).join('');
            });
        }

        function listenForCoaches() {
            const coachesQuery = query(collection(db, `/artifacts/${appId}/public/data/coaches`));
            onSnapshot(coachesQuery, (snapshot) => {
                const coachesListEl = document.getElementById('coaches-list');
                if (!coachesListEl) return;
                if(snapshot.empty) {
                    coachesListEl.innerHTML = `<p class="text-sm text-gray-400">No coaches added yet.</p>`;
                    return;
                }
                coachesListEl.innerHTML = snapshot.docs.map(doc => {
                    const coach = doc.data();
                    return `<div class="bg-gray-700 p-2 rounded-md text-sm">${coach.name} (${coach.email})</div>`;
                }).join('');
            });
        }
        
        function listenForDiscounts() {
            const discountsQuery = query(collection(db, `/artifacts/${appId}/public/data/discounts`));
            onSnapshot(discountsQuery, (snapshot) => {
                appState.discounts = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
                const discountsListEl = document.getElementById('discounts-list');
                if (!discountsListEl) return;
                if(snapshot.empty) {
                    discountsListEl.innerHTML = `<p class="text-sm text-gray-400">No discount codes created yet.</p>`;
                    return;
                }
                discountsListEl.innerHTML = appState.discounts.map(d => `<div class="bg-gray-700 p-2 rounded-md text-sm flex justify-between"><span>${d.code}</span> <span>${d.percentage}% off</span></div>`).join('');
            });
        }


        // --- MODAL & FORM LOGIC ---
        function showModal(content, maxWidth = 'max-w-md') {
            const overlay = document.createElement('div');
            overlay.className = 'fixed inset-0 bg-black bg-opacity-70 flex justify-center items-center z-50 p-4 fade-in';
            overlay.innerHTML = `
                <div class="bg-gray-800 rounded-lg shadow-2xl p-6 sm:p-8 w-full ${maxWidth} relative" onclick="event.stopPropagation()">
                    <button onclick="window.closeModal()" class="absolute top-2 right-2 text-gray-400 hover:text-white text-3xl leading-none">&times;</button>
                    ${content}
                </div>
            `;
            overlay.addEventListener('click', closeModal);
            modalContainer.innerHTML = '';
            modalContainer.appendChild(overlay);
            modalContainer.classList.remove('pointer-events-none');
        }

        window.closeModal = function() {
            modalContainer.classList.add('pointer-events-none');
            modalContainer.innerHTML = '';
        }

        function showConfirmationModal(title, message, onConfirm) {
            const modalContent = `
                <h2 class="text-2xl font-bold mb-4 text-white">${title}</h2>
                <p class="text-gray-300 mb-6">${message}</p>
                <div class="flex justify-end gap-4">
                    <button onclick="window.closeModal()" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg">Cancel</button>
                    <button id="confirm-action-btn" class="bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-2 px-4 rounded-lg">Confirm</button>
                </div>
            `;
            showModal(modalContent, 'max-w-sm');
            document.getElementById('confirm-action-btn').onclick = () => {
                onConfirm();
                closeModal();
            };
        }

        window.showRequestAvailabilityModal = function(scheduleId, termId) {
            const modalContent = `
                <h2 class="text-2xl font-bold mb-6 text-white">Request Availability</h2>
                <form id="request-availability-form" class="space-y-4" data-schedule-id="${scheduleId}" data-term-id="${termId}">
                    <input name="deadline" type="datetime-local" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    <textarea name="message" placeholder="Optional message for parents..." class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500"></textarea>
                    <button type="submit" class="w-full bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-3 rounded-lg transition duration-200">Send Request</button>
                </form>
            `;
            showModal(modalContent);
            document.getElementById('request-availability-form').addEventListener('submit', handleCreateAvailabilityRequest);
        }

        window.showCreateTermModal = function() {
            const venueOptions = appState.venues.map(v => `<option value="${v.id}|${v.name}">${v.name}</option>`).join('');
            const modalContent = `
                <h2 class="text-2xl font-bold mb-6 text-white">Create New Activity</h2>
                <form id="create-term-form" class="space-y-4">
                    <select name="type" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                        <option value="term" selected>Term (Weekly recurring)</option>
                        <option value="camp">Camp (Multi-day event)</option>
                    </select>
                    <input name="name" type="text" placeholder="Activity Name (e.g., Autumn Term 2024)" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    <select name="venue" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                        <option value="" disabled selected>Select a Venue</option>
                        ${venueOptions}
                    </select>
                    <div class="grid grid-cols-2 gap-4">
                        <input name="startDate" type="text" onfocus="(this.type='date')" onblur="(this.type='text')" placeholder="Start Date" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                        <input name="endDate" type="text" onfocus="(this.type='date')" onblur="(this.type='text')" placeholder="End Date" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <select name="recurringDay" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                            <option value="" disabled selected>Day of Week</option>
                            <option value="1">Monday</option><option value="2">Tuesday</option><option value="3">Wednesday</option><option value="4">Thursday</option><option value="5">Friday</option><option value="6">Saturday</option><option value="0">Sunday</option>
                        </select>
                        <input name="startTime" type="text" onfocus="(this.type='time')" onblur="(this.type='text')" placeholder="Start Time" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    </div>
                    <select name="paymentType" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                        <option value="one-time" selected>One-Time Payment</option>
                        <option value="subscription">Monthly Subscription</option>
                    </select>
                    <textarea name="description" placeholder="Short description" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500"></textarea>
                    <input name="capacity" type="number" placeholder="Max Capacity (e.g., 20)" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    <input name="price" type="number" placeholder="Price (£)" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    <div class="flex items-center">
                        <input type="checkbox" id="allowTrials" name="allowTrials" class="h-4 w-4 rounded border-gray-300 text-cyan-600 focus:ring-cyan-500">
                        <label for="allowTrials" class="ml-2 text-sm">Allow trial sessions for this activity?</label>
                    </div>
                    <button type="submit" class="w-full bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-3 rounded-lg transition duration-200">Create & Generate Schedule</button>
                </form>
            `;
            showModal(modalContent);
            document.getElementById('create-term-form').addEventListener('submit', handleCreateTerm);
        }

        window.showBookingModal = async function(termId) {
            const term = appState.termsData.find(t => t.id === termId);
            if (!term) { showModal(`<p>Could not find term data.</p>`); return; }
            
            const isFull = term.spotsLeft <= 0;
            const priceText = term.paymentType === 'subscription' ? `£${term.price}/month` : `£${term.price}`;

            const modalContent = `
                <h2 class="text-2xl font-bold mb-2 text-white">${isFull ? 'Join Waitlist for' : 'Book a Spot in'} ${term.name}</h2>
                <p class="text-gray-400 mb-2">${isFull ? 'This activity is full, but you can join the waitlist.' : `There are ${term.spotsLeft} spots left.`}</p>
                <p id="price-display" class="text-lg font-bold text-cyan-400 mb-6">Price: ${priceText}</p>
                <form id="booking-form" class="space-y-4" 
                    data-term-id="${termId}" 
                    data-term-price="${term.price}"
                    data-current-price="${term.price}"
                    data-is-full="${isFull}"
                    data-term-name="${term.name}"
                    data-allow-trials="${term.allowTrials}"
                >
                    <input name="parentName" type="text" placeholder="Your Full Name" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    <input name="childName" type="text" placeholder="Child's Full Name" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    
                    <div id="sibling-section" class="hidden space-y-2 border-t border-gray-700 pt-4">
                        <p class="text-sm font-semibold text-gray-300">Sibling Details</p>
                        <input name="childName2" type="text" placeholder="Sibling's Full Name" class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    </div>

                    ${!isFull ? `
                    <div class="flex items-center">
                        <input type="checkbox" id="addSibling" onchange="window.updateBookingPrice()" class="h-4 w-4 rounded border-gray-400 bg-gray-700 text-cyan-500 focus:ring-cyan-600">
                        <label for="addSibling" class="ml-2 text-sm">Add another child? (10% sibling discount)</label>
                    </div>
                    <div class="flex items-center gap-2">
                        <input name="discountCode" id="discount-code-input" type="text" placeholder="Discount Code" class="flex-grow bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                        <button type="button" onclick="window.applyDiscount()" class="bg-gray-600 hover:bg-gray-500 px-4 py-3 rounded-lg text-sm font-semibold transition">Apply</button>
                    </div>
                    <p id="discount-message" class="text-xs text-green-400 h-4"></p>
                    ` : ''}

                    <div class="flex flex-col sm:flex-row gap-2 mt-4 pt-4 border-t border-gray-700">
                        ${!isFull ? `<button type="submit" class="w-full text-center bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-3 rounded-lg transition duration-200">Confirm Booking</button>` : ''}
                        ${isFull ? `<button type="submit" class="w-full bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-3 rounded-lg transition duration-200">Join Waitlist</button>` : ''}
                        ${term.allowTrials && !isFull ? `<button type="button" onclick="window.handleTrialBooking()" class="w-full sm:w-auto bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-4 rounded-lg transition duration-200">Book Trial (£${(term.price/10).toFixed(2)})</button>` : ''}
                    </div>
                </form>
            `;
            showModal(modalContent);
            document.getElementById('booking-form').addEventListener('submit', handleUniversalBookingSubmit);
        }

        window.updateBookingPrice = function() {
            const form = document.getElementById('booking-form');
            const priceDisplay = document.getElementById('price-display');
            const addSiblingCheckbox = document.getElementById('addSibling');
            const basePrice = parseFloat(form.dataset.termPrice);
            
            let finalPrice = basePrice;
            if (addSiblingCheckbox.checked) {
                finalPrice = (basePrice * 2) * 0.9; // 10% discount for two
                document.getElementById('sibling-section').classList.remove('hidden');
                document.querySelector('[name="childName2"]').required = true;
            } else {
                document.getElementById('sibling-section').classList.add('hidden');
                 document.querySelector('[name="childName2"]').required = false;
            }
            priceDisplay.textContent = `Price: £${finalPrice.toFixed(2)}`;
            form.dataset.currentPrice = finalPrice;
            // Clear discount when sibling status changes
            form.dataset.discountApplied = "0";
            form.dataset.discountCode = "";
            document.getElementById('discount-message').textContent = '';
            document.getElementById('discount-code-input').value = '';
        }

        window.applyDiscount = function() {
            const form = document.getElementById('booking-form');
            const priceDisplay = document.getElementById('price-display');
            const discountInput = document.getElementById('discount-code-input');
            const discountMessage = document.getElementById('discount-message');
            const code = discountInput.value.toUpperCase();
            
            // Re-calculate base price (with sibling discount if applied) before applying code
            window.updateBookingPrice();
            
            const discount = appState.discounts.find(d => d.code === code);
            
            if (!discount) {
                discountMessage.textContent = 'Invalid discount code.';
                discountMessage.className = 'text-xs text-red-400 h-4';
                return;
            }

            const currentPrice = parseFloat(form.dataset.currentPrice);
            const discountAmount = currentPrice * (discount.percentage / 100);
            const finalPrice = currentPrice - discountAmount;

            priceDisplay.textContent = `Price: £${finalPrice.toFixed(2)}`;
            form.dataset.currentPrice = finalPrice;
            form.dataset.discountApplied = discount.percentage;
            form.dataset.discountCode = code;
            discountMessage.textContent = `${discount.percentage}% discount applied!`;
            discountMessage.className = 'text-xs text-green-400 h-4';
        }

        window.showCreateVenueModal = function() {
            const modalContent = `
                <h2 class="text-2xl font-bold mb-6 text-white">Create New Venue</h2>
                <form id="create-venue-form" class="space-y-4">
                    <input name="name" type="text" placeholder="Venue Name (e.g., Isleham Community Hall)" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    <input name="postcode" type="text" placeholder="Postcode (e.g., CB7 5RP)" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    <button type="submit" class="w-full bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-3 rounded-lg transition duration-200">Save Venue</button>
                </form>
            `;
            showModal(modalContent);
            document.getElementById('create-venue-form').addEventListener('submit', handleCreateVenue);
        }
        
        window.showCreateCoachModal = function() {
            const modalContent = `
                <h2 class="text-2xl font-bold mb-6 text-white">Add New Coach/Staff</h2>
                <form id="create-coach-form" class="space-y-4">
                    <input name="name" type="text" placeholder="Full Name" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    <input name="email" type="email" placeholder="Email Address" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    <input name="phone" type="tel" placeholder="Phone Number" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    <button type="submit" class="w-full bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-3 rounded-lg transition duration-200">Save Coach</button>
                </form>
            `;
            showModal(modalContent);
            document.getElementById('create-coach-form').addEventListener('submit', handleCreateCoach);
        }

        window.showCreateDiscountModal = function() {
             const modalContent = `
                <h2 class="text-2xl font-bold mb-6 text-white">Create Discount Code</h2>
                <form id="create-discount-form" class="space-y-4">
                    <input name="code" type="text" placeholder="Code (e.g., EARLYBIRD15)" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    <input name="percentage" type="number" placeholder="Percentage Off (e.g., 15)" required class="w-full bg-gray-700 text-white p-3 rounded-lg border border-gray-600 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                    <button type="submit" class="w-full bg-cyan-500 hover:bg-cyan-600 text-white font-bold py-3 rounded-lg transition duration-200">Save Code</button>
                </form>
            `;
            showModal(modalContent);
            document.getElementById('create-discount-form').addEventListener('submit', handleCreateDiscount);
        }

        // --- HANDLERS ---
        async function handleCreateAvailabilityRequest(e) { /* ... existing code ... */ }
        window.handleAvailabilityResponse = async function(requestId, registrationId, status) { /* ... existing code ... */ }

        async function handleCreateTerm(e) {
            e.preventDefault();
            const formData = new FormData(e.target);
            const [venueId, venueName] = formData.get('venue').split('|');
            const newTerm = {
                name: formData.get('name'),
                venueId: venueId,
                venueName: venueName,
                startDate: formData.get('startDate'),
                endDate: formData.get('endDate'),
                recurringDay: parseInt(formData.get('recurringDay')),
                startTime: formData.get('startTime'),
                description: formData.get('description'),
                capacity: parseInt(formData.get('capacity'), 10),
                price: parseFloat(formData.get('price')),
                coachId: appState.userId,
                createdAt: serverTimestamp(),
                type: formData.get('type'),
                paymentType: formData.get('paymentType'),
                allowTrials: formData.get('allowTrials') === 'on',
            };
            try {
                const termRef = await addDoc(collection(db, `/artifacts/${appId}/public/data/terms`), newTerm);
                
                const batch = writeBatch(db);
                let currentDate = new Date(newTerm.startDate);
                const endDate = new Date(newTerm.endDate);
                let weekNumber = 1;

                while(currentDate <= endDate) {
                    if (newTerm.type === 'camp' || currentDate.getDay() === newTerm.recurringDay) {
                        const scheduleItem = {
                            termId: termRef.id,
                            title: newTerm.type === 'camp' ? `Day ${weekNumber}` : `Week ${weekNumber}`,
                            date: currentDate.toISOString().split('T')[0],
                            startTime: newTerm.startTime,
                            attendees: [],
                            createdAt: serverTimestamp()
                        };
                        const scheduleRef = doc(collection(db, `/artifacts/${appId}/public/data/schedules`));
                        batch.set(scheduleRef, scheduleItem);
                        weekNumber++;
                    }
                    currentDate.setDate(currentDate.getDate() + 1);
                }
                await batch.commit();
                closeModal();

            } catch (error) {
                console.error("Error creating term: ", error);
                showModal(`<p>Could not create the term. Please try again.</p>`);
            }
        }

        async function handleUniversalBookingSubmit(e) {
            e.preventDefault();
            const form = e.target;
            const isFull = form.dataset.isFull === 'true';

            if (isFull) {
                await handleWaitlistJoin(form);
            } else {
                await handleBookingSubmit(form);
            }
        }

        async function handleBookingSubmit(form) {
            const termId = form.dataset.termId;
            const termName = form.dataset.termName;
            
            try {
                await runTransaction(db, async (transaction) => {
                    const termRef = doc(db, `/artifacts/${appId}/public/data/terms`, termId);
                    const termDoc = await transaction.get(termRef);
                    if (!termDoc.exists()) {
                        throw "Term does not exist.";
                    }
                    const termData = termDoc.data();

                    const regsQuery = query(collection(db, `/artifacts/${appId}/public/data/registrations`), where("termId", "==", termId));
                    const regsSnapshot = await getDocs(regsQuery); // Note: getDocs is not transactional
                    
                    const addSibling = form.elements.addSibling && form.elements.addSibling.checked;
                    const spotsNeeded = addSibling ? 2 : 1;

                    if (regsSnapshot.size + spotsNeeded > termData.capacity) {
                        throw "Sorry, there are not enough spots available for this booking.";
                    }

                    const formData = new FormData(form);
                    const finalPrice = parseFloat(form.dataset.currentPrice);
                    const pricePerChild = addSibling ? finalPrice / 2 : finalPrice;

                    const baseRegistration = {
                        termId: termId,
                        parentName: formData.get('parentName'),
                        parentId: appState.userId,
                        paymentStatus: 'unpaid',
                        registrationDate: serverTimestamp(),
                        bookingType: 'term',
                        finalPrice: pricePerChild,
                        discountCode: form.dataset.discountCode || "",
                        discountApplied: parseFloat(form.dataset.discountApplied) || 0,
                    };

                    const reg1 = { ...baseRegistration, childName: formData.get('childName') };
                    const regRef1 = doc(collection(db, `/artifacts/${appId}/public/data/registrations`));
                    transaction.set(regRef1, reg1);

                    if (addSibling) {
                        const reg2 = { ...baseRegistration, childName: formData.get('childName2') };
                        const regRef2 = doc(collection(db, `/artifacts/${appId}/public/data/registrations`));
                        transaction.set(regRef2, reg2);
                    }
                });

                showModal(`<h2 class="text-2xl font-bold mb-4 text-cyan-400">Booking Confirmed!</h2><p class="text-gray-300">Your spot in ${termName} is confirmed. Please go to "My Bookings" to complete payment.</p>`);

            } catch (error) {
                console.error("Error creating registration: ", error);
                showModal(`<h2 class="text-2xl font-bold mb-4 text-red-500">Booking Failed</h2><p class="text-gray-300">${error}</p>`);
            }
        }

        window.handleTrialBooking = async function() {
            const form = document.getElementById('booking-form');
            const formData = new FormData(form);
            const termId = form.dataset.termId;
            const termName = form.dataset.termName;
            const termPrice = parseFloat(form.dataset.termPrice);

            const newRegistration = {
                termId: termId,
                childName: formData.get('childName'),
                parentName: formData.get('parentName'),
                parentId: appState.userId,
                paymentStatus: 'unpaid',
                registrationDate: serverTimestamp(),
                bookingType: 'trial',
                finalPrice: parseFloat((termPrice / 10).toFixed(2))
            };
            try {
                await addDoc(collection(db, `/artifacts/${appId}/public/data/registrations`), newRegistration);
                showModal(`<h2 class="text-2xl font-bold mb-4 text-cyan-400">Trial Booked!</h2><p class="text-gray-300">${newRegistration.childName} is now booked for a trial session in ${termName}. Please go to "My Bookings" to complete payment.</p>`);
            } catch (error) {
                console.error("Error creating trial registration: ", error);
                showModal(`<p>Could not complete trial booking. Please try again.</p>`);
            }
        }
        
        window.handleAttendance = async function(e, scheduleId, registrationId) { /* ... existing code ... */ }
        window.handlePayment = async function(registrationId) { /* ... existing code ... */ }
        window.handleDeleteTerm = async function(termId) { /* ... existing code ... */ }
        async function handleCreateVenue(e) { /* ... existing code ... */ }
        async function handleCreateCoach(e) { /* ... existing code ... */ }
        async function handleCreateDiscount(e) { /* ... existing code ... */ }
        
        async function handleWaitlistJoin(form) {
            const formData = new FormData(form);
            const termId = form.dataset.termId;
            const termName = form.dataset.termName;
            const waitlistEntry = {
                termId: termId,
                childName: formData.get('childName'),
                parentName: formData.get('parentName'),
                parentId: appState.userId,
                addedAt: serverTimestamp()
            };
            try {
                await addDoc(collection(db, `/artifacts/${appId}/public/data/waitlist`), waitlistEntry);
                showModal(`<h2 class="text-2xl font-bold mb-4 text-cyan-400">You're on the list!</h2><p class="text-gray-300">${waitlistEntry.childName} has been added to the waitlist for ${termName}. We'll notify you if a spot opens up.</p>`);
            } catch (e) {
                console.error("Error joining waitlist:", e);
                showModal('<p>Could not join waitlist. Please try again.</p>');
            }
        }

        window.handlePromoteFromWaitlist = async function(waitlistId) {
            showConfirmationModal(
                "Promote from Waitlist?",
                "This will move the child from the waitlist to a full registration and create an unpaid booking. Are you sure?",
                async () => {
                    const waitlistRef = doc(db, `/artifacts/${appId}/public/data/waitlist`, waitlistId);
                    try {
                        await runTransaction(db, async (transaction) => {
                            const waitlistDoc = await transaction.get(waitlistRef);
                            if (!waitlistDoc.exists()) {
                                throw "Waitlist entry not found.";
                            }
                            const waitlistData = waitlistDoc.data();

                            const termRef = doc(db, `/artifacts/${appId}/public/data/terms`, waitlistData.termId);
                            const termDoc = await transaction.get(termRef);
                            if (!termDoc.exists()) {
                                throw "Term does not exist.";
                            }
                            const termData = termDoc.data();

                            const newRegistration = {
                                termId: waitlistData.termId,
                                childName: waitlistData.childName,
                                parentName: waitlistData.parentName,
                                parentId: waitlistData.parentId,
                                paymentStatus: 'unpaid',
                                registrationDate: serverTimestamp(),
                                bookingType: 'term',
                                finalPrice: termData.price 
                            };
                            
                            const newRegRef = doc(collection(db, `/artifacts/${appId}/public/data/registrations`));
                            transaction.set(newRegRef, newRegistration);
                            transaction.delete(waitlistRef);
                        });
                        showModal(`<p>Promotion successful! A booking has been created.</p>`);
                    } catch (error) {
                        console.error("Error promoting from waitlist:", error);
                        showModal(`<p>Promotion failed: ${error}</p>`);
                    }
                }
            );
        }

        // --- DEMO DATA SEEDING ---
        async function seedDemoData() {
            const seededDocRef = doc(db, `/artifacts/${appId}/public/data/seeded`, 'status');
            const seededDoc = await getDoc(seededDocRef);

            if (seededDoc.exists()) {
                console.log("Demo data already seeded.");
                return;
            }

            console.log("Seeding enhanced demo data for July 13, 2025...");
            const batch = writeBatch(db);

            // Users
            const coach1Id = 'coach_mike_123';
            const coach2Id = 'coach_sarah_456';
            const parent1Id = appState.userId; // Current user is a parent
            const parent2Id = 'parent_jane_789';

            // Coaches
            batch.set(doc(collection(db, `/artifacts/${appId}/public/data/coaches`)), { name: "Mike Wilson", email: "<EMAIL>", phone: "01234567890", clubId: coach1Id });
            batch.set(doc(collection(db, `/artifacts/${appId}/public/data/coaches`)), { name: "Sarah Smith", email: "<EMAIL>", phone: "09876543210", clubId: coach2Id });

            // Venues
            const venue1Ref = doc(collection(db, `/artifacts/${appId}/public/data/venues`));
            batch.set(venue1Ref, { name: "Isleham Community Hall", postcode: "CB7 5RP", clubId: coach1Id });
            const venue2Ref = doc(collection(db, `/artifacts/${appId}/public/data/venues`));
            batch.set(venue2Ref, { name: "Fordham Playing Fields", postcode: "CB7 5NQ", clubId: coach1Id });

            // Discounts
            const discount1Ref = doc(collection(db, `/artifacts/${appId}/public/data/discounts`));
            batch.set(discount1Ref, { code: "EARLYBIRD10", percentage: 10, clubId: coach1Id });
            const discount2Ref = doc(collection(db, `/artifacts/${appId}/public/data/discounts`));
            batch.set(discount2Ref, { code: "SUMMER20", percentage: 20, clubId: coach2Id });

            // Term 1: In-Progress Soccer Term
            const term1Ref = doc(collection(db, `/artifacts/${appId}/public/data/terms`));
            const term1Data = { name: "Summer Soccer Term (In-Progress)", description: "Weekly soccer skills for ages 7-10.", venueId: venue1Ref.id, venueName: "Isleham Community Hall", startDate: "2025-06-02", endDate: "2025-08-11", recurringDay: 1, startTime: "17:00", capacity: 12, price: 120, coachId: coach1Id, type: "term", paymentType: "one-time", allowTrials: true };
            batch.set(term1Ref, term1Data);
            // Registrations for Term 1
            const regsTerm1 = [
                { childName: "Leo Smith", parentName: "John Smith", parentId: parent1Id, paymentStatus: "paid", finalPrice: 108, bookingType: 'term', discountCode: 'EARLYBIRD10', discountApplied: 10 }, // Sibling 1
                { childName: "Lily Smith", parentName: "John Smith", parentId: parent1Id, paymentStatus: "paid", finalPrice: 108, bookingType: 'term', discountCode: 'EARLYBIRD10', discountApplied: 10 }, // Sibling 2
                { childName: "Mia Jones", parentName: "Sarah Jones", parentId: parent2Id, paymentStatus: "unpaid", finalPrice: 120, bookingType: 'term' },
                { childName: "Noah Williams", parentName: "David Williams", parentId: "parent_david_111", paymentStatus: "paid", finalPrice: 12, bookingType: 'trial' },
            ];
            regsTerm1.forEach(reg => {
                const regRef = doc(collection(db, `/artifacts/${appId}/public/data/registrations`));
                batch.set(regRef, { ...reg, termId: term1Ref.id });
            });

            // Term 2: Full Gymnastics Term with Waitlist
            const term2Ref = doc(collection(db, `/artifacts/${appId}/public/data/terms`));
            const term2Data = { name: "Advanced Gymnastics (Full)", description: "For experienced gymnasts.", venueId: venue2Ref.id, venueName: "Fordham Playing Fields", startDate: "2025-06-04", endDate: "2025-08-13", recurringDay: 3, startTime: "18:00", capacity: 2, price: 150, coachId: coach2Id, type: "term", paymentType: "subscription", allowTrials: false };
            batch.set(term2Ref, term2Data);
            // Registrations for Term 2 (Full)
            const regsTerm2 = [
                { childName: "Olivia Brown", parentName: "Emily Brown", parentId: "parent_emily_222", paymentStatus: "paid", finalPrice: 150, bookingType: 'term' },
                { childName: "Arthur Miller", parentName: "James Miller", parentId: "parent_james_333", paymentStatus: "paid", finalPrice: 150, bookingType: 'term' },
            ];
            regsTerm2.forEach(reg => {
                const regRef = doc(collection(db, `/artifacts/${appId}/public/data/registrations`));
                batch.set(regRef, { ...reg, termId: term2Ref.id });
            });
            // Waitlist for Term 2
            const waitlistTerm2 = [
                { childName: "Freya Wilson", parentName: "Chloe Wilson", parentId: parent1Id }, // Current user on waitlist
                { childName: "Oscar Taylor", parentName: "Daniel Taylor", parentId: parent2Id },
                { childName: "Isla Evans", parentName: "Megan Evans", parentId: "parent_megan_444" },
            ];
            waitlistTerm2.forEach(w => {
                 const waitlistRef = doc(collection(db, `/artifacts/${appId}/public/data/waitlist`));
                 batch.set(waitlistRef, { ...w, termId: term2Ref.id });
            });

            // Term 3: Upcoming Summer Camp
            const term3Ref = doc(collection(db, `/artifacts/${appId}/public/data/terms`));
            const term3Data = { name: "Summer Soccer School (Upcoming)", description: "A full week of soccer fun and games.", venueId: venue1Ref.id, venueName: "Isleham Community Hall", startDate: "2025-08-04", endDate: "2025-08-08", recurringDay: 1, startTime: "09:00", capacity: 20, price: 100, coachId: coach1Id, type: "camp", paymentType: "one-time", allowTrials: false };
            batch.set(term3Ref, term3Data);

            await batch.commit();
            await setDoc(seededDocRef, { seededOn: serverTimestamp() });
            console.log("Demo data seeded successfully.");
        }


        // --- EVENT LISTENERS ---
        coachModeToggle.addEventListener('change', (e) => {
            appState.isCoachMode = e.target.checked;
            renderApp();
        });

        // --- INITIALIZATION ---
        authenticateUser();

    </script>
</body>
</html>
