# Team Creation and Editing Documentation

## Overview

This document describes the functionality and behavior of the team creation and editing features in the SHOT application. These features allow users to create new teams and modify existing teams based on their roles and permissions.

## Team Creation

### Who Can Create Teams

- **Club Administrators**: Users with the `club_admin` role can create teams for clubs they administer.
- **Coaches**: Users with the `coach` role may create teams depending on their specific permissions.
- **Super Admins**: Users with the `superadmin` role can create teams for any club.

### How Teams Are Created

Teams are created through a modal form that appears when a user with appropriate permissions initiates team creation, typically from club management pages.

#### Required Fields
- Team name
- Age group
- Sport type (default is football/soccer)

#### Optional Fields
- Description
- Primary color
- Secondary color
- Home location

#### Creation Process
1. User fills out the form in the `CreateTeamShadow` component
2. On submission, the `TeamService.createTeam` method is called with the form data
3. A UUID is generated for the `team_id` if not provided
4. The team is inserted into the "teams" table in Supabase
5. The newly created team appears in the club's team list
6. After creation, the user can add coaches and players to the team

#### Default Values
- `is_active`: true
- `status`: "active"
- `created_by`: Current user's ID
- `created_at` and `updated_at`: Current timestamp

## Team Editing

### Who Can Edit Teams

- **Team Creators**: Users who originally created the team.
- **Team Coaches**: Users who are assigned as coaches to the team with an active status.
- **Club Administrators**: Administrators of the club that the team belongs to.
- **Super Admins**: Users with the `superadmin` role.

### How Teams Are Edited

Teams are edited through a dedicated `EditTeamShadow` page that loads existing team data and displays a form pre-filled with current values.

#### Editable Fields
- Team name
- Age group
- Sport type
- Team status (active, inactive, archived)
- Team visibility (is_active flag)
- Primary and secondary colors
- Home location
- Description

#### Editing Process
1. The `EditTeamShadow` component loads the team data using `TeamService.getTeamById()`
2. User modifies fields in the form
3. On submission, the `TeamService.updateTeam` method is called with the modified data
4. The team record is updated in the "teams" table in Supabase
5. A success message confirms the update, and the user can return to the team page

#### Update Tracking
- The `updated_at` field is automatically updated to the current timestamp

## Implementation Details

### Team Model

```typescript
interface Team {
  team_id: string;                // UUID
  team_name: string;              // Required
  age_group: string;              // Required
  club_id: string;                // Required - Foreign key to clubs table
  sport_type: string;             // Required
  logo_url?: string;              // Optional
  created_at: string;             // Timestamp
  updated_at: string;             // Timestamp
  created_by: string;             // User ID of creator
  is_active: boolean;             // Visibility flag
  description?: string;           // Optional
  color_primary?: string;         // Optional
  color_secondary?: string;       // Optional
  home_location?: string;         // Optional
  season?: string;                // Optional
  status: TeamStatus;             // 'active' | 'inactive' | 'archived'
  coaches?: CoachAssignment[];    // Optional - Related coaches
  last_activity?: string;         // Optional - Timestamp
}
```

### Database Relationships

- Teams belong to Clubs (club_id foreign key)
- Teams can have multiple Coaches (through team_coaches table)
- Teams can have multiple Players (through team_members table)
- Teams can have multiple Seasons (through team_seasons table)

### Access Control

Access is managed through Row Level Security (RLS) in Supabase. The system checks multiple paths for access:

1. Team creator access
2. Coach assignment access
3. Club administrator access
4. Super admin access

The `TeamService` methods implement fallback strategies to handle different access paths and ensure users can only access teams they're authorized to manage.

## Shadow DOM Implementation

The new implementation uses Shadow DOM components from the design system:

- `ShadowTextInput` - For text fields
- `ShadowSelect` - For dropdown selectors
- `ShadowTextarea` - For multi-line text input
- `ShadowCheckbox` - For boolean toggles
- `ShadowFormGroup` - For grouping form elements
- `ShadowSportSelector` - For sport selection
- `ShadowButton` - For action buttons
- `ShadowNotificationModal` - For success/error messages
- `ShadowModal` - For create team modal and loading states

This implementation:
1. Replaces all Ionic components with Shadow DOM components
2. Maintains all existing functionality and validation
3. Follows the SHOT brand styling through Tailwind config
4. Uses Shadow DOM for component styling
5. Implements proper form validation and error handling

## Role-Based Access

User roles in the application include:
- `superadmin` - System administrators
- `club_admin` - Club administrators
- `coach` - Team coaches
- `player` - Team players
- `parent` - Player parents
- `admin` - Administrative access

The current role system allows users to switch between available roles, with permissions cascading based on role hierarchy. Team creation and editing respects these role restrictions to ensure only authorized users can perform these actions.
