// CreateTeamShadow.tsx - Refactored Create Team component using Shadow DOM components
import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';
import { useUserContext } from '../../../contexts/UserContext';
import { TeamService } from '../../../services/TeamService';
import { TeamCreateInput } from '../../../models/TeamManagementModels';

// Shadow DOM components
import ShadowTextInput from '../../../components/shadow/form/ShadowTextInput';
import ShadowSelect from '../../../components/shadow/form/ShadowSelect';
import ShadowTextarea from '../../../components/shadow/form/ShadowTextarea';
import ShadowFormGroup from '../../../components/shadow/form/ShadowFormGroup';
import ShadowSportSelector from '../../../components/shadow/form/ShadowSportSelector';
import ShadowButton from '../../../components/shadow/ShadowButton';
import ShadowNotificationModal from '../../../components/shadow/ShadowNotificationModal';
import ShadowModal from '../../../components/shadow/ShadowModal';

// Lucide React icons
import { Save, XCircle, Users, Calendar, MapPin, Palette } from 'lucide-react';

interface CreateTeamShadowProps {
  isOpen: boolean;
  onDismiss: () => void;
  onSuccess: (teamId: string) => void;
  clubId: string;
}

const CreateTeamShadow: React.FC<CreateTeamShadowProps> = ({
  isOpen,
  onDismiss,
  onSuccess,
  clubId
}) => {
  const history = useHistory();
  const { user } = useUserContext();
  
  // UI state
  const [saving, setSaving] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    teamName: '',
    ageGroup: '',
    sportType: 'football',
    description: '',
    colorPrimary: '',
    colorSecondary: '',
    homeLocation: '',
  });

  // Form validation state
  const [errors, setErrors] = useState({
    teamName: '',
    ageGroup: ''
  });

  // Validation function
  const validateForm = () => {
    const newErrors = {
      teamName: !formData.teamName.trim() ? 'Team name is required' : '',
      ageGroup: !formData.ageGroup ? 'Age group is required' : ''
    };
    
    setErrors(newErrors);
    
    // Form is valid if no errors
    return !Object.values(newErrors).some(error => error !== '');
  };

  // Handle input changes for all input types
  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error on change
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Handle text input changes from ShadowTextInput
  const handleTextInputChange = (e: any) => {
    const { name, value } = e.detail || {};
    if (name) {
      handleChange(name, value);
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      setErrorMessage('Please complete all required fields');
      return;
    }
    
    if (!user) {
      setErrorMessage('You must be logged in to create a team');
      return;
    }
    
    setSaving(true);
    try {
      // Prepare team data for creation
      const teamData: TeamCreateInput = {
        team_name: formData.teamName,
        age_group: formData.ageGroup,
        sport_type: formData.sportType,
        club_id: clubId,
        created_by: user.id,
        description: formData.description || undefined,
        color_primary: formData.colorPrimary || undefined,
        color_secondary: formData.colorSecondary || undefined,
        home_location: formData.homeLocation || undefined,
        status: 'active',
        is_active: true
      };

      console.log('Creating team with data:', teamData);
      
      // Call the create team service
      const result = await TeamService.createTeam(teamData);
      console.log('Team creation result:', result);
      
      // Reset form
      setFormData({
        teamName: '',
        ageGroup: '',
        sportType: 'football',
        description: '',
        colorPrimary: '',
        colorSecondary: '',
        homeLocation: '',
      });
      
      // Notify parent component of success
      onSuccess(result.team_id);
      
    } catch (err) {
      console.error('Error creating team:', err);
      setErrorMessage(`Failed to create team: ${err.message || 'Unknown error'}`);
    } finally {
      setSaving(false);
    }
  };

  const handleDismiss = () => {
    // Reset form
    setFormData({
      teamName: '',
      ageGroup: '',
      sportType: 'football',
      description: '',
      colorPrimary: '',
      colorSecondary: '',
      homeLocation: '',
    });
    setErrors({
      teamName: '',
      ageGroup: ''
    });
    setErrorMessage(null);
    
    // Dismiss modal
    onDismiss();
  };

  if (!isOpen) return null;

  return (
    <ShadowModal
      isOpen={isOpen}
      onClose={handleDismiss}
      title="Create New Team"
      showCloseButton={true}
      size="large"
    >
      <div className="p-4">
        {/* Title Section */}
        <ShadowFormGroup>
          <div className="form-description text-gray-300 mb-4">
            Set up a new team for your club. You can add coaches and players after creation.
            Required fields are marked with an asterisk (*).
          </div>
        </ShadowFormGroup>
        
        {/* Sport Type */}
        <ShadowFormGroup legend="Sport Type">
          <ShadowSportSelector
            value={formData.sportType}
            onChange={(value) => handleChange('sportType', value)}
            label="Sport Type"
            required
          />
        </ShadowFormGroup>
        
        {/* Team Information */}
        <ShadowFormGroup legend="Team Information">
          {/* Team Name */}
          <ShadowTextInput
            label="Team Name"
            name="teamName"
            value={formData.teamName}
            onChange={handleTextInputChange}
            placeholder="e.g., Tigers U12"
            required={true}
            error={errors.teamName}
            icon="users"
          />
          
          {/* Age Group */}
          <ShadowSelect
            label="Age Group"
            value={formData.ageGroup}
            onChange={(e) => handleChange('ageGroup', e.detail.value)}
            options={[
              { value: 'U6', label: 'U6 - Under 6 Years' },
              { value: 'U8', label: 'U8 - Under 8 Years' },
              { value: 'U10', label: 'U10 - Under 10 Years' },
              { value: 'U12', label: 'U12 - Under 12 Years' },
              { value: 'U14', label: 'U14 - Under 14 Years' },
              { value: 'U16', label: 'U16 - Under 16 Years' },
              { value: 'U18', label: 'U18 - Under 18 Years' },
              { value: 'Adult', label: 'Adult - 18+ Years' }
            ]}
            required={true}
            error={errors.ageGroup}
          />
          
          {/* Primary Color */}
          <ShadowTextInput
            label="Primary Color"
            name="colorPrimary"
            value={formData.colorPrimary}
            onChange={handleTextInputChange}
            placeholder="e.g. #FF5733 or Blue"
            icon="palette"
          />
          
          {/* Secondary Color */}
          <ShadowTextInput
            label="Secondary Color"
            name="colorSecondary"
            value={formData.colorSecondary}
            onChange={handleTextInputChange}
            placeholder="e.g. #33FF57 or White"
            icon="palette"
          />
          
          {/* Home Location */}
          <ShadowTextInput
            label="Home Location"
            name="homeLocation"
            value={formData.homeLocation}
            onChange={handleTextInputChange}
            placeholder="Team's home venue or location"
            icon="map-pin"
          />
          
          {/* Description */}
          <ShadowTextarea
            label="Description"
            value={formData.description}
            onChange={(value) => handleChange('description', value)}
            placeholder="Tell us about this team..."
            rows={3}
          />
        </ShadowFormGroup>
        
        {/* Action Buttons */}
        <div className="flex justify-between mt-6">
          <ShadowButton
            onClick={handleDismiss}
            variant="outline"
            icon={<XCircle size={18} />}
            label="Cancel"
          />
          
          <ShadowButton
            onClick={handleSubmit}
            disabled={saving}
            loading={saving}
            variant="primary"
            icon={<Save size={18} />}
            label="Create Team"
          />
        </div>
        
        {/* Error Modal */}
        {errorMessage && (
          <ShadowNotificationModal
            isOpen={!!errorMessage}
            onClose={() => setErrorMessage(null)}
            title="Error"
            message={errorMessage}
            type="error"
            primaryButtonText="OK"
            onPrimaryButtonClick={() => setErrorMessage(null)}
          />
        )}
      </div>
    </ShadowModal>
  );
};

export default CreateTeamShadow;
