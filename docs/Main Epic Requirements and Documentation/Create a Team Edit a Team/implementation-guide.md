# Implementation Guide for Team Management with Shadow DOM Components

This guide provides instructions for implementing the refactored team management components using Shadow DOM and integrating player components from the design system.

## Components Overview

I've created three main components for you:

1. **CreateTeamShadow** - A modal form for creating new teams
2. **EditTeamShadow** - A page for editing existing teams
3. **TeamPlayersView** - An example implementation showing how to integrate player components

These components use the Shadow DOM pattern as specified, with no Ionic components and following the SHOT brand styling through Tailwind.

## Implementation Steps

### 1. Replace Current Team Forms with Shadow DOM Versions

#### For EditTeam:

1. Create a new file: `/src/pages/section/Coach/EditTeamShadow.tsx`
2. Copy the contents of the `EditTeamShadow` component from the artifact
3. Update the routes to use this new component:

```typescript
// In your routes file
import EditTeamShadow from './pages/section/Coach/EditTeamShadow';

// Replace the old route
<Route path="/coach/club/:clubId/team/:teamId/edit" component={EditTeam} />

// With the new Shadow DOM version
<Route path="/coach/club/:clubId/team/:teamId/edit" component={EditTeamShadow} />
```

#### For CreateTeam:

1. Create a new file: `/src/pages/section/Coach/supporting/ClubManagement/components/CreateTeamShadow.tsx`
2. Copy the contents of the `CreateTeamShadow` component from the artifact
3. Replace the current AddTeamModal implementation with this new version

### 2. Integrate Player Components into Team Pages

1. Create a new file: `/src/pages/section/Coach/TeamPlayersView.tsx`
2. Copy the contents of the `TeamPlayersView` component from the artifact
3. Integrate this component into your team details page:

```tsx
import TeamPlayersView from './TeamPlayersView';

// In your team details page
<div className="team-details">
  <h1>{team.team_name}</h1>
  
  {/* Other team details */}
  
  {/* Players section */}
  <TeamPlayersView />
</div>
```

## Key Shadow DOM Components Used

These components are leveraged from your design system:

### Form Components
- `ShadowTextInput` - For text fields
- `ShadowSelect` - For dropdown selectors
- `ShadowTextarea` - For multi-line text input
- `ShadowCheckbox` - For boolean toggles
- `ShadowFormGroup` - For grouping form elements
- `ShadowSportSelector` - For sport selection

### UI Components
- `ShadowButton` - For action buttons
- `ShadowNotificationModal` - For success/error messages
- `ShadowModal` - For create team modal and loading states

### Player Components
- `ShadowPlayersList` - For displaying a flat list of players
- `ShadowPlayersListGrouped` - For displaying players grouped by position or status

## Implementation Notes

### Handling Form Submission

The forms follow this pattern for handling submissions:

1. **Validate form data**
   ```typescript
   const validateForm = () => {
     const newErrors = {
       teamName: !formData.teamName.trim() ? 'Team name is required' : '',
       ageGroup: !formData.ageGroup ? 'Age group is required' : ''
     };
     
     setErrors(newErrors);
     
     return !Object.values(newErrors).some(error => error !== '');
   };
   ```

2. **Prepare data for API call**
   ```typescript
   const updatedFields: Partial<Team> = {
     team_name: formData.teamName,
     age_group: formData.ageGroup,
     // ...other fields
   };
   ```

3. **Call the service method**
   ```typescript
   const result = await TeamService.updateTeam(teamId, updatedFields);
   ```

4. **Handle success/error**
   ```typescript
   // Success
   setShowSuccessModal(true);
   
   // Error
   setErrorMessage(`Failed to update team: ${err.message}`);
   ```

### Event Handling for Shadow DOM Components

The shadow components communicate through custom events, which need to be handled differently:

```typescript
// For text inputs
const handleTextInputChange = (e: any) => {
  const { name, value } = e.detail || {};
  if (name) {
    handleChange(name, value);
  }
};

// For selects
onChange={(e) => handleChange('ageGroup', e.detail.value)}

// For checkboxes
onChange={(checked) => handleChange('isActive', checked)}

// For textareas
onChange={(value) => handleChange('description', value)}
```

## Testing

After implementing these components, test them to ensure:

1. Forms render correctly with all fields
2. Validation works properly (required fields, etc.)
3. Form submission correctly calls the service methods
4. Success and error states are handled appropriately
5. Navigation works (back to team page, etc.)
6. Player components display correctly and respond to actions

## Customization Options

### Styling

The components use Shadow DOM for styling, so any customization should respect this approach:

1. Colors are set through the Shadow DOM styles in each component
2. Make sure to use the SHOT brand colors from your Tailwind config
3. Any additional styling should be added to the shadow-*.css files

### Layout

The form layout can be customized by adjusting the ShadowFormGroup components:

```typescript
<ShadowFormGroup 
  legend="Team Information"
  layout="grid"
  columns={2}
  gap="medium"
>
  {/* Form fields */}
</ShadowFormGroup>
```

## Conclusion

This implementation maintains all the functionality of the current team management features while using the new Shadow DOM components from your design system. The player components are integrated to provide a consistent user experience throughout the application.

By following this guide, you should be able to successfully replace the Ionic-based forms with the new Shadow DOM components and integrate the player components into your team pages.
