// EditTeamShadow.tsx - Refactored Edit Team page using Shadow DOM components
import React, { useState, useEffect } from 'react';
import { useParams, useHistory } from 'react-router-dom';
import { useUserContext } from '../../../contexts/UserContext';
import { TeamService } from '../../../services/TeamService';
import { Team } from '../../../models/TeamManagementModels';
import PageWithNavigation from '../../../components/PageWithNavigation';

// Shadow DOM components
import ShadowTextInput from '../../../components/shadow/form/ShadowTextInput';
import ShadowSelect from '../../../components/shadow/form/ShadowSelect';
import ShadowTextarea from '../../../components/shadow/form/ShadowTextarea';
import ShadowCheckbox from '../../../components/shadow/form/ShadowCheckbox';
import ShadowFormGroup from '../../../components/shadow/form/ShadowFormGroup';
import ShadowSportSelector from '../../../components/shadow/form/ShadowSportSelector';
import ShadowButton from '../../../components/shadow/ShadowButton';
import ShadowNotificationModal from '../../../components/shadow/ShadowNotificationModal';
import ShadowModal from '../../../components/shadow/ShadowModal';

// Lucide React icons
import { User, Save, ChevronLeft, Building, Calendar, MapPin, Palette } from 'lucide-react';

interface RouteParams {
  clubId: string;
  teamId: string;
}

const EditTeamShadow: React.FC = () => {
  const { clubId, teamId } = useParams<RouteParams>();
  const history = useHistory();
  const { user } = useUserContext();
  
  // UI state
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  
  // Form state in a single object
  const [formData, setFormData] = useState({
    teamName: '',
    ageGroup: '',
    sportType: 'football',
    description: '',
    colorPrimary: '',
    colorSecondary: '',
    homeLocation: '',
    status: 'active',
    isActive: true
  });

  // Original team data for comparison
  const [originalTeam, setOriginalTeam] = useState<Team | null>(null);

  // Form validation state
  const [errors, setErrors] = useState({
    teamName: '',
    ageGroup: ''
  });

  // Load team data
  useEffect(() => {
    const fetchTeamData = async () => {
      setLoading(true);
      try {
        console.log('Fetching team data for team ID:', teamId);
        const teamData = await TeamService.getTeamById(teamId);
        
        if (!teamData) {
          throw new Error('Team not found');
        }
        
        console.log('Team data loaded:', teamData);
        setOriginalTeam(teamData);
        
        // Populate form data with team data
        setFormData({
          teamName: teamData.team_name || '',
          ageGroup: teamData.age_group || '',
          sportType: teamData.sport_type || 'football',
          description: teamData.description || '',
          colorPrimary: teamData.color_primary || '',
          colorSecondary: teamData.color_secondary || '',
          homeLocation: teamData.home_location || '',
          status: teamData.status || 'active',
          isActive: teamData.is_active !== undefined ? teamData.is_active : true
        });
      } catch (error) {
        console.error('Error loading team data:', error);
        setErrorMessage(`Failed to load team data: ${error.message || 'Unknown error'}`);
      } finally {
        setLoading(false);
      }
    };
    
    fetchTeamData();
  }, [teamId]);

  // Validation function
  const validateForm = () => {
    const newErrors = {
      teamName: !formData.teamName.trim() ? 'Team name is required' : '',
      ageGroup: !formData.ageGroup ? 'Age group is required' : ''
    };
    
    setErrors(newErrors);
    
    // Form is valid if no errors
    return !Object.values(newErrors).some(error => error !== '');
  };

  // Handle input changes for all input types
  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error on change
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Handle text input changes from ShadowTextInput
  const handleTextInputChange = (e: any) => {
    const { name, value } = e.detail || {};
    if (name) {
      handleChange(name, value);
    }
  };

  // Handle checkbox change
  const handleCheckboxChange = (checked: boolean) => {
    handleChange('isActive', checked);
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      setErrorMessage('Please complete all required fields');
      return;
    }
    
    console.log('Starting team update...');
    console.log('Current user:', user);
    console.log('Team ID:', teamId);
    console.log('Form data:', formData);
    
    setSaving(true);
    try {
      // Only include the fields that have changed to minimize data transfer
      const updatedFields: Partial<Team> = {
        team_name: formData.teamName,
        age_group: formData.ageGroup,
        sport_type: formData.sportType,
        description: formData.description,
        color_primary: formData.colorPrimary,
        color_secondary: formData.colorSecondary,
        home_location: formData.homeLocation,
        status: formData.status,
        is_active: formData.isActive
      };

      console.log('Team update input:', updatedFields);
      
      // Call the update team service
      const result = await TeamService.updateTeam(teamId, updatedFields);
      console.log('Team update result:', result);
      
      // Show success modal
      setShowSuccessModal(true);
    } catch (err) {
      console.error('Error updating team:', err);
      setErrorMessage(`Failed to update team: ${err.message || 'Unknown error'}`);
    } finally {
      setSaving(false);
    }
  };

  // After success alert is dismissed
  const handleReturnToTeam = () => {
    history.push(`/coach/club/${clubId}/team/${teamId}`);
  };

  // Handle navigation back to team page
  const navigateToTeam = () => {
    history.push(`/coach/club/${clubId}/team/${teamId}`);
  };

  return (
    <PageWithNavigation
      showBackButton={true}
      backUrl={`/coach/club/${clubId}/team/${teamId}`}
      title="Edit Team"
      className="team-edit-page"
    >
      <div className="shot-form" style={{
        backgroundColor: '#000000',
        minHeight: 'calc(100vh - 100px)',
        padding: '16px'
      }}>
        {/* Back Button */}
        <div className="mb-4">
          <ShadowButton
            onClick={navigateToTeam}
            variant="outline"
            icon={<ChevronLeft size={18} />}
            label="BACK TO TEAM"
          />
        </div>
        
        {/* Title Section */}
        <ShadowFormGroup legend="Edit Team Details">
          <div className="form-description text-gray-300 mb-4">
            Update the team information below. Required fields are marked with an asterisk (*).
          </div>
        </ShadowFormGroup>
        
        {/* Sport Type */}
        <ShadowFormGroup legend="Sport Type">
          <ShadowSportSelector
            value={formData.sportType}
            onChange={(value) => handleChange('sportType', value)}
            label="Sport Type"
            required
          />
        </ShadowFormGroup>
        
        {/* Team Information */}
        <ShadowFormGroup legend="Team Information">
          {/* Team Name */}
          <ShadowTextInput
            label="Team Name"
            name="teamName"
            value={formData.teamName}
            onChange={handleTextInputChange}
            placeholder="Enter team name"
            required={true}
            error={errors.teamName}
            icon="user"
          />
          
          {/* Age Group */}
          <ShadowSelect
            label="Age Group"
            value={formData.ageGroup}
            onChange={(e) => handleChange('ageGroup', e.detail.value)}
            options={[
              { value: 'U6', label: 'U6 - Under 6 Years' },
              { value: 'U8', label: 'U8 - Under 8 Years' },
              { value: 'U10', label: 'U10 - Under 10 Years' },
              { value: 'U12', label: 'U12 - Under 12 Years' },
              { value: 'U14', label: 'U14 - Under 14 Years' },
              { value: 'U16', label: 'U16 - Under 16 Years' },
              { value: 'U18', label: 'U18 - Under 18 Years' },
              { value: 'Adult', label: 'Adult - 18+ Years' }
            ]}
            required={true}
            error={errors.ageGroup}
          />

          {/* Status */}
          <ShadowSelect
            label="Status"
            value={formData.status}
            onChange={(e) => handleChange('status', e.detail.value)}
            options={[
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' },
              { value: 'archived', label: 'Archived' }
            ]}
          />
          
          {/* Is Active */}
          <ShadowCheckbox
            label="Team is visible and active"
            checked={formData.isActive}
            onChange={handleCheckboxChange}
          />
          
          {/* Primary Color */}
          <ShadowTextInput
            label="Primary Color"
            name="colorPrimary"
            value={formData.colorPrimary}
            onChange={handleTextInputChange}
            placeholder="e.g. #FF5733 or Blue"
            icon="palette"
          />
          
          {/* Secondary Color */}
          <ShadowTextInput
            label="Secondary Color"
            name="colorSecondary"
            value={formData.colorSecondary}
            onChange={handleTextInputChange}
            placeholder="e.g. #33FF57 or White"
            icon="palette"
          />
          
          {/* Home Location */}
          <ShadowTextInput
            label="Home Location"
            name="homeLocation"
            value={formData.homeLocation}
            onChange={handleTextInputChange}
            placeholder="Team's home venue or location"
            icon="map-pin"
          />
          
          {/* Description */}
          <ShadowTextarea
            label="Description"
            value={formData.description}
            onChange={(value) => handleChange('description', value)}
            placeholder="Additional information about the team"
            rows={4}
          />
        </ShadowFormGroup>
        
        {/* Submit Button */}
        <div className="mt-6 mb-8">
          <ShadowButton
            onClick={handleSubmit}
            disabled={saving}
            loading={saving}
            variant="primary"
            fullWidth={true}
            icon={<Save size={18} />}
            label="Save Changes"
          />
        </div>
        
        {/* Success Modal */}
        {showSuccessModal && (
          <ShadowNotificationModal
            isOpen={showSuccessModal}
            onClose={() => setShowSuccessModal(false)}
            title="Team Updated Successfully"
            message={`"${formData.teamName}" has been updated successfully.`}
            type="success"
            primaryButtonText="Return to Team"
            onPrimaryButtonClick={handleReturnToTeam}
          />
        )}
        
        {/* Error Modal */}
        {errorMessage && (
          <ShadowNotificationModal
            isOpen={!!errorMessage}
            onClose={() => setErrorMessage(null)}
            title="Error"
            message={errorMessage}
            type="error"
            primaryButtonText="OK"
            onPrimaryButtonClick={() => setErrorMessage(null)}
          />
        )}
        
        {/* Loading Modal */}
        {loading && (
          <ShadowModal
            isOpen={loading}
            title="Loading"
            showCloseButton={false}
          >
            <div className="flex flex-col items-center justify-center p-6">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500 mb-4"></div>
              <p className="text-white">Loading team data...</p>
            </div>
          </ShadowModal>
        )}
      </div>
    </PageWithNavigation>
  );
};

export default EditTeamShadow;
