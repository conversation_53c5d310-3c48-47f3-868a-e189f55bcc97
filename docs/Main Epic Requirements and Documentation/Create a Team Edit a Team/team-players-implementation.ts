// TeamPlayersView.tsx - Example of integrating player components into team pages
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { UserService } from '../../../services/UserService';
import { TeamService } from '../../../services/TeamService';
import { PlayerData } from '../../../components/shadow/players/ShadowPlayersList';

// Shadow DOM components
import ShadowPlayersList from '../../../components/shadow/players/ShadowPlayersList';
import ShadowPlayersListGrouped from '../../../components/shadow/players/ShadowPlayersListGrouped';
import ShadowButton from '../../../components/shadow/ShadowButton';
import ShadowFormGroup from '../../../components/shadow/form/ShadowFormGroup';
import ShadowSelect from '../../../components/shadow/form/ShadowSelect';
import ShadowNotificationModal from '../../../components/shadow/ShadowNotificationModal';

// Lucide React icons
import { UserPlus, Filter, RefreshCw } from 'lucide-react';

interface RouteParams {
  clubId: string;
  teamId: string;
}

const TeamPlayersView: React.FC = () => {
  const { clubId, teamId } = useParams<RouteParams>();
  
  // State
  const [players, setPlayers] = useState<PlayerData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [groupBy, setGroupBy] = useState<'none' | 'position' | 'status'>('none');
  const [showAddPlayerModal, setShowAddPlayerModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<PlayerData | null>(null);
  const [selectedAction, setSelectedAction] = useState<string | null>(null);
  
  // Fetch team players
  useEffect(() => {
    const fetchTeamPlayers = async () => {
      setLoading(true);
      try {
        // This would be your actual API call to get team players
        // For now, let's simulate with a mock implementation
        const teamMembers = await getTeamPlayers(teamId);
        setPlayers(teamMembers);
      } catch (err) {
        console.error('Error fetching team players:', err);
        setError('Failed to load team players. Please try again.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchTeamPlayers();
  }, [teamId]);
  
  // Mock function to get team players - replace with actual implementation
  const getTeamPlayers = async (teamId: string): Promise<PlayerData[]> => {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Return mock data
    return [
      {
        id: '1',
        name: 'Jamie Smith',
        position: 'Striker',
        age: 17,
        status: 'evaluated',
        statusText: 'Eval Ready',
        ratings: {
          technical: 4,
          physical: 3,
          psychological: 4,
          social: 5,
          positional: 3
        },
        avatarUrl: '/avatar/SHOT avatar1.png'
      },
      {
        id: '2',
        name: 'Alex Johnson',
        position: 'Midfielder',
        age: 16,
        status: 'pending',
        statusText: 'In Progress',
        ratings: {
          technical: 3,
          physical: 4,
          psychological: 3,
          social: 4,
          positional: 4
        },
        avatarUrl: '/avatar/SHOT avatar3.png'
      },
      {
        id: '3',
        name: 'Emma Williams',
        position: 'Defender',
        age: 15,
        status: 'not-started',
        ratings: {
          technical: 0,
          physical: 0,
          psychological: 0,
          social: 0,
          positional: 0
        },
        avatarUrl: '/avatar/SHOT avatar2.png'
      },
      {
        id: '4',
        name: 'Michael Chen',
        position: 'Goalkeeper',
        age: 16,
        status: 'evaluated',
        ratings: {
          technical: 5,
          physical: 5,
          psychological: 4,
          social: 4,
          positional: 5
        },
        avatarUrl: '/avatar/SHOT avatar4.png'
      }
    ];
  };
  
  // Handle player click
  const handlePlayerClick = (player: PlayerData) => {
    console.log('Player clicked:', player);
    // Navigate to player details or open player modal
  };
  
  // Handle action click (evaluate, remove, etc.)
  const handleActionClick = (action: string, player: PlayerData) => {
    console.log('Action:', action, 'Player:', player);
    setSelectedPlayer(player);
    setSelectedAction(action);
    
    // Show confirmation for certain actions
    if (action === 'remove') {
      setShowConfirmModal(true);
    } else if (action === 'evaluate') {
      // Navigate to evaluation page
      // history.push(`/coach/club/${clubId}/team/${teamId}/player/${player.id}/evaluate`);
    }
  };
  
  // Handle add player
  const handleAddPlayer = () => {
    setShowAddPlayerModal(true);
  };
  
  // Handle refresh
  const handleRefresh = () => {
    setLoading(true);
    // Re-fetch players
    getTeamPlayers(teamId).then(players => {
      setPlayers(players);
      setLoading(false);
    }).catch(err => {
      console.error('Error refreshing players:', err);
      setError('Failed to refresh players. Please try again.');
      setLoading(false);
    });
  };
  
  // Handle group by change
  const handleGroupByChange = (e: any) => {
    setGroupBy(e.detail.value as 'none' | 'position' | 'status');
  };
  
  // Render players based on grouping preference
  const renderPlayers = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      );
    }
    
    if (players.length === 0) {
      return (
        <div className="text-center p-8 text-gray-400">
          <p className="mb-4">No players found for this team.</p>
          <ShadowButton
            onClick={handleAddPlayer}
            variant="primary"
            icon={<UserPlus size={18} />}
            label="Add Players"
          />
        </div>
      );
    }
    
    if (groupBy === 'none') {
      return (
        <ShadowPlayersList
          players={players}
          onPlayerClick={handlePlayerClick}
          onActionClick={handleActionClick}
          actionOptions={[
            { id: 'evaluate', label: 'Evaluate' },
            { id: 'message', label: 'Message' },
            { id: 'remove', label: 'Remove' }
          ]}
        />
      );
    } else {
      return (
        <ShadowPlayersListGrouped
          players={players}
          groupBy={groupBy}
          onPlayerClick={handlePlayerClick}
          onActionClick={handleActionClick}
          actionOptions={[
            { id: 'evaluate', label: 'Evaluate' },
            { id: 'message', label: 'Message' },
            { id: 'remove', label: 'Remove' }
          ]}
        />
      );
    }
  };
  
  return (
    <div className="team-players-view" style={{ backgroundColor: '#000000', minHeight: 'calc(100vh - 100px)', padding: '16px' }}>
      {/* Header and controls */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-white text-xl font-bold">Team Players</h2>
        
        <div className="flex space-x-2">
          <ShadowButton
            onClick={handleRefresh}
            variant="outline"
            icon={<RefreshCw size={18} />}
            label="Refresh"
            disabled={loading}
          />
          
          <ShadowButton
            onClick={handleAddPlayer}
            variant="primary"
            icon={<UserPlus size={18} />}
            label="Add Player"
          />
        </div>
      </div>
      
      {/* Filters */}
      <ShadowFormGroup className="mb-4">
        <div className="flex items-center">
          <div className="mr-2 text-gray-400">
            <Filter size={18} />
          </div>
          <ShadowSelect
            label="Group By"
            value={groupBy}
            onChange={handleGroupByChange}
            options={[
              { value: 'none', label: 'No Grouping' },
              { value: 'position', label: 'Group by Position' },
              { value: 'status', label: 'Group by Evaluation Status' }
            ]}
          />
        </div>
      </ShadowFormGroup>
      
      {/* Players list */}
      {renderPlayers()}
      
      {/* Error modal */}
      {error && (
        <ShadowNotificationModal
          isOpen={!!error}
          onClose={() => setError(null)}
          title="Error"
          message={error}
          type="error"
          primaryButtonText="OK"
          onPrimaryButtonClick={() => setError(null)}
        />
      )}
      
      {/* Confirmation modal */}
      {showConfirmModal && selectedPlayer && (
        <ShadowNotificationModal
          isOpen={showConfirmModal}
          onClose={() => setShowConfirmModal(false)}
          title="Confirm Removal"
          message={`Are you sure you want to remove ${selectedPlayer.name} from this team?`}
          type="warning"
          primaryButtonText="Remove"
          secondaryButtonText="Cancel"
          onPrimaryButtonClick={() => {
            // Handle removal logic here
            console.log(`Removing player ${selectedPlayer.id}`);
            
            // Update local state
            setPlayers(players.filter(p => p.id !== selectedPlayer.id));
            
            // Close modal
            setShowConfirmModal(false);
          }}
          onSecondaryButtonClick={() => setShowConfirmModal(false)}
        />
      )}
      
      {/* Add player modal would be implemented here */}
    </div>
  );
};

export default TeamPlayersView;
