# How to Debug Trigger Issues in Supabase

## 1. **Check Trigger Status in SQL Editor**

Go to your Supabase Dashboard → SQL Editor and run:

```sql
-- Check if trigger exists and is enabled
SELECT 
    tgname as trigger_name,
    tgenabled as enabled,
    tgrelid::regclass as table_name
FROM pg_trigger 
WHERE tgname = 'trigger_auto_create_pre_evaluations';
```

If `tgenabled` is:
- `'O'` = Enabled (Origin)
- `'D'` = Disabled
- `'R'` = Enabled for Replica only
- `'A'` = Always enabled

## 2. **Check the Trigger Function**

```sql
-- View the trigger function definition
SELECT prosrc 
FROM pg_proc 
WHERE proname = 'auto_create_pre_evaluations';

-- Or use this to get full function details
\df+ auto_create_pre_evaluations
```

## 3. **Check Supabase Dashboard Logs**

1. Go to your Supabase Dashboard
2. Navigate to **Logs** → **Postgres Logs**
3. Filter by:
   - Severity: `error` or `warning`
   - Search for your function name: `auto_create_pre_evaluations`
   - Time range when you tried to publish the event

## 4. **Add Debug Logging to Your Trigger Function**

Edit your trigger function to add logging:

```sql
CREATE OR REPLACE FUNCTION auto_create_pre_evaluations() 
RETURNS TRIGGER AS $$
BEGIN
    -- Add debug logging
    RAISE LOG 'Trigger fired for event %', NEW.id;
    RAISE LOG 'Event status: %, is_pre_session: %', NEW.status, NEW.is_pre_session_evaluation;
    
    -- Your trigger logic here
    IF NEW.status = 'published' AND NEW.is_pre_session_evaluation = true THEN
        RAISE LOG 'Conditions met, creating pre-evaluations...';
        -- Create pre-evaluations
    END IF;
    
    RETURN NEW;
EXCEPTION WHEN OTHERS THEN
    RAISE LOG 'Error in trigger: %', SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## 5. **Test the Function Manually**

```sql
-- Call the function directly to test if it works
SELECT auto_create_pre_evaluations();

-- Or test with specific parameters if the function accepts them
SELECT create_pre_evaluations_for_event('e365df37-ff28-4e45-b2fd-ee50f9cdd935');
```

## 6. **Check Recent Function Executions**

```sql
-- Check if function has been called recently
SELECT 
    schemaname,
    funcname,
    calls,
    total_time,
    mean_time,
    max_time
FROM pg_stat_user_functions
WHERE funcname = 'auto_create_pre_evaluations';
```

## 7. **Common Issues to Check**

### A. Permissions
```sql
-- Check function permissions
SELECT has_function_privilege('auto_create_pre_evaluations', 'execute');
```

### B. Check for Circular Dependencies
```sql
-- List all triggers on the events table
SELECT * FROM pg_trigger WHERE tgrelid = 'events'::regclass;
```

### C. Check for Missing Tables/Columns
```sql
-- Verify all required tables and columns exist
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'events' 
AND column_name IN ('is_pre_session_evaluation', 'status');
```

## 8. **Create a Test Case**

```sql
-- Create a simple test to see if trigger fires
BEGIN;
    -- Insert a test event
    INSERT INTO events (name, status, is_pre_session_evaluation, team_id, start_datetime, hosted_by)
    VALUES ('Trigger Test', 'published', true, '2dd31c63-ef3a-4c86-b5f2-fc7ce24fc352', NOW(), 'test');
    
    -- Check if pre-evaluations were created
    SELECT COUNT(*) FROM pre_evaluations WHERE event_id = (SELECT id FROM events WHERE name = 'Trigger Test');
    
    -- Roll back the test
ROLLBACK;
```

## 9. **For Your Specific Event**

To check why your event didn't create pre-evaluations:

```sql
-- Check the event state
SELECT id, name, status, is_pre_session_evaluation, created_at, updated_at
FROM events 
WHERE id = 'e365df37-ff28-4e45-b2fd-ee50f9cdd935';

-- Check if any pre-evaluations exist
SELECT COUNT(*) FROM pre_evaluations 
WHERE event_id = 'e365df37-ff28-4e45-b2fd-ee50f9cdd935';

-- Check event participants
SELECT COUNT(*) FROM event_participants 
WHERE event_id = 'e365df37-ff28-4e45-b2fd-ee50f9cdd935';

-- Manually create pre-evaluations if needed
SELECT basic_pre_session(
    'e365df37-ff28-4e45-b2fd-ee50f9cdd935'::uuid,
    '2dd31c63-ef3a-4c86-b5f2-fc7ce24fc352'::uuid
);
```

## Next Steps

1. Check the Postgres logs first
2. Verify the trigger is enabled
3. Test the function manually
4. Add debug logging if needed
5. Contact Supabase support if the trigger appears correct but isn't firing