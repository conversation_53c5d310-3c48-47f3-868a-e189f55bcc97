# Locker Area Implementation Plan

## Overview
Locker encompasses all e-commerce functionality and personal equipment management. Currently, the e-commerce side is well-implemented with BigCommerce integration, while personal equipment features are missing. This plan organizes existing commerce features and outlines future equipment tracking additions.

## Scope Definition
**Locker includes:**
- Product catalog and browsing
- Shopping cart functionality
- Checkout and order processing
- BigCommerce integration
- Order history and tracking
- Wishlists and favorites
- Personal equipment tracking (future)
- Size profiles (future)
- Gear recommendations (future)

**Locker excludes:**
- Team equipment management (belongs to Perform)
- Club merchandise settings (belongs to Clubhouse admin)
- Product reviews/social features (belongs to Pulse)

## Current State Assessment

🚨 **CRITICAL: Broken Order System**
- Orders are NOT being saved anywhere (code expects non-existent `orders` table)
- Checkout bypasses BigCommerce order management entirely
- Orders only exist in memory during active session - lost on page refresh
- Order confirmation page displays transient data that disappears
- No order history available to users
- Issue #275 (better order confirmation) is a symptom of this fundamental problem

✅ **Partially Working:**
- Product browsing and catalog (BigCommerce as headless CMS)
- Cart functionality (local implementation)
- Checkout UI exists but fails to persist orders
- BigCommerce product sync

🔧 **Backend Ready, No UI:**
- Drops system (limited releases)
- Guardian controls
- Loyalty points
- Subscriptions
- Wishlists

❌ **Not Implemented:**
- Proper BigCommerce order management
- Order persistence and history
- BigCommerce Cart API integration
- BigCommerce Checkout API integration
- Webhook handling for order updates
- Personal equipment tracking
- Size profiles
- Gear history
- Equipment recommendations

## Implementation Steps

### Phase 1: Fix Core Commerce (CRITICAL - MUST BE COMPLETED FIRST)

**This phase fixes the broken order system and implements proper BigCommerce integration**

Related GitHub Issues:
- Epic #280: Persistent Orders in BigCommerce
- Parent Epic #264: BigCommerce E-commerce Integration (60% complete)
- Related #233: Setup Supabase Commerce Schema & Tables
- Fixes #275: Create better order confirmation page

#### Step 1: BigCommerce Cart API Integration (#281)
```typescript
// Update BigCommerceService.ts with cart methods
interface CartAPI {
  createCart(customerId?: string): Promise<Cart>
  addItem(cartId: string, item: CartItem): Promise<Cart>
  updateItem(cartId: string, itemId: string, quantity: number): Promise<Cart>
  removeItem(cartId: string, itemId: string): Promise<Cart>
  getCart(cartId: string): Promise<Cart>
}
```

#### Step 2: BigCommerce Checkout API Integration (#282)
```typescript
// Add checkout methods to BigCommerceService.ts
interface CheckoutAPI {
  createCheckout(cartId: string): Promise<Checkout>
  updateBillingAddress(checkoutId: string, address: Address): Promise<Checkout>
  updateShippingAddress(checkoutId: string, address: Address): Promise<Checkout>
  selectShippingOption(checkoutId: string, optionId: string): Promise<Checkout>
  createOrder(checkoutId: string): Promise<Order>
}
```

#### Step 3: Replace Local Checkout with BigCommerce (#283)
- Migrate from local checkout to BigCommerce checkout flow
- Update `locker-checkout` edge function to use BC APIs
- Remove dependency on non-existent `orders` table
- Ensure Stripe integration works WITH BigCommerce, not around it

#### Step 4: Update Order Confirmation Page (#284)
- Fetch order data from BigCommerce Order API
- Display real order number and details
- Show order status from BigCommerce
- Enable order tracking links

#### Step 5: Implement Webhook Handling (#285)
```typescript
// Create webhook endpoints for order lifecycle
POST /api/webhooks/bigcommerce/orders
- order.created
- order.updated
- order.shipped
- order.completed
```

#### Step 6: Order Tracking & Notifications (#286)
- Create order history page
- Implement email notifications via BigCommerce
- Add order status tracking UI
- Enable customer to view past orders

#### Step 7: Remove Obsolete Code (#287)
- Delete unused local order handling code
- Remove references to non-existent `orders` table
- Clean up temporary workarounds
- Update all imports and dependencies

### Phase 2: Directory Structure Setup
Create the following structure:
```
src/features/locker/
├── components/
│   ├── cart/
│   ├── checkout/
│   ├── products/
│   ├── orders/
│   └── equipment/ (future)
├── pages/
│   ├── shop/
│   ├── cart/
│   ├── checkout/
│   ├── orders/
│   └── equipment/ (future)
├── hooks/
├── services/
├── contexts/
├── types/
└── index.ts
```

### Phase 3: File Relocation (Low Risk)

```
FROM → TO

Pages:
src/pages/shop/Shop.tsx → features/locker/pages/shop/Shop.tsx
src/pages/shop/ProductDetail.tsx → features/locker/pages/products/ProductDetail.tsx
src/pages/shop/Cart.tsx → features/locker/pages/cart/Cart.tsx
src/pages/shop/Checkout.tsx → features/locker/pages/checkout/Checkout.tsx
src/pages/shop/OrderConfirmation.tsx → features/locker/pages/orders/OrderConfirmation.tsx

Components:
src/components/shadow/commerce/* → features/locker/components/products/*
src/components/shadow/cart/* → features/locker/components/cart/*

Services:
src/services/BigCommerceService.ts → features/locker/services/BigCommerceService.ts

Contexts:
src/contexts/EnhancedShoppingCartContext.tsx → features/locker/contexts/ShoppingCartContext.tsx

Remove Test Files:
src/pages/TestBigCommerce.tsx → DELETE
src/pages/BigCommerceProducts.tsx → DELETE
```

### Phase 4: Route Updates

```typescript
// Update routes in AppRoutes.tsx
const LockerRoutes = {
  shop: '/locker/shop',
  product: '/locker/product/:productId',
  cart: '/locker/cart',
  checkout: '/locker/checkout',
  orders: '/locker/orders',
  equipment: '/locker/equipment' // future
};

// Redirect old routes
<Route path="/shop" render={() => <Redirect to="/locker/shop" />} />
```

### Phase 5: Future Equipment Features

Plan for adding personal equipment management:
```
features/locker/
├── pages/
│   └── equipment/
│       ├── MyEquipment.tsx      // Equipment inventory
│       ├── SizeProfile.tsx      // Personal measurements
│       └── GearHistory.tsx      // Purchase/usage history
└── components/
    └── equipment/
        ├── EquipmentCard.tsx
        ├── SizeForm.tsx
        └── GearRecommendations.tsx
```

## Risk Assessment

### Critical Risk Areas (IMMEDIATE ATTENTION REQUIRED)

1. **Broken Order System (CRITICAL RISK - CURRENTLY FAILING)**
   - **Risk**: Orders are NOT being saved anywhere
   - **Current Impact**: 
     - All orders lost on page refresh
     - No order history for customers
     - No order management for admins
     - Revenue tracking impossible
   - **Root Cause**: 
     - Code expects `orders` table that doesn't exist
     - Checkout bypasses BigCommerce order APIs
     - Using BigCommerce as product catalog only
   - **Mitigation**: 
     - Implement Phase 4 immediately
     - Use BigCommerce Cart & Checkout APIs
     - Remove local order handling
   - **No Backup Plan**: This MUST be fixed

### High Risk Areas

1. **BigCommerce API Integration (HIGH RISK)**
   - **Risk**: Current integration incomplete
   - **Impact**: 
     - Can't create proper orders
     - No cart persistence in BigCommerce
     - Missing order lifecycle management
   - **Mitigation**: 
     - Implement full Cart API (#281)
     - Implement Checkout API (#282)
     - Add webhook handling (#285)
   - **Backup Plan**: 
     - None - proper integration required

### Medium Risk Areas

1. **Shopping Cart Context (MEDIUM RISK)**
   - **Risk**: Used throughout the app
   - **Impact**: Cart functionality breaks
   - **Mitigation**: 
     - Find all context consumers first
     - Update imports carefully
     - Test cart persistence
   - **Backup Plan**: 
     - Keep old context as alias temporarily
     - Gradual migration of consumers

2. **File Reorganization (MEDIUM RISK)**
   - **Risk**: Import path changes
   - **Impact**: Build failures
   - **Mitigation**: 
     - Update imports systematically
     - Test all pages after move
   - **Backup Plan**: 
     - Can revert file moves
     - Use module aliases

### Low Risk Areas

1. **Component Relocation (LOW RISK)**
   - **Risk**: Clear component boundaries
   - **Impact**: Limited to shopping features
   - **Mitigation**: 
     - Update imports systematically
   - **Backup Plan**: 
     - Can revert individual components

## Backup Strategies

### Strategy 1: Route Aliasing
```typescript
// Support both paths during transition
const handleShopRoute = (path: string) => {
  const newPath = path.replace('/shop', '/locker/shop');
  return <Redirect to={newPath} />;
};
```

### Strategy 2: Context Bridge
```typescript
// Temporary bridge for cart context migration
export const ShoppingCartContext = EnhancedShoppingCartContext;
export const useShoppingCart = () => {
  // Can switch between old/new implementation
  return useContext(EnhancedShoppingCartContext);
};
```

### Strategy 3: Service Abstraction
```typescript
// Abstract BigCommerce service location
export const useBigCommerce = () => {
  // Can import from old or new location
  return BigCommerceService;
};
```

## BigCommerce Integration Requirements

### API Access Configuration
1. **Storefront API** (Customer-facing operations)
   - Cart management (create, update, delete)
   - Checkout creation and management
   - Customer authentication
   - Real-time inventory checks

2. **Management API** (Backend operations)
   - Order management and fulfillment
   - Webhook configuration
   - Customer data sync
   - Order status updates

### Critical Integration Decisions

#### Checkout Approach
- **Option A: Hosted Checkout** (Redirect to BigCommerce)
  - Pros: Simpler, PCI compliance handled, all BC features available
  - Cons: Less control over UI, potential brand disconnect
  
- **Option B: Custom Checkout** (API-only)
  - Pros: Full UI control, seamless experience
  - Cons: More complex, must handle PCI compliance

**Recommendation**: Start with Option B (Custom) to maintain brand consistency

#### Cart Persistence Strategy
1. Use BigCommerce Cart API as source of truth
2. Local cart state for performance only
3. Sync local changes to BC immediately
4. Handle offline gracefully with sync queue

#### Order Lifecycle Management
```
Customer Cart → BC Cart → BC Checkout → BC Order → Webhooks → Local Updates
```

### Webhook Requirements
Configure endpoints for:
- `store/order/created` - New order placed
- `store/order/updated` - Order status changed
- `store/order/archived` - Order cancelled
- `store/order/message/created` - Customer communication
- `store/shipment/*` - Shipping updates

### Authentication Flow
1. Use BC customer login for existing accounts
2. Create BC customer for new registrations
3. Link BC customer ID to local profile
4. Handle guest checkout via BC guest flow

## Validation Checklist

### Phase 1 Critical Checks (Order Persistence - MUST PASS BEFORE PROCEEDING)
- [ ] Orders are created in BigCommerce (not local database)
- [ ] Order confirmation shows real BigCommerce order number
- [ ] Order persists after page refresh
- [ ] Order history displays past orders from BigCommerce
- [ ] Webhooks update local order status
- [ ] Cart syncs with BigCommerce Cart API
- [ ] Checkout uses BigCommerce Checkout API
- [ ] No references to non-existent `orders` table
- [ ] Stripe payments integrate with BC order flow

### Original Locker Checks
- [ ] Product catalog loads correctly
- [ ] Can add items to cart
- [ ] Cart persists across sessions
- [ ] Guest checkout works
- [ ] Logged-in checkout works
- [ ] Order confirmation displays
- [ ] BigCommerce sync working
- [ ] No console errors
- [ ] All imports updated
- [ ] Old test files removed

## Playwright E2E Testing Requirements

### Test Structure
```
playwright/features/locker/
├── fixtures/
│   ├── test-products.ts      # Sample products for testing
│   ├── test-users.ts         # Test accounts (guest, member, youth)
│   └── test-addresses.ts     # Shipping/billing test data
├── page-objects/
│   ├── shop.page.ts          # Product listing interactions
│   ├── product.page.ts       # Product detail page
│   ├── cart.page.ts          # Shopping cart management
│   └── checkout.page.ts      # Checkout flow
├── shopping-flow.spec.ts     # Core shopping journey
├── cart-persistence.spec.ts  # Cart state management
├── bigcommerce.spec.ts       # API integration tests
└── edge-cases.spec.ts        # Error handling

```

### Pre-Migration Tests
- [ ] Capture baseline shopping flow timing
- [ ] Document current cart persistence behavior
- [ ] Record BigCommerce API response times
- [ ] Test current guest checkout flow
- [ ] Verify order confirmation process

### During Migration Tests
```typescript
// Parallel route testing
test('Old and new shop routes work simultaneously', async ({ page }) => {
  // Test /shop
  await page.goto('/shop');
  await expect(page.locator('[data-testid="product-grid"]')).toBeVisible();
  
  // Test /locker/shop
  await page.goto('/locker/shop');
  await expect(page.locator('[data-testid="product-grid"]')).toBeVisible();
});

// Cart persistence during migration
test('Cart survives route migration', async ({ page }) => {
  // Add item using old route
  await page.goto('/shop');
  await page.locator('[data-testid="add-to-cart-123"]').click();
  
  // Verify cart in new route
  await page.goto('/locker/cart');
  await expect(page.locator('[data-testid="cart-item-123"]')).toBeVisible();
});
```

### Post-Migration Tests
- [ ] Full shopping flow regression
- [ ] Cart persistence verification
- [ ] Performance benchmarks (< 2s load time)
- [ ] BigCommerce sync validation
- [ ] Order history accessibility

### Critical User Journeys
1. **Guest Quick Purchase**
   ```
   Browse → Add to Cart → Checkout → Payment → Confirmation
   Target: < 5 clicks, < 2 min total
   ```

2. **Member with Saved Info**
   ```
   Login → Browse → Add Multiple → Apply Discount → Express Checkout
   Target: < 3 min total
   ```

3. **Youth Guardian Approval**
   ```
   Youth Adds Items → Submit for Approval → Guardian Notified → Approval → Purchase
   Target: Approval request sent within 30s
   ```

### Test Data Requirements
```typescript
// Test products that must exist
export const requiredTestProducts = {
  simple: { id: '123', sku: 'TEST-001', price: 25.00 },
  withVariants: { id: '124', sku: 'TEST-002', sizes: ['S', 'M', 'L'] },
  highValue: { id: '125', sku: 'TEST-003', price: 150.00 }, // Free shipping
  outOfStock: { id: '126', sku: 'TEST-004', inventory: 0 }
};

// Test scenarios
export const cartScenarios = {
  emptyCart: { items: [] },
  singleItem: { items: ['123'] },
  multipleItems: { items: ['123', '124', '125'] },
  highValueCart: { items: ['125'], expectedShipping: 0 }
};
```

### Performance Benchmarks
- Product list load: < 2s
- Add to cart: < 500ms
- Cart update: < 300ms
- Checkout page: < 1.5s
- Order submission: < 3s

### Rollback Test Coverage
```typescript
test('Can rollback to old routes without data loss', async ({ page }) => {
  // Create cart in new system
  // Trigger rollback
  // Verify cart accessible in old system
  // Ensure no duplicate orders
});
```

## Rollback Plan

If issues discovered:

### Immediate (< 30 min)
1. Revert route changes
2. Point imports back to original locations
3. Clear any caches

### Short-term (< 2 hours)
1. Keep both structures active
2. Use feature flag for new structure
3. Monitor for issues

## Dependencies on Other Areas

- **Clubhouse**: User authentication for orders
- **Perform**: Future integration for team equipment
- **Pulse**: Future product reviews/social features
- **Shared**: Payment processing, user profiles

## Success Metrics

- No disruption to active shopping sessions
- Cart abandonment rate unchanged
- Order completion rate maintained
- Page load times improved
- Clean code structure
- Ready for equipment features

## Future Considerations

### Personal Equipment Management
When implementing equipment tracking:
1. Design equipment data model
2. Create UI for equipment inventory
3. Link purchases to equipment records
4. Build size profile system
5. Implement gear recommendations

### Advanced Commerce Features
UI needed for backend-ready features:
1. Wishlists UI
2. Subscription management
3. Loyalty points display
4. Guardian approval flow
5. Drops/limited releases

## Notes

- Locker is the most self-contained area
- Low risk for reorganization
- Main challenge is future equipment features
- Consider BigCommerce webhook integration
- Plan for inventory management UI