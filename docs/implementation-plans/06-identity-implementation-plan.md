# Identity Area Implementation Plan

## Overview
The Identity area consolidates all authentication, user management, and family relationship features into a unified system. This is the critical path for the entire application as all other features depend on authentication and user context.

## Current State Analysis

### Authentication Pages (5 separate implementations)
1. `pages/Login.tsx` - Basic login
2. `pages/UpdatedLogin.tsx` - Enhanced login
3. `pages/UpdatedLoginV2.tsx` - Latest version (KEEP THIS)
4. `pages/LoginUpdated.tsx` - Another variant
5. `pages/CapLogin.tsx` - Special login variant

### Account Management
- `pages/Account.tsx` - Account settings
- `pages/Signup.tsx` - Registration
- `pages/EmailVerificationPage.tsx` - Email verification
- `pages/ResetPassword.tsx` - Password reset
- `pages/registration/` - Multi-step registration flow
- `pages/Onboarding/` - User onboarding

### Family Management
- `pages/Family.tsx` - Family dashboard
- `pages/AddFamily.tsx` - Add family members
- `pages/EditFamily.tsx` - Edit family details
- `pages/ManageChildren.tsx` - Child account management
- `services/FamilyRelationshipService.ts` - Family logic
- `hooks/useChildManagement.ts` - Child management hooks
- `hooks/useUserChildren.ts` - User children data
- `components/v2/parent/` - Parent-specific components

### Account Switching
- `hooks/useAccountSwitching.ts` - Switch between profiles
- `components/ProfileStep.tsx` - Profile selection
- `components/AccountStep.tsx` - Account setup

## Target Architecture

```
src/features/identity/
├── components/
│   ├── auth/
│   │   ├── LoginForm.tsx
│   │   ├── SignupForm.tsx
│   │   ├── PasswordReset.tsx
│   │   └── EmailVerification.tsx
│   ├── family/
│   │   ├── FamilyDashboard.tsx
│   │   ├── ChildList.tsx
│   │   ├── AddFamilyMember.tsx
│   │   └── ParentApproval.tsx
│   └── account/
│       ├── AccountSettings.tsx
│       ├── ProfileSwitcher.tsx
│       └── OnboardingFlow.tsx
├── pages/
│   ├── LoginPage.tsx (unified login)
│   ├── SignupPage.tsx
│   ├── AccountPage.tsx
│   └── FamilyPage.tsx
├── hooks/
│   ├── useAuth.ts
│   ├── useAccountSwitching.ts
│   ├── useFamily.ts
│   └── useOnboarding.ts
├── services/
│   ├── AuthService.ts
│   ├── FamilyService.ts
│   └── AccountService.ts
├── types/
│   ├── auth.types.ts
│   └── family.types.ts
└── index.ts
```

## Files to Deprecate

### Authentication Pages (5 → 1)
Files to move to `src/_deprecated/identity/pages/`:
- `pages/Login.tsx` - Basic login implementation
- `pages/UpdatedLogin.tsx` - Enhanced version
- `pages/LoginUpdated.tsx` - Another variant
- `pages/CapLogin.tsx` - Special case login
- `pages/SimpleLogin.tsx` - Minimal implementation

**Keep**: `pages/UpdatedLoginV2.tsx` (will be moved and enhanced)

### Deprecated Patterns
Move to `src/_deprecated/identity/components/`:
- Old auth components that aren't using latest patterns
- Duplicate family management components
- Legacy onboarding flows

### Deprecation Timeline
1. **Week 1**: Move files to deprecated directory
2. **Week 2-3**: Update all references
3. **Week 4**: Add deprecation warnings
4. **Month 2**: Verify no usage in production
5. **Month 3**: Delete deprecated files

## Migration Strategy

### Phase 1: Foundation Setup (Week 1)
**Risk Level**: LOW

1. Create directory structure
2. Set up base types and interfaces
3. Create AuthService abstraction
4. Write comprehensive tests for auth flows

**Files to create**:
- `src/features/identity/` structure
- Base service abstractions
- Type definitions

### Phase 2: Authentication Consolidation (Week 2)
**Risk Level**: HIGH

1. Copy UpdatedLoginV2.tsx as base for unified login
2. Create feature flags for gradual rollout
3. Implement parallel authentication system
4. Route all login pages to new unified login (with feature flag)

**Migration approach**:
```typescript
// In each old login page during transition:
export default function Login() {
  if (featureFlags.useUnifiedLogin) {
    return <Navigate to="/identity/login" />;
  }
  // Original login code...
}
```

### Phase 3: Family Management Migration (Week 3)
**Risk Level**: MEDIUM

1. Move family components to new structure
2. Consolidate family services
3. Update parent dashboard components
4. Migrate child management features

**Key considerations**:
- Preserve all family relationships
- Maintain approval workflows
- Test parent-child linking extensively

### Phase 4: Account Features (Week 3-4)
**Risk Level**: MEDIUM

1. Consolidate account settings
2. Migrate profile switching
3. Update onboarding flows
4. Implement unified account dashboard

## Critical Components

### Unified Login Page
```typescript
// Features to include:
- Social login options
- Remember me functionality
- Account switching shortcut
- Family member quick switch
- Forgot password link
- Sign up prompt
```

### Family Dashboard
```typescript
// Must support:
- View all family members
- Add/remove children
- Manage permissions
- Approval workflows
- Quick account switching
```

### Account Switcher
```typescript
// Requirements:
- Show all linked accounts
- Visual indicators for account type
- Quick switch without re-auth
- Remember last used account
```

## Data Migration

### User Authentication
- No data migration needed
- Supabase auth remains unchanged
- Only UI/UX consolidation

### Family Relationships
- Existing relationships preserved
- No schema changes required
- Test data integrity thoroughly

## Testing Strategy

### Unit Tests
- Auth service methods
- Family relationship logic
- Account switching logic
- Form validations

### Integration Tests
- Login flow variations
- Family member management
- Account switching scenarios
- Password reset flow

### E2E Tests (Critical)
```javascript
// Playwright tests needed:
- Complete signup flow
- Login with all methods
- Family member addition
- Child account creation
- Account switching
- Password reset journey
- Email verification
```

## Rollback Plan

### Phase-specific rollbacks:

1. **Auth Consolidation**
   - Feature flag to disable unified login
   - Routes remain unchanged
   - Original pages still accessible

2. **Family Management**
   - Keep old family pages active
   - Database unchanged
   - Service layer abstraction allows switching

3. **Emergency Full Rollback**
   - Revert routing changes
   - Restore original imports
   - Clear feature flags

## Success Criteria

- [ ] Single login page handles all auth methods
- [ ] 0% increase in auth failures
- [ ] Account switching works seamlessly
- [ ] Family management fully functional
- [ ] All existing users can login
- [ ] Performance metrics maintained
- [ ] Positive user feedback on simplified flow

## Dependencies

### Must complete before Identity:
- Foundation Modules (Auth abstraction)

### Blocks these areas:
- ALL other feature areas depend on Identity

## Risk Mitigation

### Highest Risk: Breaking Authentication
**Mitigation Strategy**:
1. Extensive testing before any production changes
2. Gradual rollout with feature flags
3. Monitor auth metrics in real-time
4. Keep all 5 login pages until validated
5. Run parallel systems for 2 weeks minimum

### Medium Risk: Family Relationships
**Mitigation Strategy**:
1. Complete data integrity audit first
2. Test with production data copies
3. Validate all parent-child links
4. Have database rollback ready

## Implementation Checklist

### Week 1: Setup
- [ ] Create directory structure
- [ ] Set up routing
- [ ] Create service abstractions
- [ ] Write test suite
- [ ] Set up feature flags

### Week 2: Auth Migration
- [ ] Consolidate login pages
- [ ] Implement unified login
- [ ] Test all auth methods
- [ ] Set up parallel system
- [ ] Monitor metrics

### Week 3: Family & Account
- [ ] Migrate family components
- [ ] Move account settings
- [ ] Implement account switching
- [ ] Update onboarding
- [ ] Full integration testing

### Week 4: Validation & Cleanup
- [ ] Run full regression suite
- [ ] Performance testing
- [ ] User acceptance testing
- [ ] Remove deprecated code
- [ ] Documentation update

## Notes

- UpdatedLoginV2.tsx is the most complete implementation
- Family relationships are critical for youth sports
- Account switching must be seamless
- Email verification flow must not break
- Consider SSO options for future