# Domain Relationships - SHOT Platform

## Overview
This document visualizes and explains the relationships between the four core domains (Clubs, Teams, Players, Evaluations) and how they map to the implementation areas.

## Domain Model

```mermaid
graph TB
    %% Core Domains
    Club[Club Domain]
    Team[Team Domain]
    Player[Player Domain]
    Evaluation[Evaluation Domain]
    
    %% Implementation Areas
    Clubhouse[Clubhouse<br/>Member Experience]
    Control[Control<br/>Administration]
    Perform[Perform<br/>Team Operations]
    Assess[Assess<br/>Evaluations]
    Identity[Identity<br/>Authentication]
    
    %% Domain Relationships
    Club -->|contains| Team
    Club -->|has members| Player
    Team -->|has players| Player
    Team -->|conducts| Evaluation
    Player -->|receives| Evaluation
    
    %% Area Mappings
    Club -.->|member facing| Clubhouse
    Club -.->|admin facing| Control
    Team -.->|managed in| Perform
    Player -.->|authenticated by| Identity
    Player -.->|coached in| Perform
    Evaluation -.->|conducted in| Assess
    
    %% Shared Core
    SharedCore[Shared Club Core<br/>Entities & Context]
    Clubhouse --> SharedCore
    Control --> SharedCore
    
    style Club fill:#f9f,stroke:#333,stroke-width:4px
    style SharedCore fill:#bbf,stroke:#333,stroke-width:2px,stroke-dasharray: 5 5
```

## Domain Hierarchy

### 1. Club Domain (Top Level)
**Definition**: The organizational entity that contains everything else

**Split Implementation**:
- **Clubhouse** (Member Experience)
  - Public club information
  - Member dashboards
  - Community features
  - Announcements
  - Member directory
  
- **Control** (Administrative)
  - Club registration/setup
  - Billing management
  - Permission management
  - Compliance
  - Settings configuration

**Shared Core**: Both areas use a common club entity model and context provider

### 2. Team Domain (Within Club)
**Definition**: Operational units within a club for training and competition

**Implementation**: **Perform Area**
- Team creation and management
- Roster management
- Team objectives
- Performance tracking
- Team-level analytics

**Relationships**:
- Teams exist only within a Club context
- Teams inherit club settings and policies
- Teams have operational autonomy

### 3. Player Domain (Members of Club/Team)
**Definition**: Individual members who participate in club activities

**Split Implementation**:
- **Identity** (Account Management)
  - Authentication
  - Profile management
  - Account settings
  
- **Perform** (Team Context)
  - Player performance
  - Team assignments
  - Individual objectives
  
- **Clubhouse** (Member Experience)
  - Member profile display
  - Social features
  - Personal dashboard

**Relationships**:
- Players are members of a Club first
- Players can be on multiple Teams
- Player data is owned by the Club

### 4. Evaluation Domain
**Definition**: Assessment and feedback system for player development

**Implementation**: **Assess Area**
- Evaluation templates (defined at Club level)
- Conducting evaluations (at Team level)
- Individual Development Plans (IDPs)
- Progress tracking
- Historical analysis

**Relationships**:
- Club defines evaluation framework
- Teams conduct evaluations
- Players receive evaluations
- Evaluations link to player objectives

## Cross-Domain Interactions

### Club ↔ Team
```typescript
interface ClubTeamRelationship {
  // Club provides
  framework: EvaluationFramework;
  policies: ClubPolicies;
  branding: ClubBranding;
  
  // Team operates within
  autonomy: TeamAutonomy;
  constraints: ClubConstraints;
}
```

### Club ↔ Player
```typescript
interface ClubPlayerRelationship {
  // Membership
  status: MembershipStatus;
  subscription: MemberSubscription;
  permissions: MemberPermissions;
  
  // Participation
  teams: TeamAssignment[];
  roles: MemberRole[];
}
```

### Team ↔ Player
```typescript
interface TeamPlayerRelationship {
  // Assignment
  position: Position;
  number: JerseyNumber;
  status: PlayerStatus;
  
  // Performance
  stats: PlayerStatistics;
  objectives: PlayerObjectives;
}
```

### Player ↔ Evaluation
```typescript
interface PlayerEvaluationRelationship {
  // Assessments
  evaluations: Evaluation[];
  selfAssessments: SelfAssessment[];
  
  // Development
  idp: IndividualDevelopmentPlan;
  progress: ProgressMetrics;
}
```

## Data Flow Patterns

### Read Patterns
```
Clubhouse → reads → Club (public info), Player (profiles), Team (summaries)
Control → reads → All domain data (full access)
Perform → reads → Club (context), Team (full), Player (team members)
Assess → reads → Club (framework), Team (context), Player (evaluations)
Identity → reads → Player (authentication)
```

### Write Patterns
```
Clubhouse → writes → Member preferences, RSVP status
Control → writes → Club settings, Permissions, Billing
Perform → writes → Team data, Player objectives
Assess → writes → Evaluations, IDPs
Identity → writes → Player profiles, Authentication
```

## Navigation Flow

### Member Journey
```
Login (Identity) 
  → Member Dashboard (Clubhouse)
    → View Teams (navigate to Perform)
    → View Evaluations (navigate to Assess)
    → Edit Profile (navigate to Identity)
```

### Admin Journey
```
Login (Identity)
  → Admin Dashboard (Control)
    → Manage Club (Control)
    → View Member Activity (read from Clubhouse)
    → Configure Teams (policy to Perform)
    → Setup Evaluations (framework to Assess)
```

### Coach Journey
```
Login (Identity)
  → Coach Dashboard (Perform)
    → Manage Team (Perform)
    → Conduct Evaluations (navigate to Assess)
    → View Club Info (read from Clubhouse)
```

## Implementation Guidelines

### 1. Respect Domain Boundaries
- Never bypass the designated area for domain operations
- Use navigation for cross-domain user flows
- Maintain data consistency through shared services

### 2. Use Shared Context
- Club context should be available throughout the app
- Player context comes from Identity
- Team context is managed by Perform

### 3. Enforce Permissions
- Check permissions at the component level
- Domain boundaries should align with permission boundaries
- Audit all administrative actions

### 4. Optimize Performance
- Cache frequently accessed cross-domain data
- Use lazy loading for domain-specific features
- Minimize cross-domain API calls

## Testing Domain Boundaries

```typescript
describe('Domain Boundary Enforcement', () => {
  test('Clubhouse cannot write to Control domain', () => {
    // Verify write operations are blocked
  });
  
  test('Cross-domain navigation works correctly', () => {
    // Verify seamless user flow between areas
  });
  
  test('Shared context updates propagate', () => {
    // Verify context changes reflect everywhere
  });
  
  test('Domain data isolation is maintained', () => {
    // Verify no data leakage between domains
  });
});
```

## Future Considerations

### Multi-Club Support
- Players may belong to multiple clubs
- Need club switching in UI
- Maintain separate contexts per club

### Federation/League Support
- Clubs may belong to associations
- Additional hierarchy level needed
- Cross-club competitions and evaluations

### API Design
- Consider GraphQL for complex relationships
- Domain-driven API endpoints
- Versioning strategy for domain changes

## Conclusion

The domain model provides clear boundaries while maintaining necessary relationships. The split of the Club domain between Clubhouse (member-facing) and Control (administrative) ensures proper separation of concerns while sharing a common core. This architecture supports current needs while allowing for future growth and maintains security through clear domain boundaries.