# Clubhouse Area Implementation Plan

## Overview
Clubhouse serves as the **member-facing experience** for club features, providing the community hub and member services. This is distinct from administrative functions (Control area) and focuses on member engagement, club culture, and self-service features.

**UPDATED**: Authentication has moved to Identity area, Admin features to Control area. See Club Domain Strategy (10-club-domain-strategy.md) for complete domain model.

## Domain Responsibility
Clubhouse is responsible for the **Member Experience** within the Club domain:

### Core Responsibilities
- **Member Journey**: From first visit through active membership
- **Club Community**: Social features, directories, and engagement
- **Information Access**: News, schedules, announcements
- **Self-Service**: Member profile updates, RSVP, preferences
- **Club Culture**: Branding, values, community standards

### What Clubhouse is NOT
- NOT for administrative functions (→ Control area)
- NOT for team operations (→ Perform area)  
- NOT for evaluations (→ Assess area)
- NOT for platform-wide features (→ Control area)

## Scope Definition
**Clubhouse NOW includes:**
- Member dashboards and home experience
- Club member directories and social features
- Club news, announcements, and communications
- Member-visible statistics and achievements
- Club branding experience (colors, logos, themes)
- Event calendars and member schedules
- Member onboarding and welcome flows
- Club culture and community features

**MOVED to other areas:**
- Authentication → Identity area
- Platform/Club admin → Control area
- User account management → Identity area
- System configuration → Control area
- Team operations → Perform area
- Player evaluations → Assess area

## Implementation Steps

### Phase 1: Directory Structure Setup
Create the following member-focused structure:
```
src/features/clubhouse/
├── components/
│   ├── dashboard/          # Member dashboards
│   ├── directory/          # Member directories
│   ├── announcements/      # News and updates
│   ├── community/          # Social features
│   ├── onboarding/         # New member flows
│   └── branding/           # Club theme components
├── pages/
│   ├── MemberHome.tsx      # Member landing page
│   ├── Directory.tsx       # Member directory
│   ├── Announcements.tsx   # Club news
│   └── Welcome.tsx         # Onboarding
├── hooks/
│   ├── useClubContext.ts   # Access club data
│   ├── useMemberData.ts    # Member-specific data
│   └── useAnnouncements.ts # Club communications
├── services/
│   ├── MemberService.ts    # Member operations
│   └── AnnouncementService.ts
├── types/
│   └── clubhouse.types.ts
└── index.ts
```

### Phase 2: File Consolidation and Movement

#### Member-Facing Pages to Move
```
FROM → TO

Member Experience Pages:
src/pages/Home.tsx → features/clubhouse/pages/MemberHome.tsx
src/pages/Dashboard.tsx → features/clubhouse/pages/MemberDashboard.tsx
src/pages/Membership.tsx → features/clubhouse/pages/MembershipStatus.tsx

Components to Migrate:
src/components/Dashboard.tsx → features/clubhouse/components/dashboard/MemberDashboard.tsx
src/components/MembershipCard.tsx → features/clubhouse/components/membership/MembershipCard.tsx
src/components/announcements/* → features/clubhouse/components/announcements/*
src/components/club/MemberDirectory.tsx → features/clubhouse/components/directory/MemberDirectory.tsx

New Components to Create:
- features/clubhouse/components/dashboard/WelcomeWidget.tsx
- features/clubhouse/components/dashboard/UpcomingEvents.tsx
- features/clubhouse/components/dashboard/ClubNews.tsx
- features/clubhouse/components/community/MemberProfile.tsx
- features/clubhouse/components/community/ClubChat.tsx
```

#### Integration with Shared Club Core
```typescript
// Use shared club context from core
import { useClub } from '@/core/club/hooks/useClub';
import { ClubMember } from '@/core/club/entities/ClubMember';

// Clubhouse focuses on member experience
const MemberDashboard = () => {
  const { currentClub } = useClub();
  const member = useClubMember();
  
  return <MemberExperience club={currentClub} member={member} />;
};
```

### Phase 3: Update Imports and Routes

#### Route Updates
```typescript
// Before
<Route path="/home" component={Home} />
<Route path="/login" component={Login} />

// After
<Route path="/home" component={() => import('features/clubhouse/pages/dashboard/Home')} />
<Route path="/login" component={() => import('features/clubhouse/pages/auth/Login')} />
```

#### Import Updates
- Update all imports to use new paths
- Create barrel exports in `features/clubhouse/index.ts`
- Set up TypeScript path aliases

## Domain Integration

### Relationship with Other Domains

#### Club → Teams (via Perform)
```typescript
// Clubhouse shows team associations but doesn't manage them
const MemberTeams = () => {
  const { currentClub } = useClub();
  const memberTeams = useMyTeams(currentClub.id);
  
  return (
    <TeamList 
      teams={memberTeams}
      onViewTeam={(team) => navigate(`/perform/team/${team.id}`)}
    />
  );
};
```

#### Club → Players (via Identity)
```typescript
// Clubhouse displays member info but Identity manages accounts
const MemberProfile = () => {
  const profile = useMyProfile(); // From Identity
  const membership = useMyMembership(); // Club-specific
  
  return (
    <ProfileDisplay 
      profile={profile}
      membership={membership}
      onEditProfile={() => navigate('/identity/profile')}
    />
  );
};
```

#### Club → Evaluations (via Assess)
```typescript
// Clubhouse can show evaluation summaries
const MyEvaluations = () => {
  const evaluationSummary = useMyEvaluationSummary();
  
  return (
    <EvaluationWidget
      summary={evaluationSummary}
      onViewDetails={() => navigate('/assess/my-evaluations')}
    />
  );
};
```

### Data Access Patterns

**Clubhouse READS from:**
- Club core (branding, info, settings)
- Member data (directory, profiles)
- Team summaries (from Perform)
- Evaluation summaries (from Assess)
- Event schedules (from Schedule)

**Clubhouse WRITES to:**
- Member preferences
- RSVP status
- Community posts
- Profile updates (via Identity)

**Clubhouse NEVER:**
- Modifies club settings (Control only)
- Creates/manages teams (Perform only)
- Conducts evaluations (Assess only)
- Handles billing (Control only)

## Risk Assessment

### High Risk Areas

1. **Member Dashboard Integration (HIGH RISK)**
   - **Risk**: Dashboard aggregates data from multiple domains
   - **Impact**: Broken dashboard blocks member access to key info
   - **Mitigation**: 
     - Implement proper error boundaries
     - Graceful fallbacks for each widget
     - Load data asynchronously
   - **Backup Plan**: 
     - Progressive enhancement approach
     - Show available data even if some fails
     - Clear error messages for failures

2. **Domain Boundary Violations (MEDIUM RISK)**
   - **Risk**: Accidentally including admin functions in member area
   - **Impact**: Security vulnerabilities or broken features
   - **Mitigation**: 
     - Clear separation in code structure
     - Permission checks at component level
     - Code review checklist for boundaries
   - **Backup Plan**: 
     - Runtime permission validation
     - Feature flags for sensitive features

3. **Cross-Domain Data Dependencies (MEDIUM RISK)**
   - **Risk**: Clubhouse depends on data from Perform, Assess, Identity
   - **Impact**: Missing data or broken integrations
   - **Mitigation**: 
     - Define clear data contracts
     - Mock data for testing
     - Service layer abstraction
   - **Backup Plan**: 
     - Fallback to basic member info
     - Cache last known good data
     - Show "data unavailable" states

### Low Risk Areas

1. **Profile/Account Pages (LOW RISK)**
   - Self-contained functionality
   - Limited cross-feature dependencies

2. **Membership Pages (LOW RISK)**
   - Isolated feature set
   - Clear boundaries

## Backup Strategies

### Strategy 1: Feature Flag Approach
```typescript
const useLoginPage = () => {
  const featureFlags = useFeatureFlags();
  return featureFlags.newClubhouseStructure 
    ? NewLoginPage 
    : LegacyLoginPage;
};
```

### Strategy 2: Route Aliasing
Keep both old and new routes active during transition:
```typescript
// Support both paths temporarily
<Route path="/login" component={LegacyLogin} />
<Route path="/clubhouse/login" component={NewLogin} />
```

### Strategy 3: Gradual Migration
1. Move low-risk components first
2. Test thoroughly
3. Move medium-risk components
4. Move high-risk components last

## Validation Checklist

Before marking complete:
- [ ] Member dashboard loads with all widgets
- [ ] Member directory displays correctly
- [ ] Club announcements visible to members
- [ ] Member can view their teams (via Perform)
- [ ] Member can see evaluation summaries (via Assess)
- [ ] No admin functions accessible to regular members
- [ ] Clear navigation to other domains
- [ ] All domain boundaries respected
- [ ] Error states handled gracefully
- [ ] Performance targets met

## Playwright E2E Testing Requirements

### Test Structure
```
playwright/features/clubhouse/
├── fixtures/
│   ├── test-members.ts       # Player, parent, coach member data
│   ├── test-clubs.ts         # Sample club with branding
│   └── test-announcements.ts # Club news and updates
├── page-objects/
│   ├── member-dashboard.page.ts  # Member home experience
│   ├── directory.page.ts         # Member directory
│   ├── announcements.page.ts     # Club news
│   ├── community.page.ts         # Social features
│   └── welcome.page.ts           # Onboarding flow
├── member-journeys.spec.ts       # Complete member workflows
├── dashboard-widgets.spec.ts     # Dashboard component tests
├── directory-search.spec.ts      # Member directory features
├── announcement-viewing.spec.ts  # News and updates
└── domain-boundaries.spec.ts     # Verify no admin access
```

### Pre-Migration Tests (HIGH PRIORITY - MEDIUM RISK)
- [ ] **Capture all authentication flows**
  - Login with email/username
  - Password reset flow
  - Signup with verification
  - Social auth (if applicable)
  - Remember me functionality
- [ ] **Document role-based access patterns**
  - Admin dashboard content
  - Coach dashboard vs Player vs Parent
  - Permission boundaries
- [ ] **Record session management behavior**
  - Auto-logout timing
  - Token refresh
  - Cross-tab authentication

### During Migration Tests
```typescript
// Multi-login page verification during consolidation
test.describe('Login Consolidation', () => {
  test('All 5 login variants work before removal', async ({ page }) => {
    const loginPages = ['/login', '/updated-login', '/simple-login'];
    
    for (const path of loginPages) {
      await page.goto(path);
      await expect(page.locator('[data-testid="login-form"]')).toBeVisible();
      
      // Test successful login
      await page.fill('[data-testid="email-input"]', testUsers.coach.email);
      await page.fill('[data-testid="password-input"]', testUsers.coach.password);
      await page.click('[data-testid="login-button"]');
      await expect(page).toHaveURL('/dashboard');
      
      // Logout for next test
      await page.click('[data-testid="logout-button"]');
    }
  });
  
  // Route migration verification
  test('Old routes redirect to new Clubhouse structure', async ({ page }) => {
    const redirects = [
      { from: '/home', to: '/clubhouse/dashboard' },
      { from: '/account', to: '/clubhouse/profile' },
      { from: '/admin', to: '/clubhouse/admin' }
    ];
    
    for (const redirect of redirects) {
      await page.goto(redirect.from);
      await expect(page).toHaveURL(redirect.to);
    }
  });
});

// Auth state persistence during migration
test('Authentication persists through route changes', async ({ page }) => {
  // Login with old route
  await page.goto('/login');
  await loginAsUser(page, testUsers.coach);
  
  // Navigate using new routes
  await page.goto('/clubhouse/dashboard');
  await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  
  // Verify no re-authentication required
  await expect(page.locator('[data-testid="login-form"]')).not.toBeVisible();
});
```

### Post-Migration Tests
- [ ] **Complete authentication regression**
  - All login methods work from consolidated page
  - Password reset sends emails correctly
  - Signup creates accounts properly
  - Role assignment works correctly
- [ ] **Admin feature accessibility**
  - All admin tools accessible from new structure
  - Permission checks still enforce correctly
  - Bulk operations work
- [ ] **Dashboard role verification**
  - Each role sees appropriate content
  - Navigation reflects user permissions
  - Data loads correctly for all roles

### Critical User Journeys (MEDIUM RISK)

1. **New Member Welcome Experience**
   ```
   First Visit → View Club Info → Join Club → Welcome Flow → Setup Profile → Member Dashboard
   Target: < 5 min total, engaging club-branded experience
   ```

2. **Daily Member Check-in**
   ```
   Login → Member Dashboard → Check Announcements → View Schedule → RSVP to Event
   Target: < 30s to reach dashboard, personalized content visible
   ```

3. **Find and Connect with Members**
   ```
   Dashboard → Member Directory → Search/Filter → View Profile → Send Message
   Target: < 1 min to find specific member, intuitive search
   ```

4. **View My Teams and Evaluations**
   ```
   Dashboard → My Teams Widget → Select Team → Navigate to Perform
   Dashboard → My Evaluations → View Summary → Navigate to Assess
   Target: Seamless cross-domain navigation
   ```

### Test Data Requirements
```typescript
// Essential test members for Clubhouse
export const clubhouseTestMembers = {
  activeMember: {
    email: '<EMAIL>',
    password: 'Member123!',
    membershipStatus: 'active',
    teams: ['team-001', 'team-002'],
    joinedDate: '2023-01-15'
  },
  newMember: {
    email: '<EMAIL>',
    password: 'NewMember123!',
    membershipStatus: 'pending',
    needsOnboarding: true,
    joinedDate: 'today'
  },
  parentMember: {
    email: '<EMAIL>',
    password: 'Parent123!',
    membershipStatus: 'active',
    children: ['<EMAIL>', '<EMAIL>'],
    familyAccount: true
  },
  coachMember: {
    email: '<EMAIL>',
    password: 'Coach123!',
    membershipStatus: 'active',
    teams: ['team-001'],
    roles: ['member', 'coach']
  },
  inactiveMember: {
    email: '<EMAIL>',
    password: 'Inactive123!',
    membershipStatus: 'expired',
    lastActiveDate: '2023-06-30'
  }
};

// Test scenarios for different auth states
export const authScenarios = {
  firstLogin: { hasCompletedOnboarding: false },
  returningUser: { hasCompletedOnboarding: true, lastLogin: '2023-12-01' },
  expiredSession: { sessionExpired: true },
  multipleDevices: { activeSessionsCount: 3 }
};
```

### Performance Benchmarks (Critical for UX)
- Login page load: < 1s
- Login submission: < 2s
- Dashboard load after login: < 1.5s
- Role switching: < 500ms
- Profile updates: < 1s

### Domain Boundary Testing
```typescript
test.describe('Clubhouse Domain Boundaries', () => {
  test('Members cannot access Control area functions', async ({ page }) => {
    await loginAsUser(page, testMembers.activeMember);
    
    // Should not see admin options
    await page.goto('/clubhouse/dashboard');
    await expect(page.locator('[data-testid="admin-panel"]')).not.toBeVisible();
    await expect(page.locator('[data-testid="billing-management"]')).not.toBeVisible();
    
    // Direct navigation should redirect
    await page.goto('/control/admin');
    await expect(page).toHaveURL('/clubhouse/dashboard');
  });
  
  test('Clubhouse correctly delegates to other domains', async ({ page }) => {
    await loginAsUser(page, testMembers.activeMember);
    
    // Click on team should navigate to Perform
    await page.click('[data-testid="my-teams-widget"] [data-testid="team-link"]');
    await expect(page).toHaveURL(/^\/perform\/team\//);
    
    // Click on evaluation should navigate to Assess
    await page.goBack();
    await page.click('[data-testid="my-evaluations-widget"] [data-testid="view-details"]');
    await expect(page).toHaveURL(/^\/assess\//);
  });
  
  test('Member data is read-only in Clubhouse', async ({ page }) => {
    await loginAsUser(page, testMembers.activeMember);
    
    // Profile displays but edit redirects to Identity
    await page.goto('/clubhouse/profile');
    await expect(page.locator('[data-testid="member-info"]')).toBeVisible();
    await page.click('[data-testid="edit-profile-button"]');
    await expect(page).toHaveURL('/identity/profile/edit');
  });
});
```

### Rollback Test Coverage
```typescript
test.describe('Clubhouse Rollback Safety', () => {
  test('Can revert to old login system without user disruption', async ({ page }) => {
    // Create session in new system
    await page.goto('/clubhouse/login');
    await loginAsUser(page, testUsers.coach);
    
    // Simulate rollback to old routes
    await page.goto('/login'); // Old route
    
    // Should still be authenticated
    await page.goto('/home'); // Old home route
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  });
  
  test('No duplicate user accounts after rollback', async ({ page }) => {
    // Test that rollback doesn't cause auth state corruption
    // Check database consistency
    // Verify no duplicate sessions
  });
});
```

### Feature Flag Testing
```typescript
// Support for gradual rollout
test.describe('Clubhouse Feature Flags', () => {
  test('Old structure when flag disabled', async ({ page }) => {
    await page.addInitScript(() => {
      localStorage.setItem('feature_flags', JSON.stringify({
        newClubhouseStructure: false
      }));
    });
    
    await page.goto('/home');
    await expect(page.locator('[data-testid="legacy-dashboard"]')).toBeVisible();
  });
  
  test('New structure when flag enabled', async ({ page }) => {
    await page.addInitScript(() => {
      localStorage.setItem('feature_flags', JSON.stringify({
        newClubhouseStructure: true
      }));
    });
    
    await page.goto('/home');
    await expect(page.locator('[data-testid="new-dashboard"]')).toBeVisible();
  });
});
```

## Rollback Plan

If critical issues discovered:
1. **Immediate**: Revert route configuration to original
2. **Short-term**: Keep both structures, use feature flags
3. **Long-term**: Plan smaller incremental moves

## Dependencies on Other Areas

### Core Dependencies
- **Club Core Module**: Shared club entities and context (to be created)
- **Identity**: Authentication and user profiles  
- **Perform**: Team data for member's teams widget
- **Assess**: Evaluation summaries for dashboard
- **Schedule**: Event data for member schedules

### Data Flow Dependencies
- **FROM Identity**: User authentication state, profile data
- **FROM Perform**: Member's team assignments and roles
- **FROM Assess**: Member's evaluation summaries
- **FROM Schedule**: Upcoming events for member
- **TO Control**: Navigation only (no data writes)

### No Dependencies On
- **Control**: Clubhouse must not depend on Control area
- **Admin Services**: No direct admin service calls
- **Platform Features**: Uses only member-scoped APIs

## Success Metrics

- Clear separation between member and admin features
- All member journeys work seamlessly
- Domain boundaries properly enforced
- Cross-domain navigation intuitive
- Member engagement metrics improved
- Page load times < 2s for dashboard
- Zero security boundary violations
- Positive member feedback on experience