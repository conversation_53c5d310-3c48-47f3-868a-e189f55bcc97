# Pulse Area Implementation Plan

## Overview
Pulse is the communication and content hub, handling news feeds, articles, videos, notifications, and future messaging features. The area has already undergone a v1 to v2 migration with PulseV2 being the active implementation.

## Scope Definition
**Pulse includes:**
- News feed and article management
- Content synchronization from external sources
- Video library and player
- Notification system (SMS, push, in-app)
- Social interactions (likes, bookmarks, shares)
- Tag following and content filtering
- Future: Messaging and team communications
- Future: Comments and user-generated content

**Pulse excludes:**
- Performance metrics (belongs to Perform)
- Product announcements (belongs to Locker)
- Administrative announcements (belongs to Clubhouse)

## Current State Assessment
✅ **Fully Implemented:**
- Article feed with search and filters
- Interactive features (like, bookmark, share)
- Tag following system
- Content caching and optimization
- Video library
- Basic notifications

🔧 **Partially Implemented:**
- Content management (admin only)
- SMS notifications (limited use)

❌ **Not Implemented:**
- Direct messaging
- Comments on articles
- User-generated content
- Team communications

## Implementation Steps

### Phase 1: Directory Structure Setup
Create the following structure:
```
src/features/pulse/
├── components/
│   ├── articles/
│   ├── notifications/
│   ├── videos/
│   └── messaging/ (future)
├── pages/
│   ├── feed/
│   ├── articles/
│   ├── videos/
│   ├── notifications/
│   └── messaging/ (future)
├── hooks/
├── services/
├── types/
└── index.ts
```

### Phase 2: File Consolidation and Movement

```
FROM → TO

Core Pages:
src/pages/PulseV2.tsx → features/pulse/pages/feed/PulseFeed.tsx
src/pages/Pulse.tsx → DELETE (deprecated)
src/pages/PulseComparison.tsx → DELETE (no longer needed)
src/pages/Videos.tsx → features/pulse/pages/videos/VideoLibrary.tsx
src/pages/VideoPlayer.tsx → features/pulse/pages/videos/VideoPlayer.tsx

Hooks:
src/hooks/usePulseContent.ts → features/pulse/hooks/usePulseContent.ts
src/hooks/useVideos.ts → features/pulse/hooks/useVideos.ts
src/pages/v2/pulse/hooks/* → features/pulse/hooks/*

Components:
src/components/ContentCard/* → features/pulse/components/articles/*
src/pages/v2/DesignSystem/sections/PulseSection.tsx → Keep in design system
src/components/shadow/ShadowNotificationModal.tsx → features/pulse/components/notifications/

Services:
src/services/NotificationService.ts → features/pulse/services/NotificationService.ts
src/services/SmsNotificationService.ts → features/pulse/services/SmsNotificationService.ts

Admin Tools:
src/pages/admin/ContentSyncDashboard.tsx → features/pulse/pages/admin/ContentSync.tsx
src/pages/admin/WidgetBuilder.tsx → features/pulse/pages/admin/WidgetBuilder.tsx

Future Messaging:
src/pages/section/Coach/TeamComms/* → features/pulse/pages/messaging/* (when implemented)
```

### Phase 3: Route Updates

```typescript
// Update routes in AppRoutes.tsx
const PulseRoutes = {
  feed: '/pulse',
  article: '/pulse/article/:id',
  videos: '/pulse/videos',
  video: '/pulse/video/:id',
  notifications: '/pulse/notifications',
  messaging: '/pulse/messages' // future
};

// Remove deprecated route
// DELETE: <Route path="/pulse-old" component={Pulse} />
```

### Phase 4: Clean Up Deprecated Code

1. Remove old Pulse.tsx implementation
2. Remove comparison pages
3. Update all imports to use PulseV2 logic
4. Clean up unused Google API fetching code

## Risk Assessment

### Low Risk Areas

1. **File Movement (LOW RISK)**
   - **Risk**: Well-isolated functionality
   - **Impact**: Limited to content features
   - **Mitigation**: 
     - Test article loading after move
     - Verify interactions still work
   - **Backup Plan**: 
     - Routes can redirect if needed

2. **Removing Deprecated Pulse.tsx (LOW RISK)**
   - **Risk**: Already replaced by PulseV2
   - **Impact**: None if routes updated
   - **Mitigation**: 
     - Verify no remaining references
   - **Backup Plan**: 
     - Keep file but redirect internally

### Medium Risk Areas

1. **Notification Service (MEDIUM RISK)**
   - **Risk**: Used across multiple features
   - **Impact**: Notifications stop working
   - **Mitigation**: 
     - Map all notification consumers
     - Test SMS and push notifications
   - **Backup Plan**: 
     - Keep service in shared location temporarily
     - Use service locator pattern

2. **Content Sync Dashboard (MEDIUM RISK)**
   - **Risk**: Critical for content updates
   - **Impact**: No new articles
   - **Mitigation**: 
     - Test sync process thoroughly
     - Verify admin access
   - **Backup Plan**: 
     - Manual sync option
     - Keep backup of sync logic

## Backup Strategies

### Strategy 1: Gradual Deprecation
```typescript
// Temporarily support both routes
<Route path="/pulse" component={PulseV2} />
<Route path="/pulse-v1" component={Pulse} /> // Remove after validation
```

### Strategy 2: Service Abstraction
```typescript
// Abstract notification service location
export const useNotifications = () => {
  // Can switch between implementations
  return NotificationService;
};
```

### Strategy 3: Content Fallback
```typescript
// Fallback for content loading
const usePulseContent = () => {
  try {
    return useSupabasePulse();
  } catch {
    return useGoogleApiPulse(); // Emergency fallback
  }
};
```

## Validation Checklist

Before marking complete:
- [ ] Article feed loads correctly
- [ ] Search and filters work
- [ ] Like/bookmark/share functions work
- [ ] SP points awarded for interactions
- [ ] Videos load and play
- [ ] Notifications deliver (SMS)
- [ ] Content sync dashboard accessible
- [ ] No console errors
- [ ] All imports updated
- [ ] Old Pulse.tsx removed

## Playwright E2E Testing Requirements (LOW RISK - 40% Coverage)

### Test Structure
```
playwright/features/pulse/
├── fixtures/
│   ├── test-articles.ts      # Sample articles and content
│   ├── test-videos.ts        # Video library content
│   ├── test-notifications.ts # Notification scenarios
│   └── test-users.ts         # User interaction patterns
├── page-objects/
│   ├── pulse-feed.page.ts    # Main feed interactions
│   ├── article.page.ts       # Article reading and sharing
│   ├── video-library.page.ts # Video browsing
│   ├── video-player.page.ts  # Video playback
│   └── notifications.page.ts # Notification center
├── content-consumption.spec.ts # Reading and viewing flows
├── social-interactions.spec.ts # Like, bookmark, share features
├── video-playback.spec.ts      # Video streaming and controls
├── notification-delivery.spec.ts # Push and SMS notifications
├── content-sync.spec.ts        # Admin content management
└── sp-points-rewards.spec.ts   # Rewards for engagement
```

### Pre-Migration Tests (LOW RISK)
- [ ] **Content feed baseline**
  - Article loading performance
  - Search and filter functionality
  - Social interaction buttons work
  - SP points awarded correctly
- [ ] **Video system baseline**
  - Video library browsing
  - Video player functionality
  - Playback controls and quality
- [ ] **Notification system baseline**
  - SMS notification delivery
  - Push notification display
  - In-app notification center

### During Migration Tests
```typescript
// PulseV2 migration verification
test.describe('Pulse V1 to V2 Migration', () => {
  test('Old and new Pulse feeds show same content', async ({ page }) => {
    // Test old pulse implementation
    await page.goto('/pulse-v1');
    const oldArticles = await page.locator('[data-testid="article-card"]').all();
    const oldTitles = await Promise.all(
      oldArticles.map(article => article.locator('[data-testid="article-title"]').textContent())
    );
    
    // Test new pulse implementation
    await page.goto('/pulse');
    const newArticles = await page.locator('[data-testid="article-card"]').all();
    const newTitles = await Promise.all(
      newArticles.map(article => article.locator('[data-testid="article-title"]').textContent())
    );
    
    expect(oldTitles.sort()).toEqual(newTitles.sort());
  });
  
  // Social interaction consistency
  test('Like/bookmark state preserved during migration', async ({ page }) => {
    await loginAsUser(page, testUsers.member);
    
    // Like article in old system
    await page.goto('/pulse-v1');
    await page.click('[data-testid="like-button-article-123"]');
    await expect(page.locator('[data-testid="like-button-article-123"]')).toHaveClass(/active/);
    
    // Check state in new system
    await page.goto('/pulse');
    await expect(page.locator('[data-testid="like-button-article-123"]')).toHaveClass(/active/);
  });
});

// Content sync during migration
test('Content sync continues during migration', async ({ page }) => {
  await loginAsUser(page, testUsers.admin);
  
  // Access content sync in old location
  await page.goto('/admin/content-sync');
  await page.click('[data-testid="sync-now-button"]');
  await expect(page.locator('[data-testid="sync-status"]')).toContainText('Success');
  
  // Verify sync works in new location
  await page.goto('/pulse/admin/content-sync');
  await expect(page.locator('[data-testid="last-sync-time"]')).toBeVisible();
});
```

### Post-Migration Tests
- [ ] **Complete content consumption regression**
  - Article reading flow works
  - Search finds relevant content
  - Social interactions functional
  - SP points awarded correctly
- [ ] **Video system functionality**
  - Video library accessible
  - Video player loads and plays
  - Quality controls work
- [ ] **Notification system verification**
  - Notifications reach users
  - Settings can be configured
  - Delivery preferences respected

### Critical User Journeys (LOW RISK - High Engagement)

1. **Daily Content Consumption**
   ```
   Open App → Browse Feed → Read Article → Like → Bookmark → Share → Earn SP
   Target: < 2 min to consume content, SP points awarded within 5s
   ```

2. **Video Learning Session**
   ```
   Browse Video Library → Select Video → Watch → Take Notes → 
   Related Content → Bookmark for Later
   Target: Seamless playback, no buffering interruptions
   ```

3. **Content Discovery**
   ```
   Search for Topic → Filter Results → Follow Tags → 
   Receive Notifications → Engage with Content
   Target: Relevant results in < 1s, accurate filtering
   ```

4. **Social Engagement**
   ```
   Read Article → React (Like) → Share with Team → 
   Check Engagement Stats → View SP Points Earned
   Target: Instant reactions, sharing works across platforms
   ```

### Test Data Requirements
```typescript
// Content and engagement test data
export const pulseTestData = {
  articles: {
    featured: {
      id: 'article-001',
      title: 'Training Techniques for Young Athletes',
      tags: ['training', 'youth', 'development'],
      likes: 0,
      bookmarks: 0,
      spPointsValue: 10
    },
    trending: {
      id: 'article-002',
      title: 'Nutrition for Peak Performance',
      tags: ['nutrition', 'performance', 'health'],
      likes: 25,
      bookmarks: 12,
      spPointsValue: 15
    }
  },
  
  videos: {
    technique: {
      id: 'video-001',
      title: 'Advanced Passing Techniques',
      duration: '10:30',
      quality: ['720p', '1080p'],
      tags: ['technique', 'passing', 'skills']
    },
    match: {
      id: 'video-002',
      title: 'Champions League Final Highlights',
      duration: '25:45',
      quality: ['480p', '720p', '1080p'],
      tags: ['match', 'highlights', 'champions-league']
    }
  },
  
  notifications: {
    newContent: {
      type: 'new_article',
      title: 'New Training Article Available',
      message: 'Check out the latest training techniques',
      targetAudience: ['coaches', 'players']
    },
    spPointsEarned: {
      type: 'sp_points',
      title: 'SP Points Earned!',
      message: 'You earned 10 SP points for reading an article',
      points: 10
    }
  }
};

// User engagement patterns
export const engagementScenarios = {
  highEngagement: {
    articlesPerWeek: 5,
    videosPerWeek: 3,
    likesPerWeek: 8,
    sharesPerWeek: 2,
    expectedSP: 150
  },
  lowEngagement: {
    articlesPerWeek: 1,
    videosPerWeek: 0,
    likesPerWeek: 1,
    sharesPerWeek: 0,
    expectedSP: 20
  }
};
```

### Performance Benchmarks (Critical for Content UX)
- Feed load time: < 1s
- Article open: < 500ms
- Video start time: < 2s
- Search results: < 800ms
- Social action response: < 200ms

### Social Interaction Testing
```typescript
test.describe('Social Features', () => {
  test('Like button provides immediate feedback', async ({ page }) => {
    await loginAsUser(page, testUsers.member);
    await page.goto('/pulse');
    
    // Click like button
    const likeButton = page.locator('[data-testid="like-button-article-001"]');
    await likeButton.click();
    
    // Should show immediate visual feedback
    await expect(likeButton).toHaveClass(/liked/);
    
    // SP points should be awarded
    await expect(page.locator('[data-testid="sp-notification"]')).toContainText('10 SP points');
  });
  
  test('Bookmark saves for later viewing', async ({ page }) => {
    await loginAsUser(page, testUsers.member);
    await page.goto('/pulse');
    
    // Bookmark an article
    await page.click('[data-testid="bookmark-button-article-002"]');
    
    // Navigate to bookmarks page
    await page.goto('/pulse/bookmarks');
    await expect(page.locator('[data-testid="bookmarked-article-002"]')).toBeVisible();
  });
  
  test('Share functionality works across platforms', async ({ page }) => {
    await page.goto('/pulse/article/article-001');
    
    // Test share button opens share modal
    await page.click('[data-testid="share-button"]');
    await expect(page.locator('[data-testid="share-modal"]')).toBeVisible();
    
    // Test different share options
    await expect(page.locator('[data-testid="share-whatsapp"]')).toBeVisible();
    await expect(page.locator('[data-testid="share-email"]')).toBeVisible();
    await expect(page.locator('[data-testid="copy-link"]')).toBeVisible();
  });
});
```

### Video System Testing
```typescript
test.describe('Video Functionality', () => {
  test('Video player loads and plays smoothly', async ({ page }) => {
    await page.goto('/pulse/videos');
    
    // Select video
    await page.click('[data-testid="video-card-video-001"]');
    
    // Video should load
    await expect(page.locator('video')).toBeVisible();
    
    // Play controls should work
    await page.click('[data-testid="play-button"]');
    await expect(page.locator('video')).toHaveAttribute('paused', 'false');
  });
  
  test('Video quality can be adjusted', async ({ page }) => {
    await page.goto('/pulse/video/video-001');
    
    // Open quality settings
    await page.click('[data-testid="quality-settings"]');
    
    // Change to 1080p
    await page.click('[data-testid="quality-1080p"]');
    
    // Should change video source
    const videoSrc = await page.locator('video').getAttribute('src');
    expect(videoSrc).toContain('1080p');
  });
});
```

### SP Points Integration Testing
```typescript
test.describe('SP Points Rewards', () => {
  test('Points awarded for content engagement', async ({ page }) => {
    await loginAsUser(page, testUsers.member);
    
    // Check initial SP balance
    await page.goto('/profile');
    const initialPoints = await page.locator('[data-testid="sp-balance"]').textContent();
    
    // Read an article (should award 10 SP)
    await page.goto('/pulse/article/article-001');
    await page.waitForTimeout(30000); // Simulate reading time
    
    // Check SP balance increased
    await page.goto('/profile');
    const newPoints = await page.locator('[data-testid="sp-balance"]').textContent();
    
    expect(parseInt(newPoints || '0')).toBe(parseInt(initialPoints || '0') + 10);
  });
  
  test('Engagement activities tracked correctly', async ({ page }) => {
    await loginAsUser(page, testUsers.member);
    await page.goto('/pulse');
    
    // Perform various engagement activities
    await page.click('[data-testid="like-button-article-001"]'); // +5 SP
    await page.click('[data-testid="bookmark-button-article-001"]'); // +2 SP
    await page.click('[data-testid="share-button-article-001"]'); // +3 SP
    await page.click('[data-testid="share-whatsapp"]');
    
    // Check activity log
    await page.goto('/profile/activity');
    await expect(page.locator('[data-testid="activity-like"]')).toBeVisible();
    await expect(page.locator('[data-testid="activity-bookmark"]')).toBeVisible();
    await expect(page.locator('[data-testid="activity-share"]')).toBeVisible();
  });
});
```

### Notification System Testing
```typescript
test.describe('Notification Delivery', () => {
  test('Push notifications appear for new content', async ({ page, context }) => {
    // Grant notification permissions
    await context.grantPermissions(['notifications']);
    
    await loginAsUser(page, testUsers.member);
    await page.goto('/pulse/settings');
    
    // Enable notifications
    await page.check('[data-testid="enable-content-notifications"]');
    
    // Simulate new content being published
    await simulateNewContentPublished();
    
    // Should see notification
    await expect(page.locator('[data-testid="notification-toast"]')).toBeVisible();
  });
  
  // Note: SMS testing would require test phone numbers or mock service
  test('SMS notification preferences respected', async ({ page }) => {
    await loginAsUser(page, testUsers.member);
    await page.goto('/pulse/settings');
    
    // Disable SMS for content updates
    await page.uncheck('[data-testid="sms-content-updates"]');
    await page.click('[data-testid="save-preferences"]');
    
    // Preferences should be saved
    await page.reload();
    await expect(page.locator('[data-testid="sms-content-updates"]')).not.toBeChecked();
  });
});
```

### Content Management Testing (Admin)
```typescript
test.describe('Admin Content Management', () => {
  test('Content sync dashboard accessible and functional', async ({ page }) => {
    await loginAsUser(page, testUsers.admin);
    await page.goto('/pulse/admin/content-sync');
    
    // Should show sync status
    await expect(page.locator('[data-testid="last-sync-status"]')).toBeVisible();
    
    // Should be able to trigger manual sync
    await page.click('[data-testid="manual-sync-button"]');
    await expect(page.locator('[data-testid="sync-in-progress"]')).toBeVisible();
    
    // Should complete successfully
    await expect(page.locator('[data-testid="sync-complete"]')).toBeVisible({ timeout: 30000 });
  });
});
```

### Rollback Test Coverage
```typescript
test.describe('Pulse Migration Rollback', () => {
  test('Can revert to PulseV1 if needed', async ({ page }) => {
    // Test that V1 fallback works
    await page.addInitScript(() => {
      localStorage.setItem('use_pulse_v1', 'true');
    });
    
    await page.goto('/pulse');
    
    // Should load old interface
    await expect(page.locator('[data-testid="pulse-v1-interface"]')).toBeVisible();
    
    // Content should still be accessible
    await expect(page.locator('[data-testid="article-list"]')).toBeVisible();
  });
});
```

## Rollback Plan

If issues discovered:

### Immediate (< 15 min)
1. Revert route changes
2. Re-enable old Pulse page
3. Clear caches

### Short-term (< 1 hour)
1. Use route redirects
2. Keep both implementations
3. Monitor for issues

## Dependencies on Other Areas

- **Perform**: SP points for interactions
- **Clubhouse**: User profiles for content
- **Shared**: Authentication for personalization
- **Future**: Integration with all areas for notifications

## Success Metrics

- No disruption to content consumption
- Interaction rates maintained
- Page load times < 100ms
- Content sync continues working
- Clean code structure
- Ready for messaging features

## Future Enhancements

### Phase 1: Messaging Foundation
1. Design message data model
2. Create chat UI components
3. Implement real-time updates
4. Add notification integration

### Phase 2: Enhanced Social Features
1. Comments on articles
2. User-generated content
3. Content moderation tools
4. Social sharing improvements

### Phase 3: Advanced Features
1. AI-powered content recommendations
2. Personalized feeds
3. Multi-language support
4. Advanced analytics

## Special Considerations

1. **Performance**: Maintain sub-100ms load times
2. **Real-time**: Plan for WebSocket integration
3. **Moderation**: Build moderation tools early
4. **Scale**: Design for high-volume messaging
5. **Privacy**: Ensure GDPR compliance

## Notes

- Pulse has the cleanest migration path
- Main challenge is future messaging system
- Consider using Supabase Realtime for chat
- Plan for push notification integration
- Think about content moderation early