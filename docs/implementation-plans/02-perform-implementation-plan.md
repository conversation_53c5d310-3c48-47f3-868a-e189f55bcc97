# Perform Area Implementation Plan

**UPDATED**: This plan has been revised to focus Perform purely on sports performance and training, with evaluations moved to Assess area and events moved to Schedule area. This reduces complexity and risk.

## Overview
Perform is the core sports performance tracking area, focusing on training, performance metrics, and athletic development. With evaluations and scheduling moved to separate areas, Perform can focus purely on the athletic performance aspects.

## Scope Definition
**Perform NOW includes:**
- Training plans and session management
- Performance metrics and analytics
- Sport-specific training programs
- Player performance dashboards
- Training history and progress
- Performance goals and achievements
- Match performance analysis

**MOVED to other areas:**
- Player evaluations → Assess area
- Event scheduling → Schedule area
- Team management → Split between Clubhouse (club level) and Perform (training focus)
- Weekly evaluations → Assess area
- Event evaluations → Assess area
- Individual Development Plans → Assess area

**Perform excludes:**
- All evaluation features (moved to Assess)
- Event/calendar management (moved to Schedule)
- Club administration (stays in Clubhouse)
- E-commerce features (stays in Locker)
- Social/communication features (stays in Pulse)

## Implementation Steps

### Phase 1: Directory Structure Setup
Create the following structure:
```
src/features/perform/
├── components/
│   ├── evaluations/
│   ├── events/
│   ├── players/
│   ├── teams/
│   └── training/
├── pages/
│   ├── dashboard/
│   │   ├── CoachDashboard.tsx
│   │   ├── PlayerDashboard.tsx
│   │   ├── ParentDashboard.tsx
│   │   └── MemberDashboard.tsx
│   ├── evaluations/
│   ├── events/
│   ├── players/
│   ├── teams/
│   └── training/
├── hooks/
├── services/
├── types/
└── index.ts
```

### Phase 2: File Consolidation - MAJOR OPERATION

#### Critical Consolidation: Coach Section → Perform
**This is the highest risk operation** - 90% of Coach functionality moves to Perform

```
FROM → TO

Team Management:
src/pages/section/Coach/TeamFlat.tsx → features/perform/pages/teams/TeamList.tsx
src/pages/section/Coach/EditTeam.tsx → features/perform/pages/teams/EditTeam.tsx
src/pages/section/Coach/TeamObjectives.tsx → features/perform/pages/teams/TeamObjectives.tsx
src/pages/section/Coach/TeamStats/* → features/perform/pages/teams/stats/*
src/pages/section/Coach/TeamEvaluation.tsx → features/perform/pages/teams/TeamEvaluation.tsx
src/pages/section/Coach/components/team/* → features/perform/components/teams/*

Player Management:
src/pages/section/Coach/PlayerManagement.tsx → features/perform/pages/players/PlayerManagement.tsx
src/pages/section/Coach/PlayerDetailPage.tsx → features/perform/pages/players/PlayerDetails.tsx
src/pages/section/Coach/PlayerObjectives.tsx → features/perform/pages/players/PlayerObjectives.tsx
src/pages/section/Player/* → features/perform/pages/players/*
src/components/v2/player/* → features/perform/components/players/*

Event Management:
src/pages/section/Coach/EventManagement.tsx → features/perform/pages/events/EventManagement.tsx
src/pages/section/Coach/events/* → features/perform/pages/events/*
src/pages/section/Coach/createEvent.tsx → features/perform/pages/events/CreateEvent.tsx
src/pages/section/Coach/MatchSetup.tsx → features/perform/pages/events/MatchSetup.tsx

Training & Sessions:
src/pages/section/Coach/SessionPlanner.tsx → features/perform/pages/training/SessionPlanner.tsx
src/pages/section/Coach/SessionCalendar/* → features/perform/pages/training/calendar/*
src/pages/section/Coach/WeeklyEvaluation.tsx → features/perform/pages/training/WeeklyEvaluation.tsx

Evaluations:
src/pages/evaluation/* → features/perform/pages/evaluations/*
src/components/evaluation/* → features/perform/components/evaluations/*
All pre/post evaluation components → features/perform/components/evaluations/*

Role-Based Dashboards:
src/pages/v2/perform/* → features/perform/pages/dashboard/*
```

### Phase 3: Route and Navigation Updates

#### Major Route Changes
```typescript
// OLD Coach Routes (REMOVE)
/coach/teams → /perform/teams
/coach/players → /perform/players
/coach/events → /perform/events
/coach/training → /perform/training

// NEW Perform Routes
/perform → Main perform dashboard (role-based)
/perform/teams/* → Team management
/perform/players/* → Player management
/perform/events/* → Event management
/perform/training/* → Training management
/perform/evaluations/* → Evaluation system
```

## Risk Assessment - CRITICAL

### EXTREME RISK Areas

1. **Coach Section Migration (EXTREME RISK)**
   - **Risk**: Moving 100+ files from Coach to Perform
   - **Impact**: Complete breakdown of coach functionality
   - **Why High Risk**:
     - Extensive interconnected components
     - Complex role-based permissions
     - Database queries assume coach context
     - Navigation deeply integrated
   - **Mitigation**:
     - Create complete dependency map first
     - Move in small batches
     - Test each batch thoroughly
     - Keep parallel systems temporarily
   - **Backup Plan**:
     - Phase 1: Copy files (don't move)
     - Phase 2: Update imports gradually
     - Phase 3: Remove old files only after validation
     - Emergency: Route aliasing to old paths

2. **Role-Based Access (HIGH RISK)**
   - **Risk**: Breaking role permissions
   - **Impact**: Users see wrong data or lose access
   - **Mitigation**:
     - Map all role checks before moving
     - Create comprehensive role tests
     - Test each role thoroughly
   - **Backup Plan**:
     - Keep old permission checks
     - Use feature flags per role

3. **Database Dependencies (HIGH RISK)**
   - **Risk**: Queries expect coach-centric structure
   - **Impact**: Data not loading, errors
   - **Mitigation**:
     - Audit all database queries
     - Update service layer carefully
   - **Backup Plan**:
     - Keep service layer unchanged initially
     - Create adapter layer if needed

### Medium Risk Areas

1. **Navigation System**
   - Updates needed throughout app
   - Backup: Route redirects

2. **Import Dependencies**
   - Hundreds of imports to update
   - Backup: Automated refactoring tools

## Detailed Backup Strategies

### Strategy 1: Parallel Implementation
```typescript
// Run both systems temporarily
const useTeamManagement = () => {
  const { migrateToPerform } = useFeatureFlags();
  
  if (migrateToPerform) {
    return import('features/perform/pages/teams/TeamManagement');
  }
  return import('pages/section/Coach/TeamManagement');
};
```

### Strategy 2: Progressive Migration
1. **Week 1**: Copy (don't move) Coach files to Perform
2. **Week 2**: Update imports to use Perform copies
3. **Week 3**: Test thoroughly with both active
4. **Week 4**: Remove Coach files after validation

### Strategy 3: Route Preservation
```typescript
// Keep old routes working during transition
<Route path="/coach/teams" component={RedirectToPerform} />
<Route path="/perform/teams" component={TeamManagement} />
```

### Strategy 4: Service Layer Abstraction
```typescript
// Abstract data layer to work with both structures
class PerformService {
  async getTeams(userId: string) {
    try {
      // Try new structure
      return await this.getPerformTeams(userId);
    } catch {
      // Fallback to old structure
      return await this.getCoachTeams(userId);
    }
  }
}
```

## Validation Checklist

Critical validations before considering complete:
- [ ] All coach features accessible via Perform
- [ ] Each role can access their dashboards
- [ ] Team management fully functional
- [ ] Player evaluations working
- [ ] Event scheduling operational
- [ ] Training sessions can be created/managed
- [ ] Historical data still accessible
- [ ] No broken navigation links
- [ ] Performance metrics loading
- [ ] Database queries working
- [ ] No console errors
- [ ] Role permissions correct

## Playwright E2E Testing Requirements (EXTREME RISK - 80% Coverage)

### Test Structure
```
playwright/features/perform/
├── fixtures/
│   ├── test-teams.ts           # Sample teams and players
│   ├── test-evaluations.ts     # Evaluation templates and data
│   ├── test-events.ts          # Training sessions and matches
│   ├── test-users.ts           # Coach, player, parent test accounts
│   └── test-objectives.ts      # Team and player goals
├── page-objects/
│   ├── coach-dashboard.page.ts    # Coach main dashboard
│   ├── team-management.page.ts    # Team CRUD operations
│   ├── player-detail.page.ts      # Individual player management
│   ├── evaluation.page.ts         # Create/manage evaluations
│   ├── event-management.page.ts   # Training and match scheduling
│   ├── training-planner.page.ts   # Session planning
│   └── reporting.page.ts          # Performance analytics
├── coach-workflows.spec.ts         # Core coach user journeys
├── evaluation-flows.spec.ts        # Complete evaluation cycles
├── team-management.spec.ts         # Team CRUD and roster management
├── event-scheduling.spec.ts        # Training and match creation
├── role-permissions.spec.ts        # Multi-role access testing
├── data-integrity.spec.ts          # Historical data preservation
├── migration-parallel.spec.ts      # Old vs new system comparison
└── performance-benchmarks.spec.ts  # Load time and responsiveness
```

### Pre-Migration Tests (CRITICAL - EXTREME RISK)
- [ ] **Complete coach workflow mapping**
  - Weekly team management routine
  - Evaluation creation and distribution process
  - Event scheduling and communication
  - Player objective setting and tracking
  - Performance report generation
- [ ] **Historical data verification**
  - Access to past evaluations
  - Historical team rosters
  - Previous training session data
  - Player progression records
- [ ] **Role-based access documentation**
  - Coach permissions per team
  - Player access to own data
  - Parent visibility of children
  - Admin override capabilities

### During Migration Tests (PARALLEL SYSTEM VERIFICATION)
```typescript
// Critical parallel system testing
test.describe('Coach Section Migration', () => {
  test('Old /coach and new /perform routes return same data', async ({ page }) => {
    await loginAsUser(page, testUsers.coach);
    
    // Get team data from old route
    await page.goto('/coach/teams');
    const oldTeamData = await page.locator('[data-testid="team-list"]').textContent();
    
    // Get team data from new route
    await page.goto('/perform/teams');
    const newTeamData = await page.locator('[data-testid="team-list"]').textContent();
    
    expect(oldTeamData).toEqual(newTeamData);
  });
  
  // Evaluation system parallel test
  test('Can create evaluations in both old and new systems', async ({ page }) => {
    await loginAsUser(page, testUsers.coach);
    
    // Create evaluation in old system
    await page.goto('/coach/evaluations');
    await createTestEvaluation(page, 'Old System Test');
    
    // Verify it appears in new system
    await page.goto('/perform/evaluations');
    await expect(page.locator('[data-testid="evaluation-old-system-test"]')).toBeVisible();
    
    // Create evaluation in new system
    await createTestEvaluation(page, 'New System Test');
    
    // Verify it appears in old system
    await page.goto('/coach/evaluations');
    await expect(page.locator('[data-testid="evaluation-new-system-test"]')).toBeVisible();
  });
  
  // Navigation consistency test
  test('Navigation works between old and new structures', async ({ page }) => {
    await loginAsUser(page, testUsers.coach);
    
    // Start in old coach section
    await page.goto('/coach/teams');
    
    // Navigate to perform area
    await page.click('[data-testid="navigate-to-perform"]');
    await expect(page).toHaveURL('/perform/teams');
    
    // Data should be the same
    await expect(page.locator('[data-testid="team-count"]')).toBeVisible();
  });
});

// Database integrity during migration
test.describe('Data Consistency', () => {
  test('No evaluation data lost during migration', async ({ page }) => {
    await loginAsUser(page, testUsers.coach);
    
    // Count evaluations before migration
    await page.goto('/coach/evaluations');
    const beforeCount = await page.locator('[data-testid="evaluation-count"]').textContent();
    
    // Simulate migration checkpoint
    await page.goto('/perform/evaluations');
    const afterCount = await page.locator('[data-testid="evaluation-count"]').textContent();
    
    expect(beforeCount).toEqual(afterCount);
  });
  
  test('Team roster integrity maintained', async ({ page }) => {
    await loginAsUser(page, testUsers.coach);
    
    // Verify all players appear in new structure
    await page.goto('/perform/teams/test-team-001');
    
    const expectedPlayers = testData.teams.testTeam001.players;
    for (const player of expectedPlayers) {
      await expect(page.locator(`[data-testid="player-${player.id}"]`)).toBeVisible();
    }
  });
});
```

### Post-Migration Tests (FULL REGRESSION - EXTREME RISK)
- [ ] **Complete coach feature regression**
  - Every coach feature accessible from perform structure
  - All CRUD operations work (teams, players, evaluations, events)
  - Historical data loads correctly
  - Reports generate properly
- [ ] **Multi-role access verification**
  - Coaches see only their teams
  - Players access only permitted data
  - Parents see children's information
  - Admins have override access
- [ ] **Performance data integrity**
  - All evaluation scores preserved
  - Player progression data intact
  - Team statistics accurate
  - Historical comparisons work

### Critical User Journeys (EXTREME RISK - Must Work Flawlessly)

1. **Weekly Coach Evaluation Cycle**
   ```
   Login → Dashboard → Select Team → Create Pre-Evaluation → 
   Distribute to Players → Collect Responses → Review & Score → 
   Generate Reports → Plan Next Training
   Target: Complete cycle in < 15 min, no data loss
   ```

2. **Match Day Setup and Evaluation**
   ```
   Schedule Match → Set Objectives → Create Match Evaluation → 
   Live Scoring During Match → Post-Match Review → 
   Individual Player Feedback → Team Performance Report
   Target: Real-time data entry, immediate report generation
   ```

3. **Player Development Tracking**
   ```
   View Player Profile → Review Past Evaluations → 
   Set Development Goals → Create Training Plan → 
   Track Progress → Update Objectives → Parent Communication
   Target: Complete player review in < 10 min
   ```

4. **Team Season Management**
   ```
   Setup Team → Import Players → Create Season Objectives → 
   Schedule Training Sessions → Plan Match Calendar → 
   Track Team Progress → End Season Report
   Target: Season setup < 30 min, ongoing management < 5 min/week
   ```

### Test Data Requirements (Complex Multi-Entity)
```typescript
// Comprehensive test data for Perform area
export const performTestData = {
  teams: {
    testTeam001: {
      id: 'team-001',
      name: 'Test Lions U12',
      coachId: 'coach-001',
      players: ['player-001', 'player-002', 'player-003'],
      season: '2024',
      objectives: ['Improve passing accuracy', 'Team communication']
    }
  },
  
  evaluations: {
    preEvaluation: {
      id: 'pre-001',
      teamId: 'team-001',
      type: 'pre_match',
      criteria: ['technical', 'physical', 'mental', 'social'],
      status: 'active'
    },
    postEvaluation: {
      id: 'post-001',
      preEvaluationId: 'pre-001',
      type: 'post_match',
      scores: { 'player-001': 85, 'player-002': 92, 'player-003': 78 }
    }
  },
  
  events: {
    trainingSession: {
      id: 'event-001',
      type: 'training',
      teamId: 'team-001',
      date: '2024-01-15',
      objectives: ['Passing drills', 'Set pieces']
    },
    match: {
      id: 'event-002',
      type: 'match',
      teamId: 'team-001',
      opponent: 'Test Eagles U12',
      date: '2024-01-22'
    }
  },
  
  players: {
    player001: {
      id: 'player-001',
      name: 'Test Player One',
      teamIds: ['team-001'],
      parentId: 'parent-001',
      position: 'midfielder',
      evaluationHistory: ['pre-001', 'post-001']
    }
  }
};

// Complex workflow scenarios
export const coachWorkflows = {
  weeklyRoutine: {
    day1: 'Review previous week performance',
    day2: 'Plan training session',
    day3: 'Conduct training',
    day4: 'Create pre-match evaluation',
    day5: 'Match day evaluation',
    day6: 'Post-match analysis',
    day7: 'Player development planning'
  },
  
  evaluationCycle: {
    phases: ['create', 'distribute', 'collect', 'analyze', 'report', 'plan'],
    timing: '48 hours from creation to completion',
    participants: ['coach', 'players', 'parents']
  }
};
```

### Performance Benchmarks (Critical for Coach UX)
- Coach dashboard load: < 2s (complex data aggregation)
- Team list with stats: < 1.5s
- Player detail page: < 1s
- Evaluation creation: < 3s (form complexity)
- Report generation: < 5s (data processing)
- Live match scoring: < 500ms response

### Data Integrity Tests (Zero Tolerance for Loss)
```typescript
test.describe('Historical Data Preservation', () => {
  test('All past evaluations accessible after migration', async ({ page }) => {
    await loginAsUser(page, testUsers.coach);
    await page.goto('/perform/evaluations/history');
    
    // Check evaluations from each past season
    const seasons = ['2021', '2022', '2023', '2024'];
    for (const season of seasons) {
      await page.selectOption('[data-testid="season-filter"]', season);
      await expect(page.locator('[data-testid="evaluation-list"]')).toContainText(`${season}`);
    }
  });
  
  test('Player progression data spans full history', async ({ page }) => {
    await loginAsUser(page, testUsers.coach);
    await page.goto('/perform/players/player-001/progression');
    
    // Should show data from player's entire history
    await expect(page.locator('[data-testid="progression-chart"]')).toBeVisible();
    await expect(page.locator('[data-testid="first-evaluation-date"]')).toContainText('2022');
  });
  
  test('Team statistics include all historical data', async ({ page }) => {
    await loginAsUser(page, testUsers.coach);
    await page.goto('/perform/teams/team-001/statistics');
    
    // Verify multi-season statistics
    await expect(page.locator('[data-testid="all-time-stats"]')).toBeVisible();
    await expect(page.locator('[data-testid="season-comparison"]')).toBeVisible();
  });
});
```

### Security and Permission Tests (Multi-Role Complexity)
```typescript
test.describe('Role-Based Access Control', () => {
  test('Coach can only access assigned teams', async ({ page }) => {
    await loginAsUser(page, testUsers.coachLimitedAccess);
    await page.goto('/perform/teams');
    
    // Should only see assigned teams
    await expect(page.locator('[data-testid="team-list"]')).toContainText('Test Lions U12');
    await expect(page.locator('[data-testid="team-list"]')).not.toContainText('Test Eagles U12');
  });
  
  test('Player sees only own evaluation data', async ({ page }) => {
    await loginAsUser(page, testUsers.player);
    await page.goto('/perform/evaluations/my-evaluations');
    
    // Should only see own evaluations
    await expect(page.locator('[data-testid="evaluation-player-001"]')).toBeVisible();
    await expect(page.locator('[data-testid="evaluation-player-002"]')).not.toBeVisible();
  });
  
  test('Parent sees only children\'s data', async ({ page }) => {
    await loginAsUser(page, testUsers.parentWithTwoChildren);
    await page.goto('/perform/players');
    
    // Should see both children but no other players
    await expect(page.locator('[data-testid="child-player-001"]')).toBeVisible();
    await expect(page.locator('[data-testid="child-player-002"]')).toBeVisible();
    await expect(page.locator('[data-testid="other-player-003"]')).not.toBeVisible();
  });
});
```

### Rollback Test Coverage (Emergency Procedures)
```typescript
test.describe('Emergency Rollback Procedures', () => {
  test('Can immediately revert to coach structure', async ({ page }) => {
    // Test that emergency rollback preserves all functionality
    await page.addInitScript(() => {
      localStorage.setItem('emergency_rollback', 'true');
    });
    
    await page.goto('/coach/teams');
    await expect(page.locator('[data-testid="team-management"]')).toBeVisible();
    
    // All coach features should work
    await expect(page.locator('[data-testid="create-evaluation"]')).toBeEnabled();
    await expect(page.locator('[data-testid="schedule-training"]')).toBeEnabled();
  });
  
  test('No data corruption after rollback', async ({ page }) => {
    // Simulate rollback scenario
    await simulateRollback();
    
    await loginAsUser(page, testUsers.coach);
    await page.goto('/coach/evaluations');
    
    // All evaluations should still be accessible
    const evaluationCount = await page.locator('[data-testid="evaluation-count"]').textContent();
    expect(parseInt(evaluationCount)).toBeGreaterThan(0);
  });
});
```

### Load Testing for Coach Peak Usage
```typescript
// Simulate multiple coaches accessing system simultaneously
test.describe('Peak Usage Scenarios', () => {
  test('System handles 50 coaches creating evaluations simultaneously', async ({ browser }) => {
    const contexts = await Promise.all(
      Array(5).fill(null).map(() => browser.newContext())
    );
    
    const startTime = Date.now();
    
    await Promise.all(contexts.map(async (context, index) => {
      const page = await context.newPage();
      await loginAsUser(page, testUsers[`coach${index}`]);
      await page.goto('/perform/evaluations/create');
      await createTestEvaluation(page, `Load Test ${index}`);
    }));
    
    const duration = Date.now() - startTime;
    expect(duration).toBeLessThan(30000); // 30 second max
    
    // Cleanup
    await Promise.all(contexts.map(c => c.close()));
  });
});
```

## Emergency Rollback Plan

If critical failure occurs:

### Immediate (< 1 hour)
1. Revert routing configuration
2. Restore Coach navigation menu
3. Clear any caches

### Short-term (< 1 day)
1. Re-enable Coach routes
2. Update navigation to show both
3. Communicate to users

### Full Rollback
1. Git revert to pre-migration commit
2. Restore database if schema changed
3. Deploy previous version

## Dependencies on Other Areas

- **Critical**: Authentication from Clubhouse
- **Important**: User profiles from Clubhouse
- **Integration**: Event RSVPs might connect to Pulse
- **Future**: Performance data might feed into Locker

## Success Metrics

- Zero loss of coach functionality
- All evaluation data accessible
- No increase in error rates
- Page load times maintained
- Positive user feedback
- Simplified code structure

## Special Considerations

1. **Data Integrity**: Ensure no evaluation data is lost
2. **Historical Access**: Past evaluations must remain accessible
3. **Active Sessions**: Don't break ongoing training sessions
4. **Coach Workflows**: Preserve complex multi-step workflows

## DO NOT PROCEED WITHOUT:
1. Complete dependency mapping
2. Comprehensive test suite
3. Rollback plan tested
4. All stakeholders informed
5. Off-peak deployment window scheduled