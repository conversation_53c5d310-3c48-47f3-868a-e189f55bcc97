# Foundation Modules Implementation Plan

## Overview
Foundation Modules provide the essential infrastructure that all feature areas depend on. This includes the Design System (Shadow DOM components), Authentication Module (core auth services), and Communication Module (notifications, SMS, email). These must be established first to ensure consistency across all features.

## Current State Analysis

### Design System Components
- `components/shadow/` - Extensive Shadow DOM components
  - `ShadowButton.tsx`, `ShadowCard.tsx`, etc.
  - Custom styling system
  - Isolated component styling
- `pages/v2/DesignSystem/` - Design system showcase
- `components/FormStyles/` - Form styling patterns
- `styles/` - Global styles and variables

### Authentication Infrastructure
- Scattered auth logic across pages
- Multiple auth hooks and utilities
- Supabase integration points
- Session management code

### Communication Services
- `services/SmsNotificationService.ts` - SMS functionality
- Email sending scattered across features
- Push notification setup
- In-app notification components

## Target Architecture

```
src/foundation/
├── design-system/
│   ├── components/
│   │   ├── atoms/
│   │   │   ├── Button/
│   │   │   ├── Input/
│   │   │   ├── Badge/
│   │   │   └── Icon/
│   │   ├── molecules/
│   │   │   ├── Card/
│   │   │   ├── Modal/
│   │   │   ├── Dropdown/
│   │   │   └── Toast/
│   │   ├── organisms/
│   │   │   ├── Navigation/
│   │   │   ├── DataTable/
│   │   │   ├── Form/
│   │   │   └── Dashboard/
│   │   └── templates/
│   │       ├── PageLayout/
│   │       ├── AuthLayout/
│   │       └── DashboardLayout/
│   ├── tokens/
│   │   ├── colors.ts
│   │   ├── typography.ts
│   │   ├── spacing.ts
│   │   └── animations.ts
│   ├── utils/
│   │   ├── shadowDom.ts
│   │   ├── styling.ts
│   │   └── responsive.ts
│   └── index.ts
├── auth/
│   ├── providers/
│   │   ├── SupabaseAuthProvider.tsx
│   │   ├── AuthContext.tsx
│   │   └── SessionProvider.tsx
│   ├── hooks/
│   │   ├── useAuth.ts
│   │   ├── useSession.ts
│   │   ├── usePermissions.ts
│   │   └── useRoles.ts
│   ├── guards/
│   │   ├── AuthGuard.tsx
│   │   ├── RoleGuard.tsx
│   │   └── PermissionGuard.tsx
│   ├── services/
│   │   ├── AuthService.ts
│   │   ├── SessionService.ts
│   │   └── TokenService.ts
│   └── index.ts
├── communication/
│   ├── providers/
│   │   ├── NotificationProvider.tsx
│   │   └── CommunicationContext.tsx
│   ├── services/
│   │   ├── SMSService.ts
│   │   ├── EmailService.ts
│   │   ├── PushService.ts
│   │   └── InAppNotificationService.ts
│   ├── components/
│   │   ├── NotificationBell.tsx
│   │   ├── NotificationList.tsx
│   │   ├── Toast.tsx
│   │   └── Alert.tsx
│   ├── templates/
│   │   ├── email/
│   │   └── sms/
│   └── index.ts
└── index.ts
```

## Implementation Strategy

### Phase 1: Design System Foundation (Week 1)
**Risk Level**: LOW

1. **Atomic Design Structure**
   - Set up component hierarchy
   - Migrate existing Shadow components
   - Create component documentation
   - Build Storybook integration

2. **Design Tokens**
   - Extract color system
   - Define typography scales
   - Set spacing system
   - Create animation library

3. **Component Migration**
   ```typescript
   // Priority order:
   1. Button, Input, Card (most used)
   2. Modal, Dropdown, Toast
   3. Navigation, DataTable
   4. Complex organisms
   ```

### Phase 2: Authentication Module (Week 1-2)
**Risk Level**: MEDIUM

1. **Core Auth Services**
   - Extract Supabase logic
   - Create auth abstraction
   - Build session management
   - Implement token handling

2. **Auth Providers**
   ```typescript
   // AuthProvider setup:
   - Centralized auth state
   - Session persistence
   - Auto-refresh tokens
   - Error handling
   ```

3. **Guards & Hooks**
   - Route protection
   - Role-based access
   - Permission checks
   - Custom auth hooks

### Phase 3: Communication Module (Week 2)
**Risk Level**: LOW

1. **Service Integration**
   - SMS provider setup
   - Email service config
   - Push notification setup
   - In-app messaging

2. **Unified API**
   ```typescript
   // Communication interface:
   interface CommunicationService {
     send(channel: Channel, message: Message): Promise<Result>
     schedule(channel: Channel, message: Message, time: Date): Promise<Result>
     getStatus(messageId: string): Promise<Status>
   }
   ```

## Design System Specifications

### Component Standards
```typescript
// Every component must:
1. Use Shadow DOM for style isolation
2. Support theme customization
3. Be fully accessible (WCAG 2.1)
4. Include TypeScript definitions
5. Have comprehensive tests
6. Include Storybook stories
```

### Shadow DOM Pattern
```typescript
// Standard Shadow component:
export const ShadowComponent: FC<Props> = ({ children, ...props }) => {
  const shadowRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (!shadowRef.current?.shadowRoot) {
      const shadow = shadowRef.current?.attachShadow({ mode: 'open' });
      // Apply styles and content
    }
  }, []);
  
  return <div ref={shadowRef} />;
};
```

### Theme System
```typescript
interface Theme {
  colors: ColorTokens;
  typography: TypographyTokens;
  spacing: SpacingTokens;
  shadows: ShadowTokens;
  animations: AnimationTokens;
}
```

## Authentication Architecture

### Auth Flow
```typescript
// Centralized auth flow:
1. User initiates login
2. AuthService handles provider
3. Session created and stored
4. Token refresh automated
5. Guards protect routes
6. Hooks provide auth state
```

### Permission System
```typescript
interface AuthContext {
  user: User | null;
  session: Session | null;
  roles: Role[];
  permissions: Permission[];
  hasRole: (role: string) => boolean;
  hasPermission: (permission: string) => boolean;
  can: (action: string, resource: string) => boolean;
}
```

## Communication Architecture

### Channel Abstraction
```typescript
type Channel = 'sms' | 'email' | 'push' | 'in-app';

interface Message {
  to: Recipient | Recipient[];
  subject?: string;
  body: string;
  template?: string;
  data?: Record<string, any>;
  priority?: 'high' | 'normal' | 'low';
}
```

### Notification Queue
```typescript
// Queue system for reliability:
1. Message queued
2. Provider selected
3. Delivery attempted
4. Retry on failure
5. Status tracked
6. Analytics recorded
```

## Testing Strategy

### Design System Tests
- Visual regression tests
- Component unit tests
- Accessibility tests
- Theme switching tests
- Browser compatibility

### Auth Module Tests
- Authentication flows
- Session management
- Token refresh
- Permission checks
- Guard functionality

### Communication Tests
- Message delivery
- Template rendering
- Queue processing
- Error handling
- Provider failover

## Migration Path

### For Existing Components
1. Identify component usage
2. Create foundation version
3. Add migration wrapper
4. Update imports gradually
5. Remove old component

### Migration Wrapper Example
```typescript
// Temporary wrapper during migration:
export { ShadowButton as Button } from '@/foundation/design-system';
// Allows gradual migration without breaking changes
```

## Performance Considerations

### Design System
- Lazy load complex components
- Tree-shake unused components
- Optimize Shadow DOM creation
- Cache theme calculations

### Authentication
- Minimize auth checks
- Cache permissions
- Efficient token storage
- Background token refresh

### Communication
- Batch notifications
- Queue optimization
- Provider selection logic
- Delivery tracking

## Success Criteria

- [ ] All Shadow components migrated
- [ ] Consistent styling across app
- [ ] Centralized auth working
- [ ] All auth flows functional
- [ ] Communication services unified
- [ ] Performance targets met
- [ ] Zero breaking changes

## Implementation Checklist

### Week 1: Design System
- [ ] Set up atomic structure
- [ ] Migrate core components
- [ ] Create design tokens
- [ ] Build component docs
- [ ] Set up Storybook

### Week 1-2: Auth Module
- [ ] Extract auth logic
- [ ] Build providers
- [ ] Create guards
- [ ] Implement hooks
- [ ] Test all flows

### Week 2: Communication
- [ ] Unify services
- [ ] Build queue system
- [ ] Create templates
- [ ] Test delivery
- [ ] Monitor performance

## Developer Experience

### Component Development
```bash
# Generate new component
npm run generate:component Button --atomic=atom

# Creates:
- Component file with Shadow DOM
- Test file with basic tests
- Storybook story
- TypeScript definitions
```

### Documentation
- Interactive component gallery
- Auth flow diagrams
- Communication examples
- Migration guides
- Best practices

## Future Enhancements

### Design System
- Theme marketplace
- Component analytics
- A/B testing support
- Design-to-code tools

### Authentication
- Biometric support
- Social login expansion
- SSO integration
- Advanced MFA options

### Communication
- Rich media support
- Translation services
- Delivery optimization
- Cost management tools