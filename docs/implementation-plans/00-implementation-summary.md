# SHOT App Architecture Implementation Summary

## Overview
This document summarizes the implementation plans for reorganizing the SHOT app into seven primary feature areas plus foundation modules. The architecture has been expanded from the initial four areas (Clubhouse, Perform, Locker, Pulse) to include Identity, Schedule, and Assess areas, along with critical foundation modules for Design System, Authentication, and Communication infrastructure.

**Last Updated**: January 2025

## Critical Issues Requiring Immediate Attention

### 🚨 CRITICAL: Locker Order System Broken
- **Issue**: Orders are NOT being saved - expects non-existent `orders` table
- **Impact**: Users can complete checkout but no order records are created
- **Required**: Implement proper BigCommerce integration (Epic #280)
- **Priority**: Must be fixed in Locker Phase 1 before any other Locker features

### ⚠️ HIGH PRIORITY: Identity Consolidation
- **Issue**: 5 separate login page implementations creating confusion
- **Impact**: Inconsistent user experience and maintenance nightmare
- **Required**: Consolidate to single UpdatedLoginV2.tsx implementation
- **Priority**: Critical path - all other features depend on unified authentication

## Implementation Priority and Risk Matrix

| Area | Risk Level | Complexity | Priority | Est. Time | Dependencies |
|------|------------|------------|----------|-----------|--------------|
| **Foundation Modules** | LOW | Moderate | 1st | 2 weeks | None |
| **Identity** | HIGH | Complex | 2nd | 2-3 weeks | Foundation |
| **Locker** | LOW | Simple | 3rd | 1 week | Identity |
| **Pulse** | LOW | Simple | 4th | 1 week | Identity |
| **Control** | MEDIUM | Moderate | 5th | 2 weeks | Identity |
| **Schedule** | MEDIUM | Complex | 6th | 2-3 weeks | Identity, Control |
| **Clubhouse** | MEDIUM | Moderate | 7th | 2 weeks | Identity, Control |
| **Assess** | HIGH | Very Complex | 8th | 3 weeks | Identity, Schedule |
| **Perform** | EXTREME | Very Complex | 9th | 3-4 weeks | All above |

## Area-by-Area Summary

### 1. Foundation Modules - START HERE
**Risk**: LOW  
**Why First**: Essential infrastructure for all features
- **Design System**: Shadow DOM components, UI patterns
- **Auth Module**: Core authentication services
- **Communication**: Notification services, SMS, email
- Creates consistent foundation for all migrations

### 2. Identity (Authentication & Family) - CRITICAL PATH
**Risk**: HIGH  
**Why Second**: All features depend on authentication
- Consolidate 5 login pages → 1 unified login
- Account switching infrastructure
- Family management system
- Registration and onboarding flows
- Email verification and password reset

### 3. Locker (E-commerce)
**Risk**: LOW (after Phase 1 fixes)  
**Why Third**: Most self-contained, minimal dependencies
- **Phase 1 CRITICAL**: Fix broken order system
  - Implement Cart API (#281)
  - Implement Checkout API (#282)
  - Replace local checkout (#283)
  - Update order confirmation (#284)
  - Implement webhooks (#285)
- **Phase 2**: Move shopping pages and components
- Payment processing with Stripe
- Membership management
- Already well-organized with clear boundaries

### 4. Pulse (Content/Social)
**Risk**: LOW  
**Why Fourth**: Clean v2 implementation exists
- Consolidate to PulseV2
- Remove deprecated Pulse.tsx
- Move notification services
- SP points system integration

### 5. Control (Platform Administration)
**Risk**: MEDIUM  
**Why Fifth**: Operational features for platform management
- Super admin capabilities
- Club administration tools
- User management interfaces
- System configuration
- SMS notification dashboard

### 6. Schedule (Events & Calendar)
**Risk**: MEDIUM  
**Why Sixth**: Complex but well-defined domain
- Event creation and management
- RSVP and attendance tracking
- Pre-event evaluations
- Event-based communications
- Calendar integrations

### 7. Clubhouse (Club Management)
**Risk**: MEDIUM  
**Why Seventh**: Builds on Identity and Control
- Club-specific features (distinct from admin)
- Team management at club level
- Club statistics and reporting
- Member management

### 8. Assess (Evaluations & Development)
**Risk**: HIGH  
**Why Eighth**: Complex evaluation workflows with high data loss risk
- Player evaluations (weekly, event-based)
- Self-evaluations and pre-evaluations
- Individual Development Plans (IDP)
- Progress tracking and reporting
- Evaluation criteria management
- **Data Volume**: Thousands of evaluations per team
- **Critical**: Requires complete database backup before migration
- **Complex Aggregations**: Performance-critical queries

### 9. Perform (Sports Performance) - LAST
**Risk**: EXTREME  
**Why Last**: Most complex migration, depends on all others
- Merge entire Coach section (100+ files)
- Complex role permissions
- Training management
- Performance tracking
- Integrates with Schedule and Assess
- **Phased Sub-Migration**: 4 phases over 4-6 weeks
  - Phase 1: Foundation & Read-Only (LOW risk)
  - Phase 2: Team Management (MEDIUM risk)
  - Phase 3: Player & Evaluation Integration (HIGH risk)
  - Phase 4: Events & Training (MEDIUM risk)

## Deprecation Strategy

### File Management During Migration
To ensure safe migration without losing code:

1. **Deprecated Directory Structure**
   ```
   src/_deprecated/
   ├── foundation/
   ├── identity/
   ├── locker/
   ├── pulse/
   ├── control/
   ├── schedule/
   ├── clubhouse/
   ├── assess/
   ├── perform/
   └── unknown/
   ```

2. **Migration Process**
   - Move old files to `_deprecated/{area}/` instead of deleting
   - Document why files were deprecated in area-specific README
   - Set deletion date (2 months after production validation)
   - Use `git mv` to preserve history
   - Add deprecation warnings if files are still referenced

3. **Deletion Criteria**
   - ✅ New implementation fully tested
   - ✅ 2 months stable in production
   - ✅ No references in codebase
   - ✅ Team approval received

## Shared Infrastructure Components

### Club Core Module
Shared between Clubhouse and Control areas:
- Club entity model and database operations
- Club context provider for state management
- Common club-related services
- Shared type definitions
- Permission boundaries and access control

### Design System Foundation
- Shadow DOM components for consistent UI
- Shared styling and theming
- Common UI patterns and layouts
- Accessibility standards

## Critical Success Factors

### 1. Phased Approach
- One area at a time
- Full testing between phases
- Rollback capability maintained
- No "big bang" migrations

### 2. Backup Strategies
- **Route Aliasing**: Keep old routes during transition
- **Feature Flags**: Toggle between old/new
- **Parallel Systems**: Run both temporarily
- **Service Abstraction**: Flexible service locations

### 3. Testing Requirements
- Unit tests for moved components
- Integration tests for workflows
- E2E tests for critical paths
- Performance benchmarks
- **Playwright E2E Testing**: Risk-based coverage per area
- **Migration Testing**: Parallel system verification
- **Rollback Testing**: Emergency procedures validated

## Major Risks and Mitigations

### Highest Risk: Identity System Migration
**Risk**: Breaking authentication affects entire application
**Mitigation**:
1. Implement comprehensive auth testing first
2. Run parallel auth systems during transition
3. Feature flag for gradual rollout
4. Monitor all auth metrics closely
5. Keep all 5 login pages until fully validated
6. Test account switching extensively

### Extreme Risk: Perform Area Coach Migration
**Risk**: Breaking all coach functionality
**Mitigation**:
1. Complete Schedule and Assess migrations first
2. Copy files first (don't move)
3. Update imports gradually
4. Test extensively with real coaches
5. Keep parallel systems for 4 weeks
6. Have emergency rollback ready

### High Risk: Evaluation System (Assess)
**Risk**: Loss of evaluation data or workflows
**Mitigation**:
1. Full data backup before migration
2. Validate all evaluation types work
3. Test with historical data
4. Verify IDP functionality
5. Coach approval before cutover

### Medium Risk: Events & Scheduling
**Risk**: Teams miss events or lose RSVPs
**Mitigation**:
1. Migrate during off-season if possible
2. Test all notification paths
3. Verify RSVP data integrity
4. Keep legacy event views temporarily
5. Monitor event participation rates

### Low Risk: Foundation Modules
**Risk**: Breaking changes to shared infrastructure
**Mitigation**:
1. Start with read-only extraction
2. Gradual migration of services
3. Extensive unit testing
4. Keep backwards compatibility

## Implementation Timeline (Revised for 7-Area Architecture)

### Week 1-2: Foundation Modules Setup
- Create Design System module structure
- Extract Shadow DOM components
- Set up Auth module abstraction
- Create Communication module
- **Write comprehensive tests for all foundation modules**
- Configure module dependencies

### Week 3-5: Identity System Migration (Critical Path)
- Consolidate authentication pages
- Implement unified login
- Migrate family management
- Set up account switching
- **Extensive auth flow testing**
- **Parallel system validation**

### Week 6-7: Locker Migration (Phase 1 Critical)
- **Week 6**: Fix broken order system
  - Implement proper BigCommerce integration
  - Create orders table/API
  - Fix checkout flow
- **Week 7**: Complete migration
  - Move e-commerce files
  - Integrate payment processing
  - Update membership management
- **Shopping flow E2E tests**
- Performance validation

### Week 7: Pulse Migration
- Consolidate to PulseV2
- Remove deprecated code
- Integrate notifications
- **Content flow testing**
- SP points validation

### Week 8-9: Control (Admin) Migration
- Extract admin features
- Set up super admin tools
- Migrate club administration
- **Permission testing**
- **Admin workflow validation**

### Week 10-12: Schedule (Events) Migration
- Consolidate event management
- Migrate RSVP system
- Set up attendance tracking
- **Event flow testing**
- **Calendar integration tests**

### Week 13-14: Clubhouse Migration
- Club-specific features
- Team management migration
- Statistics and reporting
- **Club workflow tests**
- Integration validation

### Week 15-17: Assess (Evaluations) Migration
- Evaluation system consolidation
- IDP functionality
- Progress tracking setup
- **Evaluation flow testing**
- **Historical data validation**

### Week 18-23: Perform Migration (Extended Timeline)
- **Week 18**: Phase 1 - Foundation & Read-Only
  - Core module setup
  - Read-only views
  - Coach dashboard shell
- **Week 19-20**: Phase 2 - Team Management
  - Team operations
  - Player roster management
- **Week 21**: Phase 3 - Player & Evaluation Integration
  - Connect to Assess area
  - Player development features
- **Week 22-23**: Phase 4 - Events & Training
  - Connect to Schedule area
  - Training management
- **Comprehensive coach testing throughout**
- **Load and stress testing**
- **Parallel system validation**

### Week 24-26: Final Integration & Cleanup
- Remove all deprecated code
- **Complete regression testing**
- Performance optimization
- Documentation updates
- Team training
- Production deployment preparation

## Validation Gates

Before proceeding to next area:
1. ✓ **All Playwright E2E tests passing**
2. ✓ **Unit and integration tests passing**
3. ✓ No increase in errors
4. ✓ Performance benchmarks met
5. ✓ **Critical user journeys validated**
6. ✓ User workflows intact
7. ✓ **Rollback procedures tested**
8. ✓ **Data integrity verified**
9. ✓ **Security permissions maintained**

## Playwright E2E Testing Strategy by Risk Level

### EXTREME Risk - Perform (80% Test Coverage)
- **Complete coach workflow automation**
- **Parallel system verification** (old vs new routes)
- **Historical data preservation testing**
- **Multi-role permission testing**
- **Load testing for peak coach usage**
- **Emergency rollback procedures**

### MEDIUM Risk - Clubhouse (60% Test Coverage)
- **Authentication flow automation**
- **Role-based access testing**
- **Admin feature verification**
- **Security boundary testing**
- **Session management testing**

### LOW Risk - Locker & Pulse (40% Test Coverage)
- **Happy path user journeys**
- **Core feature smoke tests**
- **Performance benchmarking**
- **Basic rollback verification**

## Emergency Procedures

### If Critical Failure:
1. **Immediate** (< 1 hour)
   - Revert route configuration
   - Re-enable old navigation
   - Clear caches

2. **Short-term** (< 24 hours)
   - Git revert to stable commit
   - Restore from backup
   - Communicate to users

3. **Investigation**
   - Analyze what went wrong
   - Adjust plan
   - Smaller increments

## Expected Outcomes

### Immediate Benefits
- Clear code organization
- Easier feature location
- Reduced duplication
- Better developer experience

### Long-term Benefits
- Faster feature development
- Easier onboarding
- Better performance
- Scalable architecture

## Key Decisions Made

1. **Expand to 7-area architecture** plus foundation modules
2. **Foundation modules first** to establish consistent infrastructure
3. **Identity system is critical path** - all features depend on auth
4. **Keep UpdatedLoginV2.tsx** as primary login in Identity
5. **Remove Pulse.tsx** in favor of PulseV2
6. **Split Coach section** across Perform, Assess, and Schedule
7. **Separate Control (admin)** from Clubhouse (club management)
8. **Events become Schedule** area with calendar focus
9. **Evaluations become Assess** area for all assessment features
10. **Family management** moves to Identity with auth
11. **Payment/memberships** stay in Locker
12. **Notifications** distributed based on context (Pulse for social, Schedule for events)
13. **Implement comprehensive Playwright E2E testing** with risk-based coverage
14. **Use parallel system testing** during all migrations
15. **Require rollback procedure validation** before each migration
16. **Fix critical Locker order system** before any other Locker work
17. **Consolidate 5 login pages** to UpdatedLoginV2.tsx in Identity
18. **Create shared Club Core module** for Clubhouse/Control
19. **Implement Foundation Modules first** as prerequisites
20. **Use 4-phase approach for Perform** due to extreme complexity

## Open Questions

1. Where should team communications live? (Pulse vs Perform)
2. Should coach admin functions stay separate?
3. How to handle shared components?
4. When to remove legacy support?
5. BigCommerce integration timeline vs local order storage?
6. How to maintain order history during Locker migration?
7. Strategy for consolidating 5 login implementations?
8. Performance impact of complex Assess aggregations?

## Success Metrics

- 40% reduction in code duplication
- 0% increase in errors
- < 5% performance impact  
- 100% feature parity
- Clear separation of concerns across 7 areas
- Improved developer onboarding time (target: 50% reduction)
- Consistent UI/UX through Design System adoption
- Simplified authentication flow (5 pages → 1)
- Positive developer and user feedback

## Next Steps

1. **IMMEDIATE**: Fix Locker order system (Epic #280)
2. Review and approve updated plans
3. Set up Foundation Modules structure
4. Create feature flags infrastructure
5. Develop rollback procedures
6. Begin Identity consolidation planning
7. Schedule phased implementation
8. Create shared Club Core module design

---

**Remember**: This is a major refactor. Take it slow, test thoroughly, and always have a rollback plan. The goal is zero disruption to users while achieving a cleaner, more maintainable codebase.