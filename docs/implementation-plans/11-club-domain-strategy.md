# Club Domain Strategy

## Overview
This document defines the Club domain architecture, clarifying the boundaries between Clubhouse (member-facing features) and Control (administrative features), while establishing how Clubs relate to Teams, Players, and Evaluations across the SHOT platform.

## Domain Definition

### What is a Club?
A Club in the SHOT platform represents:
- **Legal Entity**: The registered sports organization with billing, compliance, and governance requirements
- **Community Hub**: The social and organizational center for members, teams, and activities
- **Administrative Unit**: The top-level organizational boundary for permissions, data, and operations
- **Brand Identity**: Custom branding, messaging, and member experience

### Club Domain Core Concepts
```typescript
interface Club {
  // Identity
  id: string;
  name: string;
  registrationNumber?: string;
  taxId?: string;
  
  // Branding
  logo: string;
  colors: ColorScheme;
  customDomain?: string;
  
  // Organization
  sport: Sport;
  founded: Date;
  timezone: string;
  locale: string;
  
  // Operational
  status: 'active' | 'suspended' | 'archived';
  subscription: Subscription;
  settings: ClubSettings;
  
  // Relationships
  teams: Team[];
  members: Member[];
  facilities: Facility[];
}
```

## Domain Boundaries

### Clubhouse Area (Member-Facing)
**Purpose**: Provides the member experience and club community features

**Responsibilities**:
- Member dashboard and home experience
- Club news and announcements
- Member directory and social features
- Club branding and customization
- Member self-service features
- Club statistics and public analytics
- Event calendars and schedules
- Member onboarding flows

**Key Entities**:
- ClubProfile (public-facing club information)
- ClubBranding (logos, colors, themes)
- ClubAnnouncements
- MemberDirectory
- ClubStats (public metrics)

### Control Area (Administrative)
**Purpose**: Provides administrative control and platform management

**Responsibilities**:
- Club registration and setup
- Subscription and billing management
- Member role and permission management
- Club-level configuration and settings
- Compliance and regulatory features
- Administrative reporting and analytics
- Platform-level club management
- Super admin club operations

**Key Entities**:
- ClubAdmin (administrative record)
- ClubSubscription
- ClubPermissions
- ClubConfiguration
- ClubCompliance
- ClubBilling

## Domain Relationships

### Club → Team Relationship
```typescript
interface ClubTeamRelationship {
  // A Club contains multiple Teams
  club: Club;
  teams: Team[];
  
  // Teams inherit from Club
  inheritance: {
    branding: boolean;
    settings: boolean;
    permissions: boolean;
  };
  
  // Team autonomy within Club
  teamAutonomy: {
    roster: 'full';
    schedule: 'full';
    evaluations: 'full';
    objectives: 'club_guided';
  };
}
```

**Key Principles**:
1. Teams exist within a Club context
2. Teams inherit club-level settings but maintain operational autonomy
3. Club provides the organizational boundary for teams
4. Teams cannot exist without a parent Club

### Club → Player Relationship
```typescript
interface ClubPlayerRelationship {
  // Players are members of a Club
  club: Club;
  player: Player;
  membership: Membership;
  
  // Player can be on multiple teams within a club
  teamAssignments: TeamAssignment[];
  
  // Club-level player attributes
  clubProfile: {
    memberNumber: string;
    joinDate: Date;
    status: MemberStatus;
    subscription: MemberSubscription;
  };
}
```

**Key Principles**:
1. Players are members of a Club first, team assignments second
2. Club membership persists across seasons and team changes
3. Player data is owned by the Club, accessed by Teams
4. Club defines membership rules and player eligibility

### Club → Evaluation Relationship
```typescript
interface ClubEvaluationRelationship {
  // Club defines evaluation framework
  club: Club;
  evaluationFramework: {
    templates: EvaluationTemplate[];
    criteria: EvaluationCriteria[];
    workflow: EvaluationWorkflow;
    permissions: EvaluationPermissions;
  };
  
  // Evaluations happen at team level
  scope: 'team_level';
  
  // Club aggregates evaluation data
  analytics: {
    clubWideMetrics: boolean;
    crossTeamComparisons: boolean;
    historicalTrends: boolean;
  };
}
```

**Key Principles**:
1. Club defines the evaluation framework and standards
2. Evaluations are conducted at the team level
3. Club can view aggregated evaluation data across all teams
4. Evaluation templates are managed at club level

## Implementation Architecture

### Shared Club Core
Create a shared club domain module used by both Clubhouse and Control:

```typescript
// src/core/club/
├── entities/
│   ├── Club.ts              // Core club entity
│   ├── ClubMember.ts        // Member within club context
│   ├── ClubSettings.ts      // Shared settings model
│   └── ClubBranding.ts      // Branding configuration
├── services/
│   ├── ClubService.ts       // Core club operations
│   ├── MembershipService.ts // Member management
│   └── ClubContextService.ts // Club context provider
├── hooks/
│   ├── useClub.ts           // Current club context
│   ├── useClubMember.ts     // Member in club context
│   └── useClubSettings.ts   // Club settings access
└── types/
    └── club.types.ts        // Shared type definitions
```

### Area-Specific Implementations

**Clubhouse Implementation**:
```typescript
// src/features/clubhouse/
├── club/
│   ├── components/
│   │   ├── ClubDashboard.tsx
│   │   ├── ClubBranding.tsx
│   │   └── MemberDirectory.tsx
│   └── pages/
│       └── ClubHome.tsx
```

**Control Implementation**:
```typescript
// src/features/control/
├── club/
│   ├── components/
│   │   ├── ClubAdmin.tsx
│   │   ├── ClubSetup.tsx
│   │   └── ClubBilling.tsx
│   └── pages/
│       └── ClubManagement.tsx
```

## Data Flow Architecture

### Club Context Provider
```typescript
interface ClubContext {
  currentClub: Club;
  memberRole: MemberRole;
  permissions: Permission[];
  switchClub: (clubId: string) => Promise<void>;
}

// Provides club context throughout the app
<ClubProvider>
  <App />
</ClubProvider>
```

### Cross-Domain Data Access
```typescript
// In Perform area (Teams)
const { currentClub } = useClub();
const teams = useClubTeams(currentClub.id);

// In Assess area (Evaluations)
const { evaluationFramework } = useClubEvaluationSettings(currentClub.id);

// In Identity area (Players)
const membership = useClubMembership(playerId, currentClub.id);
```

## Migration Strategy

### Phase 1: Create Shared Club Core
1. Define core club entities and types
2. Build ClubService with essential operations
3. Implement ClubContext provider
4. Create shared hooks for club access

### Phase 2: Refactor Clubhouse
1. Update to use shared club core
2. Focus on member-facing features
3. Remove administrative functions
4. Enhance member experience features

### Phase 3: Refactor Control
1. Update to use shared club core
2. Consolidate all admin functions
3. Build comprehensive club management
4. Implement platform-level controls

### Phase 4: Update Related Areas
1. Update Perform to use club context
2. Update Assess to use club evaluation settings
3. Update Identity to include club membership
4. Update Schedule to respect club boundaries

## Security and Permissions

### Club-Level Permissions
```typescript
enum ClubPermission {
  // Clubhouse permissions (member-facing)
  VIEW_CLUB_INFO = 'club:view:info',
  VIEW_MEMBER_DIRECTORY = 'club:view:directory',
  VIEW_CLUB_STATS = 'club:view:stats',
  
  // Control permissions (administrative)
  MANAGE_CLUB_SETTINGS = 'club:manage:settings',
  MANAGE_CLUB_BILLING = 'club:manage:billing',
  MANAGE_CLUB_MEMBERS = 'club:manage:members',
  MANAGE_CLUB_PERMISSIONS = 'club:manage:permissions',
}
```

### Permission Hierarchy
```
Platform Admin
  └── Club Admin
      └── Team Admin/Coach
          └── Team Member/Player
```

## Testing Strategy

### Domain Boundary Tests
```typescript
describe('Club Domain Boundaries', () => {
  test('Clubhouse cannot access Control functions', async () => {
    // Verify separation of concerns
  });
  
  test('Control has full access to club data', async () => {
    // Verify administrative access
  });
  
  test('Teams inherit club context correctly', async () => {
    // Verify inheritance model
  });
});
```

### Integration Tests
```typescript
describe('Club Cross-Domain Integration', () => {
  test('Creating a team requires valid club context', async () => {
    // Verify team-club relationship
  });
  
  test('Player membership flows through club', async () => {
    // Verify player-club relationship
  });
  
  test('Evaluations respect club framework', async () => {
    // Verify evaluation-club relationship
  });
});
```

## Success Metrics

1. **Clear Separation**: No mixed concerns between Clubhouse and Control
2. **Consistent Context**: Club context available throughout the app
3. **Proper Inheritance**: Teams/Players properly inherit club settings
4. **Security**: Permissions correctly enforced at club level
5. **Performance**: No degradation from context providers
6. **Developer Experience**: Clear where club logic belongs

## Future Enhancements

1. **Multi-Club Support**: Allow users to belong to multiple clubs
2. **Club Federation**: Support for club associations and leagues
3. **White-Label**: Full white-label support per club
4. **Club Marketplace**: Share resources between clubs
5. **Advanced Analytics**: Cross-club benchmarking (with consent)

## Conclusion

This strategy provides a clear separation between member-facing (Clubhouse) and administrative (Control) features while establishing a shared understanding of the Club domain. The architecture supports current needs while allowing for future growth and maintains clear relationships with Teams, Players, and Evaluations domains.