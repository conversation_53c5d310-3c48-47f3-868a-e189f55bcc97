# Evaluation Database Structure Analysis

## Overview
This document provides a comprehensive analysis of the evaluation-related database structure in the SHOT App production database.

## Database Statistics
- **pre_evaluations**: 173 records
- **player_evaluations**: 916 records

## Table Structures

### pre_evaluations Table
Key columns identified from sample data:
- `id` (UUID) - Primary key
- `event_id` (UUID) - Link to events table
- `player_id` (UUID) - Link to profiles table
- `team_id` (UUID) - Link to teams table
- `player_position` (string) - The player's position (e.g., "STRIKER", "goalkeeper", "defender", "forward")
- `status` (string) - Values: "pending", "completed"
- `evaluation_status` (string) - Values: "draft"
- `framework_version` (string) - e.g., "SHOT-2025"
- `week_number` (integer)
- `responses` (JSONB) - Structure: `{summary: {}, questions: []}`
- `questions_answered` (integer)
- `total_questions` (integer)
- `completion_percentage` (number)
- `access_token` (UUID) - For secure access
- `request_method` (string) - e.g., "coach_initiated"
- `points_earned` (integer) - Default: 30
- `points_claimed` (boolean)
- Various timestamps and metadata fields

### player_evaluations Table
Key columns identified from sample data:
- `id` (UUID) - Primary key
- `player_id` (UUID) - Link to profiles table
- `evaluator_id` (UUID) - Who performed the evaluation
- `team_id` (UUID) - Link to teams table
- `event_id` (UUID) - Link to events table
- `pre_evaluation_id` (UUID) - Link to pre_evaluations table (nullable)
- `position` (string) - Player's position (e.g., "STRIKER", "WINGER", "All", "GOALKEEPER")
- `category` (string) - e.g., "TECHNICAL"
- `area` (string) - e.g., "technical evaluation"
- `question` (string) - The evaluation question
- `rating` (integer) - Numerical rating
- `evaluation_status` (string) - All records show "draft"
- `evaluation_criteria_data` (JSONB) - Contains pre-evaluation answers
- `framework_version` (string) - e.g., "SHOT-2025"
- `is_event_based` (boolean)
- Various timestamps and metadata fields

## Key Findings

### 1. Position Storage
- **pre_evaluations**: Uses `player_position` column (string)
- **player_evaluations**: Uses `position` column (string)
- Both tables store position as a simple string, not as a foreign key

### 2. Position Values Found
Unique positions in player_evaluations:
- "All" (generic position)
- "STRIKER"
- "WINGER"
- "GOALKEEPER"
- "CENTRE BACK"
- "FULL BACK"
- "goalkeeper" (lowercase variant)
- "full_back" (lowercase with underscore)

This shows inconsistent position naming (uppercase vs lowercase, spaces vs underscores).

### 3. Relationships
- **10 player_evaluations** have a `pre_evaluation_id` linking them to pre-evaluations
- Most player_evaluations (906 out of 916) do NOT have a pre_evaluation_id
- This suggests pre-evaluations are not always required for player evaluations

### 4. Status Distribution
- **pre_evaluations**: 
  - 119 pending
  - 54 completed
- **player_evaluations**: 
  - 916 draft (100% are in draft status)

### 5. evaluation_criteria_data Structure
Found in some player_evaluations records, contains:
```json
{
  "answers_pre": {
    "1": "I really struggle with this",
    "2": "I find this difficult",
    "3": "I'm okay at this"
    // ... more answers
  }
}
```

### 6. Missing Database Objects
Could not access via REST API:
- Database functions (pg_proc)
- Triggers (information_schema.triggers)
- RLS policies
- Views
- These would need direct database access or checking migration files

## Potential Issues Identified

1. **Position Inconsistency**: Multiple formats for the same positions (e.g., "GOALKEEPER" vs "goalkeeper")
2. **All Draft Status**: All 916 player_evaluations are in "draft" status - none are finalized
3. **Low Pre-Evaluation Usage**: Only 10 out of 916 player evaluations are linked to pre-evaluations
4. **Empty Responses**: No pre_evaluations found with actual question responses in the data sample

## Recommendations

1. **Standardize Position Names**: Create a positions lookup table or enum to ensure consistency
2. **Implement Status Workflow**: Add functionality to move evaluations from draft to completed
3. **Data Integrity**: Add constraints or triggers to ensure position consistency
4. **Complete Migration**: The untracked migration file `20250813_fix_positions_and_pre_evaluations.sql` may address some of these issues

## Related Files Found
- `/supabase/functions/send-sms-v2/create_pre_evaluation_notifications_original.sql`
- `/supabase/functions/send-sms-v2/trigger_pre_evaluation_notifications_original.sql`
- `/supabase/functions/send-sms-v2/create_pre_evaluation_notifications_modified.sql`

These suggest there's a notification system tied to pre-evaluations.