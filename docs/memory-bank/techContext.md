# Technical Context - Club Management

## Technology Stack
- **Framework**: Ionic React v6
- **Language**: TypeScript
- **Database**: Supabase (PostgreSQL)
- **State Management**: React Context API
- **Styling**: Tailwind CSS, Ionic Components, Custom CSS
- **Routing**: React Router

## Development Setup
- Node.js environment
- React development server
- Supabase local development
- TypeScript compilation

## Technical Architecture
1. **Frontend Components**:
   - Page components (ClubManagementPage, ClubDashboard, etc.)
   - Form components (ClubCreationForm, TeamCreationForm)
   - List components (ClubsList, ClubVerificationList)
   - Shared components (ClubHeader, TeamCard, etc.)

2. **Services Layer**:
   - ClubService (interfaces and types)
   - ClubServiceImpl (Supabase integration)
   - TeamService (team management)
   - Mock data services (for testing)

3. **Database Schema**:
   - clubs table (club information)
   - teams table (team details)
   - club_coaches table (club-level coach assignments)
   - team_coaches table (team-level coach assignments)
   - profiles table (user information)

## Key Dependencies
- @ionic/react: UI components
- @supabase/supabase-js: Database client
- react-router-dom: Navigation
- ionicons: Icon library

## Constraints
- Mobile-first design requirements
- Supabase RLS (Row Level Security) policies
- TypeScript strict mode
- Ionic component guidelines
- Browser UUID generation (no crypto module)
