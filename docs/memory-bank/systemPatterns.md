# System Patterns - Club Management

## Architecture Patterns

### Component Structure
```
src/pages/section/Coach/supporting/ClubManagement/
├── Main Pages (ClubManagementPage, ClubDashboard)
├── Forms (ClubCreationForm, TeamCreationForm)
├── Lists (ClubsList, ClubVerificationList)
├── Sub-components (ClubTeams, ClubSettings, etc.)
└── Shared components/ (reusable UI elements)
```

### Data Flow
1. **Page Components** → fetch data via services
2. **Services** → interact with Supabase
3. **Mock Data** → fallback for testing/development
4. **Context/Hooks** → manage state and side effects

### Navigation Patterns
- Hierarchical navigation: /coach → /coach/clubs → /coach/club/:clubId
- Tab-based sections within pages
- Back button navigation with explicit URLs
- Query parameters for initial state (?tab=verified)

### Form Patterns
- Multi-step forms with progress indicators
- Form validation before submission
- Loading states during async operations
- Success/error feedback via toasts and alerts
- Separate creation and edit modes

### Database Interaction
1. **UUID Generation**: Browser-compatible UUID function
2. **Soft Deletes**: Using is_active flag
3. **Verification Workflow**: Status field with approval tracking
4. **Timestamps**: created_at, updated_at automatic handling
5. **Relations**: Foreign keys to profiles and other tables

### State Management
- React hooks for local state
- Context API for user information
- Custom hooks for data fetching (useClubData, useTeams)
- Loading/error states in components

### Error Handling
- Try-catch blocks in service methods
- Error boundaries for component failures
- Toast notifications for user feedback
- Console logging for debugging

### Styling Patterns
- Component-specific CSS files
- Tailwind utilities for rapid styling
- Ionic components for mobile-first UI
- Dark theme with black backgrounds
- Custom form styles with consistent spacing

## Key Technical Decisions
1. **Hybrid Data Approach**: Real database with mock data fallback
2. **Multi-step Forms**: Better UX for complex data entry
3. **Service Layer**: Separation of concerns between UI and data
4. **TypeScript Interfaces**: Strong typing for data structures
5. **Mobile-first Design**: Ionic components and responsive layouts
