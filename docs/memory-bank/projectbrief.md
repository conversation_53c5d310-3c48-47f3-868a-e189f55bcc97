# SHOT App - Club Management Project Brief

## Project Overview
The SHOT app includes comprehensive coaching and club management capabilities. This module focuses on the club management aspect, which allows coaches and administrators to create, manage, and organize sports clubs.

## Core Requirements
1. **Club Creation and Management**
   - Create new clubs with essential information
   - Manage club details and settings
   - Verification workflow for clubs
   - Multiple sport type support

2. **Team Management**
   - Create teams within clubs
   - Assign coaches to teams
   - Manage team rosters
   - Track team settings and seasons

3. **Club Administration**
   - Assign administrators and coaches at club level
   - Define roles and permissions
   - Track coach assignments

4. **User Experience**
   - Mobile-first Ionic/React interface
   - Dashboard views for clubs and teams
   - Multi-step forms for club creation
   - Seamless navigation between club components

## Technical Constraints
- Built with Ionic React framework
- Uses Supabase for database backend
- TypeScript for type safety
- Mobile-optimized UI/UX

## Project Scope
Focus specifically on club management functionality within the coach section of the app, including:
- Club CRUD operations
- Team management within clubs
- Coach assignments
- Club verification workflow
- Dashboard and listing views
