# Evaluation Data Loading Differences: TeamFlat vs EventEvaluation

## Executive Summary
TeamFlat.tsx and EventEvaluation.tsx load evaluation data differently, which can lead to inconsistencies in displayed statistics.

## Key Differences

### 1. Data Source
- **TeamFlat.tsx**: Uses `event_summary` database view (pre-aggregated data)
- **EventEvaluation.tsx**: Loads raw data from multiple tables and calculates stats client-side

### 2. Coach Evaluation Calculation

#### TeamFlat.tsx (Lines 336-339)
```typescript
coachEvaluationTotal: summary.attended_count || 0,
coachEvaluationCompleted: summary.coach_eval_completed_count || 0,
```

#### EventEvaluation.tsx (Lines 1055-1062)
```typescript
const attendedPlayers = players; // Already filtered by 'attended' status
const evaluatedPlayers = attendedPlayers.filter(p => 
  p.ratings.technical > 0 && 
  p.ratings.physical > 0 && 
  p.ratings.psychological > 0 && 
  p.ratings.social > 0
);
```

### 3. Completion Logic

**TeamFlat**: 
- Uses `coach_eval_completed_count` from database view
- Relies on server-side calculation

**EventEvaluation**:
- Counts players with ALL 4 categories rated > 0
- Client-side calculation requiring all ratings to be complete

## Potential Issues

### 1. Definition Mismatch
The database view might count evaluations differently than the client-side logic. For example:
- Database might count ANY evaluation as "started"
- Client requires ALL 4 categories to be rated

### 2. Data Freshness
- TeamFlat loads from a view that might have cached/stale data
- EventEvaluation loads fresh data directly from tables

### 3. Attendance Synchronization
Both rely on `attended_count`, but if attendance isn't properly updated, percentages will be wrong.

## Data Flow

### TeamFlat Data Loading (Simplified)
```
event_summary view → Transform → ShadowEventCard
```

### EventEvaluation Data Loading (Simplified)
```
event_participants (attended only) → 
profiles → 
player_evaluations → 
Calculate stats → 
ShadowEventCard
```

## Recommendations

1. **Verify View Definition**: Check how `coach_eval_completed_count` is calculated in the `event_summary` view

2. **Consistent Logic**: Ensure both pages use the same definition of "completed evaluation"

3. **Use EventSummaryService**: Consider using the same service in both pages for consistency

4. **Debug Logging**: Both components have extensive logging that can help verify the data:
   - TeamFlat: Lines 313-321 log raw event summary data
   - EventEvaluation: Lines 1175-1193 log ShadowEventCard props

## Technical Details

### ShadowEventCard Props Mapping

TeamFlat passes:
```typescript
evaluationsCompleted: summary.coach_eval_completed_count
evaluationsTotal: summary.attended_count
```

EventEvaluation passes:
```typescript
evaluationsCompleted: evaluatedPlayers.length  // Players with all 4 ratings
evaluationsTotal: attendedPlayers.length
```

This difference in calculation is likely the root cause of any discrepancies.