# Evaluation System Migration Plan

## Executive Summary

This plan outlines the complete migration from the current two-table evaluation system (`pre_evaluations` and `player_evaluations`) to a unified single-table architecture (`event_evaluations`). The migration will consolidate 173 pre-evaluations and 916 player evaluations into a cleaner, more maintainable structure.

## Current State Analysis

### Database
- **pre_evaluations**: 173 records (119 pending, 54 completed)
- **player_evaluations**: 916 records (all in draft status)
- **evaluation_criteria**: Master table with position-specific questions
- **Position inconsistency**: Mix of formats (STRIKER vs striker, FULL BACK vs full_back)

### Codebase
- **3 Core Services**: PlayerEvaluationService, PreEvaluationService, EventEvaluationService
- **15+ UI Components**: EventEvaluation, PlayerSelfEvaluationForm, etc.
- **5+ Shadow Components**: Design system evaluation components
- **Database Functions**: Multiple RPC functions for evaluation summaries

## Migration Strategy

### Phase 1: Database Schema Creation (Week 1)

#### 1.1 Create New Table
```sql
-- New unified table with decimal support for ratings
CREATE TABLE event_evaluations (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id uuid NOT NULL REFERENCES events(id),
    player_id uuid NOT NULL REFERENCES profiles(id),
    team_id uuid NOT NULL REFERENCES teams(id),
    
    -- Criteria reference
    criteria_id uuid REFERENCES evaluation_criteria(id),
    category text NOT NULL,
    area text NOT NULL,
    position text NOT NULL,
    
    -- Player position (frozen at creation)
    player_position text NOT NULL,
    
    -- Questions (frozen at creation)
    question_pre text,
    question_coach text NOT NULL,
    question_post text,
    
    -- Answer options (frozen)
    answers_pre jsonb,
    answers_post jsonb,
    
    -- Scores with decimal support
    pre_score numeric(3,1) CHECK (pre_score BETWEEN 1.0 AND 10.0),
    coach_score numeric(3,1) CHECK (coach_score BETWEEN 1.0 AND 10.0),
    post_score numeric(3,1) CHECK (post_score BETWEEN 1.0 AND 10.0),
    
    -- Timestamps
    pre_submitted_at timestamp,
    pre_submitted_by uuid REFERENCES profiles(id),
    coach_submitted_at timestamp,
    coach_submitted_by uuid REFERENCES profiles(id),
    post_submitted_at timestamp,
    post_submitted_by uuid REFERENCES profiles(id),
    
    -- Metadata
    week_number integer NOT NULL,
    framework_version text DEFAULT 'SHOT-2025',
    created_at timestamp DEFAULT now(),
    updated_at timestamp DEFAULT now(),
    
    -- Tracking
    pre_reminder_sent_at timestamp,
    post_reminder_sent_at timestamp,
    
    CONSTRAINT unique_player_event_criteria UNIQUE (event_id, player_id, criteria_id)
);
```

#### 1.2 Create Supporting Functions
```sql
-- Function to create evaluations when event is created
CREATE OR REPLACE FUNCTION create_event_evaluations(p_event_id uuid)
RETURNS void AS $$...

-- Function to handle position mapping consistency
CREATE OR REPLACE FUNCTION normalize_position(p_position text)
RETURNS text AS $$
BEGIN
    RETURN UPPER(REPLACE(p_position, '_', ' '));
END;
$$ LANGUAGE plpgsql;
```

#### 1.3 Create Indexes
```sql
CREATE INDEX idx_event_evaluations_event ON event_evaluations(event_id);
CREATE INDEX idx_event_evaluations_player ON event_evaluations(player_id);
CREATE INDEX idx_event_evaluations_category ON event_evaluations(category);
CREATE INDEX idx_event_evaluations_pending_coach ON event_evaluations(event_id) 
    WHERE coach_score IS NULL;
CREATE INDEX idx_event_evaluations_pending_pre ON event_evaluations(event_id, player_id) 
    WHERE pre_score IS NULL;
```

### Phase 2: Data Migration (Week 1)

#### 2.1 Migration Script
```sql
-- Step 1: Normalize positions in source tables
UPDATE pre_evaluations SET player_position = normalize_position(player_position);
UPDATE player_evaluations SET position = normalize_position(position);

-- Step 2: Create mapping of pre_evaluation sessions to criteria
CREATE TEMP TABLE pre_eval_mapping AS ...;

-- Step 3: Migrate data
INSERT INTO event_evaluations (...) 
SELECT ... FROM pre_evaluations pe
LEFT JOIN player_evaluations pev ON ...;

-- Step 4: Verify migration
SELECT COUNT(*), 'migrated' as status FROM event_evaluations
UNION ALL
SELECT COUNT(*), 'source_pre' FROM pre_evaluations
UNION ALL
SELECT COUNT(*), 'source_player' FROM player_evaluations;
```

#### 2.2 Data Validation
- Verify all records migrated correctly
- Check for orphaned evaluations
- Validate score ranges
- Ensure position consistency

### Phase 3: Service Layer Updates (Week 2)

#### 3.1 PlayerEvaluationService.ts
**Major Changes:**
- Update all queries to use `event_evaluations` table
- Modify `createEventEvaluation()` to create multiple rows per player
- Update `upsertEventEvaluation()` to handle individual criteria updates
- Simplify `getPlayerProgressSummary()` with new structure

**Key Methods to Update:**
```typescript
// Before
async getEvaluationCriteria(playerId: string, eventId: string) {
  // Complex join between pre_evaluations and player_evaluations
}

// After
async getEvaluationCriteria(playerId: string, eventId: string) {
  return supabase
    .from('event_evaluations')
    .select('*')
    .eq('event_id', eventId)
    .eq('player_id', playerId)
    .order('category, area');
}
```

#### 3.2 PreEvaluationService.ts
**Major Changes:**
- Remove separate pre_evaluations table operations
- Update to work with event_evaluations pre_score columns
- Simplify statistics queries

#### 3.3 EventEvaluationService.ts
**Major Changes:**
- Simplify summary calculations
- Direct queries instead of complex joins
- Better performance with indexed queries

### Phase 4: UI Component Updates (Week 2-3)

#### 4.1 EventEvaluation.tsx (Coach evaluation page)
**Changes:**
- Fetch from event_evaluations table
- Display pre_score alongside coach evaluation
- Update save logic to update coach_score columns

#### 4.2 PlayerSelfEvaluationForm.tsx
**Changes:**
- Save to pre_score columns in event_evaluations
- Show question_pre and answers_pre
- Update completion tracking

#### 4.3 Shadow Components
**Changes:**
- Update data interfaces
- Modify props to accept new structure
- Ensure backward compatibility during transition

### Phase 5: Database Function Updates (Week 3)

#### 5.1 Update RPC Functions
```sql
-- Update get_event_evaluation_summary_for_user
CREATE OR REPLACE FUNCTION get_event_evaluation_summary_for_user(...)
RETURNS TABLE (...) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        e.event_id,
        COUNT(*) as total_criteria,
        COUNT(e.pre_score) as pre_completed,
        COUNT(e.coach_score) as coach_completed
    FROM event_evaluations e
    WHERE ...;
END;
$$ LANGUAGE plpgsql;
```

#### 5.2 Update Triggers
- Remove triggers on old tables
- Add updated_at trigger on new table
- Add notification triggers

### Phase 6: Testing Strategy (Week 3-4)

#### 6.1 Unit Tests
- Update all service tests
- Mock new table structure
- Test decimal score support

#### 6.2 Integration Tests
- Test complete evaluation flow
- Verify data integrity
- Test position normalization

#### 6.3 End-to-End Tests
- Coach creates event → evaluations created
- Player completes pre-evaluation
- Coach completes evaluation
- View history and progress

### Phase 7: Deployment Plan (Week 4)

#### 7.1 Pre-deployment
1. Backup production database
2. Run migration in staging
3. Complete testing suite
4. Prepare rollback scripts

#### 7.2 Deployment Steps
1. **Maintenance mode** (30 min window)
2. Create new table structure
3. Run data migration
4. Deploy updated services
5. Deploy updated UI
6. Verify functionality
7. Exit maintenance mode

#### 7.3 Post-deployment
1. Monitor for errors
2. Check performance metrics
3. Verify data integrity
4. Keep old tables for 30 days

### Phase 8: Cleanup (Week 5)

#### 8.1 After 30 Days
```sql
-- Rename old tables
ALTER TABLE pre_evaluations RENAME TO pre_evaluations_deprecated_2024;
ALTER TABLE player_evaluations RENAME TO player_evaluations_deprecated_2024;

-- After 60 days, drop if no issues
DROP TABLE pre_evaluations_deprecated_2024;
DROP TABLE player_evaluations_deprecated_2024;
```

## Risk Mitigation

### 1. Data Loss Prevention
- Full backup before migration
- Incremental migration with verification
- Keep old tables for rollback

### 2. Service Disruption
- Deploy during low-usage window
- Feature flags for gradual rollout
- Quick rollback procedure

### 3. Performance Issues
- Comprehensive indexing strategy
- Query optimization
- Load testing before deployment

## Success Metrics

1. **Data Integrity**: 100% of evaluations migrated successfully
2. **Performance**: Query response time < 100ms
3. **User Experience**: No disruption to evaluation workflows
4. **Code Quality**: Reduced complexity, better maintainability

## Timeline Summary

- **Week 1**: Database schema and data migration
- **Week 2**: Service layer updates
- **Week 3**: UI updates and testing
- **Week 4**: Deployment
- **Week 5**: Monitoring and cleanup

## Checklist

### Pre-Migration
- [ ] Backup production database
- [ ] Review migration scripts
- [ ] Test in development environment
- [ ] Prepare rollback procedures

### Migration
- [ ] Create new table structure
- [ ] Run data migration scripts
- [ ] Verify data integrity
- [ ] Update all services
- [ ] Update all UI components
- [ ] Update database functions

### Post-Migration
- [ ] Monitor error logs
- [ ] Check performance metrics
- [ ] Gather user feedback
- [ ] Document lessons learned
- [ ] Schedule old table cleanup

## Rollback Plan

If issues arise:
1. Revert code deployment
2. Switch back to old tables
3. Investigate and fix issues
4. Re-attempt migration

## Conclusion

This migration will significantly simplify the evaluation system, improve performance, and make future enhancements easier. The key is careful planning, thorough testing, and a gradual rollout with monitoring at each stage.