# Handover: Event Attendance and Draft Event Fixes

**Date**: 2025-08-14
**Feature**: Event Player Management - Attendance Tracking and Draft Event Issues
**Status**: Completed

## Summary

Fixed critical issues with event attendance tracking and draft event player management. The attendance feature was broken due to condition changes, and the draft event was showing unnecessary invitation management UI. Also resolved a publishing error related to pre-evaluations.

## Problems Addressed

### 1. Publishing Error with Pre-evaluations
**Issue**: Events with `is_pre_session_evaluation: true` failed to publish with error:
```
null value in column "rating" of relation "player_evaluations" violates not-null constraint
```

**Root Cause**: Database trigger `trigger_generate_player_evaluations` was attempting to create `player_evaluations` records immediately when `pre_evaluations` were inserted, without a rating value.

**Solution**: Implemented temporary workaround that:
- Disables `is_pre_session_evaluation` before publishing
- Publishes the event
- Re-enables `is_pre_session_evaluation` after publishing

### 2. Attendance Tracking Not Showing
**Issue**: Attendance section wasn't displaying for published events that had started.

**Root Cause**: The condition was changed from the original `isEventStarted()` to a more complex status check.

**Solution**: Restored original logic:
- Draft events → Show nothing
- Published events (started) → Show attendance tracking
- Published events (not started) → Show ShadowInvitationManager

### 3. Draft Event Invitation UI
**Issue**: Draft events were showing a complex invitation management interface with "Confirm Invitations", "Select All", and "Clear" buttons.

**Solution**: Per user request, removed entire draft event invitation section. Draft events now show no player management UI.

### 4. ShadowInvitationManager Error
**Issue**: Component crash with "Cannot read properties of undefined (reading 'length')" for `availableMembers`.

**Solution**: Fixed missing required props when calling ShadowInvitationManager for published events that haven't started.

## Key Code Changes

### 1. EventPage.tsx - Publishing Workaround
```typescript
// TEMPORARY FIX: Disable pre-evaluations to avoid database trigger issue
const hadPreEval = eventDetails.is_pre_session_evaluation;
if (hadPreEval) {
  await supabase
    .from('events')
    .update({ is_pre_session_evaluation: false })
    .eq('id', eventId);
}

// Update event status to published
await EventService.updateEventStatus(eventId, 'published');

// Re-enable pre-evaluations if it was enabled
if (hadPreEval) {
  await supabase
    .from('events')
    .update({ is_pre_session_evaluation: true })
    .eq('id', eventId);
}
```

### 2. EventPage.tsx - Event Status Conditions
```typescript
{eventDetails?.status === 'draft' ? (
  /* Draft event - no invitation management */
  null
) : isEventStarted() && eventDetails?.status === 'published' ? (
  /* Event has started - show attendance/evaluation */
  <div className="attendance-tracker-container">
    {/* Attendance tracking UI */}
  </div>
) : eventDetails?.status === 'published' && !isEventStarted() ? (
  /* Published event not started - show invitations */
  <ShadowInvitationManager
    availableMembers={...}
    invitedMembers={...}
    // ... other required props
  />
) : null}
```

## Database Issue - Permanent Fix Needed

The publishing error is caused by the database trigger `trigger_generate_player_evaluations` that runs `AFTER INSERT ON pre_evaluations`. This trigger should NOT create `player_evaluations` immediately, as there's no rating value at that point.

**Recommended fixes**:
1. Remove the trigger entirely
2. Modify trigger to only run when pre_evaluations are completed
3. Set a default rating value in the trigger function

## Testing Notes

1. **Draft Events**: Should show no player management UI
2. **Published Events (Not Started)**: Should show ShadowInvitationManager for sending invitations
3. **Published Events (Started)**: Should show attendance tracking with "Attending" and "Did Not Attend" sections
4. **Publishing Events**: Works with temporary workaround, but needs database fix

## Files Modified

1. `/src/pages/section/Coach/events/EventPage.tsx`
   - Removed draft event invitation management UI
   - Fixed attendance display conditions
   - Added publishing workaround
   - Fixed ShadowInvitationManager props
   - Removed debug logging

## Known Issues

1. **Database Trigger**: The `trigger_generate_player_evaluations` needs to be fixed at the database level
2. **Manual Pre-evaluation Creation**: Code for manually creating pre-evaluations is commented out as it conflicts with database trigger

## Next Steps

1. Fix database trigger for pre-evaluations
2. Test full event lifecycle (draft → published → started → completed)
3. Verify attendance data is saved correctly
4. Ensure evaluation flow works after attendance is marked