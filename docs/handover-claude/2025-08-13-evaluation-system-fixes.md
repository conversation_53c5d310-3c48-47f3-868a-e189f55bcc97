# Session Handover: Evaluation System Fixes

**Date**: 2025-08-13
**Session Focus**: Fixing attendance saving, evaluation persistence, and ring display issues

## Summary

This session addressed multiple issues with the SHOT app's evaluation system:
1. Attendance changes not being saved properly
2. Player evaluations not persisting correctly
3. Evaluation rings showing incorrect counts
4. Understanding the partial evaluation save behavior

## Key Findings

### 1. Attendance System Issues

**Problem**: Players unmarked from attendance couldn't be saved due to incorrect column names.

**Root Cause**: The code was using `attended_at` and `attended_confirmed_by` instead of the actual column names `attendance_confirmed_at` and `attendance_confirmed_by`.

**Fix Applied**:
- Updated `/src/pages/section/Coach/events/hooks/useAttendance.ts` (lines 197-203) to use correct column names
- Added proper handling for unmarking attendance (previously just logged, now actually updates)

### 2. Evaluation Persistence Issue

**Problem**: Coach evaluations appeared to save but showed as 0 when page reloaded.

**Root Cause**: The system considers a player "fully evaluated" only when ALL 4 categories (technical, physical, psychological, social) have ratings > 0. Partial evaluations (e.g., only POSITIONAL category) are saved but don't count toward the "evaluated" total.

**Database Analysis**:
- Event `c5f71932-6e82-4ec9-b4f6-4e4bb1eab875` has 24 total evaluations
- Only 4 are coach evaluations (pre_evaluation_id = NULL)
- All 4 coach evaluations are only for POSITIONAL category (rating = 5)
- Other categories remain at 0, so players aren't counted as "evaluated"

### 3. Ring Display Logic

The evaluation rings show:
- **Numerator**: Players with ALL 4 categories rated > 0
- **Denominator**: Total attended players

Since players only have POSITIONAL rated, the rings show 0/4 even though evaluations exist in the database.

## Technical Details

### Database Schema

**event_participants** table columns:
- `participant_id`, `event_id`, `user_id`, `role`
- `invitation_status` (values: 'invited', 'confirmed', 'declined', 'attended')
- `attendance_confirmed_at`, `attendance_confirmed_by`
- `invited_at`, `responded_at`, `notes`
- `created_at`, `updated_at`

**player_evaluations** table columns:
- `id`, `player_id`, `evaluator_id`, `team_id`, `event_id`
- `category`, `area`, `rating`, `question`
- `pre_evaluation_id` (NULL for coach evaluations)
- `evaluation_status`, `framework_version`
- Various metadata columns

### Code Changes Made

1. **Fixed isAttendee property issue**: Removed unnecessary filter since `loadEventParticipants` already filters by 'attended' status

2. **Added debug logging**: 
   - Player evaluation saves
   - Props passed to ShadowEventCard
   - Evaluation loading and grouping

3. **Fixed attendance persistence**:
   - Added data reload after saving attendance
   - Fixed unattending players functionality
   - Corrected database column names

4. **Added explicit pre_evaluation_id handling**: Ensures coach evaluations have `pre_evaluation_id = NULL`

5. **Removed POSITIONAL category**: The system now uses 4 main categories, with position-specific questions integrated within them

## Current State

- Attendance saving/updating works correctly
- Evaluations ARE saving to the database
- Ring display shows 0 because it requires ALL categories to be rated
- The UI correctly loads and displays saved evaluations
- Pre-evaluations and coach evaluations are properly separated

## Next Steps for Resolution

To fix the evaluation counting issue, consider one of these approaches:

1. **Option A**: Change the "evaluated" definition to count players with at least one category rated
2. **Option B**: Enforce UI to require all categories before saving
3. **Option C**: Implement different counting logic for partial vs complete evaluations

## Important Notes

- Pre-evaluations are set by players before events
- Coach evaluations are separate (pre_evaluation_id = NULL)
- The system uses `invitation_status = 'attended'` as the source of truth for attendance
- Evaluation criteria are position and week-specific

## Files Modified

1. `/src/pages/section/Coach/events/EventEvaluation.tsx`
   - Fixed isAttendee filter
   - Added debug logging
   - Added key prop for re-rendering
   - Removed POSITIONAL from rating requirements

2. `/src/pages/section/Coach/events/hooks/useAttendance.ts`
   - Fixed column names for attendance updates
   - Added proper unattending functionality
   - Added debug logging

3. `/src/services/PlayerEvaluationService.ts`
   - Added explicit pre_evaluation_id = NULL for coach evaluations
   - Added comprehensive debug logging

4. `/src/pages/section/Coach/events/EventPage.tsx`
   - Added data reload after attendance save
   - Fixed useAttendance hook parameters

## Testing Performed

- Verified attendance data in database
- Confirmed evaluations are saving correctly
- Tested unmarking attendance
- Verified evaluation loading on page refresh
- Checked ring display calculations

The core functionality is working - the issue is a mismatch between the UI's definition of "fully evaluated" and what users expect when partially evaluating players.