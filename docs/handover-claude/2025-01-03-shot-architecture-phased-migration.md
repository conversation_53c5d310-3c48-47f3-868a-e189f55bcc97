# Handover: SHOT App Architecture Reorganization - Phased Migration Plans

**Date**: 2025-01-03  
**Session Focus**: Creating detailed implementation plans for reorganizing SHOT app into 4 areas with phased approach for high-risk Perform migration  
**Status**: Planning Complete - Ready for Implementation  

## Session Summary

Built upon the initial architecture analysis to create detailed, risk-based implementation plans for each of the four SHOT app areas (Clubhouse, Perform, Locker, Pulse). Special focus on breaking down the high-risk Perform migration into 4 manageable sub-phases.

## Work Completed

### 1. Individual Area Implementation Plans
Created detailed plans for each area in `/docs/implementation-plans/`:
- `01-clubhouse-implementation-plan.md` - Auth consolidation, admin organization
- `02-perform-implementation-plan.md` - Complex Coach section merger
- `03-locker-implementation-plan.md` - E-commerce reorganization  
- `04-pulse-implementation-plan.md` - Content and communication features
- `00-implementation-summary.md` - Overview with risk matrix

### 2. Phased Perform Migration Plan
Created `02a-perform-phased-migration-plan.md` breaking the high-risk Perform migration into 4 sub-phases:
- **Phase 1**: Foundation & Read-Only (LOW risk)
- **Phase 2**: Team Management (MEDIUM risk)
- **Phase 3**: Player & Evaluation (HIGH risk)
- **Phase 4**: Events & Training (MEDIUM risk)

## Key Architecture Decisions

### Implementation Order (Risk-Based)
1. **Locker** (LOW) - 1 week - Self-contained e-commerce
2. **Pulse** (LOW) - 1 week - Clean v2 implementation exists
3. **Clubhouse** (MEDIUM) - 2 weeks - Critical auth consolidation
4. **Perform** (EXTREME) - 4-6 weeks - Phased Coach migration

### Major Consolidations
- **5 login pages → 1** (Keep UpdatedLoginV2.tsx)
- **2 Pulse versions → 1** (Keep PulseV2.tsx)
- **Coach section → Perform** (~100 files to migrate)
- **30% file reduction** expected from removing duplicates

## Technical Context

### File Structure Changes
```
Current:                    Target:
src/                       src/
├── pages/                 ├── features/
│   ├── shop/             │   ├── clubhouse/
│   ├── section/Coach/    │   ├── perform/
│   └── v2/perform/       │   ├── locker/
└── components/           │   └── pulse/
                          └── shared/
```

### Critical Files Modified During Session
- `src/pages/section/Coach/TeamObjectives.tsx` - User made edits (position mapping)
- `src/contexts/EnhancedShoppingCartContext.tsx` - User updated error handling

### Risk Mitigation Strategies
1. **Feature Flags** - Toggle between old/new implementations
2. **Route Aliasing** - Support both paths during transition
3. **Parallel Systems** - Run both simultaneously
4. **Service Abstraction** - Flexible service locations
5. **Gradual Rollout** - Start with pilot teams for Perform

## Next Steps for Implementation

### Immediate Actions (Week 1)
1. Set up feature flag system
2. Create new directory structure
3. Configure TypeScript path aliases
4. Start with Locker migration (lowest risk)

### Perform Migration Phases
1. **Foundation (Week 3)** - Move read-only dashboards
2. **Teams (Week 4)** - Migrate team management
3. **Players (Week 5-6)** - Complex evaluation system
4. **Events (Week 7)** - Complete migration, remove Coach

### Testing Requirements
- Unit tests for each moved component
- Integration tests for workflows
- E2E tests for critical paths (auth, evaluations)
- Performance benchmarks before/after

## Important Warnings

### High-Risk Areas
1. **Coach → Perform Migration**
   - 100+ interconnected files
   - Complex role permissions
   - Active evaluation data
   - Requires 4-6 week phased approach

2. **Authentication Consolidation**
   - Affects entire app
   - Must maintain all auth flows
   - Deploy during low usage

### Do NOT Proceed Without
1. Complete dependency mapping for Perform
2. Backup of production database
3. Rollback procedures tested
4. Feature flag system implemented
5. Stakeholder approval

## Resume Instructions

To continue implementation:

### For Locker Migration (Start Here):
1. Review `/docs/implementation-plans/03-locker-implementation-plan.md`
2. Create `src/features/locker/` structure
3. Move shop pages (low risk)
4. Test e-commerce flows
5. Remove test files

### For Perform Phased Migration:
1. Review `/docs/implementation-plans/02a-perform-phased-migration-plan.md`
2. Start with Phase 1 (read-only components)
3. Implement feature flags first
4. Test thoroughly between phases
5. Monitor error rates closely

### Key Commands for Testing:
```bash
# Type checking
npm run type-check

# Run tests
npm test

# Check for Coach imports after migration
grep -r "section/Coach" src/ --include="*.tsx" --include="*.ts"
```

## Session Metrics
- Total messages: 11
- Documents created: 6
- Risk assessments: 4 areas analyzed
- Migration phases: 4 for Perform area

## Open Questions for Team

1. Should team communications (currently placeholder) go in Pulse or Perform?
2. How to handle active evaluations during Phase 3 migration?
3. Preferred rollout percentage for gradual Perform migration?
4. Should we keep coach admin functions separate from Perform?

## Files for Reference

All planning documents in: `/Users/<USER>/d/shotNew/docs/`
- `shot-app-architecture-proposal.md` - Original proposal
- `files-to-remove-and-relocate.md` - Detailed file lists
- `implementation-plans/` - All implementation plans

---
*Handover generated by Claude*  
*Next session should begin with Locker migration as the lowest risk starting point*