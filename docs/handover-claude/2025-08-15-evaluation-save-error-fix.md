# Handover: Player Evaluation Save Error Fix
**Date**: 2025-08-15
**Session Focus**: Fixing 406 error when saving player evaluations

## Session Summary
Fixed a critical error that was preventing player evaluations from being saved. The error was caused by using `.maybe<PERSON><PERSON><PERSON>()` in a Supabase query which was still expecting a single object response but failing when no rows were returned.

## Error Details
- **Error Code**: PGRST116
- **Error Message**: "JSON object requested, multiple (or no) rows returned"
- **HTTP Status**: 406 (Not Acceptable)
- **Location**: PlayerEvaluationService.ts line 345

## Root Cause
The `upsertEventEvaluation` method was using `.maybeSingle()` to check if an evaluation already exists. While `.maybeSingle()` is designed to handle 0 or 1 rows, the Supabase client was still sending a request that expected a single JSON object response, causing a 406 error when no rows were found.

## Fix Applied

### Changed Query Approach
**File**: `/src/services/PlayerEvaluationService.ts`
**Lines**: 337-346

Changed from:
```typescript
const { data: existing, error: checkError } = await supabase
  .from('player_evaluations')
  .select('*')
  .eq('player_id', playerId)
  .eq('event_id', eventId)
  .eq('category', evaluation.category)
  .eq('area', evaluation.area)
  .maybeSingle();
```

To:
```typescript
const { data: existingRows, error: checkError } = await supabase
  .from('player_evaluations')
  .select('*')
  .eq('player_id', playerId)
  .eq('event_id', eventId)
  .eq('category', evaluation.category)
  .eq('area', evaluation.area);
  
const existing = existingRows && existingRows.length > 0 ? existingRows[0] : null;
```

### Enhanced Logging
Updated the console log to show the number of rows found:
```typescript
console.log('📊 Database check result:', {
  rowsFound: existingRows?.length || 0,
  found: !!existing,
  existingId: existing?.id,
  error: checkError?.code,
  errorMessage: checkError?.message
});
```

## Testing Status
- ✅ Build successful
- ✅ No TypeScript errors
- ⏳ Runtime testing needed to confirm evaluations save properly

## Next Steps
1. Test the evaluation saving flow in the UI
2. Verify that:
   - New evaluations can be created
   - Existing evaluations can be updated
   - Multiple evaluations for different categories save correctly
   - Positional ratings persist (if implemented)

## Related Context
This fix is part of the ongoing work to debug why positional ratings aren't persisting. The previous handover document (`2025-08-15-positional-ratings-debug.md`) added extensive logging to track the evaluation flow.

## Technical Details
- **Framework**: React + Ionic + TypeScript
- **Backend**: Supabase
- **Key Files Modified**: 
  - `/src/services/PlayerEvaluationService.ts`
- **No Database Changes**: Only query logic was modified

## Important Notes
- The fix handles edge cases where multiple rows might be returned (takes the first one)
- No changes to the database schema or RLS policies were made
- All other `.single()` usage in the file appears appropriate (used after inserts/updates where exactly one row is expected)

---
*Generated by Claude Code Assistant*