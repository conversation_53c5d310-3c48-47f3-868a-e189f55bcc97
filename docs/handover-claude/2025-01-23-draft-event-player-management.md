# Handover: Draft Event Player Management Enhancement

**Date**: 2025-01-23
**Feature**: Draft Event Player Invitation and Position Management
**Status**: Completed

## Summary

Enhanced the draft event page to use the same intuitive player management interface as the attendance tracker. Users can now properly manage which players will be invited to an event before publishing, including setting player positions.

## Problem Statement

1. When creating a draft event with 6 selected players, those players weren't showing as "invited" on the event page
2. No ability to add/remove players from the invitation list before publishing
3. Player positions couldn't be edited or saved
4. Publishing events was failing with database constraint errors

## Solution Implemented

### 1. Fixed Player Display for Draft Events

**Issue**: Selected players during event creation weren't showing as invited
**Root Cause**: The initialization logic wasn't properly loading participants for draft events

**Fix** in `EventPage.tsx`:
```typescript
// Initialize selectedMembers with already invited players for draft events
useEffect(() => {
  if (eventDetails?.status === 'draft' && teamMembers.length > 0 && participants.length >= 0) {
    // Get user IDs of players who were already added as participants
    const invitedUserIds = participants
      .filter(p => p.was_invited && p.role === 'player')
      .map(p => p.user_id);
    
    setSelectedMembers(invitedUserIds);
  }
}, [eventDetails?.status, participants.length, teamMembers.length]);
```

### 2. Implemented Attendance-Style Player Management

Replaced the simple list with a two-section interface:
- **"Players to Invite"** section (green border) - shows selected players with "Remove" buttons
- **"Not Invited"** section - shows unselected players with "Add" buttons

Players can be moved between sections by clicking the action buttons.

### 3. Added Position Management

Integrated the existing `ShadowPlayersList` position editing functionality:
- Added `showPositionManagement={true}` and `variant="team"` props
- "Edit Positions" button toggles dropdown mode
- Position changes save immediately to the database via `teamMemberService.updateTeamMember`

**Position Update Handler**:
```typescript
onPositionChange: async (playerId: string, newPosition: string) => {
  // Find the team member by user_id (not id)
  const teamMember = teamMembers.find(m => m.user_id === playerId);
  
  if (teamMember && teamMember.membership_id) {
    await teamMemberService.updateTeamMember(teamMember.membership_id, {
      position: newPosition || null
    });
    setSuccessMsg(`Position updated for ${teamMember.profiles?.full_name || 'player'}`);
  }
}
```

### 4. Implemented Invitation Saving

Added logic to the "Confirm Invitations" button to persist changes:
```typescript
// Find players to remove (were invited but now deselected)
const playersToRemove = currentParticipants.filter(
  userId => !selectedMembers.includes(userId)
);

// Find players to add (newly selected)
const playersToAdd = selectedMembers.filter(
  userId => !currentParticipants.includes(userId)
);

// Remove deselected players
for (const userId of playersToRemove) {
  const participant = participants.find(p => p.user_id === userId);
  if (participant && participant.participant_id) {
    await EventParticipantService.removeParticipant(participant.participant_id);
  }
}

// Add newly selected players
if (playersToAdd.length > 0) {
  await EventParticipantService.addMultipleParticipants(
    eventId,
    playersToAdd,
    'player'
  );
}
```

## Key Technical Details

1. **ID Management**: 
   - Team members use `user_id` for identification
   - Position updates require `membership_id`
   - Proper mapping between these IDs was crucial

2. **Event Creation Flow**:
   - `createEvent.tsx` passes selected attendees to `EventService.createEvent`
   - The service creates the event and adds participants via `EventParticipantService.addMultipleParticipants`
   - Draft events DO have participants saved, but the UI wasn't loading them correctly

3. **Component Reuse**:
   - Leveraged existing `ShadowPlayersList` component functionality
   - Used the same pattern as attendance tracking for consistency

## Files Modified

1. `/src/pages/section/Coach/events/EventPage.tsx`
   - Updated draft event section to use attendance-style interface
   - Fixed initialization to load existing participants
   - Added position saving logic
   - Implemented invitation confirmation

2. No database changes required - used existing services and APIs

## Testing Notes

1. Create a new draft event with selected players
2. Navigate to the event page - selected players should appear in "Players to Invite"
3. Move players between sections using Add/Remove buttons
4. Click "Edit Positions" and change player positions
5. Click "Confirm Invitations" to save changes
6. Refresh page - changes should persist

## Known Issues

1. **Publishing Error**: "null value in column 'rating' of relation 'player_evaluations' violates not-null constraint"
   - This appears to be a database trigger/constraint issue when publishing events
   - Not related to the invitation management feature

## Next Steps

1. Investigate and fix the publishing error related to player_evaluations
2. Consider adding bulk position assignment functionality
3. Add ability to save invitation changes automatically without confirmation button

## Code Quality

- Added comprehensive logging for debugging
- Maintained consistent code style with existing codebase
- Reused existing components and services
- No new dependencies added