# Handover: Rating Constraint Issue in Player Evaluations

**Date**: 2025-08-16  
**Session Focus**: Fixing database trigger that inserts NULL for rating instead of 1  
**Status**: Issue identified, solution provided, awaiting final verification

## Session Summary

This session focused on resolving a critical database constraint violation that prevents events from being published when pre-evaluations are enabled. The error: "null value in column 'rating' of relation 'player_evaluations' violates not-null constraint".

## Key Findings

### 1. **Issue Timeline**
- **Last working**: August 13, 2025 at 07:01 UTC
- **Broke on**: August 14, 2025 after commit `40b8c34` "consolidate the triggers into remote schema"
- **Root cause**: Production database has an outdated version of the trigger function

### 2. **The Problem**
- Trigger chain: Event published → `auto_create_pre_evaluations` ✅ → creates pre_evaluations → `trigger_generate_player_evaluations` → **ERROR: rating NULL**
- The function `generate_player_evaluations_for_pre_evaluation` in production is inserting NULL for rating
- Git repository has correct version with `rating = 1`
- Production has wrong version (likely from the consolidation)

### 3. **Evidence**
- Debug logs show successful executions until Aug 13
- Error logs show: `errorDetails: 'Failing row contains (...: "I really struggle with this"...)'`
- The error includes evaluation_criteria_data, proving the function runs but with NULL rating

## Current State

### Files Created
1. `/scripts/fix-rating-constraint-final.sql` - Complete function replacement
2. `/scripts/force-fix-rating-function.sql` - Force drop and recreate approach
3. `/scripts/verify-function-update.sql` - Verification queries
4. Multiple diagnostic scripts for debugging

### What Was Done
1. Added comprehensive debugging to EventService.ts and EventPage.tsx
2. Identified the exact trigger and function causing the issue
3. Provided the correct function from git repository
4. Created scripts to verify and force-fix the issue

## Next Steps

### Immediate Actions Required

1. **Run the force-fix script**:
   ```sql
   -- Run the contents of:
   /Users/<USER>/d/shotNew/scripts/force-fix-rating-function.sql
   ```
   This will DROP and RECREATE the function to ensure it updates.

2. **Verify the fix**:
   ```sql
   -- Check if function now has rating = 1
   SELECT routine_definition 
   FROM information_schema.routines 
   WHERE routine_name = 'generate_player_evaluations_for_pre_evaluation'
   AND routine_definition LIKE '%1,%';
   ```

3. **Test publishing an event** with pre-evaluations enabled

### If Still Failing

1. Check for duplicate functions:
   ```sql
   SELECT routine_name 
   FROM information_schema.routines 
   WHERE routine_definition LIKE '%INSERT INTO player_evaluations%';
   ```

2. Manually inspect the trigger function in Supabase dashboard
3. Consider that there might be a caching issue with the function

## Technical Context

### The Correct Function
Located in: `/supabase/migrations/20250813090308_remote_schema.sql` at line 6777
Key line: `1,  -- Use the minimum valid rating of 1` (should be at line 154 in the VALUES clause)

### Database Objects Involved
- Table: `player_evaluations` (has NOT NULL constraint on rating)
- Trigger: `trigger_generate_player_evaluations` (AFTER INSERT on pre_evaluations)
- Function: `generate_player_evaluations_for_pre_evaluation()` (needs to insert rating = 1)

### Debug Logs Added
- EventService.ts: Lines 587-661 (comprehensive error logging)
- EventPage.tsx: Lines 410-530 (publish flow debugging)

## Important Notes

1. The `auto_create_pre_evaluations` trigger is working correctly
2. The issue is specifically in `generate_player_evaluations_for_pre_evaluation`
3. The function in git is correct, but production has wrong version
4. Simple CREATE OR REPLACE might not work - may need DROP then CREATE

## Contact for Questions

The core issue is that the production database has an outdated version of the trigger function. The fix is to replace it with the correct version from the git repository that sets `rating = 1` instead of NULL.

---

**Handover prepared by**: Claude  
**Session health**: Good  
**Todos completed**: All diagnostic work done, fix provided, awaiting application