# Handover: Assess System Design and Planning

**Date**: 2025-08-16 
**Session Focus**: Complete redesign of event and evaluation system under /assess structure  
**Status**: Planning complete, ready for implementation

## Session Summary

This session focused on designing a completely new event and evaluation system that:
1. Separates concerns into focused pages (one page = one purpose)
2. Simplifies the database structure and trigger system
3. Adds SMS automation control for pre-evaluations
4. Includes data import capabilities for onboarding existing teams
5. Provides no-login access for player pre-evaluations

## Key Accomplishments

### 1. **Complete System Architecture**
- Designed 9 distinct pages covering the full event lifecycle
- Created clear separation from legacy system under `/features/assess/`
- Defined component structure with reusable Shadow components
- Established smart routing based on event status/timing

### 2. **Simplified Database Design**
- Single `event_evaluations` table replacing complex two-table structure
- One trigger for event publish instead of cascade
- SMS automation preference stored on event
- Token-based access for pre-evaluations (no login required)

### 3. **SMS Control System**
- Toggle on draft page: auto-send on publish vs manual send
- Clear status indication on published page
- Reuses existing SMS infrastructure
- Default to auto-send for convenience

### 4. **Data Import System**
- Designed to handle Isleham Skylarks U13 CSV data
- Supports players, coaches, events import
- Duplicate detection and resolution
- Progress tracking and detailed reports

## Current State

### Documentation Created
- **Complete Plan**: `/docs/assess-system-complete-plan.md` - Full implementation guide
- **Flow Diagrams**: Included in plan document showing page transitions
- **Component Map**: Complete file paths for all components

### Key Design Decisions
1. **Page-Based Architecture**: Each stage has its own focused page
2. **No Mega Pages**: EventPage split into 5+ specialized pages
3. **Token Access**: Pre-evaluations don't require player login
4. **Auto-Save Everything**: Prevent any data loss
5. **Mobile First**: All interfaces optimized for mobile

### Technical Specifications
- **Route Structure**: `/coach/assess/event/:eventId/{stage}`
- **Smart Router**: Automatically directs to correct stage
- **Feature Flag**: `REACT_APP_USE_ASSESS_EVENTS=true`
- **Parallel Development**: Complete separation from old system

## Next Steps

### Immediate Implementation (Week 1)
1. **Create Directory Structure**
   ```bash
   mkdir -p src/features/assess/{components,pages,hooks,services,routes,types}
   ```

2. **Build Core Pages**
   - CreateEventPage - Basic event creation
   - EventDraftPage - Player selection and configuration
   - EventPublishedPage - Pre-event monitoring
   - Smart router for stage detection

3. **Database Migration**
   - Create `evaluations` schema
   - Add SMS control columns to events
   - Create simplified trigger function

### Week 2 Tasks
- Event session (attendance) page
- Evaluation pages (hub and individual)
- Pre-evaluation token system
- Basic import functionality

### Week 3 Tasks
- Review/summary pages
- Player/parent viewing pages
- Complete import system
- SMS integration testing

### Week 4 Tasks
- End-to-end testing
- Import Isleham Skylarks data
- Performance optimization
- Coach training materials

## Technical Context

### Key Services to Create
```typescript
// AssessEventService.ts
- createEvent()
- updateEvent() 
- publishEvent()
- getEventStage()

// AssessEvaluationService.ts
- createEvaluations()
- saveEvaluation()
- getEvaluationProgress()

// AssessNotificationService.ts
- sendPreEvalSMS()
- generatePreEvalToken()
- validateToken()
```

### Database Changes Required
```sql
-- Add to events table
ALTER TABLE events ADD COLUMN pre_eval_auto_sms BOOLEAN DEFAULT true;
ALTER TABLE events ADD COLUMN sms_sent_at TIMESTAMP;

-- Create new schema
CREATE SCHEMA evaluations;

-- Create simplified evaluation table
CREATE TABLE evaluations.event_evaluations (...);
```

### Import Data Structure
- **Players.csv**: 16 players with guardian details
- **Club Coaches.csv**: 2 coaches
- **Events - Fixtures.csv**: Match schedule
- **Events - Training.csv**: Training sessions

## Important Notes

### Design Principles
1. **Simplicity First**: MVP features only, enhance later
2. **Clear Separation**: New system completely separate from old
3. **User-Centric**: Each page serves specific user goal
4. **Mobile-Optimized**: Every interface works on phone
5. **No Lost Work**: Auto-save on every change

### What We're NOT Building (Phase 1)
- Complex evaluation templates
- IDP integration
- Historical comparisons
- Advanced reporting
- Video/photo attachments
- Email notifications
- Recurring events

### Migration Strategy
- Build parallel system first
- Test with Isleham Skylarks U13
- Gradual team-by-team migration
- Keep old system running until confident

## Blockers/Risks

1. **Supabase v1 Compatibility**: May need view workarounds for new schema
2. **SMS Rate Limits**: Need to handle bulk SMS carefully
3. **Import Data Quality**: CSV formats vary, need flexible parsing
4. **Token Security**: Ensure pre-eval tokens can't be guessed

## Contact for Questions

The new Assess system is designed for maximum simplicity while maintaining all essential functionality. The modular page structure allows for incremental development and testing.

Key innovation: SMS automation toggle gives coaches control over notification timing while defaulting to the most common use case (auto-send).

---

**Handover prepared by**: Claude  
**Session health**: Good  
**Recommendation**: Start with Week 1 implementation tasks, beginning with directory structure and core pages