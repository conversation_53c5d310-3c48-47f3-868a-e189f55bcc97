# Handover: Evaluation System Position-Specific Questions Issue

**Date**: 2025-08-13
**Author**: Claude
**Issue**: Position-specific questions implementation inconsistency

## Summary

The evaluation system has an architectural mismatch regarding position-specific questions. The UI component expects a 5th "positional" category, but the backend design integrates position-specific questions within the 4 main categories (TECHNICAL, PHYSICAL, PSYCHOLOGICAL, SOCIAL).

## Current State

### Problems Identified

1. **Component Mismatch**:
   - `ShadowPlayerEvaluation` component expects a `positional` rating property
   - `EventEvaluation` only provides 4 categories (technical, physical, psychological, social)
   - This causes TypeScript errors and potential runtime issues

2. **Evaluation Not Persisting Correctly**:
   - Evaluations are saved successfully (confirmed in logs)
   - But when the page reloads, ratings don't appear to be loaded
   - The component re-mounts multiple times, potentially losing state

3. **Modal Not Showing**:
   - The attendance warning modal was implemented but not appearing
   - Component re-mounting may be resetting the modal state

## Technical Details

### Position-Specific Questions Design
According to the migration and earlier discussions:
- There should NOT be a separate POSITIONAL category
- Position-specific questions are integrated within the 4 main categories
- The `loadEvaluationCriteria` function correctly implements this (line 586-587 in EventEvaluation.tsx)

### Component Expectations
The `ShadowPlayerEvaluation` component (in design system) expects:
```typescript
interface PlayerRatings {
  technical: number;
  physical: number;
  psychological: number;
  social: number;
  positional: number;  // This shouldn't exist
}
```

### Database Structure
The evaluation_criteria table has position-specific questions within the main categories:
- Example: A goalkeeper gets a specific TECHNICAL question about "Shot stopping"
- Example: A striker gets a specific PHYSICAL question about "Sprint speed"

## Recommended Solution

1. **Update ShadowPlayerEvaluation Component**:
   - Remove the `positional` property from PlayerRatings interface
   - Remove the 5th evaluation slider for positional category
   - Ensure position-specific questions are shown within the 4 main categories

2. **Fix Evaluation Loading**:
   - Debug why saved evaluations aren't loading on page refresh
   - Check if the mapping between saved data and UI state is correct
   - Ensure component state is preserved during re-renders

3. **Fix Modal Issue**:
   - Investigate why component is re-mounting multiple times
   - Consider lifting the modal state higher in the component tree
   - Or use a more stable mounting strategy

## Temporary Workaround Applied

Added a dummy `positional: 0` to prevent TypeScript errors:
```typescript
ratings={{
  ...player.ratings,
  positional: 0 // Temporary fix
}}
```

This should be removed once the ShadowPlayerEvaluation component is updated.

## Next Steps

1. Update the ShadowPlayerEvaluation component to match the 4-category design
2. Fix the evaluation loading issue to ensure saved data appears on refresh
3. Stabilize the component mounting to fix the modal issue
4. Remove all references to POSITIONAL category throughout the codebase

## Related Files

- `/src/pages/section/Coach/events/EventEvaluation.tsx`
- `/src/foundation/design-system/components/molecules/Evaluation/ShadowPlayerEvaluation.tsx`
- `/src/services/PlayerEvaluationService.ts`
- `/supabase/migrations/20250813_fix_positions_and_pre_evaluations.sql`

## User Requirements

Per Jonny: "The fifth question for evaluation should be the specific question from evaluation_criteria for the player's position that was asked in the pre-evaluation."

This needs clarification: Should position-specific questions be:
1. Integrated within the 4 categories (current implementation)
2. Shown as a separate 5th category (component expectation)

This architectural decision needs to be resolved before proceeding with fixes.