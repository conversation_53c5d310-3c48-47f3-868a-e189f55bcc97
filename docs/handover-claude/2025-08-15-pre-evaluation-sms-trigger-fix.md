# Handover: Pre-evaluation SMS Trigger Fix

**Date**: 2025-08-15  
**Session Type**: Bug Investigation & Fix  
**Issue**: Published events with pre-evaluation flag not sending SMS notifications  

## 🎯 **Issue Summary**

User reported that a published event with pre-evaluation enabled didn't send SMS notifications to players. Investigation revealed that the database trigger responsible for creating pre-evaluations was being bypassed by a "temporary fix" in the code.

## 🔍 **Root Cause Found**

**Problem**: In `src/pages/section/Coach/events/EventPage.tsx`, the `handlePublishEvent` function contained a "temporary fix" that:

1. Set `is_pre_session_evaluation = false` before publishing
2. Published the event (`status = 'published'`)  
3. Set `is_pre_session_evaluation = true` after publishing

**Why this broke SMS**: The database trigger `trigger_auto_create_pre_evaluations` only fires when an event is published WITH `is_pre_session_evaluation = true`. Since the event was published while the flag was `false`, no pre-evaluations were created, and therefore no SMS notifications were sent.

## ✅ **Fix Applied**

**File Modified**: `src/pages/section/Coach/events/EventPage.tsx:407-416`

**Before**:
```typescript
// TEMPORARY FIX: Disable pre-evaluations to avoid database trigger issue
const hadPreEval = eventDetails.is_pre_session_evaluation;
if (hadPreEval) {
  console.log('Temporarily disabling pre-evaluations to avoid trigger issue');
  await supabase
    .from('events')
    .update({ is_pre_session_evaluation: false })
    .eq('id', eventId);
}

// Update event status to published
await EventService.updateEventStatus(eventId, 'published');

// Re-enable pre-evaluations if it was enabled
if (hadPreEval) {
  console.log('Re-enabling pre-evaluations');
  await supabase
    .from('events')
    .update({ is_pre_session_evaluation: true })
    .eq('id', eventId);
}
```

**After**:
```typescript
// Update event status to published
// The database trigger 'trigger_auto_create_pre_evaluations' will automatically
// create pre-evaluations when an event with is_pre_session_evaluation=true is published
await EventService.updateEventStatus(eventId, 'published');
```

## 🔧 **Database Trigger Flow**

The system works via these database triggers:

1. **`trigger_auto_create_pre_evaluations`** (on events table)
   - Fires: AFTER INSERT/UPDATE on events
   - Calls: `auto_create_pre_evaluations()` function
   - Creates pre-evaluations for event participants

2. **`trigger_send_notification_sms`** (on pre_evaluation_notifications table)  
   - Fires: AFTER INSERT when channel='sms' AND status='pending'
   - Calls edge function: `/functions/v1/send-sms-v2`
   - Sends actual SMS via Twilio

## 📊 **Investigation Results**

For the specific event `e365df37-ff28-4e45-b2fd-ee50f9cdd935`:

- ✅ Event has `is_pre_session_evaluation: true`
- ✅ Event status: `published`  
- ✅ 6 event_participants exist (invited players)
- ❌ 0 pre-evaluations created (due to bypass)
- ❌ 0 SMS notifications sent

## 🧪 **Testing Required**

**Next Steps for Verification**:
1. Create a new event with pre-evaluation enabled
2. Add participants to the event
3. Publish the event
4. Verify pre-evaluations are created automatically
5. Verify SMS notifications are sent

**SQL to check results**:
```sql
-- Check pre-evaluations were created
SELECT COUNT(*) FROM pre_evaluations WHERE event_id = 'NEW_EVENT_ID';

-- Check SMS notifications  
SELECT COUNT(*) FROM pre_evaluation_notifications 
WHERE pre_evaluation_id IN (
  SELECT id FROM pre_evaluations WHERE event_id = 'NEW_EVENT_ID'
) AND channel = 'sms';

-- Check SMS queue
SELECT * FROM sms_queue WHERE pre_evaluation_id IN (
  SELECT id FROM pre_evaluations WHERE event_id = 'NEW_EVENT_ID'
);
```

## 📝 **Scripts Created**

During investigation, several diagnostic scripts were created in `/scripts/`:

- `check-event-sms-status-v2.js` - Check event and SMS status
- `check-event-attendance-v3.js` - Check attendance records  
- `test-pre-evaluation-creation.js` - Test pre-evaluation functions
- `force-trigger-pre-evaluations.js` - Force trigger testing
- `debug-trigger-issues.js` - General trigger debugging

## 🚨 **Important Notes**

1. **The "temporary fix" was preventing the core functionality from working**
2. **SMS sending depends on the database trigger chain working correctly**  
3. **Event participants must exist before publishing for pre-evaluations to be created**
4. **The trigger was working correctly - it was being bypassed by the code**

## 🔄 **Follow-up Actions**

1. **Immediate**: Test the fix with a new event
2. **Short-term**: Monitor SMS delivery success rates
3. **Long-term**: Consider adding logging/monitoring for trigger failures

## 📧 **Contact**

If issues persist, check:
- Supabase Dashboard → Logs → Postgres (for trigger errors)
- Edge function logs at `/functions/v1/send-sms-v2`
- Twilio delivery status

**Status**: ✅ Fix applied, ready for testing