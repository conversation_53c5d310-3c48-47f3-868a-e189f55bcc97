# Handover: Player Evaluations Rating Constraint Fix

**Date**: 2025-08-15  
**Session Focus**: Fixing database constraint violation during event publishing  
**Status**: Issue analyzed and fix implemented, awaiting verification

## Session Summary

This session focused on resolving a database constraint violation that occurred when publishing events with pre-session evaluations enabled. The error was: "null value in column 'rating' of relation 'player_evaluations' violates not-null constraint".

## Key Accomplishments

### 1. **Evaluation Schema Migration Organization**
- Moved evaluation system files from general locations to proper Assess area structure
- Updated paths:
  - Service: `src/features/assess/services/NewEvaluationService.ts`
  - Component: `src/features/assess/components/TestNewEvaluations.tsx`
- Fixed handover document date from 2025-01-15 to 2025-08-15

### 2. **Constraint Violation Root Cause Analysis**
- Identified the issue in database trigger `generate_player_evaluations_for_pre_evaluation()`
- Trigger fires when pre-evaluations are created (when event published with `is_pre_session_evaluation = true`)
- <PERSON><PERSON> was attempting to insert NULL for rating column, violating NOT NULL constraint
- Rating column has constraints: NOT NULL and CHECK (rating >= 1 AND rating <= 5)

### 3. **Fix Implementation**
- Created migration to add DEFAULT value of 1 to rating column
- Migration file: `/supabase/migrations/fix-player-evaluations-rating-default.sql`
- Database trigger already updated to use `rating = 1` instead of NULL
- Debug logs confirm trigger is now working correctly

## Current State

### Database Status
- **Migration Created**: Sets DEFAULT 1 for player_evaluations.rating column
- **Trigger Updated**: Now inserts rating = 1 instead of NULL
- **Constraint**: Rating must be 1-5 (NOT NULL)
- **Debug Logs**: Show successful evaluation generation

### Code Changes
- Evaluation schema files relocated to Assess area
- No application code changes needed (issue was database-level)

### Remaining Issue
- User still experiencing constraint violation despite fix
- Possible causes:
  - Browser/API cache holding old trigger logic
  - Multiple database triggers with old logic
  - Connection pooling issues

## Next Steps

### Immediate Actions
1. **Hard refresh browser** (Cmd+Shift+R) to clear cached API calls
2. **Try publishing a different event** to rule out event-specific issues
3. **Check browser network tab** for exact error details
4. **Verify migration was applied** to production database

### If Issue Persists
1. **Check for duplicate triggers**:
   ```sql
   SELECT trigger_name, event_object_table, action_statement 
   FROM information_schema.triggers 
   WHERE trigger_name LIKE '%evaluation%';
   ```

2. **Force trigger recreation**:
   ```sql
   DROP TRIGGER IF EXISTS trigger_generate_player_evaluations ON pre_evaluations;
   CREATE TRIGGER trigger_generate_player_evaluations 
   AFTER INSERT ON pre_evaluations 
   FOR EACH ROW EXECUTE FUNCTION generate_player_evaluations_for_pre_evaluation();
   ```

3. **Clear Supabase connection pool** (restart services if self-hosted)

## Technical Context

### Trigger Flow
1. Event status → 'published' (with is_pre_session_evaluation = true)
2. Trigger creates pre_evaluations for each invited player
3. Pre_evaluation trigger creates 4 player_evaluations (TECHNICAL, SOCIAL, PSYCHOLOGICAL, PHYSICAL)
4. Each evaluation gets rating = 1 (satisfies constraints)

### Key Files
- Database schema: `/supabase/migrations/20250813090308_remote_schema.sql`
- Player evaluations constraint: Line 7546
- Trigger function: Line ~6970
- Event service: `/src/services/EventService.ts`

### Related Issues
- New evaluation schema (from earlier handover) is implemented but not deployed
- Located in `/src/features/assess/` following domain architecture
- Migration exists at `/supabase/migrations/create_evaluations_schema.sql`

## Important Notes

### Database Constraints
- Rating: INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5)
- Default value now set to 1
- Trigger explicitly sets rating = 1 for auto-generated evaluations

### Debug Information
- Recent debug logs show successful evaluation generation
- 5 player evaluations created in last hour
- No constraint violations in test queries

## Contact for Questions

The constraint violation should be resolved at the database level. If the issue persists after browser refresh and cache clearing, it likely indicates a caching or connection pooling issue rather than a database problem.

---

**Handover prepared by**: Claude  
**Session health**: Good  
**Todos completed**: 3/3 (All tasks completed)