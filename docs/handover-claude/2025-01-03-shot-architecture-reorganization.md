# Handover: SHOT App Architecture Reorganization

**Date**: 2025-01-03  
**Session Focus**: Analyzing and proposing reorganization of SHOT app into distinct areas (Clubhouse, Perform, Locker, Pulse)  
**Status**: Analysis Complete - Ready for Implementation

## Work Completed

### 1. Comprehensive Codebase Analysis
- Analyzed entire source code structure in `/Users/<USER>/d/shotNew`
- Identified current organization patterns and issues
- Mapped all pages and components to their logical areas

### 2. Created Architecture Proposal
- **Document**: `/docs/shot-app-architecture-proposal.md`
- Proposed feature-based organization with clear boundaries
- Defined scope for each area:
  - **Clubhouse**: Central hub, admin, club management
  - **Perform**: Sports performance, teams, evaluations
  - **Locker**: E-commerce, equipment management
  - **Pulse**: Social feed, news, communications

### 3. Identified Files for Removal/Relocation
- **Document**: `/docs/files-to-remove-and-relocate.md`
- Found ~30% of files are duplicates or obsolete
- Created detailed migration map for all files

## Key Findings

### Major Issues Discovered
1. **5 different login implementations** (Login.tsx, NonIonicLogin.tsx, SimpleLogin.tsx, UpdatedLogin.tsx, UpdatedLoginV2.tsx)
2. **Entire Coach section** (`src/pages/section/Coach/*`) duplicates Perform functionality
3. **Test/demo pages in production** (TestBigCommerce.tsx, CheckDatabaseTables.tsx, ButtonShowcase/)
4. **No clear feature boundaries** - files scattered across multiple directories

### Proposed Solution Structure
```
src/
├── features/
│   ├── clubhouse/
│   ├── perform/
│   ├── locker/
│   └── pulse/
├── shared/
├── core/
└── design-system/
```

## Next Steps

### Phase 1: Setup (Week 1)
1. Create new directory structure
2. Setup TypeScript path aliases
3. Create barrel exports for each feature
4. Update build configuration

### Phase 2: Consolidation (Week 2)
1. Choose primary login implementation (UpdatedLoginV2.tsx)
2. Merge Coach functionality into Perform
3. Remove test/demo pages
4. Consolidate duplicate components

### Phase 3: Migration (Week 3)
1. Move files to new feature directories
2. Update all import paths
3. Fix routing configuration
4. Update tests

### Phase 4: Cleanup (Week 4)
1. Remove obsolete files
2. Update documentation
3. Run full regression tests
4. Team training on new structure

## Important Considerations

### High-Risk Areas
1. **Login consolidation** - All auth flows must be tested
2. **Coach/Perform merge** - Complex functionality with role-based access
3. **Routing updates** - All navigation paths must work

### Dependencies
- Need to update import statements (200+ files affected)
- TypeScript configuration needs path aliases
- Build tools may need updates
- CI/CD pipelines need testing

## Files for Immediate Removal

These files can be safely removed immediately:
- `src/pages/TestBigCommerce.tsx`
- `src/pages/CheckDatabaseTables.tsx`
- `src/pages/ButtonShowcase/*`
- `src/pages/BigCommerceProducts.tsx`

## Technical Context

### Working Directory
- `/Users/<USER>/d/shotNew`

### Key Technologies
- React + TypeScript
- Ionic Framework (being phased out for Shadow DOM components)
- Supabase backend
- Vite build system

### Current Branch
- main (with uncommitted changes)

## Session Metrics
- Total messages: 5
- Tasks completed: 5/5
- Documents created: 2
- Analysis depth: Comprehensive

## Resume Instructions

To continue this work:
1. Review the two created documents in `/docs/`
2. Get approval for the proposed architecture
3. Start with Phase 1 setup tasks
4. Use the file removal/relocation lists as reference
5. Test thoroughly after each phase

## Notes
- The Coach section is particularly complex - it essentially duplicates all Perform functionality and needs careful merging
- Consider using automated tools for import path updates
- Ensure all role-based access controls are maintained during the reorganization
- The proposed structure will significantly improve maintainability and developer experience

---
*Handover generated by Claude*