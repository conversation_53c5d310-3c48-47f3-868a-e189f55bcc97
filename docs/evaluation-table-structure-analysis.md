# Evaluation System Database Structure Analysis

## Overview

The evaluation system currently uses two main tables with significant overlap and complexity:
- `pre_evaluations` - 173 records, 36 columns
- `player_evaluations` - 904 records, 30 columns
- No `evaluations` table exists

## Table Schemas

### 1. pre_evaluations Table

**Purpose**: Track evaluation requests/invitations sent to players for self-assessment

**Key Columns** (36 total):
- **IDs**: id, event_id, player_id, sport_head_id, team_id, template_id, coach_id
- **Status**: status (pending/completed), evaluation_status
- **Timestamps**: requested_at, started_at, completed_at, expires_at, created_at, updated_at, viewed_at, last_reminder_at
- **Player Data**: player_position, responses (JSONB for self-eval answers)
- **Scoring**: mood_rating, confidence_level, points_earned, points_claimed
- **Text Fields**: goals_text, concerns_text
- **Tracking**: access_token, viewed, viewed_by, reminder_count, questions_answered, total_questions, completion_percentage
- **Metadata**: framework_version, week_number, request_method, requested_by

### 2. player_evaluations Table

**Purpose**: Store actual evaluation scores/ratings from coaches

**Key Columns** (30 total):
- **IDs**: id, player_id, evaluator_id, team_id, event_id, session_id, pre_evaluation_id
- **Evaluation Data**: position, category, area, rating, notes, evaluation_criteria_data (JSONB)
- **Status**: evaluation_status, viewed, viewed_at, viewed_by
- **Player Response**: player_rating, player_notes, player_submitted_at, player_viewed, player_viewed_at
- **Metadata**: evaluation_date, week_number, framework_version, sport_type, question, is_event_based
- **Timestamps**: created_at, updated_at

### 3. Relationships

- 536 of 904 player_evaluations are linked to pre_evaluations via `pre_evaluation_id`
- 368 player_evaluations are standalone (no pre_evaluation)
- Both tables store redundant data: player_id, team_id, event_id

## Identified Issues

### 1. Data Redundancy
- **player_id** stored in both tables
- **team_id** stored in both tables  
- **event_id** stored in both tables
- **Position data** stored in 3 places:
  - `team_members.position` (current position)
  - `pre_evaluations.player_position` (position at request time)
  - `player_evaluations.position` (position for criteria matching)

### 2. Complex Status Tracking
- `pre_evaluations.status` - for request status
- `pre_evaluations.evaluation_status` - duplicate purpose
- `player_evaluations.evaluation_status` - evaluation completion status
- Multiple "viewed" fields in both tables

### 3. Mixed Purposes
- pre_evaluations combines:
  - Invitation/request tracking
  - Self-evaluation responses
  - Points/gamification
  - Reminder management
  
- player_evaluations combines:
  - Coach evaluations
  - Player self-ratings
  - Position-specific criteria
  - View tracking

### 4. Unclear Separation
- Two different evaluation flows exist:
  1. Pre-evaluation flow: Request → Self-assess → Coach evaluates
  2. Direct evaluation flow: Coach evaluates directly
- This creates complexity in queries and business logic

## Position Storage Analysis

**Current Positions in Database**:
- GOALKEEPER, CENTRE BACK, FULL BACK, MIDFIELD, STRIKER, WINGER
- Position-specific evaluation criteria exist for each position
- "All" position used for general criteria

**Issues**:
- Position can change between evaluation request and completion
- Unclear which position to use for criteria matching
- Position stored redundantly across tables

## Simplification Recommendations

### Option 1: Merge Tables (Recommended)
Create single `evaluations` table combining both purposes:

```sql
evaluations
- id
- player_id
- evaluator_id (null for self-eval)
- team_id
- event_id
- position_snapshot (position at eval time)
- type (self/coach/peer)
- status (invited/started/completed)
- category
- criteria_data
- score
- notes
- metadata (JSONB for flexible data)
- timestamps
```

**Benefits**:
- Single source of truth
- Simpler queries
- Clear status flow
- Reduced redundancy

### Option 2: Clear Separation
Keep two tables but with distinct purposes:

**evaluation_requests** (rename pre_evaluations):
- Only track invitations/requests
- Remove evaluation data
- Simple status tracking

**evaluation_scores** (rename player_evaluations):
- Only store actual scores/ratings
- Link to request if applicable
- Remove duplicate request data

### Option 3: Minimal Changes
Keep current structure but:
- Remove redundant columns
- Standardize status fields
- Store position once at evaluation creation
- Clear up which table owns which data

## Impact on Current System

**Usage Statistics**:
- 59% of evaluations use the pre-evaluation flow
- 41% are direct evaluations
- Position-specific criteria are actively used

**Migration Considerations**:
- Need to preserve existing data
- Update application code
- Maintain backward compatibility during transition
- Test evaluation flows thoroughly

## Conclusion

The current two-table structure creates unnecessary complexity through:
1. Redundant data storage
2. Unclear separation of concerns
3. Multiple ways to track the same information
4. Confusion about position handling

Merging into a single, well-designed evaluations table would significantly simplify the system while maintaining all current functionality.