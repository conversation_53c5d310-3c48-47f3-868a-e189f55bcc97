# PACT Testing Strategy - EPIC 3: Perform System

## Overview

This document outlines the Consumer-Driven Contract Testing (PACT) strategy for the Perform system. The Perform system focuses on training session management, performance metrics tracking, player development monitoring, and analytics dashboards. PACT tests ensure seamless integration between the frontend applications and backend services throughout the development lifecycle.

## Key Principles

1. **Performance-First Contracts**: Include response time expectations in contracts
2. **Real-time Data Contracts**: Support WebSocket and streaming data patterns
3. **Bulk Operation Support**: Efficient handling of multi-player/multi-metric operations
4. **Analytics Accuracy**: Ensure calculation consistency between consumer and provider
5. **Cross-System Integration**: Maintain compatibility with Assess, Identity, and Schedule systems

## Service Architecture

### Consumers (Frontend)
- **Coach Training Portal**: Session planning and metric entry interfaces
- **Player Performance Dashboard**: Individual performance tracking
- **Parent Progress Portal**: Filtered view of child's development
- **Analytics Dashboard**: Team insights and reporting tools
- **Mobile Metric Entry**: Quick metric capture during training

### Providers (Backend Services)
- **SessionService**: Training session management
- **DrillService**: Drill library and categorization
- **MetricService**: Performance data collection and validation
- **AnalyticsService**: Data aggregation and insights
- **ReportService**: Report generation and distribution
- **ImportService**: Bulk data import and transformation

## PACT Test Structure

### 1. Training Session Management Contracts

#### Create Training Session Contract
```typescript
// Consumer: CoachTrainingPortal
// Provider: SessionService
{
  consumer: "CoachTrainingPortal",
  provider: "SessionService",
  interactions: [
    {
      description: "Create a new training session with drills",
      providerStates: [{
        name: "coach has team assigned",
        params: { coachId: "coach-123", teamId: "team-456" }
      }],
      request: {
        method: "POST",
        path: "/api/perform/sessions",
        headers: {
          "Authorization": "Bearer ${coachToken}",
          "Content-Type": "application/json"
        },
        body: {
          teamId: "team-456",
          date: "2024-03-20",
          startTime: "18:00",
          duration: 90,
          location: "Training Ground A",
          objectives: ["Improve passing accuracy", "Build fitness"],
          drills: [
            {
              drillId: "drill-001",
              duration: 20,
              order: 1,
              intensity: "medium",
              equipment: ["cones", "balls"],
              playerGroups: [
                {
                  name: "Group A",
                  playerIds: ["player-1", "player-2", "player-3"]
                }
              ]
            }
          ],
          sessionType: "technical",
          weatherConditions: "clear"
        }
      },
      response: {
        status: 201,
        headers: {
          "Content-Type": "application/json",
          "X-Response-Time": Matchers.integer({ max: 500 })
        },
        body: {
          id: Matchers.uuid(),
          teamId: "team-456",
          coachId: "coach-123",
          status: "planned",
          totalIntensityScore: Matchers.decimal(2.5, 5.0),
          estimatedCalories: Matchers.integer({ min: 300, max: 800 }),
          drillCount: 1,
          playerCount: Matchers.integer(),
          createdAt: Matchers.iso8601DateTime(),
          sessionPlan: {
            totalDuration: 90,
            warmupTime: 15,
            cooldownTime: 10,
            activeDrillTime: 65,
            intensityProfile: {
              low: 20,
              medium: 50,
              high: 20
            }
          }
        }
      }
    }
  ]
}
```

#### Get Session with Performance Data Contract
```typescript
{
  description: "Get training session with collected metrics",
  request: {
    method: "GET",
    path: "/api/perform/sessions/${sessionId}",
    query: {
      include: "metrics,attendance,drills"
    },
    headers: {
      "Authorization": "Bearer ${coachToken}"
    }
  },
  response: {
    status: 200,
    body: {
      session: {
        id: Matchers.uuid(),
        date: Matchers.date("yyyy-MM-dd"),
        status: "completed",
        attendance: {
          expected: Matchers.integer(),
          actual: Matchers.integer(),
          attendanceRate: Matchers.decimal(0.0, 1.0)
        },
        drills: Matchers.eachLike({
          id: Matchers.uuid(),
          name: Matchers.string(),
          completionStatus: Matchers.term("completed", "completed|partial|skipped"),
          actualDuration: Matchers.integer(),
          playerFeedback: {
            averageRating: Matchers.decimal(1.0, 5.0),
            difficultyScore: Matchers.decimal(1.0, 5.0)
          }
        }),
        metricsCollected: {
          totalMetrics: Matchers.integer(),
          byCategory: {
            physical: Matchers.integer(),
            technical: Matchers.integer(),
            tactical: Matchers.integer()
          },
          completionRate: Matchers.decimal(0.0, 1.0)
        }
      }
    }
  }
}
```

### 2. Drill Library Contracts

#### Search Drills Contract
```typescript
// Consumer: CoachTrainingPortal
// Provider: DrillService
{
  consumer: "CoachTrainingPortal",
  provider: "DrillService",
  interactions: [
    {
      description: "Search drills by category and skill focus",
      request: {
        method: "GET",
        path: "/api/perform/drills",
        query: {
          category: "technical",
          skillFocus: "passing",
          ageGroup: "U16",
          maxDuration: "30",
          page: "1",
          limit: "20"
        },
        headers: {
          "Authorization": "Bearer ${coachToken}"
        }
      },
      response: {
        status: 200,
        body: {
          drills: Matchers.eachLike({
            id: Matchers.uuid(),
            name: Matchers.string(),
            category: "technical",
            skillFocus: ["passing"],
            duration: Matchers.integer({ max: 30 }),
            difficulty: Matchers.term("intermediate", "beginner|intermediate|advanced"),
            equipment: Matchers.eachLike(Matchers.string()),
            minPlayers: Matchers.integer(),
            maxPlayers: Matchers.integer(),
            description: Matchers.string(),
            objectives: Matchers.eachLike(Matchers.string()),
            setup: {
              diagram: Matchers.string(), // URL to diagram
              instructions: Matchers.eachLike(Matchers.string())
            },
            variations: Matchers.eachLike({
              name: Matchers.string(),
              modification: Matchers.string()
            }),
            coachTips: Matchers.eachLike(Matchers.string()),
            commonMistakes: Matchers.eachLike(Matchers.string()),
            rating: Matchers.decimal(1.0, 5.0),
            usageCount: Matchers.integer()
          }, { min: 1 }),
          pagination: {
            total: Matchers.integer(),
            page: 1,
            limit: 20,
            hasMore: Matchers.boolean()
          }
        }
      }
    }
  ]
}
```

### 3. Performance Metrics Contracts

#### Single Metric Entry Contract
```typescript
// Consumer: MobileMetricEntry
// Provider: MetricService
{
  consumer: "MobileMetricEntry",
  provider: "MetricService",
  interactions: [
    {
      description: "Record single performance metric",
      request: {
        method: "POST",
        path: "/api/perform/metrics",
        headers: {
          "Authorization": "Bearer ${coachToken}",
          "Content-Type": "application/json"
        },
        body: {
          playerId: "player-789",
          sessionId: "session-123",
          metricType: "sprint_time",
          value: 4.82,
          unit: "seconds",
          distance: 30,
          timestamp: Matchers.iso8601DateTime(),
          conditions: {
            surface: "grass",
            weather: "dry"
          },
          notes: "Good improvement from last week"
        }
      },
      response: {
        status: 201,
        body: {
          id: Matchers.uuid(),
          playerId: "player-789",
          validated: true,
          percentile: Matchers.integer({ min: 1, max: 100 }),
          improvement: {
            fromLast: Matchers.decimal(-5.0, 5.0),
            fromAverage: Matchers.decimal(-10.0, 10.0),
            trend: Matchers.term("improving", "improving|stable|declining")
          },
          anomalyDetected: false,
          savedAt: Matchers.iso8601DateTime()
        }
      }
    }
  ]
}
```

#### Bulk Metric Entry Contract
```typescript
{
  description: "Record metrics for multiple players",
  request: {
    method: "POST",
    path: "/api/perform/metrics/bulk",
    headers: {
      "Authorization": "Bearer ${coachToken}",
      "Content-Type": "application/json"
    },
    body: {
      sessionId: "session-123",
      metricType: "beep_test_level",
      metrics: Matchers.eachLike({
        playerId: Matchers.uuid(),
        value: Matchers.decimal(5.0, 15.0),
        timestamp: Matchers.iso8601DateTime()
      }, { min: 5, max: 20 })
    }
  },
  response: {
    status: 200,
    headers: {
      "X-Response-Time": Matchers.integer({ max: 3000 })
    },
    body: {
      saved: Matchers.integer(),
      failed: 0,
      validationErrors: [],
      teamStats: {
        average: Matchers.decimal(8.0, 12.0),
        median: Matchers.decimal(8.0, 12.0),
        standardDeviation: Matchers.decimal(0.5, 2.0),
        improvement: {
          teamAverage: Matchers.decimal(-2.0, 2.0),
          improvingPlayers: Matchers.integer()
        }
      },
      processingTime: Matchers.integer({ max: 2000 })
    }
  }
}
```

#### Import Metrics Contract
```typescript
{
  description: "Import metrics from CSV file",
  request: {
    method: "POST",
    path: "/api/perform/metrics/import",
    headers: {
      "Authorization": "Bearer ${coachToken}",
      "Content-Type": "multipart/form-data"
    },
    body: {
      file: Matchers.file("metrics.csv"),
      sessionId: "session-123",
      mappingConfig: {
        playerIdentifier: "jersey_number",
        valueColumn: "time",
        metricType: "sprint_30m"
      }
    }
  },
  response: {
    status: 200,
    headers: {
      "X-Import-Job-Id": Matchers.uuid()
    },
    body: {
      jobId: Matchers.uuid(),
      status: "processing",
      rowsDetected: Matchers.integer(),
      estimatedTime: Matchers.integer({ max: 30 }),
      preview: {
        validRows: Matchers.integer(),
        invalidRows: Matchers.integer(),
        warnings: Matchers.eachLike({
          row: Matchers.integer(),
          issue: Matchers.string(),
          suggestion: Matchers.string()
        }),
        sampleData: Matchers.eachLike({
          playerName: Matchers.string(),
          value: Matchers.decimal(),
          status: "valid"
        }, { max: 5 })
      }
    }
  }
}
```

### 4. Player Development Tracking Contracts

#### Get Player Performance Profile Contract
```typescript
// Consumer: PlayerPerformanceDashboard
// Provider: AnalyticsService
{
  consumer: "PlayerPerformanceDashboard",
  provider: "AnalyticsService",
  interactions: [
    {
      description: "Get comprehensive player performance profile",
      request: {
        method: "GET",
        path: "/api/perform/analytics/player/${playerId}/profile",
        query: {
          dateFrom: "2024-01-01",
          dateTo: "2024-03-20",
          includeGoals: "true",
          includeComparisons: "true"
        },
        headers: {
          "Authorization": "Bearer ${playerToken}"
        }
      },
      response: {
        status: 200,
        body: {
          player: {
            id: Matchers.uuid(),
            name: Matchers.string(),
            position: Matchers.string(),
            ageGroup: Matchers.string()
          },
          performanceMetrics: {
            physical: {
              sprintSpeed: {
                current: Matchers.decimal(5.0, 10.0),
                percentile: Matchers.integer({ min: 1, max: 100 }),
                trend: Matchers.eachLike({
                  date: Matchers.date("yyyy-MM-dd"),
                  value: Matchers.decimal()
                }, { min: 5 }),
                improvement: Matchers.decimal(-10.0, 10.0)
              },
              endurance: {
                current: Matchers.decimal(8.0, 15.0),
                percentile: Matchers.integer({ min: 1, max: 100 }),
                trend: Matchers.eachLike({
                  date: Matchers.date("yyyy-MM-dd"),
                  value: Matchers.decimal()
                })
              }
            },
            technical: {
              passingAccuracy: {
                current: Matchers.decimal(60.0, 95.0),
                trend: Matchers.eachLike({
                  date: Matchers.date("yyyy-MM-dd"),
                  value: Matchers.decimal()
                })
              }
            }
          },
          goals: Matchers.eachLike({
            id: Matchers.uuid(),
            title: Matchers.string(),
            category: Matchers.string(),
            target: {
              value: Matchers.decimal(),
              unit: Matchers.string(),
              deadline: Matchers.date("yyyy-MM-dd")
            },
            progress: Matchers.decimal(0.0, 100.0),
            status: Matchers.term("on_track", "on_track|at_risk|achieved|missed"),
            milestones: Matchers.eachLike({
              description: Matchers.string(),
              completed: Matchers.boolean(),
              date: Matchers.date("yyyy-MM-dd")
            })
          }),
          comparisons: {
            teamAverage: {
              sprintSpeed: Matchers.decimal(),
              endurance: Matchers.decimal()
            },
            positionAverage: {
              sprintSpeed: Matchers.decimal(),
              endurance: Matchers.decimal()
            },
            personalBest: {
              sprintSpeed: {
                value: Matchers.decimal(),
                date: Matchers.date("yyyy-MM-dd")
              }
            }
          }
        }
      }
    }
  ]
}
```

#### Create SMART Goal Contract
```typescript
{
  description: "Create SMART goal for player development",
  request: {
    method: "POST",
    path: "/api/perform/goals",
    headers: {
      "Authorization": "Bearer ${coachToken}",
      "Content-Type": "application/json"
    },
    body: {
      playerId: "player-789",
      title: "Improve 30m sprint time",
      category: "physical",
      specific: "Reduce 30m sprint time through speed training",
      measurable: {
        metric: "sprint_30m",
        currentValue: 4.82,
        targetValue: 4.50,
        unit: "seconds"
      },
      achievable: "Based on 0.32s improvement potential identified",
      relevant: "Key requirement for striker position",
      timeBound: {
        startDate: "2024-03-20",
        endDate: "2024-06-20",
        milestones: [
          {
            date: "2024-04-20",
            target: 4.70,
            description: "First month checkpoint"
          },
          {
            date: "2024-05-20",
            target: 4.60,
            description: "Second month checkpoint"
          }
        ]
      },
      assignedBy: "coach-123",
      visibility: ["player", "parent", "coach"]
    }
  },
  response: {
    status: 201,
    body: {
      id: Matchers.uuid(),
      playerId: "player-789",
      status: "active",
      progressTracking: {
        automatic: true,
        metricType: "sprint_30m",
        updateFrequency: "on_new_metric"
      },
      notifications: {
        milestoneReminders: true,
        progressUpdates: "weekly",
        recipients: ["player", "parent"]
      },
      createdAt: Matchers.iso8601DateTime()
    }
  }
}
```

### 5. Parent Portal Contracts

#### Get Child Performance Summary Contract
```typescript
// Consumer: ParentProgressPortal
// Provider: AnalyticsService
{
  consumer: "ParentProgressPortal",
  provider: "AnalyticsService",
  interactions: [
    {
      description: "Parent views child's performance summary",
      providerStates: [{
        name: "parent has child with performance data",
        params: { 
          parentId: "parent-456",
          childId: "player-789",
          hasRecentMetrics: true
        }
      }],
      request: {
        method: "GET",
        path: "/api/perform/parent/children/${childId}/summary",
        headers: {
          "Authorization": "Bearer ${parentToken}"
        }
      },
      response: {
        status: 200,
        body: {
          child: {
            id: "player-789",
            name: Matchers.string(),
            teams: Matchers.eachLike({
              name: Matchers.string(),
              role: Matchers.string()
            })
          },
          recentHighlights: Matchers.eachLike({
            type: Matchers.term("achievement", "achievement|improvement|milestone"),
            title: Matchers.string(),
            description: Matchers.string(),
            date: Matchers.date("yyyy-MM-dd"),
            icon: Matchers.string()
          }, { max: 5 }),
          currentGoals: Matchers.eachLike({
            title: Matchers.string(),
            progress: Matchers.integer({ min: 0, max: 100 }),
            status: Matchers.string(),
            nextMilestone: {
              description: Matchers.string(),
              dueDate: Matchers.date("yyyy-MM-dd")
            }
          }),
          attendanceSummary: {
            last30Days: {
              attended: Matchers.integer(),
              total: Matchers.integer(),
              rate: Matchers.decimal(0.0, 1.0)
            }
          },
          coachNotes: Matchers.eachLike({
            date: Matchers.date("yyyy-MM-dd"),
            note: Matchers.string(),
            sentiment: Matchers.term("positive", "positive|neutral|constructive")
          }, { max: 3 }),
          nextSteps: Matchers.eachLike({
            area: Matchers.string(),
            recommendation: Matchers.string()
          })
        }
      }
    }
  ]
}
```

### 6. Analytics Dashboard Contracts

#### Get Team Analytics Contract
```typescript
// Consumer: AnalyticsDashboard
// Provider: AnalyticsService
{
  consumer: "AnalyticsDashboard",
  provider: "AnalyticsService",
  interactions: [
    {
      description: "Get comprehensive team analytics",
      request: {
        method: "GET",
        path: "/api/perform/analytics/team/${teamId}",
        query: {
          period: "last_90_days",
          metrics: "physical,technical,tactical",
          groupBy: "position"
        },
        headers: {
          "Authorization": "Bearer ${coachToken}"
        }
      },
      response: {
        status: 200,
        headers: {
          "X-Response-Time": Matchers.integer({ max: 2000 }),
          "X-Cache-Status": Matchers.term("HIT", "HIT|MISS")
        },
        body: {
          team: {
            id: Matchers.uuid(),
            name: Matchers.string(),
            playerCount: Matchers.integer()
          },
          period: {
            from: Matchers.date("yyyy-MM-dd"),
            to: Matchers.date("yyyy-MM-dd"),
            sessionsAnalyzed: Matchers.integer(),
            metricsCollected: Matchers.integer()
          },
          analytics: {
            physical: {
              teamAverages: {
                sprintSpeed: {
                  value: Matchers.decimal(),
                  change: Matchers.decimal(-10.0, 10.0),
                  trend: "improving"
                },
                endurance: {
                  value: Matchers.decimal(),
                  change: Matchers.decimal(-10.0, 10.0)
                }
              },
              byPosition: Matchers.eachLike({
                position: Matchers.string(),
                averages: {
                  sprintSpeed: Matchers.decimal(),
                  endurance: Matchers.decimal()
                },
                topPerformers: Matchers.eachLike({
                  playerId: Matchers.uuid(),
                  name: Matchers.string(),
                  value: Matchers.decimal()
                }, { max: 3 })
              })
            },
            insights: Matchers.eachLike({
              type: Matchers.term("trend", "trend|anomaly|recommendation"),
              category: Matchers.string(),
              description: Matchers.string(),
              impact: Matchers.term("high", "low|medium|high"),
              suggestedAction: Matchers.string(),
              affectedPlayers: Matchers.eachLike(Matchers.uuid())
            }, { min: 3, max: 10 }),
            correlations: Matchers.eachLike({
              metric1: Matchers.string(),
              metric2: Matchers.string(),
              coefficient: Matchers.decimal(-1.0, 1.0),
              significance: Matchers.decimal(0.0, 1.0)
            })
          }
        }
      }
    }
  ]
}
```

#### Real-time Dashboard Updates Contract (WebSocket)
```typescript
{
  description: "Subscribe to real-time metric updates",
  transport: "websocket",
  request: {
    action: "subscribe",
    channel: "team-metrics",
    params: {
      teamId: "team-456",
      metrics: ["sprint_time", "endurance_level"]
    }
  },
  response: {
    type: "metric-update",
    data: {
      timestamp: Matchers.iso8601DateTime(),
      updates: Matchers.eachLike({
        playerId: Matchers.uuid(),
        playerName: Matchers.string(),
        metric: Matchers.string(),
        oldValue: Matchers.decimal(),
        newValue: Matchers.decimal(),
        percentileChange: Matchers.integer({ min: -50, max: 50 }),
        alert: Matchers.somethingLike({
          type: "improvement",
          message: Matchers.string()
        })
      })
    }
  }
}
```

### 7. Report Generation Contracts

#### Generate Performance Report Contract
```typescript
// Consumer: AnalyticsDashboard
// Provider: ReportService
{
  consumer: "AnalyticsDashboard",
  provider: "ReportService",
  interactions: [
    {
      description: "Generate team performance report",
      request: {
        method: "POST",
        path: "/api/perform/reports/generate",
        headers: {
          "Authorization": "Bearer ${coachToken}",
          "Content-Type": "application/json"
        },
        body: {
          reportType: "team_performance",
          teamId: "team-456",
          period: {
            from: "2024-01-01",
            to: "2024-03-20"
          },
          template: "standard_monthly",
          sections: [
            "executive_summary",
            "physical_metrics",
            "technical_progress",
            "player_rankings",
            "recommendations"
          ],
          format: "pdf",
          distribution: {
            immediate: ["<EMAIL>"],
            scheduled: {
              recipients: ["<EMAIL>"],
              sendAt: "2024-03-21T09:00:00Z"
            }
          }
        }
      },
      response: {
        status: 202,
        headers: {
          "Location": "/api/perform/reports/jobs/${jobId}"
        },
        body: {
          jobId: Matchers.uuid(),
          status: "processing",
          estimatedCompletion: Matchers.iso8601DateTime(),
          reportDetails: {
            pages: Matchers.integer({ min: 10, max: 50 }),
            charts: Matchers.integer({ min: 5, max: 20 }),
            players: Matchers.integer(),
            dataPoints: Matchers.integer({ min: 100 })
          },
          webhookUrl: Matchers.string()
        }
      }
    }
  ]
}
```

## Frontend Development Process

### 1. Performance-Optimized Development

```typescript
// Example: Real-time dashboard with caching
import { usePerformanceMetrics } from '@/hooks/usePerformanceMetrics';
import { useWebSocket } from '@/hooks/useWebSocket';

export const TeamDashboard: React.FC = () => {
  const { metrics, loading, error } = usePerformanceMetrics({
    cacheTime: 5 * 60 * 1000, // 5 minute cache
    staleTime: 60 * 1000 // Consider stale after 1 minute
  });
  
  // Real-time updates via WebSocket
  const { subscribe } = useWebSocket();
  
  useEffect(() => {
    const unsubscribe = subscribe('team-metrics', (update) => {
      // Update cached data with real-time changes
      updateMetricsCache(update);
    });
    
    return unsubscribe;
  }, []);
  
  return (
    <DashboardLayout>
      <MetricGrid metrics={metrics} />
      <PerformanceCharts data={metrics} />
      <InsightsPanel insights={metrics.insights} />
    </DashboardLayout>
  );
};
```

### 2. Bulk Operations Handling

```typescript
// Example: Efficient bulk metric entry
class MetricService {
  async saveBulkMetrics(metrics: BulkMetricData): Promise<BulkSaveResult> {
    // Validate all metrics client-side first
    const validation = await this.validateBulkMetrics(metrics);
    
    if (!validation.isValid) {
      throw new ValidationError(validation.errors);
    }
    
    // Use PACT stub during development
    const response = await fetch(`${API_BASE}/api/perform/metrics/bulk`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(metrics)
    });
    
    const result = await response.json();
    
    // Update local cache with new values
    this.updateMetricCache(result.saved);
    
    return result;
  }
  
  private async validateBulkMetrics(metrics: BulkMetricData): Promise<ValidationResult> {
    // Client-side validation matching server rules
    const errors = [];
    
    for (const metric of metrics.metrics) {
      if (metric.value < 0 || metric.value > this.getMaxValue(metrics.metricType)) {
        errors.push({
          playerId: metric.playerId,
          error: 'Value out of valid range'
        });
      }
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
```

### 3. Analytics Integration Testing

```typescript
describe('Analytics Dashboard Contracts', () => {
  it('should handle large dataset responses efficiently', async () => {
    const startTime = Date.now();
    
    const analytics = await analyticsService.getTeamAnalytics({
      teamId: 'team-456',
      period: 'last_90_days',
      metrics: 'all'
    });
    
    const responseTime = Date.now() - startTime;
    
    // Performance assertion
    expect(responseTime).toBeLessThan(2000);
    
    // Data completeness
    expect(analytics).toMatchObject({
      analytics: {
        physical: expect.any(Object),
        technical: expect.any(Object),
        insights: expect.arrayContaining([
          expect.objectContaining({
            type: expect.any(String),
            description: expect.any(String)
          })
        ])
      }
    });
  });
});
```

### 4. Parent Portal Access Control

```typescript
// Example: Filtered data access for parents
describe('Parent Portal Access', () => {
  it('should only return data for parent\'s children', async () => {
    const parentToken = 'parent-auth-token';
    
    // This should succeed
    const childData = await parentService.getChildSummary('player-789', parentToken);
    expect(childData.child.id).toBe('player-789');
    
    // This should fail with 403
    await expect(
      parentService.getChildSummary('other-player-123', parentToken)
    ).rejects.toThrow('Forbidden');
  });
  
  it('should filter sensitive coach notes', async () => {
    const summary = await parentService.getChildSummary('player-789', parentToken);
    
    // Parent should not see internal coach notes
    expect(summary.coachNotes).not.toContainEqual(
      expect.objectContaining({
        visibility: 'coach_only'
      })
    );
  });
});
```

## Test Data Scenarios

### Scenario 1: Training Session with Mixed Abilities
```json
{
  "session": {
    "id": "session-mixed",
    "team": "U16 Eagles",
    "players": 22,
    "performanceLevels": {
      "elite": 4,
      "advanced": 10,
      "intermediate": 6,
      "beginner": 2
    },
    "metrics": {
      "sprintTimes": {
        "range": [4.2, 5.8],
        "distribution": "normal"
      }
    }
  }
}
```

### Scenario 2: Long-term Development Tracking
```json
{
  "player": {
    "id": "player-789",
    "trackingPeriod": "6_months",
    "dataPoints": 150,
    "goals": 3,
    "improvements": {
      "sprint30m": "-0.32s",
      "endurance": "+15%",
      "passingAccuracy": "+12%"
    }
  }
}
```

### Scenario 3: Large Team Import
```json
{
  "import": {
    "teamSize": 45,
    "sessions": 20,
    "metricsPerSession": 5,
    "totalDataPoints": 4500,
    "processingTime": "< 30 seconds"
  }
}
```

## Performance Benchmarks

```typescript
// Performance expectations in contracts
const performanceContracts = {
  dashboardLoad: {
    maxResponseTime: 2000,
    maxRenderTime: 500
  },
  bulkMetricSave: {
    20_players: 3000,
    50_players: 5000
  },
  reportGeneration: {
    standard: 10000,
    comprehensive: 30000
  },
  realtimeLatency: {
    p50: 100,
    p95: 500,
    p99: 1000
  }
};
```

## CI/CD Integration

### Performance Testing Pipeline
```yaml
name: Perform System Tests
on: [push, pull_request]

jobs:
  performance-tests:
    steps:
      - name: Run PACT consumer tests
        run: npm run test:pact:perform
        
      - name: Run performance benchmarks
        run: npm run test:performance
        
      - name: Verify response times
        run: |
          npm run test:response-times
          # Fail if any endpoint exceeds SLA
          
      - name: Load test with bulk operations
        run: npm run test:load:bulk-operations
```

## WebSocket Contract Testing

```typescript
// Special handling for real-time contracts
describe('WebSocket Contracts', () => {
  let mockWebSocket: MockWebSocket;
  
  beforeEach(() => {
    mockWebSocket = new MockWebSocket();
    mockWebSocket.loadPactExpectations('./pacts/websocket-contracts.json');
  });
  
  it('should receive real-time metric updates', async () => {
    const updates = [];
    
    mockWebSocket.on('metric-update', (data) => {
      updates.push(data);
    });
    
    mockWebSocket.send({
      action: 'subscribe',
      channel: 'team-metrics',
      params: { teamId: 'team-456' }
    });
    
    // Wait for updates
    await waitFor(() => expect(updates).toHaveLength(1));
    
    expect(updates[0]).toMatchObject({
      type: 'metric-update',
      data: {
        updates: expect.arrayContaining([
          expect.objectContaining({
            playerId: expect.any(String),
            metric: expect.any(String)
          })
        ])
      }
    });
  });
});
```

## Getting Started

```bash
# Install dependencies
npm install --save-dev @pact-foundation/pact

# Start stub server with performance contracts
npm run pact:stub-server:perform

# Run consumer tests
npm run test:pact:perform

# Run performance benchmarks
npm run test:performance:benchmarks

# Monitor real-time contract compliance
npm run pact:monitor:websocket
```

## Support & Resources

- **Perform System Docs**: `/docs/perform/`
- **Metric Definitions**: `/docs/perform/metrics-glossary.md`
- **Dashboard Templates**: `/templates/dashboards/`
- **Team Contact**: #perform-system-dev on Slack

This PACT testing strategy ensures the Perform system delivers high-performance training management, accurate analytics, and real-time insights while maintaining seamless integration across all system components.