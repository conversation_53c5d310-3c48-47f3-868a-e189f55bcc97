# PACT Testing Strategy - EPIC 2: Identity System

## Overview

This document outlines the Consumer-Driven Contract Testing (PACT) strategy for the Identity system. Given the critical emphasis on code reuse (80%+) and zero breaking changes, our PACT tests will ensure new identity features integrate seamlessly with existing systems while maintaining backward compatibility.

## Key Principles

1. **Wrapper-Based Testing**: Test new components that wrap existing ones without breaking legacy contracts
2. **Addition-Only Contracts**: New endpoints only; existing contracts remain unchanged
3. **Parallel System Support**: Ensure old and new systems can run simultaneously
4. **Feature Flag Awareness**: Contracts support gradual rollout scenarios
5. **Family Relationship Integrity**: Complex parent-child relationships must maintain consistency

## Service Architecture

### Consumers (Frontend)
- **Identity Dashboard**: New unified identity management interface
- **Unified Login Page**: Consolidates 5+ login pages
- **Family Management UI**: Parent account management
- **Onboarding Flows**: Role-specific setup wizards
- **Legacy Components**: Existing login pages (must continue working)

### Providers (Backend Services)
- **AuthenticationService**: Extended auth with username support
- **FamilyService**: New service for parent-child relationships
- **ProfileService**: Enhanced with identity features
- **OnboardingService**: New service for user onboarding
- **LegacyAuthService**: Existing auth (unchanged)

## PACT Test Structure

### 1. Authentication Contracts

#### Email Login Contract (Extended)
```typescript
// Consumer: UnifiedLoginPage
// Provider: AuthenticationService
{
  consumer: "UnifiedLoginPage",
  provider: "AuthenticationService",
  interactions: [
    {
      description: "Login with email credentials",
      request: {
        method: "POST",
        path: "/api/auth/login",
        headers: {
          "Content-Type": "application/json"
        },
        body: {
          email: Matchers.email("<EMAIL>"),
          password: Matchers.string("password123"),
          loginType: "email"
        }
      },
      response: {
        status: 200,
        headers: {
          "Content-Type": "application/json"
        },
        body: {
          user: {
            id: Matchers.uuid(),
            email: "<EMAIL>",
            profiles: Matchers.eachLike({
              id: Matchers.uuid(),
              name: Matchers.string(),
              role: Matchers.term("parent", "parent|player|coach"),
              avatar: Matchers.somethingLike({
                type: "sporthead",
                id: Matchers.string()
              })
            }, { min: 1 }),
            defaultProfileId: Matchers.uuid()
          },
          session: {
            access_token: Matchers.string(),
            refresh_token: Matchers.string(),
            expires_at: Matchers.integer()
          },
          identityFeatures: {
            hasFamily: Matchers.boolean(),
            onboardingComplete: Matchers.boolean(),
            featureFlags: {
              identityDashboard: Matchers.boolean(),
              familyManagement: Matchers.boolean()
            }
          }
        }
      }
    }
  ]
}
```

#### Username Login Contract (New)
```typescript
{
  description: "Login with username (child account)",
  request: {
    method: "POST",
    path: "/api/auth/login",
    headers: {
      "Content-Type": "application/json"
    },
    body: {
      username: "johnny_doe",
      password: "password123",
      loginType: "username"
    }
  },
  response: {
    status: 200,
    body: {
      user: {
        id: Matchers.uuid(),
        email: "<EMAIL>",
        isChildAccount: true,
        profiles: Matchers.eachLike({
          id: Matchers.uuid(),
          name: "Johnny Doe",
          role: "player",
          parentProfileId: Matchers.uuid(),
          restrictions: {
            canManageFamily: false,
            requiresParentApproval: ["payment", "personal_info"]
          }
        }, { min: 1, max: 1 })
      },
      session: {
        access_token: Matchers.string(),
        refresh_token: Matchers.string(),
        expires_at: Matchers.integer()
      }
    }
  }
}
```

#### Legacy Login Compatibility Contract
```typescript
// Ensure old login endpoints still work
{
  consumer: "LegacyLoginPage",
  provider: "LegacyAuthService",
  interactions: [
    {
      description: "Legacy login endpoint continues working",
      request: {
        method: "POST",
        path: "/auth/signin", // Old endpoint
        body: {
          email: Matchers.email(),
          password: Matchers.string()
        }
      },
      response: {
        status: 200,
        body: {
          // Original response format unchanged
          token: Matchers.string(),
          user: {
            id: Matchers.uuid(),
            email: Matchers.email()
          }
        }
      }
    }
  ]
}
```

### 2. Family Management Contracts

#### Create Child Account Contract
```typescript
// Consumer: FamilyManagementUI
// Provider: FamilyService
{
  consumer: "FamilyManagementUI",
  provider: "FamilyService",
  interactions: [
    {
      description: "Parent creates child account",
      providerStates: [{
        name: "parent is authenticated",
        params: { parentId: "parent-123" }
      }],
      request: {
        method: "POST",
        path: "/api/identity/family/children",
        headers: {
          "Authorization": "Bearer ${parentToken}",
          "Content-Type": "application/json"
        },
        body: {
          firstName: "Johnny",
          lastName: "Doe",
          dateOfBirth: "2010-05-15",
          desiredUsername: "johnny_doe",
          teamInviteToken: Matchers.string() // Optional
        }
      },
      response: {
        status: 201,
        body: {
          childProfile: {
            id: Matchers.uuid(),
            userId: Matchers.uuid(),
            firstName: "Johnny",
            lastName: "Doe",
            username: "johnny_doe",
            email: "<EMAIL>",
            role: "player",
            parentProfileId: "parent-123",
            temporaryPassword: Matchers.regex(/^[A-Za-z0-9]{8}$/),
            avatar: {
              type: "sporthead",
              id: "default-youth-01"
            }
          },
          familyRelationship: {
            id: Matchers.uuid(),
            parentProfileId: "parent-123",
            childProfileId: Matchers.uuid(),
            relationship: "parent",
            createdAt: Matchers.iso8601DateTime()
          },
          onboardingUrl: Matchers.string()
        }
      }
    }
  ]
}
```

#### Get Family Members Contract
```typescript
{
  description: "Get all family members for account switching",
  request: {
    method: "GET",
    path: "/api/identity/family/members",
    headers: {
      "Authorization": "Bearer ${parentToken}"
    }
  },
  response: {
    status: 200,
    body: {
      currentProfileId: Matchers.uuid(),
      familyMembers: Matchers.eachLike({
        profileId: Matchers.uuid(),
        name: Matchers.string(),
        role: Matchers.term("player", "parent|player"),
        username: Matchers.string(),
        avatar: {
          type: "sporthead",
          id: Matchers.string(),
          color: Matchers.hexColor()
        },
        isCurrentUser: Matchers.boolean(),
        lastActiveAt: Matchers.iso8601DateTime(),
        teams: Matchers.eachLike({
          id: Matchers.uuid(),
          name: Matchers.string(),
          role: Matchers.string()
        })
      }, { min: 1 }),
      switchingEnabled: true
    }
  }
}
```

#### Switch Account Contract
```typescript
{
  description: "Switch between family accounts",
  request: {
    method: "POST",
    path: "/api/identity/family/switch-account",
    headers: {
      "Authorization": "Bearer ${currentToken}",
      "Content-Type": "application/json"
    },
    body: {
      targetProfileId: Matchers.uuid(),
      preserveContext: {
        returnUrl: "/teams/team-123/events",
        contextData: {
          teamId: "team-123",
          view: "events"
        }
      }
    }
  },
  response: {
    status: 200,
    body: {
      newSession: {
        access_token: Matchers.string(),
        refresh_token: Matchers.string(),
        expires_at: Matchers.integer()
      },
      switchedProfile: {
        id: Matchers.uuid(),
        name: Matchers.string(),
        role: Matchers.string(),
        permissions: Matchers.eachLike(Matchers.string())
      },
      previousProfileId: Matchers.uuid(),
      contextRestored: true
    }
  }
}
```

### 3. Profile Management Contracts

#### Update Profile Contract (Extended)
```typescript
// Consumer: ProfileManagementUI
// Provider: ProfileService
{
  consumer: "ProfileManagementUI",
  provider: "ProfileService",
  interactions: [
    {
      description: "Update profile with identity enhancements",
      request: {
        method: "PATCH",
        path: "/api/profiles/${profileId}",
        headers: {
          "Authorization": "Bearer ${userToken}",
          "Content-Type": "application/json"
        },
        body: {
          // Existing fields preserved
          firstName: "John",
          lastName: "Doe",
          
          // New identity fields
          avatar: {
            type: "sporthead",
            id: "striker-01",
            customization: {
              primaryColor: "#FF5733",
              secondaryColor: "#33FF57"
            }
          },
          jerseyNumber: "10",
          position: "striker",
          
          // Parent-specific fields
          emergencyContacts: Matchers.eachLike({
            name: Matchers.string(),
            phone: Matchers.string(),
            relationship: Matchers.string()
          }, { min: 2 })
        }
      },
      response: {
        status: 200,
        body: {
          profile: {
            id: Matchers.uuid(),
            // All fields echoed back
            completionPercentage: Matchers.integer({ min: 0, max: 100 }),
            missingRequiredFields: Matchers.eachLike(Matchers.string()),
            lastUpdated: Matchers.iso8601DateTime()
          }
        }
      }
    }
  ]
}
```

#### Get Profile Completion Contract
```typescript
{
  description: "Check profile completion for feature access",
  request: {
    method: "GET",
    path: "/api/profiles/${profileId}/completion",
    headers: {
      "Authorization": "Bearer ${userToken}"
    }
  },
  response: {
    status: 200,
    body: {
      profileId: Matchers.uuid(),
      role: Matchers.string(),
      completionPercentage: Matchers.integer({ min: 0, max: 100 }),
      requiredFields: {
        completed: Matchers.eachLike(Matchers.string()),
        missing: Matchers.eachLike(Matchers.string())
      },
      featureAccess: {
        canRSVP: Matchers.boolean(),
        canViewEvaluations: Matchers.boolean(),
        canManageFamily: Matchers.boolean()
      },
      nextSteps: Matchers.eachLike({
        field: Matchers.string(),
        description: Matchers.string(),
        required: Matchers.boolean()
      })
    }
  }
}
```

### 4. Onboarding Contracts

#### Validate Invite Token Contract
```typescript
// Consumer: OnboardingFlow
// Provider: OnboardingService
{
  consumer: "OnboardingFlow",
  provider: "OnboardingService",
  interactions: [
    {
      description: "Validate team invitation token",
      request: {
        method: "POST",
        path: "/api/identity/onboarding/validate-token",
        headers: {
          "Content-Type": "application/json"
        },
        body: {
          token: Matchers.string("invite_abc123")
        }
      },
      response: {
        status: 200,
        body: {
          valid: true,
          inviteType: "team_player",
          teamInfo: {
            id: Matchers.uuid(),
            name: "U16 Eagles",
            sport: "football",
            ageGroup: "U16"
          },
          suggestedRole: "player",
          expiresAt: Matchers.iso8601DateTime(),
          invitedBy: {
            name: Matchers.string(),
            role: "coach"
          }
        }
      }
    }
  ]
}
```

#### Get Onboarding Steps Contract
```typescript
{
  description: "Get role-specific onboarding steps",
  request: {
    method: "GET",
    path: "/api/identity/onboarding/steps",
    query: {
      role: "player",
      hasInvite: "true"
    },
    headers: {
      "Authorization": "Bearer ${newUserToken}"
    }
  },
  response: {
    status: 200,
    body: {
      steps: Matchers.eachLike({
        id: Matchers.string(),
        title: Matchers.string(),
        description: Matchers.string(),
        required: Matchers.boolean(),
        completed: Matchers.boolean(),
        order: Matchers.integer(),
        component: Matchers.string(),
        validationRules: Matchers.eachLike({
          field: Matchers.string(),
          rule: Matchers.string()
        })
      }, { min: 3 }),
      currentStep: Matchers.integer(),
      progress: Matchers.integer({ min: 0, max: 100 }),
      canSkip: Matchers.boolean()
    }
  }
}
```

## Frontend Development Process

### 1. Working with Existing Code

```typescript
// Example: Wrapping existing login component
import { UpdatedLoginV2 } from '@/components/existing/UpdatedLoginV2';
import { useIdentityFeatures } from '@/hooks/useIdentityFeatures';

export const UnifiedLoginPage: React.FC = () => {
  const { isIdentityEnabled } = useIdentityFeatures();
  
  if (!isIdentityEnabled) {
    // Feature flag off: use existing component
    return <UpdatedLoginV2 />;
  }
  
  // Enhanced version with identity features
  return (
    <IdentityWrapper>
      <UpdatedLoginV2 
        onSuccess={handleIdentityLogin}
        additionalFields={['username']}
      />
      <FamilyLoginOptions />
    </IdentityWrapper>
  );
};
```

### 2. Contract-Based Development

```typescript
// Frontend service using PACT stubs
class IdentityService {
  private api = process.env.REACT_APP_USE_STUBS 
    ? 'http://localhost:8080'  // PACT stub server
    : process.env.REACT_APP_API_URL;
    
  async createChildAccount(childData: CreateChildRequest): Promise<ChildProfile> {
    // This works against PACT stubs during development
    const response = await fetch(`${this.api}/api/identity/family/children`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(childData)
    });
    
    if (!response.ok) {
      throw new Error(`Failed to create child account: ${response.statusText}`);
    }
    
    return response.json();
  }
  
  // Wrapper around existing profile service
  async updateProfile(profileId: string, updates: ProfileUpdate): Promise<Profile> {
    // First try new endpoint with identity features
    try {
      return await this.updateProfileWithIdentity(profileId, updates);
    } catch (error) {
      // Fallback to legacy endpoint if identity not available
      console.warn('Identity features not available, using legacy profile update');
      return await legacyProfileService.updateProfile(profileId, updates);
    }
  }
}
```

### 3. Testing Code Reuse

```typescript
describe('Identity System Code Reuse', () => {
  it('should reuse existing UpdatedLoginV2 component', () => {
    // Verify wrapper doesn't break existing functionality
    const wrapper = mount(<UnifiedLoginPage />);
    const legacyLogin = wrapper.find('UpdatedLoginV2');
    
    expect(legacyLogin).toExist();
    expect(legacyLogin.props()).toMatchObject({
      // All original props still supported
      onSuccess: expect.any(Function),
      onError: expect.any(Function)
    });
  });
  
  it('should maintain backward compatibility with legacy auth', async () => {
    // Old endpoint should still work
    const legacyResponse = await fetch('/auth/signin', {
      method: 'POST',
      body: JSON.stringify({ email: '<EMAIL>', password: 'pass' })
    });
    
    expect(legacyResponse.ok).toBe(true);
    expect(await legacyResponse.json()).toMatchObject({
      token: expect.any(String),
      user: expect.any(Object)
    });
  });
});
```

### 4. Zero Breaking Changes Verification

```typescript
// Contract test ensuring no breaking changes
describe('Zero Breaking Changes', () => {
  const pactVerifier = new Verifier({
    provider: 'AuthenticationService',
    providerBaseUrl: 'http://localhost:3000',
    
    // Verify BOTH old and new contracts
    pactUrls: [
      './pacts/LegacyLoginPage-LegacyAuthService.json',
      './pacts/UnifiedLoginPage-AuthenticationService.json'
    ],
    
    // Ensure old behaviors still work
    stateHandlers: {
      'legacy login endpoint exists': async () => {
        // No changes to legacy endpoints
      },
      'new identity features enabled': async () => {
        // New features are additive only
        await enableFeatureFlag('identity_system');
      }
    }
  });
  
  it('should verify all contracts without breaking existing ones', async () => {
    await pactVerifier.verifyProvider();
  });
});
```

## Test Data Scenarios

### Scenario 1: Single Parent, Multiple Children
```json
{
  "parent": {
    "id": "parent-123",
    "email": "<EMAIL>",
    "children": [
      {
        "name": "Alice Smith",
        "username": "alice_smith",
        "age": 14,
        "teams": ["U16 Eagles"]
      },
      {
        "name": "Bob Smith",
        "username": "bob_smith",
        "age": 12,
        "teams": ["U14 Eagles", "U14 Select"]
      }
    ]
  }
}
```

### Scenario 2: Feature Flag Migration
```json
{
  "featureFlags": {
    "identity_dashboard": {
      "enabled": true,
      "rolloutPercentage": 25,
      "enabledForTeams": ["team-pilot-1", "team-pilot-2"]
    },
    "unified_login": {
      "enabled": false,
      "plannedDate": "2024-04-01"
    }
  }
}
```

### Scenario 3: Complex Family Structure
```json
{
  "family": {
    "parents": [
      { "id": "parent-1", "name": "John Doe" },
      { "id": "parent-2", "name": "Jane Doe" }
    ],
    "children": [
      {
        "id": "child-1",
        "name": "Jimmy Doe",
        "managedBy": ["parent-1", "parent-2"],
        "restrictions": {
          "payment": "requires_approval",
          "personal_info": "requires_approval"
        }
      }
    ]
  }
}
```

## Backward Compatibility Testing

### 1. Parallel System Tests
```typescript
describe('Parallel System Operation', () => {
  it('should allow both old and new login pages to work', async () => {
    // Old login page
    const oldLogin = await request(app)
      .post('/auth/signin')
      .send({ email: '<EMAIL>', password: 'password' });
    expect(oldLogin.status).toBe(200);
    
    // New login page
    const newLogin = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password', loginType: 'email' });
    expect(newLogin.status).toBe(200);
    
    // Both should authenticate the same user
    expect(oldLogin.body.user.id).toBe(newLogin.body.user.id);
  });
});
```

### 2. Database Compatibility Tests
```sql
-- Verify no changes to existing tables
SELECT COUNT(*) as unchanged_tables
FROM information_schema.columns
WHERE table_name IN ('profiles', 'auth.users')
AND column_name NOT LIKE 'identity_%';

-- Verify new tables use identity_ prefix
SELECT table_name
FROM information_schema.tables
WHERE table_name LIKE 'identity_%';
```

## Monitoring & Rollback Strategy

### Contract Version Matrix
```yaml
# Track which consumers work with which provider versions
compatibility_matrix:
  providers:
    AuthenticationService:
      v1.0: [LegacyLoginPage-v1.0]
      v2.0: [LegacyLoginPage-v1.0, UnifiedLoginPage-v1.0]
    
    ProfileService:
      v1.0: [LegacyProfilePage-v1.0]
      v1.1: [LegacyProfilePage-v1.0, IdentityDashboard-v1.0]
```

### Rollback Procedure
```typescript
// Quick rollback using feature flags
const rollbackIdentityFeatures = async () => {
  await disableFeatureFlag('identity_dashboard');
  await disableFeatureFlag('family_management');
  await disableFeatureFlag('unified_login');
  
  // Verify legacy systems still working
  await runLegacySystemHealthChecks();
};
```

## Performance Benchmarks

```typescript
// Contract includes performance expectations
{
  description: "Account switching performance",
  request: {
    method: "POST",
    path: "/api/identity/family/switch-account"
  },
  response: {
    status: 200,
    headers: {
      "X-Response-Time": Matchers.integer({ max: 1000 }) // Must be under 1 second
    }
  }
}
```

## CI/CD Integration

### Frontend Pipeline with Compatibility Checks
```yaml
name: Identity Frontend Tests
on: [push, pull_request]

jobs:
  compatibility-tests:
    steps:
      - name: Run legacy component tests
        run: npm run test:legacy
        
      - name: Run new identity tests
        run: npm run test:identity
        
      - name: Verify code reuse metrics
        run: |
          npm run analyze:code-reuse
          # Fail if code reuse < 80%
          
      - name: Run PACT consumer tests
        run: npm run test:pact
        
      - name: Verify zero breaking changes
        run: npm run test:breaking-changes
```

## Getting Started

```bash
# Install dependencies
npm install --save-dev @pact-foundation/pact

# Start stub server with identity contracts
npm run pact:stub-server:identity

# Run consumer tests
npm run test:pact:identity

# Verify backward compatibility
npm run test:compatibility

# Check code reuse percentage
npm run analyze:code-reuse
```

## Support & Resources

- **Identity System Docs**: `/docs/identity/`
- **Migration Guide**: `/docs/identity/migration-guide.md`
- **Feature Flag Config**: `/config/feature-flags.json`
- **Team Contact**: #identity-system-dev on Slack

This PACT testing strategy ensures the Identity system successfully adds new features while maintaining 100% backward compatibility and achieving 80%+ code reuse through careful wrapper-based development.