# PACT Testing Strategy - EPIC 4: Schedule System

## Overview

This document outlines the Consumer-Driven Contract Testing (PACT) strategy for the Schedule system. The Schedule system manages event creation, calendar views, RSVP workflows, attendance tracking, and family coordination. PACT tests ensure reliable integration between frontend applications and backend services throughout development.

## Key Principles

1. **Multi-Consumer Support**: Handle coach, player, parent, and admin interfaces
2. **Real-time Updates**: Support live RSVP and attendance changes
3. **Offline-First**: Enable offline viewing with sync capabilities
4. **Family Complexity**: Handle multi-child scenarios and bulk operations
5. **Performance at Scale**: Support teams with 100+ events per month

## Service Architecture

### Consumers (Frontend)
- **Coach Event Portal**: Event management and attendance tracking
- **Player Calendar**: Personal event view and RSVP submission
- **Family Dashboard**: Multi-child calendar and bulk operations
- **Mobile PWA**: Offline-capable mobile experience
- **Calendar Sync**: Native calendar integration endpoints

### Providers (Backend Services)
- **EventService**: Event lifecycle management
- **CalendarService**: View generation and filtering
- **RSVPService**: Response handling and waitlist management
- **AttendanceService**: Check-in and reporting
- **NotificationService**: Event reminders and updates
- **ExportService**: Calendar feeds and data exports

## PACT Test Structure

### 1. Event Management Contracts

#### Create Event Contract
```typescript
// Consumer: CoachEventPortal
// Provider: EventService
{
  consumer: "CoachEventPortal",
  provider: "EventService",
  interactions: [
    {
      description: "Create a training event with RSVP required",
      providerStates: [{
        name: "coach has active team",
        params: { coachId: "coach-123", teamId: "team-456" }
      }],
      request: {
        method: "POST",
        path: "/api/schedule/events",
        headers: {
          "Authorization": "Bearer ${coachToken}",
          "Content-Type": "application/json"
        },
        body: {
          teamId: "team-456",
          type: "training",
          title: "Weekly Training Session",
          date: "2024-03-25",
          startTime: "18:00",
          endTime: "19:30",
          location: {
            name: "Main Training Ground",
            address: "123 Sports Complex",
            coordinates: {
              lat: 51.5074,
              lng: -0.1278
            }
          },
          details: {
            description: "Focus on passing drills and fitness",
            equipment: ["balls", "cones", "bibs"],
            kit: "training_kit"
          },
          rsvpSettings: {
            required: true,
            deadline: "2024-03-24T12:00:00Z",
            maxParticipants: 20,
            enableWaitlist: true,
            requireParentApproval: true
          },
          notifications: {
            sendOnCreate: true,
            reminderDays: [1, 3]
          },
          visibility: "team_only"
        }
      },
      response: {
        status: 201,
        headers: {
          "Content-Type": "application/json",
          "Location": Matchers.regex(/^\/api\/schedule\/events\/[\w-]+$/)
        },
        body: {
          id: Matchers.uuid(),
          teamId: "team-456",
          coachId: "coach-123",
          status: "scheduled",
          rsvpStats: {
            invited: Matchers.integer(),
            responded: 0,
            attending: 0,
            notAttending: 0,
            maybe: 0,
            waitlist: 0
          },
          conflictInfo: {
            hasConflicts: Matchers.boolean(),
            conflictingEvents: Matchers.eachLike({
              id: Matchers.uuid(),
              title: Matchers.string(),
              type: Matchers.string()
            })
          },
          createdAt: Matchers.iso8601DateTime(),
          notificationStatus: {
            sent: Matchers.integer(),
            failed: 0,
            pending: 0
          }
        }
      }
    }
  ]
}
```

#### Create Event from Template Contract
```typescript
{
  description: "Create event using saved template",
  request: {
    method: "POST",
    path: "/api/schedule/events/from-template",
    headers: {
      "Authorization": "Bearer ${coachToken}",
      "Content-Type": "application/json"
    },
    body: {
      templateId: "template-789",
      date: "2024-03-25",
      overrides: {
        startTime: "19:00", // Override template time
        maxParticipants: 25
      }
    }
  },
  response: {
    status: 201,
    body: {
      id: Matchers.uuid(),
      basedOnTemplate: "template-789",
      // All template fields applied with overrides
      startTime: "19:00",
      maxParticipants: 25,
      templateFields: {
        title: Matchers.string(),
        type: Matchers.string(),
        duration: Matchers.integer()
      }
    }
  }
}
```

#### Bulk Event Creation Contract
```typescript
{
  description: "Create multiple events in one request",
  request: {
    method: "POST",
    path: "/api/schedule/events/bulk",
    headers: {
      "Authorization": "Bearer ${coachToken}",
      "Content-Type": "application/json"
    },
    body: {
      baseEvent: {
        teamId: "team-456",
        type: "training",
        title: "Weekly Training",
        duration: 90,
        location: { name: "Training Ground" }
      },
      dates: [
        "2024-03-25",
        "2024-04-01",
        "2024-04-08",
        "2024-04-15"
      ],
      startTime: "18:00",
      skipConflicts: true
    }
  },
  response: {
    status: 200,
    body: {
      created: Matchers.eachLike({
        id: Matchers.uuid(),
        date: Matchers.date("yyyy-MM-dd"),
        status: "scheduled"
      }, { min: 1 }),
      skipped: Matchers.eachLike({
        date: Matchers.date("yyyy-MM-dd"),
        reason: Matchers.string(),
        conflictingEvent: {
          id: Matchers.uuid(),
          title: Matchers.string()
        }
      }),
      summary: {
        totalRequested: 4,
        created: Matchers.integer({ max: 4 }),
        skipped: Matchers.integer({ max: 4 })
      }
    }
  }
}
```

### 2. Calendar View Contracts

#### Get Month View Contract
```typescript
// Consumer: PlayerCalendar
// Provider: CalendarService
{
  consumer: "PlayerCalendar",
  provider: "CalendarService",
  interactions: [
    {
      description: "Get calendar month view with player's events",
      providerStates: [{
        name: "player has events in March 2024",
        params: { playerId: "player-789", month: "2024-03" }
      }],
      request: {
        method: "GET",
        path: "/api/schedule/calendar/month",
        query: {
          year: "2024",
          month: "3",
          timezone: "Europe/London"
        },
        headers: {
          "Authorization": "Bearer ${playerToken}"
        }
      },
      response: {
        status: 200,
        headers: {
          "Content-Type": "application/json",
          "X-Total-Events": Matchers.integer()
        },
        body: {
          month: {
            year: 2024,
            month: 3,
            startDate: "2024-03-01",
            endDate: "2024-03-31",
            weeks: 5
          },
          events: Matchers.eachLike({
            id: Matchers.uuid(),
            date: Matchers.date("yyyy-MM-dd"),
            startTime: Matchers.time("HH:mm"),
            endTime: Matchers.time("HH:mm"),
            type: Matchers.term("training", "training|match|tournament|social|other"),
            title: Matchers.string(),
            team: {
              id: Matchers.uuid(),
              name: Matchers.string(),
              color: Matchers.hexColor()
            },
            location: {
              name: Matchers.string(),
              isHome: Matchers.boolean()
            },
            rsvp: {
              required: Matchers.boolean(),
              status: Matchers.term("pending", "pending|yes|no|maybe"),
              deadline: Matchers.iso8601DateTime(),
              deadlinePassed: Matchers.boolean()
            },
            flags: {
              isCancelled: false,
              hasConflict: Matchers.boolean(),
              requiresParentApproval: Matchers.boolean()
            }
          }),
          summary: {
            totalEvents: Matchers.integer(),
            byType: {
              training: Matchers.integer(),
              match: Matchers.integer(),
              tournament: Matchers.integer(),
              social: Matchers.integer(),
              other: Matchers.integer()
            },
            pendingRsvps: Matchers.integer()
          }
        }
      }
    }
  ]
}
```

#### Get Week View with Filters Contract
```typescript
{
  description: "Get week view filtered by event type",
  request: {
    method: "GET",
    path: "/api/schedule/calendar/week",
    query: {
      startDate: "2024-03-25",
      filters: "type:training,match;location:home",
      timezone: "Europe/London"
    },
    headers: {
      "Authorization": "Bearer ${playerToken}"
    }
  },
  response: {
    status: 200,
    body: {
      week: {
        startDate: "2024-03-25",
        endDate: "2024-03-31",
        weekNumber: 13
      },
      days: Matchers.eachLike({
        date: Matchers.date("yyyy-MM-dd"),
        dayOfWeek: Matchers.integer({ min: 1, max: 7 }),
        isToday: Matchers.boolean(),
        events: Matchers.eachLike({
          id: Matchers.uuid(),
          startTime: Matchers.time("HH:mm"),
          endTime: Matchers.time("HH:mm"),
          title: Matchers.string(),
          type: Matchers.regex(/^(training|match)$/),
          overlaps: Matchers.eachLike(Matchers.uuid()) // IDs of overlapping events
        })
      }, { min: 7, max: 7 }),
      appliedFilters: {
        type: ["training", "match"],
        location: ["home"]
      }
    }
  }
}
```

### 3. RSVP Management Contracts

#### Submit RSVP Contract
```typescript
// Consumer: PlayerCalendar
// Provider: RSVPService
{
  consumer: "PlayerCalendar",
  provider: "RSVPService",
  interactions: [
    {
      description: "Player submits RSVP for training event",
      providerStates: [{
        name: "event requires RSVP",
        params: { 
          eventId: "event-123",
          playerId: "player-789",
          requiresParentApproval: false
        }
      }],
      request: {
        method: "POST",
        path: "/api/schedule/events/${eventId}/rsvp",
        headers: {
          "Authorization": "Bearer ${playerToken}",
          "Content-Type": "application/json"
        },
        body: {
          response: "yes",
          note: "Looking forward to it!",
          transportMethod: "parent_driving"
        }
      },
      response: {
        status: 200,
        body: {
          rsvpId: Matchers.uuid(),
          eventId: "event-123",
          playerId: "player-789",
          response: "yes",
          status: "confirmed",
          position: null, // Not on waitlist
          confirmationMessage: "You're confirmed for training on March 25th",
          transportInfo: {
            method: "parent_driving",
            needsRide: false
          },
          updatedAt: Matchers.iso8601DateTime()
        }
      }
    }
  ]
}
```

#### Submit RSVP with Parent Approval Required Contract
```typescript
{
  description: "Youth player RSVP requires parent approval",
  providerStates: [{
    name: "youth team requires parent approval",
    params: { 
      eventId: "event-456",
      playerId: "youth-player-123",
      parentId: "parent-456"
    }
  }],
  request: {
    method: "POST",
    path: "/api/schedule/events/${eventId}/rsvp",
    headers: {
      "Authorization": "Bearer ${youthPlayerToken}",
      "Content-Type": "application/json"
    },
    body: {
      response: "yes",
      note: "Want to attend"
    }
  },
  response: {
    status: 200,
    body: {
      rsvpId: Matchers.uuid(),
      response: "yes",
      status: "pending_approval",
      approvalRequired: {
        needed: true,
        approver: {
          id: "parent-456",
          name: Matchers.string(),
          notified: true
        },
        deadline: Matchers.iso8601DateTime()
      },
      confirmationMessage: "Your RSVP is pending parent approval"
    }
  }
}
```

#### Waitlist Management Contract
```typescript
{
  description: "Player joins waitlist when event is full",
  providerStates: [{
    name: "event is at capacity",
    params: { 
      eventId: "event-789",
      maxParticipants: 20,
      currentParticipants: 20,
      waitlistEnabled: true
    }
  }],
  request: {
    method: "POST",
    path: "/api/schedule/events/${eventId}/rsvp",
    headers: {
      "Authorization": "Bearer ${playerToken}",
      "Content-Type": "application/json"
    },
    body: {
      response: "yes",
      acceptWaitlist: true
    }
  },
  response: {
    status: 200,
    body: {
      rsvpId: Matchers.uuid(),
      response: "yes",
      status: "waitlisted",
      position: Matchers.integer({ min: 1 }),
      waitlistInfo: {
        position: Matchers.integer({ min: 1 }),
        totalWaitlisted: Matchers.integer({ min: 1 }),
        estimatedChance: Matchers.term("high", "high|medium|low"),
        autoPromote: true
      },
      confirmationMessage: "You're #${position} on the waitlist"
    }
  }
}
```

### 4. Family Dashboard Contracts

#### Get Family Calendar Contract
```typescript
// Consumer: FamilyDashboard
// Provider: CalendarService
{
  consumer: "FamilyDashboard",
  provider: "CalendarService",
  interactions: [
    {
      description: "Get combined calendar for all children",
      providerStates: [{
        name: "parent has multiple children with events",
        params: { 
          parentId: "parent-123",
          childIds: ["child-1", "child-2", "child-3"]
        }
      }],
      request: {
        method: "GET",
        path: "/api/schedule/family/calendar",
        query: {
          view: "month",
          year: "2024",
          month: "3"
        },
        headers: {
          "Authorization": "Bearer ${parentToken}"
        }
      },
      response: {
        status: 200,
        body: {
          children: Matchers.eachLike({
            id: Matchers.uuid(),
            name: Matchers.string(),
            color: Matchers.hexColor(),
            eventCount: Matchers.integer()
          }, { min: 3, max: 3 }),
          events: Matchers.eachLike({
            id: Matchers.uuid(),
            childId: Matchers.uuid(),
            childName: Matchers.string(),
            date: Matchers.date("yyyy-MM-dd"),
            time: Matchers.time("HH:mm"),
            type: Matchers.string(),
            title: Matchers.string(),
            team: Matchers.string(),
            rsvp: {
              required: Matchers.boolean(),
              status: Matchers.string(),
              needsApproval: Matchers.boolean()
            }
          }),
          conflicts: Matchers.eachLike({
            date: Matchers.date("yyyy-MM-dd"),
            events: Matchers.eachLike({
              childName: Matchers.string(),
              eventTitle: Matchers.string(),
              time: Matchers.time("HH:mm")
            }, { min: 2 }),
            transportIssue: Matchers.boolean(),
            suggestedSolution: Matchers.string()
          }),
          summary: {
            totalEvents: Matchers.integer(),
            pendingRsvps: Matchers.integer(),
            conflicts: Matchers.integer(),
            needsApproval: Matchers.integer()
          }
        }
      }
    }
  ]
}
```

#### Bulk RSVP for Family Contract
```typescript
{
  description: "Submit RSVPs for multiple children at once",
  request: {
    method: "POST",
    path: "/api/schedule/rsvp/bulk",
    headers: {
      "Authorization": "Bearer ${parentToken}",
      "Content-Type": "application/json"
    },
    body: {
      rsvps: [
        {
          eventId: "event-123",
          childId: "child-1",
          response: "yes"
        },
        {
          eventId: "event-456",
          childId: "child-2",
          response: "no",
          note: "Has another commitment"
        },
        {
          eventId: "event-789",
          childId: "child-3",
          response: "yes"
        }
      ],
      handleConflicts: "prompt" // or "skip" or "override"
    }
  },
  response: {
    status: 200,
    body: {
      processed: Matchers.eachLike({
        eventId: Matchers.uuid(),
        childId: Matchers.uuid(),
        success: true,
        rsvpId: Matchers.uuid(),
        status: Matchers.string()
      }, { min: 3 }),
      conflicts: Matchers.eachLike({
        childId: Matchers.uuid(),
        reason: Matchers.string(),
        suggestion: Matchers.string()
      }),
      summary: {
        total: 3,
        successful: Matchers.integer({ max: 3 }),
        failed: Matchers.integer({ max: 3 })
      }
    }
  }
}
```

### 5. Attendance Tracking Contracts

#### Get Attendance List Contract
```typescript
// Consumer: CoachEventPortal
// Provider: AttendanceService
{
  consumer: "CoachEventPortal",
  provider: "AttendanceService",
  interactions: [
    {
      description: "Get attendance list for event check-in",
      providerStates: [{
        name: "event has RSVPs",
        params: { 
          eventId: "event-123",
          expectedAttendees: 18
        }
      }],
      request: {
        method: "GET",
        path: "/api/schedule/events/${eventId}/attendance",
        headers: {
          "Authorization": "Bearer ${coachToken}"
        }
      },
      response: {
        status: 200,
        body: {
          event: {
            id: "event-123",
            title: Matchers.string(),
            date: Matchers.date("yyyy-MM-dd"),
            startTime: Matchers.time("HH:mm")
          },
          attendanceList: Matchers.eachLike({
            playerId: Matchers.uuid(),
            playerName: Matchers.string(),
            jerseyNumber: Matchers.string(),
            rsvpStatus: Matchers.term("yes", "yes|no|maybe|no_response"),
            attendanceStatus: Matchers.term("not_marked", "present|absent|late|not_marked"),
            arrivalTime: Matchers.somethingLike(null),
            departureTime: Matchers.somethingLike(null),
            note: Matchers.somethingLike(""),
            parentContact: {
              name: Matchers.string(),
              phone: Matchers.string()
            }
          }, { min: 1 }),
          summary: {
            expected: 18,
            present: 0,
            absent: 0,
            late: 0,
            notMarked: 18
          },
          lastUpdated: Matchers.iso8601DateTime()
        }
      }
    }
  ]
}
```

#### Mark Attendance Contract
```typescript
{
  description: "Mark player attendance with arrival time",
  request: {
    method: "POST",
    path: "/api/schedule/events/${eventId}/attendance",
    headers: {
      "Authorization": "Bearer ${coachToken}",
      "Content-Type": "application/json"
    },
    body: {
      updates: [
        {
          playerId: "player-1",
          status: "present",
          arrivalTime: "17:55"
        },
        {
          playerId: "player-2",
          status: "late",
          arrivalTime: "18:15",
          note: "Traffic delay"
        },
        {
          playerId: "player-3",
          status: "absent",
          reason: "sick",
          note: "Flu symptoms"
        }
      ]
    }
  },
  response: {
    status: 200,
    body: {
      updated: 3,
      summary: {
        present: Matchers.integer(),
        late: Matchers.integer(),
        absent: Matchers.integer(),
        notMarked: Matchers.integer(),
        attendanceRate: Matchers.decimal(0.0, 1.0)
      },
      notifications: {
        sent: Matchers.integer(), // Absence notifications to parents
        failed: 0
      }
    }
  }
}
```

### 6. Calendar Export & Sync Contracts

#### Generate Calendar Feed Contract
```typescript
// Consumer: CalendarSync
// Provider: ExportService
{
  consumer: "CalendarSync",
  provider: "ExportService",
  interactions: [
    {
      description: "Generate personal calendar subscription feed",
      request: {
        method: "POST",
        path: "/api/schedule/calendar/feed",
        headers: {
          "Authorization": "Bearer ${playerToken}",
          "Content-Type": "application/json"
        },
        body: {
          name: "SHOT Calendar - Johnny",
          includeTypes: ["training", "match", "tournament"],
          includeTeams: ["team-456"],
          options: {
            includeCancelled: false,
            includeLocation: true,
            includeDescription: true,
            reminderMinutes: 60
          }
        }
      },
      response: {
        status: 201,
        body: {
          feedId: Matchers.uuid(),
          feedUrl: Matchers.regex(/^https:\/\/.*\/api\/calendar\/feed\/[\w-]+$/),
          token: Matchers.regex(/^[A-Za-z0-9_-]{32}$/),
          webcalUrl: Matchers.regex(/^webcal:\/\/.*\/api\/calendar\/feed\/[\w-]+\?token=[\w-]+$/),
          instructions: {
            ios: Matchers.string(),
            android: Matchers.string(),
            outlook: Matchers.string(),
            google: Matchers.string()
          },
          expiresAt: Matchers.somethingLike(null) // Optional expiry
        }
      }
    }
  ]
}
```

#### Export Events Contract
```typescript
{
  description: "Export events to CSV format",
  request: {
    method: "POST",
    path: "/api/schedule/events/export",
    headers: {
      "Authorization": "Bearer ${coachToken}",
      "Content-Type": "application/json"
    },
    body: {
      format: "csv",
      dateRange: {
        from: "2024-01-01",
        to: "2024-12-31"
      },
      filters: {
        teamId: "team-456",
        types: ["training", "match"]
      },
      columns: [
        "date",
        "time",
        "type",
        "title",
        "location",
        "attendance_rate",
        "notes"
      ]
    }
  },
  response: {
    status: 202,
    headers: {
      "Location": "/api/schedule/exports/${jobId}"
    },
    body: {
      jobId: Matchers.uuid(),
      status: "processing",
      estimatedRows: Matchers.integer(),
      format: "csv",
      downloadUrl: Matchers.somethingLike(null), // Available when complete
      expiresAt: Matchers.iso8601DateTime()
    }
  }
}
```

## Frontend Development Process

### 1. Offline-First Calendar Implementation

```typescript
// Example: Offline-capable calendar with sync
import { useOfflineCalendar } from '@/hooks/useOfflineCalendar';
import { useSyncQueue } from '@/hooks/useSyncQueue';

export const CalendarView: React.FC = () => {
  const { events, isOffline, lastSync } = useOfflineCalendar();
  const { pendingChanges, sync } = useSyncQueue();
  
  // Work against local cache first
  const handleRSVP = async (eventId: string, response: RSVPResponse) => {
    // Optimistic update
    updateLocalEvent(eventId, { rsvp: response });
    
    // Queue for sync
    await queueRSVPChange(eventId, response);
    
    // Sync if online
    if (!isOffline) {
      await sync();
    }
  };
  
  return (
    <Calendar
      events={events}
      onRSVP={handleRSVP}
      offlineIndicator={isOffline}
      pendingChanges={pendingChanges.length}
    />
  );
};
```

### 2. Family Dashboard Complexity

```typescript
// Example: Multi-child conflict detection
class FamilyCalendarService {
  async detectConflicts(events: FamilyEvent[]): Promise<ConflictInfo[]> {
    const conflicts = [];
    
    // Group events by date/time
    const eventsByTime = groupBy(events, e => `${e.date}-${e.time}`);
    
    for (const [timeSlot, overlappingEvents] of Object.entries(eventsByTime)) {
      if (overlappingEvents.length > 1) {
        // Calculate transport time between locations
        const transportIssue = await this.checkTransportFeasibility(
          overlappingEvents.map(e => e.location)
        );
        
        conflicts.push({
          date: overlappingEvents[0].date,
          events: overlappingEvents,
          transportIssue,
          suggestedSolution: this.generateSolution(overlappingEvents, transportIssue)
        });
      }
    }
    
    return conflicts;
  }
  
  private generateSolution(events: FamilyEvent[], hasTransportIssue: boolean): string {
    if (hasTransportIssue) {
      return "Consider carpooling with another family or updating one RSVP";
    }
    if (events.every(e => e.type === 'training')) {
      return "Contact coaches to see if one child can attend earlier/later";
    }
    return "Prioritize based on event importance";
  }
}
```

### 3. Real-time RSVP Updates

```typescript
// Contract test for real-time updates
describe('RSVP Real-time Updates', () => {
  it('should receive RSVP updates via WebSocket', async () => {
    const mockWS = new MockWebSocket();
    
    // Subscribe to event RSVP updates
    mockWS.send({
      action: 'subscribe',
      channel: 'event-rsvp',
      eventId: 'event-123'
    });
    
    // Expect real-time update when another player RSVPs
    const update = await mockWS.waitForMessage();
    
    expect(update).toMatchObject({
      type: 'rsvp-update',
      data: {
        eventId: 'event-123',
        playerId: expect.any(String),
        response: expect.stringMatching(/yes|no|maybe/),
        updatedStats: {
          attending: expect.any(Number),
          notAttending: expect.any(Number),
          maybe: expect.any(Number),
          waitlist: expect.any(Number)
        }
      }
    });
  });
});
```

### 4. Performance Testing for Large Teams

```typescript
describe('Calendar Performance', () => {
  it('should handle 100+ events efficiently', async () => {
    const startTime = performance.now();
    
    const calendar = await calendarService.getMonthView({
      year: 2024,
      month: 3,
      // Team with heavy schedule
      teamId: 'busy-team-123'
    });
    
    const loadTime = performance.now() - startTime;
    
    // Performance assertions
    expect(loadTime).toBeLessThan(2000); // 2 second max
    expect(calendar.events.length).toBeGreaterThan(100);
    
    // Verify virtualization is working
    const rendered = countRenderedElements();
    expect(rendered).toBeLessThan(50); // Only visible events rendered
  });
  
  it('should efficiently filter large event lists', async () => {
    const events = generateMockEvents(500);
    const startTime = performance.now();
    
    const filtered = filterEvents(events, {
      type: ['training', 'match'],
      dateRange: { from: '2024-03-01', to: '2024-03-31' }
    });
    
    const filterTime = performance.now() - startTime;
    
    expect(filterTime).toBeLessThan(50); // 50ms max for filtering
    expect(filtered.length).toBeLessThan(events.length);
  });
});
```

## Test Data Scenarios

### Scenario 1: Youth Team with Parent Approvals
```json
{
  "team": {
    "id": "youth-team-u12",
    "ageGroup": "U12",
    "requiresParentApproval": true,
    "players": 22,
    "events": {
      "weekly": {
        "training": 2,
        "matches": 1
      }
    }
  },
  "rsvpPatterns": {
    "parentResponseTime": "1-24 hours",
    "approvalRate": 0.95,
    "commonDeclineReasons": ["illness", "family_commitment", "academic"]
  }
}
```

### Scenario 2: Multi-Team Family
```json
{
  "family": {
    "parentId": "parent-busy",
    "children": [
      {
        "name": "Alice",
        "teams": ["U14 Eagles", "Regional Squad"],
        "eventsPerWeek": 5
      },
      {
        "name": "Bob",
        "teams": ["U12 Eagles"],
        "eventsPerWeek": 3
      },
      {
        "name": "Charlie",
        "teams": ["U10 Eagles", "Swimming Club"],
        "eventsPerWeek": 4
      }
    ],
    "weeklyConflicts": 2,
    "transportChallenges": true
  }
}
```

### Scenario 3: Tournament Weekend
```json
{
  "tournament": {
    "id": "spring-tournament",
    "dates": ["2024-03-23", "2024-03-24"],
    "teams": 8,
    "matchesPerTeam": 5,
    "logistics": {
      "venues": 2,
      "fields": 4,
      "simultaneousMatches": true
    },
    "rsvpComplexity": {
      "fullWeekendCommitment": true,
      "accommodationOptions": true,
      "mealArrangements": true
    }
  }
}
```

## Mobile PWA Testing

```typescript
// PWA-specific contract tests
describe('PWA Offline Capabilities', () => {
  it('should cache calendar data for offline viewing', async () => {
    // Prime cache while online
    await calendarService.getMonthView({ year: 2024, month: 3 });
    
    // Go offline
    await simulateOffline();
    
    // Should still work from cache
    const offlineCalendar = await calendarService.getMonthView({ 
      year: 2024, 
      month: 3 
    });
    
    expect(offlineCalendar.events).toHaveLength(greaterThan(0));
    expect(offlineCalendar.fromCache).toBe(true);
  });
  
  it('should queue RSVP changes when offline', async () => {
    await simulateOffline();
    
    // Submit RSVP while offline
    const result = await rsvpService.submitRSVP('event-123', {
      response: 'yes',
      note: 'Submitted offline'
    });
    
    expect(result.queued).toBe(true);
    expect(result.syncPending).toBe(true);
    
    // Go back online
    await simulateOnline();
    
    // Auto-sync should process queued changes
    await waitFor(() => {
      expect(getSyncQueue()).toHaveLength(0);
    });
  });
});
```

## Calendar Integration Testing

```typescript
// Native calendar integration contracts
describe('Calendar Sync Integration', () => {
  it('should generate valid iCal feed', async () => {
    const feed = await exportService.generateCalendarFeed({
      name: 'SHOT Calendar',
      includeTypes: ['training', 'match']
    });
    
    // Validate iCal format
    const icalData = await fetch(feed.feedUrl).then(r => r.text());
    
    expect(icalData).toMatch(/BEGIN:VCALENDAR/);
    expect(icalData).toMatch(/VERSION:2.0/);
    expect(icalData).toMatch(/BEGIN:VEVENT/);
    expect(icalData).toMatch(/DTSTART:/);
    expect(icalData).toMatch(/SUMMARY:/);
  });
  
  it('should handle calendar app subscriptions', async () => {
    const subscription = await calendarService.createSubscription({
      platform: 'ios',
      includeReminders: true
    });
    
    expect(subscription.webcalUrl).toMatch(/^webcal:\/\//);
    expect(subscription.instructions.ios).toContain('Settings > Calendar > Accounts');
  });
});
```

## Performance Monitoring

```yaml
# Performance metrics in contracts
performance_slas:
  calendar_views:
    month_view: 2000ms
    week_view: 1000ms
    day_view: 500ms
  
  rsvp_operations:
    single_rsvp: 500ms
    bulk_rsvp: 2000ms
    
  export_operations:
    ical_generation: 1000ms
    csv_export_100_events: 3000ms
    
  mobile_targets:
    initial_load: 3000ms
    subsequent_loads: 1000ms
    offline_cache_access: 100ms
```

## CI/CD Integration

```yaml
name: Schedule System Tests
on: [push, pull_request]

jobs:
  contract-tests:
    steps:
      - name: Run PACT consumer tests
        run: npm run test:pact:schedule
        
      - name: Test offline scenarios
        run: npm run test:offline:schedule
        
      - name: Test family scenarios
        run: npm run test:family:complex
        
      - name: Performance benchmarks
        run: npm run test:performance:calendar
        
      - name: Mobile PWA tests
        run: npm run test:pwa:schedule
```

## Getting Started

```bash
# Install dependencies
npm install --save-dev @pact-foundation/pact

# Start stub server with schedule contracts
npm run pact:stub-server:schedule

# Run consumer tests
npm run test:pact:schedule

# Test offline scenarios
npm run test:offline:schedule

# Run family complexity tests
npm run test:family:scenarios
```

## Support & Resources

- **Schedule System Docs**: `/docs/schedule/`
- **Event Types Guide**: `/docs/schedule/event-types.md`
- **RSVP Flow Diagrams**: `/docs/schedule/rsvp-flows.md`
- **Team Contact**: #schedule-system-dev on Slack

This PACT testing strategy ensures the Schedule system provides reliable event management, seamless RSVP workflows, and excellent family coordination while maintaining performance at scale and supporting offline mobile usage.