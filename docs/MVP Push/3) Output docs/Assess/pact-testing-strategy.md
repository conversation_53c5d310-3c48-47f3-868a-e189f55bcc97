# PACT Testing Strategy - EPIC 1: Assess System

## Overview

This document outlines the Consumer-Driven Contract Testing (PACT) strategy for the Assess system. PACT tests ensure that the frontend (consumer) and backend services (providers) maintain consistent API contracts throughout development. This enables frontend developers to work against test stubs with confidence that their implementations will integrate seamlessly with the real backend.

## Key Principles

1. **Contract-First Development**: Define API contracts before implementation
2. **Frontend Independence**: Frontend developers can build against verified stubs
3. **Backward Compatibility**: Ensure changes don't break existing consumers
4. **Continuous Verification**: Automated contract validation in CI/CD pipeline

## Service Architecture

### Consumers (Frontend)
- **Coach Web App**: React-based coach interface
- **Player Mobile Web**: Token-based evaluation forms
- **Parent Share View**: Read-only evaluation results

### Providers (Backend Services)
- **AssessEventService**: Event lifecycle management
- **EvaluationService**: Evaluation CRUD operations
- **TokenService**: Security token validation
- **SMSService**: Notification delivery
- **ImportService**: CSV data import

## PACT Test Structure

### 1. Coach Event Management Contracts

#### Create Event Contract
```typescript
// Consumer: CoachApp
// Provider: AssessEventService
{
  consumer: "CoachApp",
  provider: "AssessEventService",
  interactions: [
    {
      description: "Create a new training event",
      request: {
        method: "POST",
        path: "/api/assess/events",
        headers: {
          "Authorization": "Bearer ${coachToken}",
          "Content-Type": "application/json"
        },
        body: {
          teamId: "team-123",
          type: "training",
          date: "2024-03-15",
          time: "18:00",
          location: "Main Field",
          selectedPlayerIds: ["player-1", "player-2", "player-3"]
        }
      },
      response: {
        status: 201,
        headers: {
          "Content-Type": "application/json"
        },
        body: {
          id: Matchers.uuid(),
          teamId: "team-123",
          coachId: Matchers.uuid(),
          status: "draft",
          createdAt: Matchers.iso8601DateTime(),
          evaluations: Matchers.eachLike({
            playerId: Matchers.uuid(),
            status: "pending"
          }, { min: 3 })
        }
      }
    }
  ]
}
```

#### Publish Event Contract
```typescript
{
  description: "Publish event and trigger SMS notifications",
  request: {
    method: "POST",
    path: "/api/assess/events/${eventId}/publish",
    headers: {
      "Authorization": "Bearer ${coachToken}"
    }
  },
  response: {
    status: 200,
    body: {
      id: Matchers.uuid(),
      status: "published",
      smsStatus: "sending",
      smsBatchId: Matchers.uuid(),
      publishedAt: Matchers.iso8601DateTime()
    }
  }
}
```

### 2. Player Evaluation Contracts

#### Token Validation Contract
```typescript
// Consumer: PlayerMobileWeb
// Provider: TokenService
{
  consumer: "PlayerMobileWeb",
  provider: "TokenService",
  interactions: [
    {
      description: "Validate pre-evaluation token",
      request: {
        method: "GET",
        path: "/api/assess/validate-token",
        query: {
          token: "eyJhbGciOiJIUzI1NiIs..."
        }
      },
      response: {
        status: 200,
        body: {
          valid: true,
          type: "pre-evaluation",
          eventId: Matchers.uuid(),
          playerId: Matchers.uuid(),
          playerName: Matchers.string(),
          eventDetails: {
            type: Matchers.term("training", "training|match"),
            date: Matchers.date("yyyy-MM-dd"),
            time: Matchers.time("HH:mm"),
            location: Matchers.string()
          },
          expiresAt: Matchers.iso8601DateTime()
        }
      }
    }
  ]
}
```

#### Submit Pre-Evaluation Contract
```typescript
{
  description: "Submit player pre-evaluation",
  request: {
    method: "POST",
    path: "/api/assess/evaluations/${eventId}/players/${playerId}/pre-evaluation",
    headers: {
      "Authorization": "Bearer ${evaluationToken}",
      "Content-Type": "application/json"
    },
    body: {
      scores: [
        {
          questionId: "shot-s-1",
          category: "skill",
          rating: 4,
          notes: "Showed improvement"
        },
        {
          questionId: "shot-h-1",
          category: "hunger",
          rating: 5
        },
        {
          questionId: "shot-o-1",
          category: "organisation",
          rating: 3
        },
        {
          questionId: "shot-t-1",
          category: "teamwork",
          rating: 4
        },
        {
          questionId: "position-gk-1",
          category: "skill",
          rating: 4,
          notes: "Good positioning"
        }
      ]
    }
  },
  response: {
    status: 200,
    body: {
      evaluationId: Matchers.uuid(),
      status: "pre_evaluation_complete",
      submittedAt: Matchers.iso8601DateTime(),
      confirmationMessage: "Thank you! Your evaluation has been submitted."
    }
  }
}
```

### 3. Coach Evaluation Contracts

#### Get Event Evaluations Contract
```typescript
// Consumer: CoachApp
// Provider: EvaluationService
{
  consumer: "CoachApp",
  provider: "EvaluationService",
  interactions: [
    {
      description: "Get all player evaluations for an event",
      request: {
        method: "GET",
        path: "/api/assess/events/${eventId}/evaluations",
        headers: {
          "Authorization": "Bearer ${coachToken}"
        }
      },
      response: {
        status: 200,
        body: {
          eventId: Matchers.uuid(),
          players: Matchers.eachLike({
            playerId: Matchers.uuid(),
            playerName: Matchers.string(),
            position: Matchers.string(),
            preEvaluationStatus: Matchers.term("complete", "pending|complete"),
            preEvaluationScores: Matchers.somethingLike({
              skill: 4,
              hunger: 5,
              organisation: 3,
              teamwork: 4
            }),
            coachEvaluationStatus: Matchers.term("pending", "pending|complete|in_progress"),
            lastSaved: Matchers.iso8601DateTime()
          }, { min: 1 })
        }
      }
    }
  ]
}
```

#### Auto-Save Coach Evaluation Contract
```typescript
{
  description: "Auto-save coach evaluation progress",
  request: {
    method: "PATCH",
    path: "/api/assess/evaluations/${eventId}/players/${playerId}/coach-evaluation",
    headers: {
      "Authorization": "Bearer ${coachToken}",
      "Content-Type": "application/json"
    },
    body: {
      scores: [
        {
          questionId: "shot-s-1",
          rating: 4,
          notes: "Excellent ball control"
        }
      ],
      isComplete: false
    }
  },
  response: {
    status: 200,
    body: {
      saved: true,
      lastSaved: Matchers.iso8601DateTime()
    }
  }
}
```

### 4. Import Service Contracts

#### Parse CSV Contract
```typescript
// Consumer: CoachApp
// Provider: ImportService
{
  consumer: "CoachApp",
  provider: "ImportService",
  interactions: [
    {
      description: "Parse players CSV file",
      request: {
        method: "POST",
        path: "/api/assess/import/parse",
        headers: {
          "Authorization": "Bearer ${coachToken}",
          "Content-Type": "multipart/form-data"
        },
        body: {
          file: Matchers.file("players.csv"),
          type: "players"
        }
      },
      response: {
        status: 200,
        body: {
          parsed: true,
          rowCount: Matchers.integer({ min: 1 }),
          columns: ["Name", "Position", "Email", "Phone"],
          preview: Matchers.eachLike({
            name: Matchers.string(),
            position: Matchers.string(),
            email: Matchers.email(),
            phone: Matchers.string()
          }, { min: 1, max: 5 }),
          validation: {
            valid: true,
            errors: [],
            warnings: Matchers.eachLike({
              row: Matchers.integer(),
              field: Matchers.string(),
              message: Matchers.string()
            })
          }
        }
      }
    }
  ]
}
```

## Frontend Development Process

### 1. Contract Definition Phase
1. **Requirements Review**: Frontend and backend teams review user stories together
2. **Contract Design**: Define API contracts in PACT format
3. **Mock Data Creation**: Create realistic test data matching business scenarios
4. **Contract Review**: Both teams approve contracts before implementation

### 2. Stub Generation
```bash
# Generate TypeScript interfaces from PACT contracts
npm run pact:generate-types

# Start PACT stub server with agreed contracts
npm run pact:stub-server

# Stub server runs on http://localhost:8080
# All API calls automatically served from contract definitions
```

### 3. Frontend Development
```typescript
// Frontend developers work against stub server
const API_BASE = process.env.REACT_APP_USE_STUBS 
  ? 'http://localhost:8080'  // PACT stub server
  : process.env.REACT_APP_API_URL;

// Example: Creating an event
const createEvent = async (eventData: CreateEventRequest): Promise<Event> => {
  const response = await fetch(`${API_BASE}/api/assess/events`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${getAuthToken()}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(eventData)
  });
  
  if (!response.ok) {
    throw new Error(`Failed to create event: ${response.statusText}`);
  }
  
  return response.json();
};
```

### 4. Contract Testing
```typescript
// Frontend PACT tests verify contract compliance
describe('Assess Event Service', () => {
  describe('Create Event', () => {
    it('should create a draft event with evaluations', async () => {
      // This test runs against PACT mock server
      const eventData = {
        teamId: 'team-123',
        type: 'training',
        date: '2024-03-15',
        time: '18:00',
        location: 'Main Field',
        selectedPlayerIds: ['player-1', 'player-2', 'player-3']
      };
      
      const event = await createEvent(eventData);
      
      expect(event).toMatchObject({
        id: expect.any(String),
        status: 'draft',
        evaluations: expect.arrayContaining([
          expect.objectContaining({
            playerId: expect.any(String),
            status: 'pending'
          })
        ])
      });
    });
  });
});
```

### 5. Backend Provider Verification
```typescript
// Backend implements provider tests
describe('AssessEventService Provider', () => {
  it('should verify the create event contract', async () => {
    const verifier = new Verifier({
      provider: 'AssessEventService',
      providerBaseUrl: 'http://localhost:3000',
      pactUrls: ['./pacts/CoachApp-AssessEventService.json'],
      
      stateHandlers: {
        'coach is authenticated': async () => {
          // Set up test data
          await seedTestCoach();
          await seedTestTeam();
        }
      }
    });
    
    await verifier.verifyProvider();
  });
});
```

## Test Data Scenarios

### Scenario 1: Small Team Training
```json
{
  "team": {
    "id": "team-small",
    "name": "U12 Eagles",
    "playerCount": 15
  },
  "event": {
    "type": "training",
    "selectedPlayers": 12,
    "preEvaluationCompletion": 10,
    "coachEvaluationTime": "8 minutes"
  }
}
```

### Scenario 2: Large Team Match
```json
{
  "team": {
    "id": "team-large",
    "name": "U16 Lions",
    "playerCount": 45
  },
  "event": {
    "type": "match",
    "selectedPlayers": 18,
    "preEvaluationCompletion": 16,
    "coachEvaluationTime": "12 minutes"
  }
}
```

### Scenario 3: Import Flow
```json
{
  "import": {
    "type": "bulk_setup",
    "files": {
      "players": { "count": 150, "duplicates": 5 },
      "coaches": { "count": 8, "conflicts": 2 },
      "events": { "count": 20, "futureOnly": true }
    }
  }
}
```

## CI/CD Integration

### Frontend Pipeline
```yaml
# .github/workflows/frontend-pact.yml
name: Frontend PACT Tests
on: [push, pull_request]

jobs:
  pact-tests:
    steps:
      - name: Run PACT consumer tests
        run: npm run test:pact
      
      - name: Publish PACT contracts
        run: npm run pact:publish
        env:
          PACT_BROKER_URL: ${{ secrets.PACT_BROKER_URL }}
```

### Backend Pipeline
```yaml
# .github/workflows/backend-pact.yml
name: Backend PACT Verification
on: [push, pull_request]

jobs:
  verify-contracts:
    steps:
      - name: Verify PACT contracts
        run: npm run pact:verify
        env:
          PACT_BROKER_URL: ${{ secrets.PACT_BROKER_URL }}
```

## Breaking Change Process

1. **Identify Breaking Change**: Recognize when a contract needs modification
2. **Version Planning**: Plan migration using versioned endpoints if needed
3. **Update Contracts**: Create new PACT files with updated contracts
4. **Parallel Implementation**: Both teams implement changes
5. **Staged Rollout**: Deploy with feature flags for gradual migration
6. **Deprecation**: Remove old contracts after all consumers updated

## Monitoring & Reporting

### Contract Coverage Metrics
- **API Coverage**: 100% of endpoints have PACT tests
- **Scenario Coverage**: All user flows represented in contracts
- **Version Compatibility**: Track consumer/provider version matrix

### PACT Broker Dashboard
- View all contracts and their verification status
- Track consumer/provider compatibility
- Alert on contract failures
- Historical contract evolution

## Benefits for Frontend Developers

1. **Independence**: Build features without waiting for backend
2. **Confidence**: Contracts guarantee API compatibility
3. **Fast Feedback**: Instant validation of API integration
4. **Documentation**: Contracts serve as living API documentation
5. **Realistic Data**: Stubs provide production-like responses
6. **Error Handling**: Test error scenarios easily with stub configuration

## Stub Server Configuration

```javascript
// pact-stub-config.js
module.exports = {
  port: 8080,
  contracts: './pacts/*.json',
  cors: true,
  delay: 100, // Simulate network latency
  
  // Dynamic responses based on request
  stateHandlers: {
    'no players selected': (request) => ({
      status: 400,
      body: { error: 'At least one player must be selected' }
    }),
    
    'SMS service unavailable': (request) => ({
      status: 503,
      body: { error: 'SMS service temporarily unavailable' }
    }),
    
    'large team': (request) => ({
      // Return 50 players for performance testing
      body: generateLargeTeamResponse(50)
    })
  }
};
```

## Getting Started

```bash
# Install PACT dependencies
npm install --save-dev @pact-foundation/pact

# Generate initial contracts from OpenAPI spec (if available)
npm run pact:generate-from-openapi

# Start stub server for development
npm run pact:stub-server

# Run consumer tests
npm run test:pact

# Verify contracts are published
npm run pact:status
```

## Support & Resources

- **PACT Documentation**: https://docs.pact.io/
- **Contract Repository**: `./pacts/` directory
- **Stub Server Logs**: `./logs/pact-stub-server.log`
- **Team Contact**: #assess-system-dev on Slack

This PACT testing strategy ensures smooth collaboration between frontend and backend teams, reduces integration issues, and accelerates development through parallel work streams.