# PACT Testing Introduction Guide - SHOT MVP

## What is PACT?

PACT is a consumer-driven contract testing tool that ensures your frontend and backend services can communicate reliably. Instead of testing the integration between services in a shared environment, PACT allows you to test the integration through "contracts" - agreed-upon expectations of how services should interact.

### Key Concepts

**Consumer**: The service that makes requests (typically frontend applications)
- Coach Portal, Player App, Parent Dashboard, etc.

**Provider**: The service that responds to requests (typically backend APIs)
- EventService, ProfileService, MetricService, etc.

**Contract**: A formal specification of the expected interaction between consumer and provider
- Request format, response format, data types, and business rules

**Stub Server**: A mock server that serves responses based on contracts during development

## Why PACT for SHOT MVP?

### 1. Parallel Development
Frontend and backend teams can work independently once contracts are agreed upon. Frontend developers build against stub servers that behave exactly like the real API will.

### 2. Early Integration Testing
Discover integration issues during development, not during deployment. Contracts are verified continuously.

### 3. Living Documentation
PACT contracts serve as always-accurate API documentation. If the contract test passes, the documentation is correct.

### 4. Confidence in Changes
When modifying APIs, PACT immediately shows which consumers will be affected, preventing breaking changes.

### 5. Realistic Development Experience
Stub servers provide realistic responses including:
- Proper HTTP status codes
- Realistic data variations
- Error scenarios
- Performance characteristics

## How PACT Works

```mermaid
graph LR
    A[Frontend Dev] -->|Defines Expected API| B[PACT Contract]
    B -->|Generates| C[Stub Server]
    A -->|Develops Against| C
    
    D[Backend Dev] -->|Implements API| E[Real Service]
    E -->|Verifies Against| B
    
    B -->|Published To| F[PACT Broker]
    F -->|Continuous Verification| G[CI/CD Pipeline]
```

### Development Flow

1. **Contract Definition Phase**
   - Frontend and backend teams collaborate to define API contracts
   - Contracts specify request/response formats, headers, status codes
   - Edge cases and error scenarios are included

2. **Frontend Development**
   - PACT generates a stub server from contracts
   - Frontend developers build features against the stub server
   - All API behaviors are predictable and consistent

3. **Backend Development**
   - Backend team implements services to match contracts
   - PACT verifies implementation against contracts
   - Any deviation fails the build

4. **Integration**
   - Both sides are guaranteed to work together
   - No integration surprises during deployment

## Setting Up PACT in Different Environments

### Local Development Setup

```bash
# Install PACT dependencies
npm install --save-dev @pact-foundation/pact @pact-foundation/pact-node

# Install PACT stub server
npm install --save-dev @pact-foundation/stub-server

# Create PACT configuration
cat > pact.config.js << 'EOF'
module.exports = {
  // Consumer configuration
  consumer: "SHOT-Frontend",
  
  // Stub server settings
  stub: {
    port: 8080,
    host: "localhost",
    cors: true,
    logLevel: "info"
  },
  
  // Contract directories
  pactFilesOrDirs: ["./pacts"],
  
  // Broker configuration (if using)
  pactBrokerUrl: process.env.PACT_BROKER_URL,
  pactBrokerToken: process.env.PACT_BROKER_TOKEN,
  
  // Provider states
  stateHandlers: "./pact/provider-states.js"
};
EOF
```

### Package.json Scripts

```json
{
  "scripts": {
    "pact:stub": "pact-stub-server --port 8080 --pact-dir ./pacts",
    "pact:stub:watch": "nodemon --watch ./pacts --exec 'npm run pact:stub'",
    "pact:generate": "node ./scripts/generate-pacts.js",
    "pact:test:consumer": "jest --testMatch='**/*.pact.test.js'",
    "pact:test:provider": "jest --testMatch='**/*.provider.test.js'",
    "pact:publish": "pact-broker publish ./pacts --consumer-app-version=$npm_package_version",
    "pact:verify": "pact-broker can-i-deploy --pacticipant SHOT-Frontend --latest"
  }
}
```

### Environment Configuration

#### Development Environment
```bash
# .env.development
REACT_APP_API_URL=http://localhost:8080  # PACT stub server
REACT_APP_USE_PACT_STUBS=true
PACT_LOG_LEVEL=debug
```

#### Staging Environment
```bash
# .env.staging
REACT_APP_API_URL=https://api-staging.shot.app
REACT_APP_USE_PACT_STUBS=false
PACT_BROKER_URL=https://pact-broker.shot.app
```

#### Production Environment
```bash
# .env.production
REACT_APP_API_URL=https://api.shot.app
REACT_APP_USE_PACT_STUBS=false
```

### Docker Setup

```dockerfile
# Dockerfile.pact-stub
FROM node:18-alpine

WORKDIR /app

# Install PACT stub server globally
RUN npm install -g @pact-foundation/stub-server

# Copy contracts
COPY ./pacts /app/pacts

# Expose stub server port
EXPOSE 8080

# Start stub server
CMD ["pact-stub-server", "--port", "8080", "--pact-dir", "/app/pacts"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  frontend:
    build: .
    environment:
      - REACT_APP_API_URL=http://pact-stub:8080
    depends_on:
      - pact-stub
      
  pact-stub:
    build:
      context: .
      dockerfile: Dockerfile.pact-stub
    ports:
      - "8080:8080"
    volumes:
      - ./pacts:/app/pacts:ro
```

## Frontend Developer Experience

### 1. Starting Your Day

```bash
# Start the PACT stub server
npm run pact:stub:watch

# In another terminal, start your frontend
npm start

# You're now developing against realistic API stubs!
```

### 2. Understanding Available APIs

#### API Explorer Dashboard
Access the PACT API Explorer at `http://localhost:8080/explorer`:

```typescript
// The explorer shows:
- All available endpoints
- Request/response examples
- Try-it-out functionality
- Contract source files
```

#### TypeScript Types Generation
PACT contracts automatically generate TypeScript types:

```bash
# Generate types from contracts
npm run pact:generate:types

# This creates:
# src/types/generated/api.ts
```

```typescript
// Auto-generated types from PACT contracts
export interface CreateEventRequest {
  teamId: string;
  type: 'training' | 'match' | 'tournament' | 'social' | 'other';
  date: string; // Format: YYYY-MM-DD
  startTime: string; // Format: HH:mm
  location: {
    name: string;
    address?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  // ... all fields from contract
}

export interface CreateEventResponse {
  id: string;
  status: 'draft' | 'scheduled' | 'cancelled' | 'completed';
  // ... all fields from contract
}
```

### 3. Discovering APIs Through IDE

#### IntelliSense Support
Your IDE automatically suggests available endpoints:

```typescript
import { api } from '@/services/pact-client';

// IDE shows all available methods
api.events.create() // ← Autocomplete shows parameters
api.events.list()   // ← Shows query options
api.events.update() // ← Shows required fields
```

#### Inline Documentation
Contracts include descriptions that appear in your IDE:

```typescript
/**
 * Create a new training or match event
 * @param data Event creation data
 * @returns Created event with ID and initial RSVP stats
 * @throws 400 if required fields missing
 * @throws 409 if scheduling conflict exists
 */
api.events.create(data: CreateEventRequest): Promise<CreateEventResponse>
```

### 4. Working with Different Scenarios

PACT stubs support multiple scenarios through provider states:

```typescript
// Test different scenarios by setting provider state
await pactStub.setState('event at full capacity');
const response = await api.events.rsvp(eventId, { response: 'yes' });
// Returns waitlist response

await pactStub.setState('user is youth player requiring approval');
const rsvp = await api.events.rsvp(eventId, { response: 'yes' });
// Returns pending_approval status
```

### 5. Exploring Contracts Visually

#### Contract Viewer UI
Access visual contract documentation at `http://localhost:8080/contracts`:

```
SHOT API Contracts
├── Assess System
│   ├── POST /api/assess/events
│   ├── GET /api/assess/events/:id
│   └── POST /api/assess/evaluations
├── Identity System
│   ├── POST /api/auth/login
│   ├── POST /api/identity/family/children
│   └── GET /api/identity/family/members
├── Perform System
│   ├── POST /api/perform/sessions
│   ├── POST /api/perform/metrics/bulk
│   └── GET /api/perform/analytics/team/:id
└── Schedule System
    ├── POST /api/schedule/events
    ├── POST /api/schedule/events/:id/rsvp
    └── GET /api/schedule/calendar/month
```

Each endpoint shows:
- Request format with examples
- Response format with examples
- Possible error responses
- Performance expectations
- Related endpoints

### 6. Testing Your Integration

```typescript
// Frontend PACT test example
describe('Event Creation', () => {
  const provider = new PactV3({
    consumer: 'CoachPortal',
    provider: 'EventService'
  });

  it('creates a training event', async () => {
    // Define expected interaction
    await provider
      .given('coach has active team')
      .uponReceiving('a request to create training event')
      .withRequest({
        method: 'POST',
        path: '/api/schedule/events',
        headers: { 'Authorization': 'Bearer token' },
        body: {
          teamId: 'team-123',
          type: 'training',
          date: '2024-03-25',
          startTime: '18:00'
        }
      })
      .willRespondWith({
        status: 201,
        body: {
          id: Matchers.uuid(),
          status: 'scheduled'
        }
      });

    // Test your actual code
    const event = await eventService.createEvent({
      teamId: 'team-123',
      type: 'training',
      date: '2024-03-25',
      startTime: '18:00'
    });

    expect(event.id).toBeDefined();
    expect(event.status).toBe('scheduled');
  });
});
```

## API Discovery Tools

### 1. CLI Tool
```bash
# List all available endpoints
npx pact-cli list-endpoints

# Get details about specific endpoint
npx pact-cli describe POST /api/schedule/events

# Generate sample request
npx pact-cli generate-request POST /api/schedule/events
```

### 2. VS Code Extension
Install the "PACT Contract Explorer" extension:
- Browse contracts in sidebar
- Generate code snippets
- Preview requests/responses
- Test endpoints directly

### 3. Postman Collection
Import PACT contracts into Postman:
```bash
npx pact-to-postman --input ./pacts --output ./shot-api.postman_collection.json
```

### 4. OpenAPI Generation
Generate OpenAPI specs from PACT:
```bash
npx pact-to-openapi --input ./pacts --output ./openapi.yaml
```

## Development Workflow Example

### Day in the Life of a Frontend Developer

#### Morning: Starting New Feature
```bash
# 1. Pull latest contracts
git pull origin main

# 2. Start PACT stub server
npm run pact:stub:watch

# 3. Generate fresh TypeScript types
npm run pact:generate:types

# 4. Start development server
npm start
```

#### During Development
```typescript
// Building RSVP feature
import { useScheduleApi } from '@/hooks/useScheduleApi';

function RSVPButton({ eventId }) {
  const api = useScheduleApi();
  
  const handleRSVP = async (response: 'yes' | 'no' | 'maybe') => {
    try {
      // API client generated from PACT contracts
      const result = await api.events.submitRSVP(eventId, {
        response,
        note: 'Looking forward to it!'
      });
      
      // TypeScript knows all response fields
      if (result.status === 'waitlisted') {
        showNotification(`You're #${result.waitlistInfo.position} on the waitlist`);
      }
    } catch (error) {
      // Error types also from contract
      if (error.code === 'RSVP_DEADLINE_PASSED') {
        showError('RSVP deadline has passed');
      }
    }
  };
  
  return <Button onClick={() => handleRSVP('yes')}>Attend</Button>;
}
```

#### Testing Integration
```bash
# Run your PACT consumer tests
npm run pact:test:consumer

# Tests verify your code matches contracts
✓ Event creation follows contract
✓ RSVP submission follows contract
✓ Error handling matches contract
```

## Best Practices

### 1. Contract Design
- Include all likely scenarios in contracts
- Define clear error responses
- Use realistic example data
- Document performance expectations

### 2. Development Process
- Always work against stub server in development
- Run consumer tests before committing
- Keep contracts in version control
- Review contract changes in PRs

### 3. Team Collaboration
- Frontend and backend discuss contracts before implementation
- Use contracts as discussion artifacts
- Update contracts when requirements change
- Share contract updates in team channels

### 4. Testing Strategy
- Write PACT tests for all API integrations
- Include happy path and error scenarios
- Test performance-sensitive operations
- Verify offline/retry behavior

## Troubleshooting

### Common Issues

#### Stub Server Not Starting
```bash
# Check if port is in use
lsof -i :8080

# Use different port
PACT_STUB_PORT=8081 npm run pact:stub
```

#### Contract Mismatch
```bash
# Verify contract version
npm run pact:verify

# Update contracts
git pull origin main
npm run pact:generate:types
```

#### Type Generation Failing
```bash
# Clear cache and regenerate
rm -rf node_modules/.cache/pact
npm run pact:generate:types
```

## Resources

### Documentation
- [PACT Contracts Directory](./pacts/)
- [API Explorer](http://localhost:8080/explorer)
- [Contract Viewer](http://localhost:8080/contracts)

### Team Channels
- #shot-api-contracts - Contract discussions
- #shot-frontend - Frontend development
- #shot-backend - Backend development

### Tools
- [PACT CLI](https://docs.pact.io/implementation_guides/cli)
- [VS Code Extension](https://marketplace.visualstudio.com/items?itemName=pact.explorer)
- [Postman Collection](./shot-api.postman_collection.json)

## Getting Help

1. Check the contract viewer for API details
2. Run `npx pact-cli describe <endpoint>` for specifics
3. Ask in #shot-api-contracts channel
4. Review example implementations in codebase

This PACT setup ensures smooth frontend development with reliable API contracts, enabling teams to work efficiently in parallel while maintaining integration quality.