# Assess System Week 3 - Integration & Data Import Implementation Plan - Developer Work Split

## Developer Assignments

### DEV1 - Coach Event & Evaluation System
**Focus**: Polish coach experience and advanced features
- Event review and completion workflow
- Advanced filtering and sorting
- Bulk operations for coaches
- Integration with existing event system

### DEV2 - Player/Parent Experience  
**Focus**: Share links and result viewing
- Parent share link system
- Enhanced result visualization
- Post-evaluation forms
- Mobile app deep linking

### DEV3 - Backend/Pipeline/Data Import
**Focus**: CSV import system and data migration
- Complete CSV import pipeline
- Data validation and normalization
- Bulk team/player/event import
- Performance optimization for large datasets

---

## Overview

Week 3 focuses on integration, data import capabilities, and polishing the user experience. The CSV import system allows teams to quickly onboard their existing data, while share links enable parents to view their children's evaluations. This week also includes performance optimization and preparation for production deployment.

The data import system is crucial for adoption, as it allows teams to import their existing rosters, coaches, and scheduled events without manual data entry.

## Architecture Overview

### Directory Structure

```
src/features/assess/
├── components/
│   ├── import/
│   │   ├── CSVUploader.tsx [DEV3]
│   │   ├── ImportPreview.tsx [DEV3]
│   │   ├── ImportProgress.tsx [DEV3]
│   │   └── ValidationErrors.tsx [DEV3]
│   ├── review/
│   │   ├── EventReviewSummary.tsx [DEV1]
│   │   ├── PlayerHighlights.tsx [DEV1]
│   │   ├── TeamInsights.tsx [DEV1]
│   │   └── ExportOptions.tsx [DEV1]
│   ├── share/
│   │   ├── ShareLinkGenerator.tsx [DEV2]
│   │   ├── ParentViewLayout.tsx [DEV2]
│   │   ├── ResultsVisualization.tsx [DEV2]
│   │   └── ProgressChart.tsx [DEV2]
│   └── filters/
│       ├── EventFilters.tsx [DEV1]
│       ├── PlayerFilters.tsx [DEV1]
│       └── DateRangePicker.tsx [DEV1]
├── pages/
│   ├── ImportDataPage.tsx [DEV3]
│   ├── ImportResultsPage.tsx [DEV3]
│   ├── EventReviewPage.tsx [DEV1]
│   ├── ShareLinkPage.tsx [DEV2]
│   └── PostEvaluationPage.tsx [DEV2]
├── hooks/
│   ├── useCSVParser.ts [DEV3]
│   ├── useImportValidation.ts [DEV3]
│   ├── useShareLinks.ts [DEV2]
│   └── useDataExport.ts [DEV1]
├── services/
│   ├── ImportService.ts [DEV3]
│   ├── DataValidationService.ts [DEV3]
│   ├── ShareLinkService.ts [DEV2]
│   └── ExportService.ts [DEV1]
└── utils/
    ├── csvParsers.ts [DEV3]
    ├── dataMappers.ts [DEV3]
    └── shareTokens.ts [DEV2]
```

## Page Flow and Purpose

### Complete Feature Flow Diagram

```mermaid
graph TB
    %% Import Flow
    ImportStart([Coach Selects Import]) --> ImportData[Import Data Page - DEV3]
    ImportData --> |Upload CSV| Preview[Import Preview - DEV3]
    Preview --> |Validate| ImportResults[Import Results - DEV3]
    ImportResults --> |Success| EventsList[Events List]
    
    %% Share Flow
    EventComplete([Event Completed]) --> Review[Event Review Page - DEV1]
    Review --> |Generate Links| ShareLinks[Share Links - DEV2]
    
    ParentAccess([Parent Link]) --> ShareView[Parent View Page - DEV2]
    ShareView --> |View Results| ResultsViz[Results Visualization - DEV2]
    
    %% Post-Evaluation Flow
    PlayerNotif([Post-Event SMS]) --> PostEval[Post-Evaluation Page - DEV2]
    PostEval --> |Submit| PlayerDashboard[Player Dashboard]
```

### Page Details

#### 1. Import Data Page [DEV3]

**Purpose**: Central hub for importing team data via CSV files

**Path**: `/src/features/assess/pages/ImportDataPage.tsx`
**Route**: `/assess/import`

```typescript
/**
 * ImportDataPage
 * 
 * PURPOSE:
 * - Upload multiple CSV files
 * - Select import type (teams, players, events, coaches)
 * - Preview data before import
 * - Handle validation errors
 * 
 * USER GOALS:
 * - Import existing team data quickly
 * - Understand what will be imported
 * - Fix any data issues before import
 * - Avoid duplicate imports
 */
```

**New Components Required:**
```typescript
// CSV file uploader
import { CSVUploader } from '@/features/assess/components/import/CSVUploader';
// Path: /src/features/assess/components/import/CSVUploader.tsx

// Import preview table
import { ImportPreview } from '@/features/assess/components/import/ImportPreview';
// Path: /src/features/assess/components/import/ImportPreview.tsx

// Validation error display
import { ValidationErrors } from '@/features/assess/components/import/ValidationErrors';
// Path: /src/features/assess/components/import/ValidationErrors.tsx
```

**Services Used:**
```typescript
import { ImportService } from '@/features/assess/services/ImportService';
// Methods: 
// - parseCSV(file: File, type: ImportType): Promise<ParsedData>
// - validateData(data: ParsedData, type: ImportType): Promise<ValidationResult>
// - previewImport(data: ParsedData): Promise<ImportPreview>

import { DataValidationService } from '@/features/assess/services/DataValidationService';
// Methods:
// - validatePlayers(players: PlayerData[]): ValidationResult
// - validateEvents(events: EventData[]): ValidationResult
// - checkDuplicates(data: any[], type: ImportType): DuplicateResult
```

#### 2. Import Results Page [DEV3]

**Purpose**: Show results of import operation with detailed statistics

**Path**: `/src/features/assess/pages/ImportResultsPage.tsx`
**Route**: `/assess/import/results/:importId`

```typescript
/**
 * ImportResultsPage
 * 
 * PURPOSE:
 * - Display import statistics
 * - Show successfully imported records
 * - List any failures with reasons
 * - Provide next steps
 * 
 * USER GOALS:
 * - Know what was imported successfully
 * - Understand any failures
 * - Take action on imported data
 * - Download import report
 */
```

**Services Used:**
```typescript
import { ImportService } from '@/features/assess/services/ImportService';
// Methods: 
// - executeImport(data: ValidatedData, type: ImportType): Promise<ImportResult>
// - getImportResults(importId: string): Promise<ImportResult>
// - downloadImportReport(importId: string): Promise<Blob>
```

#### 3. Event Review Page [DEV1]

**Purpose**: Review completed event with insights and export options

**Path**: `/src/features/assess/pages/EventReviewPage.tsx`
**Route**: `/assess/events/:id/review`

```typescript
/**
 * EventReviewPage
 * 
 * PURPOSE:
 * - Show event completion summary
 * - Highlight top performers
 * - Display team insights
 * - Export evaluation data
 * 
 * USER GOALS:
 * - See event outcomes at a glance
 * - Identify standout players
 * - Export data for reports
 * - Generate share links
 */
```

**New Components Required:**
```typescript
// Event summary statistics
import { EventReviewSummary } from '@/features/assess/components/review/EventReviewSummary';
// Path: /src/features/assess/components/review/EventReviewSummary.tsx

// Top player highlights
import { PlayerHighlights } from '@/features/assess/components/review/PlayerHighlights';
// Path: /src/features/assess/components/review/PlayerHighlights.tsx

// Export options
import { ExportOptions } from '@/features/assess/components/review/ExportOptions';
// Path: /src/features/assess/components/review/ExportOptions.tsx
```

**Services Used:**
```typescript
import { ExportService } from '@/features/assess/services/ExportService';
// Methods:
// - exportEventData(eventId: string, format: 'csv' | 'pdf'): Promise<Blob>
// - generateReport(eventId: string): Promise<Report>
```

#### 4. Share Link Page (Parent View) [DEV2]

**Purpose**: Parents view their child's evaluation results

**Path**: `/src/features/assess/pages/ShareLinkPage.tsx`
**Route**: `/assess/share/:shareToken`

```typescript
/**
 * ShareLinkPage
 * 
 * PURPOSE:
 * - Display player evaluation results
 * - Show progress over time
 * - Visualize strengths/growth areas
 * - Mobile-optimized for parents
 * 
 * USER GOALS:
 * - See my child's evaluation clearly
 * - Understand their progress
 * - Identify areas to support
 * - Save or share results
 */
```

**New Components Required:**
```typescript
// Parent-friendly layout
import { ParentViewLayout } from '@/features/assess/components/share/ParentViewLayout';
// Path: /src/features/assess/components/share/ParentViewLayout.tsx

// Results visualization
import { ResultsVisualization } from '@/features/assess/components/share/ResultsVisualization';
// Path: /src/features/assess/components/share/ResultsVisualization.tsx

// Progress over time chart
import { ProgressChart } from '@/features/assess/components/share/ProgressChart';
// Path: /src/features/assess/components/share/ProgressChart.tsx
```

**Services Used:**
```typescript
import { ShareLinkService } from '@/features/assess/services/ShareLinkService';
// Methods:
// - validateShareToken(token: string): Promise<ShareData | null>
// - getPlayerEvaluationHistory(playerId: string, limit: number): Promise<EvaluationHistory>
// - generateShareLink(evaluationId: string): Promise<string>
```

#### 5. Post-Evaluation Page [DEV2]

**Purpose**: Players complete post-event self-evaluation

**Path**: `/src/features/assess/pages/PostEvaluationPage.tsx`
**Route**: `/assess/post-evaluate/:token`

```typescript
/**
 * PostEvaluationPage
 * 
 * PURPOSE:
 * - Player self-reflection post-event
 * - Compare with coach evaluation
 * - Set personal goals
 * - Track improvement areas
 * 
 * USER GOALS:
 * - Reflect on my performance
 * - See coach feedback
 * - Set improvement goals
 * - Track my progress
 */
```

## Route Configuration

```typescript
// /src/features/assess/routes/AssessRoutes.tsx [DEV1]
export const AssessRoutes = () => {
  const { isAssessEnabled } = useFeatureFlag('REACT_APP_USE_ASSESS_EVENTS');
  
  if (!isAssessEnabled) return null;
  
  return (
    <Switch>
      {/* Previous routes from Week 1-2 */}
      
      {/* Import routes - Week 3 */}
      <Route path="/assess/import" exact component={ImportDataPage} />
      <Route path="/assess/import/results/:importId" component={ImportResultsPage} />
      
      {/* Review routes - Week 3 */}
      <Route path="/assess/events/:id/review" component={EventReviewPage} />
      
      {/* Share routes - Week 3 */}
      <Route path="/assess/share/:shareToken" component={ShareLinkPage} />
      <Route path="/assess/post-evaluate/:token" component={PostEvaluationPage} />
    </Switch>
  );
};
```

## Database Design

### Import Tracking Schema [DEV3]

```sql
-- Week 3: Import tracking and batch operations

-- Import jobs table
CREATE TABLE IF NOT EXISTS import_jobs (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id uuid NOT NULL REFERENCES teams(id),
    imported_by uuid NOT NULL REFERENCES profiles(id),
    
    -- Import details
    import_type text CHECK (import_type IN ('players', 'coaches', 'events', 'full_team')),
    file_name text NOT NULL,
    file_size integer NOT NULL,
    
    -- Status tracking
    status text CHECK (status IN ('pending', 'validating', 'processing', 'completed', 'failed')),
    total_rows integer,
    processed_rows integer DEFAULT 0,
    successful_rows integer DEFAULT 0,
    failed_rows integer DEFAULT 0,
    
    -- Results
    validation_errors jsonb,
    import_results jsonb,
    
    -- Timestamps
    started_at timestamp with time zone,
    completed_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now()
);

-- Import mapping for deduplication
CREATE TABLE IF NOT EXISTS import_mappings (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    import_job_id uuid NOT NULL REFERENCES import_jobs(id),
    
    -- Source data
    source_type text NOT NULL,
    source_identifier text NOT NULL, -- e.g., email, name+dob
    
    -- Mapped result
    mapped_id uuid NOT NULL,
    mapped_type text NOT NULL,
    
    -- Status
    status text CHECK (status IN ('created', 'updated', 'skipped')),
    
    created_at timestamp with time zone DEFAULT now(),
    
    -- Prevent duplicate imports
    CONSTRAINT unique_import_mapping UNIQUE(import_job_id, source_type, source_identifier)
);

-- Share links table
CREATE TABLE IF NOT EXISTS evaluation_share_links (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- What's being shared
    event_id uuid NOT NULL REFERENCES events(id),
    player_id uuid NOT NULL REFERENCES profiles(id),
    
    -- Access control
    share_token uuid DEFAULT gen_random_uuid() UNIQUE NOT NULL,
    created_by uuid NOT NULL REFERENCES profiles(id),
    expires_at timestamp with time zone,
    
    -- Usage tracking
    access_count integer DEFAULT 0,
    last_accessed_at timestamp with time zone,
    
    -- Options
    include_history boolean DEFAULT false,
    include_coach_notes boolean DEFAULT false,
    
    created_at timestamp with time zone DEFAULT now(),
    
    -- One share link per player per event
    CONSTRAINT unique_player_event_share UNIQUE(event_id, player_id)
);

-- Indexes for performance
CREATE INDEX idx_import_jobs_team ON import_jobs(team_id);
CREATE INDEX idx_import_jobs_status ON import_jobs(status);
CREATE INDEX idx_import_mappings_job ON import_mappings(import_job_id);
CREATE INDEX idx_share_links_token ON evaluation_share_links(share_token);
CREATE INDEX idx_share_links_player ON evaluation_share_links(player_id);

-- Function to process CSV import data
CREATE OR REPLACE FUNCTION process_player_import(
    p_import_job_id uuid,
    p_players jsonb -- Array of player data
)
RETURNS TABLE (
    created_count integer,
    updated_count integer,
    skipped_count integer,
    error_count integer
)
SECURITY DEFINER
AS $$
DECLARE
    v_player jsonb;
    v_existing_id uuid;
    v_new_id uuid;
    v_created integer := 0;
    v_updated integer := 0;
    v_skipped integer := 0;
    v_errors integer := 0;
BEGIN
    -- Process each player
    FOR v_player IN SELECT * FROM jsonb_array_elements(p_players)
    LOOP
        BEGIN
            -- Check if player exists (by email or name+dob)
            SELECT id INTO v_existing_id
            FROM profiles
            WHERE email = v_player->>'email'
            OR (display_name = v_player->>'name' AND date_of_birth::text = v_player->>'dob');
            
            IF v_existing_id IS NOT NULL THEN
                -- Update existing player
                UPDATE profiles
                SET 
                    display_name = COALESCE(v_player->>'name', display_name),
                    phone = COALESCE(v_player->>'phone', phone),
                    updated_at = now()
                WHERE id = v_existing_id;
                
                v_updated := v_updated + 1;
                
                -- Record mapping
                INSERT INTO import_mappings (import_job_id, source_type, source_identifier, mapped_id, mapped_type, status)
                VALUES (p_import_job_id, 'player', v_player->>'email', v_existing_id, 'profile', 'updated');
            ELSE
                -- Create new player
                INSERT INTO profiles (display_name, email, phone, date_of_birth, user_type)
                VALUES (
                    v_player->>'name',
                    v_player->>'email',
                    v_player->>'phone',
                    (v_player->>'dob')::date,
                    'player'
                )
                RETURNING id INTO v_new_id;
                
                v_created := v_created + 1;
                
                -- Record mapping
                INSERT INTO import_mappings (import_job_id, source_type, source_identifier, mapped_id, mapped_type, status)
                VALUES (p_import_job_id, 'player', v_player->>'email', v_new_id, 'profile', 'created');
            END IF;
            
        EXCEPTION WHEN OTHERS THEN
            v_errors := v_errors + 1;
            -- Log error but continue processing
            RAISE NOTICE 'Error importing player %: %', v_player->>'email', SQLERRM;
        END;
    END LOOP;
    
    -- Return summary
    RETURN QUERY SELECT v_created, v_updated, v_skipped, v_errors;
END;
$$ LANGUAGE plpgsql;
```

## Service Architecture

### ImportService [DEV3]

```typescript
// /src/features/assess/services/ImportService.ts

export interface ImportService {
  // CSV parsing
  parseCSV(file: File, type: ImportType): Promise<ParsedData>;
  detectCSVFormat(headers: string[]): ImportType | null;
  
  // Validation
  validateData(data: ParsedData, type: ImportType): Promise<ValidationResult>;
  previewImport(data: ParsedData): Promise<ImportPreview>;
  
  // Import execution
  executeImport(data: ValidatedData, options: ImportOptions): Promise<ImportResult>;
  processInBatches(data: any[], batchSize: number): Promise<BatchResult[]>;
  
  // Status tracking
  getImportStatus(jobId: string): Promise<ImportJob>;
  getImportHistory(teamId: string): Promise<ImportJob[]>;
  
  // Results
  getImportResults(jobId: string): Promise<DetailedImportResult>;
  downloadImportReport(jobId: string): Promise<Blob>;
}

export interface ImportOptions {
  updateExisting: boolean;
  skipDuplicates: boolean;
  validateOnly: boolean;
  teamId: string;
}
```

### DataValidationService [DEV3]

```typescript
// /src/features/assess/services/DataValidationService.ts

export interface DataValidationService {
  // Player validation
  validatePlayers(players: PlayerImportData[]): ValidationResult;
  checkPlayerDuplicates(players: PlayerImportData[]): DuplicateCheck[];
  
  // Event validation
  validateEvents(events: EventImportData[]): ValidationResult;
  validateEventDates(events: EventImportData[]): DateValidation[];
  
  // Coach validation
  validateCoaches(coaches: CoachImportData[]): ValidationResult;
  checkCoachPermissions(coaches: CoachImportData[]): PermissionCheck[];
  
  // Data normalization
  normalizePhoneNumbers(data: any[]): any[];
  normalizeDates(data: any[]): any[];
  normalizePositions(data: any[]): any[];
}
```

### ShareLinkService [DEV2]

```typescript
// /src/features/assess/services/ShareLinkService.ts

export interface ShareLinkService {
  // Link generation
  generateShareLink(eventId: string, playerId: string, options?: ShareOptions): Promise<ShareLink>;
  generateBulkShareLinks(eventId: string): Promise<ShareLink[]>;
  
  // Link validation
  validateShareToken(token: string): Promise<ShareData | null>;
  checkLinkExpiry(token: string): Promise<boolean>;
  
  // Access tracking
  trackLinkAccess(token: string): Promise<void>;
  getAccessHistory(linkId: string): Promise<AccessLog[]>;
  
  // Data retrieval
  getSharedEvaluationData(token: string): Promise<SharedEvaluation>;
  getPlayerHistory(playerId: string, limit: number): Promise<EvaluationHistory>;
}

export interface ShareOptions {
  expiresInDays?: number;
  includeHistory?: boolean;
  includeCoachNotes?: boolean;
}
```

## Implementation Timeline

### Phase 1: Import Infrastructure (Days 1-2)

**Goal**: Complete CSV import system with validation

**DEV1**: 
- Build filtering system for events
- Create bulk selection UI
- Prepare export functionality

**DEV2**:
- Design share link UI components
- Create parent view layouts
- Build visualization components

**DEV3**:
- Implement CSV parsing
- Create validation rules
- Build import preview
- Set up batch processing

### Phase 2: Data Import & Sharing (Days 3-4)

**Goal**: Working import system and share links

**DEV1**:
- Complete Event Review page
- Implement export functionality
- Add advanced filtering
- Create team insights

**DEV2**:
- Implement share link generation
- Complete parent view page
- Add progress visualization
- Test on various devices

**DEV3**:
- Complete import execution
- Handle edge cases
- Optimize for large files
- Create import reports

### Phase 3: Polish & Integration (Day 5)

**Goal**: Production-ready system with smooth workflows

**DEV1**:
- Integration with main app
- Performance optimization
- Polish UI interactions
- Complete documentation

**DEV2**:
- Mobile deep linking
- Share link security
- Accessibility testing
- Parent feedback incorporation

**DEV3**:
- Import stress testing
- Data integrity checks
- Performance optimization
- Error recovery

## Integration Points

### DEV1 ↔ DEV3
- Export format compatibility (DEV3 import = DEV1 export)
- Bulk operations performance (shared optimization)
- Data filtering queries (DEV3 optimizes, DEV1 uses)

### DEV2 ↔ DEV3
- Share link generation and validation (DEV3 provides, DEV2 uses)
- Performance for mobile data loading (joint optimization)
- Security model for parent access (shared design)

### DEV1 ↔ DEV2
- Consistent result visualization (shared components)
- Export includes share links (DEV1 triggers, DEV2 generates)
- Mobile/desktop responsive patterns (continued from Week 2)

## Key Design Decisions

1. **CSV format flexibility**: Support multiple CSV formats with smart detection
2. **Incremental imports**: Allow adding to existing data without duplicates
3. **Share link security**: Time-limited tokens with access tracking
4. **Batch processing**: Handle large imports without blocking UI
5. **Parent-friendly design**: Simplified UI for non-technical users

## Success Metrics

### Performance
- Import 500 players in < 30 seconds [DEV3]
- Generate 100 share links in < 5 seconds [DEV2]
- Export event data in < 3 seconds [DEV1]
- CSV validation feedback in < 2 seconds [DEV3]

### User Experience
- 95% of imports succeed without errors [DEV3]
- Parents access results without help [DEV2]
- Coaches can filter/sort efficiently [DEV1]
- Import preview prevents mistakes [DEV3]

### Data Quality
- Zero data corruption from imports [DEV3]
- Duplicate detection accuracy > 99% [DEV3]
- Share links expire correctly [DEV2]
- Complete audit trail maintained [All]

## Out of Scope (Week 3)

- Automated recurring imports
- API-based integrations
- Custom report templates
- Video/photo attachments
- Multi-language support
- Advanced analytics

---

## Document Metadata

**Last Updated**: 2025-01-17
**Status**: Ready for Implementation
**Feature Owner**: Assess System Team
**Technical Lead**: DEV3
**Document Version**: 1.0