# Assess System Week 1 - Foundation & Core Infrastructure Implementation Plan - Developer Work Split

## 🚨 Critical: Zero Breaking Changes Approach

This implementation introduces **NO breaking changes** to the existing system:
- **New routes**: All under `/assess/*` (old routes untouched)
- **New schema**: `evaluations` schema (old tables untouched)  
- **New code**: All in `/src/features/assess/` (old code untouched)
- **Only addition**: New columns to events table (non-breaking)
- **Parallel systems**: Both old and new work simultaneously

## Developer Assignments

### DEV1 - Coach Event & Evaluation System
**Focus**: Coach-facing pages and workflows for event management
- Create Event page and event draft management
- Event lifecycle state management (draft → published → session → complete)
- Coach navigation and routing setup
- Integration with event service layer

### DEV2 - Player/Parent Experience  
**Focus**: Token-based access and mobile-first interfaces
- Pre-evaluation token routing infrastructure
- Mobile-responsive foundation components
- Error handling for invalid/expired tokens
- Development test harness for token testing

### DEV3 - Backend/Pipeline/Data Import
**Focus**: Database schema, services, and core infrastructure
- Complete database migration implementation
- Core service layer architecture (events, evaluations, tokens)
- Token generation and validation system
- Performance optimization and indexing

---

## Overview

Week 1 establishes the foundation for the Assess system, focusing on database infrastructure, core services, and the basic page structure that will support all future development. This week prioritizes getting the data layer fully operational, establishing the service architecture, and building the first complete user flow (coach creating an event).

The goal is to have a working system where coaches can create draft events, all database operations are functional, and the token-based access system is operational for future player interfaces.

## Architecture Overview

### Directory Structure

```
src/features/assess/
├── components/
│   ├── shared/
│   │   ├── EventStatusBadge.tsx [DEV1]
│   │   ├── PlayerSelector.tsx [DEV1]
│   │   └── MobileResponsiveWrapper.tsx [DEV2]
│   ├── event-creation/
│   │   ├── EventForm.tsx [DEV1]
│   │   ├── DateTimePicker.tsx [DEV1]
│   │   └── DraftSaveIndicator.tsx [DEV1]
├── pages/
│   ├── CreateEventPage.tsx [DEV1]
│   ├── EventsListPage.tsx [DEV1]
│   ├── EventDetailPage.tsx [DEV1]
│   ├── PreEvaluationPage.tsx [DEV2]
│   └── TokenErrorPage.tsx [DEV2]
├── hooks/
│   ├── useAutoSave.ts [DEV1]
│   ├── useTokenValidation.ts [DEV2]
│   └── useFeatureFlag.ts [DEV3]
├── services/
│   ├── AssessEventService.ts [DEV3]
│   ├── TokenService.ts [DEV3]
│   └── EvaluationService.ts [DEV3]
├── routes/
│   └── AssessRoutes.tsx [DEV1]
└── types/
    ├── event.types.ts [DEV3]
    └── evaluation.types.ts [DEV3]
```

## Page Flow and Purpose

### Complete Feature Flow Diagram

```mermaid
graph TB
    Start([Coach Login]) --> EventsList[Events List Page - DEV1]
    
    EventsList --> |Create New| CreateEvent[Create Event Page - DEV1]
    CreateEvent --> |Save Draft| EventDetail[Event Detail Page - DEV1]
    CreateEvent --> |Cancel| EventsList
    
    EventsList --> |View Event| EventDetail
    EventDetail --> |Edit| CreateEvent
    
    TokenAccess([Token URL Access]) --> TokenValidation{Valid Token? - DEV2}
    TokenValidation --> |Yes| PreEval[Pre-Evaluation Page - DEV2]
    TokenValidation --> |No| TokenError[Token Error Page - DEV2]
```

### Page Details

#### 1. Events List Page [DEV1]

**Purpose**: Central hub for coaches to manage all their events

**Path**: `/src/features/assess/pages/EventsListPage.tsx`
**Route**: `/assess/events`

```typescript
/**
 * EventsListPage
 * 
 * PURPOSE:
 * - Display all events for the current coach
 * - Show event status (draft, published, session, complete)
 * - Provide quick actions for event management
 * 
 * USER GOALS:
 * - See all my events at a glance
 * - Quickly create a new event
 * - Understand event status immediately
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowButton } from '@/components/shadow/ShadowButton';
import { ShadowCard } from '@/components/shadow/ShadowCard';
```

**New Components Required:**
```typescript
// Event status indicator
import { EventStatusBadge } from '@/features/assess/components/shared/EventStatusBadge';
// Path: /src/features/assess/components/shared/EventStatusBadge.tsx
```

**Services Used:**
```typescript
import { AssessEventService } from '@/features/assess/services/AssessEventService';
// Methods: 
// - getCoachEvents(coachId: string): Promise<Event[]>
// - getEventCounts(): Promise<EventCounts>
```

#### 2. Create Event Page [DEV1]

**Purpose**: Allow coaches to create new events quickly (<30 seconds)

**Path**: `/src/features/assess/pages/CreateEventPage.tsx`
**Route**: `/assess/events/create`

```typescript
/**
 * CreateEventPage
 * 
 * PURPOSE:
 * - Create new training or match events
 * - Select players for the event
 * - Auto-save as draft to prevent data loss
 * 
 * USER GOALS:
 * - Create an event in under 30 seconds
 * - Never lose my work if interrupted
 * - Easily select which players are attending
 */
```

**New Components Required:**
```typescript
// Complete event creation form
import { EventForm } from '@/features/assess/components/event-creation/EventForm';
// Path: /src/features/assess/components/event-creation/EventForm.tsx

// Custom date/time picker
import { DateTimePicker } from '@/features/assess/components/event-creation/DateTimePicker';
// Path: /src/features/assess/components/event-creation/DateTimePicker.tsx

// Player multi-select
import { PlayerSelector } from '@/features/assess/components/shared/PlayerSelector';
// Path: /src/features/assess/components/shared/PlayerSelector.tsx

// Draft save indicator
import { DraftSaveIndicator } from '@/features/assess/components/event-creation/DraftSaveIndicator';
// Path: /src/features/assess/components/event-creation/DraftSaveIndicator.tsx
```

**Services Used:**
```typescript
import { AssessEventService } from '@/features/assess/services/AssessEventService';
// Methods: 
// - createEvent(data: CreateEventDTO): Promise<Event>
// - updateDraft(id: string, data: Partial<Event>): Promise<Event>
```

**Hooks Used:**
```typescript
import { useAutoSave } from '@/features/assess/hooks/useAutoSave';
// Returns: { isSaving, lastSaved, error }
```

#### 3. Event Detail Page (Basic) [DEV1]

**Purpose**: View and manage a draft event

**Path**: `/src/features/assess/pages/EventDetailPage.tsx`
**Route**: `/assess/events/:id`

```typescript
/**
 * EventDetailPage (Week 1 - Basic Version)
 * 
 * PURPOSE:
 * - View draft event details
 * - Edit event information
 * - See selected players
 * - Delete draft if needed
 * 
 * USER GOALS:
 * - Confirm my event was created successfully
 * - Review event details before publishing (Week 2)
 * - Make changes to draft events
 * - See which players are included
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowButton } from '@/components/shadow/ShadowButton';
import { ShadowCard } from '@/components/shadow/ShadowCard';
import { ShadowHeading } from '@/components/shadow/ShadowHeading';
```

**New Components Required:**
```typescript
// Event status badge (reused in Week 2)
import { EventStatusBadge } from '@/features/assess/components/shared/EventStatusBadge';
// Path: /src/features/assess/components/shared/EventStatusBadge.tsx
```

**Services Used:**
```typescript
import { AssessEventService } from '@/features/assess/services/AssessEventService';
// Methods: 
// - getEventById(id: string): Promise<Event>
// - updateDraft(id: string, data: Partial<Event>): Promise<Event>
// - deleteDraft(id: string): Promise<void>
```

**Week 1 Features**:
- Display event type, date, time, location
- Show list of selected players
- Edit button (navigates to Create Event page in edit mode)
- Delete draft functionality
- Basic status display (Draft)

**Week 2 Enhancements**:
- Publish functionality
- SMS controls
- Pre-evaluation status tracking
- Session management actions

#### 4. Pre-Evaluation Page (Shell) [DEV2]

**Purpose**: Token-based pre-evaluation form for players (building on existing approach)

**Path**: `/src/features/assess/pages/PreEvaluationPage.tsx`
**Route**: `/assess/evaluate/:eventToken/:playerId`

```typescript
/**
 * PreEvaluationPage
 * 
 * PURPOSE:
 * - Allow players to complete pre-evaluations without login
 * - Reuse existing token-based approach (UUID access)
 * - Mobile-first interface for quick completion
 * 
 * USER GOALS:
 * - Complete my pre-evaluation in under 2 minutes
 * - Access from SMS link on my phone
 * - See confirmation when submitted
 * 
 * SECURITY MODEL (Reusing existing):
 * - Event has a pre_evaluation_token (UUID)
 * - Combined with player_id for access
 * - No authentication required (non-sensitive data)
 * - Links expire after event starts
 */
```

**Services Used:**
```typescript
import { TokenService } from '@/features/assess/services/TokenService';
// Methods: 
// - validateToken(token: string): Promise<TokenData | null>
// - getTokenMetadata(token: string): Promise<TokenMetadata>
```

**Hooks Used:**
```typescript
import { useTokenValidation } from '@/features/assess/hooks/useTokenValidation';
// Returns: { isValid, isLoading, tokenData, error }
```

#### 4. Token Error Page [DEV2]

**Purpose**: User-friendly error page for invalid or expired tokens

**Path**: `/src/features/assess/pages/TokenErrorPage.tsx`
**Route**: Rendered by TokenValidation hook

```typescript
/**
 * TokenErrorPage
 * 
 * PURPOSE:
 * - Show clear error message for invalid tokens
 * - Explain why access was denied
 * - Provide next steps for users
 * 
 * USER GOALS:
 * - Understand why I can't access the page
 * - Know what to do next
 * - Contact my coach if needed
 */
```

## Route Configuration

```typescript
// /src/features/assess/routes/AssessRoutes.tsx [DEV1]
export const AssessRoutes = () => {
  const { isAssessEnabled } = useFeatureFlag('REACT_APP_USE_ASSESS_EVENTS');
  
  if (!isAssessEnabled) return null;
  
  return (
    <Switch>
      {/* All new routes under /assess - completely separate from old system */}
      
      {/* Coach routes */}
      <Route path="/assess/events" exact component={EventsListPage} />
      <Route path="/assess/events/create" component={CreateEventPage} />
      <Route path="/assess/events/:id" component={EventDetailPage} />
      
      {/* Token-based routes (no auth required) */}
      <Route path="/assess/evaluate/:eventToken/:playerId" component={PreEvaluationPage} />
      
      {/* Week 2+ routes commented out */}
      {/* <Route path="/assess/events/:id/publish" component={PublishEventPage} /> */}
      {/* <Route path="/assess/events/:id/evaluate" component={CoachEvaluationPage} /> */}
    </Switch>
  );
};
```

## Database Design

### SHOT Perform Framework Schema [DEV3]

```sql
-- Week 1: Clean new implementation - NO migration of old evaluation data
-- The Assess system is 100% independent with:
--   1. New 'evaluations' schema
--   2. New tables: evaluations.frameworks, evaluations.criteria, evaluations.event_evaluations
--   3. Copy SHOT framework data only (evaluation_criteria content, not old evaluations)
--   4. New routes under /assess/*
--   5. Feature flag controls navigation link visibility

-- Add SMS control columns to events table (Week 1)
ALTER TABLE events ADD COLUMN IF NOT EXISTS sms_auto_send boolean DEFAULT false;
ALTER TABLE events ADD COLUMN IF NOT EXISTS sms_sent_at timestamp with time zone;
ALTER TABLE events ADD COLUMN IF NOT EXISTS sms_status text CHECK (sms_status IN ('pending', 'sending', 'sent', 'failed'));

-- Add token columns for pre-evaluation access (Week 1)
-- Reusing existing pattern from pre_evaluations table where each record has its own UUID
-- But adding event-level token for simplified access
ALTER TABLE events ADD COLUMN IF NOT EXISTS pre_evaluation_token uuid DEFAULT gen_random_uuid();
ALTER TABLE events ADD COLUMN IF NOT EXISTS pre_evaluation_expires_at timestamp with time zone;

-- Update the token to expire when event starts (security enhancement)
CREATE OR REPLACE FUNCTION set_pre_evaluation_expiry()
RETURNS TRIGGER AS $$
BEGIN
    NEW.pre_evaluation_expires_at := NEW.start_datetime;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_pre_eval_expiry_trigger
    BEFORE INSERT OR UPDATE OF start_datetime ON events
    FOR EACH ROW
    EXECUTE FUNCTION set_pre_evaluation_expiry();

-- The evaluations.event_evaluations table structure (already exists):
-- Each player gets multiple evaluation records per event:
-- - 4 standard Technical area questions (for all positions)
-- - 1 position-specific question (if player has a position)
-- Total: typically 5 event_evaluations per player per event

-- Key fields in event_evaluations:
-- - criteria_id: links to evaluation_criteria table
-- - category: TECHNICAL, PHYSICAL, PSYCHOLOGICAL, SOCIAL
-- - area: specific focus area within category
-- - position: which position this criteria applies to
-- - player_position: the player's actual position
-- - question_pre/coach/post: the actual questions (frozen at creation)
-- - pre_score/coach_score/post_score: numeric scores (1.0-10.0)
-- - week_number: which week of the season

-- Function to create evaluations for an event (updated for clarity)
-- This function is called when an event moves to "published" status
-- It creates 4-5 evaluation records per player based on:
-- 1. The week number (from event date)
-- 2. The player's position
-- 3. Active evaluation criteria for that week/position
CREATE OR REPLACE FUNCTION create_event_evaluations_for_assess(
    p_event_id uuid
)
RETURNS void 
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_event record;
    v_participant record;
    v_criteria record;
    v_week integer;
BEGIN
    -- Get event details
    SELECT * INTO v_event FROM events WHERE id = p_event_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Event not found: %', p_event_id;
    END IF;
    
    -- Calculate week number from event date
    v_week := EXTRACT(WEEK FROM v_event.start_datetime);
    
    -- For each participant in the event
    FOR v_participant IN
        SELECT 
            ep.user_id as player_id,
            tm.position as player_position,
            tm.team_id
        FROM event_participants ep
        JOIN team_members tm ON tm.user_id = ep.user_id AND tm.team_id = v_event.team_id
        WHERE ep.event_id = p_event_id
        AND tm.status = 'active'
    LOOP
        -- Get all applicable criteria for this week
        -- This includes: 4 Technical areas (for 'All' positions) + 1 position-specific
        FOR v_criteria IN
            SELECT ec.*
            FROM evaluation_criteria ec
            WHERE ec.week_number = v_week
            AND ec.framework_version = 'SHOT-2025'
            AND ec.sport_type = 'football'  -- TODO: Get from team sport_type
            AND (
                -- Standard technical questions for all positions
                (ec.category = 'TECHNICAL' AND ec.position = 'All')
                OR 
                -- Position-specific question
                (ec.position = v_participant.player_position)
            )
            AND ec.is_active = true
            ORDER BY 
                CASE WHEN ec.position = 'All' THEN 0 ELSE 1 END,
                ec.category, 
                ec.area
        LOOP
            -- Insert evaluation record with frozen question text
            INSERT INTO evaluations.event_evaluations (
                event_id, player_id, team_id,
                criteria_id, category, area, position,
                player_position,
                question_pre, question_coach, question_post,
                answers_pre, answers_post,
                week_number, framework_version
            ) VALUES (
                p_event_id, 
                v_participant.player_id, 
                v_participant.team_id,
                v_criteria.id, 
                v_criteria.category, 
                v_criteria.area, 
                v_criteria.position,
                COALESCE(v_participant.player_position, 'All'),
                v_criteria.question_pre, 
                v_criteria.question, 
                v_criteria.question_post,
                v_criteria.answers_pre, 
                v_criteria.answers_post,
                v_week, 
                'SHOT-2025'
            )
            ON CONFLICT (event_id, player_id, criteria_id) DO NOTHING;
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Indexes for token-based access (Week 1)
CREATE INDEX IF NOT EXISTS idx_events_pre_evaluation_token ON events(pre_evaluation_token);
CREATE INDEX IF NOT EXISTS idx_events_pre_evaluation_expires ON events(pre_evaluation_expires_at) 
    WHERE pre_evaluation_expires_at IS NOT NULL;

-- Create new evaluation tables in evaluations schema (NOT moving old ones)
-- Old tables remain untouched for existing system

-- 1. Create frameworks table (better named version)
CREATE TABLE evaluations.frameworks (
    framework_version character varying(20) NOT NULL PRIMARY KEY,
    name character varying(100) NOT NULL,
    description text,
    start_date date NOT NULL,
    end_date date NOT NULL,
    total_weeks integer NOT NULL,
    positions text[] NOT NULL,
    categories text[] NOT NULL,
    sport_type character varying(50) DEFAULT 'football'::character varying NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);

-- 2. Create criteria table with SHOT framework data
CREATE TABLE evaluations.criteria (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    position character varying(50) NOT NULL,
    category character varying(50) NOT NULL,
    area character varying(100) NOT NULL,
    evaluation_focus text NOT NULL,
    question text NOT NULL,
    question_pre text,
    question_post text,
    answers_pre jsonb,
    answers_post jsonb,
    week_number integer NOT NULL CHECK (week_number >= 1 AND week_number <= 52),
    framework_version character varying(20) DEFAULT 'SHOT-2025' NOT NULL,
    framework_date date,
    sport_type character varying(50) DEFAULT 'football' NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT unique_criteria UNIQUE (position, category, area, week_number, framework_version, sport_type)
);

-- 3. Copy ONLY the SHOT framework data (not old evaluations)
INSERT INTO evaluations.frameworks 
SELECT DISTINCT 
    framework_version,
    'SHOT Perform Framework' as name,
    'SHOT performance evaluation framework' as description,
    MIN(framework_date) as start_date,
    MAX(framework_date) as end_date,
    52 as total_weeks,
    ARRAY['All', 'Goalkeeper', 'Defender', 'Midfielder', 'Forward'] as positions,
    ARRAY['TECHNICAL', 'PHYSICAL', 'PSYCHOLOGICAL', 'SOCIAL'] as categories,
    sport_type,
    is_active,
    now() as created_at
FROM public.evaluation_criteria
WHERE framework_version = 'SHOT-2025'
GROUP BY framework_version, sport_type, is_active;

INSERT INTO evaluations.criteria
SELECT * FROM public.evaluation_criteria
WHERE framework_version = 'SHOT-2025';

-- 4. Update the function to use new schema/table names
CREATE OR REPLACE FUNCTION create_event_evaluations_for_assess(
    p_event_id uuid
)
RETURNS void 
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_event record;
    v_participant record;
    v_criteria record;
    v_week integer;
BEGIN
    -- Get event details
    SELECT * INTO v_event FROM events WHERE id = p_event_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Event not found: %', p_event_id;
    END IF;
    
    -- Calculate week number from event date
    v_week := EXTRACT(WEEK FROM v_event.start_datetime);
    
    -- For each participant in the event
    FOR v_participant IN
        SELECT 
            ep.user_id as player_id,
            tm.position as player_position,
            tm.team_id
        FROM event_participants ep
        JOIN team_members tm ON tm.user_id = ep.user_id AND tm.team_id = v_event.team_id
        WHERE ep.event_id = p_event_id
        AND tm.status = 'active'
    LOOP
        -- Get all applicable criteria for this week
        FOR v_criteria IN
            SELECT ec.*
            FROM evaluations.criteria ec  -- Using new schema/table name
            WHERE ec.week_number = v_week
            AND ec.framework_version = 'SHOT-2025'
            AND ec.sport_type = 'football'  -- TODO: Get from team sport_type
            AND (
                -- Standard technical questions for all positions
                (ec.category = 'TECHNICAL' AND ec.position = 'All')
                OR 
                -- Position-specific question
                (ec.position = v_participant.player_position)
            )
            AND ec.is_active = true
            ORDER BY 
                CASE WHEN ec.position = 'All' THEN 0 ELSE 1 END,
                ec.category, 
                ec.area
        LOOP
            -- Insert evaluation record with frozen question text
            INSERT INTO evaluations.event_evaluations (
                event_id, player_id, team_id,
                criteria_id, category, area, position,
                player_position,
                question_pre, question_coach, question_post,
                answers_pre, answers_post,
                week_number, framework_version
            ) VALUES (
                p_event_id, 
                v_participant.player_id, 
                v_participant.team_id,
                v_criteria.id, 
                v_criteria.category, 
                v_criteria.area, 
                v_criteria.position,
                COALESCE(v_participant.player_position, 'All'),
                v_criteria.question_pre, 
                v_criteria.question, 
                v_criteria.question_post,
                v_criteria.answers_pre, 
                v_criteria.answers_post,
                v_week, 
                'SHOT-2025'
            )
            ON CONFLICT (event_id, player_id, criteria_id) DO NOTHING;
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

## Service Architecture

### AssessEventService [DEV3]

```typescript
// /src/features/assess/services/AssessEventService.ts

export interface AssessEventService {
  // Event CRUD operations
  createEvent(data: CreateEventDTO): Promise<Event>;
  updateDraft(id: string, data: Partial<Event>): Promise<Event>;
  getCoachEvents(coachId: string): Promise<Event[]>;
  getEventById(id: string): Promise<Event | null>;
  
  // Event lifecycle
  publishEvent(id: string): Promise<Event>;
  startSession(id: string): Promise<Event>;
  completeEvent(id: string): Promise<Event>;
  
  // Utility methods
  getEventCounts(coachId: string): Promise<EventCounts>;
  validateEventData(data: any): boolean;
}

export class AssessEventServiceImpl implements AssessEventService {
  // Implementation using Supabase client
}
```

### TokenService [DEV3]

```typescript
// /src/features/assess/services/TokenService.ts

export interface TokenService {
  // Token validation
  validateToken(token: string): Promise<TokenData | null>;
  isTokenExpired(token: string): Promise<boolean>;
  
  // Token generation (called by backend)
  generatePreEvaluationToken(eventId: string, playerId: string): Promise<string>;
  generateShareToken(evaluationId: string): Promise<string>;
  
  // Token metadata
  getTokenMetadata(token: string): Promise<TokenMetadata>;
  getTokenType(token: string): Promise<'pre-evaluation' | 'share' | null>;
}
```

### EvaluationService [DEV3]

```typescript
// /src/features/assess/services/EvaluationService.ts

export interface EvaluationService {
  // Pre-evaluation operations
  getPlayerPreEvaluations(eventId: string, playerId: string): Promise<EventEvaluation[]>;
  submitPreEvaluationScores(eventId: string, playerId: string, scores: EvaluationScore[]): Promise<void>;
  
  // Coach evaluation operations (Week 2)
  getEventEvaluationsForCoach(eventId: string): Promise<PlayerEvaluationGroup[]>;
  submitCoachEvaluations(eventId: string, playerId: string, scores: EvaluationScore[]): Promise<void>;
  
  // SHOT Framework operations
  createEventEvaluations(eventId: string): Promise<void>;
  getEvaluationSummary(eventId: string): Promise<EvaluationSummary>;
  
  // Types used
  // EventEvaluation: single evaluation record (1 of ~5 per player)
  // EvaluationScore: { criteriaId: string, score: number }
  // PlayerEvaluationGroup: all evaluations for one player
}
```

## Implementation Timeline

### Phase 1: Database & Infrastructure (Days 1-2)

**Goal**: Complete database schema and migrations running successfully

**DEV1**: 
- Set up feature flag integration
- Create basic routing structure
- Prepare shared component shells

**DEV2**:
- Research token-based routing patterns
- Set up mobile testing environment
- Create responsive wrapper components

**DEV3**:
- Create new evaluations schema with clean tables
- Copy SHOT framework data only (not old evaluations)
- Add SMS and token columns to events table
- Create and test all indexes
- Verify trigger functionality
- Set up service layer architecture

### Phase 2: Core Services & Basic UI (Days 3-4)

**Goal**: Services operational, basic pages accessible

**DEV1**:
- Build Events List page
- Implement Create Event page
- Add auto-save functionality

**DEV2**:
- Implement token validation hook
- Create token error page
- Build pre-evaluation page shell

**DEV3**:
- Complete AssessEventService
- Implement TokenService
- Create EvaluationService
- Write comprehensive service tests

### Phase 3: Integration & Testing (Day 5)

**Goal**: End-to-end flow working for event creation

**DEV1**:
- Polish Create Event page
- Test auto-save edge cases
- Mobile responsive testing

**DEV2**:
- Test token routing thoroughly
- Verify error handling
- Prepare for Week 2 integration

**DEV3**:
- Performance testing
- Load testing token generation
- Database optimization

## Integration Points

### DEV1 ↔ DEV3
- Event service API contract (DEV3 provides, DEV1 consumes)
- Auto-save debouncing logic (shared implementation)
- Event data validation rules (DEV3 enforces, DEV1 respects)

### DEV2 ↔ DEV3
- Token validation API (DEV3 provides, DEV2 consumes)
- Token error scenarios (jointly defined)
- Performance requirements for token lookup (shared responsibility)

### DEV1 ↔ DEV2
- Route configuration (DEV1 sets up, DEV2 extends)
- Mobile responsive patterns (DEV2 creates, DEV1 uses)
- Feature flag usage (shared pattern)

## Key Design Decisions

1. **100% Clean Implementation**: 
   - New evaluations schema, new tables, new routes (/assess/*)
   - Copy SHOT framework data only (no old evaluations)
   - Old system remains completely untouched
2. **Token-based access**: Reuse existing no-auth approach for pre-evaluations
3. **Auto-save everything**: Prevents data loss and improves user experience
4. **Feature flag controls navigation**: Switch to new system by changing nav link
5. **Mobile-first approach**: Ensures all interfaces work perfectly on phones

## Success Metrics

### Performance
- Event creation completes in < 2 seconds [DEV3]
- Auto-save triggers within 500ms of changes [DEV1]
- Token validation completes in < 100ms [DEV3]
- Page load time < 2 seconds on 4G mobile [DEV2]

### User Experience
- Create new event requires fewer than 5 fields [DEV1]
- All pages work on 320px wide screens [DEV2]
- Error messages are clear and actionable [All]
- No data loss during event creation [DEV1]

### Data Quality
- All events have required fields [DEV3]
- Token generation never fails [DEV3]
- Evaluation records created for all participants [DEV3]
- No duplicate evaluations per player/event [DEV3]

## Out of Scope (Week 1)

- SMS sending functionality (Week 2)
- Complete pre-evaluation form UI (Week 2)
- Coach evaluation interfaces (Week 2)
- Event publishing workflow (Week 2)
- Share links (Week 3)
- Data import (Week 3)

---

## Document Metadata

**Last Updated**: 2025-01-17
**Status**: Ready for Implementation
**Feature Owner**: Assess System Team
**Technical Lead**: DEV3
**Document Version**: 1.0