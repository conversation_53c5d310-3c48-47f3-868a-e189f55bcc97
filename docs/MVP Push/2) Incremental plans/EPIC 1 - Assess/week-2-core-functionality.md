# Assess System Week 2 - Core Functionality Implementation Plan - Developer Work Split

## Developer Assignments

### DEV1 - Coach Event & Evaluation System
**Focus**: Complete event lifecycle and coach evaluation interfaces
- Event detail and management pages
- Coach evaluation interface with auto-save
- Event state transitions (publish, session, complete)
- SMS trigger controls and status display

### DEV2 - Player/Parent Experience  
**Focus**: Complete pre-evaluation forms and player viewing
- Pre-evaluation form UI (5 questions per player)
- Mobile-optimized question flow
- Post-evaluation viewing for players
- Parent access via share links

### DEV3 - Backend/Pipeline/Data Import
**Focus**: SMS integration and evaluation processing
- SMS sending infrastructure
- Bulk pre-evaluation link generation
- Evaluation completion tracking
- Performance optimization for large teams

---

## Overview

Week 2 delivers the core functionality of the Assess system, building on Week 1's foundation. This week focuses on completing the event lifecycle management, implementing the full pre-evaluation experience for players, and enabling coaches to evaluate players efficiently. By the end of Week 2, coaches can publish events, players can complete pre-evaluations via SMS links, and coaches can evaluate all players post-event.

The SHOT perform framework is fully integrated, with each player receiving 4-5 evaluation questions based on their position and the week of the season.

## Architecture Overview

### Directory Structure

```
src/features/assess/
├── components/
│   ├── shared/
│   │   ├── EvaluationScore.tsx [DEV1]
│   │   ├── PlayerEvaluationCard.tsx [DEV1]
│   │   ├── SMSStatusIndicator.tsx [DEV1]
│   │   └── QuestionCard.tsx [DEV2]
│   ├── event-detail/
│   │   ├── EventHeader.tsx [DEV1]
│   │   ├── EventActions.tsx [DEV1]
│   │   ├── ParticipantsList.tsx [DEV1]
│   │   └── PreEvaluationStatus.tsx [DEV1]
│   ├── coach-evaluation/
│   │   ├── PlayerEvaluationForm.tsx [DEV1]
│   │   ├── EvaluationSlider.tsx [DEV1]
│   │   ├── BulkEvaluationView.tsx [DEV1]
│   │   └── AutoSaveIndicator.tsx [DEV1]
│   ├── pre-evaluation/
│   │   ├── PreEvaluationForm.tsx [DEV2]
│   │   ├── QuestionSlider.tsx [DEV2]
│   │   ├── ProgressIndicator.tsx [DEV2]
│   │   └── SubmitConfirmation.tsx [DEV2]
│   └── player-view/
│       ├── EvaluationResults.tsx [DEV2]
│       ├── CategoryBreakdown.tsx [DEV2]
│       └── ShareableResults.tsx [DEV2]
├── pages/
│   ├── EventDetailPage.tsx [DEV1]
│   ├── PublishEventPage.tsx [DEV1]
│   ├── CoachEvaluationPage.tsx [DEV1]
│   ├── PreEvaluationFormPage.tsx [DEV2]
│   └── ViewEvaluationPage.tsx [DEV2]
├── hooks/
│   ├── useEventState.ts [DEV1]
│   ├── useSMSStatus.ts [DEV1]
│   ├── useEvaluationProgress.ts [DEV2]
│   └── useBulkOperations.ts [DEV3]
├── services/
│   ├── SMSService.ts [DEV3]
│   ├── BulkEvaluationService.ts [DEV3]
│   └── EventStateService.ts [DEV3]
└── types/
    ├── sms.types.ts [DEV3]
    └── shot-framework.types.ts [DEV3]
```

## Page Flow and Purpose

### Complete Feature Flow Diagram

```mermaid
graph TB
    Start([Coach Login]) --> EventsList[Events List Page]
    
    EventsList --> |View Event| EventDetail[Event Detail Page - DEV1]
    EventDetail --> |Publish| PublishEvent[Publish Event Page - DEV1]
    PublishEvent --> |Send SMS| SMSSending[SMS Service - DEV3]
    
    EventDetail --> |Start Session| SessionMode[Session Mode - DEV1]
    SessionMode --> |Evaluate| CoachEval[Coach Evaluation Page - DEV1]
    
    PlayerAccess([Player SMS Link]) --> PreEvalForm[Pre-Evaluation Form - DEV2]
    PreEvalForm --> |Submit| ThankYou[Confirmation Page - DEV2]
    
    EventDetail --> |Complete Event| ViewResults[View Results - DEV2]
    ViewResults --> |Share Link| PlayerView[Player View Page - DEV2]
```

### Page Details

#### 1. Event Detail Page [DEV1]

**Purpose**: Central hub for managing a specific event through its lifecycle

**Path**: `/src/features/assess/pages/EventDetailPage.tsx`
**Route**: `/assess/events/:id`

```typescript
/**
 * EventDetailPage
 * 
 * PURPOSE:
 * - Display event information and current status
 * - Show participant list with pre-evaluation completion
 * - Provide actions based on event state
 * - Monitor SMS sending status
 * 
 * USER GOALS:
 * - See event status at a glance
 * - Track pre-evaluation completion
 * - Take appropriate next action
 * - Know if SMS messages were sent
 */
```

**New Components Required:**
```typescript
// Event header with status
import { EventHeader } from '@/features/assess/components/event-detail/EventHeader';
// Path: /src/features/assess/components/event-detail/EventHeader.tsx

// Action buttons based on state
import { EventActions } from '@/features/assess/components/event-detail/EventActions';
// Path: /src/features/assess/components/event-detail/EventActions.tsx

// Participant list with evaluation status
import { ParticipantsList } from '@/features/assess/components/event-detail/ParticipantsList';
// Path: /src/features/assess/components/event-detail/ParticipantsList.tsx

// SMS status indicator
import { SMSStatusIndicator } from '@/features/assess/components/shared/SMSStatusIndicator';
// Path: /src/features/assess/components/shared/SMSStatusIndicator.tsx
```

**Services Used:**
```typescript
import { AssessEventService } from '@/features/assess/services/AssessEventService';
// Methods: 
// - getEventById(id: string): Promise<Event>
// - getEventParticipants(id: string): Promise<Participant[]>

import { EvaluationService } from '@/features/assess/services/EvaluationService';
// Methods:
// - getEvaluationSummary(eventId: string): Promise<EvaluationSummary>
```

#### 2. Publish Event Page [DEV1]

**Purpose**: Confirm event details and trigger SMS notifications

**Path**: `/src/features/assess/pages/PublishEventPage.tsx`
**Route**: `/assess/events/:id/publish`

```typescript
/**
 * PublishEventPage
 * 
 * PURPOSE:
 * - Review event before publishing
 * - Select SMS sending preference
 * - Trigger evaluation creation
 * - Monitor SMS sending progress
 * 
 * USER GOALS:
 * - Ensure event details are correct
 * - Control when SMS messages are sent
 * - See confirmation of publishing
 */
```

**Services Used:**
```typescript
import { AssessEventService } from '@/features/assess/services/AssessEventService';
// Methods: 
// - publishEvent(id: string): Promise<Event>

import { EvaluationService } from '@/features/assess/services/EvaluationService';
// Methods:
// - createEventEvaluations(eventId: string): Promise<void>

import { SMSService } from '@/features/assess/services/SMSService';
// Methods:
// - sendPreEvaluationLinks(eventId: string): Promise<SMSBatchResult>
```

#### 3. Coach Evaluation Page [DEV1]

**Purpose**: Evaluate all players efficiently with auto-save

**Path**: `/src/features/assess/pages/CoachEvaluationPage.tsx`
**Route**: `/assess/events/:id/evaluate`

```typescript
/**
 * CoachEvaluationPage
 * 
 * PURPOSE:
 * - Evaluate multiple players quickly
 * - Auto-save every change
 * - Show evaluation progress
 * - Support bulk operations
 * 
 * USER GOALS:
 * - Evaluate all players in <15 minutes
 * - Never lose evaluation data
 * - See who's been evaluated
 * - Navigate between players easily
 */
```

**New Components Required:**
```typescript
// Individual player evaluation form
import { PlayerEvaluationForm } from '@/features/assess/components/coach-evaluation/PlayerEvaluationForm';
// Path: /src/features/assess/components/coach-evaluation/PlayerEvaluationForm.tsx

// Score slider component
import { EvaluationSlider } from '@/features/assess/components/coach-evaluation/EvaluationSlider';
// Path: /src/features/assess/components/coach-evaluation/EvaluationSlider.tsx

// Auto-save status
import { AutoSaveIndicator } from '@/features/assess/components/coach-evaluation/AutoSaveIndicator';
// Path: /src/features/assess/components/coach-evaluation/AutoSaveIndicator.tsx
```

**Hooks Used:**
```typescript
import { useAutoSave } from '@/features/assess/hooks/useAutoSave';
// Enhanced for multiple concurrent saves
```

#### 4. Pre-Evaluation Form Page [DEV2]

**Purpose**: Mobile-first form for players to complete pre-evaluations

**Path**: `/src/features/assess/pages/PreEvaluationFormPage.tsx`
**Route**: `/assess/evaluate/:token`

```typescript
/**
 * PreEvaluationFormPage
 * 
 * PURPOSE:
 * - Display 4-5 evaluation questions
 * - Mobile-optimized interface
 * - Quick completion (<2 minutes)
 * - No login required
 * 
 * USER GOALS:
 * - Complete evaluation quickly on phone
 * - Understand each question
 * - See progress through questions
 * - Get confirmation when done
 */
```

**New Components Required:**
```typescript
// Complete pre-evaluation form
import { PreEvaluationForm } from '@/features/assess/components/pre-evaluation/PreEvaluationForm';
// Path: /src/features/assess/components/pre-evaluation/PreEvaluationForm.tsx

// Individual question display
import { QuestionCard } from '@/features/assess/components/shared/QuestionCard';
// Path: /src/features/assess/components/shared/QuestionCard.tsx

// Score input slider
import { QuestionSlider } from '@/features/assess/components/pre-evaluation/QuestionSlider';
// Path: /src/features/assess/components/pre-evaluation/QuestionSlider.tsx

// Progress through questions
import { ProgressIndicator } from '@/features/assess/components/pre-evaluation/ProgressIndicator';
// Path: /src/features/assess/components/pre-evaluation/ProgressIndicator.tsx
```

**Services Used:**
```typescript
import { EvaluationService } from '@/features/assess/services/EvaluationService';
// Methods:
// - getPlayerPreEvaluations(eventId: string, playerId: string): Promise<EventEvaluation[]>
// - submitPreEvaluationScores(eventId: string, playerId: string, scores: EvaluationScore[]): Promise<void>
```

#### 5. View Evaluation Page [DEV2]

**Purpose**: Players/parents view evaluation results

**Path**: `/src/features/assess/pages/ViewEvaluationPage.tsx`
**Route**: `/assess/evaluations/:shareToken`

```typescript
/**
 * ViewEvaluationPage
 * 
 * PURPOSE:
 * - Display evaluation results clearly
 * - Show category breakdowns
 * - Compare pre/coach/post scores
 * - Mobile-friendly display
 * 
 * USER GOALS:
 * - See my evaluation scores
 * - Understand areas of strength
 * - Identify growth opportunities
 * - Share with parents if needed
 */
```

## Route Configuration

```typescript
// /src/features/assess/routes/AssessRoutes.tsx [DEV1]
export const AssessRoutes = () => {
  const { isAssessEnabled } = useFeatureFlag('REACT_APP_USE_ASSESS_EVENTS');
  
  if (!isAssessEnabled) return null;
  
  return (
    <Switch>
      {/* Coach routes - Week 1 */}
      <Route path="/assess/events" exact component={EventsListPage} />
      <Route path="/assess/events/create" component={CreateEventPage} />
      
      {/* Coach routes - Week 2 */}
      <Route path="/assess/events/:id" exact component={EventDetailPage} />
      <Route path="/assess/events/:id/publish" component={PublishEventPage} />
      <Route path="/assess/events/:id/evaluate" component={CoachEvaluationPage} />
      
      {/* Token-based routes - Week 2 */}
      <Route path="/assess/evaluate/:token" component={PreEvaluationFormPage} />
      <Route path="/assess/evaluations/:shareToken" component={ViewEvaluationPage} />
    </Switch>
  );
};
```

## Database Design

### SMS Integration Updates [DEV3]

```sql
-- Week 2: SMS tracking and batch operations

-- SMS batch tracking table
CREATE TABLE IF NOT EXISTS sms_batches (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id uuid NOT NULL REFERENCES events(id),
    batch_type text CHECK (batch_type IN ('pre_evaluation', 'reminder', 'results')),
    
    -- Batch status
    status text CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    total_count integer NOT NULL,
    sent_count integer DEFAULT 0,
    failed_count integer DEFAULT 0,
    
    -- Tracking
    started_at timestamp with time zone,
    completed_at timestamp with time zone,
    error_message text,
    
    created_at timestamp with time zone DEFAULT now()
);

-- Individual SMS tracking
CREATE TABLE IF NOT EXISTS sms_messages (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    batch_id uuid REFERENCES sms_batches(id),
    
    -- Recipient
    player_id uuid NOT NULL REFERENCES profiles(id),
    phone_number text NOT NULL,
    
    -- Message details
    message_type text CHECK (message_type IN ('pre_evaluation_link', 'reminder', 'results_link')),
    message_body text NOT NULL,
    
    -- Status
    status text CHECK (status IN ('pending', 'sent', 'failed')),
    sent_at timestamp with time zone,
    error_message text,
    
    -- Link tracking
    link_token uuid,
    link_clicked_at timestamp with time zone,
    
    created_at timestamp with time zone DEFAULT now()
);

-- Indexes for performance
CREATE INDEX idx_sms_batches_event ON sms_batches(event_id);
CREATE INDEX idx_sms_messages_batch ON sms_messages(batch_id);
CREATE INDEX idx_sms_messages_player ON sms_messages(player_id);
CREATE INDEX idx_sms_messages_token ON sms_messages(link_token);

-- Function to generate pre-evaluation links
CREATE OR REPLACE FUNCTION generate_pre_evaluation_links(p_event_id uuid)
RETURNS TABLE (
    player_id uuid,
    player_name text,
    phone_number text,
    evaluation_link text
)
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id as player_id,
        p.display_name as player_name,
        p.phone as phone_number,
        'https://shot.app/assess/evaluate/' || e.pre_evaluation_token || '/' || p.id as evaluation_link
    FROM event_participants ep
    JOIN profiles p ON p.id = ep.user_id
    JOIN events e ON e.id = ep.event_id
    WHERE ep.event_id = p_event_id
    AND ep.invitation_status IN ('pending', 'accepted', 'attended')
    AND p.phone IS NOT NULL;
END;
$$ LANGUAGE plpgsql;
```

## Service Architecture

### SMSService [DEV3]

```typescript
// /src/features/assess/services/SMSService.ts

export interface SMSService {
  // Batch operations
  sendPreEvaluationLinks(eventId: string): Promise<SMSBatchResult>;
  sendReminders(eventId: string): Promise<SMSBatchResult>;
  sendResultsLinks(eventId: string): Promise<SMSBatchResult>;
  
  // Status tracking
  getBatchStatus(batchId: string): Promise<SMSBatchStatus>;
  getEventSMSHistory(eventId: string): Promise<SMSBatch[]>;
  
  // Individual operations
  resendFailedMessages(batchId: string): Promise<SMSBatchResult>;
  trackLinkClick(token: string): Promise<void>;
}

export interface SMSBatchResult {
  batchId: string;
  totalCount: number;
  sentCount: number;
  failedCount: number;
  status: 'processing' | 'completed' | 'failed';
}
```

### EventStateService [DEV3]

```typescript
// /src/features/assess/services/EventStateService.ts

export interface EventStateService {
  // State transitions
  publishEvent(eventId: string, autoSendSMS: boolean): Promise<Event>;
  startEventSession(eventId: string): Promise<Event>;
  completeEvent(eventId: string): Promise<Event>;
  
  // State validation
  canPublish(event: Event): boolean;
  canStartSession(event: Event): boolean;
  canComplete(event: Event): boolean;
  
  // Bulk state operations
  publishMultipleEvents(eventIds: string[]): Promise<Event[]>;
}
```

### BulkEvaluationService [DEV3]

```typescript
// /src/features/assess/services/BulkEvaluationService.ts

export interface BulkEvaluationService {
  // Efficient data loading
  loadAllEvaluationsForEvent(eventId: string): Promise<EvaluationMatrix>;
  
  // Bulk updates with conflict resolution
  saveBulkEvaluations(updates: BulkEvaluationUpdate[]): Promise<BulkSaveResult>;
  
  // Progress tracking
  getEvaluationProgress(eventId: string): Promise<EvaluationProgress>;
  
  // Export capabilities (Week 3)
  prepareExportData(eventId: string): Promise<ExportData>;
}
```

## Implementation Timeline

### Phase 1: Event Management & SMS (Days 1-2)

**Goal**: Complete event lifecycle and SMS infrastructure

**DEV1**: 
- Build Event Detail page
- Implement Publish Event page
- Create event state transitions
- Add SMS control UI

**DEV2**:
- Design pre-evaluation UI components
- Create mobile-responsive layouts
- Build progress indicators
- Prepare question display components

**DEV3**:
- Implement SMS service
- Create batch sending logic
- Set up link generation
- Build tracking infrastructure

### Phase 2: Evaluation Forms (Days 3-4)

**Goal**: Complete pre-evaluation and coach evaluation interfaces

**DEV1**:
- Build Coach Evaluation page
- Implement auto-save for evaluations
- Create player navigation
- Add bulk evaluation view

**DEV2**:
- Complete Pre-evaluation form
- Implement question flow
- Add form validation
- Create submission confirmation

**DEV3**:
- Optimize evaluation queries
- Implement caching strategy
- Create progress tracking
- Performance test with 50+ players

### Phase 3: Integration & Polish (Day 5)

**Goal**: Full end-to-end flow working smoothly

**DEV1**:
- Test complete coach workflow
- Polish UI interactions
- Fix integration issues
- Performance optimization

**DEV2**:
- Test on multiple devices
- Optimize for slow connections
- Polish animations
- Accessibility improvements

**DEV3**:
- Load testing
- SMS delivery monitoring
- Database optimization
- Error handling improvements

## Integration Points

### DEV1 ↔ DEV3
- Event state transitions (DEV3 provides, DEV1 triggers)
- SMS sending control (DEV1 initiates, DEV3 executes)
- Evaluation progress tracking (DEV3 calculates, DEV1 displays)

### DEV2 ↔ DEV3
- Pre-evaluation data flow (DEV2 collects, DEV3 processes)
- Token validation and security (DEV3 validates, DEV2 handles errors)
- Performance requirements for mobile (shared optimization)

### DEV1 ↔ DEV2
- Shared evaluation components (jointly designed)
- Consistent scoring UI (1-10 sliders)
- Mobile/desktop responsive patterns (DEV2 leads, DEV1 follows)

## Key Design Decisions

1. **SHOT Framework Integration**: Each player receives 4-5 questions based on position and week
2. **Token-based access**: Pre-evaluations use event token + player ID for security
3. **Auto-save everything**: Both coach and player interfaces save on every change
4. **SMS batching**: Send messages in batches with retry logic for failures
5. **Mobile-first forms**: All player interfaces optimized for phone screens

## Success Metrics

### Performance
- Publish event with SMS to 30 players < 5 seconds [DEV3]
- Coach can evaluate one player in < 30 seconds [DEV1]
- Pre-evaluation form loads in < 1 second on 4G [DEV2]
- Auto-save completes in < 500ms [DEV1/DEV3]

### User Experience
- 90% of players complete pre-evaluations [DEV2]
- Coach can evaluate 30 players in < 15 minutes [DEV1]
- Zero data loss from auto-save [All]
- SMS delivery rate > 95% [DEV3]

### Data Quality
- All evaluations have valid scores (1-10) [DEV3]
- No duplicate evaluation submissions [DEV3]
- Complete audit trail for all changes [DEV3]
- Token security prevents unauthorized access [DEV2/DEV3]

## Out of Scope (Week 2)

- Share links for parents (Week 3)
- CSV import functionality (Week 3)
- Advanced filtering/sorting (Week 3)
- Email notifications (Future)
- Recurring events (Future)
- Custom evaluation templates (Future)

---

## Document Metadata

**Last Updated**: 2025-01-17
**Status**: Ready for Implementation
**Feature Owner**: Assess System Team
**Technical Lead**: DEV3
**Document Version**: 1.0