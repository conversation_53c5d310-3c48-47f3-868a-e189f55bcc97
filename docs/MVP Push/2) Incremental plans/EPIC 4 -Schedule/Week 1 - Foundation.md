# Schedule System Week 1: Foundation Implementation Plan - Developer Work Split

## Developer Assignments

### DEV1 - Event Management Foundation
**Focus**: Core event data model and basic CRUD operations
- Event database schema setup and migrations
- Basic EventService implementation (create, read, update)
- EventForm component with validation
- Location management system
- Event type configuration

### DEV2 - Calendar Display Foundation
**Focus**: Basic calendar grid and navigation
- Calendar grid component architecture
- Month view implementation
- Calendar navigation controls
- Basic event rendering on calendar
- ScheduleHome page with role detection

### DEV3 - RSVP Data Foundation
**Focus**: RSVP data structures and basic operations
- RSVP database schema design
- Basic RSVPService setup
- Simple RSVP form component
- Event participant relationships
- RSVP status tracking model

### DEV4 - Family Structure Foundation
**Focus**: Family data model and basic views
- Family relationship data structures
- Family member selection component
- Basic family calendar layout
- Multi-child data organization
- Family service foundation

---

## Overview

Week 1 focuses on establishing the foundational data structures, services, and core components that all other features will build upon. Each developer will create the essential infrastructure for their domain while ensuring clean interfaces for future integration.

The goal is to have a working skeleton where events can be created and displayed on a basic calendar, with the data structures in place for RSVP and family features to be built in subsequent weeks.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Page Flow and Purpose](#page-flow-and-purpose)
3. [Component Structure](#component-structure)
4. [Database Design](#database-design)
5. [Service Architecture](#service-architecture)
6. [Implementation Timeline](#implementation-timeline)
7. [Integration Points](#integration-points)
8. [Success Metrics](#success-metrics)

## Architecture Overview

### Directory Structure

```
src/features/schedule/
├── components/
│   ├── shared/
│   │   ├── EventCard.tsx [DEV1] - Basic event display card
│   │   ├── DateTimePicker.tsx [DEV1] - Date/time selection
│   │   └── LoadingStates.tsx [DEV2] - Shared loading components
│   ├── events/
│   │   ├── EventForm.tsx [DEV1] - Event creation form
│   │   ├── EventTypeSelector.tsx [DEV1] - Event type dropdown
│   │   └── LocationInput.tsx [DEV1] - Location selection
│   ├── calendar/
│   │   ├── CalendarGrid.tsx [DEV2] - Month grid structure
│   │   ├── CalendarHeader.tsx [DEV2] - Navigation controls
│   │   └── CalendarDay.tsx [DEV2] - Individual day cell
│   ├── rsvp/
│   │   └── RSVPForm.tsx [DEV3] - Basic RSVP form
│   └── family/
│       └── FamilyMemberList.tsx [DEV4] - Family member display
├── pages/
│   ├── ScheduleHome.tsx [DEV2] - Main entry point
│   └── CreateEvent.tsx [DEV1] - Event creation page
├── hooks/
│   ├── useEvents.ts [DEV1] - Event data hooks
│   └── useCalendar.ts [DEV2] - Calendar state hooks
├── services/
│   ├── EventService.ts [DEV1] - Event operations
│   ├── CalendarService.ts [DEV2] - Calendar utilities
│   ├── RSVPService.ts [DEV3] - RSVP operations
│   └── FamilyService.ts [DEV4] - Family data access
├── routes/
│   └── ScheduleRoutes.tsx [DEV2] - Route configuration
└── types/
    ├── event.types.ts [DEV1] - Event type definitions
    ├── calendar.types.ts [DEV2] - Calendar types
    ├── rsvp.types.ts [DEV3] - RSVP types
    └── family.types.ts [DEV4] - Family types
```

## Page Flow and Purpose

### Complete Feature Flow Diagram (Week 1 Focus)

```mermaid
graph TB
    Start([Schedule Entry]) --> ScheduleHome[Schedule Home - DEV2]
    
    %% Week 1 Focus Areas
    ScheduleHome --> |Coach: Create| CreateEvent[Create Event - DEV1]
    CreateEvent --> |Save| EventSaved[Event Created - DEV1]
    
    ScheduleHome --> |View| BasicCalendar[Basic Calendar - DEV2]
    BasicCalendar --> |Display| ShowEvents[Show Events - DEV2]
    
    %% Data Foundation (Not UI visible yet)
    EventSaved -.-> RSVPStructure[RSVP Data Ready - DEV3]
    EventSaved -.-> FamilyStructure[Family Data Ready - DEV4]
    
    style RSVPStructure stroke-dasharray: 5 5
    style FamilyStructure stroke-dasharray: 5 5
```

### Page Details

#### 1. Schedule Home [DEV2]

**Purpose**: Entry point with basic navigation

**Path**: `/src/features/schedule/pages/ScheduleHome.tsx`
**Route**: `/schedule`

```typescript
/**
 * ScheduleHome
 * 
 * PURPOSE:
 * - Detect user role and show appropriate options
 * - Link to event creation for coaches
 * - Show basic calendar view
 * 
 * WEEK 1 GOALS:
 * - Role detection working
 * - Navigation to create event
 * - Basic layout established
 */
```

**Week 1 Implementation**:
- Basic page layout with navigation
- Role detection from context
- Links to CreateEvent (coaches only)
- Placeholder for upcoming events
- Basic loading states

#### 2. Create Event [DEV1]

**Purpose**: Basic event creation form

**Path**: `/src/features/schedule/pages/CreateEvent.tsx`
**Route**: `/schedule/events/new`

```typescript
/**
 * CreateEvent
 * 
 * PURPOSE:
 * - Create new events with required fields
 * - Save to database
 * - Basic validation
 * 
 * WEEK 1 GOALS:
 * - Form with title, date, time, location
 * - Save functionality
 * - Success/error feedback
 */
```

**Week 1 Implementation**:
- EventForm component with basic fields
- Form validation
- Save to database via EventService
- Success message and redirect
- Error handling

## Database Design

### Week 1 Schema Implementation [DEV1, DEV3, DEV4]

```sql
-- WEEK 1: Core events table (simplified) [DEV1]
CREATE TABLE public.events (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id uuid NOT NULL REFERENCES public.teams(id),
    event_type varchar(50) NOT NULL DEFAULT 'training',
    title varchar(200) NOT NULL,
    description text,
    
    -- Basic temporal fields
    start_datetime timestamp with time zone NOT NULL,
    end_datetime timestamp with time zone NOT NULL,
    
    -- Simple location (no location table yet)
    location_name varchar(200),
    location_address text,
    
    -- Basic metadata
    created_by uuid NOT NULL REFERENCES auth.users(id),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- WEEK 1: Basic event participants structure [DEV3]
CREATE TABLE public.event_participants (
    event_id uuid NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
    player_id uuid NOT NULL REFERENCES public.profiles(id),
    
    -- Simple RSVP fields to start
    rsvp_status varchar(20) DEFAULT 'pending',
    rsvp_timestamp timestamp with time zone,
    
    created_at timestamp with time zone DEFAULT now(),
    
    PRIMARY KEY (event_id, player_id)
);

-- WEEK 1: Family relationships (if not existing) [DEV4]
CREATE TABLE IF NOT EXISTS public.family_members (
    family_id uuid NOT NULL,
    user_id uuid NOT NULL REFERENCES auth.users(id),
    player_id uuid REFERENCES public.profiles(id),
    relationship varchar(50), -- 'parent', 'player'
    
    PRIMARY KEY (family_id, user_id)
);

-- Basic indexes for Week 1
CREATE INDEX idx_events_team_start ON public.events(team_id, start_datetime);
CREATE INDEX idx_event_participants_event ON public.event_participants(event_id);
```

## Service Architecture

### EventService (Week 1) [DEV1]

```typescript
// /src/features/schedule/services/EventService.ts

export interface IEventService {
  // Week 1: Basic CRUD only
  createEvent(eventData: CreateEventDTO): Promise<Event>;
  getEvent(eventId: string): Promise<Event | null>;
  getTeamEvents(teamId: string): Promise<Event[]>;
  updateEvent(eventId: string, updates: UpdateEventDTO): Promise<Event>;
}

export class EventService implements IEventService {
  async createEvent(eventData: CreateEventDTO): Promise<Event> {
    // Week 1: Simple creation with validation
    const { data, error } = await supabase
      .from('events')
      .insert({
        team_id: eventData.teamId,
        title: eventData.title,
        event_type: eventData.eventType,
        start_datetime: eventData.startDateTime,
        end_datetime: eventData.endDateTime,
        location_name: eventData.locationName,
        location_address: eventData.locationAddress,
        created_by: getCurrentUserId()
      })
      .select()
      .single();
      
    if (error) throw error;
    return data;
  }
  
  // Other basic methods...
}
```

### CalendarService (Week 1) [DEV2]

```typescript
// /src/features/schedule/services/CalendarService.ts

export interface ICalendarService {
  // Week 1: Basic calendar data only
  getMonthEvents(teamId: string, month: Date): Promise<CalendarEvent[]>;
  getCalendarDays(year: number, month: number): CalendarDay[];
}

export class CalendarService implements ICalendarService {
  async getMonthEvents(teamId: string, month: Date): Promise<CalendarEvent[]> {
    // Week 1: Fetch events for the month
    const startOfMonth = new Date(month.getFullYear(), month.getMonth(), 1);
    const endOfMonth = new Date(month.getFullYear(), month.getMonth() + 1, 0);
    
    const events = await eventService.getTeamEvents(teamId);
    
    // Filter to month and format for calendar
    return events
      .filter(e => e.start_datetime >= startOfMonth && e.start_datetime <= endOfMonth)
      .map(e => this.formatForCalendar(e));
  }
  
  getCalendarDays(year: number, month: number): CalendarDay[] {
    // Generate calendar grid structure
    // Week 1: Basic 6x7 grid generation
  }
}
```

### RSVPService (Week 1) [DEV3]

```typescript
// /src/features/schedule/services/RSVPService.ts

export interface IRSVPService {
  // Week 1: Data structure setup only
  initializeEventParticipants(eventId: string, playerIds: string[]): Promise<void>;
  getEventParticipants(eventId: string): Promise<Participant[]>;
}

export class RSVPService implements IRSVPService {
  async initializeEventParticipants(eventId: string, playerIds: string[]): Promise<void> {
    // Week 1: Create participant records when event is created
    const participants = playerIds.map(playerId => ({
      event_id: eventId,
      player_id: playerId,
      rsvp_status: 'pending'
    }));
    
    const { error } = await supabase
      .from('event_participants')
      .insert(participants);
      
    if (error) throw error;
  }
}
```

### FamilyService (Week 1) [DEV4]

```typescript
// /src/features/schedule/services/FamilyService.ts

export interface IFamilyService {
  // Week 1: Basic family data access
  getFamilyMembers(userId: string): Promise<FamilyMember[]>;
  getFamilyPlayers(userId: string): Promise<Player[]>;
}

export class FamilyService implements IFamilyService {
  async getFamilyMembers(userId: string): Promise<FamilyMember[]> {
    // Week 1: Get all family members for a user
    const { data, error } = await supabase
      .from('family_members')
      .select('*')
      .or(`user_id.eq.${userId},family_id.in.(
        select family_id from family_members where user_id = ${userId}
      )`);
      
    if (error) throw error;
    return data;
  }
}
```

## Implementation Timeline

### Day 1-2: Database and Service Foundation

**DEV1 (Event Management)**:
- Create events table migration
- Implement EventService with createEvent method
- Set up event.types.ts with interfaces
- Basic error handling

**DEV2 (Calendar System)**:
- Set up calendar.types.ts
- Create CalendarService skeleton
- Implement getCalendarDays utility
- Create basic route structure

**DEV3 (RSVP & Attendance)**:
- Create event_participants table
- Set up RSVPService skeleton
- Define RSVP type interfaces
- Create participant initialization logic

**DEV4 (Family Experience)**:
- Verify/create family_members table
- Implement FamilyService basics
- Define family type interfaces
- Create getFamilyMembers query

### Day 3-4: Core Components

**DEV1 (Event Management)**:
- Build EventForm component
- Create DateTimePicker component
- Implement form validation
- Add EventTypeSelector

**DEV2 (Calendar System)**:
- Create CalendarGrid component
- Build CalendarHeader with navigation
- Implement CalendarDay cell
- Add basic event display

**DEV3 (RSVP & Attendance)**:
- Create basic RSVPForm component
- Set up participant data structure
- Define RSVP status enum
- Create placeholder components

**DEV4 (Family Experience)**:
- Build FamilyMemberList component
- Create family member selector
- Set up family view structure
- Define family calendar layout

### Day 5: Integration and Pages

**DEV1 (Event Management)**:
- Complete CreateEvent page
- Integrate EventForm with EventService
- Add success/error handling
- Test event creation flow

**DEV2 (Calendar System)**:
- Complete ScheduleHome page
- Integrate calendar with event data
- Add role-based navigation
- Test calendar display

**DEV3 (RSVP & Attendance)**:
- Connect RSVP data to events
- Verify participant creation
- Set up RSVP hooks
- Prepare for Week 2 integration

**DEV4 (Family Experience)**:
- Connect family data to UI
- Test family member display
- Prepare family calendar structure
- Set up for Week 2 features

## Integration Points

### DEV1 ↔ DEV2
- Event data format for calendar display (DEV1 provides interface)
- Calendar page links to event creation (DEV2 adds navigation)
- Shared loading states and error handling patterns

### DEV1 ↔ DEV3
- Event creation triggers participant initialization (DEV1 calls DEV3 service)
- Shared event ID references in database
- Participant count validation

### DEV1 ↔ DEV4
- Event team membership affects family visibility (shared queries)
- Creator ID links to family relationships

### DEV2 ↔ DEV3
- Calendar will show RSVP counts (prepare data structure)
- Shared event display components

### DEV2 ↔ DEV4
- Calendar filtering by family member (prepare structure)
- Shared calendar view components

### DEV3 ↔ DEV4
- Family members are potential participants
- Shared player data references

## Key Design Decisions

1. **Simple First**: Start with basic fields, add complexity in later weeks
2. **Separate Concerns**: Each developer owns their schema and service
3. **Prepare for Integration**: Design interfaces thinking ahead
4. **Mobile-Ready**: Even basic components consider mobile from start
5. **Type Safety**: Full TypeScript interfaces from day one

## Success Metrics

### Performance
- Event creation < 3 seconds [DEV1]
- Calendar month render < 2 seconds [DEV2]
- Service initialization < 1 second [All]

### Functionality
- Events can be created and saved [DEV1]
- Calendar displays current month [DEV2]
- Participant records created [DEV3]
- Family members retrieved [DEV4]

### Code Quality
- All TypeScript interfaces defined
- Services follow consistent patterns
- Components have loading states
- Error handling implemented

### Integration
- Services can be called from components
- Data flows between layers correctly
- No blocking dependencies between developers

## Week 1 Deliverables

### DEV1 Deliverables
- [ ] Events table created and migrated
- [ ] EventService with create/read operations
- [ ] EventForm component functional
- [ ] CreateEvent page working end-to-end
- [ ] Basic validation and error handling

### DEV2 Deliverables
- [ ] CalendarGrid rendering month view
- [ ] Navigation between months working
- [ ] ScheduleHome page with role detection
- [ ] Basic event display on calendar
- [ ] Route configuration complete

### DEV3 Deliverables
- [ ] Event participants table created
- [ ] RSVPService initialization working
- [ ] Basic RSVP data structures defined
- [ ] Participant creation on new events
- [ ] RSVP types and interfaces complete

### DEV4 Deliverables
- [ ] Family data structure verified/created
- [ ] FamilyService retrieving members
- [ ] Family member list component
- [ ] Family types defined
- [ ] Basic family view layout ready

## Risk Mitigation

### Technical Risks
- **Database migrations**: Test in development first
- **Service dependencies**: Use interfaces to decouple
- **Integration issues**: Daily sync meetings
- **Performance**: Monitor query execution

### Process Risks
- **Blocking dependencies**: Each dev has independent work
- **Scope creep**: Stick to Week 1 goals only
- **Integration delays**: Plan integration points early

## Out of Scope (Week 1)

- Event editing/deletion
- Complex calendar views (week, list)
- RSVP submission flow
- Attendance tracking
- Notifications
- Calendar filtering
- Multi-child features
- Event templates
- Recurring events

## Testing Requirements

### Unit Tests (Week 1 Focus)
- Service method tests
- Date utility functions
- Type validation
- Component rendering

### Integration Tests
- Database operations
- Service-to-database flow
- Basic user workflows

## Next Week Preview

Week 2 will build upon these foundations to add:
- Full RSVP submission flow
- Multiple calendar views
- Event templates
- Family bulk operations
- Advanced event creation features

---

## Document Metadata

**Last Updated**: 2024-03-10
**Status**: Ready for Implementation
**Week**: 1 of 4
**Feature**: Schedule System
**Focus**: Foundation