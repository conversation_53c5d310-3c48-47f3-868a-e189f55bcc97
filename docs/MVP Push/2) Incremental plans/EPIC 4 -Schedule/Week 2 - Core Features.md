# Schedule System Week 2: Core Features Implementation Plan - Developer Work Split

## Developer Assignments

### DEV1 - Event Management Core Features
**Focus**: Event templates, bulk operations, and conflict detection
- Event template system implementation
- Bulk event creation functionality
- Quick-create workflows
- Event conflict detection
- Notification trigger setup

### DEV2 - Calendar Views Expansion
**Focus**: Multiple view types and advanced features
- Week view implementation
- List view development
- Advanced filtering system
- Search functionality
- View state persistence

### DEV3 - RSVP Flow Implementation
**Focus**: Complete RSVP submission and management
- Full RSVP submission workflow
- Parent approval system
- RSVP reminder automation
- Waitlist functionality
- RSVP change handling

### DEV4 - Family Feature Development
**Focus**: Multi-child management and family tools
- Bulk RSVP for multiple children
- Family conflict detection
- Event details family view
- Transportation coordination basics
- Family notification preferences

---

## Overview

Week 2 builds upon the foundation established in Week 1 to deliver the core functionality users expect from a schedule system. This week focuses on making the system truly useful with features like multiple calendar views, complete RSVP workflows, and family-friendly bulk operations.

Each developer will expand their domain significantly while maintaining clean integration points. The goal is to have a fully functional scheduling system where coaches can efficiently create events, families can manage multiple children's schedules, and everyone can RSVP seamlessly.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Page Flow and Purpose](#page-flow-and-purpose)
3. [Component Structure](#component-structure)
4. [Database Design](#database-design)
5. [Service Architecture](#service-architecture)
6. [Implementation Timeline](#implementation-timeline)
7. [Integration Points](#integration-points)
8. [Success Metrics](#success-metrics)

## Architecture Overview

### Directory Structure (Week 2 Additions)

```
src/features/schedule/
├── components/
│   ├── shared/
│   │   ├── EventQuickActions.tsx [DEV1] - Quick event operations
│   │   ├── FilterBar.tsx [DEV2] - Shared filter component
│   │   ├── ConflictAlert.tsx [DEV1/DEV4] - Conflict warnings
│   │   └── BulkActionBar.tsx [DEV4] - Bulk operation controls
│   ├── events/
│   │   ├── EventTemplateSelector.tsx [DEV1] - Template picker
│   │   ├── BulkEventCreator.tsx [DEV1] - Multiple event creation
│   │   ├── ConflictChecker.tsx [DEV1] - Conflict detection UI
│   │   └── QuickCreateModal.tsx [DEV1] - Fast event creation
│   ├── calendar/
│   │   ├── WeekView.tsx [DEV2] - Weekly calendar view
│   │   ├── ListView.tsx [DEV2] - List of events
│   │   ├── EventFilters.tsx [DEV2] - Filter controls
│   │   ├── CalendarSearch.tsx [DEV2] - Search interface
│   │   └── ViewSelector.tsx [DEV2] - View toggle
│   ├── rsvp/
│   │   ├── RSVPSubmission.tsx [DEV3] - Complete RSVP flow
│   │   ├── ParentApprovalFlow.tsx [DEV3] - Parent consent
│   │   ├── RSVPStatus.tsx [DEV3] - Status display
│   │   ├── WaitlistManager.tsx [DEV3] - Waitlist UI
│   │   └── RSVPReminders.tsx [DEV3] - Reminder config
│   └── family/
│       ├── MultiChildRSVP.tsx [DEV4] - Bulk RSVP interface
│       ├── FamilyConflictView.tsx [DEV4] - Conflict display
│       ├── EventDetailsFamily.tsx [DEV4] - Family event view
│       └── TransportCoordinator.tsx [DEV4] - Ride sharing
├── pages/
│   ├── EventTemplates.tsx [DEV1] - Template management
│   ├── CalendarView.tsx [DEV2] - Full calendar page
│   ├── RSVPManagement.tsx [DEV3] - RSVP dashboard
│   └── FamilyDashboard.tsx [DEV4] - Family overview
├── hooks/
│   ├── useEventTemplates.ts [DEV1] - Template hooks
│   ├── useCalendarFilters.ts [DEV2] - Filter state
│   ├── useRSVPFlow.ts [DEV3] - RSVP logic
│   └── useFamilyEvents.ts [DEV4] - Family data
└── utils/
    ├── conflictDetection.ts [DEV1] - Conflict logic
    ├── calendarCalculations.ts [DEV2] - View helpers
    ├── rsvpValidation.ts [DEV3] - RSVP rules
    └── familyGrouping.ts [DEV4] - Family utils
```

## Page Flow and Purpose

### Complete Feature Flow Diagram (Week 2 Focus)

```mermaid
graph TB
    Start([Schedule Home]) --> CalendarView[Calendar View - DEV2]
    
    %% Week 2: Multiple Views
    CalendarView --> ViewSelector{Select View - DEV2}
    ViewSelector --> MonthView[Month View - DEV2]
    ViewSelector --> WeekView[Week View - DEV2]
    ViewSelector --> ListView[List View - DEV2]
    
    %% Week 2: Templates and Quick Create
    CalendarView --> |Coach| QuickCreate[Quick Create - DEV1]
    QuickCreate --> TemplateSelect[Select Template - DEV1]
    TemplateSelect --> CreateFromTemplate[Create Event - DEV1]
    
    CalendarView --> |Coach| BulkCreate[Bulk Create - DEV1]
    BulkCreate --> MultiEventForm[Multiple Events - DEV1]
    
    %% Week 2: RSVP Flow
    MonthView --> EventClick[Click Event]
    EventClick --> RSVPFlow[RSVP Flow - DEV3]
    RSVPFlow --> ParentCheck{Needs Parent? - DEV3}
    ParentCheck --> |Yes| ParentApproval[Parent Approval - DEV3]
    ParentCheck --> |No| SubmitRSVP[Submit RSVP - DEV3]
    ParentApproval --> SubmitRSVP
    
    %% Week 2: Family Features
    CalendarView --> |Family| FamilyDash[Family Dashboard - DEV4]
    FamilyDash --> MultiChild[Multi-Child View - DEV4]
    MultiChild --> BulkRSVP[Bulk RSVP - DEV4]
    FamilyDash --> Conflicts[View Conflicts - DEV4]
    
    %% Waitlist
    SubmitRSVP --> |Full| Waitlist[Join Waitlist - DEV3]
```

### Page Details

#### 1. Calendar View (Enhanced) [DEV2]

**Purpose**: Full-featured calendar with multiple view options

**Path**: `/src/features/schedule/pages/CalendarView.tsx`
**Route**: `/schedule/calendar`

```typescript
/**
 * CalendarView
 * 
 * PURPOSE:
 * - Display events in month, week, or list format
 * - Enable filtering by various criteria
 * - Support search functionality
 * - Persist user view preferences
 * 
 * WEEK 2 GOALS:
 * - Three working view types
 * - Filter implementation
 * - Search working
 * - View state saved
 */
```

**Week 2 Implementation**:
- ViewSelector component for switching views
- WeekView with hourly slots
- ListView with grouping by date
- EventFilters with multiple criteria
- Search with instant results
- localStorage for view persistence

**New Components Required**:
```typescript
// Week view implementation
import { WeekView } from '@/src/features/schedule/components/calendar/WeekView';

// List view implementation  
import { ListView } from '@/src/features/schedule/components/calendar/ListView';

// Filter controls
import { EventFilters } from '@/src/features/schedule/components/calendar/EventFilters';

// Search interface
import { CalendarSearch } from '@/src/features/schedule/components/calendar/CalendarSearch';
```

#### 2. Event Templates [DEV1]

**Purpose**: Manage and use event templates for quick creation

**Path**: `/src/features/schedule/pages/EventTemplates.tsx`
**Route**: `/schedule/templates`

```typescript
/**
 * EventTemplates
 * 
 * PURPOSE:
 * - View and manage event templates
 * - Create new templates from existing events
 * - Edit template defaults
 * - Track template usage
 * 
 * WEEK 2 GOALS:
 * - Template CRUD operations
 * - Quick create from template
 * - Usage statistics
 * - Template sharing (team level)
 */
```

**Week 2 Implementation**:
- Template list with preview
- Create template from event
- Edit template modal
- Quick create workflow
- Usage tracking

#### 3. RSVP Management [DEV3]

**Purpose**: Central hub for managing RSVPs

**Path**: `/src/features/schedule/pages/RSVPManagement.tsx`
**Route**: `/schedule/rsvp`

```typescript
/**
 * RSVPManagement
 * 
 * PURPOSE:
 * - Submit and update RSVPs
 * - Handle parent approvals
 * - View RSVP status
 * - Manage waitlists
 * 
 * WEEK 2 GOALS:
 * - Complete RSVP submission
 * - Parent approval flow
 * - Status updates
 * - Waitlist functionality
 */
```

**Week 2 Implementation**:
- RSVP form with all options
- Parent approval workflow
- Real-time status updates
- Waitlist position tracking
- RSVP history

#### 4. Family Dashboard [DEV4]

**Purpose**: Family-centric schedule management

**Path**: `/src/features/schedule/pages/FamilyDashboard.tsx`
**Route**: `/schedule/family`

```typescript
/**
 * FamilyDashboard
 * 
 * PURPOSE:
 * - View all family members' events
 * - Bulk RSVP operations
 * - Conflict detection
 * - Transportation coordination
 * 
 * WEEK 2 GOALS:
 * - Multi-child calendar
 * - Bulk RSVP functional
 * - Conflict alerts
 * - Basic transport features
 */
```

**Week 2 Implementation**:
- Family member tabs/filters
- Bulk RSVP interface
- Conflict visualization
- Transport offer/request
- Family preferences

## Database Design

### Week 2 Schema Additions

```sql
-- Event Templates [DEV1]
CREATE TABLE public.event_templates (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id uuid NOT NULL REFERENCES public.teams(id),
    name varchar(100) NOT NULL,
    event_type varchar(50) NOT NULL,
    
    -- Template defaults
    default_title varchar(200),
    default_description text,
    default_duration interval DEFAULT '90 minutes',
    default_location_name varchar(200),
    default_location_address text,
    
    -- RSVP defaults
    rsvp_required boolean DEFAULT true,
    rsvp_hours_before integer DEFAULT 24,
    max_participants integer,
    
    -- Usage tracking
    usage_count integer DEFAULT 0,
    last_used timestamp with time zone,
    created_by uuid REFERENCES auth.users(id),
    
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Enhanced event participants for Week 2 [DEV3]
ALTER TABLE public.event_participants ADD COLUMN IF NOT EXISTS
    rsvp_by uuid REFERENCES auth.users(id),
    rsvp_note text,
    requires_parent_approval boolean DEFAULT false,
    parent_approved boolean,
    parent_approved_by uuid REFERENCES auth.users(id),
    parent_approved_at timestamp with time zone,
    waitlist_position integer,
    waitlist_timestamp timestamp with time zone;

-- Calendar view preferences [DEV2]
CREATE TABLE public.calendar_preferences (
    user_id uuid PRIMARY KEY REFERENCES auth.users(id),
    default_view varchar(20) DEFAULT 'month',
    default_filters jsonb DEFAULT '{}',
    saved_searches jsonb DEFAULT '[]',
    color_coding varchar(20) DEFAULT 'event_type',
    
    updated_at timestamp with time zone DEFAULT now()
);

-- Family RSVP tracking [DEV4]
CREATE TABLE public.family_rsvp_batches (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    family_id uuid NOT NULL,
    submitted_by uuid NOT NULL REFERENCES auth.users(id),
    
    -- Batch details
    event_ids uuid[] NOT NULL,
    player_ids uuid[] NOT NULL,
    responses jsonb NOT NULL, -- {eventId: {playerId: response}}
    
    created_at timestamp with time zone DEFAULT now()
);

-- Basic transportation for Week 2 [DEV4]
CREATE TABLE public.event_transportation (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id uuid NOT NULL REFERENCES public.events(id),
    
    -- Offer details
    driver_id uuid NOT NULL REFERENCES auth.users(id),
    seats_available integer NOT NULL,
    departure_location text,
    notes text,
    
    created_at timestamp with time zone DEFAULT now()
);
```

## Service Architecture

### EventService (Week 2 Enhancements) [DEV1]

```typescript
// Week 2 additions to EventService

export interface IEventService {
  // Existing methods from Week 1...
  
  // Template operations
  getEventTemplates(teamId: string): Promise<EventTemplate[]>;
  createEventFromTemplate(templateId: string, overrides: Partial<CreateEventDTO>): Promise<Event>;
  saveAsTemplate(eventId: string, templateName: string): Promise<EventTemplate>;
  updateTemplate(templateId: string, updates: UpdateTemplateDTO): Promise<EventTemplate>;
  
  // Bulk operations
  createMultipleEvents(events: CreateEventDTO[]): Promise<Event[]>;
  
  // Conflict detection
  checkConflicts(eventData: ConflictCheckDTO): Promise<ConflictResult[]>;
  getTeamAvailability(teamId: string, timeRange: TimeRange): Promise<AvailabilityMap>;
}

// Implementation
export class EventService implements IEventService {
  async createEventFromTemplate(templateId: string, overrides: Partial<CreateEventDTO>): Promise<Event> {
    // Get template
    const template = await this.getTemplate(templateId);
    
    // Merge with overrides
    const eventData: CreateEventDTO = {
      teamId: template.team_id,
      title: overrides.title || template.default_title,
      eventType: template.event_type,
      description: overrides.description || template.default_description,
      startDateTime: overrides.startDateTime,
      endDateTime: overrides.endDateTime || 
        new Date(overrides.startDateTime.getTime() + template.default_duration),
      locationName: overrides.locationName || template.default_location_name,
      locationAddress: overrides.locationAddress || template.default_location_address,
      rsvpRequired: template.rsvp_required,
      maxParticipants: template.max_participants
    };
    
    // Create event
    const event = await this.createEvent(eventData);
    
    // Update template usage
    await this.incrementTemplateUsage(templateId);
    
    return event;
  }
  
  async checkConflicts(eventData: ConflictCheckDTO): Promise<ConflictResult[]> {
    // Check for overlapping events
    const overlapping = await supabase
      .from('events')
      .select('*')
      .eq('team_id', eventData.teamId)
      .gte('start_datetime', eventData.startDateTime)
      .lte('start_datetime', eventData.endDateTime);
      
    // Check player availability
    const playerConflicts = await this.checkPlayerConflicts(
      eventData.playerIds,
      eventData.startDateTime,
      eventData.endDateTime
    );
    
    return [...overlapping.data, ...playerConflicts];
  }
}
```

### CalendarService (Week 2 Enhancements) [DEV2]

```typescript
// Week 2 additions to CalendarService

export interface ICalendarService {
  // Existing methods from Week 1...
  
  // Multiple views
  getWeekEvents(teamId: string, weekStart: Date): Promise<CalendarEvent[]>;
  getListEvents(teamId: string, startDate: Date, endDate: Date): Promise<GroupedEvents>;
  
  // Filtering and search
  filterEvents(events: CalendarEvent[], filters: EventFilters): CalendarEvent[];
  searchEvents(teamId: string, query: string): Promise<CalendarEvent[]>;
  
  // View preferences
  getUserPreferences(userId: string): Promise<CalendarPreferences>;
  saveUserPreferences(userId: string, prefs: CalendarPreferences): Promise<void>;
  
  // Calendar calculations
  calculateWeekLayout(events: CalendarEvent[]): WeekLayoutData;
  groupEventsByDate(events: CalendarEvent[]): GroupedEvents;
}

export class CalendarService implements ICalendarService {
  async getWeekEvents(teamId: string, weekStart: Date): Promise<CalendarEvent[]> {
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekEnd.getDate() + 7);
    
    const { data, error } = await supabase
      .from('events')
      .select('*')
      .eq('team_id', teamId)
      .gte('start_datetime', weekStart.toISOString())
      .lt('start_datetime', weekEnd.toISOString())
      .order('start_datetime');
      
    if (error) throw error;
    
    return this.formatEventsForCalendar(data);
  }
  
  filterEvents(events: CalendarEvent[], filters: EventFilters): CalendarEvent[] {
    return events.filter(event => {
      if (filters.eventTypes?.length && !filters.eventTypes.includes(event.eventType)) {
        return false;
      }
      
      if (filters.locations?.length && !filters.locations.includes(event.locationId)) {
        return false;
      }
      
      if (filters.players?.length) {
        const hasPlayer = event.participants.some(p => 
          filters.players.includes(p.playerId)
        );
        if (!hasPlayer) return false;
      }
      
      return true;
    });
  }
  
  calculateWeekLayout(events: CalendarEvent[]): WeekLayoutData {
    // Group by day and detect overlaps
    const dayGroups = this.groupByDay(events);
    const layoutData: WeekLayoutData = {};
    
    Object.entries(dayGroups).forEach(([day, dayEvents]) => {
      layoutData[day] = this.calculateOverlaps(dayEvents);
    });
    
    return layoutData;
  }
}
```

### RSVPService (Week 2 Full Implementation) [DEV3]

```typescript
// Week 2 full RSVP implementation

export interface IRSVPService {
  // Complete RSVP flow
  submitRSVP(eventId: string, playerId: string, rsvpData: RSVPSubmission): Promise<RSVPResponse>;
  updateRSVP(eventId: string, playerId: string, updates: RSVPUpdate): Promise<RSVPResponse>;
  
  // Parent approval
  requestParentApproval(eventId: string, playerId: string): Promise<void>;
  approveChildRSVP(approvalId: string, approved: boolean, parentId: string): Promise<void>;
  getPendingApprovals(parentId: string): Promise<PendingApproval[]>;
  
  // Waitlist
  addToWaitlist(eventId: string, playerId: string): Promise<WaitlistPosition>;
  getWaitlistPosition(eventId: string, playerId: string): Promise<number>;
  promoteFromWaitlist(eventId: string): Promise<PromotionResult>;
  
  // Reminders
  scheduleRSVPReminders(eventId: string): Promise<void>;
  sendRSVPReminder(eventId: string, playerId: string): Promise<void>;
  
  // Status queries
  getEventRSVPSummary(eventId: string): Promise<RSVPSummary>;
  getPlayerUpcomingRSVPs(playerId: string): Promise<UpcomingRSVP[]>;
}

export class RSVPService implements IRSVPService {
  async submitRSVP(eventId: string, playerId: string, rsvpData: RSVPSubmission): Promise<RSVPResponse> {
    // Check if needs parent approval
    const needsApproval = await this.checkParentApprovalRequired(playerId);
    
    // Check event capacity
    const event = await this.getEventWithCapacity(eventId);
    const currentYesCount = await this.getYesResponseCount(eventId);
    
    let finalStatus = rsvpData.response;
    let waitlistPosition = null;
    
    if (rsvpData.response === 'yes' && event.max_participants && 
        currentYesCount >= event.max_participants) {
      // Add to waitlist
      waitlistPosition = await this.addToWaitlist(eventId, playerId);
      finalStatus = 'waitlist';
    }
    
    // Submit RSVP
    const { data, error } = await supabase
      .from('event_participants')
      .upsert({
        event_id: eventId,
        player_id: playerId,
        rsvp_status: finalStatus,
        rsvp_timestamp: new Date().toISOString(),
        rsvp_by: rsvpData.submittedBy,
        rsvp_note: rsvpData.note,
        requires_parent_approval: needsApproval,
        waitlist_position: waitlistPosition,
        waitlist_timestamp: waitlistPosition ? new Date().toISOString() : null
      })
      .select()
      .single();
      
    if (error) throw error;
    
    // Request parent approval if needed
    if (needsApproval && rsvpData.response === 'yes') {
      await this.requestParentApproval(eventId, playerId);
    }
    
    // Schedule reminders
    if (rsvpData.response === 'yes') {
      await this.scheduleEventReminders(eventId, playerId);
    }
    
    return {
      status: finalStatus,
      requiresParentApproval: needsApproval,
      waitlistPosition,
      message: this.getRSVPMessage(finalStatus, needsApproval, waitlistPosition)
    };
  }
  
  async promoteFromWaitlist(eventId: string): Promise<PromotionResult> {
    // Get next on waitlist
    const { data: waitlistEntry } = await supabase
      .from('event_participants')
      .select('*')
      .eq('event_id', eventId)
      .eq('rsvp_status', 'waitlist')
      .order('waitlist_position')
      .limit(1)
      .single();
      
    if (!waitlistEntry) {
      return { promoted: false };
    }
    
    // Promote to yes
    await supabase
      .from('event_participants')
      .update({
        rsvp_status: 'yes',
        waitlist_position: null,
        waitlist_timestamp: null
      })
      .eq('event_id', eventId)
      .eq('player_id', waitlistEntry.player_id);
      
    // Notify promoted player
    await this.sendPromotionNotification(eventId, waitlistEntry.player_id);
    
    // Update remaining waitlist positions
    await this.reorderWaitlist(eventId);
    
    return {
      promoted: true,
      playerId: waitlistEntry.player_id
    };
  }
}
```

### FamilyService (Week 2 Features) [DEV4]

```typescript
// Week 2 family feature implementation

export interface IFamilyService {
  // Existing from Week 1...
  
  // Multi-child operations
  getFamilyEvents(familyId: string, dateRange: DateRange): Promise<FamilyEvent[]>;
  submitBulkRSVP(familyId: string, rsvps: BulkRSVPRequest): Promise<BulkRSVPResult>;
  
  // Conflict detection
  detectFamilyConflicts(familyId: string, dateRange: DateRange): Promise<ConflictSet[]>;
  checkEventConflict(familyId: string, newEvent: Event): Promise<Conflict[]>;
  
  // Transportation
  offerTransportation(eventId: string, offer: TransportOffer): Promise<void>;
  requestTransportation(eventId: string, playerId: string): Promise<void>;
  getTransportOptions(eventId: string): Promise<TransportOption[]>;
  
  // Preferences
  getFamilyPreferences(familyId: string): Promise<FamilyPreferences>;
  updateFamilyPreferences(familyId: string, prefs: FamilyPreferences): Promise<void>;
}

export class FamilyService implements IFamilyService {
  async getFamilyEvents(familyId: string, dateRange: DateRange): Promise<FamilyEvent[]> {
    // Get all family members
    const members = await this.getFamilyMembers(familyId);
    const playerIds = members.filter(m => m.player_id).map(m => m.player_id);
    
    // Get all events for family players
    const { data: events } = await supabase
      .from('event_participants')
      .select(`
        event_id,
        player_id,
        rsvp_status,
        events!inner(*)
      `)
      .in('player_id', playerIds)
      .gte('events.start_datetime', dateRange.start)
      .lte('events.start_datetime', dateRange.end)
      .order('events.start_datetime');
      
    // Group by child and format
    return this.formatFamilyEvents(events, members);
  }
  
  async submitBulkRSVP(familyId: string, rsvps: BulkRSVPRequest): Promise<BulkRSVPResult> {
    const results: BulkRSVPResult = {
      successful: [],
      failed: [],
      conflicts: []
    };
    
    // Check for conflicts first
    const conflicts = await this.checkBulkConflicts(rsvps);
    if (conflicts.length > 0) {
      results.conflicts = conflicts;
    }
    
    // Process each RSVP
    for (const rsvp of rsvps.rsvps) {
      try {
        // Skip if conflicts
        if (conflicts.some(c => 
          c.eventId === rsvp.eventId && c.playerId === rsvp.playerId
        )) {
          continue;
        }
        
        await rsvpService.submitRSVP(
          rsvp.eventId,
          rsvp.playerId,
          {
            response: rsvp.response,
            submittedBy: rsvps.submittedBy,
            note: rsvp.note
          }
        );
        
        results.successful.push({
          eventId: rsvp.eventId,
          playerId: rsvp.playerId
        });
      } catch (error) {
        results.failed.push({
          eventId: rsvp.eventId,
          playerId: rsvp.playerId,
          error: error.message
        });
      }
    }
    
    // Record batch for tracking
    await this.recordRSVPBatch(familyId, rsvps, results);
    
    return results;
  }
  
  async detectFamilyConflicts(familyId: string, dateRange: DateRange): Promise<ConflictSet[]> {
    const events = await this.getFamilyEvents(familyId, dateRange);
    const conflicts: ConflictSet[] = [];
    
    // Check each pair of events
    for (let i = 0; i < events.length; i++) {
      for (let j = i + 1; j < events.length; j++) {
        const event1 = events[i];
        const event2 = events[j];
        
        // Check time overlap
        if (this.eventsOverlap(event1, event2)) {
          // Check if same parent needs to be in two places
          if (this.requiresSameParent(event1, event2) ||
              this.exceedsTransportTime(event1, event2)) {
            conflicts.push({
              type: event1.playerId === event2.playerId ? 'player' : 'family',
              events: [event1, event2],
              severity: this.calculateSeverity(event1, event2)
            });
          }
        }
      }
    }
    
    return conflicts;
  }
}
```

## Implementation Timeline

### Day 1-2: Core Feature Setup

**DEV1 (Event Management)**:
- Create event_templates table
- Implement template CRUD in service
- Build EventTemplateSelector component
- Create QuickCreateModal
- Set up template usage tracking

**DEV2 (Calendar System)**:
- Build WeekView component
- Create ListView component
- Implement ViewSelector
- Set up calendar preferences table
- Create filter data structures

**DEV3 (RSVP & Attendance)**:
- Enhance event_participants table
- Build complete RSVPSubmission component
- Implement RSVP submission flow
- Create parent approval structure
- Set up waitlist logic

**DEV4 (Family Experience)**:
- Create family event queries
- Build MultiChildRSVP component
- Implement conflict detection logic
- Set up transportation tables
- Create family preference structure

### Day 3-4: Feature Implementation

**DEV1 (Event Management)**:
- Complete template workflows
- Implement bulk event creation
- Build conflict detection
- Create notification triggers
- Test template system end-to-end

**DEV2 (Calendar System)**:
- Complete all three view types
- Implement filtering system
- Add search functionality
- Create view persistence
- Optimize calendar performance

**DEV3 (RSVP & Attendance)**:
- Complete RSVP submission flow
- Build parent approval UI
- Implement waitlist management
- Create reminder scheduling
- Test RSVP workflows

**DEV4 (Family Experience)**:
- Complete bulk RSVP interface
- Build conflict visualization
- Implement basic transportation
- Create family dashboard
- Test multi-child workflows

### Day 5: Integration and Polish

**All Developers**:
- Integration testing between components
- Performance optimization
- Bug fixes from testing
- Documentation updates
- Prepare for Week 3

## Integration Points

### DEV1 ↔ DEV2
- Template events appear correctly in all views
- Bulk created events render properly
- Conflict warnings show in calendar
- Quick create accessible from calendar

### DEV1 ↔ DEV3
- Event capacity enforced in RSVP
- Template RSVP settings applied
- Notification triggers on event creation
- Waitlist activated when full

### DEV1 ↔ DEV4
- Family conflicts detected for new events
- Bulk events consider family schedules
- Templates accessible to parents

### DEV2 ↔ DEV3
- RSVP counts show in all views
- Waitlist status visible
- Calendar shows personal RSVP status
- Filters include RSVP status

### DEV2 ↔ DEV4
- Family calendar uses same view components
- Filters work for multi-child view
- Calendar shows all family events
- Conflict indicators in calendar

### DEV3 ↔ DEV4
- Bulk RSVP uses same submission logic
- Parent approval works for bulk
- Waitlist considers siblings
- Family RSVP history tracked

## Key Design Decisions

1. **Template First**: Encourage template use for consistency
2. **Progressive Disclosure**: Advanced features hidden initially
3. **Real-time Updates**: RSVP changes reflect immediately
4. **Mobile Optimized**: All features work on small screens
5. **Conflict Prevention**: Proactive warnings before issues
6. **Family Centric**: Consider multi-child scenarios throughout

## Success Metrics

### Performance
- Template event creation < 2 seconds [DEV1]
- Week view renders < 1.5 seconds [DEV2]
- Bulk RSVP (5 children) < 3 seconds [DEV4]
- RSVP submission with conflict check < 2 seconds [DEV3]

### User Experience
- 3 clicks from calendar to RSVP complete [DEV3]
- View switching instant (< 200ms) [DEV2]
- Conflict warnings clear and actionable [DEV1/DEV4]
- Parent approval in single screen [DEV3]

### Data Quality
- No lost RSVPs during bulk operations [DEV4]
- Waitlist order maintained correctly [DEV3]
- Template usage tracked accurately [DEV1]
- Filter state persisted properly [DEV2]

### Feature Adoption
- 70% of events created from templates
- 80% of families use bulk RSVP
- View preferences saved for 90% of users
- 60% waitlist promotion success rate

## Risk Mitigation

### Technical Risks
- **Performance with many events**: Implement pagination
- **Concurrent RSVP conflicts**: Use database transactions
- **Complex conflict detection**: Start simple, iterate
- **View state complexity**: Use proven patterns

### Integration Risks
- **Component dependencies**: Clear interfaces defined
- **Data consistency**: Shared service methods
- **UI inconsistency**: Regular design reviews
- **Feature gaps**: Daily standups

## Testing Requirements

### Unit Tests
- Template creation and usage
- RSVP submission logic
- Conflict detection algorithms
- View filtering logic

### Integration Tests
- Full RSVP flow with approval
- Bulk operations end-to-end
- Calendar view switching
- Family conflict scenarios

### User Acceptance Tests
- Coach can create event from template
- Parent can RSVP multiple children
- Calendar shows correct events
- Conflicts are clearly indicated

## Next Week Preview

Week 3 will focus on:
- Advanced integration between systems
- Attendance tracking implementation
- Calendar export/sync features
- Performance optimization
- Notification system integration

---

## Document Metadata

**Last Updated**: 2024-03-10
**Status**: Ready for Implementation
**Week**: 2 of 4
**Feature**: Schedule System
**Focus**: Core Features