# EPIC 4 - Schedule System Implementation Plan - Developer Work Split

## Developer Assignments

### DEV1 - Event Management
**Focus**: Event creation, editing, and lifecycle management
- Event creation workflow and templates
- Event types and location management
- Event editing, status management, and cancellation
- Notification triggers and conflict detection
- Bulk event operations and series creation
- Event templates and smart scheduling

### DEV2 - Calendar System
**Focus**: Calendar views and navigation
- Monthly calendar view implementation
- Weekly and list view development
- Event filtering and search functionality
- Calendar navigation and date selection
- Team and player calendar views
- Calendar export capabilities

### DEV3 - RSVP & Attendance
**Focus**: Player responses and attendance tracking
- RSVP collection system and workflows
- Parent approval flow for child RSVPs
- Attendance recording and tracking
- Late arrival and absence management
- RSVP reminders and deadline enforcement
- Waitlist management for capacity-limited events

### DEV4 - Family Experience
**Focus**: Parent and player event interaction
- Family calendar view with multi-child support
- Bulk RSVP management across children
- Event details display and information access
- Transportation coordination features
- Calendar subscription and sync
- Mobile optimization and responsive design

---

## Overview

The Schedule System is the central hub for all event management within the SHOT app. It handles the complete lifecycle of team activities from creation through attendance tracking. This system serves as the "when and where" foundation that coaches rely on for organizing their teams and that families depend on for managing their sports commitments.

The MVP implementation focuses on delivering a seamless experience for creating events, collecting RSVPs, tracking attendance, and providing families with clear visibility of all upcoming activities. The system is designed to reduce the administrative burden on coaches while ensuring parents never miss important team events.

This implementation plan breaks down the four-week development into focused work streams, with each developer owning a specific aspect of the system while maintaining clear integration points with other components.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Page Flow and Purpose](#page-flow-and-purpose)
3. [Component Structure](#component-structure)
4. [Database Design](#database-design)
5. [Service Architecture](#service-architecture)
6. [Implementation Timeline](#implementation-timeline)
7. [Integration Points](#integration-points)
8. [Success Metrics](#success-metrics)

## Architecture Overview

### Directory Structure

```
src/features/schedule/
├── components/
│   ├── shared/
│   │   ├── EventCard.tsx [DEV1/DEV2]
│   │   ├── DateTimePicker.tsx [DEV1]
│   │   ├── LocationSelector.tsx [DEV1]
│   │   └── RSVPButton.tsx [DEV3]
│   ├── events/
│   │   ├── EventForm.tsx [DEV1]
│   │   ├── EventTemplateSelector.tsx [DEV1]
│   │   ├── EventTypeSelector.tsx [DEV1]
│   │   ├── EventStatusBadge.tsx [DEV1]
│   │   └── BulkEventCreator.tsx [DEV1]
│   ├── calendar/
│   │   ├── CalendarGrid.tsx [DEV2]
│   │   ├── CalendarHeader.tsx [DEV2]
│   │   ├── CalendarDay.tsx [DEV2]
│   │   ├── WeekView.tsx [DEV2]
│   │   ├── ListView.tsx [DEV2]
│   │   └── EventFilters.tsx [DEV2]
│   ├── rsvp/
│   │   ├── RSVPForm.tsx [DEV3]
│   │   ├── RSVPStatus.tsx [DEV3]
│   │   ├── ParentApprovalFlow.tsx [DEV3]
│   │   ├── AttendanceTracker.tsx [DEV3]
│   │   └── RSVPReminders.tsx [DEV3]
│   ├── family/
│   │   ├── FamilyCalendar.tsx [DEV4]
│   │   ├── MultiChildRSVP.tsx [DEV4]
│   │   ├── EventDetailsView.tsx [DEV4]
│   │   ├── TransportCoordinator.tsx [DEV4]
│   │   └── CalendarSubscription.tsx [DEV4]
├── pages/
│   ├── ScheduleHome.tsx [DEV2]
│   ├── CreateEvent.tsx [DEV1]
│   ├── EventDetails.tsx [DEV1/DEV3]
│   ├── TeamCalendar.tsx [DEV2]
│   ├── FamilySchedule.tsx [DEV4]
│   └── AttendanceSheet.tsx [DEV3]
├── hooks/
│   ├── useEvents.ts [DEV1]
│   ├── useCalendar.ts [DEV2]
│   ├── useRSVP.ts [DEV3]
│   └── useFamilySchedule.ts [DEV4]
├── services/
│   ├── EventService.ts [DEV1]
│   ├── CalendarService.ts [DEV2]
│   ├── RSVPService.ts [DEV3]
│   └── NotificationService.ts [DEV1/DEV3]
├── routes/
│   └── ScheduleRoutes.tsx [DEV2]
└── types/
    ├── event.types.ts [DEV1]
    ├── calendar.types.ts [DEV2]
    ├── rsvp.types.ts [DEV3]
    └── family.types.ts [DEV4]
```

## Page Flow and Purpose

### Complete Feature Flow Diagram

```mermaid
graph TB
    Start([Schedule Entry]) --> ScheduleHome[Schedule Home - DEV2]
    
    %% Coach Flow
    ScheduleHome --> |Coach: Create Event| CreateEvent[Create Event - DEV1]
    CreateEvent --> |Select Template| TemplateSelector{Use Template? - DEV1}
    TemplateSelector --> |Yes| QuickCreate[Quick Create - DEV1]
    TemplateSelector --> |No| FullForm[Full Event Form - DEV1]
    QuickCreate --> PublishEvent[Publish Event - DEV1]
    FullForm --> PublishEvent
    
    %% Calendar Views
    ScheduleHome --> |View Calendar| CalendarView{Select View - DEV2}
    CalendarView --> MonthView[Month View - DEV2]
    CalendarView --> WeekView[Week View - DEV2]
    CalendarView --> ListView[List View - DEV2]
    
    %% RSVP Flow
    MonthView --> |Click Event| EventDetails[Event Details - DEV1/DEV3]
    WeekView --> EventDetails
    ListView --> EventDetails
    EventDetails --> |Player/Parent| RSVPFlow[RSVP Form - DEV3]
    RSVPFlow --> ParentApproval{Parent Approval? - DEV3}
    ParentApproval --> |Required| ApprovalRequest[Request Approval - DEV3]
    ParentApproval --> |Not Required| RSVPConfirm[Confirm RSVP - DEV3]
    ApprovalRequest --> RSVPConfirm
    
    %% Attendance Flow
    EventDetails --> |Coach: Day Of| AttendanceSheet[Attendance Sheet - DEV3]
    AttendanceSheet --> RecordAttendance[Record Attendance - DEV3]
    
    %% Family Flow
    ScheduleHome --> |Family View| FamilySchedule[Family Schedule - DEV4]
    FamilySchedule --> MultiChildView[Multi-Child Calendar - DEV4]
    MultiChildView --> BulkRSVP[Bulk RSVP - DEV4]
    FamilySchedule --> CalendarSync[Calendar Sync - DEV4]
    
    %% End States
    PublishEvent --> End([Event Created])
    RSVPConfirm --> End2([RSVP Submitted])
    RecordAttendance --> End3([Attendance Recorded])
    CalendarSync --> End4([Calendar Synced])
```

### Page Details

#### 1. Schedule Home [DEV2]

**Purpose**: Central hub for accessing all schedule functionality

**Path**: `/src/features/schedule/pages/ScheduleHome.tsx`
**Route**: `/schedule`

```typescript
/**
 * ScheduleHome
 * 
 * PURPOSE:
 * - Provide role-based entry to schedule features
 * - Display upcoming events summary
 * - Quick access to common actions
 * 
 * USER GOALS:
 * - Quickly see what's coming up
 * - Access appropriate schedule tools
 * - Navigate to detailed views
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowCard } from '@/foundation/design-system/components/atoms/Card';
import { Navigation } from '@/components/Navigation';
```

**New Components Required:**
```typescript
// Quick stats display
import { UpcomingEvents } from '@/src/features/schedule/components/shared/UpcomingEvents';
// Path: /src/features/schedule/components/shared/UpcomingEvents.tsx

// Role-based action buttons
import { ScheduleActions } from '@/src/features/schedule/components/shared/ScheduleActions';
// Path: /src/features/schedule/components/shared/ScheduleActions.tsx
```

**Services Used:**
```typescript
import { EventService } from '@/src/features/schedule/services/EventService';
// Methods: 
// - getUpcomingEvents(teamId: string, limit: number): Promise<Event[]>
// - getUserEventCount(userId: string): Promise<EventCounts>
```

**Hooks Used:**
```typescript
import { useEvents } from '@/src/features/schedule/hooks/useEvents';
// Returns: { upcomingEvents, isLoading, refetch }
```

#### 2. Create Event [DEV1]

**Purpose**: Comprehensive event creation with templates and smart defaults

**Path**: `/src/features/schedule/pages/CreateEvent.tsx`
**Route**: `/schedule/events/new`

```typescript
/**
 * CreateEvent
 * 
 * PURPOSE:
 * - Enable quick event creation with templates
 * - Support detailed custom event configuration
 * - Handle series and bulk event creation
 * 
 * USER GOALS:
 * - Create events quickly with minimal input
 * - Set up recurring event series
 * - Configure all event details when needed
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowForm } from '@/foundation/design-system/components/molecules/Form';
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowInput } from '@/foundation/design-system/components/atoms/Input';
```

**New Components Required:**
```typescript
// Event creation form
import { EventForm } from '@/src/features/schedule/components/events/EventForm';
// Path: /src/features/schedule/components/events/EventForm.tsx

// Template selection
import { EventTemplateSelector } from '@/src/features/schedule/components/events/EventTemplateSelector';
// Path: /src/features/schedule/components/events/EventTemplateSelector.tsx

// Date/time picker
import { DateTimePicker } from '@/src/features/schedule/components/shared/DateTimePicker';
// Path: /src/features/schedule/components/shared/DateTimePicker.tsx

// Location selector
import { LocationSelector } from '@/src/features/schedule/components/shared/LocationSelector';
// Path: /src/features/schedule/components/shared/LocationSelector.tsx
```

**Services Used:**
```typescript
import { EventService } from '@/src/features/schedule/services/EventService';
// Methods: 
// - createEvent(eventData: CreateEventDTO): Promise<Event>
// - getEventTemplates(teamId: string): Promise<EventTemplate[]>
// - validateEventConflicts(eventData: EventData): Promise<ConflictResult>
```

**Hooks Used:**
```typescript
import { useEvents } from '@/src/features/schedule/hooks/useEvents';
// Returns: { createEvent, isCreating, error }
```

#### 3. Team Calendar [DEV2]

**Purpose**: Primary calendar interface with multiple view options

**Path**: `/src/features/schedule/pages/TeamCalendar.tsx`
**Route**: `/schedule/calendar`

```typescript
/**
 * TeamCalendar
 * 
 * PURPOSE:
 * - Display team events in calendar format
 * - Support month, week, and list views
 * - Enable filtering and navigation
 * 
 * USER GOALS:
 * - See team schedule at a glance
 * - Navigate between time periods
 * - Filter by event type or player
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowTabs } from '@/foundation/design-system/components/molecules/Tabs';
import { ViewToggle } from '@/components/shared/ViewToggle';
```

**New Components Required:**
```typescript
// Calendar grid component
import { CalendarGrid } from '@/src/features/schedule/components/calendar/CalendarGrid';
// Path: /src/features/schedule/components/calendar/CalendarGrid.tsx

// Week view component
import { WeekView } from '@/src/features/schedule/components/calendar/WeekView';
// Path: /src/features/schedule/components/calendar/WeekView.tsx

// List view component
import { ListView } from '@/src/features/schedule/components/calendar/ListView';
// Path: /src/features/schedule/components/calendar/ListView.tsx

// Event filters
import { EventFilters } from '@/src/features/schedule/components/calendar/EventFilters';
// Path: /src/features/schedule/components/calendar/EventFilters.tsx
```

**Services Used:**
```typescript
import { CalendarService } from '@/src/features/schedule/services/CalendarService';
// Methods: 
// - getMonthEvents(teamId: string, month: Date): Promise<CalendarEvent[]>
// - getWeekEvents(teamId: string, weekStart: Date): Promise<CalendarEvent[]>
// - exportCalendar(teamId: string, format: ExportFormat): Promise<Blob>
```

**Hooks Used:**
```typescript
import { useCalendar } from '@/src/features/schedule/hooks/useCalendar';
// Returns: { events, currentView, setView, navigate, filters }
```

#### 4. Event Details [DEV1/DEV3]

**Purpose**: Complete event information with RSVP and attendance functionality

**Path**: `/src/features/schedule/pages/EventDetails.tsx`
**Route**: `/schedule/events/:eventId`

```typescript
/**
 * EventDetails
 * 
 * PURPOSE:
 * - Display complete event information
 * - Enable RSVP submission and management
 * - Provide attendance tracking (day of event)
 * 
 * USER GOALS:
 * - See all event details clearly
 * - Submit or change RSVP
 * - Track who's attending (coaches)
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowCard } from '@/foundation/design-system/components/atoms/Card';
import { InfoDisplay } from '@/components/shared/InfoDisplay';
```

**New Components Required:**
```typescript
// RSVP form component
import { RSVPForm } from '@/src/features/schedule/components/rsvp/RSVPForm';
// Path: /src/features/schedule/components/rsvp/RSVPForm.tsx

// RSVP status display
import { RSVPStatus } from '@/src/features/schedule/components/rsvp/RSVPStatus';
// Path: /src/features/schedule/components/rsvp/RSVPStatus.tsx

// Event status badge
import { EventStatusBadge } from '@/src/features/schedule/components/events/EventStatusBadge';
// Path: /src/features/schedule/components/events/EventStatusBadge.tsx
```

**Services Used:**
```typescript
import { EventService } from '@/src/features/schedule/services/EventService';
import { RSVPService } from '@/src/features/schedule/services/RSVPService';
// Methods: 
// - getEventDetails(eventId: string): Promise<EventDetails>
// - submitRSVP(eventId: string, rsvpData: RSVPData): Promise<RSVPResponse>
// - getRSVPStatus(eventId: string, userId: string): Promise<RSVPStatus>
```

**Hooks Used:**
```typescript
import { useEvents } from '@/src/features/schedule/hooks/useEvents';
import { useRSVP } from '@/src/features/schedule/hooks/useRSVP';
// Returns: { event, rsvpStatus, submitRSVP, isSubmitting }
```

#### 5. Family Schedule [DEV4]

**Purpose**: Family-centric view of all children's events

**Path**: `/src/features/schedule/pages/FamilySchedule.tsx`
**Route**: `/schedule/family`

```typescript
/**
 * FamilySchedule
 * 
 * PURPOSE:
 * - Display all family members' events
 * - Enable bulk RSVP management
 * - Support calendar sync and export
 * 
 * USER GOALS:
 * - See all children's commitments
 * - Manage multiple RSVPs efficiently
 * - Sync with personal calendars
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowTabs } from '@/foundation/design-system/components/molecules/Tabs';
import { FamilySelector } from '@/components/shared/FamilySelector';
```

**New Components Required:**
```typescript
// Family calendar view
import { FamilyCalendar } from '@/src/features/schedule/components/family/FamilyCalendar';
// Path: /src/features/schedule/components/family/FamilyCalendar.tsx

// Multi-child RSVP management
import { MultiChildRSVP } from '@/src/features/schedule/components/family/MultiChildRSVP';
// Path: /src/features/schedule/components/family/MultiChildRSVP.tsx

// Calendar subscription setup
import { CalendarSubscription } from '@/src/features/schedule/components/family/CalendarSubscription';
// Path: /src/features/schedule/components/family/CalendarSubscription.tsx
```

**Services Used:**
```typescript
import { CalendarService } from '@/src/features/schedule/services/CalendarService';
// Methods: 
// - getFamilyEvents(familyId: string): Promise<FamilyEvent[]>
// - bulkRSVP(rsvpRequests: BulkRSVPRequest[]): Promise<BulkRSVPResponse>
// - generateCalendarSubscription(familyId: string): Promise<SubscriptionURL>
```

**Hooks Used:**
```typescript
import { useFamilySchedule } from '@/src/features/schedule/hooks/useFamilySchedule';
// Returns: { familyEvents, selectedChildren, bulkRSVP, syncStatus }
```

#### 6. Attendance Sheet [DEV3]

**Purpose**: Real-time attendance tracking on event day

**Path**: `/src/features/schedule/pages/AttendanceSheet.tsx`
**Route**: `/schedule/events/:eventId/attendance`

```typescript
/**
 * AttendanceSheet
 * 
 * PURPOSE:
 * - Track player attendance in real-time
 * - Handle late arrivals and early departures
 * - Record absence reasons
 * 
 * USER GOALS:
 * - Quickly mark attendance
 * - Track actual vs expected attendance
 * - Document special circumstances
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowCheckbox } from '@/foundation/design-system/components/atoms/Checkbox';
import { PlayerList } from '@/components/shared/PlayerList';
```

**New Components Required:**
```typescript
// Attendance tracking component
import { AttendanceTracker } from '@/src/features/schedule/components/rsvp/AttendanceTracker';
// Path: /src/features/schedule/components/rsvp/AttendanceTracker.tsx

// Late arrival handler
import { LateArrivalModal } from '@/src/features/schedule/components/rsvp/LateArrivalModal';
// Path: /src/features/schedule/components/rsvp/LateArrivalModal.tsx
```

**Services Used:**
```typescript
import { RSVPService } from '@/src/features/schedule/services/RSVPService';
// Methods: 
// - getEventParticipants(eventId: string): Promise<Participant[]>
// - recordAttendance(eventId: string, attendance: AttendanceRecord[]): Promise<void>
// - updateArrivalTime(eventId: string, playerId: string, time: Date): Promise<void>
```

**Hooks Used:**
```typescript
import { useRSVP } from '@/src/features/schedule/hooks/useRSVP';
// Returns: { participants, recordAttendance, updateStatus, saveProgress }
```

## Route Configuration

```typescript
// /src/features/schedule/routes/ScheduleRoutes.tsx [DEV2]
export const ScheduleRoutes = () => (
  <Switch>
    {/* Main schedule hub */}
    <Route exact path="/schedule" component={ScheduleHome} />
    
    {/* Event management routes */}
    <Route path="/schedule/events/new" component={CreateEvent} />
    <Route path="/schedule/events/:eventId/edit" component={EditEvent} />
    <Route path="/schedule/events/:eventId/attendance" component={AttendanceSheet} />
    <Route path="/schedule/events/:eventId" component={EventDetails} />
    
    {/* Calendar views */}
    <Route path="/schedule/calendar" component={TeamCalendar} />
    <Route path="/schedule/family" component={FamilySchedule} />
    
    {/* Smart router for role-based redirects */}
    <Route path="/schedule/:teamId" component={TeamScheduleRouter} />
  </Switch>
);
```

## Database Design

### Core Event Tables [DEV1]

```sql
-- Enhanced events table with all MVP fields
CREATE TABLE public.events (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id uuid NOT NULL REFERENCES public.teams(id),
    event_type varchar(50) NOT NULL, -- 'training', 'match', 'tournament', 'social', 'other'
    title varchar(200) NOT NULL,
    description text,
    
    -- Temporal fields
    start_datetime timestamp with time zone NOT NULL,
    end_datetime timestamp with time zone NOT NULL,
    all_day boolean DEFAULT false,
    
    -- Location information
    location_id uuid REFERENCES public.locations(id),
    location_override text, -- For one-off locations
    
    -- RSVP configuration
    rsvp_required boolean DEFAULT true,
    rsvp_deadline timestamp with time zone,
    max_participants integer,
    
    -- Status and metadata
    status varchar(20) DEFAULT 'scheduled', -- 'draft', 'scheduled', 'cancelled', 'completed'
    created_by uuid NOT NULL REFERENCES auth.users(id),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    -- Notification settings
    send_reminders boolean DEFAULT true,
    reminder_hours integer[] DEFAULT ARRAY[24, 2], -- Hours before event
    
    -- Series information (for recurring events - future phase)
    series_id uuid,
    series_position integer,
    
    CONSTRAINT valid_event_times CHECK (end_datetime > start_datetime),
    CONSTRAINT valid_rsvp_deadline CHECK (rsvp_deadline IS NULL OR rsvp_deadline < start_datetime)
);

-- Locations management
CREATE TABLE public.locations (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id uuid NOT NULL REFERENCES public.teams(id),
    name varchar(100) NOT NULL,
    address text NOT NULL,
    
    -- Geographic data
    latitude decimal(10, 8),
    longitude decimal(11, 8),
    
    -- Additional information
    directions text,
    parking_info text,
    facilities text[], -- ['parking', 'changing_rooms', 'toilets', 'refreshments']
    
    -- Metadata
    is_home_venue boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    UNIQUE(team_id, name)
);

-- Event templates for quick creation
CREATE TABLE public.event_templates (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id uuid NOT NULL REFERENCES public.teams(id),
    name varchar(100) NOT NULL,
    event_type varchar(50) NOT NULL,
    
    -- Default values
    default_duration interval DEFAULT '90 minutes',
    default_location_id uuid REFERENCES public.locations(id),
    default_description text,
    default_max_participants integer,
    
    -- RSVP defaults
    rsvp_required boolean DEFAULT true,
    rsvp_hours_before integer DEFAULT 24,
    
    -- Usage tracking
    usage_count integer DEFAULT 0,
    last_used timestamp with time zone,
    
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Indexes for performance
CREATE INDEX idx_events_team_start ON public.events(team_id, start_datetime);
CREATE INDEX idx_events_status ON public.events(status) WHERE status != 'completed';
CREATE INDEX idx_events_series ON public.events(series_id) WHERE series_id IS NOT NULL;
```

### RSVP and Attendance Tables [DEV3]

```sql
-- Event participants and RSVPs
CREATE TABLE public.event_participants (
    event_id uuid NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
    player_id uuid NOT NULL REFERENCES public.profiles(id),
    
    -- RSVP information
    rsvp_status varchar(20) DEFAULT 'pending', -- 'pending', 'yes', 'no', 'maybe'
    rsvp_timestamp timestamp with time zone,
    rsvp_by uuid REFERENCES auth.users(id), -- Who submitted (parent or player)
    rsvp_note text, -- Reason for no, or other notes
    
    -- Parent approval (for youth teams)
    requires_parent_approval boolean DEFAULT false,
    parent_approved boolean,
    parent_approved_by uuid REFERENCES auth.users(id),
    parent_approved_at timestamp with time zone,
    
    -- Attendance tracking
    attendance_status varchar(20), -- 'present', 'absent', 'late', 'left_early'
    arrival_time timestamp with time zone,
    departure_time timestamp with time zone,
    absence_reason text,
    marked_by uuid REFERENCES auth.users(id),
    marked_at timestamp with time zone,
    
    -- Waitlist
    waitlist_position integer,
    waitlist_timestamp timestamp with time zone,
    
    -- Metadata
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    PRIMARY KEY (event_id, player_id)
);

-- RSVP reminders tracking
CREATE TABLE public.rsvp_reminders (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id uuid NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
    player_id uuid NOT NULL REFERENCES public.profiles(id),
    reminder_type varchar(50) NOT NULL, -- 'initial', 'followup', 'final'
    
    -- Delivery information
    sent_at timestamp with time zone DEFAULT now(),
    sent_via varchar(20) NOT NULL, -- 'push', 'email', 'sms'
    delivery_status varchar(20), -- 'sent', 'delivered', 'failed'
    
    -- Response tracking
    opened_at timestamp with time zone,
    responded_at timestamp with time zone,
    
    UNIQUE(event_id, player_id, reminder_type)
);

-- Quick RSVP tokens (for one-click responses)
CREATE TABLE public.rsvp_tokens (
    token uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id uuid NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
    player_id uuid NOT NULL REFERENCES public.profiles(id),
    action varchar(10) NOT NULL, -- 'yes', 'no', 'maybe'
    
    -- Security
    expires_at timestamp with time zone NOT NULL,
    used_at timestamp with time zone,
    
    created_at timestamp with time zone DEFAULT now()
);

-- Indexes for RSVP performance
CREATE INDEX idx_event_participants_event ON public.event_participants(event_id);
CREATE INDEX idx_event_participants_player ON public.event_participants(player_id);
CREATE INDEX idx_event_participants_rsvp_pending ON public.event_participants(event_id) 
    WHERE rsvp_status = 'pending';
CREATE INDEX idx_rsvp_reminders_event ON public.rsvp_reminders(event_id, reminder_type);
```

### Family and Calendar Tables [DEV4]

```sql
-- Calendar subscriptions for families
CREATE TABLE public.calendar_subscriptions (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid NOT NULL REFERENCES auth.users(id),
    subscription_type varchar(50) NOT NULL, -- 'family', 'team', 'player'
    
    -- Filtering options
    filters jsonb DEFAULT '{}', -- {"teams": [], "players": [], "event_types": []}
    
    -- Subscription details
    subscription_url text NOT NULL UNIQUE,
    sync_token text NOT NULL,
    
    -- Usage tracking
    last_synced timestamp with time zone,
    sync_count integer DEFAULT 0,
    
    -- Status
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Transportation coordination
CREATE TABLE public.event_transportation (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id uuid NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
    
    -- Driver information
    driver_id uuid NOT NULL REFERENCES auth.users(id),
    seats_available integer NOT NULL,
    departure_location text,
    departure_time timestamp with time zone,
    
    -- Passengers
    notes text,
    
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Transportation requests/assignments
CREATE TABLE public.transportation_passengers (
    transportation_id uuid NOT NULL REFERENCES public.event_transportation(id) ON DELETE CASCADE,
    player_id uuid NOT NULL REFERENCES public.profiles(id),
    
    -- Request details
    requested_by uuid NOT NULL REFERENCES auth.users(id),
    requested_at timestamp with time zone DEFAULT now(),
    
    -- Confirmation
    confirmed boolean DEFAULT false,
    confirmed_at timestamp with time zone,
    
    PRIMARY KEY (transportation_id, player_id)
);

-- Family view preferences
CREATE TABLE public.family_calendar_preferences (
    user_id uuid PRIMARY KEY REFERENCES auth.users(id),
    
    -- Display preferences
    default_view varchar(20) DEFAULT 'month', -- 'month', 'week', 'list'
    show_all_children boolean DEFAULT true,
    color_by varchar(20) DEFAULT 'child', -- 'child', 'team', 'event_type'
    
    -- Notification preferences
    rsvp_reminder_hours integer DEFAULT 24,
    event_change_notifications boolean DEFAULT true,
    
    -- Calendar sync
    auto_sync_enabled boolean DEFAULT false,
    sync_calendars text[], -- ['google', 'apple', 'outlook']
    
    updated_at timestamp with time zone DEFAULT now()
);
```

## Service Architecture

### EventService [DEV1]

```typescript
// /src/features/schedule/services/EventService.ts

export interface IEventService {
  // Event CRUD operations
  createEvent(eventData: CreateEventDTO): Promise<Event>;
  updateEvent(eventId: string, updates: UpdateEventDTO): Promise<Event>;
  cancelEvent(eventId: string, reason: string): Promise<void>;
  deleteEvent(eventId: string): Promise<void>;
  
  // Event retrieval
  getEventDetails(eventId: string): Promise<EventDetails>;
  getTeamEvents(teamId: string, filters: EventFilters): Promise<Event[]>;
  getUpcomingEvents(teamId: string, limit: number): Promise<Event[]>;
  
  // Template management
  getEventTemplates(teamId: string): Promise<EventTemplate[]>;
  createEventFromTemplate(templateId: string, overrides: Partial<CreateEventDTO>): Promise<Event>;
  saveAsTemplate(eventId: string, templateName: string): Promise<EventTemplate>;
  
  // Conflict detection
  checkEventConflicts(eventData: ConflictCheckDTO): Promise<ConflictResult[]>;
  getPlayerAvailability(playerIds: string[], timeRange: TimeRange): Promise<AvailabilityMap>;
  
  // Bulk operations
  createEventSeries(seriesData: CreateSeriesDTO): Promise<Event[]>;
  bulkUpdateEvents(eventIds: string[], updates: Partial<UpdateEventDTO>): Promise<Event[]>;
  
  // Notifications
  sendEventNotification(eventId: string, type: NotificationType): Promise<void>;
  scheduleReminders(eventId: string): Promise<void>;
}

export class EventService implements IEventService {
  // Implementation with Supabase integration
}
```

### CalendarService [DEV2]

```typescript
// /src/features/schedule/services/CalendarService.ts

export interface ICalendarService {
  // Calendar data retrieval
  getMonthEvents(teamId: string, month: Date): Promise<CalendarEvent[]>;
  getWeekEvents(teamId: string, weekStart: Date): Promise<CalendarEvent[]>;
  getDateRangeEvents(teamId: string, start: Date, end: Date): Promise<CalendarEvent[]>;
  
  // Filtering and search
  filterEvents(events: CalendarEvent[], filters: EventFilters): CalendarEvent[];
  searchEvents(teamId: string, query: string): Promise<CalendarEvent[]>;
  
  // Calendar export
  exportCalendar(teamId: string, format: 'ics' | 'csv' | 'pdf'): Promise<Blob>;
  generateICSFeed(subscriptionId: string): Promise<string>;
  
  // View state management
  getCalendarPreferences(userId: string): Promise<CalendarPreferences>;
  saveCalendarPreferences(userId: string, prefs: CalendarPreferences): Promise<void>;
  
  // Calendar calculations
  getEventPositions(events: CalendarEvent[], view: CalendarView): EventPosition[];
  calculateEventOverlaps(events: CalendarEvent[]): OverlapGroup[];
}

export class CalendarService implements ICalendarService {
  // Implementation with efficient date handling
}
```

### RSVPService [DEV3]

```typescript
// /src/features/schedule/services/RSVPService.ts

export interface IRSVPService {
  // RSVP submission
  submitRSVP(eventId: string, playerId: string, response: RSVPResponse): Promise<void>;
  updateRSVP(eventId: string, playerId: string, updates: RSVPUpdate): Promise<void>;
  submitBulkRSVP(rsvps: BulkRSVPRequest[]): Promise<BulkRSVPResult>;
  
  // RSVP retrieval
  getEventRSVPs(eventId: string): Promise<EventRSVPSummary>;
  getPlayerRSVP(eventId: string, playerId: string): Promise<PlayerRSVP | null>;
  getPendingRSVPs(teamId: string): Promise<PendingRSVP[]>;
  
  // Parent approval
  requestParentApproval(eventId: string, playerId: string): Promise<void>;
  approveChildRSVP(eventId: string, playerId: string, approved: boolean): Promise<void>;
  getPendingApprovals(parentId: string): Promise<ApprovalRequest[]>;
  
  // Attendance tracking
  getEventParticipants(eventId: string): Promise<Participant[]>;
  recordAttendance(eventId: string, attendance: AttendanceRecord[]): Promise<void>;
  updateArrivalTime(eventId: string, playerId: string, time: Date): Promise<void>;
  markAbsence(eventId: string, playerId: string, reason: string): Promise<void>;
  
  // Reminders
  sendRSVPReminder(eventId: string, playerId: string): Promise<void>;
  scheduleAutomaticReminders(eventId: string): Promise<void>;
  
  // Waitlist management
  addToWaitlist(eventId: string, playerId: string): Promise<number>; // Returns position
  promoteFromWaitlist(eventId: string): Promise<string | null>; // Returns promoted player ID
}

export class RSVPService implements IRSVPService {
  // Implementation with real-time updates
}
```

### NotificationService [DEV1/DEV3]

```typescript
// /src/features/schedule/services/NotificationService.ts

export interface INotificationService {
  // Event notifications
  notifyEventCreated(event: Event, recipients: string[]): Promise<void>;
  notifyEventUpdated(event: Event, changes: EventChanges): Promise<void>;
  notifyEventCancelled(event: Event, reason: string): Promise<void>;
  
  // RSVP notifications
  sendRSVPRequest(event: Event, playerIds: string[]): Promise<void>;
  sendRSVPReminder(event: Event, pendingPlayers: string[]): Promise<void>;
  sendRSVPConfirmation(event: Event, playerId: string, response: string): Promise<void>;
  
  // Attendance notifications
  notifyLateArrival(event: Event, playerId: string): Promise<void>;
  sendAttendanceSummary(event: Event, coachId: string): Promise<void>;
  
  // Batch operations
  sendBatchNotifications(notifications: NotificationBatch[]): Promise<BatchResult>;
  
  // Preference management
  getUserNotificationPreferences(userId: string): Promise<NotificationPreferences>;
  updateNotificationPreferences(userId: string, prefs: NotificationPreferences): Promise<void>;
}

export class NotificationService implements INotificationService {
  // Implementation with multiple channels (push, email, SMS)
}
```

## Implementation Timeline

### Phase 1: Foundation (Week 1)

**Goal**: Basic event creation and calendar display working

**DEV1 (Event Management)**:
- Set up event database schema and migrations
- Implement basic event CRUD operations in EventService
- Create EventForm component with validation
- Build location management system
- Implement event type selection

**DEV2 (Calendar System)**:
- Create calendar grid component structure
- Implement month view with event display
- Set up calendar navigation (prev/next month)
- Create basic event filtering
- Build ScheduleHome page with role detection

**DEV3 (RSVP & Attendance)**:
- Design RSVP database schema
- Create RSVPService with basic operations
- Build simple RSVP form component
- Implement RSVP status tracking
- Set up event participant relationships

**DEV4 (Family Experience)**:
- Create family data structures
- Build family member selection component
- Design multi-child view layout
- Implement basic family calendar view
- Set up family relationship queries

### Phase 2: Core Features (Week 2)

**Goal**: Complete RSVP flow and multiple calendar views

**DEV1 (Event Management)**:
- Implement event templates system
- Add bulk event creation
- Build conflict detection
- Create event notification triggers
- Add quick-create workflows

**DEV2 (Calendar System)**:
- Implement week view
- Create list view
- Add advanced filtering options
- Build event search functionality
- Implement view persistence

**DEV3 (RSVP & Attendance)**:
- Complete RSVP submission flow
- Implement parent approval workflow
- Create RSVP reminder system
- Build waitlist functionality
- Add RSVP change handling

**DEV4 (Family Experience)**:
- Implement bulk RSVP for multiple children
- Create conflict detection for families
- Build event details view
- Add transportation coordination basics
- Implement family notification preferences

### Phase 3: Integration (Week 3)

**Goal**: Full feature integration and advanced functionality

**DEV1 (Event Management)**:
- Integrate with notification service
- Implement event status management
- Add recurring event prep (data model)
- Create admin event tools
- Build event change propagation

**DEV2 (Calendar System)**:
- Add calendar export functionality
- Implement print views
- Create calendar subscription feeds
- Add touch gesture support
- Optimize calendar rendering performance

**DEV3 (RSVP & Attendance)**:
- Build attendance tracking interface
- Implement late arrival handling
- Create absence reason tracking
- Add attendance reports
- Build coach attendance tools

**DEV4 (Family Experience)**:
- Complete calendar sync implementation
- Add iOS/Android calendar integration
- Build transportation coordination
- Create family communication features
- Implement mobile optimizations

### Phase 4: Polish & Launch (Week 4)

**Goal**: Production-ready with all features polished

**All Developers**:
- Cross-component integration testing
- Performance optimization
- Mobile responsiveness testing
- User acceptance testing
- Bug fixes and polish
- Documentation completion
- Production deployment preparation
- Feature flag configuration

## Integration Points

### DEV1 ↔ DEV2
- Event data structure (DEV1 provides, DEV2 displays)
- Calendar event formatting (shared responsibility)
- Event filters interface (DEV2 defines, DEV1 implements)
- Quick navigation from calendar to event creation (DEV2 initiates, DEV1 handles)

### DEV1 ↔ DEV3
- Event participant management (DEV1 creates events, DEV3 manages RSVPs)
- Notification triggers (DEV1 defines when, DEV3 executes)
- Event capacity handling (DEV1 sets limits, DEV3 enforces)
- Attendance eligibility (shared validation)

### DEV1 ↔ DEV4
- Family event visibility (DEV1 creates, DEV4 filters by family)
- Bulk event notifications (DEV1 triggers, DEV4 routes to families)
- Event information display (DEV1 provides, DEV4 formats for families)

### DEV2 ↔ DEV3
- RSVP status display on calendar (DEV3 provides, DEV2 shows)
- Calendar-based RSVP reminders (DEV2 calculates timing, DEV3 sends)
- Attendance summary on calendar (DEV3 collects, DEV2 displays)

### DEV2 ↔ DEV4
- Calendar view switching (shared UI components)
- Family calendar filtering (DEV4 defines needs, DEV2 implements)
- Calendar export for families (DEV2 generates, DEV4 customizes)

### DEV3 ↔ DEV4
- Multi-child RSVP coordination (DEV4 collects, DEV3 processes)
- Parent approval flow (DEV3 requests, DEV4 handles UI)
- Family attendance patterns (DEV3 tracks, DEV4 displays)

## Key Design Decisions

1. **Mobile-First Calendar**: All calendar views optimized for touch and small screens
2. **Optimistic Updates**: RSVP submissions show immediately with background sync
3. **Smart Defaults**: Event creation uses templates and previous event data
4. **Progressive Disclosure**: Complex features hidden until needed
5. **Offline Calendar View**: Basic calendar readable without connection
6. **Role-Based UI**: Different interfaces for coaches, parents, and players
7. **Batch Operations**: Support bulk actions for efficiency

## Success Metrics

### Performance
- Event creation completes in < 5 seconds [DEV1]
- Calendar month loads in < 1 second [DEV2]
- RSVP submission takes < 2 seconds [DEV3]
- Family calendar renders < 1.5 seconds with 5+ children [DEV4]

### User Experience
- Event creation requires < 5 fields for quick mode [DEV1]
- Calendar navigation uses familiar gestures [DEV2]
- RSVP requires single tap/click [DEV3]
- Bulk RSVP handles 5 children in one action [DEV4]

### Data Quality
- No double-bookings for players [DEV1]
- 100% RSVP status accuracy [DEV3]
- Calendar sync maintains full fidelity [DEV4]
- Attendance records match actual presence [DEV3]

### Business Metrics
- 90% RSVP response rate within 24 hours
- 50% reduction in no-shows
- 80% of parents using family calendar view
- 95% event creation using templates

## Migration Strategy

1. **Parallel Development**: Build alongside existing system without interference
2. **Data Migration**: Script to import existing events with full history
3. **Pilot Testing**: Select 2-3 teams for initial rollout
4. **Gradual Migration**: Move teams in batches with coach training
5. **Feature Flags**: Enable features progressively
6. **Fallback Plan**: Maintain old system for 30 days post-migration

## Security Considerations

1. **Authentication**: All endpoints require authenticated users
2. **Authorization**: Role-based access to event management
3. **Data Protection**: PII in events encrypted at rest
4. **Input Validation**: All date/time inputs validated for logical consistency
5. **Audit Logging**: All event changes logged with user attribution

## API Documentation

### Event Management API

#### POST /api/schedule/events

**Purpose**: Create a new event

**Authentication**: Coach or Admin role required

**Request**:
```json
{
  "teamId": "uuid",
  "eventType": "training",
  "title": "Weekly Practice",
  "startDateTime": "2024-03-15T18:00:00Z",
  "endDateTime": "2024-03-15T19:30:00Z",
  "locationId": "uuid",
  "rsvpRequired": true,
  "rsvpDeadline": "2024-03-14T18:00:00Z",
  "maxParticipants": 20
}
```

**Response** (201 Created):
```json
{
  "id": "uuid",
  "teamId": "uuid",
  "title": "Weekly Practice",
  "status": "scheduled",
  "createdAt": "2024-03-10T10:00:00Z"
}
```

**Error Responses**:
- `400 Bad Request`: Invalid date/time or missing required fields
- `401 Unauthorized`: User not authenticated
- `403 Forbidden`: User not authorized for this team
- `409 Conflict`: Scheduling conflict detected

### RSVP API

#### POST /api/schedule/events/:eventId/rsvp

**Purpose**: Submit or update RSVP

**Authentication**: Player or Parent role required

**Request**:
```json
{
  "playerId": "uuid",
  "response": "yes",
  "note": "Will be 10 minutes late"
}
```

**Response** (200 OK):
```json
{
  "eventId": "uuid",
  "playerId": "uuid",
  "rsvpStatus": "yes",
  "requiresParentApproval": false,
  "updatedAt": "2024-03-10T15:00:00Z"
}
```

## Out of Scope (Phase 1)

- Complex recurring event patterns
- External calendar two-way sync
- Advanced resource booking (fields, equipment)
- Tournament bracket management
- Live match scoring
- Weather-based cancellations
- Automated lineup generation
- Video integration for events

## Appendix

### Glossary

- **Event Series**: Related events that follow a pattern (future phase)
- **RSVP**: Répondez s'il vous plaît - player's response to event invitation
- **Bulk Operations**: Actions that affect multiple items at once
- **Calendar Subscription**: URL-based calendar that syncs to external apps
- **Waitlist**: Queue for events that have reached capacity

### References

#### Design Documents
- Schedule System Wireframes: /docs/design/schedule-wireframes.pdf
- Calendar Interaction Patterns: /docs/design/calendar-gestures.md

#### Related Features
- Assess System: Integration for evaluation events
- Identity System: User roles and permissions
- Notification System: Multi-channel message delivery

#### External Resources
- iCal Specification: https://tools.ietf.org/html/rfc5545
- Mobile Calendar Best Practices: /docs/research/mobile-calendars.md

---

## Document Metadata

**Last Updated**: 2024-03-10
**Status**: Approved
**Feature Owner**: Schedule Team
**Technical Lead**: TBD
**Document Version**: 1.0