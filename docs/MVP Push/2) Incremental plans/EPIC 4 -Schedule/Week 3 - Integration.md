# Schedule System Week 3: Integration Implementation Plan - Developer Work Split

## Developer Assignments

### DEV1 - Event Management Integration
**Focus**: Advanced event features and system integration
- Notification service integration
- Event status workflow management
- Recurring event data model preparation
- Admin event management tools
- Event change propagation system

### DEV2 - Calendar Export & Performance
**Focus**: Calendar export features and optimization
- Calendar export functionality (ICS, CSV, PDF)
- Print-optimized views
- Calendar subscription feed generation
- Touch gesture support
- Performance optimization for large datasets

### DEV3 - Attendance & Reporting
**Focus**: Complete attendance tracking system
- Full attendance tracking interface
- Late arrival and departure handling
- Absence reason management
- Attendance reports and analytics
- Coach attendance dashboard

### DEV4 - Calendar Sync & Transport
**Focus**: External calendar integration and transportation
- iOS/Android calendar sync
- Calendar subscription implementation
- Transportation coordination completion
- Family communication features
- Mobile experience optimization

---

## Overview

Week 3 focuses on integrating all components built in previous weeks and adding advanced features that require cross-system coordination. This week emphasizes performance optimization, external integrations, and completing the attendance tracking system.

The goal is to have a fully integrated schedule system where all features work seamlessly together, external calendar sync is functional, and coaches have comprehensive attendance tracking tools.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Page Flow and Purpose](#page-flow-and-purpose)
3. [Component Structure](#component-structure)
4. [Database Design](#database-design)
5. [Service Architecture](#service-architecture)
6. [Implementation Timeline](#implementation-timeline)
7. [Integration Points](#integration-points)
8. [Success Metrics](#success-metrics)

## Architecture Overview

### Directory Structure (Week 3 Additions)

```
src/features/schedule/
├── components/
│   ├── shared/
│   │   ├── NotificationBanner.tsx [DEV1] - Event notifications
│   │   ├── ExportDialog.tsx [DEV2] - Export options UI
│   │   ├── SyncStatus.tsx [DEV4] - Sync indicators
│   │   └── PerformanceMonitor.tsx [DEV2] - Performance metrics
│   ├── events/
│   │   ├── EventStatusManager.tsx [DEV1] - Status transitions
│   │   ├── RecurringEventPreview.tsx [DEV1] - Series preview
│   │   ├── ChangeNotification.tsx [DEV1] - Change alerts
│   │   └── AdminEventTools.tsx [DEV1] - Admin controls
│   ├── calendar/
│   │   ├── CalendarExport.tsx [DEV2] - Export interface
│   │   ├── PrintView.tsx [DEV2] - Print layout
│   │   ├── TouchGestures.tsx [DEV2] - Gesture handlers
│   │   └── SubscriptionManager.tsx [DEV2] - Feed management
│   ├── attendance/
│   │   ├── AttendanceSheet.tsx [DEV3] - Main interface
│   │   ├── LateArrivalModal.tsx [DEV3] - Late tracking
│   │   ├── AbsenceReasons.tsx [DEV3] - Reason capture
│   │   ├── AttendanceReports.tsx [DEV3] - Analytics
│   │   └── CoachDashboard.tsx [DEV3] - Coach view
│   └── family/
│       ├── CalendarSync.tsx [DEV4] - Sync interface
│       ├── TransportationHub.tsx [DEV4] - Transport center
│       ├── FamilyMessaging.tsx [DEV4] - Communication
│       └── MobileOptimized.tsx [DEV4] - Mobile views
├── pages/
│   ├── EventAdmin.tsx [DEV1] - Admin interface
│   ├── AttendanceTracking.tsx [DEV3] - Attendance page
│   ├── ExportCenter.tsx [DEV2] - Export hub
│   └── SyncSettings.tsx [DEV4] - Sync configuration
├── services/
│   ├── NotificationIntegration.ts [DEV1] - Notification hooks
│   ├── ExportService.ts [DEV2] - Export generation
│   ├── AttendanceService.ts [DEV3] - Attendance logic
│   └── SyncService.ts [DEV4] - Calendar sync
├── workers/
│   ├── exportWorker.ts [DEV2] - Background export
│   └── syncWorker.ts [DEV4] - Background sync
└── utils/
    ├── performanceUtils.ts [DEV2] - Performance helpers
    ├── attendanceCalculations.ts [DEV3] - Attendance math
    └── syncFormatters.ts [DEV4] - Calendar formatters
```

## Page Flow and Purpose

### Complete Feature Flow Diagram (Week 3 Focus)

```mermaid
graph TB
    Start([Schedule System]) --> Integration[Week 3 Integration]
    
    %% Notification Integration
    Integration --> Notifications[Notification System - DEV1]
    Notifications --> EventChange[Event Changes]
    EventChange --> NotifyAll[Notify Participants]
    
    %% Export Features
    Integration --> Export[Export System - DEV2]
    Export --> ExportType{Export Format}
    ExportType --> ICS[ICS Calendar]
    ExportType --> CSV[CSV Report]
    ExportType --> PDF[PDF Schedule]
    ExportType --> Feed[Subscribe Feed]
    
    %% Attendance System
    Integration --> Attendance[Attendance - DEV3]
    Attendance --> PreEvent[Pre-Event List]
    PreEvent --> LiveTracking[Live Tracking]
    LiveTracking --> PostEvent[Post-Event Report]
    
    %% Calendar Sync
    Integration --> Sync[Calendar Sync - DEV4]
    Sync --> DeviceCalendar[Device Calendar]
    DeviceCalendar --> TwoWaySync[Two-Way Sync]
    
    %% Transportation
    Sync --> Transport[Transportation - DEV4]
    Transport --> Offers[Driver Offers]
    Transport --> Requests[Ride Requests]
    Transport --> Matching[Auto-Match]
```

### Page Details

#### 1. Event Admin [DEV1]

**Purpose**: Comprehensive event management for administrators

**Path**: `/src/features/schedule/pages/EventAdmin.tsx`
**Route**: `/schedule/admin`

```typescript
/**
 * EventAdmin
 * 
 * PURPOSE:
 * - Manage all team events with admin privileges
 * - Handle event status transitions
 * - Configure notification settings
 * - Preview recurring event series
 * 
 * WEEK 3 GOALS:
 * - Full notification integration
 * - Status workflow enforcement
 * - Change propagation system
 * - Admin override capabilities
 */
```

**Week 3 Implementation**:
- EventStatusManager for workflow control
- Notification configuration interface
- Bulk event operations
- Change tracking and history
- Override permissions for admins

#### 2. Attendance Tracking [DEV3]

**Purpose**: Real-time attendance management on event day

**Path**: `/src/features/schedule/pages/AttendanceTracking.tsx`
**Route**: `/schedule/events/:eventId/attendance`

```typescript
/**
 * AttendanceTracking
 * 
 * PURPOSE:
 * - Track real-time attendance during events
 * - Handle late arrivals and early departures
 * - Capture absence reasons
 * - Generate attendance reports
 * 
 * WEEK 3 GOALS:
 * - Live attendance interface
 * - Quick marking system
 * - Reason capture modals
 * - Real-time statistics
 */
```

**Week 3 Implementation**:
- AttendanceSheet with player list
- Quick mark present/absent
- LateArrivalModal for timestamps
- AbsenceReasons dropdown
- Live attendance percentage

**New Components Required**:
```typescript
// Main attendance interface
import { AttendanceSheet } from '@/src/features/schedule/components/attendance/AttendanceSheet';

// Late arrival tracking
import { LateArrivalModal } from '@/src/features/schedule/components/attendance/LateArrivalModal';

// Absence management
import { AbsenceReasons } from '@/src/features/schedule/components/attendance/AbsenceReasons';
```

#### 3. Export Center [DEV2]

**Purpose**: Central hub for all export operations

**Path**: `/src/features/schedule/pages/ExportCenter.tsx`
**Route**: `/schedule/export`

```typescript
/**
 * ExportCenter
 * 
 * PURPOSE:
 * - Export calendar in multiple formats
 * - Generate printable schedules
 * - Create subscription feeds
 * - Manage export preferences
 * 
 * WEEK 3 GOALS:
 * - Multi-format export
 * - Print optimization
 * - Feed generation
 * - Background processing
 */
```

**Week 3 Implementation**:
- Format selection interface
- Date range picker
- Filter options for export
- Progress tracking
- Download management

#### 4. Sync Settings [DEV4]

**Purpose**: Configure calendar sync and manage subscriptions

**Path**: `/src/features/schedule/pages/SyncSettings.tsx`
**Route**: `/schedule/sync`

```typescript
/**
 * SyncSettings
 * 
 * PURPOSE:
 * - Configure device calendar sync
 * - Manage subscription preferences
 * - Set sync frequency
 * - Handle sync conflicts
 * 
 * WEEK 3 GOALS:
 * - Native calendar integration
 * - Subscription management
 * - Conflict resolution
 * - Sync status monitoring
 */
```

**Week 3 Implementation**:
- Platform detection (iOS/Android)
- Calendar permission handling
- Sync frequency settings
- Conflict resolution UI
- Sync history display

## Database Design

### Week 3 Schema Additions

```sql
-- Notification tracking [DEV1]
CREATE TABLE public.event_notifications (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id uuid NOT NULL REFERENCES public.events(id),
    notification_type varchar(50) NOT NULL, -- 'created', 'updated', 'cancelled', 'reminder'
    
    -- Recipients
    recipient_count integer NOT NULL,
    sent_count integer DEFAULT 0,
    failed_count integer DEFAULT 0,
    
    -- Change tracking
    changes jsonb, -- What changed for update notifications
    
    -- Delivery
    scheduled_at timestamp with time zone,
    started_at timestamp with time zone,
    completed_at timestamp with time zone,
    
    created_at timestamp with time zone DEFAULT now()
);

-- Attendance records [DEV3]
CREATE TABLE public.attendance_records (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id uuid NOT NULL REFERENCES public.events(id),
    player_id uuid NOT NULL REFERENCES public.profiles(id),
    
    -- Attendance details
    status varchar(20) NOT NULL, -- 'present', 'absent', 'late', 'left_early'
    arrival_time timestamp with time zone,
    departure_time timestamp with time zone,
    
    -- Absence handling
    absence_reason varchar(50), -- 'sick', 'injured', 'other', etc.
    absence_note text,
    
    -- Tracking
    marked_by uuid NOT NULL REFERENCES auth.users(id),
    marked_at timestamp with time zone DEFAULT now(),
    
    -- Late arrival
    late_reason varchar(50),
    minutes_late integer,
    
    UNIQUE(event_id, player_id)
);

-- Export jobs [DEV2]
CREATE TABLE public.export_jobs (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid NOT NULL REFERENCES auth.users(id),
    
    -- Job details
    export_type varchar(20) NOT NULL, -- 'ics', 'csv', 'pdf'
    status varchar(20) DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
    
    -- Parameters
    filters jsonb NOT NULL, -- Export filters
    date_range jsonb NOT NULL, -- Start and end dates
    
    -- Results
    file_url text,
    file_size integer,
    row_count integer,
    
    -- Timing
    created_at timestamp with time zone DEFAULT now(),
    started_at timestamp with time zone,
    completed_at timestamp with time zone,
    expires_at timestamp with time zone
);

-- Calendar sync status [DEV4]
CREATE TABLE public.calendar_sync_status (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid NOT NULL REFERENCES auth.users(id),
    
    -- Sync configuration
    sync_enabled boolean DEFAULT false,
    calendar_type varchar(20), -- 'ios', 'google', 'outlook'
    sync_token text UNIQUE,
    
    -- Sync state
    last_sync timestamp with time zone,
    next_sync timestamp with time zone,
    sync_frequency interval DEFAULT '15 minutes',
    
    -- Statistics
    events_synced integer DEFAULT 0,
    conflicts_resolved integer DEFAULT 0,
    errors_count integer DEFAULT 0,
    
    -- Filters
    sync_filters jsonb DEFAULT '{}',
    
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Transportation matches [DEV4]
CREATE TABLE public.transportation_matches (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    transportation_id uuid NOT NULL REFERENCES public.event_transportation(id),
    player_id uuid NOT NULL REFERENCES public.profiles(id),
    
    -- Match details
    requested_by uuid NOT NULL REFERENCES auth.users(id),
    confirmed boolean DEFAULT false,
    confirmed_at timestamp with time zone,
    
    -- Communication
    driver_notified boolean DEFAULT false,
    passenger_notified boolean DEFAULT false,
    
    created_at timestamp with time zone DEFAULT now(),
    
    UNIQUE(transportation_id, player_id)
);

-- Recurring event series (prep for future) [DEV1]
CREATE TABLE public.event_series (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id uuid NOT NULL REFERENCES public.teams(id),
    
    -- Series configuration
    pattern varchar(20) NOT NULL, -- 'daily', 'weekly', 'monthly'
    interval integer DEFAULT 1, -- Every N days/weeks/months
    days_of_week integer[], -- For weekly: [1,3,5] = Mon, Wed, Fri
    
    -- Template data
    template_event jsonb NOT NULL, -- Event data template
    
    -- Series bounds
    start_date date NOT NULL,
    end_date date,
    occurrence_count integer,
    
    -- Status
    is_active boolean DEFAULT true,
    created_by uuid NOT NULL REFERENCES auth.users(id),
    
    created_at timestamp with time zone DEFAULT now()
);
```

## Service Architecture

### NotificationIntegration Service [DEV1]

```typescript
// /src/features/schedule/services/NotificationIntegration.ts

export interface INotificationIntegration {
  // Event notifications
  notifyEventCreated(event: Event): Promise<void>;
  notifyEventUpdated(event: Event, changes: EventChanges): Promise<void>;
  notifyEventCancelled(event: Event, reason: string): Promise<void>;
  
  // Batch notifications
  notifyBulkChanges(events: Event[], changeType: string): Promise<void>;
  
  // Status tracking
  trackNotificationDelivery(notificationId: string): Promise<DeliveryStatus>;
  retryFailedNotifications(eventId: string): Promise<void>;
  
  // Configuration
  updateNotificationSettings(eventId: string, settings: NotificationSettings): Promise<void>;
  getNotificationHistory(eventId: string): Promise<NotificationHistory[]>;
}

export class NotificationIntegration implements INotificationIntegration {
  async notifyEventUpdated(event: Event, changes: EventChanges): Promise<void> {
    // Determine who needs notification
    const recipients = await this.getEventRecipients(event.id);
    
    // Create notification record
    const notification = await supabase
      .from('event_notifications')
      .insert({
        event_id: event.id,
        notification_type: 'updated',
        recipient_count: recipients.length,
        changes: changes,
        scheduled_at: new Date().toISOString()
      })
      .select()
      .single();
    
    // Queue notifications
    await this.queueNotifications(notification.data.id, recipients, {
      title: `Event Updated: ${event.title}`,
      changes: this.formatChanges(changes),
      eventDetails: this.formatEventDetails(event)
    });
    
    // Track delivery
    this.startDeliveryTracking(notification.data.id);
  }
  
  async notifyEventCancelled(event: Event, reason: string): Promise<void> {
    // Get all participants
    const participants = await this.getConfirmedParticipants(event.id);
    
    // High priority notification
    await this.sendImmediateNotification(participants, {
      priority: 'high',
      title: `Event Cancelled: ${event.title}`,
      message: `This event has been cancelled. Reason: ${reason}`,
      eventDate: event.start_datetime,
      actions: [{
        label: 'View Details',
        url: `/schedule/events/${event.id}`
      }]
    });
    
    // Update event status
    await this.updateEventStatus(event.id, 'cancelled');
  }
}
```

### ExportService [DEV2]

```typescript
// /src/features/schedule/services/ExportService.ts

export interface IExportService {
  // Export generation
  exportToICS(teamId: string, filters: ExportFilters): Promise<ExportJob>;
  exportToCSV(teamId: string, filters: ExportFilters): Promise<ExportJob>;
  exportToPDF(teamId: string, filters: ExportFilters): Promise<ExportJob>;
  
  // Subscription feeds
  generateSubscriptionFeed(userId: string, filters: FeedFilters): Promise<string>;
  updateSubscriptionFeed(feedId: string): Promise<void>;
  
  // Job management
  getExportJobStatus(jobId: string): Promise<ExportJobStatus>;
  cancelExportJob(jobId: string): Promise<void>;
  cleanupExpiredExports(): Promise<void>;
  
  // Print optimization
  generatePrintView(events: Event[], layout: PrintLayout): Promise<string>;
}

export class ExportService implements IExportService {
  async exportToICS(teamId: string, filters: ExportFilters): Promise<ExportJob> {
    // Create export job
    const job = await this.createExportJob('ics', teamId, filters);
    
    // Start background processing
    this.processInBackground(async () => {
      // Fetch events
      const events = await this.getFilteredEvents(teamId, filters);
      
      // Generate ICS content
      const icsContent = this.generateICSContent(events);
      
      // Upload to storage
      const fileUrl = await this.uploadExport(
        `exports/${job.id}.ics`,
        icsContent,
        'text/calendar'
      );
      
      // Update job status
      await this.updateJobStatus(job.id, {
        status: 'completed',
        file_url: fileUrl,
        file_size: Buffer.byteLength(icsContent),
        row_count: events.length
      });
    });
    
    return job;
  }
  
  generateICSContent(events: Event[]): string {
    const ics = new ICSGenerator();
    
    ics.addHeader({
      prodId: 'SHOT App',
      version: '2.0',
      calScale: 'GREGORIAN'
    });
    
    events.forEach(event => {
      ics.addEvent({
        uid: event.id,
        summary: event.title,
        description: event.description,
        dtstart: event.start_datetime,
        dtend: event.end_datetime,
        location: event.location_name,
        organizer: event.created_by,
        attendees: event.participants?.map(p => p.email) || []
      });
    });
    
    return ics.toString();
  }
  
  async generateSubscriptionFeed(userId: string, filters: FeedFilters): Promise<string> {
    // Generate unique feed URL
    const feedToken = await this.generateSecureFeedToken();
    
    // Save subscription configuration
    await supabase
      .from('calendar_subscriptions')
      .insert({
        user_id: userId,
        subscription_url: `${BASE_URL}/api/calendar/feed/${feedToken}`,
        sync_token: feedToken,
        filters: filters
      });
    
    return `${BASE_URL}/api/calendar/feed/${feedToken}`;
  }
}
```

### AttendanceService [DEV3]

```typescript
// /src/features/schedule/services/AttendanceService.ts

export interface IAttendanceService {
  // Attendance tracking
  getEventAttendanceList(eventId: string): Promise<AttendanceList>;
  markAttendance(eventId: string, playerId: string, status: AttendanceStatus): Promise<void>;
  bulkMarkAttendance(eventId: string, attendance: BulkAttendance[]): Promise<void>;
  
  // Late/early handling
  recordLateArrival(eventId: string, playerId: string, arrivalTime: Date, reason?: string): Promise<void>;
  recordEarlyDeparture(eventId: string, playerId: string, departureTime: Date): Promise<void>;
  
  // Absence management
  recordAbsence(eventId: string, playerId: string, reason: AbsenceReason, note?: string): Promise<void>;
  getAbsenceReasons(): Promise<AbsenceReason[]>;
  
  // Reports
  generateAttendanceReport(eventId: string): Promise<AttendanceReport>;
  getPlayerAttendanceHistory(playerId: string, dateRange: DateRange): Promise<AttendanceHistory>;
  getTeamAttendanceStats(teamId: string, dateRange: DateRange): Promise<AttendanceStats>;
  
  // Real-time updates
  subscribeToAttendanceUpdates(eventId: string, callback: (update: AttendanceUpdate) => void): () => void;
}

export class AttendanceService implements IAttendanceService {
  async getEventAttendanceList(eventId: string): Promise<AttendanceList> {
    // Get event details
    const event = await this.getEvent(eventId);
    
    // Get expected participants
    const { data: participants } = await supabase
      .from('event_participants')
      .select(`
        player_id,
        rsvp_status,
        profiles!inner(
          id,
          first_name,
          last_name,
          jersey_number
        )
      `)
      .eq('event_id', eventId)
      .in('rsvp_status', ['yes', 'maybe']);
    
    // Get existing attendance records
    const { data: attendance } = await supabase
      .from('attendance_records')
      .select('*')
      .eq('event_id', eventId);
    
    // Merge data
    return this.mergeAttendanceData(participants, attendance, event);
  }
  
  async markAttendance(eventId: string, playerId: string, status: AttendanceStatus): Promise<void> {
    const now = new Date();
    const event = await this.getEvent(eventId);
    
    // Calculate if late
    const isLate = status === 'present' && now > event.start_datetime;
    const minutesLate = isLate ? 
      Math.floor((now - event.start_datetime) / 60000) : 0;
    
    // Upsert attendance record
    await supabase
      .from('attendance_records')
      .upsert({
        event_id: eventId,
        player_id: playerId,
        status: status,
        arrival_time: status === 'present' ? now : null,
        marked_by: await this.getCurrentUserId(),
        marked_at: now,
        minutes_late: minutesLate
      });
    
    // Emit real-time update
    this.emitAttendanceUpdate(eventId, {
      playerId,
      status,
      timestamp: now
    });
  }
  
  async generateAttendanceReport(eventId: string): Promise<AttendanceReport> {
    const attendance = await this.getEventAttendanceList(eventId);
    
    const stats = {
      total: attendance.players.length,
      present: attendance.players.filter(p => p.status === 'present').length,
      absent: attendance.players.filter(p => p.status === 'absent').length,
      late: attendance.players.filter(p => p.minutes_late > 0).length,
      leftEarly: attendance.players.filter(p => p.status === 'left_early').length
    };
    
    const attendanceRate = (stats.present / stats.total) * 100;
    const punctualityRate = ((stats.present - stats.late) / stats.present) * 100;
    
    return {
      eventId,
      eventTitle: attendance.event.title,
      eventDate: attendance.event.start_datetime,
      statistics: stats,
      attendanceRate,
      punctualityRate,
      absentees: attendance.players
        .filter(p => p.status === 'absent')
        .map(p => ({
          player: p.profile,
          reason: p.absence_reason,
          note: p.absence_note
        })),
      lateArrivals: attendance.players
        .filter(p => p.minutes_late > 0)
        .map(p => ({
          player: p.profile,
          minutesLate: p.minutes_late,
          reason: p.late_reason
        }))
    };
  }
}
```

### SyncService [DEV4]

```typescript
// /src/features/schedule/services/SyncService.ts

export interface ISyncService {
  // Calendar sync
  setupCalendarSync(userId: string, calendarType: CalendarType): Promise<SyncSetup>;
  syncToDeviceCalendar(userId: string): Promise<SyncResult>;
  syncFromDeviceCalendar(userId: string): Promise<SyncResult>;
  
  // Subscription management
  getSubscriptionUrl(userId: string): Promise<string>;
  refreshSubscription(userId: string): Promise<void>;
  
  // Conflict resolution
  detectSyncConflicts(userId: string): Promise<SyncConflict[]>;
  resolveSyncConflict(conflictId: string, resolution: ConflictResolution): Promise<void>;
  
  // Transport coordination
  matchTransportation(eventId: string): Promise<TransportMatch[]>;
  confirmTransportMatch(matchId: string): Promise<void>;
  notifyTransportParties(matchId: string): Promise<void>;
}

export class SyncService implements ISyncService {
  async setupCalendarSync(userId: string, calendarType: CalendarType): Promise<SyncSetup> {
    // Check platform capabilities
    const platform = await this.detectPlatform();
    
    if (calendarType === 'ios' && platform !== 'ios') {
      throw new Error('iOS calendar sync only available on iOS devices');
    }
    
    // Request calendar permissions
    const permissions = await this.requestCalendarPermissions();
    if (!permissions.granted) {
      throw new Error('Calendar permissions required');
    }
    
    // Generate sync token
    const syncToken = await this.generateSyncToken();
    
    // Save sync configuration
    const { data: syncConfig } = await supabase
      .from('calendar_sync_status')
      .upsert({
        user_id: userId,
        sync_enabled: true,
        calendar_type: calendarType,
        sync_token: syncToken,
        last_sync: new Date().toISOString()
      })
      .select()
      .single();
    
    // Initial sync
    await this.performInitialSync(userId, syncConfig);
    
    return {
      success: true,
      syncToken,
      calendarType,
      nextSync: this.calculateNextSync(syncConfig)
    };
  }
  
  async syncToDeviceCalendar(userId: string): Promise<SyncResult> {
    const syncConfig = await this.getSyncConfig(userId);
    const events = await this.getUserEvents(userId, syncConfig.sync_filters);
    
    const results = {
      created: 0,
      updated: 0,
      deleted: 0,
      errors: []
    };
    
    for (const event of events) {
      try {
        const deviceEventId = await this.getDeviceEventId(event.id);
        
        if (deviceEventId) {
          // Update existing
          await this.updateDeviceEvent(deviceEventId, event);
          results.updated++;
        } else {
          // Create new
          const newId = await this.createDeviceEvent(event);
          await this.saveEventMapping(event.id, newId);
          results.created++;
        }
      } catch (error) {
        results.errors.push({
          eventId: event.id,
          error: error.message
        });
      }
    }
    
    // Update sync status
    await this.updateSyncStatus(userId, {
      last_sync: new Date(),
      events_synced: results.created + results.updated,
      errors_count: results.errors.length
    });
    
    return results;
  }
  
  async matchTransportation(eventId: string): Promise<TransportMatch[]> {
    // Get available drivers
    const { data: drivers } = await supabase
      .from('event_transportation')
      .select('*')
      .eq('event_id', eventId)
      .gt('seats_available', 0);
    
    // Get riders needing transport
    const { data: riders } = await supabase
      .from('event_participants')
      .select(`
        player_id,
        profiles!inner(*)
      `)
      .eq('event_id', eventId)
      .eq('needs_transport', true)
      .is('transport_arranged', false);
    
    // Perform matching algorithm
    const matches = this.performTransportMatching(drivers, riders);
    
    // Save matches
    for (const match of matches) {
      await supabase
        .from('transportation_matches')
        .insert({
          transportation_id: match.driverId,
          player_id: match.riderId,
          requested_by: await this.getCurrentUserId()
        });
    }
    
    return matches;
  }
}
```

## Implementation Timeline

### Day 1-2: Integration Foundation

**DEV1 (Event Management)**:
- Set up notification integration service
- Create event status workflow system
- Build notification tracking tables
- Implement change detection logic
- Create admin interface skeleton

**DEV2 (Calendar System)**:
- Build export service architecture
- Create ICS generation logic
- Set up background job system
- Implement print view templates
- Create touch gesture handlers

**DEV3 (Attendance)**:
- Create attendance tables and service
- Build attendance sheet component
- Implement quick marking system
- Set up real-time updates
- Create absence reason structure

**DEV4 (Family Experience)**:
- Build sync service foundation
- Create calendar permission handlers
- Set up transport matching tables
- Implement device detection
- Create sync configuration UI

### Day 3-4: Feature Completion

**DEV1 (Event Management)**:
- Complete notification workflows
- Implement status transitions
- Build admin override tools
- Create change propagation
- Test notification delivery

**DEV2 (Calendar System)**:
- Complete all export formats
- Implement subscription feeds
- Optimize print layouts
- Add gesture navigation
- Performance tune large datasets

**DEV3 (Attendance)**:
- Complete attendance interface
- Build late arrival modals
- Implement absence tracking
- Create attendance reports
- Add coach dashboard view

**DEV4 (Family Experience)**:
- Complete calendar sync
- Build conflict resolution
- Finish transport matching
- Create family messaging
- Optimize mobile experience

### Day 5: System Integration

**All Developers**:
- Full system integration testing
- Cross-feature workflow testing
- Performance optimization
- Mobile testing
- Documentation updates

## Integration Points

### DEV1 ↔ DEV2
- Export includes notification history
- Print view shows event status
- Subscription feeds include changes
- Admin tools affect all views

### DEV1 ↔ DEV3
- Notifications for attendance reminders
- Status affects attendance eligibility
- Admin can override attendance
- Changes notify attendance takers

### DEV1 ↔ DEV4
- Sync includes notification preferences
- Family gets change notifications
- Transport changes trigger notifications
- Admin affects family visibility

### DEV2 ↔ DEV3
- Export includes attendance data
- Print view has attendance sheets
- Reports exportable in all formats
- Performance shared for large data

### DEV2 ↔ DEV4
- Calendar sync uses export format
- Subscription compatible with devices
- Print view family-friendly
- Mobile gestures consistent

### DEV3 ↔ DEV4
- Family attendance coordination
- Transport affects attendance
- Sync includes attendance status
- Mobile attendance marking

## Key Design Decisions

1. **Background Processing**: Heavy operations run async
2. **Real-time Updates**: WebSocket for live features
3. **Progressive Enhancement**: Features degrade gracefully
4. **Offline Support**: Critical features work offline
5. **Platform Native**: Use device capabilities when available
6. **Performance First**: Optimize for large teams

## Success Metrics

### Performance
- Export generation < 10 seconds for 100 events [DEV2]
- Attendance marking < 500ms per player [DEV3]
- Calendar sync < 5 seconds for month [DEV4]
- Notification delivery < 2 seconds [DEV1]

### User Experience
- One-tap attendance marking [DEV3]
- Export in 3 clicks or less [DEV2]
- Automatic transport matching [DEV4]
- Clear change notifications [DEV1]

### Data Quality
- 100% notification delivery tracking [DEV1]
- Accurate attendance timestamps [DEV3]
- Lossless calendar sync [DEV4]
- Complete export data [DEV2]

### Integration
- All features work together seamlessly
- No data inconsistencies between systems
- Smooth workflow transitions
- Unified user experience

## Risk Mitigation

### Technical Risks
- **Platform limitations**: Graceful degradation
- **Sync conflicts**: Clear resolution UI
- **Performance at scale**: Pagination and caching
- **Network reliability**: Offline queue

### Integration Risks
- **Feature conflicts**: Clear ownership boundaries
- **Data consistency**: Transaction management
- **User confusion**: Progressive disclosure
- **Mobile limitations**: Adaptive UI

## Testing Requirements

### Integration Tests
- Full event lifecycle with all features
- Export with complex filters
- Attendance with notifications
- Calendar sync round-trip

### Performance Tests
- Large dataset exports
- Concurrent attendance marking
- Multiple device sync
- Notification delivery at scale

### User Journey Tests
- Coach creates event → players sync
- Event changes → everyone notified
- Attendance taken → reports generated
- Transport arranged → confirmed

## Next Week Preview

Week 4 will focus on:
- Final polish and UI refinement
- Comprehensive testing
- Performance optimization
- Production deployment preparation
- User documentation

---

## Document Metadata

**Last Updated**: 2024-03-10
**Status**: Ready for Implementation
**Week**: 3 of 4
**Feature**: Schedule System
**Focus**: Integration