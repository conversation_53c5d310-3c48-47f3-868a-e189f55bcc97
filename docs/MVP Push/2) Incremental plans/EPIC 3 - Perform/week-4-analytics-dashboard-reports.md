# Week 4: Analytics Dashboard & Reports Implementation Plan - Developer Work Split

## Developer Assignments

### DEV1 - Data Aggregation & Analytics Engine
**Focus**: Backend analytics processing and data aggregation
- Analytics data pipeline architecture
- Aggregation algorithms and calculations
- Performance optimization for large datasets
- Real-time data processing services

### DEV2 - Dashboard UI & Visualizations
**Focus**: Interactive dashboard components and charts
- Main analytics dashboard layout
- Interactive chart components
- Real-time data visualization
- Responsive dashboard design

### DEV3 - Report Generation System
**Focus**: Report creation, templates, and export functionality
- Report template engine
- PDF/Excel generation services
- Scheduled report automation
- Custom report builder

### DEV4 - Integration & Performance
**Focus**: System integration and performance optimization
- Dashboard data caching layer
- API optimization for analytics
- Export queue management
- Performance monitoring

---

## Overview

Week 4 completes the Perform system by implementing comprehensive analytics dashboards and reporting capabilities. This enables coaches to gain insights from the data collected in previous weeks, identify trends, make data-driven decisions, and share progress with stakeholders through professional reports.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Page Flow and Purpose](#page-flow-and-purpose)
3. [Component Structure](#component-structure)
4. [Database Design](#database-design)
5. [Service Architecture](#service-architecture)
6. [Implementation Timeline](#implementation-timeline)
7. [Integration Points](#integration-points)
8. [Success Metrics](#success-metrics)

## Architecture Overview

### Directory Structure

```
src/features/perform/
├── components/
│   ├── analytics/
│   │   ├── DashboardLayout.tsx [DEV2]
│   │   ├── MetricCard.tsx [DEV2]
│   │   ├── TrendChart.tsx [DEV2]
│   │   ├── ComparisonChart.tsx [DEV2]
│   │   ├── DistributionChart.tsx [DEV2]
│   │   ├── HeatmapGrid.tsx [DEV2]
│   │   └── FilterPanel.tsx [DEV2]
│   ├── reports/
│   │   ├── ReportBuilder.tsx [DEV3]
│   │   ├── ReportPreview.tsx [DEV3]
│   │   ├── TemplateSelector.tsx [DEV3]
│   │   ├── ExportOptions.tsx [DEV3]
│   │   └── ScheduleManager.tsx [DEV3]
│   └── insights/
│       ├── InsightCard.tsx [DEV2]
│       ├── AlertNotification.tsx [DEV2]
│       ├── RecommendationPanel.tsx [DEV2]
│       └── PerformanceIndicator.tsx [DEV2]
├── pages/
│   ├── AnalyticsDashboard.tsx [DEV2]
│   ├── TeamInsights.tsx [DEV2]
│   ├── ReportCenter.tsx [DEV3]
│   └── ExportManager.tsx [DEV3]
├── hooks/
│   ├── useAnalytics.ts [DEV1]
│   ├── useDashboard.ts [DEV2]
│   ├── useReports.ts [DEV3]
│   └── useExport.ts [DEV4]
├── services/
│   ├── AnalyticsService.ts [DEV1]
│   ├── DashboardService.ts [DEV2]
│   ├── ReportService.ts [DEV3]
│   └── ExportService.ts [DEV4]
├── utils/
│   ├── calculations.ts [DEV1]
│   ├── chartHelpers.ts [DEV2]
│   ├── reportGenerators.ts [DEV3]
│   └── exportFormatters.ts [DEV4]
└── types/
    ├── analytics.types.ts [DEV1]
    ├── dashboard.types.ts [DEV2]
    ├── report.types.ts [DEV3]
    └── export.types.ts [DEV4]
```

## Page Flow and Purpose

### Complete Feature Flow Diagram

```mermaid
graph TB
    Start([Coach Login]) --> MainDash[Analytics Dashboard - DEV2]
    
    %% Dashboard Navigation
    MainDash --> |Team Overview| TeamInsights[Team Insights - DEV2]
    MainDash --> |Individual Analysis| PlayerAnalytics[Player Analytics - DEV2]
    MainDash --> |Generate Reports| ReportCenter[Report Center - DEV3]
    MainDash --> |Export Data| ExportManager[Export Manager - DEV3]
    
    %% Analytics Flow
    TeamInsights --> |Filter Data| FilterPanel[Filter Panel - DEV2]
    FilterPanel --> |Apply Filters| UpdatedView[Updated Analytics - DEV1]
    UpdatedView --> |Drill Down| DetailView[Detailed View - DEV2]
    
    %% Report Generation Flow
    ReportCenter --> |Select Template| TemplateSelector[Template Selection - DEV3]
    TemplateSelector --> |Customize| ReportBuilder[Report Builder - DEV3]
    ReportBuilder --> |Preview| ReportPreview[Preview Report - DEV3]
    ReportPreview --> |Generate| GenerateReport{Format? - DEV3}
    GenerateReport --> |PDF| PDFGen[PDF Generation - DEV3]
    GenerateReport --> |Excel| ExcelGen[Excel Generation - DEV3]
    GenerateReport --> |Email| EmailReport[Email Report - DEV4]
    
    %% Export Flow
    ExportManager --> |Select Data| DataSelection[Data Selection - DEV4]
    DataSelection --> |Choose Format| FormatSelect[Format Selection - DEV4]
    FormatSelect --> |Process| ExportQueue[Export Queue - DEV4]
    ExportQueue --> |Download| DownloadReady[Download Ready - DEV4]
    
    %% Real-time Updates
    MainDash --> |Live Data| RealTime[Real-time Updates - DEV1]
    RealTime --> |Push Updates| UpdateCharts[Update Charts - DEV2]
    
    %% Scheduled Reports
    ReportCenter --> |Schedule| ScheduleManager[Schedule Manager - DEV3]
    ScheduleManager --> |Automated| AutoReports[Automated Reports - DEV4]
```

### Page Details

#### 1. Analytics Dashboard [DEV2]

**Purpose**: Central hub for all performance analytics and insights

**Path**: `/src/features/perform/pages/AnalyticsDashboard.tsx`
**Route**: `/coach/perform/analytics`

```typescript
/**
 * AnalyticsDashboard
 * 
 * PURPOSE:
 * - Provide comprehensive team performance overview
 * - Display key metrics and trends
 * - Enable quick insights and decision making
 * 
 * USER GOALS:
 * - See team performance at a glance
 * - Identify trends and patterns
 * - Make data-driven training decisions
 */
```

**New Components Required:**
```typescript
// Dashboard layout
import { DashboardLayout } from '@/src/features/perform/components/analytics/DashboardLayout';
// Path: /src/features/perform/components/analytics/DashboardLayout.tsx

// Metric displays
import { MetricCard } from '@/src/features/perform/components/analytics/MetricCard';
import { TrendChart } from '@/src/features/perform/components/analytics/TrendChart';
import { TeamHeatmap } from '@/src/features/perform/components/analytics/TeamHeatmap';

// Insights
import { InsightPanel } from '@/src/features/perform/components/insights/InsightPanel';
import { AlertNotification } from '@/src/features/perform/components/insights/AlertNotification';
```

**Services Used:**
```typescript
import { AnalyticsService } from '@/src/features/perform/services/AnalyticsService';
// Methods: 
// - getTeamAnalytics(teamId: string, period: DateRange): Promise<TeamAnalytics>
// - getKeyMetrics(teamId: string): Promise<KeyMetrics>
// - getInsights(teamId: string): Promise<Insight[]>
// - getAlerts(teamId: string): Promise<Alert[]>
```

**Hooks Used:**
```typescript
import { useAnalytics } from '@/src/features/perform/hooks/useAnalytics';
// Returns: { data, loading, error, refresh, filters, setFilters }

import { useDashboard } from '@/src/features/perform/hooks/useDashboard';
// Returns: { layout, widgets, updateWidget, saveLayout }
```

#### 2. Team Insights [DEV2]

**Purpose**: Deep dive into team performance patterns

**Path**: `/src/features/perform/pages/TeamInsights.tsx`
**Route**: `/coach/perform/analytics/team`

```typescript
/**
 * TeamInsights
 * 
 * PURPOSE:
 * - Analyze team-wide performance trends
 * - Compare players and identify standouts
 * - Track collective progress
 * 
 * USER GOALS:
 * - Understand team dynamics
 * - Identify training effectiveness
 * - Plan team development strategies
 */
```

**New Components Required:**
```typescript
// Team analysis components
import { PlayerComparisonGrid } from '@/src/features/perform/components/analytics/PlayerComparisonGrid';
import { TeamProgressChart } from '@/src/features/perform/components/analytics/TeamProgressChart';
import { DistributionChart } from '@/src/features/perform/components/analytics/DistributionChart';
import { PerformanceMatrix } from '@/src/features/perform/components/analytics/PerformanceMatrix';
```

**Services Used:**
```typescript
import { AnalyticsService } from '@/src/features/perform/services/AnalyticsService';
// Methods: 
// - getTeamComparison(teamId: string, metrics: string[]): Promise<ComparisonData>
// - getTeamDistribution(teamId: string, metric: string): Promise<Distribution>
// - getCorrelations(teamId: string): Promise<CorrelationMatrix>
```

#### 3. Report Center [DEV3]

**Purpose**: Create, customize, and generate professional reports

**Path**: `/src/features/perform/pages/ReportCenter.tsx`
**Route**: `/coach/perform/reports`

```typescript
/**
 * ReportCenter
 * 
 * PURPOSE:
 * - Generate customized performance reports
 * - Schedule automated reports
 * - Share insights with stakeholders
 * 
 * USER GOALS:
 * - Create professional reports quickly
 * - Automate regular reporting
 * - Communicate progress effectively
 */
```

**New Components Required:**
```typescript
// Report components
import { ReportTemplateGallery } from '@/src/features/perform/components/reports/ReportTemplateGallery';
import { ReportBuilder } from '@/src/features/perform/components/reports/ReportBuilder';
import { ReportPreview } from '@/src/features/perform/components/reports/ReportPreview';
import { ScheduleManager } from '@/src/features/perform/components/reports/ScheduleManager';
```

**Services Used:**
```typescript
import { ReportService } from '@/src/features/perform/services/ReportService';
// Methods: 
// - getTemplates(): Promise<ReportTemplate[]>
// - generateReport(config: ReportConfig): Promise<Report>
// - scheduleReport(schedule: ReportSchedule): Promise<ScheduledReport>
// - getReportHistory(teamId: string): Promise<Report[]>
```

#### 4. Export Manager [DEV3]

**Purpose**: Export data in various formats for external use

**Path**: `/src/features/perform/pages/ExportManager.tsx`
**Route**: `/coach/perform/export`

```typescript
/**
 * ExportManager
 * 
 * PURPOSE:
 * - Export performance data in multiple formats
 * - Enable data portability
 * - Support external analysis tools
 * 
 * USER GOALS:
 * - Extract data for presentations
 * - Share data with other systems
 * - Create backups of performance data
 */
```

**New Components Required:**
```typescript
// Export components
import { DataSelector } from '@/src/features/perform/components/reports/DataSelector';
import { FormatOptions } from '@/src/features/perform/components/reports/FormatOptions';
import { ExportQueue } from '@/src/features/perform/components/reports/ExportQueue';
import { DownloadManager } from '@/src/features/perform/components/reports/DownloadManager';
```

**Services Used:**
```typescript
import { ExportService } from '@/src/features/perform/services/ExportService';
// Methods: 
// - exportData(config: ExportConfig): Promise<ExportJob>
// - getExportStatus(jobId: string): Promise<ExportStatus>
// - downloadExport(jobId: string): Promise<Blob>
// - getExportHistory(): Promise<ExportJob[]>
```

## Database Design

### Analytics Cache Tables [DEV1]

```sql
-- Pre-calculated analytics for performance
CREATE TABLE analytics_cache (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Cache identification
    cache_key varchar(255) UNIQUE NOT NULL,
    cache_type varchar(50) NOT NULL, -- 'team_metrics', 'player_comparison', 'trends'
    entity_id uuid NOT NULL, -- team_id or player_id
    
    -- Cache data
    data jsonb NOT NULL,
    calculation_params jsonb, -- parameters used for calculation
    
    -- Time range
    period_start date NOT NULL,
    period_end date NOT NULL,
    
    -- Cache management
    created_at timestamp with time zone DEFAULT now(),
    expires_at timestamp with time zone NOT NULL,
    hit_count integer DEFAULT 0,
    last_accessed timestamp with time zone,
    
    -- Indexes
    INDEX idx_cache_key_expires (cache_key, expires_at),
    INDEX idx_cache_entity_type (entity_id, cache_type),
    INDEX idx_cache_expires (expires_at)
);

-- Team analytics summary
CREATE TABLE team_analytics_summary (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    team_id uuid NOT NULL REFERENCES teams(id),
    
    -- Summary period
    period_type varchar(50) NOT NULL, -- 'daily', 'weekly', 'monthly', 'seasonal'
    period_start date NOT NULL,
    period_end date NOT NULL,
    
    -- Aggregated metrics
    total_sessions integer DEFAULT 0,
    total_metrics_recorded integer DEFAULT 0,
    active_players integer DEFAULT 0,
    
    -- Performance indicators
    avg_attendance_rate decimal(5,2),
    avg_session_intensity decimal(3,1),
    goal_completion_rate decimal(5,2),
    
    -- Key metrics summary
    metrics_summary jsonb NOT NULL, -- {metric_type: {avg, min, max, trend}}
    
    -- Top performers
    top_performers jsonb, -- [{player_id, metric_type, value}]
    
    -- Insights
    auto_insights jsonb, -- AI-generated insights
    
    -- Timestamps
    calculated_at timestamp with time zone DEFAULT now(),
    
    -- Indexes
    INDEX idx_team_summary_period (team_id, period_type, period_start DESC),
    UNIQUE(team_id, period_type, period_start)
);
```

### Report Configuration [DEV3]

```sql
-- Report templates
CREATE TABLE report_templates (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Template identification
    name varchar(255) NOT NULL,
    description text,
    category varchar(50) NOT NULL, -- 'performance', 'progress', 'comparison', 'custom'
    
    -- Template configuration
    layout_config jsonb NOT NULL, -- sections, components, styling
    data_config jsonb NOT NULL, -- which metrics, time periods, filters
    
    -- Customization options
    customizable_fields jsonb, -- fields users can modify
    required_data text[], -- minimum data requirements
    
    -- Availability
    is_public boolean DEFAULT true,
    created_by uuid REFERENCES profiles(id),
    organization_id uuid REFERENCES organizations(id),
    
    -- Usage tracking
    usage_count integer DEFAULT 0,
    last_used timestamp with time zone,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Generated reports
CREATE TABLE generated_reports (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Report identification
    team_id uuid NOT NULL REFERENCES teams(id),
    generated_by uuid NOT NULL REFERENCES profiles(id),
    template_id uuid REFERENCES report_templates(id),
    
    -- Report details
    title varchar(255) NOT NULL,
    report_type varchar(50) NOT NULL,
    format varchar(20) NOT NULL, -- 'pdf', 'excel', 'html'
    
    -- Time period
    period_start date NOT NULL,
    period_end date NOT NULL,
    
    -- Content
    report_data jsonb NOT NULL,
    file_url text, -- if stored as file
    file_size integer,
    
    -- Distribution
    recipients text[],
    sent_at timestamp with time zone,
    
    -- Status
    status varchar(50) DEFAULT 'generating', -- 'generating', 'completed', 'failed', 'sent'
    error_message text,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    completed_at timestamp with time zone,
    
    -- Indexes
    INDEX idx_reports_team_date (team_id, created_at DESC),
    INDEX idx_reports_status (status) WHERE status != 'completed'
);

-- Scheduled reports
CREATE TABLE scheduled_reports (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Schedule identification
    team_id uuid NOT NULL REFERENCES teams(id),
    created_by uuid NOT NULL REFERENCES profiles(id),
    template_id uuid NOT NULL REFERENCES report_templates(id),
    
    -- Schedule configuration
    schedule_name varchar(255) NOT NULL,
    frequency varchar(50) NOT NULL, -- 'daily', 'weekly', 'monthly', 'custom'
    cron_expression varchar(100), -- for custom schedules
    
    -- Report configuration
    report_config jsonb NOT NULL, -- overrides for template
    recipients text[] NOT NULL,
    
    -- Time settings
    next_run timestamp with time zone NOT NULL,
    last_run timestamp with time zone,
    time_zone varchar(50) DEFAULT 'UTC',
    
    -- Status
    is_active boolean DEFAULT true,
    failure_count integer DEFAULT 0,
    last_error text,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    -- Indexes
    INDEX idx_scheduled_active_next (is_active, next_run) WHERE is_active = true
);
```

### Export Jobs [DEV4]

```sql
-- Export job queue
CREATE TABLE export_jobs (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Job identification
    requested_by uuid NOT NULL REFERENCES profiles(id),
    team_id uuid NOT NULL REFERENCES teams(id),
    
    -- Export configuration
    export_type varchar(50) NOT NULL, -- 'metrics', 'sessions', 'full_backup'
    format varchar(20) NOT NULL, -- 'csv', 'excel', 'json'
    filters jsonb, -- date range, metric types, players
    
    -- Processing
    status varchar(50) DEFAULT 'queued', -- 'queued', 'processing', 'completed', 'failed'
    progress integer DEFAULT 0, -- 0-100
    
    -- Output
    file_url text,
    file_size integer,
    row_count integer,
    
    -- Error handling
    error_message text,
    retry_count integer DEFAULT 0,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    started_at timestamp with time zone,
    completed_at timestamp with time zone,
    expires_at timestamp with time zone, -- for cleanup
    
    -- Indexes
    INDEX idx_export_status (status, created_at) WHERE status IN ('queued', 'processing'),
    INDEX idx_export_user (requested_by, created_at DESC)
);
```

## Service Architecture

### AnalyticsService [DEV1]

```typescript
// /src/features/perform/services/AnalyticsService.ts

export interface AnalyticsService {
  // Team analytics
  getTeamAnalytics(teamId: string, period: DateRange): Promise<TeamAnalytics>;
  getTeamMetricSummary(teamId: string, metrics: string[]): Promise<MetricSummary>;
  getTeamTrends(teamId: string, options: TrendOptions): Promise<TrendData[]>;
  
  // Player analytics
  getPlayerAnalytics(playerId: string, period: DateRange): Promise<PlayerAnalytics>;
  compareePlayers(playerIds: string[], metrics: string[]): Promise<ComparisonData>;
  getPlayerRankings(teamId: string, metric: string): Promise<Ranking[]>;
  
  // Advanced analytics
  getCorrelations(teamId: string, metrics: string[]): Promise<CorrelationMatrix>;
  getPredictions(teamId: string, targetMetric: string): Promise<Prediction[]>;
  getAnomalies(teamId: string, sensitivity?: number): Promise<Anomaly[]>;
  
  // Insights generation
  generateInsights(teamId: string): Promise<Insight[]>;
  getKeyPerformanceIndicators(teamId: string): Promise<KPI[]>;
  getRecommendations(teamId: string): Promise<Recommendation[]>;
  
  // Cache management
  warmCache(teamId: string): Promise<void>;
  invalidateCache(cacheKey: string): Promise<void>;
  getCacheStatus(): Promise<CacheStatus>;
}

export class AnalyticsServiceImpl implements AnalyticsService {
  constructor(
    private supabase: SupabaseClient,
    private metricService: MetricService,
    private sessionService: SessionService
  ) {}
  
  async getTeamAnalytics(
    teamId: string,
    period: DateRange
  ): Promise<TeamAnalytics> {
    // Check cache first
    const cacheKey = this.generateCacheKey('team_analytics', teamId, period);
    const cached = await this.getCachedData(cacheKey);
    if (cached && !this.isCacheExpired(cached)) {
      return cached.data;
    }
    
    // Gather data
    const [metrics, sessions, goals, players] = await Promise.all([
      this.metricService.getTeamMetrics(teamId, { dateRange: period }),
      this.sessionService.getSessions({ teamId, dateRange: period }),
      this.getTeamGoals(teamId, period),
      this.getTeamPlayers(teamId)
    ]);
    
    // Calculate analytics
    const analytics: TeamAnalytics = {
      period,
      summary: {
        totalSessions: sessions.length,
        totalMetricsRecorded: metrics.length,
        activePlayers: this.countActivePlayers(metrics, players),
        avgSessionIntensity: this.calculateAvgIntensity(sessions),
        goalCompletionRate: this.calculateGoalCompletionRate(goals)
      },
      metrics: await this.aggregateMetrics(metrics),
      trends: await this.calculateTrends(metrics, period),
      insights: await this.generateInsights(teamId),
      topPerformers: this.identifyTopPerformers(metrics, players)
    };
    
    // Cache results
    await this.cacheData(cacheKey, analytics, period);
    
    return analytics;
  }
  
  async generateInsights(teamId: string): Promise<Insight[]> {
    const insights: Insight[] = [];
    
    // Get recent data
    const recentPeriod = { start: subDays(new Date(), 30), end: new Date() };
    const [metrics, sessions, goals] = await Promise.all([
      this.metricService.getTeamMetrics(teamId, { dateRange: recentPeriod }),
      this.sessionService.getSessions({ teamId, dateRange: recentPeriod }),
      this.getTeamGoals(teamId, recentPeriod)
    ]);
    
    // Analyze training frequency
    const sessionFrequency = sessions.length / 30;
    if (sessionFrequency < 2) {
      insights.push({
        type: 'warning',
        category: 'training_frequency',
        title: 'Low Training Frequency',
        message: `Team is averaging ${sessionFrequency.toFixed(1)} sessions per week. Consider increasing to 3-4 for optimal development.`,
        priority: 'high',
        actionable: true
      });
    }
    
    // Analyze metric trends
    const trendAnalysis = await this.analyzeTrends(metrics);
    trendAnalysis.forEach(trend => {
      if (trend.direction === 'declining' && trend.significance > 0.8) {
        insights.push({
          type: 'alert',
          category: 'performance_trend',
          title: `Declining ${trend.metricName} Performance`,
          message: `Team average ${trend.metricName} has decreased by ${trend.changePercent}% over the past month.`,
          priority: 'medium',
          data: trend
        });
      }
    });
    
    // Analyze goal progress
    const goalProgress = this.analyzeGoalProgress(goals);
    if (goalProgress.atRiskCount > 0) {
      insights.push({
        type: 'info',
        category: 'goals',
        title: 'Goals at Risk',
        message: `${goalProgress.atRiskCount} player goals are at risk of not being met by their target date.`,
        priority: 'medium',
        actionable: true
      });
    }
    
    // Positive insights
    const improvements = trendAnalysis.filter(t => t.direction === 'improving' && t.significance > 0.8);
    if (improvements.length > 0) {
      insights.push({
        type: 'success',
        category: 'achievement',
        title: 'Performance Improvements',
        message: `Great progress in ${improvements.map(i => i.metricName).join(', ')}!`,
        priority: 'low'
      });
    }
    
    return insights;
  }
  
  private async calculateTrends(
    metrics: Metric[],
    period: DateRange
  ): Promise<TrendData[]> {
    const groupedByType = this.groupMetricsByType(metrics);
    
    return Promise.all(
      Object.entries(groupedByType).map(async ([type, typeMetrics]) => {
        // Sort by date
        const sorted = typeMetrics.sort((a, b) => 
          new Date(a.recorded_date).getTime() - new Date(b.recorded_date).getTime()
        );
        
        // Calculate trend line
        const trend = this.calculateLinearRegression(
          sorted.map((m, i) => ({ x: i, y: m.value }))
        );
        
        // Calculate moving average
        const movingAvg = this.calculateMovingAverage(
          sorted.map(m => m.value),
          7 // 7-day moving average
        );
        
        return {
          metricType: type,
          dataPoints: sorted.map((m, i) => ({
            date: m.recorded_date,
            value: m.value,
            movingAvg: movingAvg[i]
          })),
          trend: {
            slope: trend.slope,
            intercept: trend.intercept,
            r2: trend.r2,
            direction: trend.slope > 0 ? 'improving' : 'declining',
            changePercent: this.calculateChangePercent(sorted)
          }
        };
      })
    );
  }
  
  // Additional implementations...
}
```

### DashboardService [DEV2]

```typescript
// /src/features/perform/services/DashboardService.ts

export interface DashboardService {
  // Dashboard configuration
  getDashboardLayout(userId: string): Promise<DashboardLayout>;
  saveDashboardLayout(userId: string, layout: DashboardLayout): Promise<void>;
  resetDashboardLayout(userId: string): Promise<void>;
  
  // Widget management
  getAvailableWidgets(): Promise<Widget[]>;
  getWidgetData(widgetId: string, params: WidgetParams): Promise<WidgetData>;
  updateWidgetConfig(widgetId: string, config: WidgetConfig): Promise<void>;
  
  // Real-time updates
  subscribeToUpdates(teamId: string, callback: UpdateCallback): Subscription;
  getLatestUpdate(teamId: string): Promise<DashboardUpdate>;
  
  // Quick stats
  getQuickStats(teamId: string): Promise<QuickStats>;
  getActivityFeed(teamId: string, limit?: number): Promise<ActivityItem[]>;
}

export class DashboardServiceImpl implements DashboardService {
  constructor(
    private supabase: SupabaseClient,
    private analyticsService: AnalyticsService
  ) {}
  
  async getWidgetData(
    widgetId: string,
    params: WidgetParams
  ): Promise<WidgetData> {
    switch (widgetId) {
      case 'team-overview':
        return this.getTeamOverviewData(params);
      case 'metric-trends':
        return this.getMetricTrendsData(params);
      case 'player-comparison':
        return this.getPlayerComparisonData(params);
      case 'training-calendar':
        return this.getTrainingCalendarData(params);
      case 'goal-progress':
        return this.getGoalProgressData(params);
      case 'performance-alerts':
        return this.getPerformanceAlertsData(params);
      default:
        throw new Error(`Unknown widget: ${widgetId}`);
    }
  }
  
  private async getTeamOverviewData(params: WidgetParams): Promise<WidgetData> {
    const analytics = await this.analyticsService.getTeamAnalytics(
      params.teamId,
      params.dateRange || { start: subDays(new Date(), 30), end: new Date() }
    );
    
    return {
      widgetId: 'team-overview',
      title: 'Team Overview',
      data: {
        metrics: [
          {
            label: 'Active Players',
            value: analytics.summary.activePlayers,
            change: this.calculateChange('activePlayers', params.teamId)
          },
          {
            label: 'Sessions This Month',
            value: analytics.summary.totalSessions,
            change: this.calculateChange('sessions', params.teamId)
          },
          {
            label: 'Avg Session Intensity',
            value: analytics.summary.avgSessionIntensity.toFixed(1),
            unit: '/10',
            change: this.calculateChange('intensity', params.teamId)
          },
          {
            label: 'Goal Completion',
            value: `${analytics.summary.goalCompletionRate}%`,
            change: this.calculateChange('goals', params.teamId)
          }
        ],
        chartData: this.formatChartData(analytics.trends),
        lastUpdated: new Date()
      }
    };
  }
  
  subscribeToUpdates(
    teamId: string,
    callback: UpdateCallback
  ): Subscription {
    // Subscribe to real-time changes
    const subscription = this.supabase
      .channel(`dashboard-${teamId}`)
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public',
          table: 'performance_metrics',
          filter: `team_id=eq.${teamId}`
        },
        async (payload) => {
          const update = await this.processUpdate(payload);
          callback(update);
        }
      )
      .subscribe();
    
    return {
      unsubscribe: () => {
        subscription.unsubscribe();
      }
    };
  }
  
  // Additional implementations...
}
```

### ReportService [DEV3]

```typescript
// /src/features/perform/services/ReportService.ts

export interface ReportService {
  // Template management
  getTemplates(category?: string): Promise<ReportTemplate[]>;
  getTemplate(templateId: string): Promise<ReportTemplate>;
  createCustomTemplate(template: CustomTemplateDTO): Promise<ReportTemplate>;
  
  // Report generation
  generateReport(config: ReportConfig): Promise<GeneratedReport>;
  previewReport(config: ReportConfig): Promise<ReportPreview>;
  getReportStatus(reportId: string): Promise<ReportStatus>;
  
  // Report history
  getReportHistory(teamId: string, filters?: ReportFilters): Promise<GeneratedReport[]>;
  getReport(reportId: string): Promise<GeneratedReport>;
  deleteReport(reportId: string): Promise<void>;
  
  // Scheduling
  scheduleReport(schedule: ReportScheduleDTO): Promise<ScheduledReport>;
  updateSchedule(scheduleId: string, updates: ScheduleUpdateDTO): Promise<ScheduledReport>;
  getScheduledReports(teamId: string): Promise<ScheduledReport[]>;
  pauseSchedule(scheduleId: string): Promise<void>;
  
  // Distribution
  emailReport(reportId: string, recipients: string[]): Promise<void>;
  getReportUrl(reportId: string): Promise<string>;
}

export class ReportServiceImpl implements ReportService {
  constructor(
    private supabase: SupabaseClient,
    private analyticsService: AnalyticsService,
    private exportService: ExportService
  ) {}
  
  async generateReport(config: ReportConfig): Promise<GeneratedReport> {
    // Create report record
    const { data: report, error } = await this.supabase
      .from('generated_reports')
      .insert({
        team_id: config.teamId,
        generated_by: config.userId,
        template_id: config.templateId,
        title: config.title,
        report_type: config.type,
        format: config.format,
        period_start: config.dateRange.start,
        period_end: config.dateRange.end,
        status: 'generating'
      })
      .select()
      .single();
      
    if (error) throw error;
    
    // Generate report asynchronously
    this.processReport(report.id, config);
    
    return report;
  }
  
  private async processReport(reportId: string, config: ReportConfig) {
    try {
      // Get template
      const template = await this.getTemplate(config.templateId);
      
      // Gather data based on template requirements
      const reportData = await this.gatherReportData(config, template);
      
      // Generate report content
      let fileUrl: string;
      switch (config.format) {
        case 'pdf':
          fileUrl = await this.generatePdfReport(reportData, template);
          break;
        case 'excel':
          fileUrl = await this.generateExcelReport(reportData, template);
          break;
        case 'html':
          fileUrl = await this.generateHtmlReport(reportData, template);
          break;
        default:
          throw new Error(`Unsupported format: ${config.format}`);
      }
      
      // Update report record
      await this.supabase
        .from('generated_reports')
        .update({
          status: 'completed',
          file_url: fileUrl,
          report_data: reportData,
          completed_at: new Date().toISOString()
        })
        .eq('id', reportId);
        
      // Send notifications if configured
      if (config.notifyOnComplete) {
        await this.notifyReportComplete(reportId, config);
      }
      
    } catch (error) {
      // Update with error
      await this.supabase
        .from('generated_reports')
        .update({
          status: 'failed',
          error_message: error.message
        })
        .eq('id', reportId);
        
      throw error;
    }
  }
  
  private async gatherReportData(
    config: ReportConfig,
    template: ReportTemplate
  ): Promise<ReportData> {
    const data: ReportData = {
      metadata: {
        title: config.title,
        generatedAt: new Date(),
        period: config.dateRange,
        teamName: await this.getTeamName(config.teamId)
      },
      sections: []
    };
    
    // Process each section in template
    for (const section of template.layout_config.sections) {
      switch (section.type) {
        case 'summary':
          data.sections.push(await this.generateSummarySection(config, section));
          break;
        case 'metrics':
          data.sections.push(await this.generateMetricsSection(config, section));
          break;
        case 'player-progress':
          data.sections.push(await this.generatePlayerProgressSection(config, section));
          break;
        case 'goals':
          data.sections.push(await this.generateGoalsSection(config, section));
          break;
        case 'insights':
          data.sections.push(await this.generateInsightsSection(config, section));
          break;
        case 'custom':
          data.sections.push(await this.generateCustomSection(config, section));
          break;
      }
    }
    
    return data;
  }
  
  private async generatePdfReport(
    data: ReportData,
    template: ReportTemplate
  ): Promise<string> {
    // Use PDF generation library (e.g., puppeteer, jsPDF)
    const pdf = new PDFGenerator();
    
    // Apply template styling
    pdf.setStyles(template.layout_config.styles);
    
    // Add header
    pdf.addHeader({
      title: data.metadata.title,
      logo: template.layout_config.logo,
      date: data.metadata.generatedAt
    });
    
    // Add sections
    for (const section of data.sections) {
      pdf.addSection(section);
    }
    
    // Add footer
    pdf.addFooter({
      pageNumbers: true,
      generatedBy: 'SHOT Performance System'
    });
    
    // Generate and upload
    const buffer = await pdf.generate();
    const fileUrl = await this.uploadFile(buffer, 'application/pdf');
    
    return fileUrl;
  }
  
  async scheduleReport(schedule: ReportScheduleDTO): Promise<ScheduledReport> {
    // Calculate next run time
    const nextRun = this.calculateNextRun(schedule.frequency, schedule.time);
    
    const { data, error } = await this.supabase
      .from('scheduled_reports')
      .insert({
        ...schedule,
        next_run: nextRun.toISOString()
      })
      .select()
      .single();
      
    if (error) throw error;
    
    // Register with scheduler
    await this.registerScheduledJob(data);
    
    return data;
  }
  
  // Additional implementations...
}
```

### ExportService [DEV4]

```typescript
// /src/features/perform/services/ExportService.ts

export interface ExportService {
  // Export operations
  createExportJob(config: ExportConfig): Promise<ExportJob>;
  getExportStatus(jobId: string): Promise<ExportStatus>;
  downloadExport(jobId: string): Promise<Blob>;
  cancelExport(jobId: string): Promise<void>;
  
  // Export history
  getExportHistory(userId: string): Promise<ExportJob[]>;
  deleteExport(jobId: string): Promise<void>;
  
  // Bulk operations
  exportMetrics(teamId: string, options: MetricExportOptions): Promise<ExportJob>;
  exportSessions(teamId: string, options: SessionExportOptions): Promise<ExportJob>;
  exportFullBackup(teamId: string): Promise<ExportJob>;
  
  // Format converters
  convertToExcel(data: any[], options: ExcelOptions): Promise<Blob>;
  convertToCsv(data: any[], options: CsvOptions): Promise<Blob>;
  convertToJson(data: any[], options: JsonOptions): Promise<Blob>;
}

export class ExportServiceImpl implements ExportService {
  constructor(
    private supabase: SupabaseClient,
    private queueService: QueueService
  ) {}
  
  async createExportJob(config: ExportConfig): Promise<ExportJob> {
    // Create job record
    const { data: job, error } = await this.supabase
      .from('export_jobs')
      .insert({
        requested_by: config.userId,
        team_id: config.teamId,
        export_type: config.type,
        format: config.format,
        filters: config.filters,
        status: 'queued'
      })
      .select()
      .single();
      
    if (error) throw error;
    
    // Queue for processing
    await this.queueService.enqueue('exports', {
      jobId: job.id,
      config
    });
    
    return job;
  }
  
  async processExportJob(jobId: string, config: ExportConfig) {
    try {
      // Update status
      await this.updateJobStatus(jobId, 'processing', 0);
      
      // Fetch data based on export type
      let data: any[];
      switch (config.type) {
        case 'metrics':
          data = await this.fetchMetricsData(config);
          break;
        case 'sessions':
          data = await this.fetchSessionsData(config);
          break;
        case 'full_backup':
          data = await this.fetchFullBackupData(config);
          break;
        default:
          throw new Error(`Unknown export type: ${config.type}`);
      }
      
      // Update progress
      await this.updateJobStatus(jobId, 'processing', 50);
      
      // Convert to requested format
      let blob: Blob;
      switch (config.format) {
        case 'excel':
          blob = await this.convertToExcel(data, config.excelOptions);
          break;
        case 'csv':
          blob = await this.convertToCsv(data, config.csvOptions);
          break;
        case 'json':
          blob = await this.convertToJson(data, config.jsonOptions);
          break;
        default:
          throw new Error(`Unknown format: ${config.format}`);
      }
      
      // Upload file
      const fileUrl = await this.uploadExportFile(blob, jobId, config.format);
      
      // Update job as completed
      await this.supabase
        .from('export_jobs')
        .update({
          status: 'completed',
          progress: 100,
          file_url: fileUrl,
          file_size: blob.size,
          row_count: data.length,
          completed_at: new Date().toISOString(),
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
        })
        .eq('id', jobId);
        
      // Notify user
      await this.notifyExportComplete(jobId, config.userId);
      
    } catch (error) {
      await this.supabase
        .from('export_jobs')
        .update({
          status: 'failed',
          error_message: error.message
        })
        .eq('id', jobId);
        
      throw error;
    }
  }
  
  async convertToExcel(data: any[], options: ExcelOptions): Promise<Blob> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet(options.sheetName || 'Data');
    
    // Add headers
    if (data.length > 0) {
      const headers = Object.keys(data[0]);
      worksheet.addRow(headers);
      
      // Style headers
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };
    }
    
    // Add data
    data.forEach(row => {
      worksheet.addRow(Object.values(row));
    });
    
    // Auto-fit columns
    worksheet.columns.forEach(column => {
      let maxLength = 0;
      column.eachCell({ includeEmpty: false }, cell => {
        const length = cell.value ? cell.value.toString().length : 0;
        if (length > maxLength) {
          maxLength = length;
        }
      });
      column.width = Math.min(maxLength + 2, 50);
    });
    
    // Add filters
    if (options.enableFilters) {
      worksheet.autoFilter = {
        from: 'A1',
        to: `${String.fromCharCode(64 + worksheet.columns.length)}1`
      };
    }
    
    // Generate buffer
    const buffer = await workbook.xlsx.writeBuffer();
    return new Blob([buffer], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    });
  }
  
  // Additional implementations...
}
```

## Implementation Timeline

### Phase 1: Analytics Foundation (Days 1-2)

**Goal**: Build core analytics engine and data aggregation

**DEV1**: 
- Create analytics cache schema
- Implement AnalyticsService core methods
- Build aggregation algorithms
- Create trend calculation logic

**DEV2**:
- Design dashboard layout components
- Create base chart components
- Set up visualization library
- Build responsive grid system

**DEV3**:
- Create report template schema
- Design report data structures
- Build template management system
- Create report configuration types

**DEV4**:
- Set up export job queue
- Create export service framework
- Implement file upload system
- Design caching strategy

### Phase 2: Dashboard Implementation (Day 3)

**Goal**: Complete interactive dashboard features

**DEV1**:
- Implement insight generation
- Create KPI calculations
- Build anomaly detection
- Optimize query performance

**DEV2**:
- Complete analytics dashboard page
- Implement all chart types
- Create filter system
- Build real-time updates

**DEV3**:
- Create report builder UI
- Implement template selector
- Build preview system
- Add customization options

**DEV4**:
- Implement CSV export
- Create Excel converter
- Build progress tracking
- Add download manager

### Phase 3: Advanced Features (Day 4)

**Goal**: Add sophisticated analytics and reporting

**DEV1**:
- Implement correlation analysis
- Create prediction algorithms
- Build recommendation engine
- Add cache warming

**DEV2**:
- Create team insights page
- Build comparison tools
- Add interactive features
- Implement drill-down views

**DEV3**:
- Complete PDF generation
- Implement scheduled reports
- Build email distribution
- Create report history

**DEV4**:
- Add bulk export options
- Implement data transformations
- Create backup functionality
- Build API endpoints

### Phase 4: Integration and Optimization (Day 5)

**Goal**: Complete system integration and performance tuning

**DEV1**:
- Performance optimization
- Cache strategy refinement
- Load testing
- Documentation

**DEV2**:
- Dashboard performance tuning
- Mobile responsiveness
- Accessibility improvements
- User testing

**DEV3**:
- Report generation optimization
- Template library completion
- Distribution testing
- Error handling

**DEV4**:
- Export performance optimization
- Queue management tuning
- Integration testing
- API documentation

## Integration Points

### DEV1 ↔ DEV2
- Analytics data for dashboard (DEV1 provides, DEV2 displays)
- Real-time updates (DEV1 pushes changes, DEV2 updates UI)
- Cache coordination (shared cache strategy)

### DEV1 ↔ DEV3
- Analytics data for reports (DEV1 provides, DEV3 formats)
- Insight generation (DEV1 calculates, DEV3 includes in reports)
- Performance metrics (shared calculations)

### DEV1 ↔ DEV4
- Data aggregation for exports (DEV1 provides, DEV4 exports)
- Cache usage for large exports (coordination needed)
- Performance optimization (joint effort)

### DEV2 ↔ DEV3
- Chart components in reports (DEV2 components reused by DEV3)
- Visualization consistency (shared chart configurations)
- Dashboard snapshots for reports (DEV2 provides, DEV3 captures)

### DEV2 ↔ DEV4
- Export from dashboard (DEV2 triggers, DEV4 processes)
- Progress feedback (DEV4 reports progress, DEV2 displays)
- Download integration (seamless handoff)

### DEV3 ↔ DEV4
- Report export functionality (DEV3 generates, DEV4 converts)
- Scheduled report processing (DEV3 schedules, DEV4 executes)
- File management (shared responsibility)

## Key Design Decisions

1. **Cache-First Architecture**: Aggressive caching for performance
2. **Real-Time Updates**: WebSocket subscriptions for live data
3. **Modular Reports**: Template-based system for flexibility
4. **Queue-Based Exports**: Asynchronous processing for large exports
5. **Progressive Loading**: Dashboard loads incrementally for speed

## Success Metrics

### Performance
- Dashboard initial load < 2 seconds [DEV2]
- Analytics calculation < 1 second for 1000 records [DEV1]
- Report generation < 10 seconds [DEV3]
- Export processing < 30 seconds for 10k records [DEV4]

### User Experience
- Dashboard provides insights without configuration [DEV2]
- Reports are professional and customizable [DEV3]
- Exports work reliably for large datasets [DEV4]
- Real-time updates feel instantaneous [All]

### Data Quality
- Analytics are accurate and verifiable [DEV1]
- Reports reflect actual data state [DEV3]
- Exports maintain data integrity [DEV4]
- Insights are actionable and relevant [DEV1]

### Business Metrics
- 90% of coaches use dashboard weekly
- Average 5+ reports generated per team/month
- 50% reduction in time to insights
- Zero data export failures

## Security Considerations

1. **Data Access**: Enforce team-level permissions on all analytics
2. **Export Control**: Limit export size and frequency per user
3. **Report Distribution**: Verify recipient permissions
4. **Cache Security**: Encrypted cache with access controls
5. **API Rate Limiting**: Prevent abuse of analytics endpoints

## Out of Scope (Week 4)

- Machine learning models
- Predictive analytics beyond trends
- Real-time collaboration on dashboards
- Custom visualization builder
- API access for third parties
- Mobile app specific features
- Advanced statistical analysis
- Integration with external BI tools

---

## Document Metadata

**Last Updated**: 2024-03-14
**Status**: Ready for Implementation
**Feature Owner**: Product Team
**Technical Lead**: TBD
**Document Version**: 1.0