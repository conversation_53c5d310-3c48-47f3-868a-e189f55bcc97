# Week 3: Player Development Tracking Implementation Plan - Developer Work Split

## Developer Assignments

### DEV1 - Player Profile System
**Focus**: Player performance profiles and data architecture
- Player profile database schema
- Performance data aggregation models
- Profile state management
- Historical performance tracking

### DEV2 - Progress Visualization
**Focus**: Charts, graphs, and visual progress indicators
- Progress chart components
- Trend visualization tools
- Comparison visualizations
- Mobile-optimized displays

### DEV3 - Goals & Milestones
**Focus**: Goal setting, tracking, and achievement system
- Goal management services
- Milestone tracking logic
- Achievement calculations
- Progress monitoring algorithms

### DEV4 - Parent Portal
**Focus**: Parent access system and reporting
- Parent portal interface
- Access control implementation
- Report generation system
- Communication features

---

## Overview

Week 3 focuses on individual player development tracking, providing coaches with tools to monitor progress, set goals, and communicate achievements. Players gain visibility into their development journey, while parents receive appropriate access to their children's progress. The system emphasizes visual communication of progress and achievement-based motivation.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Page Flow and Purpose](#page-flow-and-purpose)
3. [Component Structure](#component-structure)
4. [Database Design](#database-design)
5. [Service Architecture](#service-architecture)
6. [Implementation Timeline](#implementation-timeline)
7. [Integration Points](#integration-points)
8. [Success Metrics](#success-metrics)

## Architecture Overview

### Directory Structure

```
src/features/perform/
├── components/
│   ├── profiles/
│   │   ├── PlayerProfileCard.tsx [DEV1]
│   │   ├── PerformanceOverview.tsx [DEV1]
│   │   ├── MetricHistory.tsx [DEV1]
│   │   └── ProfileHeader.tsx [DEV1]
│   ├── progress/
│   │   ├── ProgressChart.tsx [DEV2]
│   │   ├── TrendLine.tsx [DEV2]
│   │   ├── ComparisonChart.tsx [DEV2]
│   │   ├── MilestoneTracker.tsx [DEV2]
│   │   └── ProgressIndicators.tsx [DEV2]
│   ├── goals/
│   │   ├── GoalSetting.tsx [DEV3]
│   │   ├── GoalCard.tsx [DEV3]
│   │   ├── GoalProgress.tsx [DEV3]
│   │   └── AchievementBadges.tsx [DEV3]
│   └── parent/
│       ├── ParentDashboard.tsx [DEV4]
│       ├── ProgressReport.tsx [DEV4]
│       ├── CommunicationPanel.tsx [DEV4]
│       └── AccessControls.tsx [DEV4]
├── pages/
│   ├── PlayerProfile.tsx [DEV1]
│   ├── DevelopmentDashboard.tsx [DEV2]
│   ├── GoalManagement.tsx [DEV3]
│   └── ParentPortal.tsx [DEV4]
├── hooks/
│   ├── usePlayerProfile.ts [DEV1]
│   ├── useProgress.ts [DEV2]
│   ├── useGoals.ts [DEV3]
│   └── useParentAccess.ts [DEV4]
├── services/
│   ├── ProfileService.ts [DEV1]
│   ├── ProgressService.ts [DEV2]
│   ├── GoalService.ts [DEV3]
│   └── ParentPortalService.ts [DEV4]
└── types/
    ├── profile.types.ts [DEV1]
    ├── progress.types.ts [DEV2]
    ├── goal.types.ts [DEV3]
    └── parent.types.ts [DEV4]
```

## Page Flow and Purpose

### Complete Feature Flow Diagram

```mermaid
graph TB
    Start([User Login]) --> RoleCheck{User Role?}
    
    %% Coach Flow
    RoleCheck --> |Coach| CoachDash[Development Dashboard - DEV2]
    CoachDash --> |View Player| PlayerProfile[Player Profile - DEV1]
    CoachDash --> |Set Goals| GoalMgmt[Goal Management - DEV3]
    
    %% Player Flow
    RoleCheck --> |Player| PlayerDash[Player Dashboard - DEV1]
    PlayerDash --> |View Progress| ProgressView[Progress View - DEV2]
    PlayerDash --> |Check Goals| GoalView[My Goals - DEV3]
    
    %% Parent Flow
    RoleCheck --> |Parent| ParentPortal[Parent Portal - DEV4]
    ParentPortal --> |Select Child| ChildSelect[Child Selection - DEV4]
    ChildSelect --> |View Progress| ParentProgress[Progress Report - DEV4]
    
    %% Shared Flows
    PlayerProfile --> |View Metrics| MetricHistory[Metric History - DEV1]
    PlayerProfile --> |Set Goal| GoalSetting[Goal Setting - DEV3]
    GoalSetting --> |Track Progress| ProgressTracking[Progress Tracking - DEV2]
    
    %% Goal Achievement Flow
    ProgressTracking --> |Goal Met?| AchievementCheck{Goal Achieved? - DEV3}
    AchievementCheck --> |Yes| AwardBadge[Award Achievement - DEV3]
    AchievementCheck --> |No| UpdateProgress[Update Progress - DEV2]
    
    %% Reporting
    ParentProgress --> |Generate| ReportGen[Report Generation - DEV4]
    PlayerProfile --> |Export| DataExport[Export Data - DEV1]
```

### Page Details

#### 1. Player Profile [DEV1]

**Purpose**: Comprehensive view of individual player development

**Path**: `/src/features/perform/pages/PlayerProfile.tsx`
**Route**: `/coach/perform/player/:playerId` or `/player/perform/profile`

```typescript
/**
 * PlayerProfile
 * 
 * PURPOSE:
 * - Display complete performance history
 * - Show current metrics and trends
 * - Highlight achievements and milestones
 * 
 * USER GOALS:
 * - Understand player's development journey
 * - Identify strengths and areas for improvement
 * - Track progress over time
 */
```

**New Components Required:**
```typescript
// Profile components
import { PlayerProfileCard } from '@/src/features/perform/components/profiles/PlayerProfileCard';
// Path: /src/features/perform/components/profiles/PlayerProfileCard.tsx

// Performance overview
import { PerformanceOverview } from '@/src/features/perform/components/profiles/PerformanceOverview';
// Path: /src/features/perform/components/profiles/PerformanceOverview.tsx

// Metric history display
import { MetricHistory } from '@/src/features/perform/components/profiles/MetricHistory';
// Path: /src/features/perform/components/profiles/MetricHistory.tsx
```

**Services Used:**
```typescript
import { ProfileService } from '@/src/features/perform/services/ProfileService';
// Methods: 
// - getPlayerProfile(playerId: string): Promise<PlayerProfile>
// - getPerformanceHistory(playerId: string, dateRange?: DateRange): Promise<PerformanceData>
// - updateProfileNotes(playerId: string, notes: string): Promise<void>
```

**Hooks Used:**
```typescript
import { usePlayerProfile } from '@/src/features/perform/hooks/usePlayerProfile';
// Returns: { profile, metrics, loading, error, refetch }
```

#### 2. Development Dashboard [DEV2]

**Purpose**: Team-wide development overview with individual drill-down

**Path**: `/src/features/perform/pages/DevelopmentDashboard.tsx`
**Route**: `/coach/perform/development`

```typescript
/**
 * DevelopmentDashboard
 * 
 * PURPOSE:
 * - Overview of team development progress
 * - Quick access to individual profiles
 * - Identify trends and patterns
 * 
 * USER GOALS:
 * - Monitor team-wide progress
 * - Compare player development
 * - Spot high performers and those needing support
 */
```

**New Components Required:**
```typescript
// Progress visualization
import { TeamProgressGrid } from '@/src/features/perform/components/progress/TeamProgressGrid';
import { ProgressChart } from '@/src/features/perform/components/progress/ProgressChart';
import { TrendAnalysis } from '@/src/features/perform/components/progress/TrendAnalysis';
import { PlayerComparison } from '@/src/features/perform/components/progress/PlayerComparison';
```

**Services Used:**
```typescript
import { ProgressService } from '@/src/features/perform/services/ProgressService';
// Methods: 
// - getTeamProgress(teamId: string): Promise<TeamProgress>
// - comparePlayerProgress(playerIds: string[]): Promise<ComparisonData>
// - getProgressTrends(teamId: string, metric: string): Promise<TrendData>
```

#### 3. Goal Management [DEV3]

**Purpose**: Set, track, and manage player development goals

**Path**: `/src/features/perform/pages/GoalManagement.tsx`
**Route**: `/coach/perform/goals`

```typescript
/**
 * GoalManagement
 * 
 * PURPOSE:
 * - Create SMART goals for players
 * - Track goal progress
 * - Manage achievements and milestones
 * 
 * USER GOALS:
 * - Set meaningful development targets
 * - Monitor progress toward goals
 * - Celebrate achievements
 */
```

**New Components Required:**
```typescript
// Goal components
import { GoalCreator } from '@/src/features/perform/components/goals/GoalCreator';
import { GoalList } from '@/src/features/perform/components/goals/GoalList';
import { GoalTimeline } from '@/src/features/perform/components/goals/GoalTimeline';
import { AchievementGallery } from '@/src/features/perform/components/goals/AchievementGallery';
```

**Services Used:**
```typescript
import { GoalService } from '@/src/features/perform/services/GoalService';
// Methods: 
// - createGoal(goal: GoalData): Promise<Goal>
// - updateGoalProgress(goalId: string, progress: number): Promise<Goal>
// - getPlayerGoals(playerId: string): Promise<Goal[]>
// - awardAchievement(playerId: string, achievementId: string): Promise<Achievement>
```

#### 4. Parent Portal [DEV4]

**Purpose**: Secure, read-only access for parents to view child's progress

**Path**: `/src/features/perform/pages/ParentPortal.tsx`
**Route**: `/parent/perform`

```typescript
/**
 * ParentPortal
 * 
 * PURPOSE:
 * - View child's performance data
 * - Access progress reports
 * - Communicate with coaches
 * 
 * USER GOALS:
 * - Stay informed about child's development
 * - Understand areas of progress
 * - Support child's athletic journey
 */
```

**New Components Required:**
```typescript
// Parent portal components
import { ChildSelector } from '@/src/features/perform/components/parent/ChildSelector';
import { ProgressSummary } from '@/src/features/perform/components/parent/ProgressSummary';
import { ReportViewer } from '@/src/features/perform/components/parent/ReportViewer';
import { CoachMessage } from '@/src/features/perform/components/parent/CoachMessage';
```

**Services Used:**
```typescript
import { ParentPortalService } from '@/src/features/perform/services/ParentPortalService';
// Methods: 
// - getChildren(parentId: string): Promise<Child[]>
// - getChildProgress(childId: string): Promise<ProgressReport>
// - generateReport(childId: string, options: ReportOptions): Promise<Report>
// - getCoachMessages(childId: string): Promise<Message[]>
```

## Database Design

### Player Goals Schema [DEV3]

```sql
-- Player development goals
CREATE TABLE player_goals (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    player_id uuid NOT NULL REFERENCES profiles(id),
    created_by uuid NOT NULL REFERENCES profiles(id),
    
    -- Goal details
    title varchar(255) NOT NULL,
    description text,
    goal_type varchar(50) NOT NULL, -- 'performance', 'skill', 'fitness', 'behavioral'
    
    -- SMART goal components
    metric_type varchar(100) REFERENCES metric_definitions(metric_type),
    target_value decimal(10,3),
    target_unit varchar(50),
    baseline_value decimal(10,3),
    
    -- Timeline
    start_date date NOT NULL DEFAULT CURRENT_DATE,
    target_date date NOT NULL,
    
    -- Progress tracking
    current_value decimal(10,3),
    progress_percentage integer DEFAULT 0,
    last_updated timestamp with time zone,
    
    -- Status
    status varchar(50) DEFAULT 'active', -- 'active', 'completed', 'paused', 'cancelled'
    completed_date date,
    
    -- Metadata
    priority varchar(20) DEFAULT 'medium', -- 'low', 'medium', 'high'
    visibility varchar(20) DEFAULT 'private', -- 'private', 'team', 'public'
    notes text,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    -- Indexes
    INDEX idx_goals_player_status (player_id, status),
    INDEX idx_goals_target_date (target_date)
);

-- Goal milestones
CREATE TABLE goal_milestones (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    goal_id uuid NOT NULL REFERENCES player_goals(id) ON DELETE CASCADE,
    
    -- Milestone details
    title varchar(255) NOT NULL,
    target_value decimal(10,3),
    target_date date,
    
    -- Achievement
    achieved boolean DEFAULT false,
    achieved_date date,
    achieved_value decimal(10,3),
    
    -- Order
    sequence_order integer NOT NULL,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    -- Constraints
    UNIQUE(goal_id, sequence_order)
);

-- Player achievements
CREATE TABLE player_achievements (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    player_id uuid NOT NULL REFERENCES profiles(id),
    
    -- Achievement details
    achievement_type varchar(100) NOT NULL,
    achievement_name varchar(255) NOT NULL,
    achievement_description text,
    
    -- Earning criteria
    earned_date date NOT NULL,
    earned_for varchar(255), -- what triggered the achievement
    related_goal_id uuid REFERENCES player_goals(id),
    related_metric_id uuid REFERENCES performance_metrics(id),
    
    -- Display
    icon_name varchar(100),
    badge_color varchar(7), -- hex color
    display_priority integer DEFAULT 0,
    
    -- Metadata
    awarded_by uuid REFERENCES profiles(id),
    notes text,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    
    -- Indexes
    INDEX idx_achievements_player_date (player_id, earned_date DESC)
);
```

### Player Development Profiles [DEV1]

```sql
-- Player development profiles (extends base profiles)
CREATE TABLE player_development_profiles (
    player_id uuid PRIMARY KEY REFERENCES profiles(id),
    
    -- Development summary
    overall_rating integer CHECK (overall_rating BETWEEN 1 AND 100),
    potential_rating integer CHECK (potential_rating BETWEEN 1 AND 100),
    
    -- Key strengths and areas
    strengths text[],
    improvement_areas text[],
    
    -- Coach notes
    coach_notes text,
    last_coach_review date,
    
    -- Development stage
    development_stage varchar(50), -- 'beginner', 'developing', 'intermediate', 'advanced', 'elite'
    stage_updated_date date,
    
    -- Preferences
    preferred_positions text[],
    learning_style varchar(50),
    
    -- Parent visibility settings
    parent_can_view_notes boolean DEFAULT false,
    parent_can_view_goals boolean DEFAULT true,
    parent_can_view_comparisons boolean DEFAULT false,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Progress snapshots for historical tracking
CREATE TABLE development_snapshots (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    player_id uuid NOT NULL REFERENCES profiles(id),
    
    -- Snapshot timing
    snapshot_date date NOT NULL,
    snapshot_type varchar(50) NOT NULL, -- 'monthly', 'quarterly', 'season_end', 'manual'
    
    -- Performance summary
    metrics_summary jsonb NOT NULL, -- aggregated metrics for the period
    goals_summary jsonb NOT NULL, -- goal progress at time of snapshot
    
    -- Ratings at time
    overall_rating integer,
    ratings_by_category jsonb, -- technical, tactical, physical, mental
    
    -- Coach assessment
    coach_assessment text,
    assessed_by uuid REFERENCES profiles(id),
    
    -- Comparisons
    team_percentile integer,
    age_group_percentile integer,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    
    -- Indexes
    INDEX idx_snapshots_player_date (player_id, snapshot_date DESC),
    UNIQUE(player_id, snapshot_date, snapshot_type)
);
```

### Parent Access Control [DEV4]

```sql
-- Parent portal access settings
CREATE TABLE parent_access_settings (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    parent_id uuid NOT NULL REFERENCES profiles(id),
    child_id uuid NOT NULL REFERENCES profiles(id),
    
    -- Access levels
    can_view_metrics boolean DEFAULT true,
    can_view_goals boolean DEFAULT true,
    can_view_session_notes boolean DEFAULT false,
    can_view_evaluations boolean DEFAULT true,
    can_download_reports boolean DEFAULT true,
    
    -- Metric visibility
    visible_metric_types text[], -- null means all
    hidden_metric_types text[],
    
    -- Report preferences
    report_frequency varchar(50) DEFAULT 'monthly', -- 'weekly', 'monthly', 'quarterly'
    last_report_sent date,
    email_reports boolean DEFAULT true,
    
    -- Communication
    can_message_coach boolean DEFAULT true,
    message_notifications boolean DEFAULT true,
    
    -- Status
    access_granted_date timestamp with time zone DEFAULT now(),
    access_granted_by uuid REFERENCES profiles(id),
    is_active boolean DEFAULT true,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    -- Constraints
    UNIQUE(parent_id, child_id)
);

-- Parent report generation log
CREATE TABLE parent_reports (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    parent_id uuid NOT NULL REFERENCES profiles(id),
    child_id uuid NOT NULL REFERENCES profiles(id),
    
    -- Report details
    report_type varchar(50) NOT NULL, -- 'progress', 'monthly', 'season', 'custom'
    report_period_start date NOT NULL,
    report_period_end date NOT NULL,
    
    -- Content
    report_data jsonb NOT NULL,
    report_url text, -- if stored as file
    
    -- Status
    generated_at timestamp with time zone DEFAULT now(),
    viewed_at timestamp with time zone,
    downloaded_at timestamp with time zone,
    
    -- Metadata
    generated_by uuid REFERENCES profiles(id),
    
    -- Indexes
    INDEX idx_reports_parent_child (parent_id, child_id),
    INDEX idx_reports_generated (generated_at DESC)
);
```

## Service Architecture

### ProfileService [DEV1]

```typescript
// /src/features/perform/services/ProfileService.ts

export interface ProfileService {
  // Profile management
  getPlayerProfile(playerId: string): Promise<PlayerProfile>;
  updatePlayerProfile(playerId: string, updates: ProfileUpdate): Promise<PlayerProfile>;
  createDevelopmentProfile(playerId: string): Promise<DevelopmentProfile>;
  
  // Performance data
  getPerformanceHistory(playerId: string, options?: HistoryOptions): Promise<PerformanceHistory>;
  getMetricSummary(playerId: string, metricType: string): Promise<MetricSummary>;
  getRecentMetrics(playerId: string, limit?: number): Promise<Metric[]>;
  
  // Development tracking
  updateDevelopmentStage(playerId: string, stage: DevelopmentStage): Promise<void>;
  addStrength(playerId: string, strength: string): Promise<void>;
  addImprovementArea(playerId: string, area: string): Promise<void>;
  
  // Snapshots
  createSnapshot(playerId: string, type: SnapshotType): Promise<DevelopmentSnapshot>;
  getSnapshots(playerId: string): Promise<DevelopmentSnapshot[]>;
  compareSnapshots(snapshotIds: string[]): Promise<SnapshotComparison>;
  
  // Coach notes
  updateCoachNotes(playerId: string, notes: string): Promise<void>;
  getCoachNotes(playerId: string): Promise<CoachNotes>;
  
  // Rankings
  getPlayerRankings(playerId: string): Promise<PlayerRankings>;
  getTeamComparison(playerId: string, teamId: string): Promise<TeamComparison>;
}

export class ProfileServiceImpl implements ProfileService {
  constructor(
    private supabase: SupabaseClient,
    private metricService: MetricService
  ) {}
  
  async getPlayerProfile(playerId: string): Promise<PlayerProfile> {
    // Get base profile
    const { data: profile, error: profileError } = await this.supabase
      .from('profiles')
      .select('*')
      .eq('id', playerId)
      .single();
      
    if (profileError) throw profileError;
    
    // Get development profile
    const { data: devProfile } = await this.supabase
      .from('player_development_profiles')
      .select('*')
      .eq('player_id', playerId)
      .single();
    
    // Get recent metrics
    const recentMetrics = await this.metricService.getPlayerMetrics(
      playerId,
      { limit: 10, orderBy: 'recorded_date', order: 'desc' }
    );
    
    // Get active goals
    const { data: activeGoals } = await this.supabase
      .from('player_goals')
      .select('*')
      .eq('player_id', playerId)
      .eq('status', 'active');
    
    return {
      ...profile,
      development: devProfile,
      recentMetrics,
      activeGoals: activeGoals || []
    };
  }
  
  async createSnapshot(
    playerId: string,
    type: SnapshotType
  ): Promise<DevelopmentSnapshot> {
    // Gather current data
    const metrics = await this.metricService.getPlayerMetrics(playerId);
    const goals = await this.getPlayerGoals(playerId);
    const profile = await this.getPlayerProfile(playerId);
    
    // Calculate summaries
    const metricsSummary = this.summarizeMetrics(metrics);
    const goalsSummary = this.summarizeGoals(goals);
    
    // Calculate percentiles
    const percentiles = await this.calculatePercentiles(playerId, metricsSummary);
    
    // Create snapshot
    const { data: snapshot, error } = await this.supabase
      .from('development_snapshots')
      .insert({
        player_id: playerId,
        snapshot_date: new Date().toISOString().split('T')[0],
        snapshot_type: type,
        metrics_summary: metricsSummary,
        goals_summary: goalsSummary,
        overall_rating: profile.development?.overall_rating,
        team_percentile: percentiles.team,
        age_group_percentile: percentiles.ageGroup
      })
      .select()
      .single();
      
    if (error) throw error;
    return snapshot;
  }
  
  // Additional implementations...
}
```

### ProgressService [DEV2]

```typescript
// /src/features/perform/services/ProgressService.ts

export interface ProgressService {
  // Individual progress
  getPlayerProgress(playerId: string, metricType?: string): Promise<ProgressData>;
  calculateProgressRate(playerId: string, dateRange: DateRange): Promise<ProgressRate>;
  getProgressTrend(playerId: string, metricType: string): Promise<TrendData>;
  
  // Team progress
  getTeamProgress(teamId: string): Promise<TeamProgressData>;
  comparePlayerProgress(playerIds: string[]): Promise<ComparisonData>;
  getProgressLeaderboard(teamId: string, metricType: string): Promise<Leaderboard>;
  
  // Visualizations
  generateProgressChart(playerId: string, options: ChartOptions): Promise<ChartData>;
  generateComparisonChart(playerIds: string[], metricType: string): Promise<ChartData>;
  generateTeamHeatmap(teamId: string): Promise<HeatmapData>;
  
  // Milestones
  checkMilestones(playerId: string): Promise<MilestoneCheck[]>;
  calculateNextMilestone(playerId: string, metricType: string): Promise<Milestone>;
  
  // Predictions
  predictProgress(playerId: string, metricType: string, targetDate: Date): Promise<Prediction>;
  estimateGoalCompletion(goalId: string): Promise<CompletionEstimate>;
}

export class ProgressServiceImpl implements ProgressService {
  constructor(
    private supabase: SupabaseClient,
    private metricService: MetricService,
    private aggregationService: AggregationService
  ) {}
  
  async getPlayerProgress(
    playerId: string,
    metricType?: string
  ): Promise<ProgressData> {
    const filters = metricType ? { metric_type: metricType } : {};
    const metrics = await this.metricService.getPlayerMetrics(playerId, filters);
    
    if (metrics.length === 0) {
      return { hasData: false, progress: [] };
    }
    
    // Group by metric type
    const groupedMetrics = this.groupByMetricType(metrics);
    
    const progressData = await Promise.all(
      Object.entries(groupedMetrics).map(async ([type, typeMetrics]) => {
        const trend = await this.aggregationService.calculateTrend(typeMetrics);
        const latestValue = typeMetrics[0].value;
        const firstValue = typeMetrics[typeMetrics.length - 1].value;
        const change = ((latestValue - firstValue) / firstValue) * 100;
        
        return {
          metricType: type,
          currentValue: latestValue,
          startValue: firstValue,
          changePercent: change,
          trend,
          dataPoints: typeMetrics.map(m => ({
            date: m.recorded_date,
            value: m.value
          }))
        };
      })
    );
    
    return {
      hasData: true,
      progress: progressData,
      summary: this.generateProgressSummary(progressData)
    };
  }
  
  async generateProgressChart(
    playerId: string,
    options: ChartOptions
  ): Promise<ChartData> {
    const progress = await this.getPlayerProgress(playerId, options.metricType);
    
    if (!progress.hasData) {
      return { datasets: [], labels: [] };
    }
    
    const dataset = progress.progress.find(p => p.metricType === options.metricType);
    if (!dataset) {
      return { datasets: [], labels: [] };
    }
    
    // Add trend line if requested
    const datasets = [{
      label: dataset.metricType,
      data: dataset.dataPoints.map(p => p.value),
      borderColor: options.color || '#3B82F6',
      tension: 0.1
    }];
    
    if (options.showTrend) {
      const trendLine = this.calculateTrendLine(dataset.dataPoints);
      datasets.push({
        label: 'Trend',
        data: trendLine,
        borderColor: '#10B981',
        borderDash: [5, 5]
      });
    }
    
    return {
      datasets,
      labels: dataset.dataPoints.map(p => p.date),
      options: {
        responsive: true,
        maintainAspectRatio: false,
        ...options.chartOptions
      }
    };
  }
  
  // Additional implementations...
}
```

### GoalService [DEV3]

```typescript
// /src/features/perform/services/GoalService.ts

export interface GoalService {
  // Goal CRUD
  createGoal(goal: CreateGoalDTO): Promise<Goal>;
  updateGoal(goalId: string, updates: UpdateGoalDTO): Promise<Goal>;
  deleteGoal(goalId: string): Promise<void>;
  getGoal(goalId: string): Promise<Goal>;
  
  // Goal queries
  getPlayerGoals(playerId: string, filters?: GoalFilters): Promise<Goal[]>;
  getTeamGoals(teamId: string): Promise<Goal[]>;
  getGoalsByStatus(status: GoalStatus): Promise<Goal[]>;
  
  // Progress tracking
  updateGoalProgress(goalId: string, currentValue: number): Promise<Goal>;
  checkGoalCompletion(goalId: string): Promise<CompletionCheck>;
  calculateGoalProgress(goal: Goal): number;
  
  // Milestones
  createMilestone(goalId: string, milestone: MilestoneDTO): Promise<Milestone>;
  checkMilestones(goalId: string): Promise<MilestoneCheck[]>;
  completeMilestone(milestoneId: string): Promise<Milestone>;
  
  // Achievements
  checkForAchievements(playerId: string): Promise<Achievement[]>;
  awardAchievement(playerId: string, achievement: AchievementDTO): Promise<Achievement>;
  getPlayerAchievements(playerId: string): Promise<Achievement[]>;
  
  // Goal recommendations
  suggestGoals(playerId: string): Promise<GoalSuggestion[]>;
  generateSmartGoal(playerId: string, metricType: string): Promise<SmartGoal>;
}

export class GoalServiceImpl implements GoalService {
  constructor(
    private supabase: SupabaseClient,
    private metricService: MetricService,
    private profileService: ProfileService
  ) {}
  
  async createGoal(goal: CreateGoalDTO): Promise<Goal> {
    // Validate SMART criteria
    this.validateSmartGoal(goal);
    
    // Get baseline if not provided
    let baselineValue = goal.baseline_value;
    if (!baselineValue && goal.metric_type) {
      const latestMetric = await this.metricService.getPlayerMetrics(
        goal.player_id,
        { metric_type: goal.metric_type, limit: 1 }
      );
      baselineValue = latestMetric[0]?.value || 0;
    }
    
    // Create goal
    const { data, error } = await this.supabase
      .from('player_goals')
      .insert({
        ...goal,
        baseline_value: baselineValue,
        current_value: baselineValue,
        progress_percentage: 0
      })
      .select()
      .single();
      
    if (error) throw error;
    
    // Create default milestones if requested
    if (goal.auto_milestones) {
      await this.createDefaultMilestones(data.id, goal);
    }
    
    return data;
  }
  
  async updateGoalProgress(
    goalId: string,
    currentValue: number
  ): Promise<Goal> {
    // Get goal
    const goal = await this.getGoal(goalId);
    
    // Calculate progress
    const progress = this.calculateGoalProgress({
      ...goal,
      current_value: currentValue
    });
    
    // Update goal
    const { data, error } = await this.supabase
      .from('player_goals')
      .update({
        current_value: currentValue,
        progress_percentage: Math.round(progress),
        last_updated: new Date().toISOString()
      })
      .eq('id', goalId)
      .select()
      .single();
      
    if (error) throw error;
    
    // Check for completion
    if (progress >= 100 && goal.status === 'active') {
      await this.completeGoal(goalId);
      await this.checkForAchievements(goal.player_id);
    }
    
    // Check milestones
    await this.checkMilestones(goalId);
    
    return data;
  }
  
  async checkForAchievements(playerId: string): Promise<Achievement[]> {
    const newAchievements: Achievement[] = [];
    
    // Get player data
    const goals = await this.getPlayerGoals(playerId);
    const achievements = await this.getPlayerAchievements(playerId);
    
    // Check goal-based achievements
    const completedGoals = goals.filter(g => g.status === 'completed');
    
    // First goal completed
    if (completedGoals.length === 1 && !this.hasAchievement(achievements, 'first_goal')) {
      newAchievements.push(
        await this.awardAchievement(playerId, {
          achievement_type: 'first_goal',
          achievement_name: 'Goal Getter',
          achievement_description: 'Completed your first goal!',
          icon_name: 'trophy',
          badge_color: '#F59E0B'
        })
      );
    }
    
    // 5 goals completed
    if (completedGoals.length >= 5 && !this.hasAchievement(achievements, 'five_goals')) {
      newAchievements.push(
        await this.awardAchievement(playerId, {
          achievement_type: 'five_goals',
          achievement_name: 'High Achiever',
          achievement_description: 'Completed 5 goals!',
          icon_name: 'star',
          badge_color: '#3B82F6'
        })
      );
    }
    
    // Check metric-based achievements
    const metrics = await this.metricService.getPlayerMetrics(playerId);
    await this.checkMetricAchievements(playerId, metrics, achievements, newAchievements);
    
    return newAchievements;
  }
  
  private validateSmartGoal(goal: CreateGoalDTO): void {
    const errors: string[] = [];
    
    // Specific
    if (!goal.title || goal.title.length < 5) {
      errors.push('Goal title must be specific and at least 5 characters');
    }
    
    // Measurable
    if (goal.metric_type && (!goal.target_value || !goal.target_unit)) {
      errors.push('Measurable goals must have target value and unit');
    }
    
    // Achievable
    if (goal.baseline_value && goal.target_value) {
      const changePercent = Math.abs(
        ((goal.target_value - goal.baseline_value) / goal.baseline_value) * 100
      );
      if (changePercent > 100) {
        errors.push('Goal may be unrealistic (>100% change)');
      }
    }
    
    // Time-bound
    if (!goal.target_date) {
      errors.push('Goal must have a target date');
    }
    
    if (errors.length > 0) {
      throw new ValidationError(errors);
    }
  }
  
  // Additional implementations...
}
```

### ParentPortalService [DEV4]

```typescript
// /src/features/perform/services/ParentPortalService.ts

export interface ParentPortalService {
  // Access management
  getParentAccess(parentId: string): Promise<ParentAccess[]>;
  getChildAccess(parentId: string, childId: string): Promise<ChildAccess>;
  updateAccessSettings(parentId: string, childId: string, settings: AccessSettings): Promise<void>;
  
  // Child data access
  getChildren(parentId: string): Promise<Child[]>;
  getChildProfile(parentId: string, childId: string): Promise<ChildProfile>;
  getChildProgress(parentId: string, childId: string): Promise<ProgressReport>;
  
  // Reports
  generateProgressReport(childId: string, options: ReportOptions): Promise<Report>;
  getReportHistory(parentId: string, childId: string): Promise<Report[]>;
  scheduleReport(parentId: string, childId: string, schedule: ReportSchedule): Promise<void>;
  
  // Communication
  sendMessageToCoach(parentId: string, childId: string, message: MessageDTO): Promise<Message>;
  getMessages(parentId: string, childId: string): Promise<Message[]>;
  markMessageRead(messageId: string): Promise<void>;
  
  // Notifications
  getNotificationPreferences(parentId: string): Promise<NotificationPrefs>;
  updateNotificationPreferences(parentId: string, prefs: NotificationPrefs): Promise<void>;
}

export class ParentPortalServiceImpl implements ParentPortalService {
  constructor(
    private supabase: SupabaseClient,
    private profileService: ProfileService,
    private progressService: ProgressService,
    private goalService: GoalService
  ) {}
  
  async getChildProgress(
    parentId: string,
    childId: string
  ): Promise<ProgressReport> {
    // Verify access
    const access = await this.verifyParentAccess(parentId, childId);
    if (!access) {
      throw new Error('Access denied');
    }
    
    // Get child profile
    const profile = await this.profileService.getPlayerProfile(childId);
    
    // Get allowed metrics
    const metrics = await this.getVisibleMetrics(access, childId);
    
    // Get goals if allowed
    let goals: Goal[] = [];
    if (access.can_view_goals) {
      goals = await this.goalService.getPlayerGoals(childId, {
        visibility: ['team', 'public']
      });
    }
    
    // Get progress data
    const progress = await this.progressService.getPlayerProgress(childId);
    
    // Filter based on access settings
    const filteredProgress = this.filterProgressData(progress, access);
    
    return {
      childId,
      profile: this.sanitizeProfile(profile, access),
      metrics: metrics,
      goals: goals,
      progress: filteredProgress,
      generatedAt: new Date(),
      accessLevel: this.getAccessLevel(access)
    };
  }
  
  async generateProgressReport(
    childId: string,
    options: ReportOptions
  ): Promise<Report> {
    const parentId = await this.getCurrentParentId();
    
    // Get progress data
    const progressData = await this.getChildProgress(parentId, childId);
    
    // Get historical data if requested
    let historical = null;
    if (options.includeHistorical) {
      const snapshots = await this.profileService.getSnapshots(childId);
      historical = snapshots.slice(0, options.historicalMonths || 3);
    }
    
    // Generate report content
    const reportData = {
      summary: this.generateSummary(progressData),
      currentPerformance: progressData.metrics,
      goals: progressData.goals,
      progress: progressData.progress,
      historical: historical,
      recommendations: this.generateRecommendations(progressData),
      coachNotes: progressData.profile.development?.coach_notes // if allowed
    };
    
    // Save report
    const { data: report, error } = await this.supabase
      .from('parent_reports')
      .insert({
        parent_id: parentId,
        child_id: childId,
        report_type: options.reportType || 'progress',
        report_period_start: options.startDate,
        report_period_end: options.endDate || new Date(),
        report_data: reportData
      })
      .select()
      .single();
      
    if (error) throw error;
    
    // Generate PDF if requested
    if (options.format === 'pdf') {
      report.report_url = await this.generatePdfReport(report);
    }
    
    return report;
  }
  
  private sanitizeProfile(
    profile: PlayerProfile,
    access: ParentAccess
  ): SanitizedProfile {
    const sanitized: any = {
      name: profile.name,
      age: profile.age,
      team: profile.team,
      position: profile.position
    };
    
    if (access.can_view_evaluations) {
      sanitized.overallRating = profile.development?.overall_rating;
      sanitized.developmentStage = profile.development?.development_stage;
    }
    
    if (access.can_view_session_notes && profile.development?.coach_notes) {
      sanitized.coachNotes = profile.development.coach_notes;
    }
    
    return sanitized;
  }
  
  // Additional implementations...
}
```

## Implementation Timeline

### Phase 1: Data Models and Core Services (Days 1-2)

**Goal**: Establish data structures and basic services

**DEV1**: 
- Create player development profile schema
- Implement ProfileService core methods
- Set up snapshot system
- Create profile TypeScript types

**DEV2**:
- Design progress visualization components
- Create chart component templates
- Set up visualization library integration
- Define progress data structures

**DEV3**:
- Create goals and achievements schema
- Implement GoalService CRUD operations
- Design achievement system
- Create goal validation logic

**DEV4**:
- Create parent access control schema
- Design report data structure
- Implement access verification
- Set up parent-child relationships

### Phase 2: Core Features (Day 3)

**Goal**: Implement primary functionality

**DEV1**:
- Complete player profile page
- Implement performance history view
- Create profile update functionality
- Build metric summary components

**DEV2**:
- Implement progress chart components
- Create trend visualization
- Build comparison tools
- Develop progress indicators

**DEV3**:
- Complete goal creation flow
- Implement progress tracking
- Create milestone system
- Build achievement checks

**DEV4**:
- Create parent portal layout
- Implement child selection
- Build access control UI
- Create basic report viewer

### Phase 3: Advanced Features (Day 4)

**Goal**: Add sophisticated functionality

**DEV1**:
- Implement snapshot comparisons
- Create ranking calculations
- Build development stage logic
- Add coach notes system

**DEV2**:
- Create team progress dashboard
- Implement heatmap visualization
- Build progress predictions
- Add interactive charts

**DEV3**:
- Implement smart goal suggestions
- Create achievement gallery
- Build goal timeline view
- Add celebration animations

**DEV4**:
- Complete report generation
- Implement PDF export
- Create message system
- Build notification preferences

### Phase 4: Integration and Polish (Day 5)

**Goal**: Complete integration and testing

**DEV1**:
- Integration with metrics system
- Performance optimization
- Data consistency checks
- Final testing

**DEV2**:
- Chart responsiveness
- Mobile optimization
- Cross-browser testing
- Performance tuning

**DEV3**:
- Goal-metric integration
- Achievement notifications
- Progress automation
- End-to-end testing

**DEV4**:
- Parent portal security audit
- Report generation testing
- Access control verification
- Documentation completion

## Integration Points

### DEV1 ↔ DEV2
- Profile data for visualizations (DEV1 provides data, DEV2 displays)
- Performance metrics for charts (DEV1 aggregates, DEV2 visualizes)
- Snapshot data for comparisons (shared implementation)

### DEV1 ↔ DEV3
- Goals displayed in profiles (DEV3 provides goals, DEV1 shows in profile)
- Achievement integration (DEV3 awards, DEV1 displays)
- Progress updates trigger goal checks (bidirectional)

### DEV1 ↔ DEV4
- Profile data for parent view (DEV1 provides, DEV4 filters based on access)
- Snapshot data for reports (DEV1 creates, DEV4 includes in reports)
- Coach notes visibility (DEV1 stores, DEV4 checks permissions)

### DEV2 ↔ DEV3
- Progress affects goal completion (DEV2 tracks, DEV3 updates goals)
- Goal progress visualization (DEV3 provides data, DEV2 displays)
- Milestone indicators on charts (integration needed)

### DEV2 ↔ DEV4
- Progress charts in parent portal (DEV2 components reused by DEV4)
- Filtered visualizations (DEV2 creates, DEV4 applies filters)
- Report chart generation (shared implementation)

### DEV3 ↔ DEV4
- Goal visibility settings (DEV3 manages, DEV4 respects)
- Achievement display in reports (DEV3 provides, DEV4 includes)
- Parent-visible goals only (filtering coordination)

## Key Design Decisions

1. **Privacy First**: Parent access is restricted by default, coaches control visibility
2. **Visual Progress**: Charts and graphs prioritized over raw numbers
3. **Achievement Motivation**: Gamification through badges and milestones
4. **Mobile Parent Access**: Parent portal optimized for mobile viewing
5. **Automated Snapshots**: Regular progress snapshots for historical tracking

## Success Metrics

### Performance
- Profile load time < 1 second [DEV1]
- Chart rendering < 500ms [DEV2]
- Goal progress calculation < 200ms [DEV3]
- Report generation < 5 seconds [DEV4]

### User Experience
- Parents can access child data in < 3 clicks [DEV4]
- Goal creation takes < 2 minutes [DEV3]
- Progress is visually clear without explanation [DEV2]
- Profile provides actionable insights [DEV1]

### Data Quality
- 100% of goals have SMART criteria [DEV3]
- Progress data updates in real-time [DEV2]
- Snapshots capture complete state [DEV1]
- Reports accurately reflect permissions [DEV4]

### Business Metrics
- 70% of parents access portal monthly
- 90% of players have active goals
- Average 3+ achievements per player
- 80% goal completion rate

## Security Considerations

1. **Parent Access**: Strict verification of parent-child relationships
2. **Data Filtering**: Enforce access controls at service layer
3. **Report Security**: Generated reports respect access settings
4. **Message Privacy**: Coach-parent communication is logged
5. **Audit Trail**: All access and changes are logged

## Out of Scope (Week 3)

- AI-powered goal recommendations
- Video progress demonstrations
- Peer-to-peer comparisons
- Social sharing of achievements
- Complex statistical analysis
- Predictive injury risk
- Nutrition tracking integration
- Sleep and recovery metrics

---

## Document Metadata

**Last Updated**: 2024-03-14
**Status**: Ready for Implementation
**Feature Owner**: Product Team
**Technical Lead**: TBD
**Document Version**: 1.0