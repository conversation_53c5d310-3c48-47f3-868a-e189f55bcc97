# Week 2: Performance Metrics System Implementation Plan - Developer Work Split

## Developer Assignments

### DEV1 - Metric Architecture & Data Model
**Focus**: Database schema for metrics and core data structures
- Performance metrics database schema design
- Metric type definitions and categorization
- Data validation rules and constraints
- Historical data import structure

### DEV2 - Data Collection Interfaces
**Focus**: UI components for metric entry and management
- Metric entry form components
- Bulk data entry interfaces
- Quick entry widgets for common metrics
- Mobile-optimized input methods

### DEV3 - Metric Processing & Validation
**Focus**: Business logic for metric handling and validation
- Metric calculation services
- Data validation and normalization
- Anomaly detection algorithms
- Metric aggregation logic

### DEV4 - Integration & Import Tools
**Focus**: System integration and data migration
- CSV/Excel import functionality
- API integration endpoints
- Batch processing services
- Data transformation utilities

---

## Overview

Week 2 establishes the performance metrics system that enables coaches to track player development through quantifiable measurements. This includes creating flexible metric definitions, efficient data entry interfaces, robust validation, and tools for importing historical data. The system must handle various metric types (speed, agility, strength, technique) while maintaining data integrity and supporting bulk operations.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Page Flow and Purpose](#page-flow-and-purpose)
3. [Component Structure](#component-structure)
4. [Database Design](#database-design)
5. [Service Architecture](#service-architecture)
6. [Implementation Timeline](#implementation-timeline)
7. [Integration Points](#integration-points)
8. [Success Metrics](#success-metrics)

## Architecture Overview

### Directory Structure

```
src/features/perform/
├── components/
│   ├── metrics/
│   │   ├── MetricEntryForm.tsx [DEV2]
│   │   ├── BulkEntryGrid.tsx [DEV2]
│   │   ├── QuickMetricInput.tsx [DEV2]
│   │   ├── MetricTypeSelector.tsx [DEV2]
│   │   └── MetricValidationDisplay.tsx [DEV2]
│   ├── import/
│   │   ├── ImportWizard.tsx [DEV2]
│   │   ├── DataMappingTool.tsx [DEV2]
│   │   ├── ImportPreview.tsx [DEV2]
│   │   └── ImportProgress.tsx [DEV2]
│   └── shared/
│       ├── MetricCard.tsx [DEV2]
│       ├── MetricTrend.tsx [DEV2]
│       └── ValidationFeedback.tsx [DEV2]
├── pages/
│   ├── MetricsEntry.tsx [DEV2]
│   ├── BulkDataEntry.tsx [DEV2]
│   ├── MetricsImport.tsx [DEV2]
│   └── MetricDefinitions.tsx [DEV2]
├── hooks/
│   ├── useMetrics.ts [DEV3]
│   ├── useMetricValidation.ts [DEV3]
│   └── useImport.ts [DEV4]
├── services/
│   ├── MetricService.ts [DEV3]
│   ├── ValidationService.ts [DEV3]
│   ├── ImportService.ts [DEV4]
│   └── AggregationService.ts [DEV3]
├── utils/
│   ├── metricCalculations.ts [DEV3]
│   ├── validators.ts [DEV3]
│   ├── importParsers.ts [DEV4]
│   └── dataTransformers.ts [DEV4]
└── types/
    ├── metric.types.ts [DEV1]
    ├── import.types.ts [DEV4]
    └── validation.types.ts [DEV3]
```

## Page Flow and Purpose

### Complete Feature Flow Diagram

```mermaid
graph TB
    Start([Coach Dashboard]) --> MetricsHub[Metrics Hub - DEV2]
    
    %% Entry Methods
    MetricsHub --> |Single Entry| MetricEntry[Metric Entry - DEV2]
    MetricsHub --> |Bulk Entry| BulkEntry[Bulk Entry - DEV2]
    MetricsHub --> |Import Data| ImportWizard[Import Wizard - DEV2]
    MetricsHub --> |Define Metrics| MetricDefs[Metric Definitions - DEV2]
    
    %% Single Entry Flow
    MetricEntry --> |Select Player| PlayerSelect[Player Selection - DEV2]
    PlayerSelect --> |Choose Metric| MetricSelect[Metric Selection - DEV2]
    MetricSelect --> |Enter Value| ValueInput[Value Input - DEV2]
    ValueInput --> |Validate| Validation{Valid? - DEV3}
    Validation --> |Yes| SaveSingle[Save Metric - DEV3]
    Validation --> |No| ShowErrors[Show Errors - DEV2]
    
    %% Bulk Entry Flow
    BulkEntry --> |Select Team| TeamGrid[Team Grid - DEV2]
    TeamGrid --> |Enter Values| GridInput[Grid Input - DEV2]
    GridInput --> |Validate All| BulkValidation{All Valid? - DEV3}
    BulkValidation --> |Yes| SaveBulk[Save All - DEV3]
    BulkValidation --> |Partial| ReviewErrors[Review Errors - DEV2]
    
    %% Import Flow
    ImportWizard --> |Upload File| FileUpload[File Upload - DEV4]
    FileUpload --> |Map Columns| DataMapping[Data Mapping - DEV4]
    DataMapping --> |Preview| ImportPreview[Preview Import - DEV2]
    ImportPreview --> |Process| ProcessImport[Process Import - DEV4]
    
    %% Integration
    SaveSingle --> |Update| Database[(Metrics DB - DEV1)]
    SaveBulk --> |Batch Update| Database
    ProcessImport --> |Bulk Insert| Database
```

### Page Details

#### 1. Metrics Hub [DEV2]

**Purpose**: Central dashboard for all metric-related activities

**Path**: `/src/features/perform/pages/MetricsHub.tsx`
**Route**: `/coach/perform/metrics`

```typescript
/**
 * MetricsHub
 * 
 * PURPOSE:
 * - Provide overview of recent metric entries
 * - Quick access to different entry methods
 * - Display metric trends and alerts
 * 
 * USER GOALS:
 * - Choose appropriate entry method quickly
 * - See recent entries and trends
 * - Access import tools for historical data
 */
```

**New Components Required:**
```typescript
// Recent metrics display
import { RecentMetrics } from '@/src/features/perform/components/metrics/RecentMetrics';
// Path: /src/features/perform/components/metrics/RecentMetrics.tsx

// Entry method cards
import { EntryMethodCard } from '@/src/features/perform/components/metrics/EntryMethodCard';
// Path: /src/features/perform/components/metrics/EntryMethodCard.tsx

// Metric alerts
import { MetricAlerts } from '@/src/features/perform/components/metrics/MetricAlerts';
// Path: /src/features/perform/components/metrics/MetricAlerts.tsx
```

**Services Used:**
```typescript
import { MetricService } from '@/src/features/perform/services/MetricService';
// Methods: 
// - getRecentMetrics(teamId: string, limit: number): Promise<Metric[]>
// - getMetricAlerts(teamId: string): Promise<Alert[]>
// - getMetricSummary(teamId: string): Promise<MetricSummary>
```

#### 2. Single Metric Entry [DEV2]

**Purpose**: Detailed form for entering individual metrics

**Path**: `/src/features/perform/pages/MetricsEntry.tsx`
**Route**: `/coach/perform/metrics/entry`

```typescript
/**
 * MetricsEntry
 * 
 * PURPOSE:
 * - Enter metrics for individual players
 * - Support various metric types
 * - Provide immediate validation feedback
 * 
 * USER GOALS:
 * - Quickly record performance data
 * - Get validation feedback
 * - Add contextual notes
 */
```

**New Components Required:**
```typescript
// Player selection
import { PlayerSelector } from '@/src/features/perform/components/metrics/PlayerSelector';
import { MetricTypeSelector } from '@/src/features/perform/components/metrics/MetricTypeSelector';
import { MetricValueInput } from '@/src/features/perform/components/metrics/MetricValueInput';
import { MetricNotes } from '@/src/features/perform/components/metrics/MetricNotes';
```

**Services Used:**
```typescript
import { MetricService } from '@/src/features/perform/services/MetricService';
// Methods: 
// - saveMetric(metric: MetricData): Promise<Metric>
// - getMetricTypes(): Promise<MetricType[]>
// - validateMetric(metric: MetricData): ValidationResult
```

#### 3. Bulk Data Entry [DEV2]

**Purpose**: Grid interface for entering metrics for multiple players

**Path**: `/src/features/perform/pages/BulkDataEntry.tsx`
**Route**: `/coach/perform/metrics/bulk`

```typescript
/**
 * BulkDataEntry
 * 
 * PURPOSE:
 * - Enter same metric for entire team
 * - Efficient data entry after group testing
 * - Support copy/paste from spreadsheets
 * 
 * USER GOALS:
 * - Enter data for whole team quickly
 * - Validate all entries at once
 * - Save time on repetitive entries
 */
```

**New Components Required:**
```typescript
// Grid components
import { BulkEntryGrid } from '@/src/features/perform/components/metrics/BulkEntryGrid';
import { GridToolbar } from '@/src/features/perform/components/metrics/GridToolbar';
import { ValidationSummary } from '@/src/features/perform/components/metrics/ValidationSummary';
import { QuickFillTools } from '@/src/features/perform/components/metrics/QuickFillTools';
```

**Services Used:**
```typescript
import { MetricService } from '@/src/features/perform/services/MetricService';
// Methods: 
// - saveBulkMetrics(metrics: MetricData[]): Promise<BulkSaveResult>
// - validateBulkMetrics(metrics: MetricData[]): BulkValidationResult
```

#### 4. Import Wizard [DEV2]

**Purpose**: Step-by-step interface for importing historical data

**Path**: `/src/features/perform/pages/MetricsImport.tsx`
**Route**: `/coach/perform/metrics/import`

```typescript
/**
 * MetricsImport
 * 
 * PURPOSE:
 * - Import historical performance data
 * - Map columns to metric types
 * - Preview and validate before import
 * 
 * USER GOALS:
 * - Bring in existing data easily
 * - Ensure data quality during import
 * - Avoid manual re-entry of historical data
 */
```

**New Components Required:**
```typescript
// Import wizard steps
import { FileUploadStep } from '@/src/features/perform/components/import/FileUploadStep';
import { ColumnMappingStep } from '@/src/features/perform/components/import/ColumnMappingStep';
import { ValidationStep } from '@/src/features/perform/components/import/ValidationStep';
import { ImportSummary } from '@/src/features/perform/components/import/ImportSummary';
```

**Services Used:**
```typescript
import { ImportService } from '@/src/features/perform/services/ImportService';
// Methods: 
// - parseFile(file: File): Promise<ParsedData>
// - validateImportData(data: ParsedData, mapping: ColumnMapping): ImportValidation
// - processImport(data: ParsedData, mapping: ColumnMapping): Promise<ImportResult>
```

## Database Design

### Performance Metrics Schema [DEV1]

```sql
-- Core metrics table
CREATE TABLE performance_metrics (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    player_id uuid NOT NULL REFERENCES profiles(id),
    team_id uuid NOT NULL REFERENCES teams(id),
    
    -- Metric identification
    metric_type varchar(100) NOT NULL,
    metric_subtype varchar(100),
    
    -- Metric value with unit
    value decimal(10,3) NOT NULL,
    unit varchar(50) NOT NULL,
    
    -- Context
    recorded_date date NOT NULL,
    recorded_by uuid NOT NULL REFERENCES profiles(id),
    session_id uuid REFERENCES training_sessions(id),
    
    -- Additional data
    conditions jsonb, -- weather, surface, etc.
    notes text,
    
    -- Data quality
    is_verified boolean DEFAULT false,
    verification_method varchar(50),
    anomaly_flag boolean DEFAULT false,
    
    -- Import tracking
    import_batch_id uuid REFERENCES import_batches(id),
    original_value text, -- preserve original for audit
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    -- Indexes
    INDEX idx_metrics_player_date (player_id, recorded_date),
    INDEX idx_metrics_team_type (team_id, metric_type),
    INDEX idx_metrics_session (session_id),
    INDEX idx_metrics_anomaly (anomaly_flag) WHERE anomaly_flag = true
);

-- Metric type definitions
CREATE TABLE metric_definitions (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Metric identification
    metric_type varchar(100) UNIQUE NOT NULL,
    category varchar(50) NOT NULL, -- 'speed', 'strength', 'agility', 'technique'
    
    -- Display information
    display_name varchar(255) NOT NULL,
    description text,
    unit varchar(50) NOT NULL,
    
    -- Validation rules
    min_value decimal(10,3),
    max_value decimal(10,3),
    decimal_places integer DEFAULT 2,
    
    -- Sport specificity
    sport_specific boolean DEFAULT false,
    sport varchar(50),
    
    -- Calculation rules
    higher_is_better boolean DEFAULT true,
    aggregation_method varchar(50) DEFAULT 'average', -- 'average', 'max', 'min', 'sum'
    
    -- Status
    is_active boolean DEFAULT true,
    is_custom boolean DEFAULT false,
    created_by uuid REFERENCES profiles(id),
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Import batch tracking
CREATE TABLE import_batches (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Import metadata
    filename varchar(255) NOT NULL,
    file_type varchar(50) NOT NULL,
    imported_by uuid NOT NULL REFERENCES profiles(id),
    team_id uuid NOT NULL REFERENCES teams(id),
    
    -- Import statistics
    total_rows integer NOT NULL,
    successful_rows integer DEFAULT 0,
    failed_rows integer DEFAULT 0,
    validation_errors jsonb,
    
    -- Processing status
    status varchar(50) DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
    started_at timestamp with time zone,
    completed_at timestamp with time zone,
    
    -- Data mapping
    column_mapping jsonb NOT NULL,
    import_options jsonb,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now()
);

-- Metric benchmarks for comparison
CREATE TABLE metric_benchmarks (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Benchmark identification
    metric_type varchar(100) NOT NULL REFERENCES metric_definitions(metric_type),
    age_group varchar(50),
    gender varchar(20),
    level varchar(50), -- 'beginner', 'intermediate', 'advanced', 'elite'
    
    -- Benchmark values
    percentile_25 decimal(10,3),
    percentile_50 decimal(10,3),
    percentile_75 decimal(10,3),
    percentile_90 decimal(10,3),
    
    -- Source
    source varchar(255),
    source_date date,
    sample_size integer,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    -- Unique constraint
    UNIQUE(metric_type, age_group, gender, level)
);
```

### RLS Policies [DEV1]

```sql
-- Performance metrics policies
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Coaches can manage their team metrics"
    ON performance_metrics
    FOR ALL
    USING (
        team_id IN (
            SELECT team_id FROM team_coaches 
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Players can view their own metrics"
    ON performance_metrics
    FOR SELECT
    USING (player_id = auth.uid());

CREATE POLICY "Parents can view their children's metrics"
    ON performance_metrics
    FOR SELECT
    USING (
        player_id IN (
            SELECT child_id FROM family_members 
            WHERE parent_id = auth.uid()
        )
    );

-- Metric definitions are readable by all authenticated users
CREATE POLICY "Metric definitions are public"
    ON metric_definitions
    FOR SELECT
    USING (auth.uid() IS NOT NULL);

CREATE POLICY "Custom metrics managed by creator"
    ON metric_definitions
    FOR ALL
    USING (is_custom = true AND created_by = auth.uid());
```

## Service Architecture

### MetricService [DEV3]

```typescript
// /src/features/perform/services/MetricService.ts

export interface MetricService {
  // Single metric operations
  saveMetric(data: MetricData): Promise<Metric>;
  updateMetric(id: string, data: UpdateMetricDTO): Promise<Metric>;
  deleteMetric(id: string): Promise<void>;
  getMetric(id: string): Promise<Metric>;
  
  // Bulk operations
  saveBulkMetrics(metrics: MetricData[]): Promise<BulkSaveResult>;
  updateBulkMetrics(updates: BulkUpdateDTO[]): Promise<BulkUpdateResult>;
  
  // Queries
  getPlayerMetrics(playerId: string, filters?: MetricFilters): Promise<Metric[]>;
  getTeamMetrics(teamId: string, filters?: MetricFilters): Promise<Metric[]>;
  getSessionMetrics(sessionId: string): Promise<Metric[]>;
  
  // Metric types
  getMetricDefinitions(): Promise<MetricDefinition[]>;
  getMetricCategories(): Promise<Category[]>;
  createCustomMetric(definition: CustomMetricDTO): Promise<MetricDefinition>;
  
  // Aggregations
  getPlayerSummary(playerId: string, dateRange?: DateRange): Promise<PlayerMetricSummary>;
  getTeamSummary(teamId: string, dateRange?: DateRange): Promise<TeamMetricSummary>;
  
  // Utilities
  detectAnomalies(metrics: Metric[]): AnomalyReport;
  calculateTrends(metrics: Metric[]): TrendAnalysis;
}

export class MetricServiceImpl implements MetricService {
  constructor(
    private supabase: SupabaseClient,
    private validationService: ValidationService
  ) {}
  
  async saveMetric(data: MetricData): Promise<Metric> {
    // Validate before saving
    const validation = await this.validationService.validateMetric(data);
    if (!validation.isValid) {
      throw new ValidationError(validation.errors);
    }
    
    // Check for anomalies
    const anomalyCheck = await this.checkForAnomalies(data);
    
    // Save to database
    const { data: metric, error } = await this.supabase
      .from('performance_metrics')
      .insert({
        ...data,
        anomaly_flag: anomalyCheck.isAnomaly
      })
      .select()
      .single();
      
    if (error) throw error;
    return metric;
  }
  
  // Additional implementations...
}
```

### ValidationService [DEV3]

```typescript
// /src/features/perform/services/ValidationService.ts

export interface ValidationService {
  // Metric validation
  validateMetric(data: MetricData): Promise<ValidationResult>;
  validateBulkMetrics(metrics: MetricData[]): Promise<BulkValidationResult>;
  
  // Range validation
  validateRange(value: number, metricType: string): RangeValidation;
  checkHistoricalConsistency(metric: MetricData, history: Metric[]): ConsistencyCheck;
  
  // Anomaly detection
  detectAnomaly(value: number, metricType: string, playerId: string): Promise<AnomalyCheck>;
  detectBulkAnomalies(metrics: MetricData[]): Promise<AnomalyReport>;
  
  // Import validation
  validateImportData(data: any[], mapping: ColumnMapping): ImportValidation;
  validateDataTypes(data: any[], expectedTypes: DataTypeMap): DataTypeValidation;
  
  // Custom rules
  applyCustomRules(data: MetricData, rules: ValidationRule[]): RuleValidation;
  validateAgainstBenchmarks(metric: MetricData): BenchmarkValidation;
}

export class ValidationServiceImpl implements ValidationService {
  constructor(private supabase: SupabaseClient) {}
  
  async validateMetric(data: MetricData): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    
    // Get metric definition
    const definition = await this.getMetricDefinition(data.metric_type);
    if (!definition) {
      errors.push({
        field: 'metric_type',
        message: 'Unknown metric type'
      });
      return { isValid: false, errors };
    }
    
    // Validate range
    if (definition.min_value !== null && data.value < definition.min_value) {
      errors.push({
        field: 'value',
        message: `Value must be at least ${definition.min_value}`
      });
    }
    
    if (definition.max_value !== null && data.value > definition.max_value) {
      errors.push({
        field: 'value',
        message: `Value must not exceed ${definition.max_value}`
      });
    }
    
    // Check for anomalies
    const anomalyCheck = await this.detectAnomaly(
      data.value,
      data.metric_type,
      data.player_id
    );
    
    if (anomalyCheck.isAnomaly) {
      errors.push({
        field: 'value',
        message: anomalyCheck.message,
        severity: 'warning'
      });
    }
    
    return {
      isValid: errors.filter(e => e.severity !== 'warning').length === 0,
      errors,
      warnings: errors.filter(e => e.severity === 'warning')
    };
  }
  
  // Additional implementations...
}
```

### ImportService [DEV4]

```typescript
// /src/features/perform/services/ImportService.ts

export interface ImportService {
  // File parsing
  parseCSV(file: File): Promise<ParsedData>;
  parseExcel(file: File): Promise<ParsedData>;
  detectFileType(file: File): FileType;
  
  // Data mapping
  suggestColumnMapping(headers: string[], metricTypes: string[]): ColumnMapping;
  applyColumnMapping(data: any[], mapping: ColumnMapping): MappedData;
  
  // Import processing
  createImportBatch(file: File, teamId: string): Promise<ImportBatch>;
  processImport(batchId: string, mapping: ColumnMapping): Promise<ImportResult>;
  getImportProgress(batchId: string): Promise<ImportProgress>;
  
  // Validation
  previewImport(data: any[], mapping: ColumnMapping): ImportPreview;
  validateImportBatch(data: MappedData): ImportValidation;
  
  // History
  getImportHistory(teamId: string): Promise<ImportBatch[]>;
  rollbackImport(batchId: string): Promise<void>;
}

export class ImportServiceImpl implements ImportService {
  constructor(
    private supabase: SupabaseClient,
    private metricService: MetricService,
    private validationService: ValidationService
  ) {}
  
  async parseCSV(file: File): Promise<ParsedData> {
    const text = await file.text();
    const lines = text.split('\n');
    const headers = lines[0].split(',').map(h => h.trim());
    
    const data = [];
    for (let i = 1; i < lines.length; i++) {
      if (lines[i].trim()) {
        const values = lines[i].split(',');
        const row: any = {};
        headers.forEach((header, index) => {
          row[header] = values[index]?.trim();
        });
        data.push(row);
      }
    }
    
    return {
      headers,
      data,
      rowCount: data.length,
      fileType: 'csv'
    };
  }
  
  async processImport(
    batchId: string,
    mapping: ColumnMapping
  ): Promise<ImportResult> {
    // Get import batch
    const batch = await this.getImportBatch(batchId);
    if (!batch) throw new Error('Import batch not found');
    
    // Update status
    await this.updateBatchStatus(batchId, 'processing');
    
    try {
      const results = {
        successful: 0,
        failed: 0,
        errors: []
      };
      
      // Process in chunks
      const chunkSize = 100;
      const chunks = this.chunkArray(batch.data, chunkSize);
      
      for (const chunk of chunks) {
        const mapped = this.applyColumnMapping(chunk, mapping);
        const validation = await this.validationService.validateBulkMetrics(mapped);
        
        // Save valid metrics
        if (validation.validMetrics.length > 0) {
          const saveResult = await this.metricService.saveBulkMetrics(
            validation.validMetrics.map(m => ({
              ...m,
              import_batch_id: batchId
            }))
          );
          results.successful += saveResult.successful;
          results.failed += saveResult.failed;
        }
        
        // Track errors
        results.errors.push(...validation.errors);
        results.failed += validation.invalidMetrics.length;
      }
      
      // Update batch with results
      await this.updateBatchResults(batchId, results);
      
      return results;
    } catch (error) {
      await this.updateBatchStatus(batchId, 'failed');
      throw error;
    }
  }
  
  // Additional implementations...
}
```

### AggregationService [DEV3]

```typescript
// /src/features/perform/services/AggregationService.ts

export interface AggregationService {
  // Player aggregations
  calculatePlayerAverage(playerId: string, metricType: string, period: DateRange): Promise<number>;
  calculatePlayerProgress(playerId: string, metricType: string): Promise<ProgressData>;
  getPlayerRankings(teamId: string, metricType: string): Promise<PlayerRanking[]>;
  
  // Team aggregations
  calculateTeamAverage(teamId: string, metricType: string, period: DateRange): Promise<number>;
  calculateTeamDistribution(teamId: string, metricType: string): Promise<Distribution>;
  compareTeamPeriods(teamId: string, period1: DateRange, period2: DateRange): Promise<Comparison>;
  
  // Trend analysis
  calculateTrend(metrics: Metric[]): TrendAnalysis;
  detectPatterns(metrics: Metric[]): Pattern[];
  forecastValue(metrics: Metric[], futureDate: Date): Forecast;
  
  // Benchmarking
  compareToBenchmarks(metric: Metric): BenchmarkComparison;
  calculatePercentile(value: number, metricType: string, ageGroup: string): number;
}

export class AggregationServiceImpl implements AggregationService {
  constructor(private supabase: SupabaseClient) {}
  
  // Implementation details...
}
```

## Implementation Timeline

### Phase 1: Database and Core Models (Days 1-2)

**Goal**: Complete metrics database schema and TypeScript types

**DEV1**: 
- Create performance_metrics table and migrations
- Create metric_definitions table with standard metrics
- Create import_batches table for tracking imports
- Define TypeScript interfaces for all metric types

**DEV2**:
- Set up metrics UI directory structure
- Create base component templates
- Design mobile-first entry interfaces
- Set up component storybook stories

**DEV3**:
- Create MetricService base implementation
- Implement ValidationService framework
- Set up calculation utilities
- Create service test structure

**DEV4**:
- Create ImportService base implementation
- Set up file parsing utilities
- Define import data structures
- Create batch processing framework

### Phase 2: Core Services (Day 3)

**Goal**: Implement service layer functionality

**DEV1**:
- Support DEV3 with database queries
- Create database functions for aggregations
- Optimize metric queries with indexes
- Create benchmark data seeds

**DEV2**:
- Create MetricEntryForm component
- Implement MetricTypeSelector
- Create validation feedback UI
- Build responsive layouts

**DEV3**:
- Complete MetricService CRUD operations
- Implement validation logic
- Create anomaly detection algorithm
- Build aggregation queries

**DEV4**:
- Implement CSV parser
- Create column mapping logic
- Build import preview functionality
- Set up batch processing

### Phase 3: UI Implementation (Day 4)

**Goal**: Complete all UI components

**DEV1**:
- Create metric definition management
- Support custom metric creation
- Implement benchmark management
- Test data integrity

**DEV2**:
- Complete BulkEntryGrid component
- Implement ImportWizard steps
- Create QuickMetricInput widgets
- Polish all UI components

**DEV3**:
- Create useMetrics hook
- Implement real-time validation
- Build calculation helpers
- Create metric trends logic

**DEV4**:
- Complete import processing
- Implement progress tracking
- Create rollback functionality
- Build import history UI

### Phase 4: Integration and Testing (Day 5)

**Goal**: Fully integrated metrics system

**DEV1**:
- Performance optimization
- Data migration scripts
- Security testing
- Documentation

**DEV2**:
- End-to-end UI testing
- Mobile responsiveness
- Accessibility improvements
- User documentation

**DEV3**:
- Integration testing
- Performance benchmarking
- Algorithm optimization
- API documentation

**DEV4**:
- Import stress testing
- Error handling improvements
- Batch processing optimization
- Import templates

## Integration Points

### DEV1 ↔ DEV2
- Metric type definitions (DEV1 provides schema, DEV2 displays options)
- Validation rules display (DEV1 defines rules, DEV2 shows feedback)
- Custom metric creation (DEV2 collects input, DEV1 stores definition)

### DEV1 ↔ DEV3
- Database query optimization (joint effort)
- Aggregation function implementation (DEV1 provides DB functions, DEV3 uses them)
- Data integrity maintenance (shared responsibility)

### DEV2 ↔ DEV3
- Real-time validation feedback (DEV3 validates, DEV2 displays)
- Calculation results display (DEV3 calculates, DEV2 shows)
- Error handling coordination (DEV3 throws errors, DEV2 catches and displays)

### DEV2 ↔ DEV4
- Import wizard UI (DEV2 provides UI, DEV4 processes data)
- Progress feedback (DEV4 reports progress, DEV2 displays)
- Error display during import (DEV4 validates, DEV2 shows errors)

### DEV3 ↔ DEV4
- Import data validation (DEV4 parses, DEV3 validates)
- Bulk save coordination (DEV4 prepares data, DEV3 saves)
- Anomaly detection during import (shared implementation)

### DEV1 ↔ DEV4
- Import batch tracking (DEV1 provides schema, DEV4 manages batches)
- Historical data preservation (joint design)
- Rollback implementation (DEV4 initiates, DEV1 executes)

## Key Design Decisions

1. **Flexible Metric System**: Support any measurement type without code changes
2. **Anomaly Detection**: Flag unusual values but don't block entry
3. **Bulk Operations First**: Optimize for team-wide data entry
4. **Import Preserves Original**: Keep original values for audit trail
5. **Progressive Validation**: Show warnings but allow override

## Success Metrics

### Performance
- Single metric save < 500ms [DEV3]
- Bulk entry for 20 players < 3 seconds [DEV3/DEV4]
- Import 1000 records < 30 seconds [DEV4]
- Validation feedback < 200ms [DEV3]

### User Experience
- Metric entry requires < 3 clicks [DEV2]
- Bulk entry supports copy/paste from Excel [DEV2]
- Import wizard completes in < 5 steps [DEV2/DEV4]
- Mobile entry works smoothly [DEV2]

### Data Quality
- 100% of metrics have valid types [DEV1/DEV3]
- Anomaly detection catches 90% of errors [DEV3]
- Import validation prevents bad data [DEV4]
- Historical data preserved accurately [All]

### Business Metrics
- 80% of coaches enter metrics weekly
- Average 10+ metrics per player per month
- 50% use bulk entry methods
- Zero data corruption incidents

## Migration Strategy

1. **Metric Definitions First**: Deploy standard metrics before allowing data entry
2. **Import Tools Ready**: Ensure import works before asking for historical data
3. **Pilot Testing**: Start with one team's historical data
4. **Validation Tuning**: Adjust anomaly detection based on real data
5. **Progressive Rollout**: Enable for teams as they're trained
6. **Historical Import**: Organized import sessions for each team

## Security Considerations

1. **Authentication**: All metric operations require authentication
2. **Authorization**: Coaches only access their team's metrics
3. **Data Validation**: All input sanitized and validated
4. **Audit Trail**: Track who entered/modified each metric
5. **Privacy**: Parent access limited to their children only

## Out of Scope (Week 2)

- Advanced statistical analysis
- Machine learning predictions
- Wearable device integration
- Video analysis correlation
- Automated metric capture
- Complex visualization charts
- Metric-based training recommendations
- Third-party API integrations

## Appendix

### Glossary

- **Metric Type**: A specific measurement (e.g., "40-yard dash", "vertical jump")
- **Anomaly**: A value significantly different from expected range
- **Bulk Entry**: Entering the same metric for multiple players
- **Import Batch**: A set of metrics imported together
- **Validation Rule**: Constraint on acceptable metric values

### References

#### Design Documents
- Week 1 Training Session Foundation
- MVP Perform System Overview

#### Related Features
- Training Sessions: Metrics can be linked to specific sessions
- Player Profiles: Metrics displayed in player development view
- Analytics Dashboard: Metrics feed into team analytics

---

## Document Metadata

**Last Updated**: 2024-03-14
**Status**: Ready for Implementation
**Feature Owner**: Product Team
**Technical Lead**: TBD
**Document Version**: 1.0