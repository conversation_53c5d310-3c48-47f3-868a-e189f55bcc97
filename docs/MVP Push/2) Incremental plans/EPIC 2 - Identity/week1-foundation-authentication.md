# Week 1: Foundation & Authentication Implementation Plan

## Developer Assignments

### DEV1 - Core Authentication System
**Focus**: Establish the authentication foundation with unified login and session management
- Create identity feature directory structure
- Implement unified login page replacing 5+ variants
- Build authentication service with Supabase integration
- Set up session management and token handling
- Create route configuration and redirects

### DEV2 - Family Data Model
**Focus**: Design and implement the family relationship infrastructure
- Design family relationship database schema
- Create FamilyService interface and core methods
- Build account switching hook and context
- Set up identity context provider
- Design child account email generation logic

### DEV3 - Profile Schema Design
**Focus**: Establish profile data structures and avatar system foundation
- Design profile completion tracking schema
- Create ProfileService interface
- Define SportHead avatar system structure
- Build TypeScript type definitions for profiles
- Set up profile validation rules

### DEV4 - Onboarding Architecture
**Focus**: Create the invitation and onboarding system foundation
- Design onboarding invitation database schema
- Create OnboardingService interface
- Define onboarding flow state machines
- Build invitation token validation system
- Set up onboarding progress tracking

---

## Overview

Week 1 focuses on establishing the foundational infrastructure for the Identity System. By the end of this week, we'll have a working unified login system, core database schemas designed, and the service architecture in place. This sets up all developers to build features independently in Week 2.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Page Flow and Purpose](#page-flow-and-purpose)
3. [Component Structure](#component-structure)
4. [Database Design](#database-design)
5. [Service Architecture](#service-architecture)
6. [Implementation Timeline](#implementation-timeline)
7. [Integration Points](#integration-points)
8. [Success Metrics](#success-metrics)

## Architecture Overview

### Directory Structure

```
src/features/identity/
├── components/
│   ├── shared/
│   │   ├── PermissionGuard.tsx [DEV1]
│   │   ├── LoadingSpinner.tsx [DEV1]
│   │   └── ErrorBoundary.tsx [DEV1]
│   ├── auth/
│   │   ├── LoginForm.tsx [DEV1]
│   │   ├── EmailUsernameInput.tsx [DEV1]
│   │   └── RememberMeCheckbox.tsx [DEV1]
│   └── layout/
│       └── IdentityLayout.tsx [DEV1]
├── pages/
│   ├── LoginPage.tsx [DEV1]
│   └── PasswordResetPage.tsx [DEV1]
├── hooks/
│   ├── useAuth.ts [DEV1]
│   ├── useSession.ts [DEV1]
│   ├── useAccountSwitching.ts [DEV2]
│   └── useFamily.ts [DEV2]
├── services/
│   ├── AuthenticationService.ts [DEV1]
│   ├── SessionService.ts [DEV1]
│   ├── FamilyService.ts [DEV2]
│   ├── ProfileService.ts [DEV3]
│   └── OnboardingService.ts [DEV4]
├── routes/
│   └── IdentityRoutes.tsx [DEV1]
├── contexts/
│   └── IdentityContext.tsx [DEV2]
└── types/
    ├── auth.types.ts [DEV1]
    ├── family.types.ts [DEV2]
    ├── profile.types.ts [DEV3]
    └── onboarding.types.ts [DEV4]
```

## Page Flow and Purpose

### Week 1 Page Flow Diagram

```mermaid
graph TB
    Start([User Arrives]) --> LoginCheck{Authenticated?}
    
    %% Not Authenticated Flow
    LoginCheck --> |No| LoginPage[LoginPage - DEV1]
    LoginPage --> |Success| Dashboard[Temporary Success Page]
    LoginPage --> |Forgot Password| PasswordReset[PasswordResetPage - DEV1]
    
    %% Authenticated Flow (Basic)
    LoginCheck --> |Yes| Dashboard
    
    %% Password Reset Flow
    PasswordReset --> |Email Sent| LoginPage
```

### Page Details

#### 1. Login Page [DEV1]

**Purpose**: Single unified authentication page replacing all existing login variants

**Path**: `/src/features/identity/pages/LoginPage.tsx`
**Route**: `/login`

```typescript
/**
 * LoginPage
 * 
 * PURPOSE:
 * - Single authentication page replacing 5+ login variants
 * - Support email login for adults (parents/coaches)
 * - Support username login for children (<NAME_EMAIL>)
 * - Clear path to password reset
 * - Foundation for future social login
 * 
 * USER GOALS:
 * - Login quickly with email or username
 * - Access password reset if needed
 * - See consistent, professional interface
 * - Get clear error messages
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowInput } from '@/foundation/design-system/components/atoms/Input';
import { ShadowToast } from '@/foundation/design-system/components/atoms/Toast';
import { ShadowCard } from '@/foundation/design-system/components/molecules/Cards';
```

**New Components Required:**
```typescript
// Unified login form supporting email/username
import { LoginForm } from '@/src/features/identity/components/auth/LoginForm';
// Path: /src/features/identity/components/auth/LoginForm.tsx

// Email or username input with smart detection
import { EmailUsernameInput } from '@/src/features/identity/components/auth/EmailUsernameInput';
// Path: /src/features/identity/components/auth/EmailUsernameInput.tsx

// Remember me checkbox
import { RememberMeCheckbox } from '@/src/features/identity/components/auth/RememberMeCheckbox';
// Path: /src/features/identity/components/auth/RememberMeCheckbox.tsx
```

**Services Used:**
```typescript
import { AuthenticationService } from '@/src/features/identity/services/AuthenticationService';
// Methods: 
// - login(credentials: {identifier: string, password: string}): Promise<AuthResult>
// - detectIdentifierType(identifier: string): 'email' | 'username'
```

**Hooks Used:**
```typescript
import { useAuth } from '@/src/features/identity/hooks/useAuth';
// Returns: { login, loading, error }
```

#### 2. Password Reset Page [DEV1]

**Purpose**: Allow users to reset forgotten passwords via email

**Path**: `/src/features/identity/pages/PasswordResetPage.tsx`
**Route**: `/password-reset`

```typescript
/**
 * PasswordResetPage
 * 
 * PURPOSE:
 * - Send password reset emails
 * - Handle parent requests for child password resets
 * - Provide clear feedback on email status
 * - Link back to login page
 * 
 * USER GOALS:
 * - Reset my forgotten password
 * - Get confirmation email was sent
 * - Return to login easily
 */
```

## Route Configuration

```typescript
// /src/features/identity/routes/IdentityRoutes.tsx [DEV1]
export const IdentityRoutes = () => (
  <Switch>
    {/* Public routes */}
    <Route path="/login" component={LoginPage} />
    <Route path="/password-reset" component={PasswordResetPage} />
    
    {/* Legacy redirects - Week 1 */}
    <Redirect from="/UpdatedLoginV2" to="/login" />
    <Redirect from="/UpdatedLogin" to="/login" />
    <Redirect from="/Login" to="/login" />
    <Redirect from="/LoginUpdated" to="/login" />
    <Redirect from="/CapLogin" to="/login" />
  </Switch>
);
```

## Database Design

### Authentication Tables [DEV1]

```sql
-- Session management for future account switching
CREATE TABLE identity.user_sessions (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid NOT NULL REFERENCES auth.users(id),
    active_profile_id uuid NOT NULL REFERENCES public.profiles(id),
    session_token text UNIQUE NOT NULL,
    device_info jsonb,
    last_activity timestamp with time zone DEFAULT now(),
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);

-- Index for fast session lookups
CREATE INDEX idx_user_sessions_token ON identity.user_sessions(session_token);
CREATE INDEX idx_user_sessions_user_id ON identity.user_sessions(user_id);
```

### Family Relationship Tables [DEV2]

```sql
-- Family relationships foundation
CREATE TABLE identity.family_relationships (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    parent_profile_id uuid NOT NULL REFERENCES public.profiles(id),
    child_profile_id uuid NOT NULL REFERENCES public.profiles(id),
    relationship_type text NOT NULL CHECK (relationship_type IN ('parent', 'guardian')),
    is_primary boolean DEFAULT false,
    permissions jsonb DEFAULT '{"can_view": true, "can_edit": true, "can_approve": true}'::jsonb,
    created_at timestamp with time zone DEFAULT now(),
    
    CONSTRAINT unique_parent_child UNIQUE (parent_profile_id, child_profile_id)
);

-- Child account metadata
CREATE TABLE identity.child_accounts (
    profile_id uuid PRIMARY KEY REFERENCES public.profiles(id),
    generated_username text UNIQUE NOT NULL,
    generated_email text UNIQUE NOT NULL CHECK (generated_email LIKE '%@shot.io'),
    created_at timestamp with time zone DEFAULT now()
);
```

### Profile Enhancement Tables [DEV3]

```sql
-- Profile completion tracking
CREATE TABLE identity.profile_completion (
    profile_id uuid PRIMARY KEY REFERENCES public.profiles(id),
    basic_info_complete boolean DEFAULT false,
    avatar_selected boolean DEFAULT false,
    contact_info_complete boolean DEFAULT false,
    completion_percentage integer DEFAULT 0,
    last_updated timestamp with time zone DEFAULT now()
);

-- Avatar configuration placeholder
CREATE TABLE identity.avatar_configs (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    profile_id uuid NOT NULL REFERENCES public.profiles(id),
    sport_head_id text,
    created_at timestamp with time zone DEFAULT now()
);
```

### Onboarding Tables [DEV4]

```sql
-- Onboarding invitations
CREATE TABLE identity.onboarding_invitations (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    token text UNIQUE NOT NULL,
    invite_type text NOT NULL CHECK (invite_type IN ('player', 'parent', 'coach')),
    team_id uuid REFERENCES public.teams(id),
    email text,
    phone text,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);

-- Index for token lookups
CREATE INDEX idx_onboarding_token ON identity.onboarding_invitations(token);
```

## Service Architecture

### AuthenticationService [DEV1]

```typescript
// /src/features/identity/services/AuthenticationService.ts

export interface LoginCredentials {
  identifier: string; // email or username
  password: string;
  rememberMe?: boolean;
}

export interface AuthResult {
  user: User;
  session: Session;
  profile: Profile;
}

export class AuthenticationService {
  // Week 1 Implementation
  async login(credentials: LoginCredentials): Promise<AuthResult> {
    const identifierType = this.detectIdentifierType(credentials.identifier);
    const email = identifierType === 'username' 
      ? `${credentials.identifier}@shot.io`
      : credentials.identifier;
    
    // Supabase auth
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password: credentials.password
    });
    
    if (error) throw error;
    
    // Get profile
    const profile = await this.getProfile(data.user.id);
    
    return {
      user: data.user,
      session: data.session,
      profile
    };
  }
  
  detectIdentifierType(identifier: string): 'email' | 'username' {
    return identifier.includes('@') ? 'email' : 'username';
  }
  
  async resetPassword(email: string): Promise<void> {
    await supabase.auth.resetPasswordForEmail(email);
  }
}
```

### FamilyService [DEV2]

```typescript
// /src/features/identity/services/FamilyService.ts

export interface FamilyService {
  // Week 1: Interface definition only
  generateChildEmail(username: string): string;
  validateUsername(username: string): Promise<boolean>;
  checkUsernameAvailability(username: string): Promise<boolean>;
}

export class FamilyServiceImpl implements FamilyService {
  generateChildEmail(username: string): string {
    return `${username}@shot.io`;
  }
  
  async validateUsername(username: string): Promise<boolean> {
    // Username rules: alphanumeric, 3-20 chars, no spaces
    const regex = /^[a-zA-Z0-9]{3,20}$/;
    return regex.test(username);
  }
  
  async checkUsernameAvailability(username: string): Promise<boolean> {
    const { data } = await supabase
      .from('child_accounts')
      .select('generated_username')
      .eq('generated_username', username)
      .single();
    
    return !data;
  }
}
```

### ProfileService [DEV3]

```typescript
// /src/features/identity/services/ProfileService.ts

export interface ProfileCompleteness {
  percentage: number;
  missingFields: string[];
}

export interface ProfileService {
  // Week 1: Interface definition and basic structure
  getProfileCompleteness(profileId: string): Promise<ProfileCompleteness>;
  getRequiredFields(role: UserRole): string[];
}

export class ProfileServiceImpl implements ProfileService {
  async getProfileCompleteness(profileId: string): Promise<ProfileCompleteness> {
    // Week 1: Basic implementation
    const { data } = await supabase
      .from('profile_completion')
      .select('*')
      .eq('profile_id', profileId)
      .single();
    
    return {
      percentage: data?.completion_percentage || 0,
      missingFields: [] // To be implemented
    };
  }
  
  getRequiredFields(role: UserRole): string[] {
    const baseFields = ['full_name', 'date_of_birth'];
    
    switch (role) {
      case 'player':
        return [...baseFields, 'jersey_number', 'position'];
      case 'parent':
        return [...baseFields, 'phone', 'email'];
      case 'coach':
        return [...baseFields, 'phone', 'email', 'certification'];
      default:
        return baseFields;
    }
  }
}
```

### OnboardingService [DEV4]

```typescript
// /src/features/identity/services/OnboardingService.ts

export interface InviteValidation {
  valid: boolean;
  expired: boolean;
  type: 'player' | 'parent' | 'coach';
  teamId?: string;
}

export interface OnboardingService {
  // Week 1: Core validation methods
  validateInviteToken(token: string): Promise<InviteValidation>;
  createInvitation(data: CreateInviteData): Promise<string>;
}

export class OnboardingServiceImpl implements OnboardingService {
  async validateInviteToken(token: string): Promise<InviteValidation> {
    const { data } = await supabase
      .from('onboarding_invitations')
      .select('*')
      .eq('token', token)
      .single();
    
    if (!data) {
      return { valid: false, expired: false, type: 'player' };
    }
    
    const expired = new Date(data.expires_at) < new Date();
    
    return {
      valid: !expired,
      expired,
      type: data.invite_type,
      teamId: data.team_id
    };
  }
  
  async createInvitation(data: CreateInviteData): Promise<string> {
    const token = this.generateSecureToken();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days
    
    await supabase.from('onboarding_invitations').insert({
      token,
      invite_type: data.type,
      team_id: data.teamId,
      email: data.email,
      expires_at: expiresAt
    });
    
    return token;
  }
  
  private generateSecureToken(): string {
    return crypto.randomUUID();
  }
}
```

## Implementation Timeline

### Monday - Tuesday (Days 1-2)

**DEV1**: 
- Set up identity directory structure
- Create base routing configuration
- Implement LoginPage component
- Start AuthenticationService

**DEV2**:
- Design family database schema
- Create migration files
- Set up FamilyService interface
- Create identity context

**DEV3**:
- Design profile schemas
- Create ProfileService interface
- Define TypeScript types
- Plan avatar system

**DEV4**:
- Design invitation schema
- Create OnboardingService interface
- Set up token generation
- Plan state machines

### Wednesday - Thursday (Days 3-4)

**DEV1**:
- Complete AuthenticationService
- Add password reset flow
- Implement session management
- Create auth hooks

**DEV2**:
- Implement username validation
- Create account switching hook
- Build identity provider
- Test family relationships

**DEV3**:
- Implement profile completeness
- Create validation rules
- Build type guards
- Document field requirements

**DEV4**:
- Implement token validation
- Create invitation system
- Build progress tracking
- Test invitation flow

### Friday (Day 5)

**All Developers**:
- Integration testing
- Documentation updates
- Code reviews
- Prepare for Week 2

## Integration Points

### DEV1 ↔ DEV2
- Authentication service provides user context for family features
- Session management will support account switching (foundation only)
- Child email generation uses shared format

### DEV1 ↔ DEV3
- Authentication returns profile data
- Profile service used in login flow
- Shared user/profile types

### DEV1 ↔ DEV4
- Login page will link to onboarding (Week 2)
- Shared authentication after onboarding
- Token validation for auto-login

### DEV2 ↔ DEV3
- Family relationships affect profile access
- Child profiles have special rules
- Shared profile interfaces

## Key Design Decisions

1. **Unified Login First**: Focus on replacing 5+ login pages immediately
2. **Username Support**: Built-in from day 1 for child accounts
3. **Service Interfaces**: Define contracts before implementation
4. **Database First**: Schema changes early to avoid migrations
5. **Feature Flags**: USE_UNIFIED_LOGIN environment variable ready

## Success Metrics

### Performance
- Login page loads in < 1 second [DEV1]
- Authentication completes in < 2 seconds [DEV1]
- Database queries optimized with indexes [All]

### Code Quality
- 100% TypeScript coverage [All]
- Service interfaces documented [All]
- Unit tests for critical paths [All]

### Integration
- All routes properly configured [DEV1]
- Services can be called independently [All]
- No circular dependencies [All]

### User Experience
- Clean, professional login page [DEV1]
- Clear error messages [DEV1]
- Loading states implemented [DEV1]

## Security Considerations

1. **Password Handling**: Never log or store plaintext passwords
2. **Session Security**: Secure session tokens with proper expiration
3. **Username Validation**: Prevent injection attacks
4. **Rate Limiting**: Plan for brute force protection
5. **CORS**: Proper configuration for API calls

## Testing Requirements

### Unit Tests
- AuthenticationService methods [DEV1]
- Username validation logic [DEV2]
- Profile completeness calculation [DEV3]
- Token generation and validation [DEV4]

### Integration Tests
- Login flow end-to-end [DEV1]
- Database schema integrity [All]
- Service communication [All]

### Manual Testing
- Login with email and username [DEV1]
- Password reset flow [DEV1]
- Legacy URL redirects [DEV1]

## Out of Scope (Week 1)

- Social login providers
- Complex family management UI
- Avatar selection interface
- Onboarding flows (UI)
- Email sending (just interface)
- Account switching UI
- Profile editing pages

## Week 1 Deliverables

### DEV1
- Unified login page deployed
- Authentication service functional
- Password reset page working
- Legacy redirects in place

### DEV2
- Family schema deployed
- FamilyService interface complete
- Username validation working
- Identity context ready

### DEV3
- Profile schema deployed
- ProfileService interface complete
- Type definitions finalized
- Validation rules documented

### DEV4
- Invitation schema deployed
- OnboardingService interface complete
- Token system working
- Progress tracking ready

---

## Document Metadata

**Last Updated**: 2025-08-17
**Status**: Ready for Implementation
**Week**: 1 of 4
**Technical Lead**: TBD
**Document Version**: 1.0