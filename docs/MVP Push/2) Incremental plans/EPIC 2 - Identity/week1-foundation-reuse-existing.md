# Week 1: Foundation & Authentication (Reusing Existing Code)

## Developer Assignments

### DEV1 - Authentication Wrapper & Consolidation
**Focus**: Create unified login experience by wrapping existing auth code
- Create identity feature directory structure
- Wrap UpdatedLoginV2 in new LoginPage component
- Set up route consolidation with redirects
- Implement feature flag system
- Ensure backward compatibility

### DEV2 - Family Infrastructure Wrapper
**Focus**: Organize existing family code under identity system
- Wrap existing account switching functionality
- Create IdentityContext using existing UserContext
- Set up family service wrappers
- Organize existing hooks under identity structure
- Plan enhancement points for Week 2

### DEV3 - Profile System Organization
**Focus**: Consolidate existing profile code and plan enhancements
- Wrap ProfileService with enhancement points
- Organize existing profile components
- Create profile completion calculation using existing data
- Set up profile type definitions
- Plan missing features for Week 2

### DEV4 - Onboarding Analysis & Planning
**Focus**: Analyze existing onboarding and plan role-specific flows
- Document existing QuickOnboarding flow
- Identify reusable components
- Design invitation token system
- Plan role-specific enhancements
- Create onboarding service interface

---

## Overview

Week 1 focuses on creating the identity system structure while **preserving all existing functionality**. We wrap existing components and services, adding a thin layer that allows for future enhancements without breaking current features. All existing routes, components, and APIs continue to work exactly as before.

## Code Reuse Map

### 🔄 Components We're Wrapping (Not Replacing)

| Existing Component | New Wrapper | Purpose |
|-------------------|-------------|---------|
| `/src/pages/UpdatedLoginV2.tsx` | `/src/features/identity/pages/LoginPage.tsx` | Unified login entry |
| `/src/hooks/useAccountSwitching.ts` | `/src/features/identity/hooks/useIdentitySwitching.ts` | Enhanced switching |
| `/src/services/ProfileService.ts` | `/src/features/identity/services/IdentityProfileService.ts` | Extended profile ops |
| `/src/pages/ManageChildren.tsx` | `/src/features/identity/pages/FamilyManagementPage.tsx` | Enhanced family mgmt |

### ✅ Code We're Using As-Is

| Existing Code | Usage |
|--------------|-------|
| `/src/foundation/auth/*` | Complete auth system - no changes |
| `/src/lib/supabase.ts` | Supabase client - no changes |
| `/src/components/shadow/ShadowAccountSwitcher.tsx` | Account switcher - use directly |
| `/src/contexts/UserContext.tsx` | User context - extend in IdentityContext |
| All Shadow DOM components | UI components - use directly |

## Directory Structure (Week 1)

```
src/features/identity/
├── components/
│   └── shared/
│       └── FeatureFlag.tsx [DEV1] *NEW*
├── pages/
│   ├── LoginPage.tsx [DEV1] *NEW - Wraps UpdatedLoginV2*
│   └── IdentityDashboard.tsx [DEV2] *NEW - Placeholder*
├── hooks/
│   ├── useIdentity.ts [DEV2] *NEW - Combines existing hooks*
│   └── useFeatureFlag.ts [DEV1] *NEW*
├── services/
│   ├── IdentityProfileService.ts [DEV3] *NEW - Extends ProfileService*
│   └── IdentityAuthService.ts [DEV1] *NEW - Wraps auth*
├── routes/
│   └── IdentityRoutes.tsx [DEV1] *NEW*
├── contexts/
│   └── IdentityContext.tsx [DEV2] *NEW - Wraps UserContext*
└── types/
    └── identity.types.ts [All] *NEW*
```

## Implementation Details by Developer

### DEV1 - Authentication Wrapper Implementation

#### 1. Create LoginPage Wrapper
```typescript
// /src/features/identity/pages/LoginPage.tsx
import React from 'react';
import UpdatedLoginV2 from '@/src/pages/UpdatedLoginV2';
import { useFeatureFlag } from '../hooks/useFeatureFlag';

export const LoginPage: React.FC = () => {
  const showNewFeatures = useFeatureFlag('IDENTITY_ENHANCEMENTS');
  
  // Week 1: Just wrap existing login
  // Week 2+: Add enhancements conditionally
  return (
    <div className="identity-login-wrapper">
      <UpdatedLoginV2 />
      {showNewFeatures && (
        <div className="coming-soon">
          {/* Future: Social login, biometrics, etc */}
        </div>
      )}
    </div>
  );
};
```

#### 2. Route Consolidation
```typescript
// /src/features/identity/routes/IdentityRoutes.tsx
import { Route, Redirect, Switch } from 'react-router-dom';
import { LoginPage } from '../pages/LoginPage';

export const IdentityRoutes = () => (
  <Switch>
    {/* New unified route */}
    <Route exact path="/login" component={LoginPage} />
    
    {/* Preserve all existing routes with redirects */}
    <Redirect from="/UpdatedLoginV2" to="/login" />
    <Redirect from="/UpdatedLogin" to="/login" />
    <Redirect from="/Login" to="/login" />
    <Redirect from="/LoginUpdated" to="/login" />
    <Redirect from="/CapLogin" to="/login" />
    <Redirect from="/SimpleLogin" to="/login" />
    
    {/* Future routes (Week 2+) */}
    <Route path="/identity" component={() => <div>Coming Soon</div>} />
  </Switch>
);
```

#### 3. Feature Flag System
```typescript
// /src/features/identity/hooks/useFeatureFlag.ts
export const useFeatureFlag = (flag: string): boolean => {
  // Week 1: All flags default to false
  // This ensures no new features are active yet
  const flags = {
    IDENTITY_ENHANCEMENTS: false,
    UNIFIED_LOGIN: false,
    ENHANCED_PROFILES: false,
    FAMILY_DASHBOARD: false,
  };
  
  // Check environment variable override
  const envFlag = process.env[`REACT_APP_FF_${flag}`];
  if (envFlag !== undefined) {
    return envFlag === 'true';
  }
  
  return flags[flag] || false;
};
```

#### 4. Auth Service Wrapper
```typescript
// /src/features/identity/services/IdentityAuthService.ts
import { useAuth } from '@/src/foundation/auth/useAuth';
import { supabase } from '@/src/lib/supabase';

export class IdentityAuthService {
  // Week 1: Just wrap existing auth
  static async login(email: string, password: string) {
    // Use existing Supabase auth
    return supabase.auth.signInWithPassword({ email, password });
  }
  
  static async logout() {
    return supabase.auth.signOut();
  }
  
  // Future enhancement point
  static async loginWithUsername(username: string, password: string) {
    // Week 2: Add username support
    const email = username.includes('@') ? username : `${username}@shot.io`;
    return this.login(email, password);
  }
}
```

### DEV2 - Family Infrastructure Wrapper

#### 1. Identity Context Wrapper
```typescript
// /src/features/identity/contexts/IdentityContext.tsx
import React, { useContext } from 'react';
import { UserContext } from '@/src/contexts/UserContext';
import { useAccountSwitching } from '@/src/hooks/useAccountSwitching';

interface IdentityContextValue {
  // All existing UserContext values
  user: any;
  profile: any;
  loading: boolean;
  
  // Enhanced identity features
  currentIdentity: any;
  availableIdentities: any[];
  switchIdentity: (profileId: string) => Promise<void>;
}

const IdentityContext = React.createContext<IdentityContextValue | null>(null);

export const IdentityProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Reuse existing contexts and hooks
  const userContext = useContext(UserContext);
  const accountSwitching = useAccountSwitching();
  
  // Week 1: Just combine existing functionality
  const value: IdentityContextValue = {
    ...userContext,
    currentIdentity: userContext.profile,
    availableIdentities: accountSwitching.availableAccounts || [],
    switchIdentity: accountSwitching.switchAccount,
  };
  
  return (
    <IdentityContext.Provider value={value}>
      {children}
    </IdentityContext.Provider>
  );
};

export const useIdentity = () => {
  const context = useContext(IdentityContext);
  if (!context) {
    throw new Error('useIdentity must be used within IdentityProvider');
  }
  return context;
};
```

#### 2. Identity Dashboard Placeholder
```typescript
// /src/features/identity/pages/IdentityDashboard.tsx
import React from 'react';
import { ShadowAccountSwitcher } from '@/src/components/shadow/ShadowAccountSwitcher';
import { useAccountSwitching } from '@/src/hooks/useAccountSwitching';
import { useUserChildren } from '@/src/hooks/useUserChildren';

export const IdentityDashboard: React.FC = () => {
  const { currentAccount, availableAccounts } = useAccountSwitching();
  const { children } = useUserChildren();
  
  // Week 1: Basic dashboard using existing components
  return (
    <div className="identity-dashboard">
      <h1>Identity Dashboard (Coming Soon)</h1>
      
      {/* Reuse existing account switcher */}
      <ShadowAccountSwitcher 
        variant="horizontal-bar"
        accounts={availableAccounts}
        currentAccountId={currentAccount?.id}
      />
      
      {/* Week 2: Add more features here */}
      <div className="placeholder">
        <p>Current Account: {currentAccount?.full_name}</p>
        <p>Children: {children?.length || 0}</p>
      </div>
    </div>
  );
};
```

### DEV3 - Profile System Organization

#### 1. Enhanced Profile Service
```typescript
// /src/features/identity/services/IdentityProfileService.ts
import { ProfileService } from '@/src/services/ProfileService';

export class IdentityProfileService extends ProfileService {
  // Inherit all existing ProfileService methods
  
  // Week 1: Add completion calculation
  static async getProfileCompletion(profileId: string): Promise<number> {
    const profile = await this.getProfile(profileId);
    
    // Basic completion based on existing fields
    const requiredFields = ['full_name', 'date_of_birth', 'avatar_url'];
    const completedFields = requiredFields.filter(field => profile[field]);
    
    return Math.round((completedFields.length / requiredFields.length) * 100);
  }
  
  // Week 1: Identify missing fields
  static getMissingFields(profile: any): string[] {
    const requiredFields = {
      full_name: 'Full Name',
      date_of_birth: 'Date of Birth',
      avatar_url: 'Avatar',
      phone: 'Phone Number',
    };
    
    return Object.entries(requiredFields)
      .filter(([field]) => !profile[field])
      .map(([_, label]) => label);
  }
}
```

#### 2. Profile Types Definition
```typescript
// /src/features/identity/types/identity.types.ts
// Import existing types
import { Profile } from '@/src/types/profile';
import { User } from '@supabase/supabase-js';

// Extend with identity-specific types
export interface IdentityProfile extends Profile {
  completionPercentage?: number;
  missingFields?: string[];
  lastUpdated?: Date;
}

export interface IdentityUser extends User {
  activeProfile?: IdentityProfile;
  linkedProfiles?: IdentityProfile[];
}

// Week 1: Basic types, Week 2+: Expand as needed
```

### DEV4 - Onboarding Analysis

#### 1. Document Existing Onboarding
```typescript
// /src/features/identity/docs/existing-onboarding-analysis.md
/**
 * Existing Onboarding Analysis
 * 
 * Current Flow (QuickOnboarding):
 * 1. Account creation step
 * 2. Child setup step (optional)
 * 3. Completion step
 * 
 * Reusable Components:
 * - QuickAccountStep
 * - QuickChildStep
 * - ProfileStep
 * 
 * Missing Features:
 * - Role-specific flows
 * - Team invitations
 * - Coach verification
 * - SportHead avatar selection in onboarding
 */
```

#### 2. Onboarding Service Interface
```typescript
// /src/features/identity/services/OnboardingService.ts
export interface OnboardingService {
  // Week 1: Interface only, implementation in Week 2
  
  // Analyze existing user type
  detectUserRole(email: string, inviteToken?: string): Promise<'player' | 'parent' | 'coach'>;
  
  // Get appropriate onboarding flow
  getOnboardingSteps(role: string): OnboardingStep[];
  
  // Reuse existing registration
  createAccount(data: any): Promise<any>;
}

export interface OnboardingStep {
  id: string;
  title: string;
  component: string;
  required: boolean;
}
```

## Testing Strategy (Week 1)

### 1. Backward Compatibility Tests
```typescript
// Ensure all old routes still work
describe('Legacy Route Compatibility', () => {
  test('Old login routes redirect to new unified login', () => {
    // Test each legacy route
    ['/UpdatedLoginV2', '/Login', '/SimpleLogin'].forEach(route => {
      expect(navigate(route)).toRedirectTo('/login');
    });
  });
  
  test('Existing components still render', () => {
    // Verify UpdatedLoginV2 still works when wrapped
    render(<LoginPage />);
    expect(screen.getByText('Sign In')).toBeInTheDocument();
  });
});
```

### 2. Feature Flag Tests
```typescript
describe('Feature Flags', () => {
  test('New features hidden by default', () => {
    process.env.REACT_APP_FF_IDENTITY_ENHANCEMENTS = 'false';
    render(<LoginPage />);
    expect(screen.queryByText('Coming Soon')).not.toBeInTheDocument();
  });
});
```

## Week 1 Deliverables

### DEV1
- ✅ LoginPage wrapper for UpdatedLoginV2
- ✅ Route consolidation with all redirects working
- ✅ Feature flag system implemented
- ✅ Basic auth service wrapper
- ✅ No breaking changes to existing auth

### DEV2
- ✅ IdentityContext wrapping UserContext
- ✅ Identity dashboard placeholder
- ✅ Existing account switching still works
- ✅ Family hooks organized under identity
- ✅ No breaking changes to family features

### DEV3
- ✅ IdentityProfileService extending ProfileService
- ✅ Profile completion calculation logic
- ✅ Profile types defined
- ✅ Existing profile features still work
- ✅ Documentation of missing features

### DEV4
- ✅ Complete analysis of existing onboarding
- ✅ Onboarding service interface defined
- ✅ Role detection logic planned
- ✅ List of reusable components documented
- ✅ Gap analysis for Week 2

## Success Metrics

### Code Reuse
- 90%+ of existing code unchanged
- 0 breaking changes
- All tests still pass
- All routes still work

### Organization
- Clear identity feature structure
- Existing code properly wrapped
- Enhancement points identified
- Documentation complete

---

## Document Metadata

**Last Updated**: 2025-08-17
**Status**: Ready for Implementation
**Week**: 1 of 4
**Approach**: Wrapper-based, non-breaking
**Document Version**: 2.0