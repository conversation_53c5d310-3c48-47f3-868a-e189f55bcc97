# Week 3: Integration & Polish Implementation Plan

## Developer Assignments

### DEV1 - Authentication Integration & Security
**Focus**: Complete auth integration with all systems and enhance security
- Integrate unified login with all app entry points
- Implement proper session management across account switches
- Add rate limiting and brute force protection
- Complete password reset flow integration
- Ensure all auth guards work with new system

### DEV2 - Family System Polish
**Focus**: Polish family features and complete account switching experience
- Complete seamless account switching with proper context preservation
- Add family activity tracking and audit logs
- Implement parent approval workflows for child actions
- Polish family dashboard with better UX
- Add emergency contact quick access

### DEV3 - Profile Completion & Validation
**Focus**: Complete profile system with full validation and role-based features
- Implement comprehensive profile validation
- Add role-specific field validation
- Complete SportHead avatar customization
- Add profile export/import functionality
- Implement profile sharing (for coaches to view player profiles)

### DEV4 - Onboarding Completion
**Focus**: Complete all onboarding flows with team integration
- Complete player onboarding with team auto-join
- Finish parent onboarding with multi-child setup
- Implement coach verification and team assignment
- Add SMS/email verification throughout
- Create smooth handoff to main app after onboarding

---

## Overview

Week 3 focuses on integration, polish, and completing all user flows. We ensure all systems work together seamlessly, enhance the user experience, and add the final features needed for a production-ready identity system. All edge cases are handled and the system is thoroughly tested.

## Integration Architecture

### System Integration Map

```mermaid
graph TB
    Identity[Identity System] --> Auth[Authentication]
    Identity --> Profile[Profiles]
    Identity --> Family[Family Management]
    Identity --> Onboard[Onboarding]
    
    Auth --> Teams[Team System]
    Auth --> Events[Event System]
    Auth --> Assess[Assessment System]
    
    Profile --> Teams
    Profile --> Assess
    
    Family --> Events
    Family --> Teams
    
    Onboard --> Teams
    Onboard --> Profile
```

## Integration Points by Developer

### DEV1 - Authentication Integration

#### 1. Universal Auth Guard Enhancement
```typescript
// /src/features/identity/components/auth/EnhancedAuthGuard.tsx
import { AuthGuard } from '@/src/foundation/auth/AuthGuard';
import { useIdentity } from '../hooks/useIdentity';

export const EnhancedAuthGuard: React.FC<AuthGuardProps> = ({ 
  children, 
  requiredRole,
  requiredPermissions,
  fallback 
}) => {
  const { currentIdentity, permissions } = useIdentity();
  
  // Week 3: Add identity-aware permission checking
  const hasPermission = () => {
    if (requiredRole && currentIdentity.role !== requiredRole) {
      return false;
    }
    
    if (requiredPermissions) {
      return requiredPermissions.every(perm => 
        permissions.includes(perm)
      );
    }
    
    return true;
  };
  
  return (
    <AuthGuard>
      {hasPermission() ? children : fallback}
    </AuthGuard>
  );
};
```

#### 2. Session Management Integration
```typescript
// /src/features/identity/services/SessionManagementService.ts
export class SessionManagementService {
  static async handleAccountSwitch(
    fromProfileId: string, 
    toProfileId: string
  ) {
    // Preserve important session data
    const preservedData = {
      returnUrl: window.location.pathname,
      activeFilters: this.getActiveFilters(),
      unsavedChanges: this.getUnsavedChanges()
    };
    
    // Switch account
    await FamilyService.switchAccount(fromProfileId, toProfileId);
    
    // Restore context
    await this.restoreSessionContext(preservedData);
    
    // Update all dependent systems
    await this.notifySystemsOfSwitch(toProfileId);
  }
  
  private static async notifySystemsOfSwitch(newProfileId: string) {
    // Notify team system
    EventBus.emit('identity:switched', { profileId: newProfileId });
    
    // Clear caches that depend on user context
    await TeamService.clearCache();
    await EventService.clearCache();
    await AssessmentService.clearCache();
  }
}
```

#### 3. Rate Limiting Implementation
```typescript
// /src/features/identity/middleware/RateLimiter.ts
export class RateLimiter {
  private static attempts = new Map<string, number[]>();
  
  static async checkLoginAttempt(identifier: string): Promise<boolean> {
    const key = `login:${identifier}`;
    const now = Date.now();
    const windowMs = 15 * 60 * 1000; // 15 minutes
    const maxAttempts = 5;
    
    // Get attempts in current window
    const attempts = this.attempts.get(key) || [];
    const recentAttempts = attempts.filter(
      time => now - time < windowMs
    );
    
    if (recentAttempts.length >= maxAttempts) {
      // Log security event
      await SecurityLogger.log('rate_limit_exceeded', {
        identifier,
        attempts: recentAttempts.length
      });
      
      return false;
    }
    
    // Record this attempt
    recentAttempts.push(now);
    this.attempts.set(key, recentAttempts);
    
    return true;
  }
}
```

### DEV2 - Family System Polish

#### 1. Account Switch Context Preservation
```typescript
// /src/features/identity/hooks/useContextPreservation.ts
export const useContextPreservation = () => {
  const saveContext = useCallback(async () => {
    const context = {
      // Current page and params
      route: window.location.pathname,
      params: window.location.search,
      
      // UI state
      scrollPosition: window.scrollY,
      expandedSections: document.querySelectorAll('[data-expanded="true"]'),
      
      // Form data (if any)
      formData: this.captureFormData(),
      
      // Active filters/sorts
      filters: this.captureActiveFilters()
    };
    
    sessionStorage.setItem('identity:context', JSON.stringify(context));
  }, []);
  
  const restoreContext = useCallback(async () => {
    const saved = sessionStorage.getItem('identity:context');
    if (!saved) return;
    
    const context = JSON.parse(saved);
    
    // Restore UI state
    window.scrollTo(0, context.scrollPosition);
    
    // Restore form data if on same page
    if (window.location.pathname === context.route) {
      this.restoreFormData(context.formData);
    }
    
    sessionStorage.removeItem('identity:context');
  }, []);
  
  return { saveContext, restoreContext };
};
```

#### 2. Family Activity Tracking
```typescript
// /src/features/identity/components/family/FamilyActivityLog.tsx
export const FamilyActivityLog: React.FC = () => {
  const { familyMembers } = useFamily();
  const [activities, setActivities] = useState<Activity[]>([]);
  
  useEffect(() => {
    const loadActivities = async () => {
      const logs = await FamilyService.getFamilyActivities(
        familyMembers.map(m => m.id)
      );
      setActivities(logs);
    };
    
    loadActivities();
  }, [familyMembers]);
  
  return (
    <ShadowCard>
      <h3>Family Activity</h3>
      <div className="activity-timeline">
        {activities.map(activity => (
          <ActivityItem
            key={activity.id}
            activity={activity}
            member={familyMembers.find(m => m.id === activity.profileId)}
          />
        ))}
      </div>
    </ShadowCard>
  );
};
```

#### 3. Parent Approval System
```typescript
// /src/features/identity/services/ParentApprovalService.ts
export class ParentApprovalService {
  static async requestApproval(
    childId: string,
    action: ApprovalAction,
    metadata: any
  ): Promise<ApprovalRequest> {
    // Get parent(s) for this child
    const parents = await FamilyService.getParentsForChild(childId);
    
    // Create approval request
    const request = await supabase
      .from('approval_requests')
      .insert({
        child_id: childId,
        action_type: action,
        metadata,
        status: 'pending',
        parent_ids: parents.map(p => p.id),
        expires_at: this.getExpirationTime(action)
      })
      .select()
      .single();
    
    // Notify parents
    await NotificationService.notifyParents(parents, {
      type: 'approval_needed',
      childName: metadata.childName,
      action: this.getActionDescription(action)
    });
    
    return request.data;
  }
  
  static async processApproval(
    requestId: string,
    parentId: string,
    approved: boolean,
    notes?: string
  ) {
    const { data: request } = await supabase
      .from('approval_requests')
      .select('*')
      .eq('id', requestId)
      .single();
    
    // Verify parent has permission
    if (!request.parent_ids.includes(parentId)) {
      throw new Error('Unauthorized to approve this request');
    }
    
    // Update request
    await supabase
      .from('approval_requests')
      .update({
        status: approved ? 'approved' : 'rejected',
        approved_by: parentId,
        approval_notes: notes,
        processed_at: new Date()
      })
      .eq('id', requestId);
    
    // Execute approved action
    if (approved) {
      await this.executeApprovedAction(request);
    }
  }
}
```

### DEV3 - Profile System Completion

#### 1. Comprehensive Profile Validation
```typescript
// /src/features/identity/services/ProfileValidationService.ts
export class ProfileValidationService {
  static validateProfile(
    profile: Profile,
    role: UserRole
  ): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    
    // Base validation for all roles
    if (!profile.full_name || profile.full_name.length < 2) {
      errors.push({
        field: 'full_name',
        message: 'Full name is required (minimum 2 characters)'
      });
    }
    
    if (!profile.date_of_birth) {
      errors.push({
        field: 'date_of_birth',
        message: 'Date of birth is required'
      });
    } else {
      const age = this.calculateAge(profile.date_of_birth);
      if (age < 4 || age > 100) {
        errors.push({
          field: 'date_of_birth',
          message: 'Invalid age (must be between 4 and 100)'
        });
      }
    }
    
    // Role-specific validation
    switch (role) {
      case 'player':
        this.validatePlayerProfile(profile, errors, warnings);
        break;
      case 'parent':
        this.validateParentProfile(profile, errors, warnings);
        break;
      case 'coach':
        this.validateCoachProfile(profile, errors, warnings);
        break;
    }
    
    return {
      valid: errors.length === 0,
      errors,
      warnings,
      completeness: this.calculateCompleteness(profile, role)
    };
  }
  
  private static validatePlayerProfile(
    profile: Profile,
    errors: ValidationError[],
    warnings: ValidationWarning[]
  ) {
    // Player-specific fields
    if (!profile.jersey_number) {
      warnings.push({
        field: 'jersey_number',
        message: 'Jersey number helps coaches identify you'
      });
    }
    
    if (!profile.position) {
      warnings.push({
        field: 'position',
        message: 'Adding your position helps with team planning'
      });
    }
    
    // Medical info for minors
    if (this.calculateAge(profile.date_of_birth) < 18) {
      if (!profile.emergency_contact_name) {
        errors.push({
          field: 'emergency_contact_name',
          message: 'Emergency contact required for players under 18'
        });
      }
    }
  }
}
```

#### 2. SportHead Avatar Customization
```typescript
// /src/features/identity/components/profile/AvatarCustomizer.tsx
export const AvatarCustomizer: React.FC<{
  avatarId: string;
  onSave: (customization: AvatarCustomization) => void;
}> = ({ avatarId, onSave }) => {
  const [customization, setCustomization] = useState<AvatarCustomization>({
    primaryColor: '#1976d2',
    secondaryColor: '#ffffff',
    accessories: [],
    background: 'gradient-blue'
  });
  
  const colorOptions = [
    '#1976d2', '#d32f2f', '#388e3c', '#f57c00',
    '#7b1fa2', '#00796b', '#5d4037', '#616161'
  ];
  
  const accessoryOptions = [
    { id: 'headband', name: 'Headband', compatible: ['soccer', 'basketball'] },
    { id: 'cap', name: 'Cap', compatible: ['baseball'] },
    { id: 'glasses', name: 'Glasses', compatible: ['all'] },
    { id: 'wristbands', name: 'Wristbands', compatible: ['all'] }
  ];
  
  return (
    <div className="avatar-customizer">
      <div className="preview">
        <SportHeadAvatar
          id={avatarId}
          customization={customization}
          size="large"
        />
      </div>
      
      <div className="customization-options">
        <h4>Colors</h4>
        <div className="color-picker">
          {colorOptions.map(color => (
            <button
              key={color}
              className="color-option"
              style={{ backgroundColor: color }}
              onClick={() => setCustomization({
                ...customization,
                primaryColor: color
              })}
            />
          ))}
        </div>
        
        <h4>Accessories</h4>
        <div className="accessory-picker">
          {accessoryOptions
            .filter(acc => 
              acc.compatible.includes('all') || 
              acc.compatible.includes(getAvatarSport(avatarId))
            )
            .map(accessory => (
              <label key={accessory.id}>
                <input
                  type="checkbox"
                  checked={customization.accessories.includes(accessory.id)}
                  onChange={(e) => {
                    const accessories = e.target.checked
                      ? [...customization.accessories, accessory.id]
                      : customization.accessories.filter(a => a !== accessory.id);
                    setCustomization({ ...customization, accessories });
                  }}
                />
                {accessory.name}
              </label>
            ))}
        </div>
        
        <ShadowButton onClick={() => onSave(customization)}>
          Save Customization
        </ShadowButton>
      </div>
    </div>
  );
};
```

#### 3. Profile Sharing for Coaches
```typescript
// /src/features/identity/services/ProfileSharingService.ts
export class ProfileSharingService {
  static async shareProfileWithCoach(
    playerId: string,
    coachId: string,
    teamId: string
  ): Promise<void> {
    // Verify coach is actually coaching this player's team
    const isCoach = await TeamService.isCoachOfTeam(coachId, teamId);
    if (!isCoach) {
      throw new Error('Unauthorized: Not a coach of this team');
    }
    
    // Create sharing record
    await supabase
      .from('profile_shares')
      .insert({
        profile_id: playerId,
        shared_with: coachId,
        team_context: teamId,
        permissions: ['view', 'view_medical', 'view_performance'],
        expires_at: this.getSeasonEndDate()
      });
    
    // Log access grant
    await AuditLogger.log('profile_shared', {
      playerId,
      coachId,
      teamId,
      permissions: ['view', 'view_medical', 'view_performance']
    });
  }
  
  static async getSharedProfiles(coachId: string): Promise<SharedProfile[]> {
    const { data: shares } = await supabase
      .from('profile_shares')
      .select(`
        *,
        profile:profiles(*),
        team:teams(*)
      `)
      .eq('shared_with', coachId)
      .gte('expires_at', new Date().toISOString());
    
    return shares.map(share => ({
      ...share.profile,
      sharedContext: {
        team: share.team,
        permissions: share.permissions,
        expiresAt: share.expires_at
      }
    }));
  }
}
```

### DEV4 - Onboarding Flow Completion

#### 1. Player Onboarding with Team Auto-Join
```typescript
// /src/features/identity/pages/PlayerOnboardingPage.tsx
export const PlayerOnboardingPage: React.FC = () => {
  const { inviteToken } = useParams();
  const [inviteData, setInviteData] = useState<InviteData>();
  const [currentStep, setCurrentStep] = useState(0);
  
  const steps = [
    { component: WelcomeScreen, props: { teamName: inviteData?.teamName } },
    { component: AccountCreation, props: { email: inviteData?.email } },
    { component: ProfileSetup, props: { role: 'player' } },
    { component: AvatarSelection, props: {} },
    { component: TeamJoinConfirmation, props: { team: inviteData?.team } }
  ];
  
  const handleComplete = async (stepData: any) => {
    // Save progress
    await OnboardingService.saveProgress(currentStep, stepData);
    
    if (currentStep === steps.length - 1) {
      // Final step - complete onboarding
      await completePlayerOnboarding();
    } else {
      setCurrentStep(currentStep + 1);
    }
  };
  
  const completePlayerOnboarding = async () => {
    // Auto-join team
    await TeamService.joinTeam(inviteData.teamId, 'player');
    
    // Mark onboarding complete
    await OnboardingService.markComplete('player');
    
    // Navigate to team dashboard
    navigate(`/team/${inviteData.teamId}/dashboard`);
  };
  
  const CurrentStepComponent = steps[currentStep].component;
  
  return (
    <OnboardingLayout>
      <StepIndicator 
        steps={steps.length} 
        current={currentStep} 
      />
      <CurrentStepComponent
        {...steps[currentStep].props}
        onComplete={handleComplete}
        onBack={() => setCurrentStep(Math.max(0, currentStep - 1))}
      />
    </OnboardingLayout>
  );
};
```

#### 2. Parent Onboarding with Multi-Child Setup
```typescript
// /src/features/identity/components/onboarding/MultiChildSetup.tsx
export const MultiChildSetup: React.FC<{
  onComplete: (children: ChildData[]) => void;
}> = ({ onComplete }) => {
  const [children, setChildren] = useState<ChildData[]>([]);
  const [currentChild, setCurrentChild] = useState<Partial<ChildData>>({});
  
  const addChild = () => {
    if (validateChild(currentChild)) {
      setChildren([...children, currentChild as ChildData]);
      setCurrentChild({}); // Reset form
    }
  };
  
  const generateUsername = async (fullName: string) => {
    const base = fullName.toLowerCase()
      .replace(/\s+/g, '.')
      .replace(/[^a-z0-9.]/g, '');
    
    let username = base;
    let counter = 1;
    
    while (!await FamilyService.checkUsernameAvailability(username)) {
      username = `${base}${counter}`;
      counter++;
    }
    
    return username;
  };
  
  return (
    <div className="multi-child-setup">
      <h2>Add Your Children</h2>
      
      {/* List of added children */}
      {children.length > 0 && (
        <div className="added-children">
          <h3>Children Added:</h3>
          {children.map((child, index) => (
            <div key={index} className="child-summary">
              <span>{child.fullName} ({child.username})</span>
              <button onClick={() => removeChild(index)}>Remove</button>
            </div>
          ))}
        </div>
      )}
      
      {/* Add child form */}
      <div className="add-child-form">
        <h3>{children.length > 0 ? 'Add Another Child' : 'Add Your First Child'}</h3>
        
        <ShadowTextInput
          label="Child's Full Name"
          value={currentChild.fullName || ''}
          onChange={(e) => setCurrentChild({
            ...currentChild,
            fullName: e.target.value
          })}
        />
        
        <ShadowDatePicker
          label="Date of Birth"
          value={currentChild.dateOfBirth}
          onChange={(date) => setCurrentChild({
            ...currentChild,
            dateOfBirth: date
          })}
          max={new Date()} // Can't be born in the future
        />
        
        <div className="username-generator">
          <ShadowTextInput
            label="Username"
            value={currentChild.username || ''}
            onChange={(e) => setCurrentChild({
              ...currentChild,
              username: e.target.value
            })}
            helperText="This will be their login username"
          />
          <ShadowButton
            variant="secondary"
            onClick={async () => {
              const username = await generateUsername(currentChild.fullName);
              setCurrentChild({ ...currentChild, username });
            }}
          >
            Generate
          </ShadowButton>
        </div>
        
        <ShadowButton onClick={addChild}>
          Add Child
        </ShadowButton>
      </div>
      
      <div className="actions">
        <ShadowButton
          variant="primary"
          disabled={children.length === 0}
          onClick={() => onComplete(children)}
        >
          Continue ({children.length} {children.length === 1 ? 'child' : 'children'})
        </ShadowButton>
        
        {children.length === 0 && (
          <ShadowButton
            variant="link"
            onClick={() => onComplete([])}
          >
            Skip for now
          </ShadowButton>
        )}
      </div>
    </div>
  );
};
```

#### 3. Coach Verification Flow
```typescript
// /src/features/identity/components/onboarding/CoachVerification.tsx
export const CoachVerification: React.FC<{
  onComplete: (verification: CoachVerification) => void;
}> = ({ onComplete }) => {
  const [verification, setVerification] = useState<CoachVerification>({
    certificationType: '',
    certificationNumber: '',
    issuingOrganization: '',
    expiryDate: null,
    backgroundCheckConsent: false
  });
  
  const certificationTypes = [
    { value: 'ussf', label: 'USSF Coaching License' },
    { value: 'nscaa', label: 'NSCAA Certification' },
    { value: 'ayso', label: 'AYSO Coach Certification' },
    { value: 'other', label: 'Other Certification' }
  ];
  
  const handleSubmit = async () => {
    // Validate certification
    const isValid = await CoachService.verifyCertification(verification);
    
    if (!isValid) {
      showError('Unable to verify certification. Please check your details.');
      return;
    }
    
    // Record verification
    await OnboardingService.recordCoachVerification(verification);
    
    onComplete(verification);
  };
  
  return (
    <div className="coach-verification">
      <h2>Coach Verification</h2>
      <p>To ensure child safety, we verify all coach certifications.</p>
      
      <ShadowSelect
        label="Certification Type"
        value={verification.certificationType}
        onChange={(value) => setVerification({
          ...verification,
          certificationType: value
        })}
        options={certificationTypes}
      />
      
      <ShadowTextInput
        label="Certification Number"
        value={verification.certificationNumber}
        onChange={(e) => setVerification({
          ...verification,
          certificationNumber: e.target.value
        })}
      />
      
      <ShadowTextInput
        label="Issuing Organization"
        value={verification.issuingOrganization}
        onChange={(e) => setVerification({
          ...verification,
          issuingOrganization: e.target.value
        })}
      />
      
      <ShadowDatePicker
        label="Expiry Date"
        value={verification.expiryDate}
        onChange={(date) => setVerification({
          ...verification,
          expiryDate: date
        })}
        min={new Date()} // Must be future date
      />
      
      <ShadowCheckbox
        label="I consent to a background check"
        checked={verification.backgroundCheckConsent}
        onChange={(checked) => setVerification({
          ...verification,
          backgroundCheckConsent: checked
        })}
      />
      
      <ShadowButton
        onClick={handleSubmit}
        disabled={!isVerificationComplete(verification)}
      >
        Verify & Continue
      </ShadowButton>
    </div>
  );
};
```

## Testing & Quality Assurance

### Integration Test Scenarios

#### 1. Account Switching Flow
```typescript
describe('Account Switching Integration', () => {
  test('Parent can switch to child account and back', async () => {
    // Login as parent
    await login('<EMAIL>', 'password');
    
    // Switch to child
    const childAccount = await getChildAccounts()[0];
    await switchAccount(childAccount.id);
    
    // Verify context
    expect(getCurrentProfile().id).toBe(childAccount.id);
    expect(getAvailableFeatures()).not.toContain('family_management');
    
    // Switch back
    await switchToParent();
    expect(getCurrentProfile().role).toBe('parent');
  });
  
  test('Context preserved during switch', async () => {
    // Navigate to specific page
    navigate('/team/123/events');
    
    // Fill partial form
    fillEventForm({ title: 'Practice' });
    
    // Switch account
    await switchAccount(childId);
    
    // Switch back
    await switchAccount(parentId);
    
    // Verify we're back on same page with form data
    expect(location.pathname).toBe('/team/123/events');
    expect(getFormValue('title')).toBe('Practice');
  });
});
```

#### 2. Profile Validation Integration
```typescript
describe('Profile Validation Integration', () => {
  test('Player cannot RSVP without emergency contact', async () => {
    // Create player without emergency contact
    const player = await createProfile({
      role: 'player',
      age: 15,
      emergency_contact_name: null
    });
    
    // Try to RSVP to event
    const result = await rsvpToEvent(eventId);
    
    expect(result.error).toBe('Profile incomplete: Emergency contact required');
    expect(result.redirect).toBe('/identity/profile?missing=emergency_contact');
  });
});
```

#### 3. Onboarding to Main App Flow
```typescript
describe('Onboarding Integration', () => {
  test('Player completes onboarding and lands on team dashboard', async () => {
    // Start with invite
    navigate('/onboard/player/invite-token-123');
    
    // Complete all steps
    await completeWelcomeStep();
    await createAccount('<EMAIL>', 'password');
    await setupProfile({ name: 'John Doe', position: 'Forward' });
    await selectAvatar('soccer-1');
    
    // Verify auto-join and redirect
    await waitFor(() => {
      expect(location.pathname).toBe('/team/456/dashboard');
      expect(getTeamMembers()).toContainEqual(
        expect.objectContaining({ name: 'John Doe' })
      );
    });
  });
});
```

## Performance Optimization

### 1. Account Switch Performance
```typescript
// Preload linked accounts
const preloadLinkedAccounts = async () => {
  const { data: relationships } = await supabase
    .from('family_relationships')
    .select('child_profile_id')
    .eq('parent_profile_id', currentUserId);
  
  // Prefetch profile data
  const profileIds = relationships.map(r => r.child_profile_id);
  await ProfileService.prefetchProfiles(profileIds);
};
```

### 2. Avatar Loading Optimization
```typescript
// Progressive avatar loading
const AvatarLoader = ({ avatarId, customization }) => {
  return (
    <picture>
      {/* Low quality placeholder */}
      <source 
        srcSet={`/avatars/low/${avatarId}.webp`} 
        type="image/webp"
      />
      
      {/* High quality */}
      <img 
        src={`/avatars/high/${avatarId}.png`}
        loading="lazy"
        decoding="async"
      />
    </picture>
  );
};
```

## Security Hardening

### 1. Child Account Protection
```typescript
// Middleware to prevent child accounts from accessing parent features
export const childAccountRestrictions = (req, res, next) => {
  const profile = req.user.profile;
  
  if (profile.is_child_account) {
    const restrictedPaths = [
      '/identity/family',
      '/billing',
      '/settings/security',
      '/admin'
    ];
    
    if (restrictedPaths.some(path => req.path.startsWith(path))) {
      return res.status(403).json({
        error: 'This feature is not available for child accounts'
      });
    }
  }
  
  next();
};
```

### 2. Session Security
```typescript
// Enhanced session validation
const validateSession = async (sessionToken: string) => {
  const session = await SessionService.getSession(sessionToken);
  
  // Check session hasn't been hijacked
  const currentFingerprint = generateDeviceFingerprint();
  if (session.deviceFingerprint !== currentFingerprint) {
    await SecurityLogger.log('session_hijack_attempt', {
      sessionId: session.id,
      originalDevice: session.deviceFingerprint,
      currentDevice: currentFingerprint
    });
    
    throw new SecurityError('Session invalid - please login again');
  }
  
  return session;
};
```

## Week 3 Deliverables

### DEV1
- ✅ All auth integration complete
- ✅ Session management across switches
- ✅ Rate limiting implemented
- ✅ Security hardening complete
- ✅ All auth guards updated

### DEV2
- ✅ Seamless account switching
- ✅ Context preservation working
- ✅ Family activity tracking
- ✅ Parent approval system
- ✅ Emergency contact access

### DEV3
- ✅ Profile validation complete
- ✅ Role-specific validation
- ✅ Avatar customization working
- ✅ Profile sharing for coaches
- ✅ Export/import functionality

### DEV4
- ✅ All onboarding flows complete
- ✅ Team auto-join working
- ✅ Multi-child setup functional
- ✅ Coach verification integrated
- ✅ Smooth handoff to main app

---

## Document Metadata

**Last Updated**: 2025-08-17
**Status**: Ready for Implementation
**Week**: 3 of 4
**Technical Lead**: TBD
**Document Version**: 1.0