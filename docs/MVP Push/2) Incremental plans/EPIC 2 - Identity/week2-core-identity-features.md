# Week 2: Core Identity Features Implementation Plan

## Developer Assignments

### DEV1 - Enhanced Authentication
**Focus**: Complete authentication features including password reset, email verification, and security
- Implement password reset flow with email integration
- Add email verification system
- Create permission guards and protected routes
- Build authentication error handling
- Enhance session persistence and security

### DEV2 - Family Management System
**Focus**: Build the complete family dashboard and child account management
- Create Identity Dashboard with family overview
- Implement child account creation workflow
- Build account switching functionality
- Create family relationship management UI
- Develop parent approval system

### DEV3 - Profile System
**Focus**: Complete profile management with SportHead avatars
- Build ProfilePage with role-based fields
- Implement SportHead avatar selection system
- Create profile editor components
- Add profile completion tracking UI
- Develop child-specific profile restrictions

### DEV4 - Onboarding UI Foundation
**Focus**: Create onboarding page shells and initial flows
- Build onboarding page templates
- Create welcome screens for each user type
- Implement onboarding progress tracking
- Design step-by-step wizards
- Set up invitation acceptance flow

---

## Overview

Week 2 focuses on building the core user-facing features of the Identity System. With the foundation from Week 1 in place, each developer can now work on their respective UI components and user flows. By the end of this week, users will be able to create accounts, manage family relationships, edit profiles, and see the onboarding structure.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Page Flow and Purpose](#page-flow-and-purpose)
3. [Component Structure](#component-structure)
4. [Database Design](#database-design)
5. [Service Architecture](#service-architecture)
6. [Implementation Timeline](#implementation-timeline)
7. [Integration Points](#integration-points)
8. [Success Metrics](#success-metrics)

## Architecture Overview

### Directory Structure (Week 2 Additions)

```
src/features/identity/
├── components/
│   ├── shared/
│   │   ├── IdentityCard.tsx [DEV2] *NEW*
│   │   ├── RoleIndicator.tsx [DEV3] *NEW*
│   │   ├── AvatarDisplay.tsx [DEV3] *NEW*
│   │   └── ProgressIndicator.tsx [DEV4] *NEW*
│   ├── auth/
│   │   ├── PasswordResetForm.tsx [DEV1] *NEW*
│   │   ├── EmailVerification.tsx [DEV1] *NEW*
│   │   └── SecuritySettings.tsx [DEV1] *NEW*
│   ├── family/
│   │   ├── FamilyDashboard.tsx [DEV2] *NEW*
│   │   ├── ChildAccountCreator.tsx [DEV2] *NEW*
│   │   ├── ChildList.tsx [DEV2] *NEW*
│   │   ├── AccountSwitcher.tsx [DEV2] *NEW*
│   │   └── ParentApprovalFlow.tsx [DEV2] *NEW*
│   ├── profile/
│   │   ├── ProfileEditor.tsx [DEV3] *NEW*
│   │   ├── SportHeadAvatarPicker.tsx [DEV3] *NEW*
│   │   ├── ProfileCompletionTracker.tsx [DEV3] *NEW*
│   │   ├── RoleSpecificFields.tsx [DEV3] *NEW*
│   │   └── ChildProfileFields.tsx [DEV3] *NEW*
│   └── onboarding/
│       ├── WelcomeScreen.tsx [DEV4] *NEW*
│       ├── OnboardingWizard.tsx [DEV4] *NEW*
│       ├── StepIndicator.tsx [DEV4] *NEW*
│       └── InviteAcceptance.tsx [DEV4] *NEW*
├── pages/
│   ├── EmailVerificationPage.tsx [DEV1] *NEW*
│   ├── IdentityDashboard.tsx [DEV2] *NEW*
│   ├── FamilyManagementPage.tsx [DEV2] *NEW*
│   ├── ProfilePage.tsx [DEV3] *NEW*
│   ├── PlayerOnboardingPage.tsx [DEV4] *NEW*
│   ├── ParentOnboardingPage.tsx [DEV4] *NEW*
│   └── CoachOnboardingPage.tsx [DEV4] *NEW*
└── utils/
    ├── avatarHelpers.ts [DEV3] *NEW*
    └── familyHelpers.ts [DEV2] *NEW*
```

## Page Flow and Purpose

### Complete Week 2 Flow Diagram

```mermaid
graph TB
    Start([User Login]) --> AuthCheck{Authenticated?}
    
    %% Authentication Enhancement - DEV1
    AuthCheck --> |No| LoginPage[LoginPage]
    LoginPage --> |Need Verification| EmailVerify[EmailVerificationPage - DEV1]
    LoginPage --> |Reset Password| PasswordFlow[Password Reset Flow - DEV1]
    
    %% Main Identity Flow - DEV2
    AuthCheck --> |Yes| IdentityDashboard[IdentityDashboard - DEV2]
    IdentityDashboard --> |Manage Family| FamilyPage[FamilyManagementPage - DEV2]
    IdentityDashboard --> |Switch Account| AccountSwitch[Account Switcher - DEV2]
    
    %% Family Management - DEV2
    FamilyPage --> |Add Child| ChildCreator[Child Account Creator - DEV2]
    ChildCreator --> |Create| ChildProfile[Child Profile Setup - DEV3]
    
    %% Profile Management - DEV3
    IdentityDashboard --> |Edit Profile| ProfilePage[ProfilePage - DEV3]
    ProfilePage --> |Select Avatar| AvatarPicker[SportHead Picker - DEV3]
    ProfilePage --> |Update Info| ProfileEditor[Profile Editor - DEV3]
    
    %% Onboarding Setup - DEV4
    LoginPage --> |New User| OnboardingRouter{Invite Type?}
    OnboardingRouter --> |Player| PlayerOnboarding[PlayerOnboardingPage - DEV4]
    OnboardingRouter --> |Parent| ParentOnboarding[ParentOnboardingPage - DEV4]
    OnboardingRouter --> |Coach| CoachOnboarding[CoachOnboardingPage - DEV4]
```

### Page Details

#### 1. Identity Dashboard [DEV2]

**Purpose**: Central hub for identity management showing current user and family accounts

**Path**: `/src/features/identity/pages/IdentityDashboard.tsx`
**Route**: `/identity`

```typescript
/**
 * IdentityDashboard
 * 
 * PURPOSE:
 * - Display current active identity prominently
 * - Show all linked family accounts
 * - Quick switch between accounts
 * - Access to profile and family management
 * - Display important notifications
 * 
 * USER GOALS:
 * - See who I'm logged in as
 * - Switch to my child's account easily
 * - Access all identity features
 * - View family at a glance
 */
```

**Key Components:**
```typescript
// Current identity display
import { CurrentIdentityCard } from '@/src/features/identity/components/shared/CurrentIdentityCard';

// Family members grid
import { FamilyMembersGrid } from '@/src/features/identity/components/family/FamilyMembersGrid';

// Quick actions menu
import { IdentityQuickActions } from '@/src/features/identity/components/shared/IdentityQuickActions';

// Account switcher
import { AccountSwitcher } from '@/src/features/identity/components/family/AccountSwitcher';
```

#### 2. Family Management Page [DEV2]

**Purpose**: Comprehensive family account creation and management

**Path**: `/src/features/identity/pages/FamilyManagementPage.tsx`
**Route**: `/identity/family`

```typescript
/**
 * FamilyManagementPage
 * 
 * PURPOSE:
 * - Create child accounts without email
 * - Edit existing child profiles
 * - Manage permissions and access
 * - Set emergency contacts
 * - View family activity
 * 
 * USER GOALS:
 * - Add all my children easily
 * - Keep information current
 * - Control child access
 * - Ensure safety info is complete
 */
```

**Key Features:**
- Child account creation wizard
- Username generation and validation
- Password creation for children
- Emergency contact management
- Medical information tracking

#### 3. Profile Page [DEV3]

**Purpose**: Complete profile editing with SportHead avatars

**Path**: `/src/features/identity/pages/ProfilePage.tsx`
**Route**: `/identity/profile/:profileId?`

```typescript
/**
 * ProfilePage
 * 
 * PURPOSE:
 * - Edit profile based on user role
 * - Select and customize SportHead avatar
 * - Track profile completion
 * - Update contact information
 * - Manage sport preferences
 * 
 * USER GOALS:
 * - Keep my info up to date
 * - Choose a cool avatar
 * - Complete my profile
 * - Set my preferences
 */
```

**Role-Based Sections:**
- **Players**: Jersey number, position, favorite team
- **Parents**: Contact info, emergency contacts, notifications
- **Coaches**: Certifications, team assignments, availability

#### 4. Onboarding Pages [DEV4]

**Purpose**: Structured first-time user experience

**Player Onboarding** - `/onboard/player/:token`
```typescript
/**
 * PlayerOnboardingPage
 * 
 * PURPOSE:
 * - Welcome new players
 * - Create account from invitation
 * - Set up basic profile
 * - Choose avatar
 * - Join team automatically
 */
```

**Parent Onboarding** - `/onboard/parent/:token`
```typescript
/**
 * ParentOnboardingPage
 * 
 * PURPOSE:
 * - Welcome parents
 * - Create parent account
 * - Add children immediately
 * - Set up family structure
 * - Configure permissions
 */
```

**Coach Onboarding** - `/onboard/coach/:token`
```typescript
/**
 * CoachOnboardingPage
 * 
 * PURPOSE:
 * - Verify coach identity
 * - Create coach account
 * - Set up profile
 * - Configure team access
 * - Set notification preferences
 */
```

## Component Structure

### Family Components [DEV2]

#### ChildAccountCreator Component
```typescript
// /src/features/identity/components/family/ChildAccountCreator.tsx

interface ChildAccountCreatorProps {
  parentId: string;
  onComplete: (child: Profile) => void;
}

/**
 * Multi-step form for creating child accounts:
 * 1. Basic Info (name, DOB)
 * 2. Username Selection (with availability check)
 * 3. Password Creation
 * 4. Initial Avatar Selection
 * 5. Medical/Emergency Info
 */
```

#### AccountSwitcher Component
```typescript
// /src/features/identity/components/family/AccountSwitcher.tsx

interface AccountSwitcherProps {
  currentProfile: Profile;
  availableProfiles: Profile[];
  onSwitch: (profileId: string) => void;
}

/**
 * Visual account switcher with:
 * - Current account highlight
 * - Avatar display for each account
 * - Quick switch functionality
 * - Activity indicators
 */
```

### Profile Components [DEV3]

#### SportHeadAvatarPicker Component
```typescript
// /src/features/identity/components/profile/SportHeadAvatarPicker.tsx

interface SportHeadAvatarPickerProps {
  currentAvatar?: AvatarConfig;
  sport?: string;
  onSelect: (avatar: AvatarConfig) => void;
}

/**
 * Avatar selection interface:
 * - Grid of available SportHead avatars
 * - Filter by sport/category
 * - Preview selected avatar
 * - Basic customization options
 */
```

#### ProfileCompletionTracker Component
```typescript
// /src/features/identity/components/profile/ProfileCompletionTracker.tsx

interface ProfileCompletionTrackerProps {
  profile: Profile;
  role: UserRole;
}

/**
 * Visual progress indicator showing:
 * - Overall completion percentage
 * - Section-by-section breakdown
 * - Missing required fields
 * - Next steps guidance
 */
```

### Onboarding Components [DEV4]

#### OnboardingWizard Component
```typescript
// /src/features/identity/components/onboarding/OnboardingWizard.tsx

interface OnboardingWizardProps {
  steps: OnboardingStep[];
  currentStep: number;
  onNext: () => void;
  onPrevious: () => void;
  onComplete: () => void;
}

/**
 * Reusable wizard component:
 * - Step indicator
 * - Progress tracking
 * - Navigation controls
 * - Validation per step
 */
```

## Service Architecture (Week 2 Enhancements)

### AuthenticationService [DEV1]

```typescript
// Week 2 additions to AuthenticationService

export class AuthenticationService {
  // Email verification
  async verifyEmail(token: string): Promise<void> {
    const { error } = await supabase.auth.verifyOtp({
      token_hash: token,
      type: 'email'
    });
    
    if (error) throw error;
  }
  
  async resendVerificationEmail(email: string): Promise<void> {
    const { error } = await supabase.auth.resend({
      type: 'signup',
      email
    });
    
    if (error) throw error;
  }
  
  // Enhanced password reset
  async confirmPasswordReset(token: string, newPassword: string): Promise<void> {
    const { error } = await supabase.auth.updateUser({
      password: newPassword
    });
    
    if (error) throw error;
  }
  
  // Session management
  async refreshSession(): Promise<Session> {
    const { data, error } = await supabase.auth.refreshSession();
    if (error) throw error;
    return data.session;
  }
}
```

### FamilyService [DEV2]

```typescript
// Week 2 full implementation

export class FamilyServiceImpl implements FamilyService {
  async createChildAccount(parentId: string, data: CreateChildData): Promise<Profile> {
    // Generate unique username if not provided
    const username = data.username || await this.generateUniqueUsername(data.fullName);
    const email = this.generateChildEmail(username);
    
    // Create auth user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password: data.password,
      options: {
        data: {
          full_name: data.fullName,
          parent_id: parentId,
          is_child: true
        }
      }
    });
    
    if (authError) throw authError;
    
    // Create child account record
    const { data: childAccount, error: childError } = await supabase
      .from('child_accounts')
      .insert({
        profile_id: authData.user.id,
        generated_username: username,
        generated_email: email
      })
      .select()
      .single();
    
    if (childError) throw childError;
    
    // Create family relationship
    await this.createFamilyRelationship(parentId, authData.user.id);
    
    // Return profile
    return this.getProfile(authData.user.id);
  }
  
  async getFamilyMembers(parentId: string): Promise<FamilyMember[]> {
    const { data, error } = await supabase
      .from('family_relationships')
      .select(`
        *,
        child_profile:profiles!child_profile_id(
          id,
          full_name,
          avatar_url,
          date_of_birth
        )
      `)
      .eq('parent_profile_id', parentId);
    
    if (error) throw error;
    
    return data.map(rel => ({
      ...rel.child_profile,
      relationship: rel.relationship_type,
      canSwitch: true
    }));
  }
  
  async switchAccount(fromProfileId: string, toProfileId: string): Promise<void> {
    // Validate permission
    const canSwitch = await this.canSwitchToAccount(fromProfileId, toProfileId);
    if (!canSwitch) throw new Error('Permission denied');
    
    // Update session
    await supabase.auth.updateUser({
      data: { active_profile_id: toProfileId }
    });
    
    // Log switch
    await supabase.from('account_switch_logs').insert({
      from_profile_id: fromProfileId,
      to_profile_id: toProfileId,
      switched_at: new Date()
    });
  }
}
```

### ProfileService [DEV3]

```typescript
// Week 2 full implementation

export class ProfileServiceImpl implements ProfileService {
  async updateProfile(profileId: string, updates: ProfileUpdate): Promise<Profile> {
    // Update main profile
    const { data: profile, error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', profileId)
      .select()
      .single();
    
    if (error) throw error;
    
    // Update completion tracking
    await this.updateCompletionTracking(profileId, profile);
    
    return profile;
  }
  
  async selectAvatar(profileId: string, avatarId: string): Promise<void> {
    // Update avatar config
    const { error: configError } = await supabase
      .from('avatar_configs')
      .upsert({
        profile_id: profileId,
        sport_head_id: avatarId,
        updated_at: new Date()
      });
    
    if (configError) throw configError;
    
    // Update profile avatar URL
    const avatarUrl = `/avatars/sportheads/${avatarId}.png`;
    await supabase
      .from('profiles')
      .update({ avatar_url: avatarUrl })
      .eq('id', profileId);
    
    // Mark avatar as selected in completion
    await supabase
      .from('profile_completion')
      .update({ avatar_selected: true })
      .eq('profile_id', profileId);
  }
  
  async getAvailableAvatars(sport?: string): Promise<Avatar[]> {
    // Week 2: Return static list, Week 3: Dynamic from database
    const avatars = [
      { id: 'soccer-1', name: 'Soccer Star', sport: 'soccer', url: '/avatars/soccer-1.png' },
      { id: 'basketball-1', name: 'Hoops Hero', sport: 'basketball', url: '/avatars/basketball-1.png' },
      { id: 'baseball-1', name: 'Diamond Ace', sport: 'baseball', url: '/avatars/baseball-1.png' },
      // ... more avatars
    ];
    
    return sport ? avatars.filter(a => a.sport === sport) : avatars;
  }
  
  private async updateCompletionTracking(profileId: string, profile: Profile): Promise<void> {
    const requiredFields = this.getRequiredFields(profile.role);
    const completedFields = requiredFields.filter(field => profile[field] != null);
    const percentage = Math.round((completedFields.length / requiredFields.length) * 100);
    
    await supabase
      .from('profile_completion')
      .update({
        basic_info_complete: this.isBasicInfoComplete(profile),
        contact_info_complete: this.isContactInfoComplete(profile),
        completion_percentage: percentage,
        last_updated: new Date()
      })
      .eq('profile_id', profileId);
  }
}
```

### OnboardingService [DEV4]

```typescript
// Week 2 UI support methods

export class OnboardingServiceImpl implements OnboardingService {
  async getInviteDetails(token: string): Promise<InviteDetails> {
    const { data, error } = await supabase
      .from('onboarding_invitations')
      .select(`
        *,
        team:teams(id, name, sport)
      `)
      .eq('token', token)
      .single();
    
    if (error) throw error;
    
    return {
      type: data.invite_type,
      teamName: data.team?.name,
      teamSport: data.team?.sport,
      playerName: data.player_name,
      coachName: data.coach_name,
      expiresAt: data.expires_at
    };
  }
  
  async saveOnboardingProgress(userId: string, step: string, stepData: any): Promise<void> {
    const { data: existing } = await supabase
      .from('onboarding_progress')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (existing) {
      // Update existing progress
      const completedSteps = [...existing.completed_steps, step];
      const onboardingData = { ...existing.onboarding_data, [step]: stepData };
      
      await supabase
        .from('onboarding_progress')
        .update({
          current_step: step,
          completed_steps: completedSteps,
          onboarding_data: onboardingData
        })
        .eq('user_id', userId);
    } else {
      // Create new progress
      await supabase
        .from('onboarding_progress')
        .insert({
          user_id: userId,
          current_step: step,
          completed_steps: [step],
          onboarding_data: { [step]: stepData }
        });
    }
  }
  
  getOnboardingSteps(type: 'player' | 'parent' | 'coach'): OnboardingStep[] {
    const baseSteps = [
      { id: 'welcome', title: 'Welcome', component: 'WelcomeScreen' },
      { id: 'account', title: 'Create Account', component: 'AccountSetup' },
      { id: 'profile', title: 'Set Up Profile', component: 'ProfileSetup' }
    ];
    
    switch (type) {
      case 'player':
        return [
          ...baseSteps,
          { id: 'avatar', title: 'Choose Avatar', component: 'AvatarSelection' },
          { id: 'team', title: 'Join Team', component: 'TeamJoin' }
        ];
      
      case 'parent':
        return [
          ...baseSteps,
          { id: 'children', title: 'Add Children', component: 'ChildrenSetup' },
          { id: 'emergency', title: 'Emergency Info', component: 'EmergencyContacts' }
        ];
      
      case 'coach':
        return [
          ...baseSteps,
          { id: 'verification', title: 'Verify Identity', component: 'CoachVerification' },
          { id: 'teams', title: 'Team Access', component: 'TeamAccess' }
        ];
    }
  }
}
```

## Implementation Timeline

### Monday - Tuesday (Days 6-7)

**DEV1**: 
- Implement password reset flow UI
- Create email verification page
- Add permission guards to routes
- Build error handling components

**DEV2**:
- Build Identity Dashboard page
- Create family member display grid
- Implement account switcher UI
- Start child account creator

**DEV3**:
- Create ProfilePage structure
- Build SportHead avatar picker
- Implement profile editor form
- Add role-specific fields

**DEV4**:
- Create onboarding page shells
- Build welcome screens
- Implement wizard component
- Design step indicators

### Wednesday - Thursday (Days 8-9)

**DEV1**:
- Complete email integration
- Add security settings
- Enhance session management
- Test authentication flows

**DEV2**:
- Complete child account creation
- Build family management page
- Add username validation UI
- Test account switching

**DEV3**:
- Complete avatar selection
- Add profile completion tracking
- Build child profile restrictions
- Test profile updates

**DEV4**:
- Build player onboarding flow
- Create parent onboarding flow
- Start coach onboarding
- Test invitation acceptance

### Friday (Day 10)

**All Developers**:
- Component integration
- Cross-feature testing
- UI/UX polish
- Bug fixes
- Documentation

## Integration Points

### DEV1 ↔ DEV2
- Protected routes for family pages
- Session updates for account switching
- Authentication state in family context

### DEV2 ↔ DEV3
- Child profile creation flow
- Family member profile editing
- Avatar display in family grid

### DEV2 ↔ DEV4
- Parent onboarding creates children
- Family setup during onboarding
- Account linking after onboarding

### DEV3 ↔ DEV4
- Profile creation in onboarding
- Avatar selection during setup
- Completion requirements

## Success Metrics

### Performance
- Page load times < 2 seconds [All]
- Account switch < 1 second [DEV2]
- Avatar load < 500ms [DEV3]
- Form submission < 2 seconds [All]

### User Experience
- All forms have validation [All]
- Loading states everywhere [All]
- Clear error messages [All]
- Mobile responsive [All]

### Feature Completion
- Identity Dashboard functional [DEV2]
- Child accounts can be created [DEV2]
- Profiles can be edited [DEV3]
- Onboarding flows visible [DEV4]

### Code Quality
- Components have props interfaces [All]
- Services handle errors gracefully [All]
- State management consistent [All]
- No console errors [All]

## Security Considerations

1. **Child Account Protection**: Usernames can't contain personal info
2. **Parent Verification**: Email confirmation required
3. **Session Security**: Proper token refresh
4. **Input Validation**: All forms sanitized
5. **Permission Checks**: Every action verified

## Testing Requirements

### Component Tests
- Family dashboard rendering [DEV2]
- Avatar picker interaction [DEV3]
- Onboarding wizard navigation [DEV4]
- Form validation [All]

### Integration Tests
- Child account creation flow [DEV2]
- Profile update and save [DEV3]
- Onboarding completion [DEV4]
- Account switching [DEV2]

### User Acceptance Tests
- Parent can create child account
- Child can select avatar
- Profile shows completion
- Onboarding is intuitive

## Week 2 Deliverables

### DEV1
- Password reset fully functional
- Email verification working
- Protected routes implemented
- Security enhancements complete

### DEV2
- Identity Dashboard deployed
- Family management functional
- Child accounts can be created
- Account switching works

### DEV3
- Profile editing complete
- Avatar selection working
- Completion tracking visible
- Role-based fields implemented

### DEV4
- All onboarding pages created
- Welcome flows working
- Progress tracking functional
- Invitation acceptance ready

---

## Document Metadata

**Last Updated**: 2025-08-17
**Status**: Ready for Implementation
**Week**: 2 of 4
**Technical Lead**: TBD
**Document Version**: 1.0