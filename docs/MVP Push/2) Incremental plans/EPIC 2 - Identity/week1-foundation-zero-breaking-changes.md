# Week 1: Foundation & Authentication (Zero Breaking Changes)

## Developer Assignments

### DEV1 - Identity System Foundation
**Focus**: Create independent identity system that reads from existing data
- Set up `/features/identity` directory structure
- Create read-only services using existing tables
- Build new LoginPage that uses existing auth
- Implement feature flags for gradual rollout
- Ensure zero impact on existing system

### DEV2 - Family Views (Read-Only)
**Focus**: Create new family views using existing data
- Build IdentityContext that reads from existing tables
- Create family dashboard using existing relationships
- Implement read-only account switching views
- Design new UI without modifying data
- Plan enhancement tables for Week 2

### DEV3 - Profile Views (Read-Only)
**Focus**: Create enhanced profile views using existing data
- Build profile service that reads existing `profiles` table
- Calculate completion percentage without storing it
- Create new profile UI components
- Design avatar enhancement system
- Plan metadata tables for Week 2

### DEV4 - Onboarding Planning
**Focus**: Design onboarding that works with existing registration
- Map existing registration/onboarding flows
- Design new onboarding UI components
- Plan invitation system using existing data
- Create mockups for role-specific flows
- Identify what new data is needed (Week 2)

---

## Overview

Week 1 establishes the Identity System as a **completely independent feature** that works alongside the existing system. We focus on:
- **Reading** existing data only (no writes)
- **Creating** new UI in `/features/identity`
- **Testing** compatibility with old system
- **Zero** breaking changes

## Database Strategy (Week 1)

### Tables We'll READ From (No Changes)
```sql
-- Existing tables - READ ONLY this week
profiles (
  id, full_name, avatar_url, date_of_birth, 
  phone, role, created_at, updated_at
)

auth.users (
  id, email, created_at
)

-- Any existing relationship tables
user_relationships (if exists)
```

### New Tables We'll CREATE (Additions Only)
```sql
-- Metadata for identity features only
CREATE TABLE identity_metadata (
  profile_id uuid PRIMARY KEY REFERENCES profiles(id),
  completion_percentage int DEFAULT 0,
  last_completion_check timestamp,
  onboarding_completed boolean DEFAULT false,
  feature_flags jsonb DEFAULT '{}',
  created_at timestamp DEFAULT now()
);

-- Session tracking for identity system
CREATE TABLE identity_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id),
  active_profile_id uuid REFERENCES profiles(id),
  session_data jsonb,
  created_at timestamp DEFAULT now()
);
```

## Implementation by Developer

### DEV1 - Identity System Foundation

#### 1. Directory Structure
```
src/features/identity/
├── components/
│   ├── auth/
│   │   └── IdentityLoginForm.tsx
│   └── shared/
│       └── FeatureFlag.tsx
├── pages/
│   └── LoginPage.tsx
├── services/
│   └── IdentityAuthService.ts
├── hooks/
│   └── useFeatureFlag.ts
├── routes/
│   └── IdentityRoutes.tsx
└── types/
    └── identity.types.ts
```

#### 2. New Login Page (Uses Existing Auth)
```typescript
// /src/features/identity/pages/LoginPage.tsx
import { supabase } from '@/src/lib/supabase';
import { useNavigate } from 'react-router-dom';

export const LoginPage = () => {
  const navigate = useNavigate();
  
  const handleLogin = async (email: string, password: string) => {
    // Use EXISTING Supabase auth - no changes
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    if (error) {
      console.error('Login error:', error);
      return;
    }
    
    // Optional: Track login in identity system (non-breaking)
    try {
      await supabase
        .from('identity_sessions')
        .insert({
          user_id: data.user.id,
          active_profile_id: data.user.id,
          session_data: { loginMethod: 'email' }
        });
    } catch (e) {
      // Non-critical, don't block login
      console.log('Identity tracking failed:', e);
    }
    
    navigate('/identity/dashboard');
  };
  
  return (
    <div className="identity-login">
      <h1>Welcome to SHOT Identity</h1>
      <p>This is the new login experience (using same auth)</p>
      {/* New UI, same auth */}
      <IdentityLoginForm onSubmit={handleLogin} />
      
      <p>
        <a href="/UpdatedLoginV2">Use classic login</a>
      </p>
    </div>
  );
};
```

#### 3. Feature Flag System
```typescript
// /src/features/identity/hooks/useFeatureFlag.ts
export const useFeatureFlag = (flag: string): boolean => {
  // Week 1: All flags default to false
  const defaultFlags = {
    SHOW_IDENTITY_SYSTEM: false,
    USE_IDENTITY_LOGIN: false,
    SHOW_IDENTITY_DASHBOARD: false,
    ENABLE_FAMILY_DASHBOARD: false,
  };
  
  // Check localStorage for testing
  const localOverride = localStorage.getItem(`ff_${flag}`);
  if (localOverride !== null) {
    return localOverride === 'true';
  }
  
  // Check user's feature flags from identity_metadata
  const { data } = await supabase
    .from('identity_metadata')
    .select('feature_flags')
    .eq('profile_id', currentUser.id)
    .single();
  
  if (data?.feature_flags?.[flag] !== undefined) {
    return data.feature_flags[flag];
  }
  
  return defaultFlags[flag] || false;
};
```

#### 4. Identity Routes (Parallel to Existing)
```typescript
// /src/features/identity/routes/IdentityRoutes.tsx
export const IdentityRoutes = () => {
  const showIdentity = useFeatureFlag('SHOW_IDENTITY_SYSTEM');
  
  if (!showIdentity) {
    return null; // Feature not enabled
  }
  
  return (
    <Routes>
      <Route path="/identity">
        <Route index element={<Navigate to="dashboard" />} />
        <Route path="login" element={<LoginPage />} />
        <Route path="dashboard" element={<IdentityDashboard />} />
        <Route path="profile" element={<ProfilePage />} />
        <Route path="family" element={<FamilyPage />} />
      </Route>
    </Routes>
  );
};
```

### DEV2 - Family Views (Read-Only)

#### 1. Identity Context (Reads Existing Data)
```typescript
// /src/features/identity/contexts/IdentityContext.tsx
import { useAccountSwitching } from '@/src/hooks/useAccountSwitching';
import { useUserChildren } from '@/src/hooks/useUserChildren';

export const IdentityProvider = ({ children }) => {
  // Use EXISTING hooks - no changes needed
  const accountSwitching = useAccountSwitching();
  const { children: userChildren } = useUserChildren();
  
  // Read from EXISTING profiles table
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', currentUser.id)
    .single();
  
  const contextValue = {
    // All existing data
    currentProfile: profile,
    availableAccounts: accountSwitching.availableAccounts,
    children: userChildren,
    
    // New computed values (not stored)
    familySize: userChildren.length + 1,
    hasChildren: userChildren.length > 0,
    
    // Read-only this week
    switchAccount: () => {
      console.log('Account switching coming in Week 2');
    }
  };
  
  return (
    <IdentityContext.Provider value={contextValue}>
      {children}
    </IdentityContext.Provider>
  );
};
```

#### 2. Family Dashboard (Read-Only View)
```typescript
// /src/features/identity/pages/FamilyDashboard.tsx
export const FamilyDashboard = () => {
  const { children, currentProfile } = useIdentity();
  
  return (
    <div className="identity-family-dashboard">
      <h1>Family Dashboard</h1>
      
      <div className="current-user">
        <h2>Logged in as: {currentProfile.full_name}</h2>
        <img src={currentProfile.avatar_url} alt="Avatar" />
      </div>
      
      <div className="family-members">
        <h2>Family Members ({children.length})</h2>
        {children.map(child => (
          <div key={child.id} className="family-member-card">
            <img src={child.avatar_url} alt={child.full_name} />
            <h3>{child.full_name}</h3>
            <p>Age: {calculateAge(child.date_of_birth)}</p>
            <button disabled>
              Switch Account (Coming Soon)
            </button>
          </div>
        ))}
      </div>
      
      <div className="actions">
        <button disabled>Add Child (Coming Week 2)</button>
        <a href="/ManageChildren">Use Classic Family Management</a>
      </div>
    </div>
  );
};
```

### DEV3 - Profile Views (Read-Only)

#### 1. Profile Service (Reads Existing Data)
```typescript
// /src/features/identity/services/IdentityProfileService.ts
export class IdentityProfileService {
  // Read from EXISTING profiles table
  static async getProfile(id: string) {
    const { data: profile } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single();
    
    // Calculate completion without storing
    const completion = this.calculateCompletion(profile);
    
    // Check for identity metadata (may not exist)
    const { data: metadata } = await supabase
      .from('identity_metadata')
      .select('*')
      .eq('profile_id', id)
      .single();
    
    return {
      ...profile,
      // Enhanced data (computed, not stored)
      completionPercentage: completion.percentage,
      missingFields: completion.missingFields,
      hasIdentityFeatures: !!metadata
    };
  }
  
  // Pure function - no database changes
  static calculateCompletion(profile: any) {
    const requiredFields = [
      'full_name',
      'date_of_birth', 
      'avatar_url',
      'phone'
    ];
    
    const completed = requiredFields.filter(field => 
      profile[field] && profile[field] !== ''
    );
    
    return {
      percentage: Math.round((completed.length / requiredFields.length) * 100),
      missingFields: requiredFields.filter(f => !profile[f]),
      completedFields: completed
    };
  }
}
```

#### 2. Profile Page (Read-Only View)
```typescript
// /src/features/identity/pages/ProfilePage.tsx
export const ProfilePage = () => {
  const [profile, setProfile] = useState(null);
  
  useEffect(() => {
    // Load enhanced profile
    IdentityProfileService.getProfile(currentUser.id)
      .then(setProfile);
  }, []);
  
  if (!profile) return <div>Loading...</div>;
  
  return (
    <div className="identity-profile">
      <h1>Identity Profile</h1>
      
      <div className="profile-header">
        <img src={profile.avatar_url} alt="Avatar" />
        <h2>{profile.full_name}</h2>
        <div className="completion">
          <progress value={profile.completionPercentage} max="100" />
          <span>{profile.completionPercentage}% Complete</span>
        </div>
      </div>
      
      <div className="profile-details">
        <h3>Profile Information</h3>
        <dl>
          <dt>Name</dt>
          <dd>{profile.full_name || 'Not set'}</dd>
          
          <dt>Date of Birth</dt>
          <dd>{profile.date_of_birth || 'Not set'}</dd>
          
          <dt>Phone</dt>
          <dd>{profile.phone || 'Not set'}</dd>
          
          <dt>Role</dt>
          <dd>{profile.role || 'player'}</dd>
        </dl>
      </div>
      
      {profile.missingFields.length > 0 && (
        <div className="missing-fields">
          <h3>Complete Your Profile</h3>
          <ul>
            {profile.missingFields.map(field => (
              <li key={field}>{field}</li>
            ))}
          </ul>
        </div>
      )}
      
      <div className="actions">
        <button disabled>Edit Profile (Coming Week 2)</button>
        <a href="/profile">Use Classic Profile</a>
      </div>
    </div>
  );
};
```

### DEV4 - Onboarding Planning

#### 1. Onboarding Analysis Service
```typescript
// /src/features/identity/services/OnboardingAnalysisService.ts
export class OnboardingAnalysisService {
  // Analyze existing registration patterns
  static async analyzeUserTypes() {
    // Read from existing data
    const { data: profiles } = await supabase
      .from('profiles')
      .select('role, created_at, has_children')
      .order('created_at', { ascending: false })
      .limit(1000);
    
    const analysis = {
      totalUsers: profiles.length,
      byRole: {
        player: profiles.filter(p => p.role === 'player').length,
        parent: profiles.filter(p => p.role === 'parent').length,
        coach: profiles.filter(p => p.role === 'coach').length,
      },
      parentsWithChildren: profiles.filter(p => p.has_children).length,
      registrationTrends: this.calculateTrends(profiles)
    };
    
    return analysis;
  }
  
  // Map existing flows (read-only)
  static async mapExistingFlows() {
    return {
      registration: {
        path: '/registration',
        steps: ['email', 'password', 'profile'],
        completion_rate: await this.getCompletionRate('registration')
      },
      quickOnboarding: {
        path: '/onboarding/quick',
        steps: ['account', 'child', 'complete'],
        completion_rate: await this.getCompletionRate('quick')
      }
    };
  }
}
```

#### 2. Onboarding UI Components (No Data Changes)
```typescript
// /src/features/identity/components/onboarding/OnboardingWizard.tsx
export const OnboardingWizard = ({ steps, currentStep }) => {
  // Pure UI component - no data mutations
  return (
    <div className="identity-onboarding-wizard">
      <div className="step-indicator">
        {steps.map((step, index) => (
          <div 
            key={step.id}
            className={`step ${index === currentStep ? 'active' : ''}`}
          >
            <span className="step-number">{index + 1}</span>
            <span className="step-title">{step.title}</span>
          </div>
        ))}
      </div>
      
      <div className="step-content">
        {/* Week 1: Just UI mockups */}
        <div className="mockup-notice">
          <p>Onboarding UI Preview</p>
          <p>Functional in Week 2</p>
        </div>
      </div>
    </div>
  );
};
```

## Testing Strategy (Week 1)

### 1. Compatibility Tests
```typescript
describe('Identity System Compatibility', () => {
  test('Existing login still works', async () => {
    await page.goto('/UpdatedLoginV2');
    await login('<EMAIL>', 'password');
    expect(page.url()).toContain('/dashboard');
  });
  
  test('New identity login uses same auth', async () => {
    await page.goto('/identity/login');
    await login('<EMAIL>', 'password');
    
    // Should be logged into both systems
    const oldSystemAuth = await getAuthStatus();
    const newSystemAuth = await getIdentityAuthStatus();
    
    expect(oldSystemAuth.user.id).toBe(newSystemAuth.user.id);
  });
  
  test('Profile data is consistent', async () => {
    const oldProfile = await ProfileService.getProfile(userId);
    const newProfile = await IdentityProfileService.getProfile(userId);
    
    // Core data should match
    expect(oldProfile.full_name).toBe(newProfile.full_name);
    expect(oldProfile.avatar_url).toBe(newProfile.avatar_url);
  });
});
```

### 2. Read-Only Verification
```typescript
describe('Week 1 Read-Only Constraints', () => {
  test('Identity system does not modify profiles table', async () => {
    const before = await getTableChecksum('profiles');
    
    // Use identity system
    await IdentityProfileService.getProfile(userId);
    await IdentityProfileService.calculateCompletion(profile);
    
    const after = await getTableChecksum('profiles');
    expect(before).toBe(after); // No changes
  });
  
  test('Only creates in identity_ tables', async () => {
    const tables = await getModifiedTables();
    
    const nonIdentityTables = tables.filter(t => 
      !t.startsWith('identity_')
    );
    
    expect(nonIdentityTables).toHaveLength(0);
  });
});
```

## Week 1 Deliverables

### DEV1
- ✅ Identity directory structure created
- ✅ Read-only auth service using existing auth
- ✅ New login page (same auth, new UI)
- ✅ Feature flag system ready
- ✅ Zero impact on existing login

### DEV2
- ✅ IdentityContext reading existing data
- ✅ Family dashboard showing existing relationships
- ✅ Account list display (no switching yet)
- ✅ No modifications to family data
- ✅ Classic system links provided

### DEV3
- ✅ Profile service reading existing profiles
- ✅ Completion calculation (not stored)
- ✅ Profile page showing existing data
- ✅ Missing fields identified
- ✅ No profile modifications

### DEV4
- ✅ Existing flows documented
- ✅ User type analysis complete
- ✅ Onboarding UI mockups ready
- ✅ Gap analysis for Week 2
- ✅ No data changes

## Success Metrics

### Zero Breaking Changes
- ✅ All existing pages still work
- ✅ No existing table modifications
- ✅ Old and new systems coexist
- ✅ Users unaware of changes

### Foundation Ready
- ✅ Identity structure established
- ✅ Read services functional
- ✅ UI components created
- ✅ Ready for Week 2 enhancements

---

## Document Metadata

**Last Updated**: 2025-08-17
**Status**: Zero Breaking Changes Approach
**Week**: 1 of 4
**Philosophy**: Addition-Only, Parallel Systems
**Document Version**: 3.0