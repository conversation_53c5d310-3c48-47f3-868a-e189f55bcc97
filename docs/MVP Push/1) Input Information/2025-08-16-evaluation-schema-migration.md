# Handover: Evaluation System Schema Migration

**Date**: 2025-08-16  
**Session Focus**: Implementing parallel evaluation schema for SHOT Perform framework  
**Status**: Design complete, implementation ready to begin

## Session Summary

This session focused on analyzing the current evaluation system implementation and designing a new, simplified database schema that can run in parallel with the existing system. The new design consolidates the current two-table structure (`pre_evaluations` and `player_evaluations`) into a single `event_evaluations` table within a separate schema.

## Key Accomplishments

### 1. **Comprehensive System Analysis**
- Documented the complete SHOT Perform framework architecture
- Identified all components using the evaluation system (15+ UI components, 3 core services)
- Analyzed database structure: 173 pre-evaluations, 916 player evaluations
- Found position storage inconsistencies and data redundancy issues

### 2. **New Schema Design**
- Created parallel `evaluations` schema following commerce schema pattern
- Designed single `event_evaluations` table with:
  - One row per evaluation criteria per player per event
  - All scores (pre, coach, post) in same row with decimal support
  - Questions and answers frozen at creation time
  - Individual timestamps for each evaluation type

### 3. **Implementation Files Created**
- `/supabase/migrations/create_evaluations_schema.sql` - Complete database migration
- `/src/services/NewEvaluationService.ts` - Service layer for new schema
- `/src/components/TestNewEvaluations.tsx` - Test component for validation

## Current State

### Database
- **Old System**: Still running with `pre_evaluations` (173 records) and `player_evaluations` (916 records)
- **New System**: Ready to deploy, uses separate `evaluations` schema for isolation
- **Access Pattern**: Uses public views with INSTEAD OF triggers (same as commerce schema)

### Code Changes
- `PlayerEvaluationService.ts` - Updated to mark evaluations as 'submitted' instead of 'draft'
- `EventEvaluation.tsx` - Added debug data to evaluation components
- Feature flag ready: `REACT_APP_USE_NEW_EVALUATIONS=true`

### Key Design Decisions
1. **Separate Schema**: Allows parallel testing without touching production data
2. **Public Views**: Ensures Supabase v1 compatibility (client doesn't support `.schema()`)
3. **RPC Functions**: Handle complex operations like bulk creation and security
4. **Decimal Scores**: Support ratings like 7.5, 8.3 for more precision

## Next Steps

### Immediate Actions
1. **Deploy Database Schema**
   ```bash
   psql $DATABASE_URL -f supabase/migrations/create_evaluations_schema.sql
   ```

2. **Enable Feature Flag**
   ```bash
   # Add to .env.local
   REACT_APP_USE_NEW_EVALUATIONS=true
   ```

3. **Test Basic Flow**
   - Create test event
   - Run `newEvaluationService.createEventEvaluations(eventId)`
   - Verify evaluations created correctly
   - Test saving pre and coach scores

### Implementation Tasks

#### Phase 1: Core Integration (Priority: HIGH)
- [ ] Add feature toggle to `EventEvaluation.tsx` to use new service
- [ ] Update `PlayerSelfEvaluationForm.tsx` for pre-evaluations
- [ ] Create comparison tool to verify data consistency
- [ ] Test with different player positions and weeks

#### Phase 2: UI Updates (Priority: MEDIUM)
- [ ] Update evaluation display components to handle decimal scores
- [ ] Modify save logic to use batch operations
- [ ] Add progress indicators for partial evaluations
- [ ] Update statistics calculations

#### Phase 3: Migration Tools (Priority: LOW)
- [ ] Create script to copy test data from old to new schema
- [ ] Build side-by-side comparison dashboard
- [ ] Document performance improvements
- [ ] Plan production migration strategy

## Technical Context

### Key Services/Components to Update
1. **EventEvaluation.tsx** - Main coach evaluation interface
2. **PlayerSelfEvaluationForm.tsx** - Player pre-evaluation form
3. **PreEvaluationRequestButton.tsx** - Triggers evaluation creation
4. **EventEvaluationService.ts** - Statistics and summaries
5. **Shadow components** - UI evaluation display components

### Database Queries Comparison

**Old System** (complex joins):
```sql
SELECT pe.*, pev.* 
FROM pre_evaluations pe
LEFT JOIN player_evaluations pev ON pe.id = pev.pre_evaluation_id
WHERE pe.event_id = ? AND pe.player_id = ?
```

**New System** (simple direct query):
```sql
SELECT * FROM event_evaluations
WHERE event_id = ? AND player_id = ?
ORDER BY category, area
```

### RLS Policies
- Players can view/update their own pre-evaluations
- Coaches can view/update all evaluations for their teams
- Service role has full access for RPC functions

## Important Notes

### Position Normalization
The system currently has inconsistent position formats:
- `STRIKER` vs `striker`
- `FULL BACK` vs `full_back`
- `CENTRE BACK` vs `center back`

The new schema includes normalization in the migration.

### Evaluation Criteria
- Questions vary by week (1-52) and position
- Framework version is tracked (currently SHOT-2025)
- Position-specific questions are integrated into 4 main categories
- No separate "POSITIONAL" category despite UI expectations

### Testing Recommendations
1. Start with a single test event
2. Compare old vs new query performance
3. Verify RLS policies work correctly
4. Test decimal score support (7.5, 8.3)
5. Validate pre-evaluation to coach evaluation flow

## Blockers/Risks

1. **Supabase v1 Compatibility**: Must use public views, cannot use `.schema()` directly
2. **UI Decimal Support**: Some components may need updates for decimal display
3. **Migration Complexity**: Need careful planning to migrate production data
4. **Feature Flag Management**: Ensure consistent toggling across all components

## Resources

- **Documentation Created**:
  - `/docs/shot-perform-framework-architecture.md`
  - `/docs/evaluation-database-simplification-proposal.md`
  - `/docs/event-centric-evaluation-schema-proposal.md`
  - `/docs/area-based-evaluation-schema-proposal.md`
  - `/docs/flexible-event-evaluations-schema-final.md`
  - `/docs/parallel-evaluation-schema-implementation.md`

- **Key Decisions**:
  - Use parallel schema instead of modifying existing tables
  - Follow commerce schema pattern for cross-schema access
  - Support decimal scores for more precise ratings
  - Freeze questions at event creation time

## Contact for Questions

The new evaluation system is designed to be significantly simpler while maintaining all current functionality. The parallel implementation allows safe testing without risking the production system.

---

**Handover prepared by**: Claude  
**Session health**: Good (within message limits)  
**Recommendation**: Continue with Phase 1 implementation tasks