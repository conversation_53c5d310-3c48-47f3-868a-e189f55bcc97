# Complete Assess Event System Plan

## Overview
This document outlines the complete redesign of the event and evaluation system under the `/features/assess` structure. The new system is completely separate from the old event system, allowing parallel development and gradual migration.

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [Page Flow and Purpose](#page-flow-and-purpose)
3. [Component Structure](#component-structure)
4. [Database Design](#database-design)
5. [Data Import System](#data-import-system)
6. [Implementation Timeline](#implementation-timeline)

## Architecture Overview

### Directory Structure
```
src/features/assess/
├── components/
│   ├── shared/
│   │   ├── EventCard.tsx (wrapper around ShadowEventCard)
│   │   ├── PlayerSelector.tsx (wrapper around PlayerAttendanceSelector)
│   │   ├── CoachSelector.tsx
│   │   └── EventStatusBar.tsx
│   ├── create/
│   │   ├── EventTypeSelector.tsx
│   │   ├── EventDateTimePicker.tsx
│   │   ├── LocationSelector.tsx
│   │   └── QuickEventForm.tsx
│   ├── draft/
│   │   ├── DraftEventHeader.tsx
│   │   ├── PlayerPositionEditor.tsx
│   │   ├── PreEvaluationSetup.tsx
│   │   ├── PublishChecklist.tsx
│   │   └── SMSAutomationControl.tsx
│   ├── published/
│   │   ├── InvitationManager.tsx
│   │   ├── PreEvaluationStatus.tsx
│   │   └── EventCountdown.tsx
│   ├── session/
│   │   ├── AttendanceTracker.tsx
│   │   ├── QuickAttendance.tsx
│   │   └── SessionNotes.tsx
│   ├── evaluation/
│   │   ├── PlayerEvaluationForm.tsx
│   │   ├── BatchEvaluationGrid.tsx
│   │   ├── EvaluationProgress.tsx
│   │   └── QuickRating.tsx
│   ├── review/
│   │   ├── EventSummary.tsx
│   │   ├── PlayerReports.tsx
│   │   └── ExportOptions.tsx
│   ├── import/
│   │   ├── FileUploadZone.tsx
│   │   ├── ImportPreview.tsx
│   │   ├── DuplicateResolver.tsx
│   │   └── ImportReport.tsx
│   └── player/
│       ├── PlayerEvaluationDisplay.tsx
│       ├── CoachFeedbackCard.tsx
│       └── ProgressIndicators.tsx
├── pages/
│   ├── event/
│   │   ├── CreateEventPage.tsx
│   │   ├── EventDraftPage.tsx
│   │   ├── EventPublishedPage.tsx
│   │   ├── EventSessionPage.tsx
│   │   ├── EventEvaluatePage.tsx
│   │   └── EventReviewPage.tsx
│   ├── pre-evaluation/
│   │   ├── PreEvaluationFormPage.tsx
│   ├── evaluation/
│   │   ├── PlayerEvaluationPage.tsx
│   │   └── BatchEvaluationPage.tsx
│   ├── import/
│   │   └── TeamImportPage.tsx
│   └── player/
│       └── PlayerEvaluationView.tsx
├── hooks/
│   ├── useEventStage.ts
│   ├── useEventData.ts
│   ├── useAttendance.ts
│   ├── useEvaluations.ts
│   └── usePreEvaluations.ts
├── services/
│   ├── AssessEventService.ts
│   ├── AssessEvaluationService.ts
│   ├── AssessNotificationService.ts
│   └── ImportService.ts
├── routes/
│   └── AssessRoutes.tsx
└── types/
    ├── event.types.ts
    └── evaluation.types.ts
```

## Page Flow and Purpose

### Complete Event Lifecycle Flow

```mermaid
graph TB
    Start([Coach Creates Event]) --> Create[CreateEventPage]
    
    %% Creation Stage
    Create --> |Basic Details| Draft[EventDraftPage]
    
    %% Draft Stage
    Draft --> |Edit Details| Draft
    Draft --> |Select Players| PlayerSelection[Player Selection Modal]
    PlayerSelection --> |Assign Positions| Draft
    Draft --> |Setup Pre-Eval| PreEvalSetup[Pre-Evaluation Setup]
    PreEvalSetup --> |Select Players| Draft
    Draft --> |SMS Settings| SMSControl[SMS Automation Control]
    SMSControl --> Draft
    Draft --> |Publish Event| PublishCheck{Publish Checklist}
    PublishCheck --> |Not Ready| Draft
    PublishCheck --> |Ready| Published[EventPublishedPage]
    
    %% Published Stage (Pre-Event)
    Published --> |Auto SMS?| SMSDecision{Auto-Send Enabled?}
    SMSDecision --> |Yes| AutoSMS[SMS Sent Automatically]
    SMSDecision --> |No| ManualSMS[Manual SMS Button]
    ManualSMS --> |Send| SMSSent[SMS Sent]
    AutoSMS --> PreEvalMonitor[Monitor Pre-Evals]
    SMSSent --> PreEvalMonitor
    PreEvalMonitor --> |Time Passes| Session[EventSessionPage]
    
    %% Session Stage (During Event)
    Session --> |Mark Attendance| AttendanceModal[Quick Attendance]
    AttendanceModal --> Session
    Session --> |End Session| AttendanceConfirm{Confirm Attendance}
    AttendanceConfirm --> |Edit| Session
    AttendanceConfirm --> |Confirm| Evaluate[EventEvaluatePage]
    
    %% Evaluation Stage (Post-Event)
    Evaluate --> |Select Player| PlayerEval[PlayerEvaluationPage]
    PlayerEval --> |Save & Next| Evaluate
    Evaluate --> |All Complete| Review[EventReviewPage]
    
    %% Review Stage (Completed)
    Review --> |Share Results| ShareModal[Share Options]
    ShareModal --> Review
    Review --> End([Event Complete])
```

### Page Details

#### 0. Create Event Page
**Purpose**: Simple, focused interface for creating a new event. This is the starting point before the draft page.

**Path**: `/src/features/assess/pages/event/CreateEventPage.tsx`
**Route**: `/coach/assess/team/:teamId/events/new`

```typescript
/**
 * CreateEventPage
 * 
 * PURPOSE:
 * - Quick event creation with minimal fields
 * - Set event type (training/match/tournament)
 * - Pick date, time, duration
 * - Choose location
 * - Create draft for further configuration
 * 
 * USER GOALS:
 * - Create event in under 30 seconds
 * - Not be overwhelmed with options
 * - Quickly set basic details
 * - Move to detailed configuration after
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowModal } from '@/foundation/design-system/components/molecules/Modals';
import { ShadowToast } from '@/foundation/design-system/components/atoms/Toast';
import { ShadowBackButton } from '@/foundation/design-system/components/atoms/Button/ShadowBackButton';

// From old system (if reusing)
import PageWithNavigation from '@/src/components/PageWithNavigation';
```

**New Components Required:**
```typescript
// Event type selection cards
import { EventTypeSelector } from '@/src/features/assess/components/create/EventTypeSelector';
// Path: /src/features/assess/components/create/EventTypeSelector.tsx

// Date and time picker component
import { EventDateTimePicker } from '@/src/features/assess/components/create/EventDateTimePicker';
// Path: /src/features/assess/components/create/EventDateTimePicker.tsx

// Location dropdown with saved venues
import { LocationSelector } from '@/src/features/assess/components/create/LocationSelector';
// Path: /src/features/assess/components/create/LocationSelector.tsx

// Minimal form wrapper
import { QuickEventForm } from '@/src/features/assess/components/create/QuickEventForm';
// Path: /src/features/assess/components/create/QuickEventForm.tsx
```

**Services Used:**
```typescript
import { AssessEventService } from '@/src/features/assess/services/AssessEventService';
// Methods: createEvent(data)
```

#### 1. Event Draft Page
**Purpose**: Prepare and configure an event before making it visible to players. This is where coaches set up all event details, select participants, configure pre-evaluations, and decide on notification preferences.

**Path**: `/src/features/assess/pages/event/EventDraftPage.tsx`
**Route**: `/coach/assess/event/:eventId/draft`

```typescript
/**
 * EventDraftPage
 * 
 * PURPOSE:
 * - Central hub for event preparation before publishing
 * - Configure all event settings in draft mode
 * - Select players and assign positions
 * - Set up pre-evaluation requirements
 * - Control SMS notification timing
 * 
 * USER GOALS:
 * - Coach wants to plan event without alerting players
 * - Set up everything correctly before going live
 * - Decide who needs pre-evaluations
 * - Control when players are notified
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowEventCard } from '@/foundation/design-system/components/molecules/Cards/ShadowEventCard';
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowModal } from '@/foundation/design-system/components/molecules/Modals';
import { ShadowToast } from '@/foundation/design-system/components/atoms/Toast';

// From existing event management
import { PlayerAttendanceSelector } from '@/src/pages/section/Coach/supporting/EventManagement/components/PlayerAttendanceSelector';
// Path: /src/pages/section/Coach/supporting/EventManagement/components/PlayerAttendanceSelector.tsx

// From old system
import PageWithNavigation from '@/src/components/PageWithNavigation';
```

**New Components Required:**
```typescript
// Header showing draft status
import { DraftEventHeader } from '@/src/features/assess/components/draft/DraftEventHeader';
// Path: /src/features/assess/components/draft/DraftEventHeader.tsx

// Position editing for players
import { PlayerPositionEditor } from '@/src/features/assess/components/draft/PlayerPositionEditor';
// Path: /src/features/assess/components/draft/PlayerPositionEditor.tsx

// Pre-evaluation player selection
import { PreEvaluationSetup } from '@/src/features/assess/components/draft/PreEvaluationSetup';
// Path: /src/features/assess/components/draft/PreEvaluationSetup.tsx

// Publish validation checklist
import { PublishChecklist } from '@/src/features/assess/components/draft/PublishChecklist';
// Path: /src/features/assess/components/draft/PublishChecklist.tsx

// SMS automation control
import { SMSAutomationControl } from '@/src/features/assess/components/draft/SMSAutomationControl';
// Path: /src/features/assess/components/draft/SMSAutomationControl.tsx
```

**Services Used:**
```typescript
import { AssessEventService } from '@/src/features/assess/services/AssessEventService';
// Methods: updateEvent(id, data), publishEvent(id, settings)

import { teamMemberService } from '@/src/services/TeamMemberService';
// Methods: getTeamMembers(teamId)
```

**Hooks Used:**
```typescript
import { useEventData } from '@/src/features/assess/hooks/useEventData';
import { useAutoSave } from '@/src/features/assess/hooks/useAutoSave';
```

#### 2. Event Published Page
**Purpose**: Monitor and manage a published event before it starts. This page helps coaches track pre-evaluation completion, send reminders, and ensure everything is ready for the actual event.

**Path**: `/src/features/assess/pages/event/EventPublishedPage.tsx`
**Route**: `/coach/assess/event/:eventId`

```typescript
/**
 * EventPublishedPage
 * 
 * PURPOSE:
 * - Monitor event preparation status
 * - Track pre-evaluation completion
 * - Send manual SMS reminders if not auto-sent
 * - View player responses and readiness
 * - Last chance to make adjustments
 * 
 * USER GOALS:
 * - See who has completed pre-evaluations
 * - Send reminders to non-responsive players
 * - Ensure team is prepared for event
 * - Monitor countdown to event start
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowEventCard } from '@/foundation/design-system/components/molecules/Cards/ShadowEventCard';
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowPlayersList } from '@/foundation/design-system/components/molecules/Lists/ShadowPlayersList';
import { ShadowToast } from '@/foundation/design-system/components/atoms/Toast';
import { ShadowModal } from '@/foundation/design-system/components/molecules/Modals';

// From existing components
import { ShadowPreEvaluationSMS } from '@/src/components/shadow/ShadowPreEvaluationSMS';
// Path: /src/components/shadow/ShadowPreEvaluationSMS.tsx

// From old system
import PageWithNavigation from '@/src/components/PageWithNavigation';
```

**New Components Required:**
```typescript
// Shows invitation status
import { InvitationManager } from '@/src/features/assess/components/published/InvitationManager';
// Path: /src/features/assess/components/published/InvitationManager.tsx

// Pre-evaluation completion list
import { PreEvaluationStatusList } from '@/src/features/assess/components/published/PreEvaluationStatusList';
// Path: /src/features/assess/components/published/PreEvaluationStatusList.tsx

// Countdown timer to event
import { EventCountdown } from '@/src/features/assess/components/published/EventCountdown';
// Path: /src/features/assess/components/published/EventCountdown.tsx

// Quick SMS send button
import { QuickSMSButton } from '@/src/features/assess/components/published/QuickSMSButton';
// Path: /src/features/assess/components/published/QuickSMSButton.tsx
```

**Services Used:**
```typescript
import { AssessEventService } from '@/src/features/assess/services/AssessEventService';
// Methods: getEvent(id), getEventParticipants(id)

import { AssessNotificationService } from '@/src/features/assess/services/AssessNotificationService';
// Methods: sendPreEvalSMS(eventId, playerIds), getSMSStatus(eventId)

import { EventParticipantService } from '@/src/services/EventParticipantService';
// Methods: getEventParticipants(eventId)
```

**Hooks Used:**
```typescript
import { useEventData } from '@/src/features/assess/hooks/useEventData';
import { usePreEvaluations } from '@/src/features/assess/hooks/usePreEvaluations';
```

#### 3. Event Session Page
**Purpose**: Manage the live event session. This is the active page during the event where coaches track attendance, handle late arrivals, and prepare for post-event evaluations.

**Path**: `/src/features/assess/pages/event/EventSessionPage.tsx`
**Route**: `/coach/assess/event/:eventId/session`

```typescript
/**
 * EventSessionPage
 * 
 * PURPOSE:
 * - Real-time attendance tracking during event
 * - Handle dynamic changes (late arrivals, early departures)
 * - Capture session notes
 * - Transition smoothly to evaluation phase
 * 
 * USER GOALS:
 * - Quickly mark who attended
 * - Handle unexpected attendance changes
 * - Note important observations
 * - Prepare for immediate post-event evaluation
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowEventCard } from '@/foundation/design-system/components/molecules/Cards/ShadowEventCard';
import { ShadowPlayersList } from '@/foundation/design-system/components/molecules/Lists/ShadowPlayersList';
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowModal } from '@/foundation/design-system/components/molecules/Modals';
import { ShadowToast } from '@/foundation/design-system/components/atoms/Toast';

// From old system
import PageWithNavigation from '@/src/components/PageWithNavigation';
```

**New Components Required:**
```typescript
// Main attendance tracking interface
import { AttendanceTracker } from '@/src/features/assess/components/session/AttendanceTracker';
// Path: /src/features/assess/components/session/AttendanceTracker.tsx

// Quick toggle buttons for attendance
import { AttendanceQuickToggle } from '@/src/features/assess/components/session/AttendanceQuickToggle';
// Path: /src/features/assess/components/session/AttendanceQuickToggle.tsx

// Session duration timer
import { SessionTimer } from '@/src/features/assess/components/session/SessionTimer';
// Path: /src/features/assess/components/session/SessionTimer.tsx

// Attendance summary bar
import { AttendanceSummaryBar } from '@/src/features/assess/components/session/AttendanceSummaryBar';
// Path: /src/features/assess/components/session/AttendanceSummaryBar.tsx

// Late arrival handling
import { LateArrivalButton } from '@/src/features/assess/components/session/LateArrivalButton';
// Path: /src/features/assess/components/session/LateArrivalButton.tsx

// Session notes component
import { SessionNotes } from '@/src/features/assess/components/session/SessionNotes';
// Path: /src/features/assess/components/session/SessionNotes.tsx
```

**Services Used:**
```typescript
import { AssessEventService } from '@/src/features/assess/services/AssessEventService';
// Methods: getEvent(id), updateEventStatus(id, status)

import { EventParticipantService } from '@/src/services/EventParticipantService';
// Methods: updateAttendance(participantId, status), batchUpdateAttendance(updates)
```

**Hooks Used:**
```typescript
import { useEventData } from '@/src/features/assess/hooks/useEventData';
import { useAttendance } from '@/src/features/assess/hooks/useAttendance';
import { useAutoSave } from '@/src/features/assess/hooks/useAutoSave';
```

#### 4. Event Evaluate Page
**Purpose**: Central hub for post-event player evaluations. Coaches can see all attended players, track evaluation progress, and choose between individual or batch evaluation modes.

**Path**: `/src/features/assess/pages/event/EventEvaluatePage.tsx`
**Route**: `/coach/assess/event/:eventId/evaluate`

```typescript
/**
 * EventEvaluatePage
 * 
 * PURPOSE:
 * - Overview of all players needing evaluation
 * - Track evaluation completion progress
 * - Navigate to individual player evaluations
 * - Access batch evaluation mode
 * - Ensure no player is missed
 * 
 * USER GOALS:
 * - See who needs evaluation at a glance
 * - Evaluate players while memory is fresh
 * - Choose most efficient evaluation method
 * - Track progress to completion
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowEventCard } from '@/foundation/design-system/components/molecules/Cards/ShadowEventCard';
import { ShadowPlayersList } from '@/foundation/design-system/components/molecules/Lists/ShadowPlayersList';
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowProgressBar } from '@/foundation/design-system/components/atoms/ProgressBar';
import { ShadowToast } from '@/foundation/design-system/components/atoms/Toast';

// From old system
import PageWithNavigation from '@/src/components/PageWithNavigation';
```

**New Components Required:**
```typescript
// Overall evaluation progress card
import { EvaluationProgressCard } from '@/src/features/assess/components/evaluation/EvaluationProgressCard';
// Path: /src/features/assess/components/evaluation/EvaluationProgressCard.tsx

// Grid view of players to evaluate
import { PlayerEvaluationGrid } from '@/src/features/assess/components/evaluation/PlayerEvaluationGrid';
// Path: /src/features/assess/components/evaluation/PlayerEvaluationGrid.tsx

// Quick rating modal for fast evaluation
import { QuickRatingModal } from '@/src/features/assess/components/evaluation/QuickRatingModal';
// Path: /src/features/assess/components/evaluation/QuickRatingModal.tsx

// Status badge showing evaluation state
import { EvaluationStatusBadge } from '@/src/features/assess/components/evaluation/EvaluationStatusBadge';
// Path: /src/features/assess/components/evaluation/EvaluationStatusBadge.tsx
```

**Services Used:**
```typescript
import { AssessEventService } from '@/src/features/assess/services/AssessEventService';
// Methods: getEvent(id), getAttendedPlayers(eventId)

import { AssessEvaluationService } from '@/src/features/assess/services/AssessEvaluationService';
// Methods: getEvaluationProgress(eventId), getPlayerEvaluationStatus(eventId, playerId)
```

**Hooks Used:**
```typescript
import { useEventData } from '@/src/features/assess/hooks/useEventData';
import { useEvaluations } from '@/src/features/assess/hooks/useEvaluations';
```

#### 5. Player Evaluation Form Page
**Purpose**: Detailed evaluation form for a single player. This focused interface allows coaches to provide ratings and feedback for one player at a time with auto-save and easy navigation to the next player.

**Path**: `/src/features/assess/pages/evaluation/PlayerEvaluationPage.tsx`
**Route**: `/coach/assess/event/:eventId/evaluate/:playerId`

```typescript
/**
 * PlayerEvaluationPage
 * 
 * PURPOSE:
 * - Focused evaluation of individual player
 * - Provide ratings across key categories
 * - Add personalized feedback notes
 * - Auto-save progress
 * - Quick navigation to next player
 * 
 * USER GOALS:
 * - Give thoughtful evaluation per player
 * - Not lose any entered data
 * - Move efficiently through team
 * - Provide constructive feedback
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowToast } from '@/foundation/design-system/components/atoms/Toast';
import { ShadowBackButton } from '@/foundation/design-system/components/atoms/Button/ShadowBackButton';

// From old system
import PageWithNavigation from '@/src/components/PageWithNavigation';
```

**New Components Required:**
```typescript
// Player info header with photo
import { PlayerEvaluationHeader } from '@/src/features/assess/components/evaluation/PlayerEvaluationHeader';
// Path: /src/features/assess/components/evaluation/PlayerEvaluationHeader.tsx

// Rating slider for each category
import { CategoryRatingSlider } from '@/src/features/assess/components/evaluation/CategoryRatingSlider';
// Path: /src/features/assess/components/evaluation/CategoryRatingSlider.tsx

// Text input for coach notes
import { EvaluationNotesInput } from '@/src/features/assess/components/evaluation/EvaluationNotesInput';
// Path: /src/features/assess/components/evaluation/EvaluationNotesInput.tsx

// Auto-save indicator
import { SaveDraftIndicator } from '@/src/features/assess/components/evaluation/SaveDraftIndicator';
// Path: /src/features/assess/components/evaluation/SaveDraftIndicator.tsx

// Navigate to next player button
import { NextPlayerButton } from '@/src/features/assess/components/evaluation/NextPlayerButton';
// Path: /src/features/assess/components/evaluation/NextPlayerButton.tsx

// Shared evaluation categories component
import { EvaluationCategories } from '@/src/features/assess/components/shared/EvaluationCategories';
// Path: /src/features/assess/components/shared/EvaluationCategories.tsx
```

**Services Used:**
```typescript
import { AssessEvaluationService } from '@/src/features/assess/services/AssessEvaluationService';
// Methods: getPlayerEvaluation(eventId, playerId), saveEvaluation(evaluationId, data), getNextPlayer(eventId, currentPlayerId)

import { PlayerService } from '@/src/services/PlayerService';
// Methods: getPlayerProfile(playerId)
```

**Hooks Used:**
```typescript
import { useEvaluations } from '@/src/features/assess/hooks/useEvaluations';
import { useAutoSave } from '@/src/features/assess/hooks/useAutoSave';
```

#### 6. Pre-Evaluation Form (No Login)
**Purpose**: Simple, mobile-friendly form for players to complete pre-event self-assessments. No login required - accessed via unique link sent by SMS.

**Path**: `/src/features/assess/pages/pre-evaluation/PreEvaluationFormPage.tsx`
**Route**: `/pre-eval/:token`

```typescript
/**
 * PreEvaluationFormPage
 * 
 * PURPOSE:
 * - Frictionless pre-event self-assessment
 * - No login barrier for players
 * - Mobile-optimized experience
 * - Quick completion (< 2 minutes)
 * - Automatic association with player/event
 * 
 * USER GOALS (Player):
 * - Quickly share how I'm feeling
 * - Understand what's being asked
 * - Complete on my phone easily
 * - Know my response was received
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowEventCard } from '@/foundation/design-system/components/molecules/Cards/ShadowEventCard';
import { ShadowToast } from '@/foundation/design-system/components/atoms/Toast';

// Note: No PageWithNavigation - this is a standalone page
```

**New Components Required:**
```typescript
// Header with player name and event info
import { PreEvalHeader } from '@/src/features/assess/components/pre-evaluation/PreEvalHeader';
// Path: /src/features/assess/components/pre-evaluation/PreEvalHeader.tsx

// Question card with rating options
import { PreEvalQuestionCard } from '@/src/features/assess/components/pre-evaluation/PreEvalQuestionCard';
// Path: /src/features/assess/components/pre-evaluation/PreEvalQuestionCard.tsx

// Progress bar showing completion
import { PreEvalProgressBar } from '@/src/features/assess/components/pre-evaluation/PreEvalProgressBar';
// Path: /src/features/assess/components/pre-evaluation/PreEvalProgressBar.tsx

// Thank you/confirmation screen
import { PreEvalThankYou } from '@/src/features/assess/components/pre-evaluation/PreEvalThankYou';
// Path: /src/features/assess/components/pre-evaluation/PreEvalThankYou.tsx
```

**Services Used:**
```typescript
import { PreEvalTokenService } from '@/src/features/assess/services/PreEvalTokenService';
// Methods: validateToken(token), getPreEvalData(token), submitPreEval(token, data)

import { AssessEvaluationService } from '@/src/features/assess/services/AssessEvaluationService';
// Methods: savePreEvaluation(evaluationId, scores)
```

**Hooks Used:**
```typescript
import { useAutoSave } from '@/src/features/assess/hooks/useAutoSave';
// Note: No authentication hooks needed
```

#### 7. Event Review Page
**Purpose**: Post-evaluation summary and sharing hub. Coaches can review all evaluations, generate reports, and share results with players and parents.

**Path**: `/src/features/assess/pages/event/EventReviewPage.tsx`
**Route**: `/coach/assess/event/:eventId/review`

```typescript
/**
 * EventReviewPage
 * 
 * PURPOSE:
 * - View complete event summary
 * - Review all player evaluations
 * - Generate and share reports
 * - Export data for records
 * - Close out event workflow
 * 
 * USER GOALS:
 * - See event success metrics
 * - Share feedback with players/parents
 * - Export for club records
 * - Identify team trends
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowEventCard } from '@/foundation/design-system/components/molecules/Cards/ShadowEventCard';
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowPlayersList } from '@/foundation/design-system/components/molecules/Lists/ShadowPlayersList';
import { ShadowModal } from '@/foundation/design-system/components/molecules/Modals';
import { ShadowToast } from '@/foundation/design-system/components/atoms/Toast';

// From old system
import PageWithNavigation from '@/src/components/PageWithNavigation';
```

**New Components Required:**
```typescript
// Event statistics summary
import { EventSummaryStats } from '@/src/features/assess/components/review/EventSummaryStats';
// Path: /src/features/assess/components/review/EventSummaryStats.tsx

// List of all player performances
import { PlayerPerformanceList } from '@/src/features/assess/components/review/PlayerPerformanceList';
// Path: /src/features/assess/components/review/PlayerPerformanceList.tsx

// Share via SMS/link modal
import { ShareOptionsModal } from '@/src/features/assess/components/review/ShareOptionsModal';
// Path: /src/features/assess/components/review/ShareOptionsModal.tsx

// Export to PDF/CSV button
import { ExportButton } from '@/src/features/assess/components/review/ExportButton';
// Path: /src/features/assess/components/review/ExportButton.tsx
```

**Services Used:**
```typescript
import { AssessEventService } from '@/src/features/assess/services/AssessEventService';
// Methods: getEventSummary(eventId), markEventComplete(eventId)

import { AssessEvaluationService } from '@/src/features/assess/services/AssessEvaluationService';
// Methods: getAllEvaluations(eventId), generateReport(eventId, format)

import { ShareLinkService } from '@/src/features/assess/services/ShareLinkService';
// Methods: generatePlayerShareLink(eventId, playerId), generateParentShareLink(eventId, playerId)
```

**Hooks Used:**
```typescript
import { useEventData } from '@/src/features/assess/hooks/useEventData';
import { useShareLinks } from '@/src/features/assess/hooks/useShareLinks';
```

#### 8. Player View (Shared Evaluation)
**Purpose**: Read-only view for players and parents to see evaluation results. Accessed via secure share link with appropriate information based on viewer type (player vs parent).

**Path**: `/src/features/assess/pages/player/PlayerEvaluationView.tsx`
**Route**: `/player/evaluation/:shareToken`

```typescript
/**
 * PlayerEvaluationView
 * 
 * PURPOSE:
 * - Show evaluation results to player/parent
 * - Provide constructive feedback
 * - Display progress and growth areas
 * - Motivate continued development
 * - No login required for access
 * 
 * USER GOALS (Player/Parent):
 * - Understand performance feedback
 * - See areas of strength
 * - Identify improvement opportunities
 * - Feel encouraged and supported
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowEventCard } from '@/foundation/design-system/components/molecules/Cards/ShadowEventCard';

// Note: No PageWithNavigation - this is a public page
```

**New Components Required:**
```typescript
// Display evaluation scores and ratings
import { PlayerEvaluationDisplay } from '@/src/features/assess/components/player/PlayerEvaluationDisplay';
// Path: /src/features/assess/components/player/PlayerEvaluationDisplay.tsx

// Show coach feedback and notes
import { CoachFeedbackCard } from '@/src/features/assess/components/player/CoachFeedbackCard';
// Path: /src/features/assess/components/player/CoachFeedbackCard.tsx

// Visual progress indicators
import { ProgressIndicators } from '@/src/features/assess/components/player/ProgressIndicators';
// Path: /src/features/assess/components/player/ProgressIndicators.tsx

// Development focus areas
import { NextStepsCard } from '@/src/features/assess/components/player/NextStepsCard';
// Path: /src/features/assess/components/player/NextStepsCard.tsx
```

**Services Used:**
```typescript
import { ShareLinkService } from '@/src/features/assess/services/ShareLinkService';
// Methods: validateShareToken(token), getSharedEvaluation(token)

import { AssessEvaluationService } from '@/src/features/assess/services/AssessEvaluationService';
// Methods: getPublicEvaluation(evaluationId)
```

**Hooks Used:**
```typescript
// No authentication hooks needed - public page
```

## Smart Event Router

```typescript
// useEventStage.ts
export const useEventStage = (eventId: string) => {
  const { event, loading } = useEventData(eventId);
  
  const getEventStage = () => {
    if (!event) return 'loading';
    
    if (event.status === 'draft') return 'draft';
    
    const now = new Date();
    const startTime = new Date(event.start_datetime);
    const endTime = new Date(event.end_datetime);
    
    if (event.status === 'published') {
      if (now < startTime) return 'published';
      if (now >= startTime && now <= endTime) return 'session';
      if (now > endTime) return 'evaluate';
    }
    
    if (event.status === 'completed') return 'review';
    
    return 'unknown';
  };
  
  return { stage: getEventStage(), event, loading };
};
```

## Route Configuration

```typescript
// /src/features/assess/routes/AssessRoutes.tsx
export const AssessRoutes = () => (
  <Switch>
    {/* Create new event */}
    <Route path="/coach/assess/team/:teamId/events/new" component={CreateEventPage} />
    
    {/* Smart router - redirects to correct stage */}
    <Route exact path="/coach/assess/event/:eventId" component={AssessEventRouter} />
    
    {/* Stage-specific routes */}
    <Route path="/coach/assess/event/:eventId/draft" component={EventDraftPage} />
    <Route path="/coach/assess/event/:eventId/published" component={EventPublishedPage} />
    <Route path="/coach/assess/event/:eventId/session" component={EventSessionPage} />
    <Route path="/coach/assess/event/:eventId/evaluate" component={EventEvaluatePage} />
    <Route path="/coach/assess/event/:eventId/evaluate/:playerId" component={PlayerEvaluationPage} />
    <Route path="/coach/assess/event/:eventId/review" component={EventReviewPage} />
    
    {/* No-login routes */}
    <Route path="/pre-eval/:token" component={PreEvaluationFormPage} />
    <Route path="/player/evaluation/:shareToken" component={PlayerEvaluationView} />
    
    {/* Import route */}
    <Route path="/coach/assess/team/:teamId/import" component={TeamImportPage} />
  </Switch>
);
```

## Database Design

### Simplified Evaluation Schema

```sql
-- Create new evaluations schema
CREATE SCHEMA IF NOT EXISTS evaluations;

-- Main event evaluations table
CREATE TABLE evaluations.event_evaluations (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    event_id uuid NOT NULL,
    player_id uuid NOT NULL,
    team_id uuid NOT NULL,
    
    -- Criteria reference
    criteria_id uuid NOT NULL,
    category text NOT NULL CHECK (category IN ('TECHNICAL', 'PHYSICAL', 'PSYCHOLOGICAL', 'SOCIAL')),
    area text NOT NULL,
    
    -- Player position (frozen at creation)
    player_position text NOT NULL,
    
    -- Questions (frozen at creation)
    question_pre text,
    question_coach text NOT NULL,
    question_post text,
    
    -- Scores with decimal support
    pre_score numeric(3,1) CHECK (pre_score IS NULL OR (pre_score >= 1.0 AND pre_score <= 10.0)),
    coach_score numeric(3,1) CHECK (coach_score IS NULL OR (coach_score >= 1.0 AND coach_score <= 10.0)),
    post_score numeric(3,1) CHECK (post_score IS NULL OR (post_score >= 1.0 AND post_score <= 10.0)),
    
    -- Timestamps
    pre_submitted_at timestamp with time zone,
    coach_submitted_at timestamp with time zone,
    post_submitted_at timestamp with time zone,
    
    -- Notes
    pre_notes text,
    coach_notes text,
    post_notes text,
    
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    CONSTRAINT unique_player_event_criteria UNIQUE (event_id, player_id, criteria_id)
);
```

### SMS Control Schema

```sql
-- Add SMS automation preference to events table
ALTER TABLE events ADD COLUMN pre_eval_auto_sms BOOLEAN DEFAULT true;
ALTER TABLE events ADD COLUMN sms_sent_at TIMESTAMP WITH TIME ZONE;

-- Pre-eval access tokens (no login required)
CREATE TABLE pre_eval_access (
  id uuid PRIMARY KEY,
  token text UNIQUE,
  event_id uuid,
  player_id uuid,
  expires_at timestamp,
  completed_at timestamp,
  access_count int DEFAULT 0
);
```

### Simplified Trigger System

```sql
-- Single trigger for event publish
CREATE OR REPLACE FUNCTION assess.handle_event_publish_with_sms()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.status = 'published' AND OLD.status = 'draft' 
     AND NEW.is_pre_session_evaluation = true THEN
    
    -- Create evaluations for selected players
    INSERT INTO evaluations.event_evaluations (...);
    
    -- Only create SMS notifications if auto-send is enabled
    IF NEW.pre_eval_auto_sms = true THEN
      INSERT INTO public.pre_evaluation_notifications (
        channel, 
        status, 
        recipient_id,
        recipient_phone,
        body
      )
      SELECT 
        'sms',
        'pending',
        player_id,
        phone,
        'Please complete your pre-evaluation for ' || NEW.name
      FROM assess.event_pre_eval_selections
      WHERE event_id = NEW.id;
      
      -- Mark SMS as sent
      UPDATE events 
      SET sms_sent_at = NOW() 
      WHERE id = NEW.id;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## Data Import System

### 9. Team Import Page
**Purpose**: Allow coaches to bulk import team data from CSV files, including players, coaches, events, and historical evaluations. Essential for onboarding existing teams.

**Path**: `/src/features/assess/pages/import/TeamImportPage.tsx`
**Route**: `/coach/assess/team/:teamId/import`

```typescript
/**
 * TeamImportPage
 * 
 * PURPOSE:
 * - Bulk import existing team data
 * - Support CSV format from external systems
 * - Preview and validate before importing
 * - Handle duplicates intelligently
 * - Import players, events, and evaluations
 * 
 * USER GOALS:
 * - Quickly onboard existing team data
 * - Avoid manual data entry
 * - Preserve historical information
 * - Ensure data integrity
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowModal } from '@/foundation/design-system/components/molecules/Modals';
import { ShadowToast } from '@/foundation/design-system/components/atoms/Toast';
import { ShadowProgressBar } from '@/foundation/design-system/components/atoms/ProgressBar';

// From old system
import PageWithNavigation from '@/src/components/PageWithNavigation';
```

**New Components Required:**
```typescript
// Drag and drop file upload zone
import { FileUploadZone } from '@/src/features/assess/components/import/FileUploadZone';
// Path: /src/features/assess/components/import/FileUploadZone.tsx

// Preview imported data before saving
import { ImportPreview } from '@/src/features/assess/components/import/ImportPreview';
// Path: /src/features/assess/components/import/ImportPreview.tsx

// Handle duplicate records
import { DuplicateResolver } from '@/src/features/assess/components/import/DuplicateResolver';
// Path: /src/features/assess/components/import/DuplicateResolver.tsx

// Show import progress
import { ImportProgress } from '@/src/features/assess/components/import/ImportProgress';
// Path: /src/features/assess/components/import/ImportProgress.tsx

// Final import report
import { ImportReport } from '@/src/features/assess/components/import/ImportReport';
// Path: /src/features/assess/components/import/ImportReport.tsx

// Map CSV columns to database fields
import { ImportMapping } from '@/src/features/assess/components/import/ImportMapping';
// Path: /src/features/assess/components/import/ImportMapping.tsx
```

**Services Used:**
```typescript
import { ImportService } from '@/src/features/assess/services/ImportService';
// Methods: 
// - importPlayers(csvData, teamId)
// - importCoaches(csvData, teamId)
// - importEvents(csvData, teamId)
// - checkDuplicates(data, type)
// - normalizePhoneNumbers(phones)
// - parseEventDates(dateStrings)

import { teamMemberService } from '@/src/services/TeamMemberService';
// Methods: getTeamMembers(teamId)
```

**Hooks Used:**
```typescript
// Custom import hook for managing import state
import { useImport } from '@/src/features/assess/hooks/useImport';
```

### Import Service
```typescript
export class ImportService {
  // Import methods for each data type
  async importPlayers(csvData: string, teamId: string): ImportResult;
  async importCoaches(csvData: string, teamId: string): ImportResult;
  async importEvents(csvData: string, teamId: string): ImportResult;
  
  // Utility methods
  async checkDuplicates(data: any[], type: 'player'|'coach'|'event'): DuplicateReport;
  async normalizePhoneNumbers(phones: string[]): string[];
  async parseEventDates(dateStrings: string[]): Date[];
}
```

### Expected CSV Formats

#### Players.csv
```csv
Name,Position,Date of birth,Email,Phone,Guardian 1 Name,Guardian 1 Phone,Guardian 1 Email...
```

#### Events - Fixtures.csv
```csv
Date,Time,Type,Home Team,Away Team,Location,Meet Time
```

#### Events - Training.csv
```csv
Date/Time,Location
"Wednesday 03 September 2025, 18:00 - 19:00","Isleham Recreation Ground"
```

### Import Workflow
1. Upload CSV files
2. Preview and validate data
3. Resolve duplicates
4. Execute import with progress tracking
5. View import report

## Implementation Timeline

### Week 1: Core Event Flow + Import Foundation
1. Create event page
2. Draft and published pages
3. Basic import UI
4. Player import functionality

### Week 2: Session & Evaluation Pages
1. Session attendance page
2. Evaluation pages and forms
3. Event import (fixtures & training)
4. Pre-evaluation system

### Week 3: Sharing & Polish
1. Player/parent evaluation views
2. Review/summary pages
3. Import validation and reports
4. SMS notification integration

### Week 4: Complete Integration
1. End-to-end testing
2. Import Isleham Skylarks U13 data
3. Performance optimization
4. Documentation and training

## Key Simplifications

1. **Single Evaluation Table**: One table instead of pre_evaluations + player_evaluations
2. **Simple Trigger**: One trigger for publish instead of cascade
3. **No Login Pre-Eval**: Token-based access for players
4. **Auto-Save Everything**: No lost work
5. **Mobile First**: Every page works on mobile
6. **Clear Page Purpose**: Each page does one thing well

## Success Metrics

### Performance
- Create event in < 30 seconds
- Complete pre-eval in < 2 minutes
- Evaluate all players in < 15 minutes
- Import 50+ players in < 2 minutes

### User Experience
- Zero lost data (auto-save)
- 3 clicks to any task
- Clear status at all times
- Works perfectly on mobile

### Data Quality
- No duplicate imports
- Accurate phone normalization
- Proper role assignments
- Complete audit trail

## Migration Strategy

1. **Parallel Development**: Build under `/features/assess` completely separate
2. **Feature Flag**: `REACT_APP_USE_ASSESS_EVENTS=true`
3. **Test Teams**: Start with Isleham Skylarks U13
4. **Gradual Rollout**: Team by team migration
5. **Deprecate Old**: Remove old system after full migration

## What We're NOT Building (Phase 1)

- Complex evaluation templates
- Detailed IDP integration
- Historical comparisons
- Advanced reporting
- Video/photo attachments
- Email notifications (SMS only)
- Recurring events

Keep it simple, get it working, then enhance based on feedback.