# MVP Assess System Implementation Plan

## Executive Summary

The Assess System is a complete event and evaluation management platform that replaces the fragmented evaluation functionality across the SHOT app. It provides coaches with a streamlined workflow from event creation through player evaluation, while offering players and parents easy access to feedback via mobile-friendly interfaces.

**MVP Scope**: Full implementation of 9-page event lifecycle system with SMS automation, pre-evaluations, coach evaluations, and data import capabilities for onboarding existing teams.

## Developer Assignments

### DEV1 - Coach Event & Evaluation System
**Focus**: Main event lifecycle, coach evaluation workflow, and team management
- Event creation, draft, publish, session, and evaluation pages
- Coach evaluation interfaces and workflows  
- Core services for events and evaluations
- Event management and status transitions
- SMS automation controls

### DEV2 - Player/Parent Experience
**Focus**: Player and parent views, pre-evaluations, and shared content
- Pre-evaluation forms (no-login required)
- Player evaluation viewing interfaces
- Parent access to child's evaluations
- Share link generation and access
- Mobile-optimized player experiences

### DEV3 - Backend/Pipeline/Data Import
**Focus**: Data import, pipeline configuration, and backend infrastructure
- CSV data import system for existing teams
- Data validation and normalization
- Bulk operations and batch processing
- Database migrations and triggers
- Integration with existing SMS infrastructure
- Performance optimization

**Note**: Authentication and onboarding functionality is handled by the Identity System - see `/docs/MVP Push/mvp-identity-system.md`

## Core User Flows

### 1. Coach Event Creation Flow
```
Create Event (30 sec) → Draft Configuration → Publish Event → 
Monitor Pre-Evals → Run Session → Evaluate Players → Review & Share
```

### 2. Player Pre-Evaluation Flow
```
Receive SMS → Click Link (No Login) → Complete Pre-Eval (2 min) → 
Confirmation → View Results After Event
```

### 3. Parent Access Flow
```
Receive Share Link → View Child's Evaluation → See Progress & Feedback
```

### 4. Data Import Flow (Onboarding)
```
Upload CSV → Preview & Validate → Resolve Duplicates → 
Import → Confirmation Report
```

## MVP Features

### Event Management (DEV1)
- **Create Event Page**: Quick event creation with basic details
- **Draft Page**: Configure players, positions, pre-evaluations, SMS settings
- **Published Page**: Monitor pre-eval completion, send reminders
- **Session Page**: Real-time attendance tracking
- **Evaluate Page**: Post-event player evaluations
- **Review Page**: Summary, reports, and sharing

### Pre-Evaluations (DEV2)
- Token-based access (no login required)
- Mobile-first design
- Auto-save functionality
- Simple 1-10 rating scales
- Completion tracking

### Data Import (DEV3)
- CSV import for players, coaches, events
- Phone number normalization
- Duplicate detection and resolution
- Bulk creation with validation
- Import progress tracking

### SMS Automation (DEV1/DEV2)
- Automatic SMS on event publish (optional)
- Manual SMS trigger option
- Pre-eval reminder system
- Delivery tracking

## Database Requirements

### Core Tables
```sql
-- Simplified single evaluation table
evaluations.event_evaluations
- Combines pre/post/coach evaluations
- Decimal scoring (1.0-10.0)
- Position frozen at creation
- Questions frozen at creation

-- Event configuration
events (extended)
- pre_eval_auto_sms flag
- sms_sent_at timestamp

-- Access tokens
pre_eval_access
- No-login token system
- Expiration handling

share_links
- Player/parent share tokens
- Access tracking
```

### Key Features
- Single evaluation record per player/event/criteria
- Token-based access for pre-evaluations
- Share links for result viewing
- Simplified trigger system

## Implementation Timeline

### Week 1: Foundation
- **DEV1**: Create/Draft/Published pages, core services
- **DEV2**: Pre-eval token system, basic form structure  
- **DEV3**: Database migrations, import infrastructure

### Week 2: Core Features
- **DEV1**: Session/Evaluate pages, attendance tracking
- **DEV2**: Complete pre-eval form, player view page
- **DEV3**: CSV parsing, validation pipelines

### Week 3: Integration & Polish
- **DEV1**: Review page, sharing, SMS integration
- **DEV2**: Mobile optimization, parent views
- **DEV3**: Import Isleham Skylarks data, performance tuning

### Week 4: Testing & Launch
- End-to-end testing
- Performance optimization
- Bug fixes
- Soft launch with test team

## Success Criteria

### Performance Metrics
- Event creation: < 30 seconds
- Pre-evaluation completion: < 2 minutes  
- Full team evaluation: < 15 minutes
- Import 50+ players: < 2 minutes

### User Experience
- Zero data loss (auto-save everywhere)
- Mobile-first (all player interfaces)
- Clear status visibility
- Intuitive navigation (3 clicks to any task)

### Business Metrics
- 90% pre-evaluation completion rate
- 100% coach adoption for evaluations
- Successful import of all test team data

## Integration Points

### With Identity System
- User authentication and profiles
- Team member data
- Role-based permissions

### With Schedule System (Future)
- Event calendar integration
- RSVP functionality

### With Perform System (Future)
- Training session content
- Performance metrics

## Technical Architecture

### Directory Structure
```
src/features/assess/
├── components/
│   ├── shared/      [Shared UI components]
│   ├── create/      [Event creation components]
│   ├── draft/       [Draft configuration]
│   ├── published/   [Pre-event monitoring]
│   ├── session/     [Live event management]
│   ├── evaluation/  [Coach evaluation forms]
│   ├── review/      [Summary and sharing]
│   ├── import/      [Data import UI]
│   ├── player/      [Player/parent views]
│   └── pre-evaluation/ [Pre-eval forms]
├── pages/           [Route-level components]
├── hooks/           [Custom React hooks]
├── services/        [Business logic]
├── routes/          [Route configuration]
└── types/           [TypeScript definitions]
```

### Key Design Decisions
1. **Single Evaluation Table**: Simplifies data model
2. **Token-Based Access**: No login barrier for players
3. **Auto-Save Everything**: Prevents data loss
4. **Mobile-First**: All player interfaces optimized
5. **Feature Isolation**: Complete separation from old system

## Risk Mitigation

### Data Import Risks
- Validation at every step
- Preview before commit
- Rollback capabilities
- Duplicate detection

### SMS Delivery
- Manual backup option
- Delivery tracking
- Rate limiting
- Error notifications

### Performance
- Pagination for large teams
- Optimized queries
- Background processing
- Caching strategies

## Out of MVP Scope

### Not Included in Phase 1
- Complex evaluation templates
- Historical comparisons
- Advanced analytics
- Email notifications
- Recurring events
- Video/photo attachments
- API integrations

### Future Enhancements
- Individual Development Plans (IDP)
- Trend analysis
- Peer evaluations
- Custom evaluation criteria
- Multi-language support

## Deployment Strategy

1. **Feature Flag**: `USE_ASSESS_SYSTEM=true`
2. **Test Team**: Isleham Skylarks U13
3. **Parallel Running**: Old and new systems
4. **Gradual Migration**: Team by team
5. **Full Cutover**: After validation

## Developer Handoff Points

### DEV1 → DEV2
- Share link generation service
- Evaluation data read access
- SMS notification triggers

### DEV1 → DEV3  
- Import page integration
- Event data structure
- Validation requirements

### DEV2 → DEV3
- Player data requirements
- Phone normalization needs
- Token generation specs

## Testing Requirements

### Unit Tests
- Service layer logic
- Data validation
- Token generation

### Integration Tests
- Complete workflows
- SMS delivery
- Data import

### E2E Tests
- Full event lifecycle
- Mobile responsiveness
- Multi-role scenarios

## Documentation Needs

### Coach Training
- Video walkthrough
- Quick start guide
- Best practices

### Developer Docs
- API documentation
- Database schema
- Integration guide

### User Support
- FAQ section
- Troubleshooting guide
- Contact information