# Identity System Implementation Plan - Developer Work Split

## Developer Assignments

### DEV1 - Core Authentication System
**Focus**: Login/logout flows, session management, and authentication infrastructure
- Unified login page supporting email and username authentication
- Session management and token handling
- Password reset and recovery flows
- Authentication service abstraction layer
- Security and permission enforcement

### DEV2 - Family Management System  
**Focus**: Parent-child relationships, account switching, and family features
- Family dashboard and relationship management
- Child account creation without email (<EMAIL> pattern)
- Account switching infrastructure
- Parent approval workflows
- Child-specific profile fields and restrictions

### DEV3 - Profile Management
**Focus**: Profile creation, editing, and SportHead avatar system
- Profile creation and editing interfaces
- SportHead avatar selection and customization
- Profile data validation and storage
- Role-based profile variations
- Profile completion tracking

### DEV4 - Onboarding Flows
**Focus**: First-time user experience for players, parents, and coaches
- Player onboarding from team invitations
- Parent onboarding with child account creation
- Coach onboarding with team setup
- SMS/email verification flows
- Initial password creation for child accounts

---

## Overview

The Identity System consolidates all authentication, user management, and family relationship features into a unified system under `/src/features/identity/`. This system replaces the current fragmented approach with 5+ different login pages and provides a consistent, secure authentication experience for all user types.

Key improvements include:
- Single unified login page replacing multiple implementations
- Proper support for child accounts without email addresses
- Seamless account switching for family members
- Streamlined onboarding for new users joining teams
- Clear separation between authentication (who you are) and authorization (what you can do)

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Page Flow and Purpose](#page-flow-and-purpose)
3. [Component Structure](#component-structure)
4. [Database Design](#database-design)
5. [Service Architecture](#service-architecture)
6. [Implementation Timeline](#implementation-timeline)
7. [Integration Points](#integration-points)
8. [Success Metrics](#success-metrics)

## Architecture Overview

### Directory Structure

```
src/features/identity/
├── components/
│   ├── shared/
│   │   ├── IdentityCard.tsx [DEV2]
│   │   ├── RoleIndicator.tsx [DEV3]
│   │   ├── AvatarSelector.tsx [DEV3]
│   │   └── PermissionGuard.tsx [DEV1]
│   ├── auth/
│   │   ├── LoginForm.tsx [DEV1]
│   │   ├── EmailIdForm.tsx [DEV1]
│   │   ├── PasswordResetForm.tsx [DEV1]
│   │   ├── EmailVerification.tsx [DEV1]
│   │   └── SessionManager.tsx [DEV1]
│   ├── family/
│   │   ├── FamilyDashboard.tsx [DEV2]
│   │   ├── ChildAccountCreator.tsx [DEV2]
│   │   ├── ChildList.tsx [DEV2]
│   │   ├── ParentApprovalFlow.tsx [DEV2]
│   │   └── AccountSwitcher.tsx [DEV2]
│   ├── profile/
│   │   ├── ProfileEditor.tsx [DEV3]
│   │   ├── SportHeadAvatarPicker.tsx [DEV3]
│   │   ├── ProfileCompletionTracker.tsx [DEV3]
│   │   ├── ChildProfileFields.tsx [DEV3]
│   │   └── RoleSpecificFields.tsx [DEV3]
│   └── onboarding/
│       ├── WelcomeScreen.tsx [DEV4]
│       ├── RoleSelector.tsx [DEV4]
│       ├── TeamJoinFlow.tsx [DEV4]
│       ├── ParentChildSetup.tsx [DEV4]
│       ├── CoachVerification.tsx [DEV4]
│       └── OnboardingProgress.tsx [DEV4]
├── pages/
│   ├── LoginPage.tsx [DEV1]
│   ├── PasswordResetPage.tsx [DEV1]
│   ├── EmailVerificationPage.tsx [DEV1]
│   ├── IdentityDashboard.tsx [DEV2]
│   ├── FamilyManagementPage.tsx [DEV2]
│   ├── ProfilePage.tsx [DEV3]
│   ├── PlayerOnboardingPage.tsx [DEV4]
│   ├── ParentOnboardingPage.tsx [DEV4]
│   └── CoachOnboardingPage.tsx [DEV4]
├── hooks/
│   ├── useAuth.ts [DEV1]
│   ├── useSession.ts [DEV1]
│   ├── useAccountSwitching.ts [DEV2]
│   ├── useFamily.ts [DEV2]
│   ├── useProfile.ts [DEV3]
│   └── useOnboarding.ts [DEV4]
├── services/
│   ├── AuthenticationService.ts [DEV1]
│   ├── SessionService.ts [DEV1]
│   ├── FamilyService.ts [DEV2]
│   ├── ProfileService.ts [DEV3]
│   └── OnboardingService.ts [DEV4]
├── routes/
│   └── IdentityRoutes.tsx [DEV1]
└── types/
    ├── auth.types.ts [DEV1]
    ├── family.types.ts [DEV2]
    ├── profile.types.ts [DEV3]
    └── onboarding.types.ts [DEV4]
```

## Page Flow and Purpose

### Complete Identity System Flow

```mermaid
graph TB
    Start([User Arrives]) --> LoginCheck{Authenticated?}
    
    %% Not Authenticated Flow
    LoginCheck --> |No| LoginPage[LoginPage - DEV1]
    LoginPage --> |Forgot Password| PasswordReset[PasswordResetPage - DEV1]
    LoginPage --> |New User| OnboardingEntry{From Invitation?}
    
    %% Onboarding Flows - DEV4
    OnboardingEntry --> |Player Invite| PlayerOnboarding[PlayerOnboardingPage - DEV4]
    OnboardingEntry --> |Parent Invite| ParentOnboarding[ParentOnboardingPage - DEV4]
    OnboardingEntry --> |Coach Invite| CoachOnboarding[CoachOnboardingPage - DEV4]
    OnboardingEntry --> |No Invite| ContactAdmin[Contact Admin Message - DEV4]
    
    PlayerOnboarding --> |Complete| ProfileSetup[Profile Creation - DEV3]
    ParentOnboarding --> |Complete| ChildCreation[Child Account Setup - DEV2]
    CoachOnboarding --> |Verified| TeamAccess[Team Assignment - DEV4]
    
    %% Authenticated Flow
    LoginCheck --> |Yes| IdentityDashboard[IdentityDashboard - DEV2]
    LoginPage --> |Success| IdentityDashboard
    ProfileSetup --> |Complete| IdentityDashboard
    ChildCreation --> |Complete| IdentityDashboard
    TeamAccess --> |Complete| IdentityDashboard
    
    %% Identity Management - DEV2
    IdentityDashboard --> |Switch Account| AccountSwitcher[Account Switcher - DEV2]
    AccountSwitcher --> |Select Child| ChildSession[Child Session Active - DEV2]
    AccountSwitcher --> |Select Parent| ParentSession[Parent Session Active - DEV2]
    
    IdentityDashboard --> |Manage Family| FamilyPage[FamilyManagementPage - DEV2]
    FamilyPage --> |Add Child| ChildAccountCreator[Child Account Creator - DEV2]
    ChildAccountCreator --> |Create| ChildProfileSetup[Child Profile Setup - DEV3]
    
    %% Profile Management - DEV3
    IdentityDashboard --> |Edit Profile| ProfilePage[ProfilePage - DEV3]
    ProfilePage --> |Change Avatar| AvatarPicker[SportHead Avatar Picker - DEV3]
    ProfilePage --> |Update Info| ProfileEditor[Profile Editor - DEV3]
    
    %% Session Management - DEV1
    ChildSession --> |Navigate App| AppWithChildContext[App with Child Context - DEV1]
    ParentSession --> |Navigate App| AppWithParentContext[App with Parent Context - DEV1]
```

### Page Details

#### 1. Login Page [DEV1]

**Purpose**: Unified authentication entry point supporting both email and username login

**Path**: `/src/features/identity/pages/LoginPage.tsx`
**Route**: `/login`

```typescript
/**
 * LoginPage
 * 
 * PURPOSE:
 * - Single authentication page replacing 5+ login variants
 * - Support email login for adults (parents/coaches)
 * - Support username login for children (<NAME_EMAIL>)
 * - Social login options for quick access
 * - Clear path to password reset and onboarding
 * 
 * USER GOALS:
 * - Login quickly with email or username
 * - Access password reset if needed
 * - See clear onboarding path if new
 * - Switch between family accounts easily
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowInput } from '@/foundation/design-system/components/atoms/Input';
import { ShadowToast } from '@/foundation/design-system/components/atoms/Toast';

// From existing auth components
import { EmailIdForm } from '@/src/components/EmailIdForm';
// Path: /src/components/EmailIdForm.tsx (to be moved)
```

**New Components Required:**
```typescript
// Unified login form supporting email/username
import { LoginForm } from '@/src/features/identity/components/auth/LoginForm';
// Path: /src/features/identity/components/auth/LoginForm.tsx

// Social login buttons
import { SocialLoginOptions } from '@/src/features/identity/components/auth/SocialLoginOptions';
// Path: /src/features/identity/components/auth/SocialLoginOptions.tsx

// Remember me and account switcher quick access
import { LoginQuickActions } from '@/src/features/identity/components/auth/LoginQuickActions';
// Path: /src/features/identity/components/auth/LoginQuickActions.tsx
```

**Services Used:**
```typescript
import { AuthenticationService } from '@/src/features/identity/services/AuthenticationService';
// Methods: 
// - login(credentials: {email?: string, username?: string, password: string}): Promise<User>
// - logout(): Promise<void>
// - checkSession(): Promise<User | null>
```

**Hooks Used:**
```typescript
import { useAuth } from '@/src/features/identity/hooks/useAuth';
// Returns: { user, login, logout, loading, error }
```

#### 2. Identity Dashboard [DEV2]

**Purpose**: Central hub for identity management showing current user and all linked accounts

**Path**: `/src/features/identity/pages/IdentityDashboard.tsx`
**Route**: `/identity`

```typescript
/**
 * IdentityDashboard
 * 
 * PURPOSE:
 * - Display current active identity (parent or child)
 * - Show all linked family accounts with quick switch
 * - Provide access to family management features
 * - Display identity-specific permissions and access
 * - Quick profile editing and avatar changes
 * 
 * USER GOALS:
 * - See who I'm currently logged in as
 * - Switch between my children's accounts easily
 * - Add new family members
 * - Manage my family's sports profiles
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowCard } from '@/foundation/design-system/components/molecules/Cards';
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowAvatar } from '@/foundation/design-system/components/atoms/Avatar';

// From existing components
import { ShadowAccountSwitcher } from '@/src/components/shadow/ShadowAccountSwitcher';
// Path: /src/components/shadow/ShadowAccountSwitcher.tsx (to be enhanced)
```

**New Components Required:**
```typescript
// Current identity display card
import { CurrentIdentityCard } from '@/src/features/identity/components/shared/CurrentIdentityCard';
// Path: /src/features/identity/components/shared/CurrentIdentityCard.tsx

// Family members grid
import { FamilyMembersGrid } from '@/src/features/identity/components/family/FamilyMembersGrid';
// Path: /src/features/identity/components/family/FamilyMembersGrid.tsx

// Quick actions for identity management
import { IdentityQuickActions } from '@/src/features/identity/components/shared/IdentityQuickActions';
// Path: /src/features/identity/components/shared/IdentityQuickActions.tsx

// Permission summary for current identity
import { IdentityPermissions } from '@/src/features/identity/components/shared/IdentityPermissions';
// Path: /src/features/identity/components/shared/IdentityPermissions.tsx
```

**Services Used:**
```typescript
import { FamilyService } from '@/src/features/identity/services/FamilyService';
// Methods: 
// - getFamilyMembers(parentId: string): Promise<Profile[]>
// - createChildAccount(parentId: string, childData: ChildAccountData): Promise<Profile>
// - switchAccount(targetProfileId: string): Promise<void>

import { SessionService } from '@/src/features/identity/services/SessionService';
// Methods:
// - getCurrentIdentity(): Profile
// - setActiveIdentity(profileId: string): void
// - getIdentityPermissions(profileId: string): Permissions
```

**Hooks Used:**
```typescript
import { useAccountSwitching } from '@/src/features/identity/hooks/useAccountSwitching';
// Returns: { currentAccount, availableAccounts, switchAccount, isParentAccount }

import { useFamily } from '@/src/features/identity/hooks/useFamily';
// Returns: { familyMembers, addChild, removeChild, updateChild }
```

#### 3. Family Management Page [DEV2]

**Purpose**: Comprehensive family account management for parents

**Path**: `/src/features/identity/pages/FamilyManagementPage.tsx`
**Route**: `/identity/family`

```typescript
/**
 * FamilyManagementPage
 * 
 * PURPOSE:
 * - Add new child accounts without email
 * - Edit existing child profiles
 * - Manage child permissions and access
 * - Set up emergency contacts and medical info
 * - Remove or deactivate child accounts
 * 
 * USER GOALS:
 * - Create accounts for all my children
 * - Keep their information up to date
 * - Control what they can access
 * - Ensure safety information is current
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowModal } from '@/foundation/design-system/components/molecules/Modals';
import { ShadowForm } from '@/foundation/design-system/components/molecules/Forms';
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';

// From old system
import PageWithNavigation from '@/src/components/PageWithNavigation';
```

**New Components Required:**
```typescript
// Child account creation form
import { ChildAccountCreator } from '@/src/features/identity/components/family/ChildAccountCreator';
// Path: /src/features/identity/components/family/ChildAccountCreator.tsx

// Child list with management options
import { ChildList } from '@/src/features/identity/components/family/ChildList';
// Path: /src/features/identity/components/family/ChildList.tsx

// Child profile editor modal
import { ChildProfileEditor } from '@/src/features/identity/components/family/ChildProfileEditor';
// Path: /src/features/identity/components/family/ChildProfileEditor.tsx

// Emergency contact manager
import { EmergencyContactManager } from '@/src/features/identity/components/family/EmergencyContactManager';
// Path: /src/features/identity/components/family/EmergencyContactManager.tsx
```

**Services Used:**
```typescript
import { FamilyService } from '@/src/features/identity/services/FamilyService';
// Methods: 
// - createChildAccount(parentId: string, childData: ChildAccountData): Promise<Profile>
// - updateChildProfile(childId: string, updates: Partial<Profile>): Promise<Profile>
// - deleteChildAccount(childId: string): Promise<void>
// - generateChildUsername(fullName: string): Promise<string>
```

**Hooks Used:**
```typescript
import { useFamily } from '@/src/features/identity/hooks/useFamily';
// Returns: { familyMembers, addChild, removeChild, updateChild, loading, error }

import { useChildManagement } from '@/src/hooks/useChildManagement';
// Returns: { createChild, updateChild, deleteChild } (to be moved)
```

#### 4. Profile Page [DEV3]

**Purpose**: Comprehensive profile editing with role-specific fields

**Path**: `/src/features/identity/pages/ProfilePage.tsx`
**Route**: `/identity/profile/:profileId?`

```typescript
/**
 * ProfilePage
 * 
 * PURPOSE:
 * - Edit profile information based on role
 * - Select and customize SportHead avatar
 * - Update contact and emergency information
 * - Manage sport-specific preferences
 * - Track profile completion status
 * 
 * USER GOALS:
 * - Keep my information current
 * - Choose an avatar that represents me
 * - Ensure coaches have my contact info
 * - Complete my profile for team access
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowAvatar } from '@/foundation/design-system/components/atoms/Avatar';
import { ShadowForm } from '@/foundation/design-system/components/molecules/Forms';
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowProgressBar } from '@/foundation/design-system/components/atoms/ProgressBar';

// From old system
import PageWithNavigation from '@/src/components/PageWithNavigation';
```

**New Components Required:**
```typescript
// Main profile editing form
import { ProfileEditor } from '@/src/features/identity/components/profile/ProfileEditor';
// Path: /src/features/identity/components/profile/ProfileEditor.tsx

// SportHead avatar selection modal
import { SportHeadAvatarPicker } from '@/src/features/identity/components/profile/SportHeadAvatarPicker';
// Path: /src/features/identity/components/profile/SportHeadAvatarPicker.tsx

// Profile completion tracking
import { ProfileCompletionTracker } from '@/src/features/identity/components/profile/ProfileCompletionTracker';
// Path: /src/features/identity/components/profile/ProfileCompletionTracker.tsx

// Role-specific field groups
import { RoleSpecificFields } from '@/src/features/identity/components/profile/RoleSpecificFields';
// Path: /src/features/identity/components/profile/RoleSpecificFields.tsx

// Child-specific fields (allergies, medical)
import { ChildProfileFields } from '@/src/features/identity/components/profile/ChildProfileFields';
// Path: /src/features/identity/components/profile/ChildProfileFields.tsx
```

**Services Used:**
```typescript
import { ProfileService } from '@/src/features/identity/services/ProfileService';
// Methods: 
// - getProfile(profileId: string): Promise<Profile>
// - updateProfile(profileId: string, updates: Partial<Profile>): Promise<Profile>
// - uploadAvatar(profileId: string, avatarData: AvatarData): Promise<string>
// - getProfileCompleteness(profile: Profile): ProfileCompleteness
```

**Hooks Used:**
```typescript
import { useProfile } from '@/src/features/identity/hooks/useProfile';
// Returns: { profile, updateProfile, uploadAvatar, completeness, loading, error }
```

#### 5. Player Onboarding Page [DEV4]

**Purpose**: Streamlined onboarding for players joining via team invitation

**Path**: `/src/features/identity/pages/PlayerOnboardingPage.tsx`
**Route**: `/onboard/player/:inviteToken`

```typescript
/**
 * PlayerOnboardingPage
 * 
 * PURPOSE:
 * - Create player account from team invitation
 * - Set up basic profile with avatar
 * - Establish parent connection if minor
 * - Join assigned team automatically
 * - Create initial password
 * 
 * USER GOALS:
 * - Join my team quickly
 * - Choose a cool avatar
 * - Connect with my parent if needed
 * - Start using the app immediately
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowWizard } from '@/foundation/design-system/components/molecules/Wizard';
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
import { ShadowAvatar } from '@/foundation/design-system/components/atoms/Avatar';
```

**New Components Required:**
```typescript
// Welcome screen with team info
import { WelcomeScreen } from '@/src/features/identity/components/onboarding/WelcomeScreen';
// Path: /src/features/identity/components/onboarding/WelcomeScreen.tsx

// Basic profile setup
import { PlayerProfileSetup } from '@/src/features/identity/components/onboarding/PlayerProfileSetup';
// Path: /src/features/identity/components/onboarding/PlayerProfileSetup.tsx

// Avatar selection step
import { OnboardingAvatarPicker } from '@/src/features/identity/components/onboarding/OnboardingAvatarPicker';
// Path: /src/features/identity/components/onboarding/OnboardingAvatarPicker.tsx

// Parent connection for minors
import { ParentConnectionStep } from '@/src/features/identity/components/onboarding/ParentConnectionStep';
// Path: /src/features/identity/components/onboarding/ParentConnectionStep.tsx

// Password creation
import { InitialPasswordSetup } from '@/src/features/identity/components/onboarding/InitialPasswordSetup';
// Path: /src/features/identity/components/onboarding/InitialPasswordSetup.tsx
```

**Services Used:**
```typescript
import { OnboardingService } from '@/src/features/identity/services/OnboardingService';
// Methods: 
// - validateInviteToken(token: string): Promise<InviteData>
// - createPlayerFromInvite(inviteData: InviteData, profileData: ProfileData): Promise<Profile>
// - joinTeam(profileId: string, teamId: string): Promise<void>
// - requestParentConnection(childEmail: string, parentEmail: string): Promise<void>
```

**Hooks Used:**
```typescript
import { useOnboarding } from '@/src/features/identity/hooks/useOnboarding';
// Returns: { inviteData, currentStep, nextStep, previousStep, completeOnboarding }
```

#### 6. Parent Onboarding Page [DEV4]

**Purpose**: Parent account creation with immediate child account setup

**Path**: `/src/features/identity/pages/ParentOnboardingPage.tsx`
**Route**: `/onboard/parent/:inviteToken`

```typescript
/**
 * ParentOnboardingPage
 * 
 * PURPOSE:
 * - Create parent account from team invitation
 * - Immediately create child account(s)
 * - Set up family relationships
 * - Configure emergency contacts
 * - Join children to team
 * 
 * USER GOALS:
 * - Get my child set up for their team
 * - Create accounts for all my children
 * - Provide necessary safety information
 * - Manage everything from one account
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowWizard } from '@/foundation/design-system/components/molecules/Wizard';
import { ShadowForm } from '@/foundation/design-system/components/molecules/Forms';
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
```

**New Components Required:**
```typescript
// Parent account creation
import { ParentAccountSetup } from '@/src/features/identity/components/onboarding/ParentAccountSetup';
// Path: /src/features/identity/components/onboarding/ParentAccountSetup.tsx

// Child account creation during onboarding
import { ParentChildSetup } from '@/src/features/identity/components/onboarding/ParentChildSetup';
// Path: /src/features/identity/components/onboarding/ParentChildSetup.tsx

// Multiple children support
import { MultiChildCreator } from '@/src/features/identity/components/onboarding/MultiChildCreator';
// Path: /src/features/identity/components/onboarding/MultiChildCreator.tsx

// Family summary before completion
import { FamilySummaryStep } from '@/src/features/identity/components/onboarding/FamilySummaryStep';
// Path: /src/features/identity/components/onboarding/FamilySummaryStep.tsx
```

**Services Used:**
```typescript
import { OnboardingService } from '@/src/features/identity/services/OnboardingService';
// Methods: 
// - createParentFromInvite(inviteData: InviteData, parentData: ParentData): Promise<Profile>
// - createChildrenDuringOnboarding(parentId: string, children: ChildData[]): Promise<Profile[]>
// - assignChildrenToTeam(childIds: string[], teamId: string): Promise<void>
```

**Hooks Used:**
```typescript
import { useOnboarding } from '@/src/features/identity/hooks/useOnboarding';
// Returns: { inviteData, currentStep, nextStep, previousStep, completeOnboarding }

import { useFamily } from '@/src/features/identity/hooks/useFamily';
// Returns: { addChild, familyMembers }
```

#### 7. Coach Onboarding Page [DEV4]

**Purpose**: Coach verification and team access setup

**Path**: `/src/features/identity/pages/CoachOnboardingPage.tsx`
**Route**: `/onboard/coach/:inviteToken`

```typescript
/**
 * CoachOnboardingPage
 * 
 * PURPOSE:
 * - Verify coach identity and credentials
 * - Create coach account with proper permissions
 * - Grant access to assigned teams
 * - Set up coaching preferences
 * - Configure notification settings
 * 
 * USER GOALS:
 * - Get access to my team quickly
 * - Verify my coaching credentials
 * - Set up my coaching profile
 * - Configure how I want to be notified
 */
```

**Existing Components Used:**
```typescript
// From design system
import { ShadowWizard } from '@/foundation/design-system/components/molecules/Wizard';
import { ShadowForm } from '@/foundation/design-system/components/molecules/Forms';
import { ShadowButton } from '@/foundation/design-system/components/atoms/Button';
```

**New Components Required:**
```typescript
// Coach verification step
import { CoachVerification } from '@/src/features/identity/components/onboarding/CoachVerification';
// Path: /src/features/identity/components/onboarding/CoachVerification.tsx

// Coaching profile setup
import { CoachProfileSetup } from '@/src/features/identity/components/onboarding/CoachProfileSetup';
// Path: /src/features/identity/components/onboarding/CoachProfileSetup.tsx

// Team access confirmation
import { TeamAccessConfirmation } from '@/src/features/identity/components/onboarding/TeamAccessConfirmation';
// Path: /src/features/identity/components/onboarding/TeamAccessConfirmation.tsx

// Notification preferences
import { CoachNotificationSetup } from '@/src/features/identity/components/onboarding/CoachNotificationSetup';
// Path: /src/features/identity/components/onboarding/CoachNotificationSetup.tsx
```

**Services Used:**
```typescript
import { OnboardingService } from '@/src/features/identity/services/OnboardingService';
// Methods: 
// - validateCoachInvite(token: string): Promise<CoachInviteData>
// - createCoachAccount(inviteData: CoachInviteData, coachData: CoachData): Promise<Profile>
// - verifyCoachCredentials(credentials: CoachCredentials): Promise<boolean>
// - grantTeamAccess(coachId: string, teamIds: string[]): Promise<void>
```

**Hooks Used:**
```typescript
import { useOnboarding } from '@/src/features/identity/hooks/useOnboarding';
// Returns: { inviteData, currentStep, nextStep, previousStep, completeOnboarding }
```

## Route Configuration

```typescript
// /src/features/identity/routes/IdentityRoutes.tsx [DEV1]
export const IdentityRoutes = () => (
  <Switch>
    {/* Public routes */}
    <Route path="/login" component={LoginPage} />
    <Route path="/password-reset" component={PasswordResetPage} />
    <Route path="/verify-email/:token" component={EmailVerificationPage} />
    
    {/* Onboarding routes [DEV4] */}
    <Route path="/onboard/player/:inviteToken" component={PlayerOnboardingPage} />
    <Route path="/onboard/parent/:inviteToken" component={ParentOnboardingPage} />
    <Route path="/onboard/coach/:inviteToken" component={CoachOnboardingPage} />
    
    {/* Authenticated routes */}
    <ProtectedRoute exact path="/identity" component={IdentityDashboard} />
    <ProtectedRoute path="/identity/family" component={FamilyManagementPage} />
    <ProtectedRoute path="/identity/profile/:profileId?" component={ProfilePage} />
    
    {/* Legacy redirects */}
    <Redirect from="/UpdatedLoginV2" to="/login" />
    <Redirect from="/UpdatedLogin" to="/login" />
    <Redirect from="/Login" to="/login" />
    <Redirect from="/LoginUpdated" to="/login" />
    <Redirect from="/CapLogin" to="/login" />
  </Switch>
);
```

## Database Design

### Authentication Tables [DEV1]

```sql
-- Session management for account switching
CREATE TABLE identity.user_sessions (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid NOT NULL REFERENCES auth.users(id),
    active_profile_id uuid NOT NULL REFERENCES public.profiles(id),
    session_token text UNIQUE NOT NULL,
    device_info jsonb,
    last_activity timestamp with time zone DEFAULT now(),
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);

-- Index for fast session lookups
CREATE INDEX idx_user_sessions_token ON identity.user_sessions(session_token);
CREATE INDEX idx_user_sessions_user_id ON identity.user_sessions(user_id);

-- Password reset tokens
CREATE TABLE identity.password_reset_tokens (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid NOT NULL REFERENCES auth.users(id),
    token text UNIQUE NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    used_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now()
);
```

### Family Relationship Tables [DEV2]

```sql
-- Enhanced family relationships
CREATE TABLE identity.family_relationships (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    parent_profile_id uuid NOT NULL REFERENCES public.profiles(id),
    child_profile_id uuid NOT NULL REFERENCES public.profiles(id),
    relationship_type text NOT NULL CHECK (relationship_type IN ('parent', 'guardian', 'emergency_contact')),
    is_primary boolean DEFAULT false,
    permissions jsonb DEFAULT '{"can_view": true, "can_edit": true, "can_approve": true}'::jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    CONSTRAINT unique_parent_child UNIQUE (parent_profile_id, child_profile_id)
);

-- Child account metadata
CREATE TABLE identity.child_accounts (
    profile_id uuid PRIMARY KEY REFERENCES public.profiles(id),
    generated_username text UNIQUE NOT NULL,
    generated_email text UNIQUE NOT NULL CHECK (generated_email LIKE '%@shot.io'),
    password_set_by text CHECK (password_set_by IN ('parent', 'child', 'system')),
    password_last_changed timestamp with time zone,
    account_restrictions jsonb DEFAULT '{"can_change_password": false, "can_delete_account": false}'::jsonb,
    created_at timestamp with time zone DEFAULT now()
);
```

### Profile Enhancement Tables [DEV3]

```sql
-- SportHead avatar configuration
CREATE TABLE identity.avatar_configs (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    profile_id uuid NOT NULL REFERENCES public.profiles(id),
    sport_head_id text NOT NULL,
    customization jsonb DEFAULT '{}'::jsonb, -- color schemes, accessories, etc.
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Profile completion tracking
CREATE TABLE identity.profile_completion (
    profile_id uuid PRIMARY KEY REFERENCES public.profiles(id),
    basic_info_complete boolean DEFAULT false,
    avatar_selected boolean DEFAULT false,
    contact_info_complete boolean DEFAULT false,
    emergency_info_complete boolean DEFAULT false,
    sport_preferences_complete boolean DEFAULT false,
    team_joined boolean DEFAULT false,
    completion_percentage integer DEFAULT 0,
    last_updated timestamp with time zone DEFAULT now()
);
```

### Onboarding Tables [DEV4]

```sql
-- Onboarding invitations
CREATE TABLE identity.onboarding_invitations (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    token text UNIQUE NOT NULL,
    invite_type text NOT NULL CHECK (invite_type IN ('player', 'parent', 'coach')),
    team_id uuid REFERENCES public.teams(id),
    email text,
    phone text,
    player_name text, -- For parent invites
    coach_name text, -- For coach invites
    metadata jsonb DEFAULT '{}'::jsonb,
    sent_at timestamp with time zone DEFAULT now(),
    expires_at timestamp with time zone NOT NULL,
    accepted_at timestamp with time zone,
    created_by uuid REFERENCES public.profiles(id)
);

-- Onboarding progress tracking
CREATE TABLE identity.onboarding_progress (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id),
    invite_token text REFERENCES identity.onboarding_invitations(token),
    current_step text NOT NULL,
    completed_steps jsonb DEFAULT '[]'::jsonb,
    onboarding_data jsonb DEFAULT '{}'::jsonb,
    started_at timestamp with time zone DEFAULT now(),
    completed_at timestamp with time zone
);
```

## Service Architecture

### AuthenticationService [DEV1]

```typescript
// /src/features/identity/services/AuthenticationService.ts

export interface AuthenticationService {
  // Core authentication
  login(credentials: LoginCredentials): Promise<AuthResult>;
  logout(): Promise<void>;
  refreshToken(): Promise<string>;
  
  // Session management
  getCurrentUser(): Promise<User | null>;
  switchProfile(profileId: string): Promise<void>;
  
  // Password management
  resetPassword(email: string): Promise<void>;
  confirmPasswordReset(token: string, newPassword: string): Promise<void>;
  changePassword(oldPassword: string, newPassword: string): Promise<void>;
  
  // Email verification
  verifyEmail(token: string): Promise<void>;
  resendVerificationEmail(): Promise<void>;
  
  // Child account helpers
  generateChildEmail(username: string): string;
  validateUsername(username: string): Promise<boolean>;
}

interface LoginCredentials {
  identifier: string; // email or username
  password: string;
  rememberMe?: boolean;
}
```

### FamilyService [DEV2]

```typescript
// /src/features/identity/services/FamilyService.ts

export interface FamilyService {
  // Family management
  getFamilyMembers(parentId: string): Promise<FamilyMember[]>;
  addChildAccount(parentId: string, childData: CreateChildData): Promise<Profile>;
  updateChildAccount(childId: string, updates: UpdateChildData): Promise<Profile>;
  removeChildAccount(parentId: string, childId: string): Promise<void>;
  
  // Relationship management
  updateRelationship(parentId: string, childId: string, relationship: RelationshipUpdate): Promise<void>;
  getRelationshipPermissions(parentId: string, childId: string): Promise<Permissions>;
  
  // Account switching
  getAvailableAccounts(userId: string): Promise<SwitchableAccount[]>;
  canSwitchToAccount(fromProfileId: string, toProfileId: string): Promise<boolean>;
  
  // Child account utilities
  generateUniqueUsername(fullName: string): Promise<string>;
  checkUsernameAvailability(username: string): Promise<boolean>;
  
  // Emergency contacts
  updateEmergencyContacts(childId: string, contacts: EmergencyContact[]): Promise<void>;
}

interface CreateChildData {
  fullName: string;
  dateOfBirth: string;
  username?: string; // Auto-generated if not provided
  password: string;
  teamId?: string;
  avatar?: AvatarData;
  medicalInfo?: MedicalInfo;
}
```

### ProfileService [DEV3]

```typescript
// /src/features/identity/services/ProfileService.ts

export interface ProfileService {
  // Profile management
  getProfile(profileId: string): Promise<Profile>;
  updateProfile(profileId: string, updates: ProfileUpdate): Promise<Profile>;
  deleteProfile(profileId: string): Promise<void>;
  
  // Avatar management
  getAvailableAvatars(sport?: string): Promise<Avatar[]>;
  selectAvatar(profileId: string, avatarId: string): Promise<void>;
  customizeAvatar(profileId: string, customization: AvatarCustomization): Promise<void>;
  
  // Profile completion
  getProfileCompleteness(profileId: string): Promise<ProfileCompleteness>;
  getRequiredFields(role: UserRole): Promise<RequiredField[]>;
  validateProfileData(data: ProfileData, role: UserRole): Promise<ValidationResult>;
  
  // Role-specific operations
  getCoachingCredentials(coachId: string): Promise<CoachingCredentials>;
  updatePlayerPreferences(playerId: string, preferences: PlayerPreferences): Promise<void>;
  getParentPermissions(parentId: string): Promise<ParentPermissions>;
}

interface ProfileCompleteness {
  percentage: number;
  missingFields: string[];
  completedSections: string[];
  nextSteps: string[];
}
```

### OnboardingService [DEV4]

```typescript
// /src/features/identity/services/OnboardingService.ts

export interface OnboardingService {
  // Invitation validation
  validateInviteToken(token: string): Promise<InviteValidation>;
  getInviteDetails(token: string): Promise<InviteDetails>;
  
  // Account creation
  createPlayerFromInvite(token: string, playerData: PlayerOnboardingData): Promise<OnboardingResult>;
  createParentFromInvite(token: string, parentData: ParentOnboardingData): Promise<OnboardingResult>;
  createCoachFromInvite(token: string, coachData: CoachOnboardingData): Promise<OnboardingResult>;
  
  // Progress tracking
  saveOnboardingProgress(userId: string, step: string, data: any): Promise<void>;
  getOnboardingProgress(userId: string): Promise<OnboardingProgress>;
  completeOnboarding(userId: string): Promise<void>;
  
  // Team joining
  joinTeamFromInvite(profileId: string, teamId: string, role: TeamRole): Promise<void>;
  
  // Parent-child linking
  linkChildToParent(childId: string, parentEmail: string): Promise<void>;
  approveParentLink(parentId: string, childId: string): Promise<void>;
}

interface OnboardingResult {
  success: boolean;
  profile: Profile;
  team?: Team;
  nextSteps: string[];
}
```

## Implementation Timeline

### Phase 1: Foundation & Authentication (Week 1)

**Goal**: Basic authentication working with new identity system structure

**DEV1**: 
- Create identity directory structure
- Implement AuthenticationService
- Build unified LoginPage
- Set up route configuration
- Create session management infrastructure

**DEV2**:
- Design family relationship schema
- Create FamilyService interface
- Build account switching hook
- Set up identity context

**DEV3**:
- Design profile completion schema
- Create ProfileService interface
- Define avatar system structure
- Build profile type definitions

**DEV4**:
- Design onboarding invitation system
- Create OnboardingService interface
- Define onboarding flow states
- Build invitation validation

### Phase 2: Core Identity Features (Week 2)

**Goal**: Family management and profile editing functional

**DEV1**:
- Implement password reset flow
- Add email verification
- Create permission guards
- Build authentication hooks

**DEV2**:
- Build IdentityDashboard
- Implement FamilyManagementPage
- Create child account creation flow
- Build account switcher component

**DEV3**:
- Implement ProfilePage
- Build avatar selection system
- Create profile editor components
- Add role-specific fields

**DEV4**:
- Build onboarding page shells
- Create welcome screens
- Implement progress tracking
- Design onboarding wizards

### Phase 3: Integration & Polish (Week 3)

**Goal**: Complete onboarding flows and identity integration

**DEV1**:
- Integrate with existing auth system
- Migrate old login pages
- Test permission enforcement
- Handle edge cases

**DEV2**:
- Test family relationships
- Implement emergency contacts
- Polish account switching UX
- Add family notifications

**DEV3**:
- Complete SportHead integration
- Add profile validation
- Test completion tracking
- Polish avatar customization

**DEV4**:
- Complete all onboarding flows
- Test invitation system
- Implement team joining
- Add parent-child linking

### Phase 4: Testing & Migration (Week 4)

**Goal**: Production-ready identity system

**All Developers**:
- Integration testing between systems
- Performance optimization
- Security audit
- Data migration scripts
- Documentation
- Production deployment

## Integration Points

### DEV1 ↔ DEV2
- Session management for account switching (DEV1 provides session, DEV2 uses for switching)
- Authentication state for family features (DEV1 manages auth, DEV2 checks permissions)
- Child account email generation (shared logic)

### DEV1 ↔ DEV4
- Account creation during onboarding (DEV4 collects data, DEV1 creates accounts)
- Initial password setup (DEV4 UI, DEV1 backend)
- Email verification during onboarding (shared flow)

### DEV2 ↔ DEV3
- Family member profile editing (DEV2 manages relationships, DEV3 handles profiles)
- Child-specific profile fields (DEV2 identifies children, DEV3 shows appropriate fields)
- Avatar selection for family members (shared components)

### DEV2 ↔ DEV4
- Parent account creation with children (DEV4 onboards, DEV2 creates relationships)
- Family setup during onboarding (DEV4 collects, DEV2 processes)

### DEV3 ↔ DEV4
- Profile creation during onboarding (DEV4 flow, DEV3 profile logic)
- Avatar selection in onboarding (DEV3 component in DEV4 flow)
- Profile completion requirements (DEV3 defines, DEV4 enforces)

## Key Design Decisions

1. **Username-based child authentication**: Children cannot login with email addresses. They must use username/password, and the system automatically <NAME_EMAIL> email format for Supabase auth
2. **Session-based account switching**: Use sessionStorage to persist active identity across navigation
3. **Unified login page**: Single entry point replacing 5+ login variants for consistency
4. **Token-based onboarding**: Secure invitation system for controlled user registration
5. **Profile completion tracking**: Encourage complete profiles for better team management
6. **No direct child registration**: Children cannot self-register - accounts must be created by parents through the family management system

## Success Metrics

### Performance
- Login completion in < 3 seconds [DEV1]
- Account switching in < 1 second [DEV2]
- Profile save in < 2 seconds [DEV3]
- Onboarding completion in < 5 minutes [DEV4]

### User Experience
- Single login page for all users [DEV1]
- 2-click account switching [DEV2]
- 100% profile completion rate increase [DEV3]
- 90%+ onboarding completion rate [DEV4]

### Data Quality
- Zero duplicate child accounts [DEV2]
- 100% valid username generation [DEV2]
- Complete emergency contact info [DEV3]
- Accurate team assignments [DEV4]

### Business Metrics
- 50% reduction in login support tickets
- 80% of parents create child accounts
- 95% of players complete profiles
- 100% of coaches verified before access

## Migration Strategy

1. **Parallel Development**: Build under `/features/identity/` without affecting current auth
2. **Feature Flag**: `REACT_APP_USE_UNIFIED_LOGIN=true` for gradual rollout
3. **Pilot Testing**: Start with new team onboarding only
4. **Gradual Migration**: Redirect old login pages one at a time
5. **Data Migration**: Run scripts to update existing accounts
6. **Full Cutover**: Remove old auth code after validation

## Security Considerations

1. **Authentication**: Supabase Auth with proper session management
2. **Authorization**: Role-based access with profile-level permissions
3. **Data Protection**: Child data restricted to parents and coaches only
4. **Input Validation**: Username uniqueness, email format, age verification
5. **Audit Logging**: Track all account switches and profile changes

## Out of Scope (Phase 1)

- Social login providers (Google, Facebook, etc.)
- Two-factor authentication
- Advanced parental controls
- Bulk family import
- Profile sharing outside family
- Custom avatar uploads
- API key management
- SSO integration

## Appendix

### Glossary

- **Identity**: A user profile that can be authenticated and authorized
- **Account Switching**: Ability for parents to access child profiles
- **SportHead**: Custom avatar system for player profiles
- **Generated Email**: System-created email (<EMAIL>) for children
- **Profile Completeness**: Percentage of required fields filled

### References

#### Related Documents
- Current Implementation Analysis: `/docs/implementation-plans/06-identity-implementation-plan.md`
- Assess System Integration: `/docs/assess-system-complete-plan-dev-split.md`

#### Existing Code
- UpdatedLoginV2: `/src/pages/UpdatedLoginV2.tsx` (base for new login)
- useAccountSwitching: `/src/hooks/useAccountSwitching.ts` (to be moved)
- ShadowAccountSwitcher: `/src/components/shadow/ShadowAccountSwitcher.tsx`

---

## Document Metadata

**Last Updated**: 2025-08-16
**Status**: Draft
**Feature Owner**: Identity Team
**Technical Lead**: TBD
**Document Version**: 1.0