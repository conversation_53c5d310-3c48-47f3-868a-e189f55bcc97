# SMS Notification System Documentation

## Overview

The SHOT app SMS notification system allows coaches to send pre-evaluation reminder messages to players via SMS. The system uses a database-triggered approach combined with a Supabase Edge Function that integrates with <PERSON><PERSON><PERSON> for actual SMS delivery.

## Architecture Flow

```mermaid
graph TD
    A[Coach clicks Request Pre-Evaluations] --> B[create_basic_pre_evaluations RPC]
    B --> C[Creates pre_evaluations records]
    C --> D[create_pre_evaluation_notifications RPC]
    D --> E[Inserts into pre_evaluation_notifications table]
    E --> F[Database Trigger fires]
    F --> G[trigger_send_notification_sms function]
    G --> H[HTTP POST to Edge Function]
    H --> I[send-sms-v2 Edge Function]
    I --> J[Twilio API]
    J --> K[SMS sent to player]
    I --> L[Updates notification status]
```

## Database Schema

### pre_evaluation_notifications Table

This is the primary table for managing all notifications (SMS, email, push):

```sql
CREATE TABLE pre_evaluation_notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    pre_evaluation_id UUID REFERENCES pre_evaluations(id),
    notification_type VA<PERSON>HAR DEFAULT 'reminder',
    channel VARCHAR NOT NULL, -- 'sms', 'email', or 'push'
    recipient_id UUID REFERENCES profiles(id),
    recipient_phone VARCHAR,
    recipient_email VARCHAR,
    status VARCHAR DEFAULT 'pending', -- 'pending', 'sent', 'failed'
    body TEXT,
    sent_at TIMESTAMPTZ,
    delivered_at TIMESTAMPTZ,
    read_at TIMESTAMPTZ,
    failed_at TIMESTAMPTZ,
    failure_reason TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### sms_queue Table (Legacy/Backup)

An alternative table that can be used for direct SMS sending:

```sql
CREATE TABLE sms_queue (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    phone_number TEXT NOT NULL,
    recipient_name TEXT,
    message_body TEXT NOT NULL,
    status VARCHAR DEFAULT 'pending',
    event_id UUID REFERENCES events(id),
    pre_evaluation_id UUID REFERENCES pre_evaluations(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    sent_at TIMESTAMPTZ,
    retry_count INTEGER DEFAULT 0,
    error_message TEXT
);
```

### debug_logs Table (SMS Logging)

This table captures all SMS notification activity for debugging and monitoring:

```sql
CREATE TABLE debug_logs (
    id INTEGER GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
    function_name TEXT,
    message TEXT,
    data JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Key Log Types:**
- **Trigger Activation**: When SMS notifications are initiated
- **HTTP Requests**: Calls to Supabase Edge Functions  
- **API Responses**: Status codes and content from Twilio
- **Errors**: Exception messages and SQL errors
- **Status Updates**: Success/failure states

**Example Log Entries:**
```sql
-- Trigger activation log
INSERT INTO debug_logs (function_name, message, data)
VALUES ('trigger_send_notification_sms', 'Trigger activated for SMS notification', {
  "notification_id": "uuid",
  "pre_evaluation_id": "uuid", 
  "recipient_phone": "+************",
  "timestamp": "2025-01-15T10:30:00Z"
});

-- HTTP response log
INSERT INTO debug_logs (function_name, message, data) 
VALUES ('trigger_send_notification_sms', 'Edge function response: 200', {
  "notification_id": "uuid",
  "status_code": 200,
  "response": "SMS sent successfully",
  "timestamp": "2025-01-15T10:30:05Z"
});

-- Error log
INSERT INTO debug_logs (function_name, message, data)
VALUES ('trigger_send_notification_sms', 'Error calling edge function', {
  "notification_id": "uuid", 
  "error": "connection timeout",
  "timestamp": "2025-01-15T10:30:10Z"
});
```

## SMS Trigger Mechanism

### 1. Database Trigger

The trigger automatically fires when a new SMS notification is inserted:

```sql
CREATE TRIGGER trigger_send_notification_sms
AFTER INSERT ON pre_evaluation_notifications
FOR EACH ROW
WHEN ((new.channel)::text = 'sms'::text AND (new.status)::text = 'pending'::text)
EXECUTE FUNCTION trigger_send_notification_sms();
```

### 2. Trigger Function

The `trigger_send_notification_sms()` function:
- Validates that the notification is an SMS in 'pending' status
- Retrieves Edge Function URL and service key from app_settings
- Makes an HTTP POST request to the Edge Function
- Updates notification status based on the response
- Logs all activities to debug_logs table

Key payload format sent to Edge Function:
```json
{
    "phone_number": "+************",
    "message_body": "Reminder: Please submit your pre-evaluation...",
    "notification_id": "uuid-here"
}
```

### 3. Edge Function (send-sms-v2)

The Deno-based Edge Function handles:
- Direct SMS sending via HTTP POST
- Queue processing when called with `process_queue` parameter
- UK phone number formatting (0 → +44)
- Twilio API integration
- Status updates back to the database

## Configuration

### Required App Settings

Add these to the `app_settings` table:

```sql
INSERT INTO app_settings (key, value) VALUES 
('edge_function_url', 'https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/send-sms'),
('supabase_service_key', 'your-service-key-here');
```

### Twilio Configuration

Currently hardcoded in the Edge Function (should be moved to environment variables):
- Account SID: `AC407acf6761a769c180f4253641cff662`
- Phone Number: `+************`

## Usage Examples

### 1. Creating Pre-Evaluation Notifications

```typescript
// From PreEvaluationRequestButton component
const { data: notifications } = await supabase
  .rpc('create_pre_evaluation_notifications', {
    p_event_id: eventId,
    p_reminder_message: reminderMessage,
    p_send_method: 'sms' // or 'both' for SMS + email
  });
```

### 2. Checking SMS Status

```sql
-- Check SMS status for an event
SELECT 
    n.*,
    p.first_name,
    p.last_name
FROM pre_evaluation_notifications n
JOIN profiles p ON n.recipient_id = p.id
JOIN pre_evaluations pe ON n.pre_evaluation_id = pe.id
WHERE pe.event_id = 'your-event-id'
AND n.channel = 'sms'
ORDER BY n.created_at DESC;
```

### 3. Manual SMS Processing

```bash
# Process pending SMS from queue
curl -X POST https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/send-sms \
  -H "Authorization: Bearer YOUR_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{"process_queue": true}'
```

## Debugging

### Check Debug Logs

The `debug_logs` table provides comprehensive tracing for SMS operations:

```sql
-- View recent SMS trigger activity
SELECT * FROM debug_logs 
WHERE function_name = 'trigger_send_notification_sms'
ORDER BY created_at DESC
LIMIT 20;

-- View logs for specific notification
SELECT 
    created_at,
    message,
    data->>'notification_id' as notification_id,
    data->>'status_code' as status_code,
    data->>'error' as error
FROM debug_logs 
WHERE function_name = 'trigger_send_notification_sms'
AND data->>'notification_id' = 'your-notification-uuid'
ORDER BY created_at ASC;

-- View error logs only
SELECT 
    created_at,
    message,
    data
FROM debug_logs 
WHERE function_name = 'trigger_send_notification_sms'
AND message LIKE '%Error%'
ORDER BY created_at DESC;

-- Count SMS attempts by status
SELECT 
    CASE 
        WHEN message LIKE '%response: 2%' THEN 'Success'
        WHEN message LIKE '%Error%' THEN 'Error'
        WHEN message LIKE '%Trigger activated%' THEN 'Initiated'
        ELSE 'Other'
    END as status,
    COUNT(*) as count
FROM debug_logs 
WHERE function_name = 'trigger_send_notification_sms'
AND created_at >= NOW() - INTERVAL '24 hours'
GROUP BY 1;
```

### Trace Complete SMS Flow

```sql
-- Complete flow for specific pre-evaluation
WITH sms_flow AS (
    SELECT 
        dl.created_at,
        dl.message,
        dl.data,
        n.recipient_phone,
        n.status as notification_status
    FROM debug_logs dl
    JOIN pre_evaluation_notifications n ON dl.data->>'notification_id' = n.id::text
    WHERE dl.function_name = 'trigger_send_notification_sms'
    AND n.pre_evaluation_id = 'your-pre-evaluation-uuid'
    ORDER BY dl.created_at ASC
)
SELECT * FROM sms_flow;
```

### Test Direct SMS

```bash
# Send test SMS directly
curl -X POST https://ovfwiyqhubxeqvbrggbe.supabase.co/functions/v1/send-sms \
  -H "Authorization: Bearer YOUR_SERVICE_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "phone_number": "+************",
    "message_body": "Test message from SHOT app"
  }'
```

## Common Issues

### 1. SMS Not Sending

- Check `app_settings` for correct Edge Function URL
- Verify service key is set and valid
- Check debug_logs for trigger errors
- Ensure Twilio credentials are valid

### 2. Phone Number Issues

- UK numbers must start with 0 or +44
- System automatically converts 0 to +44
- International numbers must include country code

### 3. Status Not Updating

- Check Edge Function logs in Supabase dashboard
- Verify HTTP extension is enabled in database
- Check for network/firewall issues

## Security Considerations

1. **Service Key**: Store securely in app_settings, never expose to client
2. **Twilio Credentials**: Should be moved to environment variables
3. **Phone Numbers**: Validate format before processing
4. **Rate Limiting**: Consider implementing to prevent abuse
5. **Logging**: Ensure no sensitive data in debug_logs

## Future Improvements

1. Move Twilio credentials to environment variables
2. Implement retry logic for failed messages
3. Add delivery status webhooks from Twilio
4. Create admin UI for monitoring SMS status
5. Add rate limiting and quota management
6. Implement SMS templates for consistent messaging