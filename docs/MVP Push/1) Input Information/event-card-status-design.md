# Event Card Status Display Design

## Overview
This document outlines how to display evaluation states on event cards, with attendance being a prerequisite for coach evaluations.

## Status Indicators

### 1. Pre-Evaluation Ring (Yellow) - Upcoming Events Only
- **Color**: Yellow (#FFC107)
- **Display**: Single ring for upcoming events with pre-evaluations enabled
- **States**:
  - Empty ring: No pre-evaluations completed (0%)
  - Partial ring: Some completed (show X/Y count and percentage)
  - Full ring: All completed (100%)

### 2. Coach Evaluation Ring (Green) - Past Events Only
- **Color**: Green (#4CAF50) when attendance is set, Gray (#9E9E9E) when attendance needed
- **Display**: Single ring for past events
- **States**:
  - Gray ring with "Set Attendance": When invited_count > attended_count
  - Empty green ring: Attendance set, no evaluations completed
  - Partial green ring: Some evaluations completed (show X/Y count)
  - Full green ring: All evaluations completed
  - Dotted outline on green ring: Some evaluations in draft status

## Implementation Logic

### For Upcoming Events:
```typescript
if (is_pre_session_evaluation && pre_eval_total_count > 0) {
  // Show yellow ring
  percentage = (pre_eval_completed_count / pre_eval_total_count) * 100
  display = `${pre_eval_completed_count}/${pre_eval_total_count}`
}
```

### For Past Events:
```typescript
if (invited_count > attended_count) {
  // Show gray ring with "Set Attendance" message
  display = "Set Attendance"
} else if (attended_count > 0) {
  // Show green ring with coach evaluations
  percentage = (coach_eval_completed_count / attended_count) * 100
  display = `${coach_eval_completed_count}/${attended_count}`
  
  // Add visual indicator if drafts exist
  if (coach_eval_draft_count > 0) {
    addDottedOutline()
  }
}
```

## Visual Examples

### Upcoming Event with Pre-Evaluations
```
[Yellow Ring: 0/5] 🔔 SMS
0% - Send reminder
```

### Past Event - Attendance Not Set
```
[Gray Ring] Set Attendance
5 invited, 0 attended
```

### Past Event - Ready for Evaluations
```
[Green Ring: 0/3] 
0% - Start evaluating
```

### Past Event - Evaluations In Progress
```
[Green Ring: 1/3] ···
33% - 2 drafts in progress
```

### Past Event - All Complete
```
[Green Ring: 3/3] ✅
100% complete
```

## The Flow
1. **Before Event**: Yellow ring shows pre-evaluation progress
2. **After Event**: 
   - If attendance not set → Gray ring prompting to set attendance
   - If attendance set → Green ring showing coach evaluation progress