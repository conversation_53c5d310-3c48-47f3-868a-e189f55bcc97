# Handover Command

Use this command to generate a session handover document when transferring work to another team member or continuing work in a new session.

## Usage

```
/handover [optional-notes]
```

## Description

This command generates a comprehensive handover document that includes:

- Current session health status
- Task progress and todos
- Technical context and working files
- Instructions for resuming work
- Any blockers or important notes

## Example

```
/handover Working on authentication refactor, need to complete OAuth integration
```

The handover document will be saved as a markdown file and can be used to seamlessly continue work in a new session.
EOF < /dev/null