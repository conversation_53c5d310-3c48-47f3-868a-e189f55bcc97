# Comprehensive Role-Based Component Analysis for SHOT Perform Page

## Overview
This document provides a detailed analysis of all components needed for the Perform page across Coach, Player, and Parent roles, including their data requirements, database availability, and design system component status.

## Legend
- **Data Status**: ✅ Complete | ⚠️ Partial | ❌ Missing
- **Design System**: ✅ Exists | ⚠️ Similar exists | ❌ Needs creation

---

## 🏆 COACH PERFORM PAGE COMPONENTS

### 1. Urgent Actions Card
**Purpose**: Critical alert system for time-sensitive evaluation tasks  
**Data Requirements**:
- Pending evaluations count
- Player names and IDs
- Event details (type, date)
- Deadline with countdown timer
- Evaluation type (pre/post)

**Data Status**: ⚠️ Partial
- **Available Tables**: 
  - `player_evaluations` - Coach evaluations
  - `pre_evaluations` - Pre-event self-assessments
  - `events` - Event details
  - `player_training_participation` - Comprehensive participation view
- **Missing**: Real-time countdown logic, evaluation window status

**Design System Status**: ⚠️ Similar exists
- `ShadowNotificationModal` - Can be adapted
- `ShadowStatCard` with alert styling - Can be modified
- Need custom countdown timer component

### 2. Quick Action Grid

#### a. Next Training Card
**Data Requirements**: Event title, date/time, venue, days until  
**Data Status**: ✅ Complete
- **Tables**: `events` (filtered by team_id, event_type='training', status='published')
**Design System**: ✅ Exists - `ShadowActionCard`, `ShadowActionGrid`

#### b. Next Match Card  
**Data Requirements**: Opponent, home/away, date/time, days until  
**Data Status**: ⚠️ Partial
- **Tables**: `events` (event_type='match')
- **Missing**: Opponent field, home/away designation
**Design System**: ✅ Exists - `ShadowActionCard`

#### c. Team Form Card
**Data Requirements**: Count of improving/declining/stable players  
**Data Status**: ⚠️ Partial
- **Available**: `player_progress_timeline` view for trends
- **Missing**: Aggregated team form calculations
**Design System**: ✅ Exists - `ShadowStatCard`

### 3. Team Overview Cards
**Purpose**: Snapshot of each team's evaluation health  
**Data Requirements**:
- Team name and sport type
- Player count
- Evaluation completion rate
- Average team score

**Data Status**: ⚠️ Partial
- **Available Tables**:
  - `teams` - Basic team info
  - `team_members` - Player count
  - `player_evaluation_summary_by_event` - Evaluation rates
- **Missing**: Aggregated team performance metrics

**Design System Status**: ⚠️ Similar exists
- `ShadowInfoCard` - Can be adapted
- Need custom team card variant

### 4. Recent Activity Feed
**Purpose**: Show latest team activities  
**Data Requirements**: Activity type, player/team, timestamp, details  
**Data Status**: ❌ Missing
- **Missing**: Activity/notification log table
**Design System**: ❌ Needs creation - Activity feed component

### 5. Club Selector (Multi-club coaches)
**Purpose**: Switch between clubs/sports  
**Data Requirements**: List of associated clubs/sports  
**Data Status**: ✅ Complete
- **Tables**: `teams` (filtered by coach role)
**Design System**: ⚠️ Similar exists - `ShadowAccountSwitcher` can be adapted

---

## ⚽ PLAYER PERFORM PAGE COMPONENTS

### 1. Must-Do Actions Alert
**Purpose**: Highlight urgent evaluation requirements  
**Data Requirements**:
- Evaluation type (pre/post)
- Event details
- Deadline with countdown
- Evaluation status

**Data Status**: ⚠️ Partial
- **Available Tables**: 
  - `pre_evaluations` - Pre-event status
  - `events` - Event details
  - `player_training_participation` - Comprehensive view
- **Missing**: Post-evaluation tracking table

**Design System Status**: ⚠️ Similar exists
- `ShadowPreEvaluationCard` - Can be extended
- `ShadowFlashingButton` - For urgency

### 2. Monthly Performance Summary
**Purpose**: Track progress across Four Corners  
**Data Requirements**:
- Monthly averages per corner
- Evaluation count
- Current streak
- Most improved corner

**Data Status**: ⚠️ Partial
- **Available**: 
  - `player_progress_timeline` - Monthly progress
  - `player_stats` - XP, levels, streaks
- **Missing**: Four Corners specific aggregations

**Design System Status**: ❌ Needs creation
- Need custom performance summary card
- Progress bars for each corner

### 3. Upcoming Events Panel
**Purpose**: Show scheduled training/matches  
**Data Requirements**:
- Event details
- Venue and travel info
- Availability status
- Focus areas

**Data Status**: ⚠️ Partial
- **Available**: 
  - `events` - Basic event info
  - `event_participants` - RSVP status
- **Missing**: Focus areas, travel info fields

**Design System Status**: ✅ Exists
- `ShadowEventCard` - Perfect match
- `FixedShadowEventCard` - Alternative

### 4. Recent Coach Feedback
**Purpose**: Display latest performance insights  
**Data Requirements**:
- Coach name
- Feedback text
- Event reference
- Date/timestamp

**Data Status**: ✅ Complete
- **Tables**: `player_evaluations` (notes field, evaluator_id)
**Design System**: ⚠️ Similar exists - `ShadowInfoCard` can be adapted

### 5. Profile Switcher (Multi-sport)
**Purpose**: Switch between team profiles  
**Data Requirements**: List of teams/sports for player  
**Data Status**: ✅ Complete
- **Tables**: `team_members` (filtered by player_id)
**Design System**: ✅ Exists - `ShadowAccountSwitcher`

### 6. Quick Actions Bar
**Purpose**: Fast access to key functions  
**Data Requirements**: Pending task states  
**Data Status**: ✅ Complete (derived from other data)
**Design System**: ✅ Exists - `ShadowButton`, `ShadowActionGrid`

---

## 👨‍👩‍👧 PARENT PERFORM PAGE COMPONENTS

### 1. Child Selector
**Purpose**: Navigate between children  
**Data Requirements**:
- List of linked children
- Current child's teams
- Active selection state

**Data Status**: ⚠️ Partial
- **Available**: `profiles` with family relationships
- **Missing**: Explicit parent-child linking table

**Design System Status**: ✅ Exists - `ShadowAccountSwitcher`

### 2. Child's Performance Summary
**Purpose**: Monitor child's development  
**Data Requirements**: Same as Player Performance Summary  
**Data Status**: ⚠️ Partial (same as player)
**Design System**: ❌ Needs creation (same as player)

### 3. Announcements Panel
**Purpose**: Display team announcements  
**Data Requirements**:
- Announcement text
- Author (coach)
- Date posted

**Data Status**: ❌ Missing
- **Missing**: Team announcements table
**Design System**: ⚠️ Similar exists - `ShadowInfoCard`

### 4. Recent Coach Feedback (Read-only)
**Purpose**: View feedback given to child  
**Data Requirements**: Same as player feedback  
**Data Status**: ✅ Complete (with RLS for parent access)
**Design System**: ⚠️ Similar exists

### 5. Team & Club Information
**Purpose**: Display logistics and contacts  
**Data Requirements**:
- Coach contacts
- Training schedule
- Venue info

**Data Status**: ⚠️ Partial
- **Available**: `teams`, `events` for schedule
- **Missing**: Coach contact preferences
**Design System**: ✅ Exists - `ShadowInfoCard`

### 6. Privacy & Safety Status
**Purpose**: Show permissions and safety settings  
**Data Requirements**:
- Permission status per team
- Contact info currency
- Data sharing settings

**Data Status**: ❌ Missing
- **Missing**: Parent approval/permissions table
**Design System**: ❌ Needs creation

---

## 🔄 SHARED COMPONENTS

### 1. Four Corners Chart
**Purpose**: Visualize performance over time  
**Data Status**: ⚠️ Partial
- **Available**: Raw evaluation data
- **Missing**: Aggregated corner-specific views
**Design System**: ❌ Needs creation - Custom chart component

### 2. Evaluation History List
**Purpose**: Show past evaluations  
**Data Status**: ✅ Complete
- **Tables**: `player_evaluation_history_view`
**Design System**: ⚠️ Similar exists - Can use list components

### 3. Header with SP Balance
**Purpose**: Show user info and Shot Points  
**Data Status**: ✅ Complete
- **Tables**: `sp_transactions`, `profiles`
**Design System**: ✅ Exists - `ShadowIconHeader`

### 4. Bottom Navigation
**Purpose**: Main app navigation  
**Data Status**: N/A (UI only)
**Design System**: ✅ Exists - `ShadowNavigationDrawer`

---

## 📊 SUMMARY TABLES

### Component Readiness Matrix

| Component Category | Total | Data Ready | Design Ready | Build Ready |
|-------------------|-------|------------|--------------|-------------|
| Coach Components | 9 | 3 (33%) | 5 (56%) | 2 (22%) |
| Player Components | 8 | 4 (50%) | 5 (63%) | 3 (38%) |
| Parent Components | 6 | 2 (33%) | 3 (50%) | 1 (17%) |
| Shared Components | 4 | 3 (75%) | 2 (50%) | 2 (50%) |
| **TOTAL** | **27** | **12 (44%)** | **15 (56%)** | **8 (30%)** |

### Missing Database Infrastructure

1. **High Priority**:
   - Post-evaluation tracking table
   - Team announcements table
   - Parent-child relationships table
   - Parent permissions/approvals table
   - Activity/notification log table

2. **Medium Priority**:
   - Match opponent/details fields in events
   - Focus areas for events
   - Coach contact preferences
   - Four Corners aggregation views
   - Team form calculations

### Missing Design System Components

1. **High Priority**:
   - Monthly Performance Summary Card
   - Four Corners Chart
   - Activity Feed Component
   - Privacy/Safety Status Component

2. **Medium Priority**:
   - Countdown Timer Component
   - Team Overview Card variant
   - Announcement Card variant
   - Performance Progress Bars

### Development Recommendations

**Phase 1 - Immediate (Can build now):**
1. Player Profile Switcher (100% ready)
2. Coach Quick Action Grid - Training Card (100% ready)
3. Header with SP Balance (100% ready)
4. Bottom Navigation (100% ready)

**Phase 2 - Minor Adaptations Needed:**
1. Coach Team Overview Cards (adapt ShadowInfoCard)
2. Player Upcoming Events Panel (use ShadowEventCard)
3. Recent Coach Feedback (adapt ShadowInfoCard)
4. Child Selector (use ShadowAccountSwitcher)

**Phase 3 - Requires Database Work:**
1. Urgent Actions Card (need evaluation windows)
2. Team Form calculations
3. Parent Permissions System
4. Team Announcements

**Phase 4 - Requires New Components:**
1. Monthly Performance Summary
2. Four Corners Chart
3. Activity Feed
4. Privacy & Safety Dashboard

---

## Next Steps

1. **Database Schema Updates**: Create migration for missing tables (announcements, permissions, parent-child, post-evaluations)
2. **Design System Components**: Build high-priority missing components (Performance Summary, Four Corners Chart)
3. **API Development**: Create aggregation functions for team form and Four Corners data
4. **Start Phase 1**: Implement components that are 100% ready
5. **Component Adaptation**: Modify existing design system components for Phase 2 items