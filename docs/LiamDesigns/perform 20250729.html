<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>SHOT Clubhouse MVP</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700;800&family=Montserrat:wght@400;500;600&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/feather-icons"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --shot-teal: #1ABC9C; --shot-purple: #6B00DB; --shot-gold: #F7B613;
            --shot-red: #E63946; --shot-bg: #121212; --shot-surface: #1E1E1E;
            --shot-text-primary: #FFFFFF; --shot-text-secondary: #C0C0C0;
            --font-poppins: 'Poppins', sans-serif; --font-montserrat: 'Montserrat', sans-serif;
            --corner-technical: #296DFF; --corner-physical: #2E8B57; --corner-psychological: #FF6F3C;
            --corner-social: #FF5D73; --corner-positional: #A95CFF;
        }
        body { font-family: var(--font-montserrat); background-color: var(--shot-bg); color: var(--shot-text-primary); -webkit-tap-highlight-color: transparent; }
        .font-poppins { font-family: var(--font-poppins); }
        .bg-shot-surface { background-color: var(--shot-surface); }
        .bg-shot-bg { background-color: var(--shot-bg); }
        .text-shot-gold { color: var(--shot-gold); }
        .text-secondary { color: var(--shot-text-secondary); }
        .border-shot-teal { border-color: var(--shot-teal); }
        .border-shot-purple { border-color: var(--shot-purple); }
        .hide-scrollbar::-webkit-scrollbar { display: none; }
        .hide-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }
        
        .fade-in { animation: fadeIn 0.3s ease-in-out; }
        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
        
        .slide-in-right { animation: slideInRight 0.3s ease-out forwards; }
        @keyframes slideInRight { from { transform: translateX(100%); } to { transform: translateX(0); } }
        
        .nav-btn.active i, .nav-btn.active span { color: var(--shot-gold); }
        
        .slider-input { -webkit-appearance: none; appearance: none; width: 100%; height: 8px; background: var(--shot-bg); border-radius: 5px; outline: none; }
        .slider-input::-webkit-slider-thumb { -webkit-appearance: none; appearance: none; width: 20px; height: 20px; background: var(--shot-teal); cursor: pointer; border-radius: 50%; }
        
        .accordion-content { max-height: 0; overflow: hidden; transition: max-height 0.4s ease-in-out; }
        .accordion-content.open { max-height: 1500px; }
        .accordion-header .icon-rotate { transition: transform 0.3s ease-out; }
        .accordion-header.open .icon-rotate { transform: rotate(180deg); }

        .btn-interactive { 
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out, background-color 0.2s; 
        }
        .btn-interactive:hover { 
            transform: scale(1.03);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .bg-gradient-teal { background-image: linear-gradient(to right, #1ABC9C, #16A085); }
        .bg-gradient-purple { background-image: linear-gradient(to right, #6B00DB, #512DA8); }
        .bg-gradient-red { background-image: linear-gradient(to right, #E63946, #C0392B); }
        
        .attendance-btn.selected {
            transform: scale(1.05);
            font-weight: bold;
            color: black;
            box-shadow: 0 0 10px var(--color);
        }

        input, textarea {
            color: var(--shot-text-primary) !important;
            background-color: var(--shot-bg) !important;
        }
        textarea::placeholder, input::placeholder {
            color: var(--shot-text-secondary);
            opacity: 0.7;
        }

    </style>
</head>
<body class="bg-shot-bg">

    <div class="w-full max-w-lg mx-auto flex flex-col min-h-screen bg-black">
        <header id="app-header" class="sticky top-0 bg-shot-bg/80 backdrop-blur-sm px-4 py-3 flex justify-between items-center border-b border-gray-800 z-40"></header>
        <main id="main-content" class="flex-1 overflow-y-auto scroll-smooth pb-20"></main>
        <nav id="bottom-nav" class="sticky bottom-0 bg-shot-surface/80 backdrop-blur-sm border-t border-gray-800 grid grid-cols-4 gap-2 px-2 py-2 z-40"></nav>
    </div>
    <div id="modal-container" class="fixed inset-0 z-50 pointer-events-none"></div>

    <script type="module">
        // --- MOCK DATA ---
        const mockData = {
            superUser: { 
                id: 'user_super_001', 
                name: 'Alex Taylor', 
                sp: 12540, 
                roles: ['Coach', 'Player', 'Parent'], 
                avatar: 'https://i.imgur.com/YlbXNSD.jpeg',
                profiles: [
                    { id: 'p05', sport: 'Football' },
                    { id: 'b02', sport: 'Boxing' },
                    { id: 'b03', sport: 'Boxing' }
                ]
            },
            teams: [
                { id: 'team_001', name: 'ISLEHAM FC BOYS U15S', sport: 'Football', color: 'teal', evalRate: 85, avgScore: 3.6, announcements: [{id: 1, text: "Training is cancelled this Friday due to weather.", author: "Coach Alex", date: "29 Jul 2025"}], players: [
                    { id: 'p01', name: 'Ethan Hayes', age: 14, pos: 'GK', trend: 'up', avatar: 'https://i.imgur.com/jvA0wv8.jpeg', evalStatus: 'Completed', availability: { training: 'available', match: 'available' } },
                    { id: 'p02', name: 'Lucas Miller', age: 14, pos: 'GK', trend: 'stable', avatar: 'https://i.imgur.com/arfq4om.jpeg', evalStatus: 'Pending Review', availability: { training: 'available', match: 'unavailable' } },
                    { id: 'p03', name: 'Leo Taylor', age: 15, pos: 'CB', trend: 'up', keyPlayer: true, avatar: 'https://i.imgur.com/ADMC4KP.jpeg', evalStatus: 'Completed', availability: { training: 'available', match: 'available' } },
                    { id: 'p04', name: 'Noah Wilson', age: 14, pos: 'CB', trend: 'down', avatar: 'https://i.imgur.com/7h4mhR5.jpeg', evalStatus: 'Overdue', availability: { training: 'available', match: 'available' } },
                    { id: 'p05', name: 'Kai Williams', age: 14, pos: 'CAM', trend: 'up', keyPlayer: true, avatar: 'https://i.imgur.com/UBELORe.jpeg', evalStatus: 'Pending Review', availability: { training: 'available', match: 'available' } },
                ]},
                { id: 'team_002', name: 'ISLEHAM FC GIRLS U12S', sport: 'Football', color: 'purple', evalRate: 92, avgScore: 3.4, announcements: [], players: [
                    { id: 'p06', name: 'Sophia Miller', age: 11, pos: 'FWD', trend: 'up', avatar: 'https://i.imgur.com/FapqQ1S.jpeg', evalStatus: 'Completed', availability: { training: 'available', match: 'available' } },
                    { id: 'p07', name: 'Emma Williams', age: 12, pos: 'MID', trend: 'stable', avatar: 'https://i.imgur.com/80fkfoM.jpeg', evalStatus: 'Pending Review', availability: { training: 'available', match: 'available' } },
                ]},
                 { id: 'team_003', name: 'EAST LONDON BOXING', sport: 'Boxing', color: 'gold', evalRate: 95, avgScore: 4.1, announcements: [{id: 1, text: "Reminder: Sparring session this Saturday is mandatory.", author: "Coach Alex", date: "28 Jul 2025"}], players: [
                    { id: 'b01', name: 'Zoe Khan', age: 16, pos: 'Featherweight', trend: 'up', avatar: 'https://i.imgur.com/BS02gKm.jpeg', evalStatus: 'Completed', availability: { training: 'available', match: 'available' } },
                    { id: 'b02', name: 'Ben Carter', age: 17, pos: 'Welterweight', trend: 'up', avatar: 'https://i.imgur.com/JOfnhtw.jpeg', evalStatus: 'Pending Review', availability: { training: 'available', match: 'available' } },
                    { id: 'b03', name: 'Mia Jones', age: 18, pos: 'Lightweight', trend: 'stable', avatar: 'https://i.imgur.com/QyZQxYn.jpeg', evalStatus: 'Completed', availability: { training: 'available', match: 'available' } }
                ]}
            ],
            parentData: { name: 'Sarah Williams', sp: 2100, avatar: 'https://i.imgur.com/kiYVivh.jpeg', children: [{id: 'p06', name: 'Sophia Miller'}, {id: 'b02', name: 'Ben Carter'}] },
            monthlyFocus: {
                'Football': {
                    physical: { label: 'Stamina', pre: "What are your energy levels like?", post: "Rate your stamina throughout the match.", coach: "Assess the player's stamina levels." },
                    technical: { label: 'Turning away from pressure', pre: "How confident are you in tight spaces?", post: "Did you use turns to move away from defenders?", coach: "Assess ability to turn from pressure." },
                    psychological: { label: 'Positional awareness', pre: "How's your understanding of your role?", post: "Did you position yourself well to receive passes?", coach: "Assess positional awareness." },
                    social: { label: 'Resilience', pre: "Ready to bounce back from setbacks?", post: "How did you react to mistakes or goals conceded?", coach: "Assess resilience and attitude." }
                },
                'Boxing': {
                    physical: { label: 'Recovery', pre: "How quickly are you recovering between drills?", post: "Rate your recovery between rounds.", coach: "Assess recovery rate." },
                    technical: { label: 'Ring Control', pre: "Ready to own the space?", post: "Rate your ability to control the ring.", coach: "Assess ring generalship." },
                    psychological: { label: 'Adaptability', pre: "Ready to adapt if things change?", post: "Rate your ability to adapt during sparring.", coach: "Assess adaptability mid-fight." },
                    social: { label: 'Countering', pre: "How sharp are your counters?", post: "Rate your ability to counter-punch effectively.", coach: "Assess counter-punching skill." }
                }
            },
            evaluationFrameworks: {
                Football: {
                    physical: { label: 'Physical', icon: '💪', color: 'var(--corner-physical)'},
                    technical: { label: 'Technical', icon: '🎯', color: 'var(--corner-technical)'},
                    psychological: { label: 'Tactical', icon: '🧠', color: 'var(--corner-psychological)'},
                    social: { label: 'Mental', icon: '�', color: 'var(--corner-social)'}
                },
                Boxing: {
                    physical: { label: 'Power & Endurance', icon: '💥', color: 'var(--corner-physical)'},
                    technical: { label: 'Footwork', icon: '👟', color: 'var(--corner-technical)'},
                    psychological: { label: 'Ring IQ', icon: '💡', color: 'var(--corner-psychological)'},
                    social: { label: 'Defense', icon: '🛡️', color: 'var(--corner-social)'}
                }
            },
             historicalData: {
                'p05': { // Kai Williams
                    '2025-05': { scores: { physical: 3.6, technical: 3.5, psychological: 3.9, social: 4.0 }, comment: "Good progress this month, Kai. Focus on maintaining intensity for the full 90 minutes.", selfScores: { physical: 3.5, technical: 4.0, psychological: 4.0, social: 4.0 }, trend: 'stable' },
                    '2025-06': { scores: { physical: 3.8, technical: 3.9, psychological: 4.1, social: 4.0 }, comment: "Excellent improvement in your tactical awareness. Your decision making under pressure was much better.", selfScores: { physical: 4.0, technical: 4.0, psychological: 4.5, social: 4.0 }, trend: 'up' },
                    '2025-07': { scores: { physical: 3.9, technical: 4.1, psychological: 4.3, social: 4.0 }, comment: "Fantastic month. You've become a key player. Let's work on weak-foot passing next.", selfScores: { physical: 4.0, technical: 4.5, psychological: 4.5, social: 4.5 }, trend: 'up' },
                },
                 'b02': { // Ben Carter
                    '2025-05': { scores: { physical: 4.0, technical: 3.8, psychological: 3.6, social: 3.9 }, comment: "Solid month, Ben. Endurance is top-notch. Let's sharpen up that jab.", selfScores: { physical: 4.0, technical: 4.0, psychological: 3.5, social: 4.0 }, trend: 'up' },
                    '2025-06': { scores: { physical: 4.1, technical: 4.0, psychological: 3.9, social: 4.1 }, comment: "Great work on defense this month. Your head movement has improved significantly.", selfScores: { physical: 4.5, technical: 4.0, psychological: 4.0, social: 4.0 }, trend: 'up' },
                    '2025-07': { scores: { physical: 4.3, technical: 4.1, psychological: 4.0, social: 4.2 }, comment: "Powerful month. Your combinations are looking sharp and landing with impact.", selfScores: { physical: 4.5, technical: 4.5, psychological: 4.0, social: 4.5 }, trend: 'down' },
                },
                'b03': { // Mia Jones
                    '2025-05': { scores: { physical: 3.8, technical: 4.0, psychological: 4.1, social: 3.9 }, comment: "Great start Mia, impressive power.", selfScores: { physical: 4.0, technical: 4.0, psychological: 4.0, social: 4.0 }, trend: 'up' },
                    '2025-06': { scores: { physical: 3.9, technical: 4.0, psychological: 4.2, social: 4.0 }, comment: "Keep working on your cardio. Your ring control is improving.", selfScores: { physical: 4.0, technical: 4.0, psychological: 4.5, social: 4.0 }, trend: 'stable' },
                    '2025-07': { scores: { physical: 4.0, technical: 4.1, psychological: 4.2, social: 4.1 }, comment: "Excellent month. Your focus is paying off.", selfScores: { physical: 4.5, technical: 4.5, psychological: 4.0, social: 4.5 }, trend: 'up' },
                },
                'p06': { // Sophia Miller
                    '2025-05': { scores: { physical: 3.1, technical: 3.5, psychological: 3.2, social: 3.8 }, comment: "Good start, showing promise.", selfScores: { physical: 3.0, technical: 3.5, psychological: 3.0, social: 4.0 }, trend: 'stable' },
                    '2025-06': { scores: { physical: 3.4, technical: 3.7, psychological: 3.5, social: 4.0 }, comment: "Improving well, keep it up.", selfScores: { physical: 3.5, technical: 3.5, psychological: 3.5, social: 4.0 }, trend: 'up' },
                    '2025-07': { scores: { physical: 3.6, technical: 4.0, psychological: 3.8, social: 4.1 }, comment: "Excellent progress this month.", selfScores: { physical: 4.0, technical: 4.0, psychological: 4.0, social: 4.0 }, trend: 'up' },
                }
            },
            pulseFeed: [
                { id: 1, user: 'Coach Alex', avatar: 'https://i.imgur.com/YlbXNSD.jpeg', action: 'posted an announcement for', target: 'ISLEHAM FC BOYS U15S', time: '15m ago', icon: 'megaphone' },
                { id: 2, user: 'Kai Williams', avatar: 'https://i.imgur.com/UBELORe.jpeg', action: 'completed a pre-match evaluation.', target: '', time: '45m ago', icon: 'check-circle' },
                { id: 3, user: 'SHOT Locker', avatar: 'https://i.imgur.com/v8L6k2j.png', action: 'added a new item:', target: 'SHOT Tech Enabled Cap', time: '2h ago', icon: 'shopping-bag' },
                { id: 4, user: 'Sophia Miller', avatar: 'https://i.imgur.com/FapqQ1S.jpeg', action: 'hit a new Personal Best in', target: 'Agility & Speed (4.5)', time: '1d ago', icon: 'award' },
            ],
            achievements: {
                'p05': [ // Kai Williams
                    { id: 'ach01', text: 'First Evaluation Complete', unlocked: true },
                    { id: 'ach02', text: '10 Evaluations Milestone', unlocked: true },
                    { id: 'ach03', text: 'Technical Personal Best (4.1)', unlocked: true },
                    { id: 'ach04', text: '3-Day Evaluation Streak', unlocked: true },
                    { id: 'ach05', text: 'Physical Personal Best (Current: 3.9)', unlocked: false },
                    { id: 'ach06', text: '30-Day Evaluation Streak (21 days to go)', unlocked: false },
                ],
                 'p06': [
                    { id: 'ach01', text: 'First Evaluation Complete', unlocked: true },
                    { id: 'ach02', text: '10 Evaluations Milestone', unlocked: false },
                ],
                 'b02': [
                    { id: 'ach01', text: 'First Session Evaluation Complete', unlocked: true },
                    { id: 'ach02', text: '5 Evaluations Milestone', unlocked: true },
                ],
                'b03': [
                    { id: 'ach01', text: 'First Session Evaluation Complete', unlocked: true },
                    { id: 'ach02', text: '3 Evaluations Milestone', unlocked: true },
                ],
            }
        };

        // --- APP STATE ---
        const appState = {
            currentUser: mockData.superUser,
            activePersona: 'Coach',
            currentView: 'coach-dashboard',
            previousViews: [],
            currentTeamId: 'team_001',
            currentAthleteId: 'p05', // Default to a football player
            currentPlayerProfileId: 'p05',
            currentClub: 'Football',
            currentChildId: 'p06',
            attendance: {},
            historyMonth: '2025-07'
        };

        // --- DOM ELEMENTS ---
        const mainContent = document.getElementById('main-content');
        const modalContainer = document.getElementById('modal-container');
        const header = document.getElementById('app-header');
        const bottomNav = document.getElementById('bottom-nav');
        
        // --- UTILITY & CONTEXT FUNCTIONS ---
        const safeFeatherReplace = () => {
            try { if (window.feather) feather.replace({ 'stroke-width': 1.5 }); } catch (e) { console.warn('Feather icons not loaded:', e); }
        };
        const createChart = (ctx, config) => new Chart(ctx, config);

        const getTerminology = (sport) => {
            const terms = {
                Football: { athlete: 'Player', athletes: 'Players', squad: 'Squad', squads: 'Squads', training: 'Training', match: 'Match', location: 'Pitch', training_icon: '🏃‍♀️', match_icon: '⚽️' },
                Boxing: { athlete: 'Boxer', athletes: 'Boxers', squad: 'Gym', squads: 'Gyms', training: 'Session', match: 'Fight', location: 'Ring', training_icon: '🥊', match_icon: '🏆' }
            };
            return terms[sport] || terms.Football;
        };

        const getTrendIndicator = (trend) => {
            const indicators = { up: '↗️', down: '↘️', stable: '➡️' };
            const colors = { up: 'text-green-500', down: 'text-red-500', stable: 'text-gray-500' };
            return `<span class="text-xl ${colors[trend]}">${indicators[trend] || '➡️'}</span>`;
        };
        
        const getTrendIcon = (trend) => {
             const indicators = { up: '↗️', down: '↘️', stable: '➡️' };
             return indicators[trend] || '➡️';
        }

        // --- NAVIGATION & MODAL LOGIC ---
        const navigateTo = (view, context = null) => {
            if(appState.currentView !== view) {
                 appState.previousViews.push(appState.currentView);
            }
            appState.currentView = view;
            if (context) {
                if(typeof context === 'string' && context.startsWith('team_')) {
                    appState.currentTeamId = context;
                    const team = mockData.teams.find(t => t.id === context);
                    if (team) appState.currentClub = team.sport;
                } else if(typeof context === 'string' && (context.startsWith('p') || context.startsWith('b'))) {
                    appState.currentAthleteId = context;
                     const team = mockData.teams.find(t => t.players.some(p => p.id === context));
                    if (team) {
                        appState.currentClub = team.sport;
                        appState.currentTeamId = team.id;
                    }
                } else if (typeof context === 'object' && context.childId) {
                    appState.currentChildId = context.childId;
                } else if (typeof context === 'object' && context.playerProfileId) {
                    appState.currentPlayerProfileId = context.playerProfileId;
                }
            }
            closeModal(); // Close any open modals on navigation
            renderApp();
            mainContent.scrollTop = 0;
        };

        const goBack = () => {
            closeModal(); // Ensure modals are closed on back navigation
            const previousView = appState.previousViews.pop();
            const targetView = previousView || `${appState.activePersona.toLowerCase()}-dashboard`;
            appState.currentView = targetView;
            renderApp();
        }

        const showModal = (content, fullScreen = false) => {
            const modalClass = fullScreen 
                ? 'bg-shot-bg w-full h-full' 
                : 'bg-shot-surface rounded-xl w-full max-w-sm';
            modalContainer.innerHTML = `<div class="fixed inset-0 bg-black/70 flex items-center justify-center p-0 md:p-4 fade-in" onclick="closeModal()">
                <div class="${modalClass} slide-in-right flex flex-col" onclick="event.stopPropagation()">
                    ${content}
                </div>
            </div>`;
            modalContainer.classList.remove('pointer-events-none');
            safeFeatherReplace();
        };

        const closeModal = () => {
            modalContainer.innerHTML = '';
            modalContainer.classList.add('pointer-events-none');
        }
        
        const renderHeader = () => {
            const personaIcons = { Coach: '🏆', Player: '⚽️', Parent: '👨‍👩‍👧' };
            let currentName = appState.currentUser.name, currentSp = appState.currentUser.sp, avatar = appState.currentUser.avatar;
            
            if (appState.activePersona === 'Player') { 
                const athleteData = mockData.teams.flatMap(t => t.players).find(p => p.id === appState.currentPlayerProfileId);
                if(athleteData) {
                    currentName = athleteData.name;
                    currentSp = athleteData.sp || 4000; // Mock SP for simplicity
                    avatar = athleteData.avatar;
                }
            }
            if (appState.activePersona === 'Parent') { currentName = mockData.parentData.name; currentSp = mockData.parentData.sp; avatar = mockData.parentData.avatar; }
            
            const showBackButton = appState.previousViews.length > 0;
            
            header.innerHTML = `
                <div class="flex items-center space-x-2">
                    ${showBackButton ? `<button onclick="goBack()" class="text-white"><i data-feather="arrow-left"></i></button>` : `<img src="https://i.imgur.com/v8L6k2j.png" alt="SHOT Logo" class="h-8">`}
                    <span class="font-poppins font-semibold text-text-primary">${personaIcons[appState.activePersona]} ${currentName}</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="font-bold text-shot-gold text-sm">${currentSp.toLocaleString()} SP</span>
                     <div class="relative">
                         <button id="profile-menu-button" onclick="toggleProfileMenu(event)">
                              <img src="${avatar}" class="h-9 w-9 rounded-full border-2 border-shot-purple object-cover">
                         </button>
                         <div id="profile-menu" class="hidden absolute right-0 mt-2 w-48 bg-shot-surface rounded-lg shadow-lg z-50 py-1">
                         </div>
                     </div>
                </div>`;
        };
        
        const renderBottomNav = () => {
             const navItems = [
                { label: 'Clubhouse', icon: 'home', view: 'clubhouse' },
                { label: 'Pulse', icon: 'activity', view: 'pulse' },
                { label: 'PERFORM', icon: 'trending-up', view: `${appState.activePersona.toLowerCase()}-dashboard` },
                { label: 'Locker', icon: 'shopping-bag', view: 'locker' },
            ];
            const isPerformActive = ['coach-dashboard', 'team-dashboard', 'team-squad', 'event-management', 'evaluation-review', 'evaluation-detail', 'player-analytics', 'player-dashboard', 'player-evaluation-pre', 'player-evaluation-post', 'player-performance-history', 'parent-dashboard', 'guardian-approval', 'parent-manage-children', 'event-history', 'team-chat', 'parent-fixtures'].includes(appState.currentView);
            const isPulseActive = ['my-timeline', 'my-rewards', 'pulse'].includes(appState.currentView);

            bottomNav.innerHTML = navItems.map(item => `
                <button class="nav-btn flex flex-col items-center space-y-1 text-xs text-gray-400 flex-1 ${appState.currentView === item.view || (item.label === 'PERFORM' && isPerformActive) || (item.label === 'Pulse' && isPulseActive) ? 'active' : ''}" onclick="navigateTo('${item.view}')">
                    <i data-feather="${item.icon}" class="w-6 h-6"></i>
                    <span>${item.label}</span>
                </button>
            `).join('');
        };
        
        const toggleProfileMenu = (event) => {
            event.stopPropagation();
            const menu = document.getElementById('profile-menu');
            if (!menu) return;
            const isHidden = menu.classList.contains('hidden');
            
            if (isHidden) {
                const personaOptions = appState.currentUser.roles.map(role => `
                    <a href="#" class="block px-4 py-2 text-sm rounded-md mx-1 my-1 hover:bg-shot-purple/50 ${appState.activePersona === role ? 'bg-shot-purple text-white' : 'text-text-primary'}" onclick="event.preventDefault(); switchPersona('${role}')">${role}</a>
                `).join('');

                menu.innerHTML = `
                    ${personaOptions}
                    <div class="border-t border-gray-700 my-1"></div>
                     <a href="#" class="flex items-center gap-3 px-4 py-2 text-sm text-text-primary rounded-md mx-1 my-1 hover:bg-shot-purple/50" onclick="event.preventDefault(); navigateTo('my-timeline')"><i data-feather="clock" class="w-4 h-4"></i> My Timeline</a>
                     <a href="#" class="flex items-center gap-3 px-4 py-2 text-sm text-text-primary rounded-md mx-1 my-1 hover:bg-shot-purple/50" onclick="event.preventDefault(); navigateTo('my-rewards')"><i data-feather="award" class="w-4 h-4"></i> My Rewards</a>
                    <div class="border-t border-gray-700 my-1"></div>
                    <a href="#" class="flex items-center gap-3 px-4 py-2 text-sm text-shot-red rounded-md mx-1 my-1 hover:bg-shot-red/20" onclick="event.preventDefault();"><i data-feather="log-out" class="w-4 h-4"></i> Logout</a>
                `;
                menu.classList.remove('hidden');
                safeFeatherReplace();
            } else {
                menu.classList.add('hidden');
            }
        };

        const switchPersona = (newPersona) => {
            const menu = document.getElementById('profile-menu');
            if (menu) menu.classList.add('hidden');
            if (appState.activePersona === newPersona || !appState.currentUser.roles.includes(newPersona)) return;
            appState.activePersona = newPersona;
            if(newPersona === 'Player') {
                appState.currentPlayerProfileId = mockData.superUser.profiles[0].id;
            }
            if(newPersona === 'Parent') {
                appState.currentChildId = mockData.parentData.children[0].id;
            }
            appState.currentView = `${newPersona.toLowerCase()}-dashboard`;
            appState.previousViews = [];
            renderApp();
        };
        
        const switchClub = (clubName) => {
            appState.currentClub = clubName;
            const firstTeamOfClub = mockData.teams.find(t => t.sport === clubName);
            if (firstTeamOfClub) {
                appState.currentTeamId = firstTeamOfClub.id;
            }
            renderApp();
        }
        
        const renderApp = () => {
            const viewRenderers = {
                'coach-dashboard': getCoachDashboardHTML,
                'team-dashboard': getTeamDashboardHTML,
                'team-squad': getTeamSquadHTML,
                'event-management': getEventManagementHTML,
                'event-history': getEventHistoryHTML,
                'evaluation-review': getEvaluationReviewListHTML, 
                'evaluation-detail': getEvaluationDetailHTML, 
                'player-analytics': getPlayerAnalyticsHTML,
                'player-dashboard': getAthleteDashboardHTML,
                'player-evaluation-pre': getPlayerPreEvaluationHTML,
                'player-evaluation-post': getPlayerPostEvaluationHTML,
                'player-performance-history': getPlayerPerformanceHistoryHTML,
                'parent-dashboard': getParentDashboardHTML,
                'parent-manage-children': getParentManageChildrenHTML,
                'guardian-approval': getGuardianApprovalHTML,
                'team-chat': getTeamChatHTML,
                'my-timeline': getMyTimelineHTML,
                'my-rewards': getMyRewardsHTML,
                'clubhouse': getClubhouseHTML,
                'pulse': getPulseHTML,
                'locker': getLockerHTML,
                'parent-fixtures': getParentFixturesHTML,
            };

            mainContent.innerHTML = (viewRenderers[appState.currentView] || (() => placeholderPage('Page Not Found')))();
            renderHeader();
            renderBottomNav();
            safeFeatherReplace();

            if (appState.currentView === 'player-analytics' || appState.currentView === 'player-performance-history') {
                const chartCtx = document.getElementById('player-progress-chart')?.getContext('2d');
                if (chartCtx) createChart(chartCtx, getChartConfig());
            }
        };
        
        function getChartConfig() {
            let athleteId = appState.currentAthleteId;
             if (appState.activePersona === 'Player') athleteId = appState.currentPlayerProfileId;
            if (appState.activePersona === 'Parent') athleteId = appState.currentChildId;

            const team = mockData.teams.find(t => t.players.some(p => p.id === athleteId));
            if (!team) return {};
            const framework = mockData.evaluationFrameworks[team.sport];
            
            return {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
                    datasets: Object.keys(framework).map(key => ({
                         label: framework[key].label, 
                         data: [2.5, 2.8, 3.0, 3.2, 3.5, 4.1, 4.3].map(d => d + (Math.random() - 0.5)), 
                         borderColor: framework[key].color, 
                         backgroundColor: framework[key].color, 
                         tension: 0.4, 
                         borderWidth: 2.5, 
                         pointRadius: 4, 
                         pointBackgroundColor: framework[key].color,
                         pointBorderColor: '#000',
                         pointBorderWidth: 1.5,
                    }))
                },
                options: { responsive: true, maintainAspectRatio: false, plugins: { legend: { labels: { color: 'var(--shot-text-primary)', boxWidth: 10, padding: 15 }, position: 'bottom' } }, scales: { y: { beginAtZero: false, min: 1, max: 5, ticks: { color: 'var(--shot-text-secondary)', stepSize: 1 }, grid: { color: 'rgba(255, 255, 255, 0.1)' } }, x: { ticks: { color: 'var(--shot-text-secondary)' }, grid: { color: 'rgba(255, 255, 255, 0.1)' } } } }
            };
        }

        const toggleAccordion = (element) => {
            element.classList.toggle('open');
            const content = element.nextElementSibling;
            content.classList.toggle('open');
        };

        const markAttendance = (playerId, status, button) => {
            appState.attendance[playerId] = status;
            const buttons = button.parentElement.children;
            for (let btn of buttons) {
                btn.classList.remove('selected', 'bg-green-500', 'bg-red-500', 'bg-yellow-500');
                btn.style.setProperty('--color', 'inherit');
                btn.classList.add('bg-gray-700');
            }
            button.classList.add('selected');
            if(status === 'present') {
                 button.classList.remove('bg-gray-700');
                 button.classList.add('bg-green-500');
                 button.style.setProperty('--color', '#10B981');
            }
            if(status === 'absent') {
                button.classList.remove('bg-gray-700');
                button.classList.add('bg-red-500');
                button.style.setProperty('--color', '#EF4444');
            }
            if(status === 'injured') {
                button.classList.remove('bg-gray-700');
                button.classList.add('bg-yellow-500');
                button.style.setProperty('--color', '#F59E0B');
            }
        };

        const saveAttendance = () => {
             console.log('Attendance Saved:', appState.attendance);
             closeModal();
             showModal(`
                <div class="p-4 text-center relative">
                     <button onclick="closeModal()" class="absolute top-2 right-2 text-gray-500"><i data-feather="x"></i></button>
                    <i data-feather="check-circle" class="w-12 h-12 mx-auto text-shot-teal"></i>
                    <h3 class="font-poppins font-bold text-lg mt-2">Attendance Saved!</h3>
                    <p class="text-sm text-secondary mt-1">The attendance record has been updated.</p>
                    <button class="w-full bg-shot-purple text-white font-bold py-2 mt-4 rounded-lg" onclick="closeModal(); renderApp()">OK</button>
                </div>
            `);
        };

        const savePrivateNote = () => {
            const note = document.getElementById('private-note-textarea').value;
            console.log('Private Note Saved:', note);
            closeModal();
        };

        const sendPublicFeedback = () => {
            const feedback = document.getElementById('public-feedback-textarea').value;
            console.log('Public Feedback Sent:', feedback);
            closeModal();
        };

        const sendCoachMessage = () => {
            const feedback = document.getElementById('coach-message-textarea').value;
            console.log('Message to Coach Sent:', feedback);
            closeModal();
        };
        
        const setHistoryMonth = (month) => {
            appState.historyMonth = month;
            renderApp();
        }

        const postAnnouncement = () => {
            const textarea = document.getElementById('announcement-textarea');
            const text = textarea.value.trim();
            if (text) {
                const team = mockData.teams.find(t => t.id === appState.currentTeamId);
                if (team) {
                    team.announcements.unshift({
                        id: Date.now(),
                        text: text,
                        author: appState.currentUser.name,
                        date: new Date().toLocaleDateString('en-GB', { day: 'numeric', month: 'short', year: 'numeric' })
                    });
                    closeModal();
                    renderApp();
                }
            }
        };

        const createEvent = () => {
            console.log('Event Created!');
            closeModal();
            showModal(`
                <div class="p-4 text-center relative">
                    <button onclick="closeModal()" class="absolute top-2 right-2 text-gray-500"><i data-feather="x"></i></button>
                    <i data-feather="check-circle" class="w-12 h-12 mx-auto text-shot-teal"></i>
                    <h3 class="font-poppins font-bold text-lg mt-2">Event Created!</h3>
                    <p class="text-sm text-secondary mt-1">The new event has been added to the schedule.</p>
                    <button class="w-full bg-shot-purple text-white font-bold py-2 mt-4 rounded-lg" onclick="closeModal()">OK</button>
                </div>
            `);
            const accordionHeader = document.querySelector('#event-management-accordion');
            if(accordionHeader && accordionHeader.classList.contains('open')) {
                toggleAccordion(accordionHeader);
            }
        };

        const linkChild = () => {
            const input = document.querySelector('#link-child-input');
            const code = input ? input.value : '';
            console.log('Linking child with code:', code);
            closeModal();
            showModal(`
                <div class="p-4 text-center relative">
                    <button onclick="closeModal()" class="absolute top-2 right-2 text-gray-500"><i data-feather="x"></i></button>
                    <i data-feather="check-circle" class="w-12 h-12 mx-auto text-shot-teal"></i>
                    <h3 class="font-poppins font-bold text-lg mt-2">Link Request Sent!</h3>
                    <p class="text-sm text-secondary mt-1">Once approved, the child will appear here.</p>
                    <button class="w-full bg-shot-purple text-white font-bold py-2 mt-4 rounded-lg" onclick="closeModal()">OK</button>
                </div>
            `);
            if(input) input.value = '';
        };
        
        const submitCoachEvaluation = (athleteId) => {
             mockData.teams.forEach(team => {
                const player = team.players.find(p => p.id === athleteId);
                if (player) {
                    player.evalStatus = 'Completed';
                }
            });
            goBack();
        };

        const submitPlayerEvaluation = () => {
            const athlete = mockData.teams.flatMap(t => t.players).find(p => p.id === appState.currentPlayerProfileId);
            if(athlete) {
                athlete.evalStatus = 'Pending Review';
            }
            closeModal();
            navigateTo('player-dashboard');
             showModal(`
                <div class="p-4 text-center relative">
                     <button onclick="closeModal()" class="absolute top-2 right-2 text-gray-500"><i data-feather="x"></i></button>
                    <i data-feather="check-circle" class="w-12 h-12 mx-auto text-shot-teal"></i>
                    <h3 class="font-poppins font-bold text-lg mt-2">Evaluation Submitted!</h3>
                    <p class="text-sm text-secondary mt-1">Your evaluation has been sent to your coach for review. You've earned 100 SP!</p>
                    <button class="w-full bg-shot-purple text-white font-bold py-2 mt-4 rounded-lg" onclick="closeModal()">Awesome!</button>
                </div>
            `);
        }


        // --- PAGE HTML GETTERS ---
        function getClubhouseHTML() {
            return `
            <div class="p-4 space-y-6">
                <div>
                    <h2 class="font-poppins font-bold text-2xl">Clubhouse</h2>
                    <p class="text-secondary">Welcome back, ${appState.currentUser.name.split(' ')[0]}!</p>
                </div>
                <div class="bg-shot-surface p-4 rounded-lg cursor-pointer btn-interactive" onclick="navigateTo('${appState.activePersona.toLowerCase()}-dashboard')">
                     <h3 class="font-poppins font-bold text-lg">Go to my ${appState.activePersona} Dashboard</h3>
                     <p class="text-sm text-secondary">View your teams, evaluations, and performance data.</p>
                </div>
                <div class="bg-shot-surface p-4 rounded-lg">
                    <h3 class="font-poppins font-bold text-lg mb-2">Club News & Announcements</h3>
                    <div class="space-y-3 text-sm">
                        <div class="bg-shot-bg p-3 rounded-md">
                            <p class="font-semibold">🏆 End of Season Awards</p>
                            <p class="text-secondary text-xs">The annual awards night is scheduled for August 15th. Get your tickets now!</p>
                        </div>
                         <div class="bg-shot-bg p-3 rounded-md">
                            <p class="font-semibold">⚽️ Summer Camp Sign-ups Open</p>
                            <p class="text-secondary text-xs">Limited spots available for our 2-week summer development camp.</p>
                        </div>
                    </div>
                </div>
            </div>`;
        }

        function getPulseHTML() {
            return `
            <div class="p-4 space-y-4">
                 <h2 class="font-poppins font-bold text-2xl">Pulse</h2>
                 <p class="text-secondary -mt-2">The latest activity across your network.</p>
                 <div class="space-y-3">
                    ${mockData.pulseFeed.map(item => `
                        <div class="bg-shot-surface p-3 rounded-lg flex items-start space-x-3">
                             <img src="${item.avatar}" class="w-10 h-10 rounded-full object-cover mt-1">
                             <div class="flex-1">
                                 <p class="text-sm">
                                     <span class="font-bold">${item.user}</span>
                                     <span class="text-secondary">${item.action}</span>
                                     ${item.target ? `<span class="font-semibold text-shot-teal">${item.target}</span>` : ''}
                                 </p>
                                 <p class="text-xs text-gray-500 mt-1 flex items-center gap-1">
                                     <i data-feather="${item.icon}" class="w-3 h-3"></i>
                                     ${item.time}
                                 </p>
                             </div>
                        </div>
                    `).join('')}
                 </div>
            </div>
            `;
        }

        function getLockerHTML() {
            return `
                <div class="p-4 space-y-4">
                    <h2 class="font-poppins font-bold text-2xl">SHOT Locker</h2>
                    <p class="text-secondary -mt-2">Official SHOT merchandise & partner deals.</p>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-shot-surface rounded-lg">
                            <img src="https://i.imgur.com/v8L6k2j.png" class="rounded-t-lg p-4">
                            <div class="p-3">
                                <p class="font-semibold">SHOT Tech Cap</p>
                                <p class="text-sm font-bold text-shot-gold">5,000 SP</p>
                            </div>
                        </div>
                         <div class="bg-shot-surface rounded-lg">
                            <img src="https://i.imgur.com/v8L6k2j.png" class="rounded-t-lg p-4">
                            <div class="p-3">
                                <p class="font-semibold">SHOT Performance Tee</p>
                                <p class="text-sm font-bold text-shot-gold">7,500 SP</p>
                            </div>
                        </div>
                         <div class="bg-shot-surface rounded-lg">
                            <img src="https://i.imgur.com/v8L6k2j.png" class="rounded-t-lg p-4">
                            <div class="p-3">
                                <p class="font-semibold">20% Off at Pro:Direct</p>
                                <p class="text-sm font-bold text-shot-gold">1,000 SP</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getCoachDashboardHTML() {
            const framework = mockData.evaluationFrameworks[appState.currentClub];
            const teamsForClub = mockData.teams.filter(team => team.sport === appState.currentClub);
            const pendingReviewsCount = teamsForClub.flatMap(t => t.players).filter(p => p.evalStatus === 'Pending Review').length;
            const terminology = getTerminology(appState.currentClub);
            const allPlayersInClub = teamsForClub.flatMap(t => t.players);
            const teamForm = {
                up: allPlayersInClub.filter(p => p.trend === 'up').length,
                down: allPlayersInClub.filter(p => p.trend === 'down').length,
                stable: allPlayersInClub.filter(p => p.trend === 'stable').length,
            };

            return `
            <div class="p-4 space-y-6">
                <div>
                    <label for="club-selector" class="text-sm text-secondary">Viewing Club</label>
                    <select id="club-selector" class="w-full bg-shot-surface p-2 rounded-md mt-1 font-bold" onchange="switchClub(this.value)">
                        <option value="Football" ${appState.currentClub === 'Football' ? 'selected' : ''}>Isleham FC (Football)</option>
                        <option value="Boxing" ${appState.currentClub === 'Boxing' ? 'selected' : ''}>East London Boxing</option>
                    </select>
                </div>

                <div class="bg-shot-red text-white p-4 rounded-lg shadow-lg">
                    <div class="flex justify-between items-center"><h3 class="font-poppins font-bold">⚡️ PENDING REVIEWS (${pendingReviewsCount})</h3><span class="text-sm">22h remaining</span></div>
                    <button class="w-full bg-gradient-red text-white font-bold py-2 rounded-lg mt-3 btn-interactive" onclick="navigateTo('evaluation-review')">REVIEW NOW</button>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-gradient-teal text-black p-4 rounded-lg cursor-pointer btn-interactive" onclick="navigateTo('team-dashboard', '${teamsForClub[0]?.id}')"><h4 class="font-poppins font-bold">🗓️ NEXT ${terminology.training.toUpperCase()}</h4><p>Fri 18:00 (2 days)</p></div>
                    <div class="bg-gradient-purple text-white p-4 rounded-lg cursor-pointer btn-interactive" onclick="navigateTo('team-dashboard', '${teamsForClub[0]?.id}')"><h4 class="font-poppins font-bold">${terminology.match_icon} NEXT ${terminology.match.toUpperCase()}</h4><p>vs Newmarket Town<br>Sat 14:00 (3 days)</p></div>
                </div>
                 <div class="bg-shot-surface p-4 rounded-lg">
                     <h4 class="font-poppins font-bold mb-2">📊 TEAM FORM</h4>
                     <div class="flex justify-around items-center text-center text-sm">
                         <div><p class="text-2xl">↗️</p><p class="font-bold">${teamForm.up}</p><p class="text-xs text-secondary">Improving</p></div>
                         <div><p class="text-2xl">↘️</p><p class="font-bold">${teamForm.down}</p><p class="text-xs text-secondary">Declining</p></div>
                         <div><p class="text-2xl">➡️</p><p class="font-bold">${teamForm.stable}</p><p class="text-xs text-secondary">Stable</p></div>
                     </div>
                 </div>
                <div>
                    <h3 class="font-poppins font-semibold text-lg mb-2">My ${terminology.squads}</h3>
                    ${teamsForClub.map(team => `
                        <div class="bg-shot-surface p-3 rounded-lg border-l-4 border-${team.color === 'gold' ? 'yellow-400' : `shot-${team.color}`} mb-2 cursor-pointer btn-interactive" onclick="navigateTo('team-dashboard', '${team.id}')">
                            <h4 class="font-poppins font-bold">${team.name}</h4>
                            <p class="text-xs text-secondary">${team.players.length} ${terminology.athletes} | ${team.evalRate}% Eval Rate | ${team.avgScore} Avg Score</p>
                        </div>`).join('')
                    }
                </div>
                <div>
                     <h3 class="font-poppins font-semibold text-lg mb-2">Recent Activity</h3>
                     <div class="bg-shot-surface p-3 rounded-lg space-y-2 text-sm">
                         ${mockData.pulseFeed.slice(0, 3).map(item => `
                             <div class="bg-shot-bg p-2 rounded-md flex items-center space-x-2">
                                 <i data-feather="${item.icon}" class="w-4 h-4 text-secondary"></i>
                                 <p class="flex-1"><span class="font-bold">${item.user}</span> <span class="text-secondary">${item.action}</span> <span class="text-shot-teal">${item.target || ''}</span></p>
                                 <span class="text-xs text-gray-500">${item.time}</span>
                             </div>
                         `).join('')}
                     </div>
                </div>
            </div>`;
        }

        function getTeamDashboardHTML() {
            const team = mockData.teams.find(t => t.id === appState.currentTeamId);
            if (!team) return placeholderPage("Team not found");
            const terminology = getTerminology(team.sport);
            const announcements = team.announcements || [];

            return `
                 <div class="p-4 space-y-6">
                     <div>
                         <h2 class="font-poppins font-bold text-2xl">${team.name}</h2>
                         <p class="text-secondary">${team.sport} ${terminology.squad}</p>
                     </div>

                     <div>
                         <div class="accordion-header bg-shot-teal text-black p-3 rounded-lg flex justify-between items-center cursor-pointer open" onclick="toggleAccordion(this)">
                             <h3 class="font-poppins font-semibold text-lg">Team Management</h3>
                              <i data-feather="chevron-down" class="icon-rotate"></i>
                         </div>
                         <div class="accordion-content bg-shot-surface rounded-b-lg p-4 open">
                             <div class="grid grid-cols-2 gap-4 text-sm">
                                 <button class="bg-shot-bg p-3 rounded-lg btn-interactive flex flex-col items-center justify-center space-y-2" onclick="navigateTo('team-squad')">
                                     <i data-feather="users"></i><span>Manage Squad</span>
                                 </button>
                                  <button class="bg-shot-bg p-3 rounded-lg btn-interactive flex flex-col items-center justify-center space-y-2" onclick="showModal(getInvitePlayersModalHTML())">
                                     <i data-feather="user-plus"></i><span>Invite Players</span>
                                 </button>
                                  <button class="bg-shot-bg p-3 rounded-lg btn-interactive flex flex-col items-center justify-center space-y-2" onclick="navigateTo('event-management')">
                                     <i data-feather="calendar"></i><span>Schedule</span>
                                 </button>
                                 <button class="bg-shot-bg p-3 rounded-lg btn-interactive flex flex-col items-center justify-center space-y-2" onclick="showModal(getAnnouncementModalHTML())">
                                     <i data-feather="megaphone"></i><span>Announce</span>
                                 </button>
                             </div>
                         </div>
                     </div>
                     ${announcements.length > 0 ? `
                     <div>
                         <h3 class="font-poppins font-semibold text-lg mb-2">Announcements</h3>
                         <div class="bg-shot-surface p-4 rounded-lg space-y-3">
                         ${announcements.map(ann => `
                             <div class="bg-shot-bg p-3 rounded-md text-sm">
                                 <p>${ann.text}</p>
                                 <p class="text-xs text-secondary mt-1">- ${ann.author}, ${ann.date}</p>
                             </div>`).join('')}
                         </div>
                     </div>` : ''}

                     <div>
                         <h3 class="font-poppins font-semibold text-lg mb-2">Upcoming Events</h3>
                         <div class="bg-shot-surface p-4 rounded-lg space-y-4">
                              <div>
                                 <h4 class="font-bold">${terminology.training_icon} ${terminology.training.toUpperCase()} - Defensive Drills</h4>
                                 <p class="text-sm text-secondary">Tomorrow 18:00</p>
                             </div>
                            <div class="border-t border-gray-700 pt-4">
                                 <h4 class="font-bold">${terminology.match_icon} ${terminology.match.toUpperCase()} - vs Soham Town (Away)</h4>
                                 <p class="text-sm text-secondary">Saturday 14:00</p>
                             </div>
                         </div>
                     </div>
                 </div>
            `;
        }

        function getTeamSquadHTML() {
            const team = mockData.teams.find(t => t.id === appState.currentTeamId);
            const terminology = getTerminology(team.sport);
            const positions = team.sport === 'Football' ? {
                '🥅 Goalkeepers': team.players.filter(p => p.pos === 'GK'),
                '🛡️ Defenders': team.players.filter(p => p.pos === 'CB' || p.pos === 'LB' || p.pos === 'RB'),
                '⚔️ Midfielders': team.players.filter(p => p.pos === 'CAM' || p.pos === 'CDM' || p.pos === 'MID'),
                '🎯 Forwards': team.players.filter(p => p.pos === 'FWD'),
            } : {
                [`🥊 ${terminology.athletes}`]: team.players
            };

            return `
                 <div class="p-4 pb-20">
                     <h2 class="font-poppins font-bold text-2xl">${team.name} ${terminology.squad}</h2>
                     <div class="space-y-2 mt-4">
                     ${Object.entries(positions).map(([pos, players]) => players.length > 0 ? `
                         <div>
                             <div class="accordion-header bg-shot-surface p-3 rounded-lg flex justify-between items-center cursor-pointer open" onclick="toggleAccordion(this)">
                                 <h3 class="font-poppins font-semibold text-base">${pos} (${players.length})</h3>
                                 <i data-feather="chevron-down" class="icon-rotate"></i>
                             </div>
                             <div class="accordion-content space-y-px open bg-shot-surface rounded-b-lg p-2">
                             ${players.map(p => `
                                 <div class="bg-shot-bg p-3 rounded-lg flex items-center justify-between">
                                     <div class="flex items-center space-x-3">
                                         <img src="${p.avatar}" class="w-10 h-10 rounded-full object-cover">
                                         <div><p class="font-semibold">${p.name} ${p.keyPlayer ? '⭐' : ''}</p><p class="text-xs text-secondary">${p.age}, ${p.pos}</p></div>
                                     </div>
                                     <div class="flex items-center space-x-3">
                                         ${getTrendIndicator(p.trend)}
                                         <button class="text-xs bg-gray-700 rounded-md px-3 py-1 btn-interactive" onclick="navigateTo('player-analytics', '${p.id}')">View</button>
                                     </div>
                                 </div>`).join('')}
                             </div>
                         </div>
                     ` : '').join('')}
                     </div>
                 </div>
                 <div class="fixed bottom-16 left-1/2 -translate-x-1/2 w-full max-w-lg p-4 bg-shot-surface/80 backdrop-blur-sm border-t border-gray-800">
                     <div class="grid grid-cols-2 gap-4">
                         <button class="bg-gradient-teal text-black font-bold py-3 rounded-lg btn-interactive" onclick="showModal(getAnnouncementModalHTML())">Send Announcement</button>
                         <button class="bg-gradient-purple text-white font-bold py-3 rounded-lg btn-interactive" onclick="showModal(getAttendanceModalHTML(), true)">Mark Attendance</button>
                     </div>
                 </div>
                 `;
        }
        
        function getEventManagementHTML() { 
            const team = mockData.teams.find(t => t.id === appState.currentTeamId);
            const terminology = getTerminology(team.sport);
            const presentCount = Object.values(appState.attendance).filter(s => s === 'present').length;
            const absentCount = Object.values(appState.attendance).filter(s => s === 'absent').length;
            const noResponseCount = team.players.length - presentCount - absentCount;

            return `
                 <div class="p-4 space-y-6">
                     <h2 class="font-poppins font-bold text-2xl">Manage Schedule</h2>
                     <p class="text-secondary -mt-4">${team.name}</p>
                    <div>
                         <div id="event-management-accordion" class="accordion-header bg-shot-surface p-3 rounded-lg flex justify-between items-center cursor-pointer" onclick="toggleAccordion(this)">
                             <h3 class="font-poppins font-semibold text-lg">Create New Event</h3>
                              <i data-feather="plus-circle" class="icon-rotate"></i>
                         </div>
                         <div class="accordion-content bg-shot-surface rounded-b-lg p-4 space-y-4">
                             <div>
                                 <label class="text-sm text-secondary">Event Type</label>
                                 <div class="flex space-x-2 mt-1">
                                     <button class="px-3 py-1 text-sm rounded-full bg-shot-teal text-black">${terminology.training}</button>
                                     <button class="px-3 py-1 text-sm rounded-full bg-shot-surface text-secondary">${terminology.match}</button>
                                 </div>
                             </div>
                             <div><label class="text-sm text-secondary">Title</label><input type="text" class="w-full bg-shot-bg p-2 rounded-md mt-1" value="Defensive Drills Session"></div>
                             <div class="grid grid-cols-2 gap-4">
                                 <div><label class="text-sm text-secondary">Date</label><input type="text" class="w-full bg-shot-bg p-2 rounded-md mt-1" value="31 Jul 2025"></div>
                                 <div><label class="text-sm text-secondary">Time</label><input type="text" class="w-full bg-shot-bg p-2 rounded-md mt-1" value="18:00"></div>
                             </div>
                             <button class="w-full bg-gradient-teal text-black font-bold py-3 rounded-lg btn-interactive" onclick="createEvent()">CREATE EVENT</button>
                         </div>
                    </div>

                     <div>
                         <h3 class="font-poppins font-semibold text-lg mb-2 flex justify-between items-center">
                             <span>Upcoming Events</span>
                             <button class="text-xs bg-gray-700 rounded-md px-3 py-1 btn-interactive" onclick="navigateTo('event-history')">View History</button>
                         </h3>
                         <div class="bg-shot-surface p-4 rounded-lg space-y-4">
                             <div>
                                 <h4 class="font-bold">${terminology.training_icon} ${terminology.training.toUpperCase()} - Defensive Drills</h4>
                                 <p class="text-sm text-secondary">Tomorrow 18:00 | Isleham Rec Ground</p>
                                 <div class="bg-shot-bg p-3 rounded-md mt-2 text-xs space-y-1">
                                     <p>Available: ${presentCount}/${team.players.length} ✅</p>
                                     <p>Unavailable: ${absentCount}/${team.players.length} ❌</p>
                                     <p>No Response: ${noResponseCount}/${team.players.length} ⏳</p>
                                 </div>
                                 <div class="grid grid-cols-3 gap-2 text-sm font-semibold mt-3">
                                     <button class="bg-gray-700 py-2 rounded-md btn-interactive" onclick="showModal(getAttendanceModalHTML(), true)">Attendance</button>
                                     <button class="bg-gray-700 py-2 rounded-md btn-interactive" onclick="showModal(getEvaluationStatusModalHTML(), true)">Evals</button>
                                     <button class="bg-gray-700 py-2 rounded-md btn-interactive" onclick="showModal(getMatchDetailsModalHTML())">Details</button>
                                 </div>
                             </div>
                             <div class="border-t border-gray-700 pt-4">
                                 <h4 class="font-bold">${terminology.match_icon} ${terminology.match.toUpperCase()} - vs Soham Town (Away)</h4>
                                 <p class="text-sm text-secondary">Saturday 14:00 | Soham Sports Centre</p>
                                  <div class="grid grid-cols-3 gap-2 text-sm font-semibold mt-3">
                                     <button class="bg-gray-700 py-2 rounded-md btn-interactive" onclick="showModal(getAttendanceModalHTML(), true)">Attendance</button>
                                     <button class="bg-gray-700 py-2 rounded-md btn-interactive" onclick="showModal(getEvaluationStatusModalHTML(), true)">Evals</button>
                                     <button class="bg-gray-700 py-2 rounded-md btn-interactive" onclick="showModal(getMatchDetailsModalHTML())">Details</button>
                                 </div>
                             </div>
                         </div>
                     </div>
                 </div>
            `;
        }

        function getEventHistoryHTML() {
            const team = mockData.teams.find(t => t.id === appState.currentTeamId);
            const terminology = getTerminology(team.sport);
            return `
            <div class="p-4 space-y-4">
                <div>
                    <h2 class="font-poppins font-bold text-2xl">Event History</h2>
                    <p class="text-secondary">${team.name}</p>
                </div>
                <div class="space-y-3">
                    <div class="bg-shot-surface p-3 rounded-lg">
                        <p class="font-semibold">${terminology.match_icon} ${terminology.match} vs Cambridge City (Home)</p>
                        <p class="text-sm text-secondary">26 Jul 2025</p>
                        <div class="text-xs mt-2 p-2 bg-shot-bg rounded-md flex justify-between items-center">
                            <span>Result: <span class="font-bold text-green-400">WON 2-1</span></span>
                            <span>Evals: <span class="font-bold">18/18</span></span>
                        </div>
                    </div>
                    <div class="bg-shot-surface p-3 rounded-lg">
                        <p class="font-semibold">${terminology.training_icon} ${terminology.training} - Attacking Drills</p>
                        <p class="text-sm text-secondary">24 Jul 2025</p>
                         <div class="text-xs mt-2 p-2 bg-shot-bg rounded-md flex justify-between items-center">
                            <span>Attendance: <span class="font-bold">19/22</span></span>
                            <span>Evals: <span class="font-bold">19/19</span></span>
                        </div>
                    </div>
                </div>
            </div>`;
        }


        function getEvaluationReviewListHTML() {
            const teamsForClub = mockData.teams.filter(team => team.sport === appState.currentClub);
            const athletesToReview = teamsForClub.flatMap(t => t.players).filter(p => p.evalStatus === 'Pending Review');

            if (athletesToReview.length === 0) {
                 return `<div class="p-4 text-center">
                     <h2 class="font-poppins font-bold text-2xl">Pending Reviews</h2>
                     <p class="text-secondary mt-4">🎉 No pending reviews for ${appState.currentClub}. All caught up!</p>
                 </div>`;
            }

            return `
                 <div class="p-4 space-y-4">
                     <h2 class="font-poppins font-bold text-2xl text-center">Pending Reviews for ${appState.currentClub}</h2>
                     ${athletesToReview.map(athlete => {
                         const team = mockData.teams.find(t => t.players.some(p => p.id === athlete.id));
                         return `
                         <div class="bg-shot-surface p-3 rounded-lg">
                             <div class="flex items-center justify-between">
                                 <div class="flex items-center space-x-3">
                                     <img src="${athlete.avatar}" class="w-12 h-12 rounded-full object-cover">
                                     <div>
                                         <p class="font-semibold">${athlete.name}</p>
                                         <p class="text-xs text-secondary">${team.name}</p>
                                     </div>
                                 </div>
                                 <button class="bg-gradient-red text-white font-bold py-2 px-4 rounded-lg btn-interactive" onclick="navigateTo('evaluation-detail', '${athlete.id}')">Review</button>
                             </div>
                         </div>
                         `
                     }).join('')}
                 </div>
            `;
        }

        function getEvaluationDetailHTML() { 
            const athlete = mockData.teams.flatMap(t => t.players).find(p => p.id === appState.currentAthleteId);
            if (!athlete) return placeholderPage('Athlete not found');

            const team = mockData.teams.find(t => t.players.some(p => p.id === athlete.id));
            const framework = mockData.evaluationFrameworks[team.sport];
            const monthlyFocus = mockData.monthlyFocus[team.sport];

            return `
                 <div class="p-4 space-y-4">
                     <div class="text-center">
                         <h2 class="font-poppins font-bold text-xl">Evaluation for ${athlete.name}</h2>
                         <p class="text-secondary text-sm">${team.sport} Event | Training: Defensive Drills</p>
                         <p class="text-shot-red font-semibold mt-1">⏰ Deadline: 18h remaining</p>
                     </div>
                     
                     <div class="bg-shot-surface p-4 rounded-lg">
                         <h3 class="font-poppins font-semibold text-center mb-3">Player's Self-Assessment</h3>
                         <div class="grid grid-cols-3 gap-2 text-center text-xs font-bold text-secondary">
                             <span>CORNER</span><span>PRE-EVENT</span><span>POST-EVENT</span>
                         </div>
                         ${Object.values(framework).map(corner => `
                         <div class="grid grid-cols-3 gap-2 items-center mt-2">
                             <span class="text-sm text-secondary">${corner.label}</span>
                              <div class="flex items-center gap-2"><div class="bg-shot-bg rounded-full h-2.5 w-full"><div class="bg-blue-500 h-2.5 rounded-full" style="width: ${4.0/5*100}%"></div></div><span class="text-xs font-bold">4.0</span></div>
                              <div class="flex items-center gap-2"><div class="bg-shot-bg rounded-full h-2.5 w-full"><div class="bg-green-500 h-2.5 rounded-full" style="width: ${3.0/5*100}%"></div></div><span class="text-xs font-bold">3.0</span></div>
                         </div>
                         `).join('')}
                         <p class="text-sm text-secondary mt-4 bg-shot-bg p-2 rounded-md"><strong>Notes:</strong> "Felt strong but struggled with some of the defensive positioning in final third"</p>
                     </div>

                     <div class="bg-shot-surface p-4 rounded-lg space-y-4">
                         <h3 class="font-poppins font-semibold text-center mb-3">Coach's Assessment</h3>
                         ${Object.entries(framework).map(([cornerKey, cornerData]) => {
                             const focus = monthlyFocus[cornerKey];
                             return `
                            <div class="mt-4">
                                <h4 class="font-poppins font-semibold" style="color: ${cornerData.color};">${cornerData.icon} ${cornerData.label}: <span class="font-normal text-sm text-secondary">${focus.label}</span></h4>
                                <div class="mt-2">
                                    <div class="flex justify-between items-center">
                                        <label class="font-semibold text-sm">${focus.coach}</label>
                                        <span class="font-bold text-lg" style="color: ${cornerData.color};" id="slider-value-${cornerKey}">3.5</span>
                                    </div>
                                    <input type="range" min="1" max="5" value="3.5" step="0.5" class="slider-input mt-2" style="--shot-teal: ${cornerData.color};" oninput="document.getElementById('slider-value-${cornerKey}').textContent = parseFloat(this.value).toFixed(1)">
                                </div>
                            </div>
                        `}).join('')}
                         <div>
                             <label class="font-semibold text-sm">📝 OVERALL PERFORMANCE</label>
                             <textarea class="w-full p-2 rounded-md mt-2 text-sm" rows="3" placeholder="Summarize the performance..."></textarea>
                         </div>
                         <div>
                             <label class="font-semibold text-sm">🎯 NEXT FOCUS</label>
                             <input type="text" class="w-full p-2 rounded-md mt-2 text-sm" placeholder="e.g., Work on weak foot passing">
                         </div>
                          <button class="w-full bg-gradient-teal text-black font-bold py-3 rounded-lg btn-interactive" onclick="submitCoachEvaluation('${athlete.id}')">SUBMIT EVALUATION</button>
                     </div>
                 </div>
            `;
        }

        function getPlayerAnalyticsHTML() {
           const athlete = mockData.teams.flatMap(t => t.players).find(p => p.id === appState.currentAthleteId);
           const team = mockData.teams.find(t => t.players.some(p => p.id === appState.currentAthleteId));
           const terminology = getTerminology(team.sport);
           const history = mockData.historicalData[appState.currentAthleteId];

           return `
            <div class="p-4 space-y-4">
                <div>
                    <h2 class="font-poppins font-bold text-2xl">${athlete.name} (${athlete.age})</h2>
                    <p class="text-secondary mb-2">${athlete.pos} | Form: ${getTrendIndicator(athlete.trend)}</p>
                </div>
                <div class="bg-shot-surface rounded-xl p-4">
                    <h3 class="font-poppins font-semibold">Four Corners Development Chart</h3>
                    <div class="h-64 mt-4"><canvas id="player-progress-chart"></canvas></div>
                </div>
                <div class="bg-shot-surface rounded-xl p-4">
                    <h3 class="font-poppins font-semibold mb-3">Recent Evaluation History</h3>
                    <div class="space-y-3">
                        ${Object.keys(history).slice(-2).reverse().map(monthKey => {
                            const eventData = history[monthKey];
                            return `<div class="text-sm">
                                <p><strong>${new Date(monthKey + '-15').toLocaleString('default', { month: 'short', day: 'numeric' })}: ${terminology.training}</strong> <span class="font-bold">${getTrendIcon(eventData.trend)}</span></p>
                                <p class="text-secondary mt-1 bg-shot-bg p-2 rounded-md">💬 "${eventData.comment}"</p>
                            </div>`
                        }).join('')}
                    </div>
                     <button class="text-center w-full mt-4 text-shot-teal font-bold text-sm py-2 rounded-lg btn-interactive" onclick="navigateTo('player-performance-history')">VIEW FULL HISTORY</button>
                </div>
                 <div class="bg-shot-surface rounded-xl p-4 space-y-3">
                    <h3 class="font-poppins font-semibold">Coach Notes & Development</h3>
                    <div class="space-y-3">
                         <div>
                            <h4 class="text-sm font-bold mb-1">🔒 PRIVATE COACH NOTES</h4>
                            <ul class="list-disc list-inside text-xs text-secondary space-y-1 bg-shot-bg p-2 rounded-md">
                                <li>Showing real leadership qualities lately.</li>
                                <li>Worth considering for captain next season.</li>
                            </ul>
                        </div>
                         <div>
                            <h4 class="text-sm font-bold mb-1">💬 PUBLIC FEEDBACK HISTORY</h4>
                            <ul class="list-disc list-inside text-xs text-secondary space-y-1 bg-shot-bg p-2 rounded-md">
                                <li>"Fantastic month. You've become a key player."</li>
                                <li>"Excellent improvement in your tactical awareness."</li>
                            </ul>
                        </div>
                    </div>
                     <div class="grid grid-cols-2 gap-2 mt-4">
                        <button class="w-full bg-gray-700 py-2 rounded-lg btn-interactive text-sm" onclick="showModal(getAddPrivateNoteModalHTML('${appState.currentAthleteId}'))">Add Private Note</button>
                        <button class="w-full bg-gray-700 py-2 rounded-lg btn-interactive text-sm" onclick="showModal(getSendPublicFeedbackModalHTML('${appState.currentAthleteId}'))">Send Public Feedback</button>
                    </div>
                </div>
            </div>`;
       }

        function getAthleteDashboardHTML() {
            const athleteData = mockData.teams.flatMap(t => t.players).find(p => p.id === appState.currentPlayerProfileId);
            if (!athleteData) return placeholderPage("Athlete not found");

            const team = mockData.teams.find(t => t.players.some(p => p.id === athleteData.id));
            if (!team) return placeholderPage("Athlete's team not found");
            
            const framework = mockData.evaluationFrameworks[team.sport];
            const terminology = getTerminology(team.sport);
            const monthlyData = mockData.historicalData[athleteData.id]?.['2025-07'];
            const announcements = team.announcements || [];

            return `
            <div class="p-4 space-y-6">
                 <div>
                     <label class="text-sm text-secondary mb-2 block">Viewing profile:</label>
                     <div class="flex space-x-4 overflow-x-auto pb-2 hide-scrollbar">
                         ${mockData.superUser.profiles.map(profile => {
                             const player = mockData.teams.flatMap(t => t.players).find(p => p.id === profile.id);
                             const playerTeam = mockData.teams.find(t => t.players.some(p => p.id === profile.id));
                             const isSelected = profile.id === appState.currentPlayerProfileId;
                             return `
                             <div class="flex-shrink-0 w-48 bg-shot-surface rounded-lg p-3 cursor-pointer ${isSelected ? 'border-2 border-shot-teal' : ''}" onclick="navigateTo('player-dashboard', {playerProfileId: '${profile.id}'})">
                                 <p class="font-bold text-white">${player.name}</p>
                                 <p class="text-xs text-secondary">${playerTeam.name}</p>
                                 <p class="text-xs text-secondary mt-1">${playerTeam.sport}</p>
                             </div>
                             `
                         }).join('')}
                     </div>
                 </div>

                <div class="bg-shot-surface border-l-4 border-shot-red p-4 rounded-lg shadow-lg">
                    <div class="flex items-center space-x-3">
                        <i data-feather="alert-triangle" class="text-shot-red h-8 w-8"></i>
                        <div>
                            <h3 class="font-poppins font-bold text-white">POST-EVALUATION DUE</h3>
                            <p class="text-sm text-secondary">${terminology.match} vs Cambridge City</p>
                        </div>
                    </div>
                    <button class="w-full bg-gradient-red text-white font-bold py-2 rounded-lg mt-3 btn-interactive" onclick="showModal(getMatchEvaluationSetupModalHTML())">START EVALUATION (4h left)</button>
                </div>
                
                 ${announcements.length > 0 ? `
                 <div>
                     <h3 class="font-poppins font-semibold text-lg mb-2">Announcements</h3>
                     <div class="bg-shot-surface p-4 rounded-lg space-y-3">
                     ${announcements.map(ann => `
                         <div class="bg-shot-bg p-3 rounded-md text-sm">
                             <p>${ann.text}</p>
                             <p class="text-xs text-secondary mt-1">- ${ann.author}, ${ann.date}</p>
                         </div>`).join('')}
                     </div>
                 </div>` : ''}

                <div class="bg-shot-surface rounded-xl p-4">
                     <h4 class="font-poppins font-bold mb-2">July 2025 Performance</h4>
                     <p class="text-xs text-secondary -mt-2 mb-3">YOUR MONTHLY AVERAGES ${getTrendIcon(athleteData.trend)}</p>
                     <div class="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                         ${monthlyData ? Object.keys(framework).map(key => `
                             <span>${framework[key].icon} ${framework[key].label}</span>
                             <span class="text-right font-semibold text-shot-gold">${(monthlyData.scores[key]).toFixed(1)}</span>
                         `).join('') : '<p class="text-secondary col-span-2">No data for this month</p>'}
                     </div>
                      <p class="text-xs text-secondary mt-3">🔥 3-day evaluation streak</p>
                 </div>

                <div class="bg-shot-surface rounded-xl p-4">
                     <h4 class="font-poppins font-bold mb-2">Upcoming Events</h4>
                      <div class="space-y-3 text-sm">
                          <div>
                              <p><strong>${terminology.training_icon} Tomorrow:</strong> ${terminology.training} - Defensive Drills 18:00</p>
                              <p class="text-xs text-secondary">Status: Available ✅ | Isleham Rec Ground</p>
                          </div>
                          <div class="border-t border-gray-700 pt-3">
                              <p><strong>${terminology.match_icon} Saturday:</strong> ${terminology.match} vs Soham Town (Away) 14:00</p>
                              <p class="text-xs text-secondary">Status: Available ✅ | Travel: Coach provided</p>
                          </div>
                      </div>
                     <button class="w-full text-center mt-3 bg-gray-700 text-white font-bold text-sm py-2 rounded-lg btn-interactive" onclick="showModal(getUpdateAvailabilityModalHTML())">Update Availability</button>
                </div>

                <div class="bg-shot-surface rounded-xl p-4">
                    <h4 class="font-poppins font-bold mb-2">Recent Coach Feedback</h4>
                    <div class="bg-shot-bg p-3 rounded-md text-sm">
                        <p class="text-secondary">"${monthlyData?.comment || 'No recent feedback.'}"</p>
                        <p class="text-xs text-gray-500 mt-1">- Coach Alex, 28 Jul 2025</p>
                    </div>
                </div>
                <div class="grid grid-cols-3 gap-2 text-sm text-center font-bold">
                    <button class="bg-shot-teal text-black p-3 rounded-lg btn-interactive" onclick="showModal(getMatchEvaluationSetupModalHTML())">Complete Evaluation</button>
                    <button class="bg-shot-purple text-white p-3 rounded-lg btn-interactive" onclick="navigateTo('player-performance-history')">View Progress</button>
                    <button class="bg-shot-gold text-black p-3 rounded-lg btn-interactive" onclick="navigateTo('team-chat')">Team Chat</button>
                </div>
            </div>`;
        }


        function getParentDashboardHTML() { 
            const currentChild = mockData.teams.flatMap(t => t.players).find(p => p.id === appState.currentChildId);
            if (!currentChild) return placeholderPage("Child not found");

            const team = mockData.teams.find(t => t.players.some(p => p.id === currentChild.id));
            const framework = mockData.evaluationFrameworks[team.sport];
            const terminology = getTerminology(team.sport);
            const monthlyData = mockData.historicalData[currentChild.id]?.['2025-07'];
            const announcements = team.announcements || [];


            return `
             <div class="p-4 space-y-6">
                 <div>
                     <label class="text-sm text-secondary mb-2 block">Viewing athlete:</label>
                     <div class="flex space-x-4 overflow-x-auto pb-2 hide-scrollbar">
                         ${mockData.parentData.children.map(child => {
                             const childTeam = mockData.teams.find(t => t.players.some(p => p.id === child.id));
                             const isSelected = child.id === appState.currentChildId;
                             return `
                             <div class="flex-shrink-0 w-48 bg-shot-surface rounded-lg p-3 cursor-pointer ${isSelected ? 'border-2 border-shot-teal' : ''}" onclick="navigateTo('parent-dashboard', {childId: '${child.id}'})">
                                 <p class="font-bold text-white">${child.name}</p>
                                 <p class="text-xs text-secondary">${childTeam.name}</p>
                                 <p class="text-xs text-secondary mt-1">${childTeam.sport}</p>
                             </div>
                             `
                         }).join('')}
                     </div>
                 </div>
                 
                 ${announcements.length > 0 ? `
                 <div>
                     <h3 class="font-poppins font-semibold text-lg mb-2">Announcements</h3>
                     <div class="bg-shot-surface p-4 rounded-lg space-y-3">
                     ${announcements.map(ann => `
                         <div class="bg-shot-bg p-3 rounded-md text-sm">
                             <p>${ann.text}</p>
                             <p class="text-xs text-secondary mt-1">- ${ann.author}, ${ann.date}</p>
                         </div>`).join('')}
                     </div>
                 </div>` : ''}

                 <div class="bg-shot-surface rounded-xl p-4">
                     <h3 class="font-poppins font-bold mb-2">${currentChild.name}'s July Performance ${getTrendIcon(currentChild.trend)}</h3>
                      <p class="text-xs text-secondary -mt-2 mb-3">ATHLETE'S MONTHLY PROGRESS</p>
                     <div class="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                         ${monthlyData ? Object.keys(framework).map(key => `
                             <span>${framework[key].icon} ${framework[key].label}</span>
                             <span class="text-right font-semibold text-shot-gold">${(monthlyData.scores[key]).toFixed(1)}</span>
                          `).join('') : '<p class="col-span-2 text-secondary">No data for this month.</p>'}
                     </div>
                     <p class="text-xs text-secondary mt-3">🎯 Most improved area: Technical (+0.4)</p>
                     <button class="w-full text-center mt-4 bg-gradient-purple text-white font-bold text-sm py-2 rounded-lg btn-interactive" onclick="navigateTo('player-performance-history')">VIEW FULL PROGRESS</button>
                 </div>

                 <div class="bg-shot-surface rounded-xl p-4">
                     <h4 class="font-poppins font-bold mb-2">Recent Coach Feedback</h4>
                     <div class="bg-shot-bg p-3 rounded-md text-sm">
                         <p class="text-secondary">${monthlyData?.comment || "No recent comments."}</p>
                         <p class="text-xs text-gray-500 mt-1">- Coach Alex, 28 Jul 2025</p>
                     </div>
                     <button class="w-full text-center mt-3 bg-gray-700 text-white font-bold text-sm py-2 rounded-lg btn-interactive" onclick="showModal(getCoachMessageModalHTML())">Message Coach</button>
                 </div>

                 <div class="bg-shot-surface rounded-xl p-4">
                     <h3 class="font-poppins font-bold mb-2">Team & Club Information</h3>
                     <div class="text-sm space-y-1 text-secondary bg-shot-bg p-3 rounded-lg">
                         <p><strong>Coach:</strong> Alex Taylor</p>
                         <p><strong>Training:</strong> Fri 18:00, Sun 11:00</p>
                         <p><strong>Location:</strong> Isleham Rec Ground</p>
                         <p><strong>Emergency Contact:</strong> You (Sarah Williams)</p>
                     </div>
                 </div>
                 
                 <div class="bg-shot-surface rounded-xl p-4">
                     <h3 class="font-poppins font-bold mb-2">🛡️ Privacy & Safety Status</h3>
                     <div class="text-sm space-y-2 text-secondary bg-shot-bg p-3 rounded-lg">
                          <p class="flex items-center gap-2"><i data-feather="check-circle" class="w-4 h-4 text-green-400"></i> All permissions approved.</p>
                          <p class="flex items-center gap-2"><i data-feather="check-circle" class="w-4 h-4 text-green-400"></i> Contact details up to date.</p>
                     </div>
                      <button class="w-full bg-gray-700 py-2 rounded-lg btn-interactive text-sm mt-3" onclick="navigateTo('parent-manage-children')">Manage Children & Permissions</button>
                 </div>

             </div>`; 
        }

        function getParentManageChildrenHTML() {
            const { children } = mockData.parentData;
            return `
            <div class="p-4 space-y-6">
                <div>
                    <h2 class="font-poppins font-bold text-2xl">Manage Children</h2>
                    <p class="text-secondary">Link new children or manage existing connections.</p>
                </div>
                <div class="space-y-3">
                    ${children.map(child => {
                        const athlete = mockData.teams.flatMap(t => t.players).find(p => p.id === child.id);
                        const team = mockData.teams.find(t => t.players.some(p => p.id === child.id));
                        return `
                        <div class="bg-shot-surface p-3 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <img src="${athlete.avatar}" class="w-12 h-12 rounded-full object-cover">
                                    <div>
                                        <p class="font-semibold">${athlete.name}</p>
                                        <p class="text-xs text-secondary">${team.name}</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                   <button class="text-xs bg-shot-red/20 text-shot-red rounded-md px-3 py-1 btn-interactive">Unlink</button>
                                   <button class="text-xs bg-gray-700 rounded-md px-3 py-1 btn-interactive" onclick="navigateTo('parent-dashboard', {childId: '${child.id}'})">View</button>
                                </div>
                            </div>
                        </div>
                        `;
                    }).join('')}
                </div>
                 <div class="bg-shot-surface p-4 rounded-lg text-center">
                    <h3 class="font-poppins font-semibold">Link a New Child</h3>
                    <p class="text-secondary text-sm mt-1">Enter the unique code from your child's SHOT profile.</p>
                    <input id="link-child-input" type="text" class="w-full bg-shot-bg p-2 rounded-md mt-3 text-center tracking-widest font-bold" placeholder="ABC-123">
                    <button class="w-full bg-gradient-teal text-black font-bold py-2 rounded-lg mt-3 btn-interactive" onclick="linkChild()">LINK CHILD</button>
                </div>
            </div>`;
        }

        function getMyTimelineHTML() { 
            return `
            <div class="p-4 space-y-4">
                <h2 class="font-poppins font-bold text-2xl">My Timeline</h2>
                <p class="text-secondary -mt-2">Your recent activity and SP earnings.</p>
                <div class="space-y-3">
                    <div class="bg-shot-surface p-3 rounded-lg flex justify-between items-center">
                        <div>
                            <p class="font-semibold">Completed Match Evaluation</p>
                            <p class="text-xs text-secondary">vs Soham Town | 27 Jul 2025</p>
                        </div>
                        <span class="font-bold text-shot-gold">+100 SP</span>
                    </div>
                     <div class="bg-shot-surface p-3 rounded-lg flex justify-between items-center">
                        <div>
                            <p class="font-semibold">Attended Training Session</p>
                            <p class="text-xs text-secondary">Defensive Drills | 26 Jul 2025</p>
                        </div>
                        <span class="font-bold text-shot-gold">+50 SP</span>
                    </div>
                     <div class="bg-shot-surface p-3 rounded-lg flex justify-between items-center">
                        <div>
                            <p class="font-semibold">Purchased SHOT Tech Enabled Cap</p>
                            <p class="text-xs text-secondary">Locker Store | 25 Jul 2025</p>
                        </div>
                        <span class="font-bold text-shot-gold">+50 SP</span>
                    </div>
                     <div class="bg-shot-surface p-3 rounded-lg flex justify-between items-center">
                        <div>
                            <p class="font-semibold">Entered prize draw</p>
                            <p class="text-xs text-secondary">Champions League Tickets | 24 Jul 2025</p>
                        </div>
                        <span class="font-bold text-shot-red">-100 SP</span>
                    </div>
                </div>
            </div>`;
        }
        
        function getMyRewardsHTML() {
            return `
                <div class="p-4 space-y-4">
                    <h2 class="font-poppins font-bold text-2xl">My Rewards</h2>
                    <p class="text-secondary -mt-2">Use your SP to unlock exclusive rewards.</p>
                    <div class="space-y-3">
                        <div class="bg-shot-surface p-4 rounded-lg">
                            <h3 class="font-bold text-lg">15% Off at Kick-Off Cafe</h3>
                            <p class="text-sm text-secondary mt-1">Get 15% off your next order at the best local sports cafe.</p>
                            <div class="flex justify-between items-center mt-3">
                                <span class="font-bold text-shot-gold">500 SP</span>
                                <button class="bg-gradient-teal text-black font-bold py-1 px-4 rounded-lg btn-interactive text-sm">Claim</button>
                            </div>
                        </div>
                         <div class="bg-shot-surface p-4 rounded-lg">
                            <h3 class="font-bold text-lg">Entry to win CL Final Tickets</h3>
                            <p class="text-sm text-secondary mt-1">Enter the prize draw for two tickets to the Champions League Final.</p>
                            <div class="flex justify-between items-center mt-3">
                                <span class="font-bold text-shot-gold">1,000 SP</span>
                                <button class="bg-gradient-teal text-black font-bold py-1 px-4 rounded-lg btn-interactive text-sm">Claim</button>
                            </div>
                        </div>
                         <div class="bg-shot-surface p-4 rounded-lg">
                            <h3 class="font-bold text-lg">£20 SHOT Merchandise Voucher</h3>
                            <p class="text-sm text-secondary mt-1">Get £20 off any item in the SHOT Locker.</p>
                            <div class="flex justify-between items-center mt-3">
                                <span class="font-bold text-shot-gold">2,000 SP</span>
                                <button class="bg-gray-600 text-gray-400 font-bold py-1 px-4 rounded-lg text-sm cursor-not-allowed">Claim</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function getPlayerPreEvaluationHTML() {
             const athleteData = mockData.teams.flatMap(t => t.players).find(p => p.id === appState.currentPlayerProfileId);
             if (!athleteData) return placeholderPage("Athlete not found");

            const team = mockData.teams.find(t => t.players.some(p => p.id === athleteData.id));
            if (!team) return placeholderPage("Athlete's team not found");
            const framework = mockData.evaluationFrameworks[team.sport];
            const monthlyFocus = mockData.monthlyFocus[team.sport];
            const terminology = getTerminology(team.sport);

            return `
            <div class="p-4 space-y-6">
                <div class="text-center">
                    <h2 class="font-poppins font-bold text-xl">Pre-Event Evaluation</h2>
                    <p class="text-secondary text-sm">${terminology.training}: Defensive Drills</p>
                    <p class="text-shot-red font-semibold mt-1">⏰ Complete by: Tomorrow 18:00</p>
                </div>

                <div class="bg-shot-surface p-4 rounded-lg space-y-4">
                    <h3 class="font-poppins font-semibold text-center mb-3">This month's focus areas:</h3>
                    ${Object.entries(framework).map(([cornerKey, cornerData]) => {
                        const focus = monthlyFocus[cornerKey];
                        return `
                        <div class="space-y-3">
                            <h4 class="font-poppins font-semibold text-md pt-2" style="color: ${cornerData.color};">${cornerData.icon} ${cornerData.label}: <span class="font-normal text-sm text-secondary">${focus.label}</span></h4>
                            <div>
                                <div class="flex justify-between items-center">
                                    <label class="font-semibold text-sm text-secondary">${focus.pre}</label>
                                    <span class="font-bold text-lg" style="color: ${cornerData.color};" id="slider-value-${cornerKey}">3.0</span>
                                </div>
                                <input type="range" min="1" max="5" value="3" step="0.5" class="slider-input mt-2" style="--shot-teal: ${cornerData.color};" oninput="document.getElementById('slider-value-${cornerKey}').textContent = parseFloat(this.value).toFixed(1)">
                            </div>
                        </div>
                    `}).join('')}
                    <div>
                        <label class="font-semibold text-sm">💭 Any thoughts or concerns for the session?</label>
                        <textarea class="w-full p-2 rounded-md mt-2 text-sm" rows="3" placeholder="e.g., Feeling a bit tired today..."></textarea>
                    </div>
                    <button class="w-full bg-gradient-teal text-black font-bold py-3 rounded-lg btn-interactive" onclick="goBack()">SUBMIT EVALUATION</button>
                </div>
            </div>`;
        }
        
        function getPlayerPostEvaluationHTML() {
            const athleteData = mockData.teams.flatMap(t => t.players).find(p => p.id === appState.currentPlayerProfileId);
             if (!athleteData) return placeholderPage("Athlete not found");

            const team = mockData.teams.find(t => t.players.some(p => p.id === athleteData.id));
            if (!team) return placeholderPage("Athlete's team not found");
            const framework = mockData.evaluationFrameworks[team.sport];
            const monthlyFocus = mockData.monthlyFocus[team.sport];
            const terminology = getTerminology(team.sport);

            return `
            <div class="p-4 space-y-6">
                <div class="text-center">
                    <h2 class="font-poppins font-bold text-xl">Post-Event Evaluation</h2>
                    <p class="text-secondary text-sm">${terminology.match}: vs Cambridge City</p>
                    <p class="text-shot-red font-semibold mt-1">⏰ Complete by: Today 22:00</p>
                </div>

                <div class="bg-shot-surface p-4 rounded-lg space-y-4">
                    <h3 class="font-poppins font-semibold text-center mb-3">Rate your performance on this month's focus areas:</h3>
                     ${Object.entries(framework).map(([cornerKey, cornerData]) => {
                        const focus = monthlyFocus[cornerKey];
                        return `
                        <div class="space-y-3">
                            <h4 class="font-poppins font-semibold text-md pt-2" style="color: ${cornerData.color};">${cornerData.icon} ${cornerData.label}: <span class="font-normal text-sm text-secondary">${focus.label}</span></h4>
                            <div>
                                <div class="flex justify-between items-center">
                                    <label class="font-semibold text-sm text-secondary">${focus.post}</label>
                                    <span class="font-bold text-lg" style="color: ${cornerData.color};" id="slider-value-${cornerKey}">3.0</span>
                                </div>
                                <input type="range" min="1" max="5" value="3" step="0.5" class="slider-input mt-2" style="--shot-teal: ${cornerData.color};" oninput="document.getElementById('slider-value-${cornerKey}').textContent = parseFloat(this.value).toFixed(1)">
                            </div>
                        </div>
                    `}).join('')}
                    <div>
                        <label class="font-semibold text-sm">💭 What went well? What could be improved?</label>
                        <textarea class="w-full p-2 rounded-md mt-2 text-sm" rows="3" placeholder="e.g., My passing was accurate, but I need to work on my tackling..."></textarea>
                    </div>
                    <button class="w-full bg-gradient-purple text-white font-bold py-3 rounded-lg btn-interactive" onclick="submitPlayerEvaluation()">SUBMIT & EARN 100 SP</button>
                </div>
            </div>`;
        }


        function getPlayerPerformanceHistoryHTML() {
            let athleteId = appState.currentAthleteId;
            let persona = appState.activePersona;
            if (persona === 'Player') {
                athleteId = appState.currentPlayerProfileId;
            }
            if (persona === 'Parent') {
                athleteId = appState.currentChildId;
            }
            const athlete = mockData.teams.flatMap(t => t.players).find(p => p.id === athleteId);
            const team = mockData.teams.find(t => t.players.some(p => p.id === athleteId));
            const history = mockData.historicalData[athleteId];
            const achievements = mockData.achievements[athleteId] || [];

            if (!history) {
                return `
                 <div class="p-4 text-center">
                     <h2 class="font-poppins font-bold text-2xl">${persona === 'Parent' ? `${athlete.name}'s` : 'My'} Performance</h2>
                     <p class="text-secondary mt-4">No performance history found for ${athlete.name}.</p>
                 </div>`;
            }

            return `
            <div class="p-4 space-y-4">
                <div class="text-center">
                     <h2 class="font-poppins font-bold text-2xl">${persona === 'Parent' ? `${athlete.name}'s` : 'My'} Performance</h2>
                     <p class="text-secondary">${team.name}</p>
                </div>

                <div class="bg-shot-surface rounded-xl p-4 text-center">
                    <h3 class="font-poppins font-semibold">YOUR DEVELOPMENT JOURNEY</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm mt-3">
                        <div class="bg-shot-bg p-2 rounded-lg">
                            <p class="font-bold text-lg">24</p>
                            <p class="text-xs text-secondary">Evals Completed</p>
                        </div>
                        <div class="bg-shot-bg p-2 rounded-lg">
                            <p class="font-bold text-lg text-shot-gold">3</p>
                            <p class="text-xs text-secondary">Personal Bests</p>
                        </div>
                         <div class="bg-shot-bg p-2 rounded-lg">
                            <p class="font-bold text-lg text-shot-teal">7 Days</p>
                            <p class="text-xs text-secondary">Best Streak</p>
                        </div>
                         <div class="bg-shot-bg p-2 rounded-lg">
                            <p class="font-bold text-lg text-shot-purple">Tactical</p>
                            <p class="text-xs text-secondary">Most Improved</p>
                        </div>
                    </div>
                </div>
                
                 <div class="bg-shot-surface rounded-xl p-4">
                     <h3 class="font-poppins font-semibold">Four Corners Development</h3>
                     <div class="h-64 mt-4"><canvas id="player-progress-chart"></canvas></div>
                     <div class="text-xs text-secondary grid grid-cols-2 gap-x-4 gap-y-1 mt-2">
                         <span><strong>PB Physical:</strong> <span class="text-white font-semibold">3.9</span></span>
                         <span><strong>PB Technical:</strong> <span class="text-white font-semibold">4.1</span></span>
                         <span><strong>PB Tactical:</strong> <span class="text-white font-semibold">4.3</span></span>
                         <span><strong>PB Mental:</strong> <span class="text-white font-semibold">4.0</span></span>
                     </div>
                 </div>

                 <div class="bg-shot-surface rounded-xl p-4">
                     <h3 class="font-poppins font-semibold mb-3">Evaluation History</h3>
                     ${Object.entries(history).reverse().map(([monthKey, data]) => `
                         <div class="bg-shot-bg p-3 rounded-lg mb-3">
                             <p class="text-sm font-bold mb-2">${new Date(monthKey + '-02').toLocaleString('default', { month: 'long' })} ${new Date(monthKey + '-02').getFullYear()} Summary</p>
                             <div class="bg-black p-2 rounded-md text-xs space-y-1">
                                 <h4 class="font-bold">Your vs Coach Scores:</h4>
                                 ${Object.keys(data.scores).map(scoreKey => `
                                     <div class="grid grid-cols-3 items-center">
                                         <span class="text-secondary capitalize">${scoreKey}</span>
                                         <span>You: <span class="font-semibold text-shot-teal">${data.selfScores[scoreKey].toFixed(1)}</span></span>
                                         <span>Coach: <span class="font-semibold text-shot-gold">${data.scores[scoreKey].toFixed(1)}</span></span>
                                     </div>
                                 `).join('')}
                             </div>
                             <p class="text-xs text-secondary mt-2 p-2 bg-black rounded-md"><strong>Coach Comment:</strong> ${data.comment}</p>
                         </div>
                     `).join('')}
                 </div>
                 <div class="bg-shot-surface rounded-xl p-4">
                     <h3 class="font-poppins font-semibold mb-3">🏆 MILESTONES & GOALS</h3>
                     <div class="space-y-2">
                         <div>
                             <h4 class="text-sm font-bold mb-1 text-shot-gold">Unlocked</h4>
                             <ul class="list-disc list-inside text-sm text-secondary space-y-1">
                                 ${achievements.filter(a => a.unlocked).map(a => `<li>${a.text}</li>`).join('')}
                             </ul>
                         </div>
                         <div class="pt-2 border-t border-gray-700">
                             <h4 class="text-sm font-bold mb-1 text-shot-teal">Next Goals</h4>
                             <ul class="list-disc list-inside text-sm text-secondary space-y-1">
                                 ${achievements.filter(a => !a.unlocked).map(a => `<li>${a.text}</li>`).join('')}
                             </ul>
                         </div>
                     </div>
                 </div>
            </div>`;
        }
        
        function getGuardianApprovalHTML() { 
            return `
            <div class="p-4 space-y-4 text-center">
                 <div class="bg-shot-surface p-4 rounded-lg">
                     <i data-feather="shield" class="h-12 w-12 mx-auto text-shot-teal"></i>
                     <h2 class="font-poppins font-bold text-2xl mt-2">Guardian Approval Required</h2>
                     <p class="text-secondary mt-1">Emma Williams (12) has requested to join:</p>
                     <p class="font-bold text-lg mt-2">ISLEHAM FC GIRLS U12S</p>
                     <p class="text-shot-red mt-2">⏰ This request expires in 48 hours.</p>
                 </div>
                 <div class="bg-shot-surface p-4 rounded-lg text-left text-sm space-y-4">
                     <div>
                         <h3 class="font-poppins font-semibold text-base text-green-400 mb-2">✅ WHAT THIS PERMISSION ALLOWS:</h3>
                         <ul class="list-disc list-inside text-secondary space-y-1">
                             <li>Coach to view performance scores.</li>
                             <li>Coach to provide feedback visible to you and Emma.</li>
                             <li>Coach to track attendance and participation.</li>
                             <li>Emma to be included in team communications.</li>
                         </ul>
                     </div>
                      <div>
                         <h3 class="font-poppins font-semibold text-base text-red-400 mb-2">❌ WHAT THE COACH CANNOT DO:</h3>
                         <ul class="list-disc list-inside text-secondary space-y-1">
                             <li>See Emma's phone number or email address.</li>
                             <li>Access any personal information beyond sport.</li>
                             <li>Share Emma's data with third parties.</li>
                         </ul>
                     </div>
                 </div>
                 <div class="grid grid-cols-2 gap-4">
                     <button class="bg-gradient-red text-white font-bold py-3 rounded-lg btn-interactive" onclick="goBack()">DECLINE</button>
                     <button class="bg-gradient-teal text-black font-bold py-3 rounded-lg btn-interactive" onclick="goBack()">APPROVE ACCESS</button>
                 </div>
            </div>
            `;
        }
        
        function getTeamChatHTML() {
            const team = mockData.teams.find(t => t.id === appState.currentTeamId);
             return `
            <div class="p-4 space-y-4 flex flex-col h-full">
                <div>
                    <h2 class="font-poppins font-bold text-2xl">Team Chat</h2>
                    <p class="text-secondary">${team.name}</p>
                </div>
                <div class="flex-1 space-y-4 overflow-y-auto bg-shot-bg p-2 rounded-lg">
                    <div class="flex items-start gap-2.5">
                        <img class="w-8 h-8 rounded-full object-cover" src="https://i.imgur.com/ADMC4KP.jpeg" alt="Leo Taylor">
                        <div class="flex flex-col gap-1 w-full max-w-[320px]">
                            <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                <span class="text-sm font-semibold text-white">Leo Taylor</span>
                                <span class="text-xs font-normal text-gray-500">11:46</span>
                            </div>
                            <div class="flex flex-col leading-1.5 p-4 border-gray-200 bg-shot-surface rounded-e-xl rounded-es-xl">
                                <p class="text-sm font-normal text-white">Anyone going to the match early on Saturday?</p>
                            </div>
                        </div>
                    </div>
                     <div class="flex items-start gap-2.5 justify-end">
                        <div class="flex flex-col gap-1 w-full max-w-[320px]">
                            <div class="flex items-center space-x-2 rtl:space-x-reverse justify-end">
                                <span class="text-xs font-normal text-gray-500">11:47</span>
                                <span class="text-sm font-semibold text-white">You</span>
                            </div>
                            <div class="flex flex-col leading-1.5 p-4 border-gray-200 bg-shot-purple rounded-s-xl rounded-ee-xl">
                                <p class="text-sm font-normal text-white">Yeah, my dad is dropping me off around 1pm.</p>
                            </div>
                        </div>
                        <img class="w-8 h-8 rounded-full object-cover" src="${mockData.teams.flatMap(t => t.players).find(p => p.id === appState.currentPlayerProfileId)?.avatar}" alt="Current Player">
                    </div>
                </div>
                <div class="mt-4 flex items-center">
                    <input type="text" class="w-full bg-shot-surface p-3 rounded-l-lg text-sm" placeholder="Type a message...">
                    <button class="bg-shot-purple p-3 rounded-r-lg"><i data-feather="send"></i></button>
                </div>
            </div>`;
        }

        function getParentFixturesHTML() {
            const currentChild = mockData.teams.flatMap(t => t.players).find(p => p.id === appState.currentChildId);
            const team = mockData.teams.find(t => t.players.some(p => p.id === currentChild.id));
            const terminology = getTerminology(team.sport);
            return `
            <div class="p-4 space-y-4">
                <div>
                    <h2 class="font-poppins font-bold text-2xl">Fixtures & Schedule</h2>
                    <p class="text-secondary">${currentChild.name} | ${team.name}</p>
                </div>
                <div class="space-y-3">
                    <div class="bg-shot-surface p-3 rounded-lg">
                        <p class="font-semibold">${terminology.match_icon} ${terminology.match} vs Soham Town (Away)</p>
                        <p class="text-sm text-secondary">Saturday, 2 Aug 2025 - 14:00</p>
                        <p class="text-xs text-secondary mt-1">Soham Sports Centre</p>
                    </div>
                    <div class="bg-shot-surface p-3 rounded-lg">
                        <p class="font-semibold">${terminology.training_icon} ${terminology.training} - Defensive Drills</p>
                        <p class="text-sm text-secondary">Friday, 1 Aug 2025 - 18:00</p>
                        <p class="text-xs text-secondary mt-1">Isleham Rec Ground</p>
                    </div>
                </div>
            </div>`;
        }

        function getMatchDetailsModalHTML() {
            const team = mockData.teams.find(t => t.id === appState.currentTeamId);
                 return `<div class="p-4 bg-shot-surface rounded-lg text-white m-4 relative">
                         <button onclick="closeModal()" class="absolute top-2 right-2 text-gray-500 m-2"><i data-feather="x"></i></button>
                         <div class="flex justify-between items-center">
                             <h3 class="font-poppins font-bold text-lg">Match Details</h3>
                         </div>
                         <div class="mt-4 text-sm space-y-2">
                             <p><strong>Opponent:</strong> Soham Town (Away)</p>
                             <p><strong>Date:</strong> Saturday, 2 Aug 2025</p>
                             <p><strong>Time:</strong> 14:00</p>
                             <p><strong>Location:</strong> Soham Sports Centre</p>
                             <div class="bg-shot-bg p-2 rounded-md mt-2">
                                 <p class="font-semibold">Coach's Notes:</p>
                                 <p class="text-secondary">Be there 1 hour early for warm-up. Wear the blue kit.</p>
                             </div>
                         </div>
                         <button class="w-full bg-shot-purple text-white font-bold py-2 mt-4 rounded-lg" onclick="closeModal()">Close</button>
                        </div>`;
        }
        
        function getInvitePlayersModalHTML() {
            const team = mockData.teams.find(t => t.id === appState.currentTeamId);
            const inviteCode = `SHOT-${team.id.substring(5).toUpperCase()}`;
            return `<div class="p-4 text-center relative">
                <button onclick="closeModal()" class="absolute top-2 right-2 text-gray-500 m-2"><i data-feather="x"></i></button>
                <h3 class="font-poppins font-bold text-lg">Invite Players</h3>
                <p class="text-sm text-secondary mt-2">Share this unique code with players to have them join <strong>${team.name}</strong>.</p>
                <div class="bg-shot-bg p-4 rounded-lg my-4">
                    <p class="font-poppins font-bold text-2xl tracking-widest">${inviteCode}</p>
                </div>
                <button class="w-full bg-gradient-teal text-black font-bold py-2 rounded-lg btn-interactive" onclick="closeModal()">Copy Code</button>
            </div>`;
        }


        function getMatchEvaluationSetupModalHTML() {
            return `
            <div class="p-4 bg-shot-surface rounded-lg text-white m-4 text-center relative">
                 <button onclick="closeModal()" class="absolute top-2 right-2 text-gray-500 m-2"><i data-feather="x"></i></button>
                 <h3 class="font-poppins font-bold text-lg mt-4">Post-Match Evaluation</h3>
                 <p class="text-sm text-secondary mt-2">You are about to evaluate your performance for the match against <strong>Cambridge City</strong>.</p>
                 <div class="bg-shot-bg p-3 rounded-md mt-4 text-left">
                      <p class="text-xs text-secondary mb-1">Your focus areas for this match were:</p>
                      <ul class="list-disc list-inside text-sm space-y-1">
                          <li>Turning away from pressure</li>
                          <li>Reacting quickly</li>
                          <li>Winning headers</li>
                      </ul>
                 </div>
                 <p class="text-xs text-secondary mt-3">Please rate yourself honestly on each of the four PERFORM corners based on your performance.</p>
                 <button class="w-full bg-gradient-purple text-white font-bold py-2 mt-4 rounded-lg btn-interactive" onclick="navigateTo('player-evaluation-post')">CONTINUE TO EVALUATION</button>
            </div>
            `;
        }


        function getAttendanceModalHTML() {
            const team = mockData.teams.find(t => t.id === appState.currentTeamId);
            const terminology = getTerminology(team.sport);

            return `<div class="flex flex-col h-full">
                <header class="flex-shrink-0 flex justify-between items-center p-4 border-b border-gray-800">
                    <h2 class="font-poppins text-xl font-bold">Attendance: ${terminology.training}</h2>
                    <button onclick="closeModal()"><i data-feather="x"></i></button>
                </header>
                <div class="flex-1 overflow-y-auto p-4 space-y-3">
                ${team.players.map(p => `
                    <div class="bg-shot-surface p-3 rounded-lg flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <img src="${p.avatar}" class="w-10 h-10 rounded-full object-cover">
                            <p class="font-semibold">${p.name}</p>
                        </div>
                       <div class="flex space-x-1">
                           <button class="attendance-btn p-2 rounded-md bg-gray-700 transition-all duration-200" onclick="markAttendance('${p.id}', 'present', this)">✅</button>
                           <button class="attendance-btn p-2 rounded-md bg-gray-700 transition-all duration-200" onclick="markAttendance('${p.id}', 'absent', this)">❌</button>
                           <button class="attendance-btn p-2 rounded-md bg-gray-700 transition-all duration-200" onclick="markAttendance('${p.id}', 'injured', this)">🩹</button>
                       </div>
                    </div>
                `).join('')}
                </div>
                <footer class="p-4 border-t border-gray-800">
                    <button class="w-full bg-gradient-teal text-black font-bold py-3 rounded-lg btn-interactive" onclick="saveAttendance()">SAVE ATTENDANCE</button>
                </footer>
            </div>`;
        }

        function getEvaluationStatusModalHTML() {
            const team = mockData.teams.find(t => t.id === appState.currentTeamId);
             const terminology = getTerminology(team.sport);
              const statusStyles = {
                  'Completed': { text: 'Completed', color: 'text-green-400', icon: 'check-circle' },
                  'Pending Review': { text: 'Pending Coach Review', color: 'text-yellow-400', icon: 'clock' },
                  'Overdue': { text: 'Evaluation Overdue', color: 'text-red-400', icon: 'alert-triangle' },
                  'Awaiting Player': { text: 'Awaiting Player', color: 'text-blue-400', icon: 'user-check' },
            };

              return `<div class="flex flex-col h-full">
                <header class="flex-shrink-0 flex justify-between items-center p-4 border-b border-gray-800">
                    <h2 class="font-poppins text-xl font-bold">Evaluation Status</h2>
                    <button onclick="closeModal()"><i data-feather="x"></i></button>
                </header>
                <div class="flex-1 overflow-y-auto p-4 space-y-3">
                ${team.players.map(p => {
                    const status = statusStyles[p.evalStatus] || statusStyles['Awaiting Player'];
                    return `
                    <div class="bg-shot-surface p-3 rounded-lg flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <img src="${p.avatar}" class="w-10 h-10 rounded-full object-cover">
                            <p class="font-semibold">${p.name}</p>
                        </div>
                       <div class="text-right text-xs">
                           <div class="flex items-center gap-2 ${status.color}">
                               <i data-feather="${status.icon}" class="w-4 h-4"></i>
                               <span class="font-semibold">${status.text}</span>
                           </div>
                       </div>
                    </div>
                `}).join('')}
                </div>
                <footer class="p-4 border-t border-gray-800">
                     <button class="w-full bg-shot-purple text-white font-bold py-3 rounded-lg btn-interactive" onclick="closeModal()">CLOSE</button>
                </footer>
            </div>`;
        }

        function getAddPrivateNoteModalHTML(athleteId) {
            const athlete = mockData.teams.flatMap(t => t.players).find(p => p.id === athleteId);
            return `<div class="p-4 relative">
                <button onclick="closeModal()" class="absolute top-2 right-2 text-gray-500 m-2"><i data-feather="x"></i></button>
                <h3 class="font-poppins font-bold text-lg">Add Private Note for ${athlete.name}</h3>
                <p class="text-sm text-secondary">This note is only visible to you.</p>
                <textarea id="private-note-textarea" class="w-full p-2 rounded-md mt-4 text-sm" rows="5" placeholder="e.g., Showing real leadership qualities..."></textarea>
                <div class="grid grid-cols-2 gap-2 mt-4">
                    <button class="bg-gray-700 py-2 rounded-lg" onclick="closeModal()">Cancel</button>
                    <button class="bg-shot-teal text-black font-bold py-2 rounded-lg" onclick="savePrivateNote()">Save Note</button>
                </div>
            </div>`;
        }
        
        function getSendPublicFeedbackModalHTML(athleteId) {
             const athlete = mockData.teams.flatMap(t => t.players).find(p => p.id === athleteId);
             return `<div class="p-4 relative">
                <button onclick="closeModal()" class="absolute top-2 right-2 text-gray-500 m-2"><i data-feather="x"></i></button>
                <h3 class="font-poppins font-bold text-lg">Send Feedback to ${athlete.name}</h3>
                <p class="text-sm text-secondary">This will be visible to the athlete and their parent/guardian.</p>
                <textarea id="public-feedback-textarea" class="w-full p-2 rounded-md mt-4 text-sm" rows="5" placeholder="e.g., Outstanding tactical awareness..."></textarea>
                <div class="grid grid-cols-2 gap-2 mt-4">
                    <button class="bg-gray-700 py-2 rounded-lg" onclick="closeModal()">Cancel</button>
                    <button class="bg-shot-teal text-black font-bold py-2 rounded-lg" onclick="sendPublicFeedback()">Send Feedback</button>
                </div>
            </div>`;
        }

        function getCoachMessageModalHTML() {
            const currentChild = mockData.teams.flatMap(t => t.players).find(p => p.id === appState.currentChildId);
            const team = mockData.teams.find(t => t.players.some(p => p.id === currentChild.id));
            return `<div class="p-4 relative">
                 <button onclick="closeModal()" class="absolute top-2 right-2 text-gray-500 m-2"><i data-feather="x"></i></button>
                <h3 class="font-poppins font-bold text-lg">Message Coach</h3>
                <p class="text-sm text-secondary">Your message regarding <strong>${currentChild.name}</strong> (${team.name}) will be sent to Coach Alex.</p>
                <textarea id="coach-message-textarea" class="w-full p-2 rounded-md mt-4 text-sm" rows="5" placeholder="Type your message here..."></textarea>
                <div class="grid grid-cols-2 gap-2 mt-4">
                    <button class="bg-gray-700 py-2 rounded-lg" onclick="closeModal()">Cancel</button>
                    <button class="bg-shot-teal text-black font-bold py-2 rounded-lg" onclick="sendCoachMessage()">Send Message</button>
                </div>
            </div>`;
        }
        
        function getAnnouncementModalHTML() {
            const team = mockData.teams.find(t => t.id === appState.currentTeamId);
            return `<div class="p-4 relative">
                 <button onclick="closeModal()" class="absolute top-2 right-2 text-gray-500 m-2"><i data-feather="x"></i></button>
                <h3 class="font-poppins font-bold text-lg">New Announcement</h3>
                <p class="text-sm text-secondary">Post a message for everyone on <strong>${team.name}</strong>.</p>
                <textarea id="announcement-textarea" class="w-full p-2 rounded-md mt-4 text-sm" rows="5" placeholder="Type your message here..."></textarea>
                <div class="grid grid-cols-2 gap-2 mt-4">
                    <button class="bg-gray-700 py-2 rounded-lg" onclick="closeModal()">Cancel</button>
                    <button class="bg-shot-teal text-black font-bold py-2 rounded-lg" onclick="postAnnouncement()">Post</button>
                </div>
            </div>`;
        }
        
        function getUpdateAvailabilityModalHTML() {
            const athleteData = mockData.teams.flatMap(t => t.players).find(p => p.id === appState.currentPlayerProfileId);
            return `<div class="p-4 relative">
                 <button onclick="closeModal()" class="absolute top-2 right-2 text-gray-500 m-2"><i data-feather="x"></i></button>
                <h3 class="font-poppins font-bold text-lg">Update Availability</h3>
                <p class="text-sm text-secondary">Let your coach know your status for the upcoming events.</p>
                <div class="mt-4 space-y-2">
                    <p class="font-semibold">Training - Tomorrow 18:00</p>
                    <div class="flex gap-2">
                        <button class="flex-1 bg-green-500/20 text-green-400 py-2 rounded-lg">Available</button>
                        <button class="flex-1 bg-red-500/20 text-red-400 py-2 rounded-lg">Unavailable</button>
                    </div>
                </div>
                 <div class="mt-4 space-y-2">
                    <p class="font-semibold">Match - Saturday 14:00</p>
                    <div class="flex gap-2">
                        <button class="flex-1 bg-green-500/20 text-green-400 py-2 rounded-lg">Available</button>
                        <button class="flex-1 bg-red-500/20 text-red-400 py-2 rounded-lg">Unavailable</button>
                    </div>
                </div>
                 <button class="w-full bg-shot-purple text-white font-bold py-3 rounded-lg btn-interactive mt-4" onclick="closeModal()">Confirm Status</button>
            </div>`;
        }

        // --- GLOBAL SCOPE & INITIALIZATION ---
        window.navigateTo = navigateTo;
        window.goBack = goBack;
        window.showModal = showModal;
        window.closeModal = closeModal;
        window.toggleProfileMenu = toggleProfileMenu;
        window.switchPersona = switchPersona;
        window.switchClub = switchClub;
        window.toggleAccordion = toggleAccordion;
        window.markAttendance = markAttendance;
        window.saveAttendance = saveAttendance;
        window.savePrivateNote = savePrivateNote;
        window.sendPublicFeedback = sendPublicFeedback;
        window.sendCoachMessage = sendCoachMessage;
        window.setHistoryMonth = setHistoryMonth;
        window.submitPlayerEvaluation = submitPlayerEvaluation;
        window.getMatchEvaluationSetupModalHTML = getMatchEvaluationSetupModalHTML;
        window.getInvitePlayersModalHTML = getInvitePlayersModalHTML;
        window.getAnnouncementModalHTML = getAnnouncementModalHTML;
        window.getCoachMessageModalHTML = getCoachMessageModalHTML;
        window.getUpdateAvailabilityModalHTML = getUpdateAvailabilityModalHTML;
        window.postAnnouncement = postAnnouncement;
        window.createEvent = createEvent;
        window.linkChild = linkChild;
        window.submitCoachEvaluation = submitCoachEvaluation;

        document.addEventListener('click', (event) => {
            const menu = document.getElementById('profile-menu');
            const button = document.getElementById('profile-menu-button');
            if (!menu || !button || menu.classList.contains('hidden')) return;
            if (!menu.contains(event.target) && !button.contains(event.target)) {
                menu.classList.add('hidden');
            }
        });
        
        document.addEventListener('DOMContentLoaded', renderApp);
    </script>
</body>
</html>
�