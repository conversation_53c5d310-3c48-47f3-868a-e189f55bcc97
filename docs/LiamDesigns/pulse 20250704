
<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHOT Clubhouse MVP</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700;800&family=Montserrat:wght@400;500;600&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/feather-icons"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* Base styles and theme variables */
        :root {
            --shot-teal: #1ABC9C;
            --shot-purple: #6B00DB;
            --shot-gold: #F7B613;
            --shot-red: #E63946;
            --shot-bg: #121212;
            --shot-surface: #1E1E1E;
            --shot-text-primary: #FFFFFF;
            --shot-text-secondary: #B3B3B3;
            --font-poppins: 'Poppins', sans-serif;
            --font-montserrat: 'Montserrat', sans-serif;
            /* PERFORM Corner Colors */
            --corner-technical: #296DFF; /* Blue */
            --corner-physical: #2E8B57; /* Green */
            --corner-psychological: #FF6F3C; /* Coral Red */
            --corner-social: #FF5D73; /* Red/Pink */
            --corner-positional: #A95CFF; /* Purple */
        }
        body {
            font-family: var(--font-montserrat);
            background-color: var(--shot-bg);
            color: var(--shot-text-primary);
        }
        .font-poppins { font-family: var(--font-poppins); }
        .font-montserrat { font-family: var(--font-montserrat); }
        .bg-shot-teal { background-color: var(--shot-teal); }
        .text-shot-teal { color: var(--shot-teal); }
        .border-shot-teal { border-color: var(--shot-teal); }
        .bg-shot-purple { background-color: var(--shot-purple); }
        .text-shot-purple { color: var(--shot-purple); }
        .border-shot-purple { border-color: var(--shot-purple); }
        .bg-shot-gold { background-color: var(--shot-gold); }
        .text-shot-gold { color: var(--shot-gold); }
        .border-shot-gold { border-color: var(--shot-gold); }
        .bg-shot-surface { background-color: var(--shot-surface); }
        .text-secondary { color: var(--shot-text-secondary); }

        /* Custom scrollbar for a sleeker look */
        .hide-scrollbar::-webkit-scrollbar { display: none; }
        .hide-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }

        /* Animation for page transitions and effects */
        .fade-in { animation: fadeIn 0.3s ease-in-out; }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Nav item active state */
        .nav-btn.active i, .nav-btn.active span {
            color: var(--shot-gold);
        }

        /* Pulse filter active state */
        .pulse-filter-btn.active {
            border-width: 2px;
            transform: scale(1.1);
        }
        .pulse-filter-btn {
            transition: transform 0.2s ease-in-out;
        }
        .pulse-filter-btn.active.teal { border-color: var(--shot-teal); background-color: var(--shot-teal); color: white; }
        .pulse-filter-btn.active.purple { border-color: var(--shot-purple); background-color: var(--shot-purple); color: white; }
        .pulse-filter-btn.active.gold { border-color: var(--shot-gold); background-color: var(--shot-gold); color: white; }
        
        /* Story Highlights */
        .story-highlight-ring {
            padding: 2px;
            transition: transform 0.2s;
        }
        .story-highlight-ring:hover {
            transform: scale(1.05);
        }
        .story-highlight-ring.purple { background: linear-gradient(45deg, var(--shot-gold), var(--shot-purple)); }
        .story-highlight-ring.teal { background: linear-gradient(45deg, var(--shot-teal), #15a187); }
        .story-highlight-ring.red { background: linear-gradient(45deg, var(--shot-gold), var(--shot-red)); }
        .story-highlight-ring.gold { background: linear-gradient(45deg, #FFD700, var(--shot-gold)); }

        /* Modern Slider Styles from second file */
        .slider-input {
            -webkit-appearance: none;
            appearance: none;
            width: 100%;
            height: 8px;
            background: var(--shot-bg, #121212);
            border-radius: 5px;
            outline: none;
            opacity: 0.7;
            transition: opacity .2s;
        }
        .slider-input:hover { opacity: 1; }
        .slider-input::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: var(--shot-teal, #1ABC9C);
            cursor: pointer;
            border-radius: 50%;
        }
        .slider-input::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: var(--shot-teal, #1ABC9C);
            cursor: pointer;
            border-radius: 50%;
            border: none;
        }
        .styled-select-option {
            background-color: var(--shot-surface);
            color: white;
        }
        .accordion-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.4s ease-in-out, padding 0.4s ease-in-out;
            padding: 0 1rem;
        }
        .accordion-content.open {
            max-height: 1500px; /* Increased max-height for larger content */
            padding: 1rem 1rem;
        }
        .accordion-header .icon-rotate {
            transition: transform 0.3s ease-out;
        }
        .accordion-header.open .icon-rotate {
            transform: rotate(180deg);
        }
    </style>
</head>
<body class="bg-shot-bg">

    <!-- App Wrapper -->
    <div class="w-full max-w-5xl mx-auto flex flex-col min-h-screen bg-black">

        <!-- App Header -->
        <header id="app-header" class="sticky top-0 bg-shot-bg/80 backdrop-blur-sm px-4 py-3 flex justify-between items-center border-b border-gray-800 z-40">
            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSZNxN2v-tgf_-t2Oic-m3dC2jtm3_zllEu-Q&s" alt="SHOT Logo" class="h-8">
            <div class="flex items-center space-x-4">
                <div id="shopping-cart-icon" class="relative cursor-pointer">
                    <i data-feather="shopping-cart" class="text-gray-400 hover:text-white"></i>
                    <span id="cart-count" class="absolute -top-2 -right-2 bg-shot-teal text-white text-xs font-bold rounded-full h-5 w-5 flex items-center justify-center">0</span>
                </div>
                 <div class="relative">
                    <span id="sp-total" class="font-bold text-shot-gold text-sm mr-2"></span>
                </div>
                <div id="profile-switcher" class="cursor-pointer">
                    <img id="header-avatar" src="https://i.imgur.com/BEfccy3.png" onerror="this.src='https://placehold.co/40x40/6B00DB/FFFFFF?text=A'" alt="User Avatar" class="h-9 w-9 rounded-full border-2 border-shot-purple object-cover">
                </div>
            </div>
        </header>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-1 overflow-y-auto scroll-smooth">
            <!-- Dynamic content will be rendered here -->
        </main>

        <!-- Bottom Navigation Bar -->
        <nav id="bottom-nav" class="sticky bottom-0 bg-shot-surface/80 backdrop-blur-sm border-t border-gray-800 grid grid-cols-5 gap-2 px-2 py-2 z-40">
            <!-- Nav buttons will be dynamically rendered here -->
        </nav>
        
    </div>

    <!-- Modals and Overlays Container -->
    <div id="modal-container" class="fixed inset-0 z-50 pointer-events-none"></div>

    <!-- MOCK DATA SCRIPT -->
    <script type="module" id="mock-data-script">
        const mockData = {
            // User and Base App Data
            superUser: {
                id: 'user_super_001', name: 'Alex', email: '<EMAIL>', dob: '2004-05-10', sp: 12540,
                roles: ['Player', 'Coach', 'Parent', 'Member'],
                sports: ['Football', 'Boxing'],
                avatar: 'https://i.imgur.com/BEfccy3.png',
                linkedChildIds: ['player_child_01'],
                achievements: [
                    { id: 'ach_01', title: 'Power User', icon: 'zap', date: '2025-05-20'},
                    { id: 'ach_02', title: 'First 10k SP', icon: 'award', date: '2025-06-10'},
                    { id: 'ach_03', title: 'Team Captain', icon: 'shield', date: '2025-04-15'},
                ],
                streaks: { training: 4 }
            },
            clubs: [
                { id: 'club_001', name: 'West Ham United FC', location: 'London', logo: 'https://i.imgur.com/l6bNTzT.png' },
                { id: 'club_003', name: 'Eastside Boxing Gym', location: 'London', logo: 'https://i.imgur.com/I7S11ie.png'},
            ],
            teams: [
                { id: 'team_001', clubId: 'club_001', name: 'Under 14s', sport: 'Football', coachId: 'user_super_001', playerCount: 16 },
                { id: 'team_003', clubId: 'club_003', name: 'Amateur Boxing Squad', sport: 'Boxing', coachId: 'user_super_001', playerCount: 12 },
            ],
            players: [
                { id: 'player_001', teamId: 'team_001', name: 'Kai Williams', age: 13, position: 'Midfield', positionalObjective: 'Switching play', avatar: 'https://i.imgur.com/ujSIUBs.jpeg', stats: { matches: 12, started: 12, potm: 3}, evaluations: Array.from({length: 12}, (_, i) => ({ month: new Date(2025, 5-i).toLocaleString('default', { month: 'long' }), year: 2025, avg: (4.2 - Math.sin(i*0.9)*0.4).toFixed(1), data: {Technical: 4-Math.cos(i)*0.3, Physical: 4.5-Math.sin(i)*0.5, Psychological: 4-Math.cos(i*0.6)*0.2, Social: 4+Math.sin(i*0.4)*0.1, Positional: 4.5-Math.cos(i)*0.3}}))},
                { id: 'player_002', teamId: 'team_001', name: 'Amelia Johnson', age: 14, position: 'Striker', positionalObjective: 'Finishing', avatar: 'https://i.imgur.com/JOfnhtw.jpeg', stats: { matches: 12, started: 12, potm: 4}, evaluations: Array.from({length: 12}, (_, i) => ({ month: new Date(2025, 5-i).toLocaleString('default', { month: 'long' }), year: 2025, avg: (4.5 - Math.cos(i*0.5)*0.3).toFixed(1), data: {Technical: 4.5-Math.sin(i)*0.2, Physical: 5-Math.cos(i*1.2)*0.1, Psychological: 4+Math.sin(i*0.7)*0.3, Social: 4.5-Math.cos(i)*0.2, Positional: 4.5-Math.sin(i*0.9)*0.1}}))},
                { id: 'player_003', teamId: 'team_001', name: 'Leo Taylor', age: 13, position: 'Centre Back', positionalObjective: 'Winning headers', avatar: 'https://i.imgur.com/kiYVivh.jpeg', stats: { matches: 10, started: 10, potm: 2}, evaluations: Array.from({length: 12}, (_, i) => ({ month: new Date(2025, 5-i).toLocaleString('default', { month: 'long' }), year: 2025, avg: (3.5 - Math.sin(i*0.8)*0.5).toFixed(1), data: {Technical: 3 - Math.sin(i)*0.2, Physical: 3.5-Math.cos(i)*0.3, Psychological: 4-Math.sin(i*0.5)*0.5, Social: 4-Math.cos(i*0.5)*0.2, Positional: 3.5+Math.sin(i*1.2)*0.4}}))},
                { id: 'player_004', teamId: 'team_001', name: 'Ethan Hayes', age: 14, position: 'Goalkeeper', positionalObjective: 'Shot Stopping', avatar: 'https://i.imgur.com/3Y8k42p.jpeg', stats: { matches: 11, started: 8, potm: 0}, evaluations: Array.from({length: 12}, (_, i) => ({ month: new Date(2025, 5-i).toLocaleString('default', { month: 'long' }), year: 2025, avg: (2.8 + Math.sin(i*0.7)*0.6).toFixed(1), data: {Technical: 3+Math.sin(i*0.5)*0.4, Physical: 3-Math.cos(i*0.8)*0.2, Psychological: 2+Math.sin(i)*0.8, Social: 3+Math.cos(i)*0.3, Positional: 3+Math.sin(i*1.1)*0.2}}))},
                { id: 'player_005', teamId: 'team_001', name: 'Chloe Carter', age: 13, position: 'Full Back', positionalObjective: '1v1 Defending', avatar: 'https://i.imgur.com/kiYVivh.jpeg', stats: { matches: 9, started: 6, potm: 1}, evaluations: Array.from({length: 12}, (_, i) => ({ month: new Date(2025, 5-i).toLocaleString('default', { month: 'long' }), year: 2025, avg: (3.6 + Math.sin(i*1.2)*0.4).toFixed(1), data: {Technical: 4-Math.cos(i*0.9)*0.3, Physical: 3+Math.sin(i)*0.5, Psychological: 3.5+Math.cos(i*0.4)*0.5, Social: 4, Positional: 3.5-Math.sin(i)*0.3}}))},
                { id: 'player_006', teamId: 'team_001', name: 'Mason Brooks', age: 14, position: 'Winger', positionalObjective: 'Crossing', avatar: 'https://i.imgur.com/ujSIUBs.jpeg', stats: { matches: 12, started: 11, potm: 0}, evaluations: Array.from({length: 12}, (_, i) => ({ month: new Date(2025, 5-i).toLocaleString('default', { month: 'long' }), year: 2025, avg: (3.1 + Math.sin(i)*0.3).toFixed(1), data: {Technical: 3, Physical: 4-Math.cos(i)*0.5, Psychological: 3+Math.sin(i*0.5)*0.2, Social: 3, Positional: 2.5+Math.sin(i*1.2)*0.5}}))},
                { id: 'player_007', teamId: 'team_001', name: 'Sophia Reed', age: 13, position: 'Midfield', positionalObjective: 'Passing Range', avatar: 'https://i.imgur.com/JOfnhtw.jpeg', stats: { matches: 12, started: 12, potm: 1}, evaluations: Array.from({length: 12}, (_, i) => ({ month: new Date(2025, 5-i).toLocaleString('default', { month: 'long' }), year: 2025, avg: (3.8 - Math.cos(i*0.7)*0.2).toFixed(1), data: {Technical: 3.5, Physical: 4-Math.sin(i*0.4)*0.2, Psychological: 4, Social: 4, Positional: 3.5-Math.cos(i)*0.1}}))},
                { id: 'player_008', teamId: 'team_001', name: 'Noah Patel', age: 14, position: 'Striker', positionalObjective: 'Movement', avatar: 'https://i.imgur.com/3Y8k42p.jpeg', stats: { matches: 10, started: 5, potm: 0}, evaluations: Array.from({length: 12}, (_, i) => ({ month: new Date(2025, 5-i).toLocaleString('default', { month: 'long' }), year: 2025, avg: (3.4 + Math.sin(i)*0.2).toFixed(1), data: {Technical: 3.5+Math.sin(i*0.2)*0.2, Physical: 3.5, Psychological: 3, Social: 3.5, Positional: 3.5}}))},
                { id: 'player_009', teamId: 'team_001', name: 'Isabella Evans', age: 13, position: 'Centre Back', positionalObjective: 'Tackling', avatar: 'https://i.imgur.com/kiYVivh.jpeg', stats: { matches: 9, started: 2, potm: 0}, evaluations: Array.from({length: 12}, (_, i) => ({ month: new Date(2025, 5-i).toLocaleString('default', { month: 'long' }), year: 2025, avg: (3.2 + Math.cos(i)*0.3).toFixed(1), data: {Technical: 3+Math.sin(i)*0.2, Physical: 3+Math.cos(i)*0.4, Psychological: 3.5, Social: 3.5, Positional: 3}}))},
                { id: 'player_010', teamId: 'team_001', name: 'James Morris', age: 14, position: 'Winger', positionalObjective: 'Dribbling', avatar: 'https://i.imgur.com/ujSIUBs.jpeg', stats: { matches: 11, started: 9, potm: 0}, evaluations: Array.from({length: 12}, (_, i) => ({ month: new Date(2025, 5-i).toLocaleString('default', { month: 'long' }), year: 2025, avg: (3.9 - Math.sin(i*0.6)*0.2).toFixed(1), data: {Technical: 4-Math.sin(i)*0.1, Physical: 4, Psychological: 4-Math.cos(i)*0.3, Social: 4, Positional: 3.5}}))},
                { id: 'player_011', teamId: 'team_001', name: 'Olivia King', age: 13, position: 'Full Back', positionalObjective: 'Overlapping', avatar: 'https://i.imgur.com/JOfnhtw.jpeg', stats: { matches: 7, started: 7, potm: 1}, evaluations: Array.from({length: 12}, (_, i) => ({ month: new Date(2025, 5-i).toLocaleString('default', { month: 'long' }), year: 2025, avg: (3.8).toFixed(1), data: {Technical: 4, Physical: 3.5-Math.sin(i)*0.3, Psychological: 4-Math.cos(i)*0.2, Social: 4, Positional: 3.5}}))},
                { id: 'player_012', teamId: 'team_001', name: 'Lucas Wright', age: 14, position: 'Midfield', positionalObjective: 'Awareness', avatar: 'https://i.imgur.com/3Y8k42p.jpeg', stats: { matches: 8, started: 1, potm: 0}, evaluations: Array.from({length: 12}, (_, i) => ({ month: new Date(2025, 5-i).toLocaleString('default', { month: 'long' }), year: 2025, avg: (2.5 + Math.sin(i*0.4)*0.5).toFixed(1), data: {Technical: 2+Math.sin(i*0.5)*0.5, Physical: 3, Psychological: 2.5, Social: 2.5, Positional: 2.5+Math.cos(i)*0.3}}))},
                { id: 'player_013', teamId: 'team_001', name: 'Ava Turner', age: 13, position: 'Goalkeeper', positionalObjective: 'Distribution', avatar: 'https://i.imgur.com/kiYVivh.jpeg', stats: { matches: 11, started: 11, potm: 0}, evaluations: Array.from({length: 12}, (_, i) => ({ month: new Date(2025, 5-i).toLocaleString('default', { month: 'long' }), year: 2025, avg: (3.3+Math.sin(i*0.2)*0.2).toFixed(1), data: {Technical: 3, Physical: 3, Psychological: 3.5, Social: 3.5-Math.cos(i)*0.2, Positional: 3.5}}))},
                { id: 'player_014', teamId: 'team_001', name: 'Henry Clark', age: 14, position: 'Striker', positionalObjective: 'Hold-up Play', avatar: 'https://i.imgur.com/ujSIUBs.jpeg', stats: { matches: 10, started: 4, potm: 0}, evaluations: Array.from({length: 12}, (_, i) => ({ month: new Date(2025, 5-i).toLocaleString('default', { month: 'long' }), year: 2025, avg: (3.0 + Math.cos(i*0.8)*0.4).toFixed(1), data: {Technical: 3+Math.sin(i*0.6)*0.3, Physical: 3.5, Psychological: 2.5+Math.cos(i)*0.5, Social: 3, Positional: 3}}))},
                { id: 'player_015', teamId: 'team_001', name: 'Freya Hill', age: 13, position: 'Winger', positionalObjective: '1v1 Attacking', avatar: 'https://i.imgur.com/JOfnhtw.jpeg', stats: { matches: 12, started: 12, potm: 5}, evaluations: Array.from({length: 12}, (_, i) => ({ month: new Date(2025, 5-i).toLocaleString('default', { month: 'long' }), year: 2025, avg: (4.8 - Math.cos(i*0.3)*0.1).toFixed(1), data: {Technical: 5, Physical: 4.5, Psychological: 5, Social: 4.5, Positional: 5}}))},
                { id: 'player_boxer_01', teamId: 'team_003', name: 'Rico Martinez', age: 19, position: 'Out-boxer', positionalObjective: 'Jab Control', avatar: 'https://i.imgur.com/3Y8k42p.jpeg', stats: { matches: 5, started: 5, potm: 3}, evaluations: [{ month: 'June', year: 2025, avg: 4.1, data: {Technical: 4.5, Physical: 4, Psychological: 4, Social: 4, Positional: 4}}] }
            ],
            playerProfiles: {
                'player_001': { thePlayer: "A talented and creative midfielder who reads the game well. Can sometimes drift out of games if not involved.", desires: "Wants to be the main playmaker, loves a killer pass. Motivated by team success and personal assists.", privateNotes: "Responds well to tactical challenges. Needs to be encouraged to work on defensive duties. Keep sessions engaging to maintain focus.", history: [{ date: 'May 2025', comments: { TECHNICAL: "Excellent passing range.", PSYCHOLOGICAL: "Good vision but can be quiet.", PHYSICAL: "Needs to improve stamina.", SOCIAL: "Well-liked by teammates." } }] },
                'player_002': { thePlayer: "A natural goalscorer with a powerful shot. Very determined and competitive.", desires: "Loves scoring goals. Motivated by being the top scorer and winning matches.", privateNotes: "Can get frustrated if chances don't come. Works best when given clear targets. Encourage her to work on her link-up play.", history: [{ date: 'May 2025', comments: { TECHNICAL: "Clinical finisher.", PSYCHOLOGICAL: "Very confident in front of goal.", PHYSICAL: "Strong and quick.", SOCIAL: "Leads by example with her work rate." } }] },
                'player_003': { thePlayer: "Solid and reliable defender. Reads the game well and is strong in the tackle.", desires: "Enjoys the defensive side of the game and keeping clean sheets.", privateNotes: "Needs to work on his communication and organizing the back line.", history: [{ date: 'May 2025', comments: { TECHNICAL: "Good tackler.", PSYCHOLOGICAL: "Composed under pressure.", PHYSICAL: "Strong in the air.", SOCIAL: "Quiet but respected." } }] },
                'player_004': { thePlayer: "Great shot-stopper with quick reflexes. Still developing his command of the area.", desires: "Loves making big saves and being the hero.", privateNotes: "Work on communication with his defenders and decision-making on crosses.", history: [{ date: 'May 2025', comments: { TECHNICAL: "Excellent reflexes.", PSYCHOLOGICAL: "Brave in one-on-one situations.", PHYSICAL: "Needs to improve kicking distance.", SOCIAL: "Could be more vocal." } }] },
                'player_005': { thePlayer: "Pacey full-back who loves to get forward. Good engine and works hard for the team.", desires: "Enjoys overlapping and joining the attack.", privateNotes: "Can sometimes get caught out of position. Focus on balancing attacking and defensive duties.", history: [{ date: 'May 2025', comments: { TECHNICAL: "Good crossing ability.", PSYCHOLOGICAL: "High work rate.", PHYSICAL: "Very quick.", SOCIAL: "Links up well with the winger." } }] },
                'player_006': { thePlayer: "Tricky winger with good close control. Can beat a player and deliver a cross.", desires: "Likes to take on defenders and create chances.", privateNotes: "Needs to improve his final product and decision-making in the final third.", history: [{ date: 'May 2025', comments: { TECHNICAL: "Excellent dribbler.", PSYCHOLOGICAL: "Confident in 1v1s.", PHYSICAL: "Good acceleration.", SOCIAL: "Needs to work on tracking back." } }] },
                'player_007': { thePlayer: "Tidy midfielder who keeps possession well. Good range of passing.", desires: "Likes to control the tempo of the game.", privateNotes: "Encourage her to take more risks with her passing and look for forward options.", history: [{ date: 'May 2025', comments: { TECHNICAL: "Accurate passer.", PSYCHOLOGICAL: "Good game awareness.", PHYSICAL: "Needs to improve tackling.", SOCIAL: "Good communicator." } }] },
                'player_008': { thePlayer: "Hard-working striker with intelligent movement. Good at finding space in the box.", desires: "Enjoys scoring goals and creating chances for others.", privateNotes: "Work on his finishing with his weaker foot. Can be a great asset with his link-up play.", history: [{ date: 'May 2025', comments: { TECHNICAL: "Good movement.", PSYCHOLOGICAL: "Unselfish player.", PHYSICAL: "Good in the air.", SOCIAL: "Works well with a strike partner." } }] },
                'player_009': { thePlayer: "No-nonsense defender who is strong and committed. A real team player.", desires: "Loves a clean sheet and winning her defensive battles.", privateNotes: "Can be a bit rash in the tackle. Work on timing and staying on her feet.", history: [{ date: 'May 2025', comments: { TECHNICAL: "Strong tackler.", PSYCHOLOGICAL: "Very committed.", PHYSICAL: "Dominant in the air.", SOCIAL: "A leader at the back." } }] },
                'player_010': { thePlayer: "Skillful winger with flair. Can produce moments of magic.", desires: "Enjoys entertaining the crowd and beating players.", privateNotes: "Needs to be more consistent and work on his defensive responsibilities.", history: [{ date: 'May 2025', comments: { TECHNICAL: "Very skillful.", PSYCHOLOGICAL: "Can be inconsistent.", PHYSICAL: "Quick feet.", SOCIAL: "Needs to improve teamwork." } }] },
                'player_011': { thePlayer: "Athletic full-back who is solid defensively and supports attacks well.", desires: "Enjoys the physical side of the game and making overlapping runs.", privateNotes: "Work on her final ball when she gets into attacking positions.", history: [{ date: 'May 2025', comments: { TECHNICAL: "Good engine.", PSYCHOLOGICAL: "Determined.", PHYSICAL: "Strong and fast.", SOCIAL: "Good team player." } }] },
                'player_012': { thePlayer: "Intelligent midfielder with great vision. Can unlock a defence with a pass.", desires: "To be the creative force in the team.", privateNotes: "Needs to improve his work rate off the ball and contribute more defensively.", history: [{ date: 'May 2025', comments: { TECHNICAL: "Excellent vision.", PSYCHOLOGICAL: "Creative.", PHYSICAL: "Needs to improve strength.", SOCIAL: "Good on the ball." } }] },
                'player_013': { thePlayer: "A promising goalkeeper with good size and handling. Learning the position.", desires: "To command her area and be a reliable last line of defence.", privateNotes: "Focus on footwork and positioning. Confidence will grow with experience.", history: [{ date: 'May 2025', comments: { TECHNICAL: "Good handling.", PSYCHOLOGICAL: "Eager to learn.", PHYSICAL: "Good presence.", SOCIAL: "Communicating more each game." } }] },
                'player_014': { thePlayer: "Strong striker who is excellent at holding up the ball and bringing others into play.", desires: "To be a focal point for the attack and score goals.", privateNotes: "Work on his movement in the box and getting on the end of crosses.", history: [{ date: 'May 2025', comments: { TECHNICAL: "Good hold-up play.", PSYCHOLOGICAL: "Team player.", PHYSICAL: "Very strong.", SOCIAL: "Good communicator." } }] },
                'player_015': { thePlayer: "A tricky winger with a great attitude. Always looking to improve and help the team.", desires: "To beat players and create goals. Loves a challenge.", privateNotes: "Encourage her to take on her full-back more often. She has the skill to be a real game-changer.", history: [{ date: 'May 2025', comments: { TECHNICAL: "Great 1v1 skills.", PSYCHOLOGICAL: "Very positive attitude.", PHYSICAL: "Quick and agile.", SOCIAL: "A great teammate." } }] },
                'player_boxer_01': { thePlayer: "A dedicated and disciplined boxer with a powerful jab.", desires: "To be a champion. Motivated by the challenge and the roar of the crowd.", privateNotes: "Has a tendency to drop his left hand. Drill footwork and head movement relentlessly.", history: [{date: 'May 2025', comments: {TECHNICAL: "Sharp jab, powerful right hand.", PSYCHOLOGICAL: "Fearless and has a strong will to win.", PHYSICAL: "Excellent power and stamina.", SOCIAL: "Listens well to corner instructions."}}]},
            },
            playerEvaluations: {
                'player_001': { status: 'completed' }, 'player_002': { status: 'pending_review' }, 'player_003': { status: 'awaiting_player' }, 'player_boxer_01': { status: 'awaiting_player' },
                ...Object.fromEntries(Array.from({length: 13}, (_, i) => [`player_mock_${i+1}`, {status: i < 4 ? 'completed' : (i < 8 ? 'pending_review' : 'awaiting_player')}]))
            },
            fixtures: [
                { id: 'fix_001', teamId: 'team_001', type: 'Match', opponent: 'Arsenal Youth', date: '2025-07-10', time: '15:00', status: 'upcoming', sport: 'Football' },
                { id: 'fix_002', teamId: 'team_001', type: 'Training', opponent: 'Internal', date: '2025-07-08', time: '18:00', status: 'upcoming', sport: 'Football' },
                { id: 'fix_003', teamId: 'team_001', type: 'Match', opponent: 'Chelsea Youth', date: '2025-06-28', time: '14:00', status: 'completed', sport: 'Football', evaluation: '4.2' },
                { id: 'fix_004', teamId: 'team_003', type: 'Sparring', opponent: 'Local Gym', date: '2025-07-12', time: '19:00', status: 'upcoming', sport: 'Boxing' }
            ],
            eventHistory: [
                { title: 'Completed Match Evaluation', date: '2025-06-28', sp: 100 },
                { title: 'Finished Training Drill', date: '2025-06-25', sp: 150 },
                { title: 'Team Captain Achievement', date: '2025-06-20', sp: 250 },
                { title: 'First 10k SP Milestone', date: '2025-06-15', sp: 500 }
            ],
            userRewards: [
                { id: 'reward_001', title: 'Free SHOT Merchandise', cost: 5000, status: 'available', description: 'Choose any item under £50' },
                { id: 'reward_002', title: 'Coaching Session with Pro', cost: 10000, status: 'available', description: '1-on-1 session with professional coach' },
                { id: 'reward_003', title: 'VIP Event Access', cost: 15000, status: 'available', description: 'Exclusive access to SHOT events', expiryDate: '2025-12-31' },
                { id: 'reward_004', title: 'Training Camp Scholarship', cost: 25000, status: 'available', description: 'Full scholarship to summer training camp' }
            ],
            aiPrompts: [
                "When is Dermot Kennedy's next concert?",
                "Show me my rewards",
                "Help me improve my game",
                "What drills should I do?"
            ],
            performFramework: {
                TECHNICAL: { 'ball-mastery': 'Ball Mastery', 'practice': 'Practice' },
                SOCIAL: { 'communication': 'Communication', 'self-esteem': 'Self-Esteem', 'teamwork': 'Teamwork' },
                PSYCHOLOGICAL: { 'understanding': 'Understanding', 'decision-making': 'Decision Making', 'confidence': 'Confidence' },
                PHYSICAL: { 'coordination': 'Coordination', 'conditioning': 'Conditioning', 'challenge': 'Challenge' },
                POSITIONAL: { 
                    'goalkeeping': 'Goalkeeping',
                    'defending': 'Defending',
                    'midfield-play': 'Midfield Play',
                    'attacking': 'Attacking',
                }
            },
            shotDrills: [
                {"category":"social","subcategory":"self-esteem","drills":[{"id":"soc-se-001","name":"Comeback King Training","description":"Failure is fuel. Train your mind to bounce back stronger. Every setback is a setup for your comeback story.","duration":20,"setup":{"participants":["solo"],"environment":["indoor","outdoor"],"equipment":["ball","6 cones","target or goal"]},"instructions":["Set up shooting challenges with increasing difficulty","Minutes 0-5: Miss on purpose - practice positive self-talk after misses","Minutes 5-10: Pressure shots - 10 attempts, celebrate effort not outcome","Minutes 10-15: Skill challenges - attempt tricks you've never done, embrace failure","Minutes 15-20: Victory lap - repeat early challenges, notice improvement"],"progressions":["Add audience pressure (record yourself)","Increase technical difficulty weekly","Set personal challenges beyond comfort zone"],"coaching_points":["Failure is data, not defeat","Your reaction defines your reality","Celebrate attempts, not just achievements","Build your bounce-back muscle daily"],"evaluation_focus":{"pre":"How do you currently handle mistakes and failures? (Self-Esteem / Comeback)","post":"Rate your improved response to setbacks (Self-Esteem / Comeback)"}},{"id":"soc-se-002","name":"Risk & Reward Laboratory","description":"Fortune favours the brave. Build confidence through calculated risks. This is where comfort zones come to die.","duration":20,"setup":{"participants":["pair"],"environment":["outdoor"],"equipment":["2 balls","10 cones","goals or targets"]},"instructions":["Create safe/medium/high risk zones with different point values","Minutes 0-5: Identify your comfort zone - where do you naturally play?","Minutes 5-10: Push boundaries - attempt plays from higher risk zones","Minutes 10-15: Skill showcase - each player demonstrates something new","Minutes 15-20: Confidence competition - who takes smartest risks?"],"progressions":["Increase complexity of risk scenarios","Add defenders to create real pressure","Track risk-taking improvement over weeks"],"coaching_points":["Calculated risks beat reckless chances","Confidence grows through action","Your biggest risk is playing it safe","Learn from every attempt"],"evaluation_focus":{"pre":"Rate your willingness to take risks in play (Self-Esteem / Risk & Reward)","post":"Assess growth in smart risk-taking confidence (Self-Esteem / Risk & Reward)"}},{"id":"soc-se-003","name":"Spotlight Strength Session","description":"Own your moment. Train to perform when all eyes are on you. Because champions shine brightest under pressure.","duration":20,"setup":{"participants":["group"],"environment":["indoor","outdoor"],"equipment":["1 ball per player","cones","bibs"]},"instructions":["Circle formation with one player in middle at a time","Minutes 0-5: Skill circle - each player shows their best skill for 30 seconds","Minutes 5-10: Pressure performance - middle player vs group challenges","Minutes 10-15: Positive bombardment - group gives specific praise to performer","Minutes 15-20: Team celebration creation - build collective confidence"],"progressions":["Increase audience size gradually","Add competitive elements","Create performance scenarios"],"coaching_points":["Everyone deserves their spotlight","Pressure is a privilege - embrace it","Your energy affects others - radiate confidence","Build others up, rise together"],"evaluation_focus":{"pre":"How comfortable are you being centre of attention? (Self-Esteem / Spotlight)","post":"Rate improvement in performance confidence (Self-Esteem / Spotlight)"}},{"id":"soc-se-004","name":"Body Language Bootcamp","description":"Look like a player, play like a player. Master the non-verbal game. Your presence speaks before your feet do.","duration":20,"setup":{"participants":["solo"],"environment":["indoor"],"equipment":["ball","mirror or phone camera","cones"]},"instructions":["Set up in front of mirror or camera","Minutes 0-5: Power poses - practice confident stances with ball","Minutes 5-10: Walk the walk - move like you own the pitch","Minutes 10-15: Mistake recovery - practice positive body language after errors","Minutes 15-20: Signature style - develop your unique confident presence"],"progressions":["Practice in increasingly public spaces","Add pressure scenarios","Film and review body language"],"coaching_points":["Shoulders back, head up - always","Your walk tells your story","Confidence is contagious - spread it","Fake it till you make it real"],"evaluation_focus":{"pre":"How do you currently handle mistakes and failures? (Self-Esteem / Body Language)","post":"Rate your improved response to setbacks (Self-Esteem / Body Language)"}},{"id":"soc-se-005","name":"Failure to Fire Circuit","description":"Transform setbacks into comebacks. Group session where mistakes are mandatory. Build bulletproof confidence together.","duration":20,"setup":{"participants":["group"],"environment":["outdoor"],"equipment":["multiple balls","20 cones","goals"]},"instructions":["Set up multiple challenge stations","Minutes 0-5: Failure celebration - miss shots, laugh, reset positively","Minutes 5-10: Skill attempts - everyone tries something they can't do yet","Minutes 10-15: Support system - help teammates through their challenges","Minutes 15-20: Success stories - share biggest improvement moments"],"progressions":["Increase challenge difficulty","Add performance elements","Create team resilience challenges"],"coaching_points":["Failure is just feedback","Support systems build champions","Every attempt deserves respect","Growth happens outside comfort zones"],"evaluation_focus":{"pre":"How does failure affect your confidence? (Self-Esteem / Resilience)","post":"Rate team's collective confidence growth (Self-Esteem / Resilience)"}}]},
                {"category":"technical","subcategory":"practice","drills":[{"id":"tech-prac-001","name":"Perfect Rep Protocol","description":"Repetition builds legends. Master the basics until they're automatic. Where consistency meets excellence.","duration":20,"setup":{"participants":["solo"],"environment":["indoor","outdoor"],"equipment":["ball","6 cones","wall or target"]},"instructions":["Set up 3 stations - passing, control, shooting","Minutes 0-5: 50 wall passes, alternating feet","Minutes 5-10: First touch control, 5 directions per foot","Minutes 10-15: Shooting technique, 20 reps focusing on form","Minutes 15-20: Combined flow - link all three with match intensity"],"progressions":["Week 1: Focus on technique over speed","Week 2: Add weak foot emphasis (70% weak foot)","Week 3: Introduce time pressures"],"coaching_points":["Quality beats quantity - every rep matters","Mental focus - visualise success before each rep","Consistency creates confidence","Track your numbers - improvement needs data"],"evaluation_focus":{"pre":"How consistent are your technical skills during practice? (Practice / Consistency)","post":"Assess improvement in technique quality and rep consistency (Practice / Consistency)"}},{"id":"tech-prac-002","name":"Mirror Master Method","description":"Two players, one standard. Push each other to perfection. Iron sharpens iron on the training ground.","duration":20,"setup":{"participants":["pair"],"environment":["outdoor"],"equipment":["2 balls","8 cones","2 small goals"]},"instructions":["Partners face each other 10m apart","Minutes 0-5: Synchronised ball work - mirror each other's movements","Minutes 5-10: Technique battles - who can maintain form longest","Minutes 10-15: Pressure passing - increase speed while maintaining quality","Minutes 15-20: Skill challenge - teach each other one new technique"],"progressions":["Start with basic skills, add complexity","Introduce competitive elements","Film sessions to review technique together"],"coaching_points":["Match your partner's intensity","Compete with respect - push don't punish","Share knowledge freely","Celebrate mutual improvement"],"evaluation_focus":{"pre":"How disciplined is your practice approach? (Practice / Discipline)","post":"Rate improvement in practice intensity and technique (Practice / Discipline)"}},{"id":"tech-prac-003","name":"Group Grind Session","description":"Collective commitment to excellence. Where team standards are set. No one improves alone.","duration":20,"setup":{"participants":["group"],"environment":["indoor","outdoor"],"equipment":["1 ball per player","20 cones","goals"]},"instructions":["Circuit training with 4 technical stations","Minutes 0-5: Station rotation - 1 minute per station, high intensity","Minutes 5-10: Partner competitions at each station","Minutes 10-15: Team technique challenge - synchronised skills","Minutes 15-20: Free practice - players work on personal weaknesses"],"progressions":["Increase station complexity weekly","Add fatigue factors","Introduce team scoring systems"],"coaching_points":["Energy is contagious - bring it","Help struggling teammates","Demand excellence from yourself first","Practice like you play"],"evaluation_focus":{"pre":"How consistent are your technical skills during practice? (Practice / Team Standards)","post":"Assess growth in training commitment and quality (Practice / Team Standards)"}},{"id":"tech-prac-004","name":"Habit Hacker Routine","description":"Build unbreakable muscle memory. Solo grind for solo gains. Champions are made in empty gyms.","duration":20,"setup":{"participants":["solo"],"environment":["indoor"],"equipment":["ball","small space","phone for timer"]},"instructions":["Minutes 0-5: Touch routine - 100 touches each foot, count them","Minutes 5-10: Juggling progression - hit personal records","Minutes 10-15: Close control in confined space - never lose it","Minutes 15-20: Visualisation practice - mental reps with ball at feet"],"progressions":["Increase touch targets weekly","Reduce space available","Add blindfolded elements"],"coaching_points":["Habits form in 21 days - stay consistent","Quality in private shows in public","Mental practice amplifies physical work","Document progress daily"],"evaluation_focus":{"pre":"How consistent are your personal practice habits? (Practice / Habits)","post":"Rate improvement in self-discipline and routine (Practice / Habits)"}},{"id":"tech-prac-005","name":"Pressure Practice Lab","description":"Train under stress, play with ease. Group session where practice meets match pressure.","duration":20,"setup":{"participants":["group"],"environment":["indoor","outdoor"],"equipment":["4 balls","16 cones","bibs","timer"]},"instructions":["Create pressure scenarios at practice stations","Minutes 0-5: Time pressure - complete skills against clock","Minutes 5-10: Defender pressure - practice with interference","Minutes 10-15: Consequence pressure - mistakes mean fitness","Minutes 15-20: Performance pressure - showcase skills to group"],"progressions":["Increase pressure levels gradually","Add multiple pressure types","Create match-specific scenarios"],"coaching_points":["Pressure reveals preparation","Embrace discomfort in practice","Mistakes under pressure teach most","Support teammates through struggles"],"evaluation_focus":{"pre":"How well do you maintain quality under pressure? (Practice / Pressure)","post":"Assess ability to execute skills when stressed (Practice / Pressure)"}}]},
                {"category":"technical","subcategory":"ball-mastery","drills":[{"id":"tech-bm-001","name":"Street Touch Revolution","description":"Raw ball control session. Where the concrete meets silk touch. Master your craft in tight spaces like the legends who learned in cages and car parks.","duration":20,"setup":{"participants":["solo"],"environment":["indoor","outdoor"],"equipment":["ball","4 cones or markers"]},"instructions":["Set up a 2x2m square with cones - your personal arena","Minutes 0-5: Foundation touches - sole rolls, inside/outside alternating. 30 seconds each foot, keep the rhythm","Minutes 5-10: Box boundaries - move the ball around the square edges without leaving. Right foot clockwise, left foot anti-clockwise","Minutes 10-15: Pressure simulation - reduce box to 1x1m. Quick touches, constant movement, never static","Minutes 15-20: Freestyle flow - combine all touches, add your own flavour. This is where style meets substance"],"progressions":["Week 1-2: Focus on clean touches, control over speed","Week 3-4: Add head up challenges - count fingers shown by coach/partner","Week 5+: Introduce time pressures - how many perfect touches in 30 seconds"],"coaching_points":["Low centre of gravity - stay ready, stay sharp","Soft touches - control is king, not power","Both feet equal work - no passengers in this game","Scan even when solo - build the habit"],"evaluation_focus":{"pre":"How confident are you with your ball control in tight spaces? (Ball Mastery / Close Control)","post":"Assess improvement in close control and ambidextrous ability (Ball Mastery / Close Control)"}},{"id":"tech-bm-002","name":"Pressure Cooker Pairs","description":"Two players, one ball, endless possibilities. This is where trust meets technique. Inspired by favela football where space is luxury.","duration":20,"setup":{"participants":["pair"],"environment":["indoor","outdoor"],"equipment":["1 ball per pair","8 cones"]},"instructions":["Create two 3x3m squares, 5m apart","Minutes 0-5: Mirror work - one leads with ball movements, partner shadows without ball","Minutes 5-10: Pressure passing - one player in each square, pass and move to pressure receiver","Minutes 10-15: 1v1 retention - 30 seconds on, 30 seconds off. Keep ball in your square","Minutes 15-20: Creative combinations - nutmegs score double, skills rewarded"],"progressions":["Start with 2-touch maximum, progress to 1-touch under pressure","Reduce square sizes as skills improve","Add point system - successful skills, nutmegs, retention time"],"coaching_points":["Communication is control - talk before you receive","First touch sets everything - make it count","Pressure is privilege - embrace the challenge","Protect the ball with your body - be strong, be smart"],"evaluation_focus":{"pre":"How confident are you receiving under immediate pressure? (Ball Mastery / Pressure)","post":"Did you maintain composure when pressed? Rate your improvement (Ball Mastery / Pressure)"}},{"id":"tech-bm-003","name":"Rondo Revolution","description":"The beautiful game distilled. Where Barcelona meets the block. Group mastery under constant pressure - this is football chess.","duration":20,"setup":{"participants":["group"],"environment":["indoor","outdoor"],"equipment":["1 ball","8 cones","bibs for defenders"]},"instructions":["5v2 in 8x8m grid (adjust for indoor - 6x6m)","Minutes 0-5: Warm up rondo - 2 touch maximum, defenders at 70%","Minutes 5-10: One-touch only - defenders full pressure","Minutes 10-15: Position-specific - players locked to zones, must problem-solve","Minutes 15-20: Winner stays on - complete 10 passes = defenders change, defenders win ball = swap with player who lost it"],"progressions":["Start with 3-touch, progress to 1-touch","Add neutral players on outside for overload practice","Introduce combination plays - through balls count as 3 passes"],"coaching_points":["Body shape is everything - open up to see the whole picture","Move after passing - support isn't optional","Disguise your intentions - wrong-foot the press","Quick feet, quicker minds - think two passes ahead"],"evaluation_focus":{"pre":"How well do you maintain ball control and awareness under pressure? (Ball Mastery / Rondo)","post":"Evaluate decision-making speed and technical execution improvement (Ball Mastery / Rondo)"}},{"id":"tech-bm-004","name":"Wall Ball Warriors","description":"Solo session with a twist. The wall never lies, never tires. Perfect your touch where repetition meets innovation.","duration":20,"setup":{"participants":["solo"],"environment":["indoor"],"equipment":["ball","wall","tape for targets"]},"instructions":["Mark 3 targets on wall: knee height, waist height, head height","Minutes 0-5: Two-touch patterns - receive and play to different heights","Minutes 5-10: One-touch volleys - keep ball in air, vary heights","Minutes 10-15: Turn and burn - back to wall, receive, turn, play back","Minutes 15-20: Skill combos - flicks, headers, chest controls into volleys"],"progressions":["Increase distance from wall as control improves","Add weak foot only minutes","Time challenges - how many perfect touches in 60 seconds"],"coaching_points":["Cushion the ball - kill the pace with soft touches","Stay on your toes - react faster than the rebound","Mix up surfaces - inside, outside, sole, laces","Quality over quantity - perfect practice makes perfect"],"evaluation_focus":{"pre":"How consistent is your first touch from different angles? (Ball Mastery / Wall Work)","post":"Track improvement in touch consistency and reaction speed (Ball Mastery / Wall Work)"}},{"id":"tech-bm-005","name":"Chaos Control Circuit","description":"Group chaos, individual brilliance. Navigate the madness with style. This is where street footballers are forged.","duration":20,"setup":{"participants":["group"],"environment":["outdoor"],"equipment":["1 ball per player","20 cones","bibs"]},"instructions":["20x20m area with random cone gates (2 cones, 1m apart)","Minutes 0-5: Dribble through gates - no queues, find space","Minutes 5-10: Add 2 defenders - if tagged, 5 toe taps before continuing","Minutes 10-15: Partner gates - must pass through gates to partner","Minutes 15-20: Ultimate chaos - half attack gates, half defend. Switch every 2 minutes"],"progressions":["Reduce area size to increase traffic","Add skill requirements through gates","Point system - different gates worth different points"],"coaching_points":["Head up always - see danger, see opportunity","Quick decisions - hesitation is elimination","Protect your ball - shield like your life depends on it","Create your escape - always have an out"],"evaluation_focus":{"pre":"How well do you maintain control in crowded spaces? (Ball Mastery / Chaos)","post":"Assess improvement in spatial awareness and ball protection (Ball Mastery / Chaos)"}}]},
                {"category":"social","subcategory":"teamwork","drills":[{"id":"soc-tw-001","name":"Chain Gang Excellence","description":"Strength in unity. Build unbreakable team bonds through shared struggle. Together everyone achieves more.","duration":20,"setup":{"participants":["group"],"environment":["indoor","outdoor"],"equipment":["4 balls","20 cones","bibs","rope or bands"]},"instructions":["Teams physically or tactically linked","Minutes 0-5: Linked movement - teams move as one unit with ball","Minutes 5-10: Trust falls - possession while supporting teammates","Minutes 10-15: Collective challenges - all must succeed for points","Minutes 15-20: Team celebration creation - build identity together"],"progressions":["Increase interdependence levels","Add competitive elements between units","Create team-specific challenges"],"coaching_points":["Individual success means nothing alone","Weakest link determines team strength","Celebrate collective achievements louder","Build each other up constantly"],"evaluation_focus":{"pre":"How committed are you to supporting your teammates? (Teamwork / Unity)","post":"Assess improvement in collective mindset (Teamwork / Unity)"}},{"id":"soc-tw-002","name":"Sacrifice for Success","description":"Learn when to shine and when to support. Pair work where ego dies and partnership thrives.","duration":20,"setup":{"participants":["pair"],"environment":["indoor","outdoor"],"equipment":["1 ball","6 cones","2 goals or targets"]},"instructions":["One player restricted, other must help them succeed","Minutes 0-5: Blindfold drill - guide partner to complete tasks","Minutes 5-10: One-leg challenge - support partner's movement","Minutes 10-15: Silent support - help without verbal communication","Minutes 15-20: Role reversal - experience both perspectives"],"progressions":["Increase restriction difficulty","Add time pressures","Introduce opposition"],"coaching_points":["Supporting role equally important","Anticipate partner's needs","Success shared is doubled","Learn from both roles"],"evaluation_focus":{"pre":"How willing are you to play supporting roles? (Teamwork / Selfless Play)","post":"Rate growth in selfless team play (Teamwork / Selfless Play)"}},{"id":"soc-tw-003","name":"Collective Comeback Lab","description":"Teams win together, lose together, grow together. Build resilience through shared adversity.","duration":20,"setup":{"participants":["group"],"environment":["indoor","outdoor"],"equipment":["2 balls","goals","cones","bibs"]},"instructions":["Deliberate adversity scenarios","Minutes 0-5: Start 3-0 down - work together to equalise","Minutes 5-10: Play with handicaps - numerical disadvantage","Minutes 10-15: Mistake recovery - team fixes individual errors","Minutes 15-20: Celebration practice - recognise team efforts"],"progressions":["Increase adversity levels","Add mental challenges","Create emotional scenarios"],"coaching_points":["Adversity reveals character","Blame kills teams - support builds them","Every comeback starts with belief","Together through everything"],"evaluation_focus":{"pre":"How does your team handle setbacks? (Teamwork / Resilience)","post":"Assess collective resilience improvement (Teamwork / Resilience)"}},{"id":"soc-tw-004","name":"Trust Fall Football","description":"Build blind faith in teammates. Solo practice for team mindset. Trust starts in training.","duration":20,"setup":{"participants":["solo"],"environment":["indoor"],"equipment":["ball","visualisation space","journal"]},"instructions":["Mental and physical trust exercises","Minutes 0-5: Visualise perfect team movements - trust they'll be there","Minutes 5-10: Practice passes to space - trust runners will arrive","Minutes 10-15: Defensive positioning - trust teammates cover you","Minutes 15-20: Journal team gratitude - recognise others' contributions"],"progressions":["Increase scenario complexity","Add physical trust challenges","Practice with eyes closed"],"coaching_points":["Trust is earned in training","Believe in others' abilities","Your role affects everyone","Gratitude builds bonds"],"evaluation_focus":{"pre":"How much do you trust your teammates? (Teamwork / Trust)","post":"Rate improvement in team faith (Teamwork / Trust)"}},{"id":"soc-tw-005","name":"Share the Spotlight","description":"Make others look good. Group session where assists matter more than goals. Heroes create heroes.","duration":20,"setup":{"participants":["group"],"environment":["outdoor"],"equipment":["multiple balls","goals","cones","bibs"]},"instructions":["Scoring system rewards assists over goals","Minutes 0-5: Pure passing - goal only counts if everyone touches ball","Minutes 5-10: Assist competition - most creative setup wins","Minutes 10-15: Unselfish play - scorer must credit assistant","Minutes 15-20: Team highlights - recreate each other's best moments"],"progressions":["Increase assist requirements","Add specific unselfish play bonuses","Create team play awards"],"coaching_points":["Great players make others great","Assists require more skill than goals","Celebrate others' success genuinely","Team glory over personal glory"],"evaluation_focus":{"pre":"How often do you prioritise others' success? (Teamwork / Unselfish Play)","post":"Track increase in unselfish play (Teamwork / Unselfish Play)"}}]},
                {"category":"social","subcategory":"communication","drills":[{"id":"soc-comm-001","name":"Silent Partner Challenge","description":"Actions speak louder. Master the unspoken language of football. Where eye contact and body language become your superpower.","duration":20,"setup":{"participants":["pair"],"environment":["indoor","outdoor"],"equipment":["2 balls","8 cones","2 bibs"]},"instructions":["Set up 10x10m grid with 4 cone gates on edges","Minutes 0-5: No verbal communication allowed - pass through gates using only gestures","Minutes 5-10: Add defender (rotate every 2 min) - attackers can't speak, must use signals","Minutes 10-15: Switch - now only defender can speak, attackers communicate non-verbally","Minutes 15-20: Full communication - compare effectiveness, develop team signals"],"progressions":["Start with pointing allowed, progress to eyes and body shape only","Add time pressure - must complete gate passes in 30 seconds","Introduce specific hand signals team must create and memorise"],"coaching_points":["Eye contact before the ball - connect first, pass second","Body language sells the fake - deceive with your shape","Develop your own language - create unique team signals","Clear intentions - make your movements deliberate and readable"],"evaluation_focus":{"pre":"How confident are you communicating without words on pitch? (Communication / Non-Verbal)","post":"Rate improvement in non-verbal communication and partner understanding (Communication / Non-Verbal)"}},{"id":"soc-comm-002","name":"Captain's Call","description":"Leadership isn't given, it's taken. Develop your voice, own your space. Every player a leader, every voice matters.","duration":20,"setup":{"participants":["group"],"environment":["outdoor"],"equipment":["4 balls","12 cones","bibs in 3 colours"]},"instructions":["3 teams of 3-4 players in 20x20m area","Minutes 0-5: Rotating captains - each player leads for 90 seconds, directing possession patterns","Minutes 5-10: Pressure calls - 2 teams possess, 1 defends. Constant communication required","Minutes 10-15: Blind trust - 1 player per team eyes closed, teammates guide them verbally","Minutes 15-20: Match scenario - full communication in 3v3v3, most vocal player wins MVP"],"progressions":["Start with simple commands, build to complex tactical calls","Add consequences for silence - 5 seconds quiet = turnover","Introduce code words for specific movements"],"coaching_points":["Project your voice - be heard above the chaos","Timing is everything - call before they need it, not after","Positive reinforcement - build confidence through encouragement","Own your mistakes - call them out, move on stronger"],"evaluation_focus":{"pre":"How confident are you in vocal leadership? (Communication / Vocal)","post":"Assess growth in communication frequency and quality (Communication / Vocal)"}},{"id":"soc-comm-003","name":"Echo Chamber Excellence","description":"Solo work with game impact. Train your voice like you train your touch. Because communication starts with self-belief.","duration":20,"setup":{"participants":["solo"],"environment":["indoor"],"equipment":["ball","phone for recording","cones"]},"instructions":["Set up 5x5m square for movement","Minutes 0-5: Commentary training - narrate your own movements as you dribble","Minutes 5-10: Scenario calls - imagine game situations, practice specific calls while moving","Minutes 10-15: Record yourself - play back, analyse clarity and timing","Minutes 15-20: Power phrases - develop 10 go-to calls, practice with increasing volume"],"progressions":["Start with simple calls, build vocabulary weekly","Add physical exertion - maintain clear communication while tired","Practice in different acoustic environments"],"coaching_points":["Clarity over volume - be understood, not just heard","Breathe and project - use your diaphragm, not your throat","Develop signature calls - make them yours, make them memorable","Practice builds confidence - repetition removes hesitation"],"evaluation_focus":{"pre":"How comfortable are you with your on-pitch voice? (Communication / Self-Talk)","post":"Track confidence growth in vocal projection and clarity (Communication / Self-Talk)"}},{"id":"soc-comm-004","name":"Information Highway","description":"Group intelligence wins games. Build a communication network where everyone's a broadcaster and receiver. Total football needs total communication.","duration":20,"setup":{"participants":["group"],"environment":["indoor","outdoor"],"equipment":["2 balls","16 cones","bibs"]},"instructions":["Set up 4 stations in 15x15m area","Minutes 0-5: Chain communication - pass message through team while passing ball","Minutes 5-10: Multi-ball madness - 2 balls in play, must call name before every pass","Minutes 10-15: Tactical evolution - coach calls formation changes, team must communicate shifts","Minutes 15-20: Pressure test - defenders added, communication under stress"],"progressions":["Increase passing speed requirements","Add specific information that must be communicated","Introduce opposition trying to intercept communications"],"coaching_points":["Everyone has a voice - silence is not an option","Relevant information only - quality over quantity","Support the speaker - acknowledge calls received","Create communication chains - spread information efficiently"],"evaluation_focus":{"pre":"How well does your team communicate during play? (Communication / Information Flow)","post":"Evaluate improvement in information flow and team coordination (Communication / Information Flow)"}},{"id":"soc-comm-005","name":"Pressure Cooker Comms","description":"When the heat rises, legends speak up. Train communication under maximum pressure. This is where quiet players find their roar.","duration":20,"setup":{"participants":["pair"],"environment":["outdoor"],"equipment":["1 ball","10 cones","2 bibs"]},"instructions":["8x8m grid with 2 target zones at opposite ends","Minutes 0-5: Back-to-back passing - players can't see each other, must communicate position","Minutes 5-10: Add floating defender - maintain communication while evading pressure","Minutes 10-15: Countdown pressure - must make 5 passes in 20 seconds with constant communication","Minutes 15-20: Role reversal - quieter player must lead communication for final phase"],"progressions":["Decrease time allowances for passes","Add noise distractions to test focus","Require specific tactical information in calls"],"coaching_points":["Pressure reveals character - rise to the moment","Short, sharp calls - no time for essays","Trust your voice - your teammate needs to hear you","Recovery communication - immediately call next action after mistakes"],"evaluation_focus":{"pre":"How does pressure affect your communication? (Communication / Pressure)","post":"Assess ability to maintain clear communication under stress (Communication / Pressure)"}}]},
                {"category":"psychological","subcategory":"confidence","drills":[{"id":"psy-conf-001","name":"Fearless First Touch","description":"Build unshakeable belief in your ability. Every touch a statement. Confidence starts with the ball at your feet.","duration":20,"setup":{"participants":["solo"],"environment":["indoor","outdoor"],"equipment":["ball","cones","target or wall"]},"instructions":["Progressive confidence challenges","Minutes 0-5: Comfort zone work - master what you know","Minutes 5-10: Stretch zone - attempt skills 80% ready for","Minutes 10-15: Panic zone visits - try the impossible, embrace failure","Minutes 15-20: Success lap - return to mastered skills, feel the growth"],"progressions":["Increase difficulty weekly","Add audience pressure (film yourself)","Set personal skill targets"],"coaching_points":["Confidence comes from competence","Failed attempts build mental strength","Visualise success before attempting","Your only competition is yesterday's you"],"evaluation_focus":{"pre":"How confident are you in your technical ability? (Confidence / Self-Belief)","post":"Assess confidence growth through challenge (Confidence / Self-Belief)"}},{"id":"psy-conf-002","name":"Pressure Proof Protocol","description":"Two minds, one goal: unbreakable confidence. Build each other up when pressure builds.","duration":20,"setup":{"participants":["pair"],"environment":["indoor","outdoor"],"equipment":["2 balls","goals","cones","timer"]},"instructions":["Alternating pressure scenarios","Minutes 0-5: Penalty pressure - take turns, support after misses","Minutes 5-10: Skill showcase - perform under partner's watch","Minutes 10-15: Confidence commentary - narrate partner's brilliance","Minutes 15-20: Mutual mastery - synchronised skill demonstration"],"progressions":["Add audience elements","Increase pressure stakes","Create performance scenarios"],"coaching_points":["Pressure is a privilege","Positive talk changes outcomes","Confidence is contagious - spread it","Champions lift each other"],"evaluation_focus":{"pre":"How does pressure affect your confidence? (Confidence / Pressure)","post":"Rate improvement performing under scrutiny (Confidence / Pressure)"}},{"id":"psy-conf-003","name":"Swagger School Session","description":"Where confidence meets competence. Group energy that builds bulletproof belief. Own your greatness together.","duration":20,"setup":{"participants":["group"],"environment":["indoor","outdoor"],"equipment":["balls","cones","music speaker","bibs"]},"instructions":["High-energy confidence building","Minutes 0-5: Power poses with ball - own your space","Minutes 5-10: Skill cypher - freestyle circle, hype each other","Minutes 10-15: Confidence challenges - volunteers for difficult tasks","Minutes 15-20: Victory lap - everyone showcases improvement"],"progressions":["Increase performance pressure","Add competitive elements","Create team confidence rituals"],"coaching_points":["Energy lifts everyone","Celebrate attempts not just success","Swagger backed by work wins","Build your presence"],"evaluation_focus":{"pre":"How confident are you in group settings? (Confidence / Group Dynamics)","post":"Assess growth in performance confidence (Confidence / Group Dynamics)"}},{"id":"psy-conf-004","name":"Mind Over Matter Marathon","description":"Mental strength for solo sessions. Build confidence through mental repetition. Think like a champion.","duration":20,"setup":{"participants":["solo"],"environment":["indoor"],"equipment":["ball","quiet space","notebook"]},"instructions":["Mental confidence building","Minutes 0-5: Success visualisation - see yourself succeeding","Minutes 5-10: Positive affirmations while juggling","Minutes 10-15: Mistake reframing - practice positive responses","Minutes 15-20: Future self dialogue - embody confident version"],"progressions":["Deepen visualisation detail","Add physical challenges during mental work","Create personal confidence mantras"],"coaching_points":["Mind shapes reality","Words become beliefs","Confidence is a choice","Mental reps count double"],"evaluation_focus":{"pre":"How strong is your mental confidence? (Confidence / Mental Strength)","post":"Rate improvement in self-belief (Confidence / Mental Strength)"}},{"id":"psy-conf-005","name":"Big Game Mentality","description":"Train for the moments that matter. Group pressure that forges champions. Rise when stakes are highest.","duration":20,"setup":{"participants":["group"],"environment":["indoor","outdoor"],"equipment":["balls","goals","cones","bibs","scoreboard"]},"instructions":["Match pressure simulation","Minutes 0-5: Pre-game rituals - build collective confidence","Minutes 5-10: Clutch moments - last minute scenarios","Minutes 10-15: Comeback kings - practice from behind","Minutes 15-20: Trophy moments - celebrate like champions"],"progressions":["Increase scenario pressure","Add crowd noise/distractions","Create knockout tournaments"],"coaching_points":["Big players love big moments","Preparation prevents panic","Visualise success constantly","Pressure makes diamonds"],"evaluation_focus":{"pre":"How confident are you in crucial moments? (Confidence / Big Game)","post":"Track improvement in clutch performance (Confidence / Big Game)"}}]},
                {"category":"psychological","subcategory":"decision-making","drills":[{"id":"psy-dm-001","name":"Split Second Selector","description":"Decisions at the speed of street football. Train your brain to choose fast and choose right. No time for doubt.","duration":20,"setup":{"participants":["solo"],"environment":["indoor","outdoor"],"equipment":["ball","4 different coloured cones","wall or rebounder"]},"instructions":["Place coloured cones as gates around you","Minutes 0-5: Colour calls - receive from wall, coach calls colour during ball flight, exit through that gate","Minutes 5-10: Add maths - solve equation to determine gate (e.g., 2+2 = red gate)","Minutes 10-15: Multiple decisions - colour + direction + skill requirement","Minutes 15-20: Chaos mode - random calls, faster pace, test breaking point"],"progressions":["Reduce decision time from 2 seconds to instant","Add fake calls to test discrimination","Introduce consequence for wrong decisions"],"coaching_points":["Trust your first instinct","Commit fully - half decisions fail","Process while moving - thinking is doing","Wrong decision beats no decision"],"evaluation_focus":{"pre":"How quickly do you make decisions under pressure? (Decision Making / Speed)","post":"Assess improvement in choice speed and accuracy (Decision Making / Speed)"}},{"id":"psy-dm-002","name":"Pass or Possess Matrix","description":"The eternal question answered in milliseconds. Know when to give it, when to keep it. Master the game's biggest decision.","duration":20,"setup":{"participants":["pair"],"environment":["indoor","outdoor"],"equipment":["1 ball","8 cones","2 goals or targets"]},"instructions":["15x10m area with goals at each end","Minutes 0-5: Scenario training - coach calls situations, players react","Minutes 5-10: Live 1v1 - constant decisions with active defender","Minutes 10-15: Overload practice - 2v1, quick decisions on when to combine","Minutes 15-20: Review and repeat - discuss decisions, try alternatives"],"progressions":["Add time pressure - 5 seconds to score","Include point system for good decisions","Introduce multiple defenders"],"coaching_points":["Read the defender's momentum","Space dictates choice - use it wisely","Consider next action before receiving","Sometimes wrong foot is right choice"],"evaluation_focus":{"pre":"How often do you make the right pass/dribble choice? (Decision Making / Choice)","post":"Track decision success rate improvement (Decision Making / Choice)"}},{"id":"psy-dm-003","name":"Information Overload Academy","description":"Train for chaos, play with clarity. When everything happens at once, champions process faster.","duration":20,"setup":{"participants":["group"],"environment":["indoor","outdoor"],"equipment":["3 balls","20 cones","3 sets of bibs"]},"instructions":["3 teams in 20x20m area, multiple objectives","Minutes 0-5: Simple possession - make good decisions with one ball","Minutes 5-10: Add second ball - maintain awareness of both","Minutes 10-15: Third ball + conditions - different rules for each ball","Minutes 15-20: Ultimate chaos - all balls, changing teams, constant decisions"],"progressions":["Increase number of simultaneous decisions","Add verbal information during play","Introduce changing objectives mid-play"],"coaching_points":["Prioritise decisions - not all are equal","Peripheral processing is key","Stay calm in the storm","Quick reset after each decision"],"evaluation_focus":{"pre":"How well do you handle multiple simultaneous choices? (Decision Making / Processing)","post":"Rate improvement in complex decision processing (Decision Making / Processing)"}},{"id":"psy-dm-004","name":"Risk Calculator Live","description":"Every decision has a price. Learn to calculate on the run. Smart players know when to gamble.","duration":20,"setup":{"participants":["pair"],"environment":["indoor","outdoor"],"equipment":["ball","12 cones","scoring zones"]},"instructions":["Create zones with different point values and risk levels","Minutes 0-5: Risk assessment - discuss which zones to target and why","Minutes 5-10: Live execution - attempt planned risks under pressure","Minutes 10-15: Adapt strategy - opponent defends, adjust risk levels","Minutes 15-20: Competition - highest score with smart risks wins"],"progressions":["Add time pressure to decisions","Include negative points for failures","Create dynamic risk values"],"coaching_points":["Calculate risk vs reward instantly","Game situation affects risk tolerance","Learn your success percentages","Adjust strategy based on score"],"evaluation_focus":{"pre":"How well do you assess risk in your decisions? (Decision Making / Risk)","post":"Evaluate improvement in risk/reward judgement (Decision Making / Risk)"}},{"id":"psy-dm-005","name":"Reaction Chain Championship","description":"One decision triggers the next. Train for football's domino effect. Where quick thinking meets quicker execution.","duration":20,"setup":{"participants":["group"],"environment":["indoor","outdoor"],"equipment":["2 balls","16 cones","bibs"]},"instructions":["Set up connected decision stations","Minutes 0-5: Linear chains - decision at station A affects station B","Minutes 5-10: Branching paths - one choice opens multiple options","Minutes 10-15: Pressure chains - defenders force quick decisions","Minutes 15-20: Team championship - which group makes best connected decisions"],"progressions":["Increase chain complexity","Add time limits per decision","Include consequence carries"],"coaching_points":["Think two decisions ahead","First choice affects all others","Team decisions compound","Speed of thought beats speed of feet"],"evaluation_focus":{"pre":"How well do you connect sequential decisions? (Decision Making / Chain)","post":"Rate improvement in decision chain quality (Decision Making / Chain)"}}]},
                {"category":"psychological","subcategory":"understanding","drills":[{"id":"psy-und-001","name":"360° Vision Developer","description":"See the game before it happens. Develop the awareness that separates good from great. Train your mind to process the whole picture.","duration":20,"setup":{"participants":["solo"],"environment":["indoor","outdoor"],"equipment":["ball","6 different coloured cones","wall or rebounder"]},"instructions":["Place coloured cones in semi-circle behind you, 5m away","Minutes 0-5: Pass to wall, coach/app calls colour during ball flight, turn to that cone","Minutes 5-10: Receive from wall, scan before ball arrives, touch towards free space","Minutes 10-15: Add mental maths - solve problems while controlling ball","Minutes 15-20: Memory test - memorise cone sequences while playing"],"progressions":["Start with 3 cones, build to 6","Add time pressure for decisions","Introduce multiple instructions per pass"],"coaching_points":["Scan before you receive - information is power","Picture builds with every glance - constant updates","Trust your peripheral vision - see more than you think","Process while playing - thinking doesn't stop when ball arrives"],"evaluation_focus":{"pre":"How well do you read the game and anticipate play? (Understanding / Vision)","post":"Assess improvement in information gathering and retention (Understanding / Vision)"}},{"id":"psy-und-002","name":"Pattern Recognition Matrix","description":"Football is patterns. Learn to read them like code. Where street smarts meets tactical genius.","duration":20,"setup":{"participants":["group"],"environment":["indoor","outdoor"],"equipment":["3 balls","12 cones","3 sets of bibs"]},"instructions":["3v3+3 in 20x20m area (3 teams, 1 neutral)","Minutes 0-5: Observe only - watch patterns, predict next pass","Minutes 5-10: Join as neutral - learn all three team patterns","Minutes 10-15: Disrupt patterns - defend based on recognition","Minutes 15-20: Create new patterns - innovate based on learning"],"progressions":["Start with simple 3-pass patterns","Progress to complex 5-7 pass sequences","Add conditions that force pattern changes"],"coaching_points":["Players are predictable - learn their habits","Recognise triggers - what makes teams change patterns","File information - build your mental database","Anticipate, don't just react - be two steps ahead"],"evaluation_focus":{"pre":"How well do you read developing play patterns? (Understanding / Patterns)","post":"Evaluate pattern recognition speed and accuracy improvement (Understanding / Patterns)"}},{"id":"psy-und-003","name":"Shadow Intelligence","description":"Train like a detective. Understand movement before it happens. This is football IQ at its finest.","duration":20,"setup":{"participants":["pair"],"environment":["indoor","outdoor"],"equipment":["no ball initially","8 cones","ball for final phase"]},"instructions":["10x10m area, partners start back-to-back","Minutes 0-5: Shadow movement - follower mirrors leader without seeing","Minutes 5-10: Predictive movement - guess partner's next move based on patterns","Minutes 10-15: Add ball - leader has ball, follower must anticipate and intercept","Minutes 15-20: Role reversal with competition - who reads partner better?"],"progressions":["Start with simple movements, add complexity","Reduce reaction time allowed","Add third person as random disruptor"],"coaching_points":["Body language tells stories - learn to read them","Weight distribution reveals intention","Shoulders and hips don't lie - watch for tells","Build player profiles - everyone has tendencies"],"evaluation_focus":{"pre":"How well do you read opponent body language? (Understanding / Movement)","post":"Assess improvement in movement prediction accuracy (Understanding / Movement)"}},{"id":"psy-und-004","name":"Tactical Time Machine","description":"See the future by understanding the present. Develop the game intelligence that coaches dream about.","duration":20,"setup":{"participants":["group"],"environment":["outdoor"],"equipment":["2 balls","20 cones","tactical board","bibs"]},"instructions":["Set up 6v6 scenario in half pitch","Minutes 0-5: Freeze frame - coach stops play, players predict next 3 actions","Minutes 5-10: Rewind analysis - why did previous attacks succeed/fail?","Minutes 10-15: Live prediction - call out what will happen before it does","Minutes 15-20: Tactical adjustment - implement solutions based on understanding"],"progressions":["Start with simple 2v2 scenarios","Build to complex 6v6 patterns","Add specific tactical problems to solve"],"coaching_points":["Every action has a reaction - predict the chain","Space tells you everything - read the gaps","Understand the why, not just the what","Knowledge is power - use it to influence the game"],"evaluation_focus":{"pre":"How well do you understand tactical cause and effect? (Understanding / Tactics)","post":"Evaluate improvement in reading and predicting game flow (Understanding / Tactics)"}},{"id":"psy-und-005","name":"Mind Map Master","description":"Solo session for game intelligence. Build your football brain without touching a ball. Understanding starts in the mind.","duration":20,"setup":{"participants":["solo"],"environment":["indoor"],"equipment":["video clips or tablet","notebook","cones for visualisation"]},"instructions":["Minutes 0-5: Watch game clips - pause and predict next action","Minutes 5-10: Visualise scenarios - create mental games, solve problems","Minutes 10-15: Physical rehearsal - walk through tactical scenarios without ball","Minutes 15-20: Apply learning - create your own tactical problems and solutions"],"progressions":["Start with simple 2v1 scenarios","Progress to complex team movements","Add time pressure to decision making"],"coaching_points":["Visualisation is practice - mental reps count","Break down complexity - understand each component","Question everything - why did that work?","Build your football library - store successful patterns"],"evaluation_focus":{"pre":"How well do you read the game and anticipate play? (Understanding / Mental Map)","post":"Assess growth in analytical thinking and prediction ability (Understanding / Mental Map)"}}]}
            ],
            personas: {
                Player: {
                    Football: {
                        drops: [
                            { id: "drop_liam", name: "SHOT x Liam Davies 'Dangerous'", price: 85.00, stock: 150, image: 'https://i.imgur.com/fTvsGbK.png' },
                            { id: "drop_dermot", name: "SHOT x Dermot Kennedy 'Without Fear'", price: 95.00, stock: 100, image: 'https://pbs.twimg.com/media/DNfW7fnWsAATqK5.jpg' },
                            { id: "drop_corteiz", name: "SHOT x Corteiz Collab", price: 150.00, stock: 500, image: 'https://imgur.com/Q191Z9A.png' },
                            { id: "drop_palace", name: "SHOT x Palace Range", price: 180.00, stock: 100, image: 'https://imgur.com/XInn7FN.png' },
                            { id: "drop_cap", name: "SHOT Tech Enabled Cap", price: 60.00, stock: 250, image: 'https://i.imgur.com/cZtu4Aw.png' },
                            { id: "drop_badge", name: "Physical Sport Head Badge", price: 25.00, stock: 1000, image: 'https://imgur.com/a/2VPwvJa.png' }
                        ],
                        teamwear: [
                            { id: "merch_errea_kit", name: "SHOT x Errea Kit", price: 120.00, stock: 200, image: 'https://imgur.com/l1S0stA.jpeg' },
                            { id: "merch_club", name: "Club Merchandise", price: 45.00, stock: 250, image: 'https://i.imgur.com/2w2rQD4.png' }
                        ]
                    },
                },
            },
            storyHighlights: [
                { id: "sh_001", type: "drop", label: "Dangerous", color: "red", contentTitle: "Dangerous: Limited Drop", contentBody: "Limited only by the Liam Davies x SHOT hoodie and hat combo. Claim 5 bonus entries to win FREE tickets to Liam's next event with every purchase." },
                { id: "sh_009", type: "event", label: "Soccer Aid", color: "teal", contentTitle: "Soccer Aid 2025 Highlights", contentBody: "Relive the best moments from this year's Soccer Aid! Over £15M raised for UNICEF. See exclusive performances and player interviews." },
                { id: "sh_010", type: "music", label: "Charity Single", color: "purple", contentTitle: "SHOT Presents: The Charity Single", contentBody: "Dermot Kennedy, Lana Del Rey, and more team up for 'Fields of Hope'. All proceeds go to grassroots sports foundations. Stream it now." },
                { id: "sh_004", type: "star", label: "Eilish's Next Fight", color: "gold", contentTitle: "Eilish's Next Fight Confirmed!", contentBody: "It's official! Eilish takes on her next opponent at Portman Road, Ipswich. Tickets are on sale this Friday exclusively for SHOT members." },
                { id: "sh_002", type: "event", label: "U12 Skilz Festival", color: "teal", contentTitle: "SHOT Sixes Skilz Festival", contentBody: "Find your nearest SHOT Sixes festival! Enter your postcode below to find locations, dates, and sign-up details. The closest event to you is in Hackney Marshes on July 26th." },
            ],
            pulseArticles: {
                circle: [
                    { id: "pa_c_1", author: "Eilish Tierney", category: "Behind-the-Scenes", title: "NO EASY ROUNDS", access: "CIRCLE", drop: "Friday 07:30AM", content: "4AM shadowboxing. Caffeine. Cold water dip. Eilish documents her comeback from a cracked rib with zero filters. Includes full audio from her recovery coach and a pre-fight visualisation script. Exclusive cam footage inside the Lioness Academy ring.", image: "https://i.imgur.com/mfbT1Hd.png" },
                    { id: "pa_c_2", author: "Kieron Conway", category: "Feature", title: "SUNDAYS ARE FOR THE KIDS", access: "CIRCLE", drop: "Sunday 10AM", content: "Kieron opens the gates to his backyard padwork session with his son. “Every combo I throw is for him now.” Heartbeat mic, slow-motion training frames, and a diary note from his partner about fear, fame, and family." },
                    { id: "pa_c_3", author: "Varsha Bailey-Gunputh", category: "Insight", title: "SHE GUARDS MORE THAN GOALS", access: "CIRCLE", drop: "Monday 6PM", content: "Olympia FC’s breakout keeper shares her “Girlkeeper Playbook” – the language, leadership, and love it takes to help girls feel safe on and off pitch. Downloadable template included for coaches.", image: "https://i.imgur.com/P0DnwDg.png" },
                    { id: "pa_c_4", author: "Ezra Taylor", category: "Drop", title: "NOISE ISN’T FASHION", access: "CIRCLE", drop: "Weds 9PM", content: "First-look at Ezra’s SHOT x Erreà line, inspired by 80s fight posters and Midlands grime. Shot in Nottingham in a single take. This isn’t merch. It’s armour.", image: "https://i.imgur.com/JWvNRVO.png" },
                    { id: "pa_c_5", author: "Ryan Sessegnon", category: "Behind-the-Scenes", title: "THE COMEBACK FILE", access: "CIRCLE", drop: "Thursday 4PM", content: "Mic’d up sideline moments. Unseen gym recovery. Personal reflections from Ryan on being doubted, replaced, and ready again. Playlist, journaling prompt, and physio insights included." },
                ],
                community: [
                    { id: "pa_co_1", author: "Liam Davies", category: "Impact", title: "FIGHT CLUB. FOOD BANK.", access: "COMMUNITY", drop: "Saturday 9AM", content: "Liam’s gym hosted a spar-for-charity event that raised £3K for local families. All proceeds went to a community-run food bank. Kids got gloves, food, and a lesson in fighting for others. Watch the impact reel.", image: "https://www.boxingnews24.com/wp-content/uploads/2020/07/Photo-19-11-2022-23-58-15-2-Boxing-Photos.jpg"},
                    { id: "pa_co_2", author: "Matt Beard", category: "Tactical Insight", title: "BUILDING BURNLEY WOMEN", access: "COMMUNITY", drop: "Monday 3PM", content: "Matt breaks down the principles behind Burnley Women’s top-of-the-league rise. Whiteboard footage, set piece patterns, and a full 1-on-1 with his team captain. Coach Corner content.", video: "https://imgur.com/a/yxXmE1v.mp4" },
                    { id: "pa_co_3", author: "Japh Tanganga + Millwall Community Trust", category: "Feature", title: "THE TRUST", access: "COMMUNITY", drop: "Thursday 7PM", content: "Japh revisits the same South London pitches he played on as a kid. With three Millwall teens by his side, they talk pressure, postcode identity, and what “trust” really means.", video: "https://i.imgur.com/t1nqHur.mp4" },
                    { id: "pa_co_4", author: "SHOT x Erreà", category: "Drop", title: "SHOT x ERREÀ: OWN IT", access: "COMMUNITY", drop: "Saturday 10AM", content: "Full breakdown of the new Erreà x SHOT collab. Track zips. Badge-stitched caps. Heat-reactive liners. Includes behind-the-scenes at the photo shoot and early drop code for members.", image: "https://i.imgur.com/uutfpqd.png" },
                    { id: "pa_co_5", author: "SHOT United x Varsha", category: "Impact", title: "THE GIRLKEEPERS CAMP", access: "COMMUNITY", drop: "Next Week", content: "New training camp announcement. For female players, keepers, and leaders. Led by Varsha with a guest Lioness appearance. Free to SHOT United athletes.", image: "https://i.imgur.com/NQDJDgp.png" },
                ],
                pulse: [
                    { id: "pa_p_1", author: "Dave", category: "Drop", title: "DAVE: THE NUMBER 10", access: "PULSE", drop: "Now", content: "Dave showed up to Power League in a limited-run SHOT x Nike Phantom boot. 3 goals, 6 assists, and a mic’d up bar session after. Best bits on Pulse now." },
                    { id: "pa_p_2", author: "Stormzy", category: "Feature", title: "STORMZY’S NEXT ROUND", access: "PULSE", drop: "Today", content: "In training for a doc? Or just playing with the lines? Stormzy’s recent amateur boxing footage has dropped – pads, jab drills, and his own narration." },
                    { id: "pa_p_3", author: "Alessia Russo", category: "Drop", title: "RUSSO RUMOURS", access: "PULSE", drop: "Tomorrow", content: "Alessia seen in unreleased purple-and-gold SHOT kit. Sources say it’s part of her custom capsule collab. Fashion-forward, game-ready." },
                    { id: "pa_p_4", author: "Declan Rice", category: "Feature", title: "DECLAN. DENIM. DEFENCE.", access: "PULSE", drop: "Friday", content: "Declan posts a tunnel fit in SHOT’s new drop. Clubhouse tee, over-dyed denim, gold laces. Commentary clip includes his view on mixing elite discipline with off-pitch style.", image: "https://d1mnxluw9mpf9w.cloudfront.net/media/13561/declan-rice.jpg" },
                    { id: "pa_p_5", author: "Sidemen", category: "Drop", title: "SIDEMEN LEAGUE WEEK 1", access: "PULSE", drop: "Sunday", content: "KSI and Tobi draft squads for Week 1 of the Sidemen Ballers League. The SHOT Pulse feed will follow: stats, polls, memes, and fan-vote MVPs." },
                ]
            },
            impactInitiatives: {
                communityWins: [
                    { title: "Isleham Skylarks U12s & U14s Finish Strong", content: "Our SHOT United girls’ teams wrapped up the season on a high. U12s and U14s played with grit, style, and serious energy. Every player is re-signing for next season. Proudly sponsored and powered by SHOT.", image: "https://i.imgur.com/FhPZXkY.png" },
                    { title: "Women’s Walking Football Champions", content: "The SHOT-backed Women’s Walking Football Team took the league by storm and lifted the trophy. Proving that age doesn’t limit ambition, they showed exactly what “Take Your Shot” means.", image: "https://i.imgur.com/SHGZMUv.jpeg" }
                ],
                partnerships: [
                    { title: "SHOT x Soham Village College", content: "We’ve launched the SHOT Sixers League in partnership with Cambridge FA and Soham Village College. Students earn volunteering credits by officiating and coaching – building skills while giving back through sport. Real-world XP, powered by SHOT.", image: "https://i.imgur.com/wVJRaOf.jpeg" },
                    { title: "Soham Village College Celebration Sponsor", content: "SHOT proudly sponsored the 2025 End of Year Celebration at Soham Village College – backing the next wave of talent on and off the pitch.", image: "https://i.imgur.com/eYChVVG.png" }
                ],
                givingBack: [
                    { title: "£450 Raised for a Community Defibrillator", content: "Together, we raised £450 to install a life-saving defibrillator in the community. Because sport should be safe, everywhere.", image: "https://placehold.co/600x400/F7B613/FFFFFF?text=Defibrillator" },
                    { title: "World Book Day Donation", content: "We dropped a collection of books into local schools to celebrate World Book Day – backing both body and mind in the SHOT journey.", image: "https://placehold.co/600x400/F7B613/FFFFFF?text=World+Book+Day" },
                    { title: "Trophies for Local Legends", content: "From village grassroots to college end-of-year awards, we supplied trophies to recognise standout contributions across sport and community.", image: "https://placehold.co/600x400/F7B613/FFFFFF?text=Trophies" }
                ],
                coachingReach: [
                    { title: "500+ Coached This Year", content: "In the past 12 months, we’ve coached over 500 people – from children with disabilities and SEN needs, to WSL academy prospects, to adult squads like Cambridge Uni Men’s and local Women’s First Teams. Every level. Every background. One thing in common: they showed up.", image: "https://i.imgur.com/eR7CAPf.png" },
                    { title: "250+ Kids Every Week", content: "Across SHOT United sessions and school partnerships, over 250 children now train with us every single week. A real community in motion. Built on consistency, not hype.", image: "https://i.imgur.com/U5hw4HN.png" }
                ]
            }
        };
        window.mockData = mockData;
    </script>
    
    <!-- MAIN APP SCRIPT -->
    <script type="module">
        // Hoist all function declarations
        let renderClubhouse, renderLocker, renderPerform, renderPulse, renderImpact,
            renderMyRewards, renderTimeline, renderPlayerDetailModal,
            renderSwitchContextModal, renderStoryHighlights, renderStoryDetailModal, 
            renderProfileSwitcherModal, startFullImprovementFlow, renderSuccessModal, 
            renderAIHubModal, startMatchEvaluationFlow, renderMatchEvaluationModal,
            handleMatchEvalSubmit, renderImprovementSetupModal, handleImprovementSetupSubmit, 
            selectDrill, renderPreDrillModal, handlePreDrillEvalSubmit, 
            renderDrillInProgressModal, handlePostDrillEval, handleSubmitFinalEval, 
            renderDrillDetailsHTML, renderCornerSliders, updateSliderValue,
            renderPlayerFormTab, renderPlayerCommentsTab, renderPlayerStatsTab,
            switchPlayerDetailTab, addAccordionListeners;

        let closeModal, showModal;

        const DATA = window.mockData;
        const appState = {
            currentUser: DATA.superUser,
            activePersona: 'Player',
            activeSport: 'Football',
            currentView: 'clubhouse',
            activePulseFilter: 'all',
            currentTeamId: 'team_001',
            improvementFlowData: {}, // For multi-step drill/eval modals
            playerDashboardData: {}, // Will be initialized from localStorage or created new
            chartInstance: null, // To hold the chart instance
            coachPlayerDetailTab: 'form', // 'form', 'comments', 'stats'
        };

        const safeLocalStorage = {
            setItem: (key, value) => {
                try {
                    localStorage.setItem(key, value);
                } catch (e) {
                    console.warn('LocalStorage not available:', e);
                }
            },
            getItem: (key) => {
                try {
                    return localStorage.getItem(key);
                } catch (e) {
                    console.warn('LocalStorage not available:', e);
                    return null;
                }
            }
        };

        const dataManager = {
            getTeamById: (teamId) => DATA.teams.find(t => t.id === teamId),
            getPlayersByTeam: (teamId) => DATA.players.filter(p => p.teamId === teamId),
            getPlayerById: (playerId) => DATA.players.find(p => p.id === playerId),
            getPlayerProfile: (playerId) => DATA.playerProfiles[playerId],
            getFixturesByTeam: (teamId) => DATA.fixtures.filter(f => f.teamId === teamId).sort((a, b) => new Date(b.date) - new Date(a.date)),
            getClubById: (clubId) => DATA.clubs.find(c => c.id === clubId),
            getSportTerminology: (sport) => {
                const terms = {
                    Football: { player: 'Baller', coach: 'Coach', event: 'Match', club: 'Club' },
                    Boxing: { player: 'Boxer', coach: 'Trainer', event: 'Fight', club: 'Gym' },
                };
                return terms[sport] || { player: 'Athlete', coach: 'Coach', event: 'Event', club: 'Club' };
            },
            getCurrentFocusTargets: (playerId) => {
                // Return mock focus targets for demo
                return [
                    { corner: 'technical', name: 'Ball Control' },
                    { corner: 'physical', name: 'Stamina' },
                    { corner: 'psychological', name: 'Confidence' },
                    { corner: 'social', name: 'Communication' },
                    { corner: 'positional', name: 'Positioning' }
                ];
            }
        };

        const mainContent = document.getElementById('main-content');
        
        const safeFeatherReplace = () => {
            try {
                if (typeof feather !== 'undefined' && feather.replace) {
                    feather.replace();
                }
            } catch (e) {
                console.warn('Feather icons not loaded:', e);
            }
        };

        const createChart = (ctx, config) => {
            try {
                if (typeof Chart === 'undefined') {
                    console.warn('Chart.js not loaded');
                    return null;
                }
                
                // Destroy existing chart if any
                if (appState.chartInstance) {
                    appState.chartInstance.destroy();
                }
                
                appState.chartInstance = new Chart(ctx, config);
                return appState.chartInstance;
            } catch (e) {
                console.error('Error creating chart:', e);
                return null;
            }
        };

        // --- EVENT HANDLERS & LOGIC ---
        const updateSPDisplay = () => {
            const spTotalEl = document.getElementById('sp-total');
            if (spTotalEl) spTotalEl.innerText = `${appState.currentUser.sp.toLocaleString()} SP`;
        };
        
        const addPoints = (amount, reason) => {
            appState.currentUser.sp += amount;
            // In a real app, this would be saved to a database. For now, it's in-memory.
            console.log(`Earned ${amount} SP for: ${reason}. Total SP: ${appState.currentUser.sp}`);
            updateSPDisplay();
        };

        const switchContext = (sport) => {
            appState.activeSport = sport;
            const teamForSport = DATA.teams.find(t => t.sport.toLowerCase() === sport.toLowerCase());
            if (teamForSport) {
               appState.currentTeamId = teamForSport.id;
            }
            closeModal();
            navigateTo('perform');
        };

        // --- ROUTING & APP RENDER ---
        const navigateTo = (view, context) => {
            appState.currentView = view;
            if (context) {
                if(view === 'coach-player-detail') {
                    appState.currentEvaluationPlayerId = context;
                }
            }
            renderApp();
            mainContent.scrollTop = 0;
        };

        const renderApp = () => {
            mainContent.innerHTML = ''; 
            mainContent.className = 'flex-1 overflow-y-auto scroll-smooth';
            
            const viewRenderers = {
                'clubhouse': renderClubhouse, 
                'locker': renderLocker, 
                'perform': renderPerform, 
                'pulse': renderPulse,
                'impact': renderImpact,
                'timeline': renderTimeline,
                'my-rewards': renderMyRewards,
            };

            const renderFunction = viewRenderers[appState.currentView] || viewRenderers['clubhouse'];
            renderFunction();
            
            updateBottomNav();
            updateSPDisplay();
            safeFeatherReplace();
        };
        
        // --- MODAL FUNCTIONS ---
        closeModal = () => { 
            if (appState.chartInstance) {
                appState.chartInstance.destroy();
                appState.chartInstance = null;
            }
            const modalContainer = document.getElementById('modal-container'); 
            if (modalContainer) {
                modalContainer.innerHTML = ''; 
                modalContainer.classList.add('pointer-events-none');
            }
            if(appState.improvementFlowData && appState.improvementFlowData.timerId) {
                clearInterval(appState.improvementFlowData.timerId);
                appState.improvementFlowData.timerId = null;
            }
        };

        showModal = (modalHTML, onOkCallback = null, isLarge = false) => {
            const modalContainer = document.getElementById('modal-container');
            if (!modalContainer) return;
            
            const content = typeof modalHTML === 'function' ? modalHTML() : modalHTML;
            if (typeof content === 'undefined') return;

            const sizeClass = isLarge ? 'max-w-2xl' : 'max-w-md';
            const overlay = document.createElement('div');
            overlay.className = 'fixed inset-0 bg-black bg-opacity-80 flex justify-center items-center z-50 p-4';
            
            if (content.includes('h-full') || content.includes('inset-0')) {
                overlay.innerHTML = content;
            } else {
                 overlay.innerHTML = `<div class="bg-shot-surface rounded-2xl p-6 w-full ${sizeClass} space-y-4 relative shadow-2xl fade-in max-h-[90vh] overflow-y-auto hide-scrollbar" onclick="event.stopPropagation()">${content}</div>`;
            }

            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) closeModal();
            });

            modalContainer.innerHTML = '';
            modalContainer.appendChild(overlay);
            modalContainer.classList.remove('pointer-events-none');
            safeFeatherReplace();
            addAccordionListeners();

            const okBtn = overlay.querySelector('.modal-ok-btn');
            if (okBtn) {
                okBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    closeModal();
                    if (onOkCallback) onOkCallback();
                });
            }
            const closeBtn = overlay.querySelector('.modal-close-btn');
            if(closeBtn) closeBtn.addEventListener('click', closeModal);
        };

        renderSuccessModal = (title, message) => `<i data-feather="check-circle" class="w-16 h-16 mx-auto text-shot-teal"></i> <h2 class="font-poppins font-bold text-lg text-center">${title}</h2> <p class="text-sm text-secondary text-center">${message}</p> <button class="modal-ok-btn w-full bg-shot-teal text-white font-bold py-2 rounded-lg mt-4">OK</button>`;
        
        renderAIHubModal = () => {
            const handleAIChatSubmit = (e) => {
                e.preventDefault();
                const input = document.getElementById('ai-chat-input');
                const prompt = input.value.trim();
                input.value = '';
                
                const nextFixture = DATA.fixtures.find(f => new Date(f.date) >= new Date() && f.sport === appState.activeSport);
                let responseText = "I'm sorry, I can't answer that right now.";
                if (nextFixture) {
                    responseText = `Your next event is a ${nextFixture.type} against <strong>${nextFixture.opponent}</strong> on ${new Date(nextFixture.date).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })} at ${nextFixture.time}.`;
                } else {
                    responseText = "You don't have any upcoming events scheduled.";
                }
                showModal(renderSuccessModal('AI Response', responseText));
            };
            window.handleAIChatSubmit = handleAIChatSubmit;

            const quickClick = (prompt) => {
                let responseTitle = "AI Response";
                let responseText = "Here is the information you requested.";

                if (prompt.includes("concert")) {
                    responseText = "Dermot Kennedy is playing at the O2 Arena on August 15th. Tickets are available via the Pulse feed.";
                } else if (prompt.includes("rewards")) {
                    closeModal();
                    navigateTo('my-rewards');
                    return;
                } else if (prompt.includes("improve") || prompt.includes("drill")) {
                    closeModal();
                    startFullImprovementFlow();
                    return;
                }
                showModal(renderSuccessModal(responseTitle, responseText));
            }
            window.quickClick = quickClick;

            return `
            <div class="fixed inset-0 bg-shot-bg flex flex-col z-50" onclick="event.stopPropagation()">
                <div class="p-4 bg-shot-surface/80 backdrop-blur-sm">
                    <div class="flex justify-between items-center">
                        <h2 class="text-2xl font-poppins font-bold text-shot-purple">SPORT HEAD AI</h2>
                        <button onclick="closeModal()" class="text-gray-400 hover:text-white modal-close-btn"><i data-feather="x" class="w-6 h-6"></i></button>
                    </div>
                </div>
                <div class="flex-1 p-4 overflow-y-auto space-y-4 flex flex-col justify-end">
                    <div class="p-3 rounded-lg chat-bubble-ai max-w-xs md:max-w-md bg-shot-surface">
                        <p class="text-sm">Hi ${appState.currentUser.name}, how can I help you improve today?</p>
                    </div>
                </div>
                <div class="p-4 bg-shot-surface/80 backdrop-blur-sm">
                     <div class="grid grid-cols-2 gap-2 mb-3">
                         ${DATA.aiPrompts.map(prompt => {
                             const escapedPrompt = prompt.replace(/'/g, "\\'");
                             return `<button onclick="quickClick('${escapedPrompt}')" class="text-left text-sm bg-shot-purple text-white p-3 rounded-lg hover:bg-shot-purple/80 font-semibold">${prompt}</button>`
                         }).join('')}
                     </div>
                     <form onsubmit="handleAIChatSubmit(event)" class="flex items-center space-x-2">
                         <input id="ai-chat-input" type="text" class="flex-1 bg-shot-bg border border-gray-700 rounded-lg p-3 text-sm focus:outline-none focus:ring-2 focus:ring-shot-purple" placeholder="Ask me anything...">
                         <button type="submit" class="bg-shot-purple p-3 rounded-lg text-white"><i data-feather="send" class="w-5 h-5"></i></button>
                     </form>
                </div>
            </div>`;
        };
        
        renderSwitchContextModal = () => {
            return `<div>
                <h2 class="font-poppins font-bold text-lg uppercase">Switch Context</h2>
                <p class="text-sm text-secondary mb-4">Select your primary sport context.</p>
                <div class="space-y-2">
                    <button class="w-full text-left p-3 rounded-lg bg-gray-700" onclick="switchContext('Football')">West Ham United FC</button>
                    <button class="w-full text-left p-3 rounded-lg bg-gray-700" onclick="switchContext('Boxing')">Eastside Boxing Gym</button>
                </div>
            </div>`;
        };

        renderProfileSwitcherModal = () => {
            const user = appState.currentUser;
            const nextRewardTier = DATA.userRewards.find(r => r.cost > user.sp && r.status === 'available') || { cost: 20000 };
            const spPercentage = (user.sp / nextRewardTier.cost) * 100;
            const circumference = 2 * Math.PI * 36;
            const strokeDashoffset = circumference - (spPercentage / 100) * circumference;

            return ` <div class="fixed inset-0 bg-black/70 flex justify-end" onclick="closeModal()"> <div class="bg-shot-surface w-4/5 max-w-sm h-full p-6 space-y-6 overflow-y-auto" onclick="event.stopPropagation()"> <div class="flex items-center space-x-4"> <div class="relative"> <svg class="w-20 h-20 transform -rotate-90"> <circle cx="40" cy="40" r="36" stroke="rgba(255,255,255,0.1)" stroke-width="4" fill="transparent" /> <circle cx="40" cy="40" r="36" stroke="var(--shot-purple)" stroke-width="4" fill="transparent" stroke-linecap="round" stroke-dasharray="${circumference}" stroke-dashoffset="${strokeDashoffset}" /> </svg> <img src="${user.avatar}" class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 h-16 w-16 rounded-full border-2 border-shot-surface object-cover"> </div> <div> <p class="text-xl font-poppins font-bold">${user.name}</p> <p class="text-secondary">${appState.activePersona} | ${appState.activeSport}</p> </div> </div> <div class="border-t border-gray-700 pt-6 space-y-2"> <h3 class="font-semibold mb-2">My SHOT</h3> <button class="w-full text-left p-3 rounded-lg bg-gray-700 flex items-center" onclick="navigateTo('timeline'); closeModal();"> <i data-feather="activity" class="w-5 h-5 mr-3"></i> My Timeline </button> <button class="w-full text-left p-3 rounded-lg bg-gray-700 flex items-center" onclick="navigateTo('my-rewards'); closeModal();"> <i data-feather="award" class="w-5 h-5 mr-3"></i> My Rewards </button> </div> <!-- Achievements Section --> <div class="border-t border-gray-700 pt-4 mt-4 space-y-2"> <h3 class="font-semibold mb-2">Achievements</h3> <div class="flex space-x-4 overflow-x-auto pb-2"> ${user.achievements.map(ach => ` <div class="flex flex-col items-center flex-shrink-0 text-center w-16"> <div class="w-12 h-12 rounded-full bg-shot-gold/20 flex items-center justify-center"> <i data-feather="${ach.icon}" class="text-shot-gold"></i> </div> <p class="text-xs mt-1 text-secondary truncate">${ach.title}</p> </div> `).join('')} </div> </div> <div class="border-t border-gray-700 pt-4 mt-4"> <button class="w-full text-left p-3 rounded-lg bg-gray-700 flex items-center"> <i data-feather="settings" class="w-5 h-5 mr-3"></i> Profile Settings </button> </div> </div> </div> `;
        };

        renderStoryDetailModal = (storyId) => {
            const stories = DATA.storyHighlights;
            const currentIndex = stories.findIndex(s => s.id === storyId);
            if (currentIndex === -1) return '';

            const story = stories[currentIndex];
            const prevIndex = (currentIndex - 1 + stories.length) % stories.length;
            const nextIndex = (currentIndex + 1) % stories.length;

            return `
                <div class="fixed inset-0 bg-black/90 flex items-center justify-center z-50" onclick="closeModal()">
                    <div class="relative w-full max-w-lg p-4">
                         <div class="bg-shot-surface rounded-xl p-6 text-center space-y-4 relative">
                            <button onclick="closeModal()" class="modal-close-btn absolute top-2 right-2 text-gray-400 hover:text-white z-10"><i data-feather="x"></i></button>
                            <h2 class="font-poppins font-bold text-2xl">${story.contentTitle}</h2>
                            <p class="text-secondary">${story.contentBody}</p>
                            <button class="w-full bg-shot-teal text-white font-bold py-3 rounded-lg mt-4 modal-ok-btn">Got it</button>
                        </div>
                        <button onclick="showModal(renderStoryDetailModal('${stories[prevIndex].id}'))" class="absolute left-0 top-1/2 -translate-y-1/2 bg-white/10 p-2 rounded-full text-white"><i data-feather="chevron-left"></i></button>
                        <button onclick="showModal(renderStoryDetailModal('${stories[nextIndex].id}'))" class="absolute right-0 top-1/2 -translate-y-1/2 bg-white/10 p-2 rounded-full text-white"><i data-feather="chevron-right"></i></button>
                    </div>
                </div>
            `;
        };

        renderStoryHighlights = () => {
            const stories = DATA.storyHighlights;
            if (stories.length === 0) return '';
            
            const html = `<div class="mb-4">
                <div class="flex space-x-4 overflow-x-auto pb-2 px-4 hide-scrollbar">
                ${stories.map(story => `
                    <div class="flex-shrink-0 w-20 text-center cursor-pointer" onclick="showModal(renderStoryDetailModal('${story.id}'))">
                        <div class="w-20 h-20 rounded-full story-highlight-ring ${story.color} flex items-center justify-center">
                             <div class="w-[72px] h-[72px] bg-shot-surface rounded-full flex flex-col items-center justify-center p-1">
                                 <p class="text-xs font-semibold leading-tight text-center">${story.label}</p>
                             </div>
                        </div>
                    </div>
                `).join('')}
                </div>
            </div>`;

            return html;
        };
        
        const renderClubhouseSection = (title, icon, items, navView) => {
            if (!items || items.length === 0) return '';
            return `
            <div>
                <div class="flex justify-between items-center px-4 mb-3">
                    <div class="flex items-center space-x-3">
                        <i data-feather="${icon}" class="text-shot-teal"></i>
                        <h2 class="text-xl font-poppins font-bold uppercase">${title}</h2>
                    </div>
                    <button onclick="navigateTo('${navView}')" class="text-xs font-semibold text-shot-teal">View All &rarr;</button>
                </div>
                <div class="flex space-x-4 overflow-x-auto pb-4 px-4 hide-scrollbar" style="scroll-snap-type: x mandatory;">
                    ${items.join('')}
                </div>
            </div>
            `
        };

        // --- CORE RENDER FUNCTIONS ---
        renderClubhouse = () => {
            mainContent.classList.add('space-y-6', 'fade-in', 'pb-6');
            const personaData = DATA.personas.Player.Football; // Simplified for demo
            const pulseFeed = DATA.pulseArticles.pulse;
            const nextFixture = DATA.fixtures.find(f => new Date(f.date) >= new Date() && f.sport === appState.activeSport);

            const performTiles = [];
            performTiles.push(`<div class="bg-shot-purple/20 border border-shot-purple rounded-xl p-4 w-64 flex-shrink-0" style="scroll-snap-align: start;"><p class="text-sm text-secondary">Your evaluation window is open.</p><button onclick="navigateTo('perform')" class="w-full text-center bg-shot-purple text-white font-bold py-2 rounded-lg mt-2 animate-pulse">Start Evaluation</button></div>`);
            if (nextFixture) {
                performTiles.push(`<div class="bg-gray-800 rounded-xl p-4 w-64 flex-shrink-0" style="scroll-snap-align: start;">
                    <p class="text-xs text-secondary">Next Match</p>
                    <p class="font-bold text-lg">vs ${nextFixture.opponent}</p>
                    <p class="text-sm text-secondary">${new Date(nextFixture.date).toLocaleDateString('en-US', {weekday: 'short', month: 'short', day: 'numeric'})}, ${nextFixture.time}</p>
                </div>`);
            }
             performTiles.push(`<div class="bg-gray-800 rounded-xl p-4 w-64 flex-shrink-0 cursor-pointer" style="scroll-snap-align: start;" onclick="startFullImprovementFlow()"><p class="font-bold text-lg">Improve Your Game</p><p class="text-sm text-secondary">Generate a personalized drill.</p></div>`);

            const impactTile = `<div class="bg-shot-gold/20 border border-shot-gold rounded-xl p-4 w-64 flex-shrink-0 cursor-pointer" style="scroll-snap-align: start;" onclick="navigateTo('impact')"><p class="font-bold text-lg">SHOT Impact</p><p class="text-sm text-secondary">See how we're making a difference in the community.</p></div>`;

            const lockerTiles = (personaData.drops || []).slice(0, 5).map(item => `<div class="bg-gray-800 rounded-xl w-40 flex-shrink-0 aspect-square" style="scroll-snap-align: start;" onclick="navigateTo('locker')"><img src="${item.image}" class="h-2/3 w-full object-cover rounded-t-lg"><div class="p-2 h-1/3 flex flex-col justify-between"><p class="text-sm font-semibold truncate">${item.name}</p><p class="text-xs text-shot-teal font-bold">£${item.price.toFixed(2)}</p></div></div>`);
            const pulseTiles = (pulseFeed || []).slice(0, 5).map(post => `<div class="bg-gray-800 rounded-xl w-64 flex-shrink-0 p-3" style="scroll-snap-align: start;" onclick="navigateTo('pulse')"><div class="flex items-center space-x-2 mb-2"><p class="font-semibold text-sm">${post.title}</p></div><p class="text-sm text-secondary">${post.content.substring(0, 100)}...</p></div>`);

            mainContent.innerHTML = `
                <div class="p-4"><h1 class="text-3xl font-poppins font-bold uppercase">Clubhouse</h1></div>
                ${renderStoryHighlights()}
                <div class="space-y-8">
                    ${renderClubhouseSection('PERFORM', 'trending-up', performTiles, 'perform')}
                    ${renderClubhouseSection('IMPACT', 'heart', [impactTile], 'impact')}
                    ${renderClubhouseSection('LOCKER', 'shopping-bag', lockerTiles, 'locker')}
                    ${renderClubhouseSection('PULSE', 'globe', pulseTiles, 'pulse')}
                </div>`;
            safeFeatherReplace();
        };

        renderLocker = () => { 
            const personaData = DATA.personas.Player.Football; // Simplified for demo
            mainContent.innerHTML = `
            <div class="p-4 space-y-6 fade-in"> 
                <h1 class="text-3xl font-poppins font-bold uppercase">The Locker</h1> 
                <p class="text-secondary">This isn't just a shop. Your purchases create impact and earn you SP. Style with purpose.</p> 
                <div>
                    <h2 class="text-xl font-poppins font-semibold mb-3">Latest Drops</h2>
                    <div class="grid grid-cols-2 sm:grid-cols-3 gap-4">${(personaData.drops || []).map(item => `<div class="bg-shot-surface rounded-xl group aspect-square flex flex-col"><div class="relative h-2/3"><img src="${item.image}" class="w-full h-full object-cover rounded-t-xl"><div class="absolute top-2 right-2 bg-shot-red text-white text-xs font-bold px-2 py-1 rounded-md">${item.stock} LEFT</div></div><div class="p-3 h-1/3 flex flex-col justify-between"><p class="font-semibold text-sm truncate">${item.name}</p><div class="flex justify-between items-center mt-2"><p class="text-sm font-bold text-shot-teal">£${item.price.toFixed(2)}</p><button class="bg-shot-teal p-1.5 rounded-full"><i data-feather="plus" class="w-4 h-4 text-white"></i></button></div></div></div>`).join('')}</div>
                </div>
                <div>
                    <h2 class="text-xl font-poppins font-semibold mb-3">Your Merchandise Shop</h2>
                    <div class="grid grid-cols-2 sm:grid-cols-3 gap-4">${(personaData.teamwear || []).map(item => `<div class="bg-shot-surface rounded-xl group aspect-square flex flex-col"><img src="${item.image}" class="w-full h-2/3 object-cover rounded-t-xl"><div class="p-3 h-1/3 flex flex-col justify-between"><p class="font-semibold text-sm truncate">${item.name}</p><div class="flex justify-between items-center mt-2"><p class="text-sm font-bold text-shot-teal">£${item.price.toFixed(2)}</p><button class="bg-shot-teal p-1.5 rounded-full"><i data-feather="plus" class="w-4 h-4 text-white"></i></button></div></div></div>`).join('')}</div>
                </div>
            </div>`; 
            safeFeatherReplace(); 
        };

        renderPulse = () => {
            let feed = [];
            if (appState.activePulseFilter === 'innerCircle') {
                feed = DATA.pulseArticles.circle;
            } else if (appState.activePulseFilter === 'community') {
                feed = DATA.pulseArticles.community;
            } else {
                feed = DATA.pulseArticles.pulse;
            }

            const setPulseFilter = (filter) => { appState.activePulseFilter = filter; renderPulse(); };
            window.setPulseFilter = setPulseFilter;

            const accessColors = {
                'CIRCLE': 'text-shot-purple',
                'COMMUNITY': 'text-shot-teal',
                'PULSE': 'text-shot-gold'
            };

            mainContent.innerHTML = `
            <div class="p-4 space-y-8 fade-in"> 
                <div><h1 class="text-3xl font-poppins font-bold uppercase">Pulse</h1><p class="text-secondary">The heart of the SHOT sport and culture movement.</p></div>
                <div class="flex justify-center items-center space-x-4">
                    <button onclick="setPulseFilter('innerCircle')" class="text-center">
                        <div class="pulse-filter-btn purple w-16 h-16 rounded-full flex items-center justify-center bg-shot-surface border-2 border-gray-700 ${appState.activePulseFilter === 'innerCircle' ? 'active' : ''}"><i data-feather="star" class="w-8 h-8"></i></div>
                        <p class="text-xs mt-2 font-semibold ${appState.activePulseFilter === 'innerCircle' ? 'text-shot-purple' : 'text-secondary'}">Circle</p>
                    </button>
                    <button onclick="setPulseFilter('community')" class="text-center">
                        <div class="pulse-filter-btn teal w-16 h-16 rounded-full flex items-center justify-center bg-shot-surface border-2 border-gray-700 ${appState.activePulseFilter === 'community' ? 'active' : ''}"><i data-feather="users" class="w-8 h-8"></i></div>
                        <p class="text-xs mt-2 font-semibold ${appState.activePulseFilter === 'community' ? 'text-shot-teal' : 'text-secondary'}">Community</p>
                    </button>
                    <button onclick="setPulseFilter('all')" class="text-center">
                        <div class="pulse-filter-btn gold w-16 h-16 rounded-full flex items-center justify-center bg-shot-surface border-2 border-gray-700 ${appState.activePulseFilter === 'all' ? 'active' : ''}"><i data-feather="globe" class="w-8 h-8"></i></div>
                        <p class="text-xs mt-2 font-semibold ${appState.activePulseFilter === 'all' ? 'text-shot-gold' : 'text-secondary'}">Pulse</p>
                    </button>
                </div>
                <div class="space-y-4">
                    ${feed.map(post => {
                        let mediaHTML = '';
                        if (post.image) {
                            mediaHTML = `<img src="${post.image}" class="w-full object-cover rounded-lg aspect-square my-2">`;
                        } else if (post.video) {
                            mediaHTML = `<video controls class="w-full rounded-lg my-2" src="${post.video}"></video>`;
                        }
                        return `
                        <div class="bg-shot-surface p-4 rounded-xl">
                            <div class="flex justify-between items-start mb-2">
                                <div>
                                    <p class="text-xs font-semibold text-secondary">${post.author.toUpperCase()}</p>
                                    <h3 class="font-poppins font-bold text-xl text-white">${post.title}</h3>
                                </div>
                                <span class="text-xs font-bold ${accessColors[post.access]} border ${accessColors[post.access].replace('text-', 'border-')} rounded-full px-2 py-0.5">${post.access}</span>
                            </div>
                            ${mediaHTML}
                            <p class="text-secondary text-sm mb-3">${post.content}</p>
                            <div class="flex justify-between items-center text-xs text-secondary">
                                <span><i data-feather="tag" class="w-3 h-3 inline-block mr-1"></i>${post.category}</span>
                                <span><i data-feather="clock" class="w-3 h-3 inline-block mr-1"></i>${post.drop}</span>
                            </div>
                        </div>
                    `}).join('')}
                </div>
            </div>`; 
            safeFeatherReplace(); 
        };

        renderImpact = () => {
            const { communityWins, partnerships, givingBack, coachingReach } = DATA.impactInitiatives;

            const renderSection = (title, items) => `
                <div class="mb-8">
                    <h2 class="text-2xl font-poppins font-bold uppercase text-shot-teal mb-4">${title}</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        ${items.map(item => `
                            <div class="bg-shot-surface rounded-lg overflow-hidden">
                                <img src="${item.image}" alt="${item.title}" class="w-full h-40 object-cover">
                                <div class="p-4">
                                    <h3 class="font-semibold text-lg">${item.title}</h3>
                                    <p class="text-secondary text-sm mt-1">${item.content}</p>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;

            mainContent.innerHTML = `
                <div class="p-4 space-y-6 fade-in">
                    <div>
                        <h1 class="text-3xl font-poppins font-bold uppercase">SHOT Impact</h1>
                        <p class="text-secondary">Making a difference, on and off the pitch.</p>
                    </div>
                    ${renderSection('Community Wins', communityWins)}
                    ${renderSection('Partnerships with Purpose', partnerships)}
                    ${renderSection('Giving Back', givingBack)}
                    ${renderSection('Our Coaching Reach', coachingReach)}
                </div>
            `;
            safeFeatherReplace();
        };

        renderTimeline = () => {
             mainContent.innerHTML = `
                <div class="p-4 space-y-4 fade-in">
                    <h1 class="text-3xl font-poppins font-bold uppercase">My Timeline</h1>
                    <p class="text-secondary">Your recent activity and SP earnings.</p>
                    <div class="space-y-3">
                    ${DATA.eventHistory.map(event => `<div class="bg-shot-surface p-4 rounded-lg flex justify-between items-center"><div><p class="font-semibold">${event.title}</p><p class="text-xs text-secondary">${new Date(event.date).toLocaleDateString()}</p></div><p class="font-bold ${event.sp > 0 ? 'text-shot-gold' : 'text-shot-red'}">${event.sp > 0 ? '+' : ''}${event.sp} SP</p></div>`).join('')}
                    </div>
                </div>`;
            safeFeatherReplace();
        };
        
        renderMyRewards = () => {
            const now = new Date();
            mainContent.innerHTML = `
                <div class="p-4 space-y-4 fade-in">
                    <h1 class="text-3xl font-poppins font-bold uppercase">My Rewards</h1>
                    <p class="text-secondary">Use your SP to unlock exclusive rewards.</p>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    ${DATA.userRewards.map(reward => {
                        let status = reward.status;
                        if (status === 'available' && reward.expiryDate && new Date(reward.expiryDate) < now) {
                            status = 'expired';
                        }
                        const isClaimable = status === 'available' && appState.currentUser.sp >= reward.cost;

                        return `<div class="bg-shot-surface p-4 rounded-lg flex flex-col justify-between ${status !== 'available' ? 'opacity-50' : ''}">
                            <div>
                                <p class="font-semibold">${reward.title}</p>
                                <p class="text-xs text-secondary mt-1">${reward.description || ''}</p>
                                ${reward.expiryDate ? `<p class="text-xs text-shot-red mt-1">Expires: ${new Date(reward.expiryDate).toLocaleDateString()}</p>` : ''}
                            </div>
                            <div class="mt-4">
                                <p class="text-lg font-bold my-2 text-shot-gold">${reward.cost.toLocaleString()} SP</p>
                                <button class="w-full py-2 rounded-lg text-sm font-semibold ${isClaimable ? 'bg-shot-teal' : 'bg-gray-600'}" ${!isClaimable ? 'disabled' : ''}>
                                    ${status === 'claimed' ? 'Claimed' : (status === 'expired' ? 'Expired' : (isClaimable ? 'Claim' : 'Not Enough SP'))}
                                </button>
                            </div>
                        </div>`
                    }).join('')}
                    </div>
                </div>`;
            safeFeatherReplace();
        };

        // --- MERGED & REFACTORED PERFORM/EVALUATION FUNCTIONS ---

        renderPerform = () => {
            const team = dataManager.getTeamById(appState.currentTeamId);
            if (!team) {
                mainContent.innerHTML = `<div class="p-4 text-center"><p class="text-secondary">Team data not found.</p></div>`;
                return;
            }
            
            const club = dataManager.getClubById(team.clubId);
            const terminology = dataManager.getSportTerminology(appState.activeSport);
            const teamPlayers = dataManager.getPlayersByTeam(appState.currentTeamId);
            const allFixtures = dataManager.getFixturesByTeam(appState.currentTeamId);
            const upcomingFixtures = allFixtures.filter(f => f.status === 'upcoming').slice(0, 3);
            const previousFixtures = allFixtures.filter(f => f.status === 'completed').slice(0, 3);

            const playerName = appState.currentUser.name;
            const hasDashboardData = appState.playerDashboardData[playerName];
            let dashboardHTML = '';

            if (hasDashboardData) {
                const dashboard = appState.playerDashboardData[playerName];
                const scores = dashboard.scores;
                const evalsDone = dashboard.history.length;
                const avgScore = evalsDone > 0 ? (Object.values(scores).reduce((a, b) => parseFloat(a) + parseFloat(b), 0) / Object.values(scores).length).toFixed(1) : '0.0';


                const corners = [
                    { name: 'TECHNICAL', key: 'technical', color: 'var(--corner-technical)', icon: 'sliders' },
                    { name: 'PHYSICAL', key: 'physical', color: 'var(--corner-physical)', icon: 'zap' },
                    { name: 'PSYCHOLOGICAL', key: 'psychological', color: 'var(--corner-psychological)', icon: 'activity' },
                    { name: 'SOCIAL', key: 'social', color: 'var(--corner-social)', icon: 'users' },
                    { name: 'POSITIONAL', key: 'positional', color: 'var(--corner-positional)', icon: 'crosshair' },
                ];

                dashboardHTML = `
                    <div class="bg-shot-surface p-4 rounded-xl text-center">
                         <h2 class="font-poppins font-semibold">MY DASHBOARD - ${playerName}</h2>
                         <div class="grid grid-cols-3 gap-2 mt-4 text-center">
                            <div>
                                <p class="text-2xl font-bold">${evalsDone}</p>
                                <p class="text-xs text-secondary uppercase">Evals Done</p>
                            </div>
                            <div>
                                <p class="text-2xl font-bold text-shot-teal">${avgScore > 0 ? avgScore : 'N/A'}</p>
                                <p class="text-xs text-secondary uppercase">Avg Score</p>
                            </div>
                             <div>
                                <p class="text-2xl font-bold">${appState.currentUser.streaks.training}</p>
                                <p class="text-xs text-secondary uppercase">Streak</p>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        ${corners.map(corner => {
                            const score = scores[corner.key] || 0;
                            return `
                            <div class="bg-shot-bg p-4 rounded-xl border-l-4" style="border-color: ${corner.color};">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-semibold text-white uppercase text-sm">${corner.name}</h3>
                                    <i data-feather="${corner.icon}" class="w-4 h-4" style="color: ${corner.color};"></i>
                                </div>
                                <p class="text-3xl font-poppins font-bold mt-1">${parseFloat(score).toFixed(1)}</p>
                                <div class="w-full bg-gray-700 rounded-full h-1.5 mt-2">
                                    <div class="h-1.5 rounded-full" style="width: ${score / 5 * 100}%; background-color: ${corner.color};"></div>
                                </div>
                            </div>
                            `
                        }).join('')}
                    </div>
                `;
            }

            mainContent.innerHTML = `
                <div class="p-4 space-y-6 fade-in">
                    <div>
                        <div class="bg-gray-800 p-2 rounded-lg inline-flex items-center space-x-2 cursor-pointer" onclick="showModal(renderSwitchContextModal)">
                            <span class="text-xs font-bold">${terminology.club.toUpperCase()}: ${club.name}</span>
                            <i data-feather="chevron-down" class="w-4 h-4"></i>
                        </div>
                        <h1 class="text-3xl font-poppins font-bold uppercase mt-2">MY PERFORM</h1>
                        <p class="text-secondary">My Team: ${team.name}</p>
                    </div>

                    ${dashboardHTML}

                    <div class="bg-shot-surface p-4 rounded-xl text-center">
                        <h2 class="font-poppins font-semibold">MY PERFORM DASHBOARD</h2>
                        <div class="grid grid-cols-2 gap-2 mt-4">
                            <button onclick="startMatchEvaluationFlow()" class="w-full bg-shot-teal text-white font-bold py-3 rounded-lg">Start Evaluation</button>
                            <button onclick="startFullImprovementFlow()" class="w-full bg-shot-purple text-white font-bold py-3 rounded-lg">Improve</button>
                        </div>
                    </div>

                    <div>
                        <h2 class="text-xl font-poppins font-semibold mb-3">My Team</h2>
                        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                        ${teamPlayers.map(p => `<div class="bg-shot-surface p-2 rounded-lg text-center cursor-pointer" onclick="renderPlayerDetailModal('${p.id}')"><img src="${p.avatar}" class="w-12 h-12 rounded-full mx-auto mb-2"><p class="text-sm font-semibold truncate">${p.name}</p><p class="text-xs text-secondary">${p.position}</p></div>`).join('')}
                        </div>
                    </div>
                    
                    <div>
                        <h2 class="text-xl font-poppins font-semibold mb-3">Next 3 Sessions</h2>
                        <div class="space-y-2">
                        ${upcomingFixtures.map(f => `<div class="bg-shot-surface p-3 rounded-lg flex justify-between items-center"><p>${f.type}: ${f.opponent}</p><p class="text-sm text-secondary">${new Date(f.date).toLocaleDateString('en-GB', {day:'2-digit', month:'short'})}</p></div>`).join('')}
                        </div>
                    </div>

                    <div>
                        <h2 class="text-xl font-poppins font-semibold mb-3">Previous 3 Sessions</h2>
                        <div class="space-y-2">
                        ${previousFixtures.map(f => `<div class="bg-shot-surface p-3 rounded-lg flex justify-between items-center cursor-pointer hover:bg-gray-700"><p>${f.type}: ${f.opponent}</p><p class="text-sm font-bold text-shot-gold">${f.evaluation || 'N/A'}</p></div>`).join('')}
                        </div>
                    </div>
                </div>`;
            safeFeatherReplace();
        };

        renderPlayerDetailModal = (playerId) => {
            const player = dataManager.getPlayerById(playerId);
            if (!player) return;

            const renderTabContent = () => {
                switch(appState.coachPlayerDetailTab) {
                    case 'form': return renderPlayerFormTab(playerId);
                    case 'comments': return renderPlayerCommentsTab(playerId);
                    case 'stats': return renderPlayerStatsTab(playerId);
                    default: return '';
                }
            }

            const modalContent = `
                <div class="text-left">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-2xl font-poppins font-bold text-white">${player.name}</h2>
                        <button type="button" onclick="closeModal()" class="text-gray-400 hover:text-white modal-close-btn"><i data-feather="x"></i></button>
                    </div>
                    <div class="flex items-center space-x-4 mb-4">
                        <img src="${player.avatar}" class="w-20 h-20 rounded-full border-2 border-shot-purple object-cover">
                        <div>
                            <p class="font-semibold text-lg">${player.position}</p>
                            <p class="text-secondary">Objective: ${player.positionalObjective}</p>
                        </div>
                    </div>
                    
                    <!-- Tab Navigation -->
                    <div class="flex border-b border-gray-700 mb-4">
                        <button class="flex-1 py-2 text-center font-semibold border-b-2 ${appState.coachPlayerDetailTab === 'form' ? 'text-shot-teal border-shot-teal' : 'text-secondary border-transparent'}" onclick="switchPlayerDetailTab('${playerId}', 'form')">Form</button>
                        <button class="flex-1 py-2 text-center font-semibold border-b-2 ${appState.coachPlayerDetailTab === 'comments' ? 'text-shot-teal border-shot-teal' : 'text-secondary border-transparent'}" onclick="switchPlayerDetailTab('${playerId}', 'comments')">Comments</button>
                        <button class="flex-1 py-2 text-center font-semibold border-b-2 ${appState.coachPlayerDetailTab === 'stats' ? 'text-shot-teal border-shot-teal' : 'text-secondary border-transparent'}" onclick="switchPlayerDetailTab('${playerId}', 'stats')">Stats</button>
                    </div>

                    <!-- Tab Content -->
                    <div id="player-detail-content">
                        ${renderTabContent()}
                    </div>
                </div>
            `;
            showModal(modalContent, null, true);
            if (appState.coachPlayerDetailTab === 'form') {
                renderPlayerFormTab(playerId, true); // re-render to attach chart
            }
        };

        switchPlayerDetailTab = (playerId, tab) => {
            appState.coachPlayerDetailTab = tab;
            renderPlayerDetailModal(playerId);
        };

        renderPlayerFormTab = (playerId, isRerender = false) => {
            const player = dataManager.getPlayerById(playerId);
            const latestEval = player.evaluations[0];
            const focusTargets = dataManager.getCurrentFocusTargets ? dataManager.getCurrentFocusTargets(playerId) : [];

            const cornerData = Object.keys(latestEval.data).map(cornerName => ({
                name: cornerName,
                avg: parseFloat(latestEval.data[cornerName]).toFixed(1),
                focus: (focusTargets.find(t => t.corner.toUpperCase() === cornerName.toUpperCase()) || {}).name || 'N/A',
                color: `var(--corner-${cornerName.toLowerCase()})`,
            }));

            const html = `
                <div class="space-y-6">
                    <div class="bg-shot-surface p-4 rounded-xl">
                        <div class="flex justify-between items-center mb-4">
                            <h2 class="font-poppins font-semibold text-lg text-white">PERFORM Progress</h2>
                            <p class="text-sm text-secondary">Last 12 Months</p>
                        </div>
                        <div class="h-48">
                            <canvas id="player-form-chart"></canvas>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        ${cornerData.map(corner => `
                        <div class="bg-shot-bg p-4 rounded-xl border-l-4" style="border-color: ${corner.color};">
                            <h3 class="font-semibold text-white">${corner.name}</h3>
                            <p class="text-xs text-secondary mt-2 truncate">Focus: ${corner.focus}</p>
                            <p class="text-2xl font-poppins font-bold mt-1">${corner.avg}</p>
                            <div class="w-full bg-gray-700 rounded-full h-1.5 mt-1">
                                <div class="h-1.5 rounded-full" style="width: ${corner.avg / 5 * 100}%; background-color: ${corner.color};"></div>
                            </div>
                        </div>`).join('')}
                    </div>
                </div>`;
            
            if (isRerender) {
                const progressCtx = document.getElementById('player-form-chart');
                if (progressCtx) {
                    const last12Evals = player.evaluations.slice(0, 12).reverse();
                    const chartLabels = last12Evals.map(e => e.month.substring(0,3));
                    
                    const datasets = Object.keys(player.evaluations[0].data).map(corner => {
                        const cornerName = corner.charAt(0).toUpperCase() + corner.slice(1);
                        return {
                            label: cornerName,
                            data: last12Evals.map(e => e.data[cornerName]),
                            borderColor: `var(--corner-${corner.toLowerCase()})`,
                            backgroundColor: `var(--corner-${corner.toLowerCase()})`,
                            fill: false,
                            tension: 0.4,
                            pointRadius: 2,
                            pointBackgroundColor: `var(--corner-${corner.toLowerCase()})`,
                        }
                    });

                    if (appState.chartInstance) appState.chartInstance.destroy();
                    createChart(progressCtx, {
                        type: 'line',
                        data: { labels: chartLabels, datasets: datasets },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: { legend: { display: true, position: 'bottom', labels: { color: 'white', boxWidth: 12, font: { size: 10 } } } },
                            scales: {
                                y: { beginAtZero: false, max: 5, grid: { color: 'rgba(255,255,255,0.1)' }, ticks: { color: 'white'} },
                                x: { grid: { display: false }, ticks: { color: 'white' } }
                            }
                        }
                    });
                }
            }
            return html;
        };

        renderPlayerCommentsTab = (playerId) => {
            const profile = dataManager.getPlayerProfile(playerId) || { thePlayer: '', desires: '', privateNotes: '', history: [] };
            const latestHistory = profile.history[0] || {date: 'N/A', comments: {}};
            const olderHistory = profile.history.slice(1);

            return `
                <div class="space-y-6">
                    <div class="bg-shot-bg rounded-xl">
                        <button class="accordion-header open w-full flex justify-between items-center p-4">
                            <h3 class="font-semibold text-lg">Coach's Notes</h3>
                            <i data-feather="chevron-down" class="icon-rotate rotate-180"></i>
                        </button>
                        <div class="accordion-content open">
                            <div class="p-2 space-y-4">
                                <div>
                                    <h4 class="font-bold text-sm text-shot-purple">The Player (Personality)</h4>
                                    <p class="text-sm text-secondary">${profile.thePlayer.replace(/\n/g, '<br>')}</p>
                                </div>
                                <div>
                                    <h4 class="font-bold text-sm text-shot-purple">Desires & Motivations</h4>
                                    <p class="text-sm text-secondary">${profile.desires.replace(/\n/g, '<br>')}</p>
                                </div>
                                <div>
                                    <h4 class="font-bold text-sm text-shot-purple">Private Notes</h4>
                                    <p class="text-sm text-secondary">${(profile.privateNotes || 'No private notes added.').replace(/\n/g, '<br>')}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>`;
        };

        renderPlayerStatsTab = (playerId) => {
            const player = dataManager.getPlayerById(playerId);
            const stats = player.stats || {matches: 0, started: 0, potm: 0};
            const playedPercent = stats.matches > 0 ? ((stats.started / stats.matches) * 100).toFixed(0) : 0;

            return `
                <div class="grid grid-cols-2 gap-4">
                    <div class="bg-shot-bg p-4 rounded-xl text-center">
                        <p class="text-3xl font-poppins font-bold text-shot-teal">${stats.matches}</p>
                        <p class="text-sm text-secondary">Appearances</p>
                    </div>
                    <div class="bg-shot-bg p-4 rounded-xl text-center">
                        <p class="text-3xl font-poppins font-bold text-shot-teal">${stats.started}</p>
                        <p class="text-sm text-secondary">Started</p>
                    </div>
                    <div class="bg-shot-bg p-4 rounded-xl text-center">
                        <p class="text-3xl font-poppins font-bold text-shot-teal">${playedPercent}%</p>
                        <p class="text-sm text-secondary">% Started</p>
                    </div>
                    <div class="bg-shot-bg p-4 rounded-xl text-center">
                        <p class="text-3xl font-poppins font-bold text-shot-gold">${stats.potm}</p>
                        <p class="text-sm text-secondary">Player of the Match</p>
                    </div>
                </div>`;
        };

        addAccordionListeners = () => {
            document.querySelectorAll('.accordion-header').forEach(header => {
                header.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const content = header.nextElementSibling;
                    const icon = header.querySelector('.icon-rotate');
                    header.classList.toggle('open');
                    content.classList.toggle('open');
                    if (icon) icon.classList.toggle('rotate-180');
                });
            });
        };

        startFullImprovementFlow = () => {
            appState.improvementFlowData = {}; // Reset data
            renderImprovementSetupModal();
        };

        startMatchEvaluationFlow = () => {
            renderMatchEvaluationModal();
        };

        renderImprovementSetupModal = () => {
            const framework = DATA.performFramework;
            const modalContent = `
                <form id="improvement-setup-form">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-2xl font-poppins font-bold text-shot-purple uppercase">Create a Drill</h2>
                        <button type="button" onclick="closeModal()" class="text-gray-400 hover:text-white modal-close-btn"><i data-feather="x"></i></button>
                    </div>
                    <p class="text-secondary mb-6 text-left">Select your training type and a focus from each corner to generate a personalized drill.</p>
                    
                    <div class="space-y-4 text-left">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="drill-participants" class="block text-sm font-medium mb-1">HOW ARE YOU TRAINING?</label>
                                <select id="drill-participants" name="participants" class="w-full bg-shot-surface text-white border-gray-700 rounded-lg p-3 text-sm">
                                    <option class="styled-select-option" value="solo">Solo</option> <option class="styled-select-option" value="pair">In a Pair</option> <option class="styled-select-option" value="group">In a Group</option>
                                </select>
                            </div>
                            <div>
                                <label for="drill-environment" class="block text-sm font-medium mb-1">LOCATION</label>
                                <select id="drill-environment" name="environment" class="w-full bg-shot-surface text-white border-gray-700 rounded-lg p-3 text-sm">
                                    <option class="styled-select-option" value="indoor">Indoor</option> <option class="styled-select-option" value="outdoor">Outdoor</option>
                                </select>
                            </div>
                        </div>
                        ${Object.entries(framework).filter(([key]) => key !== 'POSITIONAL').map(([corner, subcategories]) => `
                            <div>
                                <label for="focus-${corner.toLowerCase()}" class="block text-sm font-medium mb-1 uppercase" style="color: var(--corner-${corner.toLowerCase()})">${corner}</label>
                                <select id="focus-${corner.toLowerCase()}" name="focus_${corner.toLowerCase()}" required class="w-full text-white font-semibold border-gray-700 rounded-lg p-3 text-sm" style="background-color: var(--corner-${corner.toLowerCase()});">
                                    <option class="styled-select-option" value="" disabled selected>Choose a ${corner.toLowerCase()} focus...</option>
                                    ${Object.entries(subcategories).map(([key, value]) => `<option class="styled-select-option" value="${key}">${value}</option>`).join('')}
                                </select>
                            </div>
                        `).join('')}
                    </div>
                    <button type="submit" class="w-full bg-shot-purple text-white font-bold py-3 rounded-lg mt-6 uppercase">Create Drill</button>
                </form>
            `;
            showModal(modalContent, null, true);
            document.getElementById('improvement-setup-form').addEventListener('submit', handleImprovementSetupSubmit);
        };

        handleImprovementSetupSubmit = (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const setupData = Object.fromEntries(formData.entries());
            appState.improvementFlowData.setup = setupData;

            const criteria = {
                participants: setupData.participants,
                environment: setupData.environment,
                corners: {
                    focus_technical: setupData.focus_technical,
                    focus_social: setupData.focus_social,
                    focus_psychological: setupData.focus_psychological,
                    focus_physical: setupData.focus_physical,
                }
            };
            
            const { drill, adaptationNotes } = selectDrill(criteria);
            
            if (!drill) {
                showModal(renderSuccessModal('Drill Not Found', 'Could not find a suitable drill for your selection. Please try another combination.'));
                return;
            }

            appState.improvementFlowData.drill = drill;
            appState.improvementFlowData.adaptationNotes = adaptationNotes;
            
            renderPreDrillModal();
        };
        
        selectDrill = (criteria) => {
            const { participants, environment, corners } = criteria;
            const allDrills = DATA.shotDrills.flatMap(category => category.drills);
            
            let possibleDrills = allDrills.filter(d => 
                d.setup.participants.includes(participants) && 
                d.setup.environment.includes(environment)
            );

            let adaptationNotes = "";
            if (possibleDrills.length === 0) {
                possibleDrills = allDrills.filter(d => d.setup.participants.includes(participants));
                if (possibleDrills.length > 0) {
                    adaptationNotes = "This drill has been adapted for your environment. Be creative!";
                }
            }
            if (possibleDrills.length === 0) {
                possibleDrills = allDrills.filter(d => d.setup.participants.includes('solo'));
                if (possibleDrills.length > 0) {
                    adaptationNotes = "This drill has been adapted for your setup. Be creative!";
                }
            }
            if(possibleDrills.length === 0) {
                return { drill: DATA.shotDrills[0].drills[0], adaptationNotes: "No perfect match found. Here's a general drill." };
            }

            let bestMatch = possibleDrills[0];
            let maxScore = -1;

            possibleDrills.forEach(drill => {
                let currentScore = 0;
                const drillCategory = DATA.shotDrills.find(cat => cat.drills.some(d => d.id === drill.id));
                if (drillCategory) {
                    if (drillCategory.category === 'technical' && drillCategory.subcategory === corners.focus_technical) currentScore++;
                    if (drillCategory.category === 'social' && drillCategory.subcategory === corners.focus_social) currentScore++;
                    if (drillCategory.category === 'psychological' && drillCategory.subcategory === corners.focus_psychological) currentScore++;
                    if (drillCategory.category === 'physical' && drillCategory.subcategory === corners.focus_physical) currentScore++;
                }
                if (currentScore > maxScore) {
                    maxScore = currentScore;
                    bestMatch = drill;
                }
            });
            
            return { drill: { ...bestMatch }, adaptationNotes };
        };

        renderDrillDetailsHTML = () => {
            const { drill, adaptationNotes, setup } = appState.improvementFlowData;
            return `
                <div class="bg-shot-bg p-4 rounded-lg">
                    <h3 class="text-xl font-bold font-poppins text-shot-teal">${drill.name}</h3>
                    <p class="text-sm text-secondary italic mt-1">${drill.description}</p>
                </div>
                ${adaptationNotes ? `<div class="bg-shot-gold/20 text-shot-gold p-3 rounded-lg text-sm font-semibold">${adaptationNotes}</div>` : ''}
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="bg-shot-bg p-3 rounded-lg"><strong><i data-feather="users" class="inline w-4 h-4 mr-1"></i>For:</strong> ${setup.participants}</div>
                    <div class="bg-shot-bg p-3 rounded-lg"><strong><i data-feather="tool" class="inline w-4 h-4 mr-1"></i>Equipment:</strong> ${drill.setup.equipment.join(', ')}</div>
                </div>
                <div>
                    <h4 class="font-bold text-lg mb-2">Instructions</h4>
                    <ol class="list-decimal list-inside space-y-2 text-sm bg-shot-bg p-3 rounded-lg">
                        ${drill.instructions.map(inst => `<li>${inst}</li>`).join('')}
                    </ol>
                </div>
                <div>
                    <h4 class="font-bold text-lg mb-2">Coaching Points</h4>
                    <ul class="list-disc list-inside space-y-2 text-sm bg-shot-bg p-3 rounded-lg">
                        ${drill.coaching_points.map(point => `<li>${point}</li>`).join('')}
                    </ul>
                </div>
            `;
        }

        renderPreDrillModal = () => {
            const { drill } = appState.improvementFlowData;
            const modalContent = `
                <div class="text-left space-y-4">
                    <div class="flex justify-between items-center">
                        <h2 class="text-2xl font-poppins font-bold text-shot-purple uppercase">Your Drill & Pre-Eval</h2>
                        <button type="button" onclick="closeModal()" class="text-gray-400 hover:text-white modal-close-btn"><i data-feather="x"></i></button>
                    </div>
                    ${renderDrillDetailsHTML()}
                    <p class="text-sm text-center text-secondary mb-2 pt-4 border-t border-gray-700"><strong>Pre-Drill Question:</strong> ${drill.evaluation_focus.pre}</p>
                    <form id="pre-drill-eval-form" class="space-y-4">
                        ${renderCornerSliders(['technical', 'physical', 'psychological', 'social'])}
                        <button type="submit" class="w-full bg-shot-teal text-white font-bold py-3 rounded-lg uppercase mt-4">Start Drill</button>
                    </form>
                </div>
            `;
            showModal(modalContent, null, true);
            document.getElementById('pre-drill-eval-form').addEventListener('submit', handlePreDrillEvalSubmit);
        };

        handlePreDrillEvalSubmit = (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            appState.improvementFlowData.preDrill = Object.fromEntries(formData.entries());
            renderDrillInProgressModal();
        };

        renderDrillInProgressModal = () => {
            let seconds = 1200; // 20 minutes for the drill
            const modalContent = `
                <div class="text-left space-y-4">
                    <h2 class="text-2xl font-poppins font-bold uppercase text-center">Drill in Progress</h2>
                    <div id="countdown-timer" class="text-6xl font-poppins font-bold my-4 text-center">20:00</div>
                    ${renderDrillDetailsHTML()}
                    <button onclick="handlePostDrillEval()" class="w-full bg-shot-teal text-white font-bold py-3 rounded-lg uppercase mt-4">Complete Drill</button>
                </div>
            `;
            showModal(modalContent, null, true);
            
            const timerEl = document.getElementById('countdown-timer');
            appState.improvementFlowData.timerId = setInterval(() => {
                seconds--;
                const mins = Math.floor(seconds / 60);
                const secs = seconds % 60;
                if(timerEl) timerEl.textContent = `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                if (seconds <= 0) {
                    clearInterval(appState.improvementFlowData.timerId);
                    if(timerEl) timerEl.textContent = "Time's Up!";
                    handlePostDrillEval();
                }
            }, 1000);
        };

        handlePostDrillEval = () => {
            clearInterval(appState.improvementFlowData.timerId);
            const { drill } = appState.improvementFlowData;
            
            const modalContent = `
                <div class="text-left">
                    <h2 class="text-2xl font-poppins font-bold uppercase text-center">Post-Drill Evaluation</h2>
                    <p class="text-secondary my-4 text-center">Great work! Now, rate yourself again on the same areas.</p>
                    <p class="text-sm text-center text-secondary mb-4"><strong>Post-Drill Question:</strong> ${drill.evaluation_focus.post}</p>
                    <form id="post-drill-eval-form" class="space-y-4 mt-4">
                        ${renderCornerSliders(['technical', 'physical', 'psychological', 'social'])}
                        <button type="submit" class="w-full bg-shot-purple text-white font-bold py-3 rounded-lg uppercase mt-4">Submit Final Evaluation</button>
                    </form>
                </div>
            `;
            showModal(modalContent, null, true);
            document.getElementById('post-drill-eval-form').addEventListener('submit', handleSubmitFinalEval);
        };

        handleSubmitFinalEval = (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            appState.improvementFlowData.postDrill = Object.fromEntries(formData.entries());
            
            const playerName = appState.currentUser.name;
            if (!appState.playerDashboardData[playerName]) {
                appState.playerDashboardData[playerName] = { scores: {}, history: [] };
            }
            
            const postDrillRatings = appState.improvementFlowData.postDrill;
            const newScores = {
                technical: parseFloat(postDrillRatings.rating_technical),
                physical: parseFloat(postDrillRatings.rating_physical),
                psychological: parseFloat(postDrillRatings.rating_psychological),
                social: parseFloat(postDrillRatings.rating_social),
            };
            
            const currentScores = appState.playerDashboardData[playerName].scores;
            for(const corner in newScores) {
                if(currentScores[corner]) {
                    currentScores[corner] = (currentScores[corner] + newScores[corner]) / 2;
                } else {
                    currentScores[corner] = newScores[corner];
                }
            }
            appState.playerDashboardData[playerName].scores = currentScores;
            appState.playerDashboardData[playerName].history.push(appState.improvementFlowData);
            safeLocalStorage.setItem('playerDashboardData', JSON.stringify(appState.playerDashboardData));
            
            addPoints(150, `Completed drill: ${appState.improvementFlowData.drill.name}`);
            appState.currentUser.streaks.training += 1;

            const successMessage = `Well done, ${playerName.split(' ')[0]}! You've earned 150 SP. Your evaluation has been saved and your coach has been notified.`;
            showModal(renderSuccessModal('Evaluation Submitted!', successMessage), () => navigateTo('perform'));
        };

        renderMatchEvaluationModal = () => {
            const lastFixture = DATA.fixtures.find(f => f.status === 'completed');
            const modalContent = `
                <form id="match-eval-form">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-2xl font-poppins font-bold text-shot-purple uppercase">Post-Match Evaluation</h2>
                        <button type="button" onclick="closeModal()" class="text-gray-400 hover:text-white modal-close-btn"><i data-feather="x"></i></button>
                    </div>
                    <p class="text-secondary mb-4 text-left">Rate your performance in the last match vs <strong>${lastFixture ? lastFixture.opponent : 'your last opponent'}</strong>.</p>
                    
                    ${renderCornerSliders(['technical', 'physical', 'psychological', 'social', 'positional'])}

                    <div>
                        <label for="comments-match" class="block text-sm font-medium mb-1 mt-4">COMMENTS</label>
                        <textarea id="comments-match" name="comments" rows="3" class="w-full bg-shot-bg border border-gray-700 rounded-lg p-3 text-sm" placeholder="What went well? What could be improved?"></textarea>
                    </div>

                    <button type="submit" class="w-full bg-shot-purple text-white font-bold py-3 rounded-lg mt-6 uppercase">Submit Evaluation</button>
                </form>
            `;
            showModal(modalContent, null, true);
            document.getElementById('match-eval-form').addEventListener('submit', handleMatchEvalSubmit);
        };

        handleMatchEvalSubmit = (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const evalData = Object.fromEntries(formData.entries());
            
            const playerName = appState.currentUser.name;
            if (!appState.playerDashboardData[playerName]) {
                appState.playerDashboardData[playerName] = { scores: {}, history: [] };
            }
            
            const newScores = {
                technical: parseFloat(evalData.rating_technical),
                physical: parseFloat(evalData.rating_physical),
                psychological: parseFloat(evalData.rating_psychological),
                social: parseFloat(evalData.rating_social),
                positional: parseFloat(evalData.rating_positional),
            };
            
            const currentScores = appState.playerDashboardData[playerName].scores;
            for(const corner in newScores) {
                if(currentScores[corner] && currentScores[corner] > 0) {
                    currentScores[corner] = (parseFloat(currentScores[corner]) + newScores[corner]) / 2; // Simple average for now
                } else {
                    currentScores[corner] = newScores[corner];
                }
            }
            
            appState.playerDashboardData[playerName].scores = currentScores;
            appState.playerDashboardData[playerName].history.push({
                type: 'match_eval',
                date: new Date().toISOString(),
                data: evalData
            });
            safeLocalStorage.setItem('playerDashboardData', JSON.stringify(appState.playerDashboardData));
            
            addPoints(100, `Completed match evaluation`);
            
            const successMessage = `Thanks, ${playerName.split(' ')[0]}! Your evaluation has been saved. You've earned 100 SP.`;
            showModal(renderSuccessModal('Evaluation Saved!', successMessage), () => navigateTo('perform'));
        };

        renderCornerSliders = (cornersToRender, cornerExamples = {}) => {
            const allCorners = [
                { key: 'technical', name: 'TECHNICAL', color: 'var(--corner-technical)' },
                { key: 'physical', name: 'PHYSICAL', color: 'var(--corner-physical)' },
                { key: 'psychological', name: 'PSYCHOLOGICAL', color: 'var(--corner-psychological)' },
                { key: 'social', name: 'SOCIAL', color: 'var(--corner-social)' },
                { key: 'positional', name: 'POSITIONAL', color: 'var(--corner-positional)' }
            ];

            const corners = allCorners.filter(c => cornersToRender.includes(c.key));
            const ratingLabels = `<div class="text-xs text-secondary flex justify-between mt-1"><span>Focus</span><span>Good</span><span>Great</span></div>`;
            
            return `
            <div class="space-y-4 pt-4 border-t border-gray-700">
                <p class="font-semibold text-center">PLAYER EVALUATION</p>
                ${corners.map(corner => {
                    const exampleText = cornerExamples[corner.key] ? ` (${cornerExamples[corner.key]})` : '';
                    return `
                    <div>
                        <label class="block text-sm font-medium" style="color: ${corner.color}">${corner.name}${exampleText}</label>
                        <div class="flex items-center space-x-4">
                            <input type="range" id="rating-${corner.key}" name="rating_${corner.key}" min="1" max="5" value="3" step="0.5" class="slider-input" oninput="updateSliderValue('rating-${corner.key}', 'rating-${corner.key}-value')">
                            <span id="rating-${corner.key}-value" class="font-bold text-lg w-10 text-center">3.0</span>
                        </div>
                        ${ratingLabels}
                    </div>
                    `;
                }).join('')}
            </div>
            `;
        };

        updateSliderValue = (sliderId, outputId) => {
            const slider = document.getElementById(sliderId);
            const output = document.getElementById(outputId);
            if(slider && output) {
                output.textContent = parseFloat(slider.value).toFixed(1);
            }
        };


        const updateBottomNav = () => {
            const nav = document.getElementById('bottom-nav');
            const navItems = [
                { view: 'clubhouse', icon: 'home', label: 'CLUBHOUSE'},
                { view: 'locker', icon: 'shopping-bag', label: 'LOCKER'},
                { view: 'aihub', icon: 'cpu', label: 'AI'},
                { view: 'perform', icon: 'trending-up', label: 'PERFORM'},
                { view: 'pulse', icon: 'globe', label: 'PULSE'},
            ];
            
            nav.innerHTML = navItems.map(item => {
                if (item.view === 'aihub') {
                    return `<button data-view="aihub" class="nav-btn flex flex-col items-center -translate-y-4">
                                <div class="bg-shot-purple p-4 rounded-full shadow-lg shadow-purple-500/50">
                                    <i data-feather="cpu" class="text-white"></i>
                                </div>
                            </button>`;
                }
                const isActive = item.view === appState.currentView;
                return `<button data-view="${item.view}" class="nav-btn flex flex-col items-center p-2 rounded-lg text-secondary ${isActive ? 'active' : ''}">
                            <i data-feather="${item.icon}"></i><span class="text-xs mt-1 uppercase">${item.label}</span>
                        </button>`;
            }).join('');

             document.querySelectorAll('.nav-btn').forEach(btn => {
                  const view = btn.dataset.view;
                   if(view === 'aihub') {
                      btn.addEventListener('click', () => { showModal(renderAIHubModal); });
                  } else {
                      btn.addEventListener('click', () => navigateTo(view));
                  }
              });
            safeFeatherReplace();
        };

        const initializeDashboardData = () => {
            const dashboardData = JSON.parse(safeLocalStorage.getItem('playerDashboardData') || '{}');
            let updated = false;
            
            DATA.players.forEach(player => {
                if (!dashboardData[player.name]) {
                    dashboardData[player.name] = {
                        scores: {
                            technical: (Math.random() * 2 + 2.5),
                            physical: (Math.random() * 2 + 2.5),
                            psychological: (Math.random() * 2 + 2.5),
                            social: (Math.random() * 2 + 2.5),
                            positional: (Math.random() * 2 + 2.5),
                        },
                        history: [],
                    };
                    updated = true;
                }
            });
            if (updated) {
                safeLocalStorage.setItem('playerDashboardData', JSON.stringify(dashboardData));
            }
            appState.playerDashboardData = dashboardData;
        };


        // --- EXPOSE GLOBALLY & INITIALIZE ---
        window.navigateTo = navigateTo;
        window.showModal = showModal;
        window.closeModal = closeModal;
        window.switchContext = switchContext;
        window.renderStoryDetailModal = renderStoryDetailModal;
        window.startFullImprovementFlow = startFullImprovementFlow;
        window.startMatchEvaluationFlow = startMatchEvaluationFlow;
        window.handlePostDrillEval = handlePostDrillEval;
        window.updateSliderValue = updateSliderValue;
        window.renderPlayerDetailModal = renderPlayerDetailModal;
        window.switchPlayerDetailTab = switchPlayerDetailTab;
        
        document.addEventListener('DOMContentLoaded', () => {
            initializeDashboardData();
            renderApp();
            document.getElementById('profile-switcher').addEventListener('click', () => { 
                showModal(renderProfileSwitcherModal);
            });
        });
        
    </script>
</body>
</html>
