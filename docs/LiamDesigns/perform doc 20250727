SHOT Clubhouse: 'Perform' Module Plan
This document outlines the epics, features, and user stories required to build the complete "Perform" experience within the SHOT Clubhouse application. It synthesises requirements from stakeholder discussions and the MVP specification document, focusing on the end-to-end journeys for the Coach, Player, and Parent personas.

To a Club Administrator
"The Perform module is the digital backbone for our club's player development. It gives our coaches a professional, unified system to manage their squads, schedule fixtures, and provide consistent, high-quality feedback to every player based on our chosen 'Four Corners' framework.
For the club, this means we can see how players are progressing across all our teams, ensure a high standard of coaching is maintained, and have a clear, data-backed view of our talent pipeline. It modernises our operations, moving us away from spreadsheets and informal chats to a structured, secure platform that benefits everyone."

To a Coach
"Perform is your complete digital assistant for managing your team and developing your players.
It replaces all your admin headaches. You can create your squad, easily invite players with a link, and see everyone's availability for upcoming matches and training sessions. When you schedule an event, the app automatically handles the evaluation process for you.
Before a game, your players assess their readiness. After, they reflect on their performance. All of this lands on your dashboard, ready for you to add your own ratings and comments based on the Four Corners model. You get simple charts to track each player's progress month by month, see who's in good form, and add private notes to remember key details. It saves you time on admin so you can focus on coaching."

To a Player
"Perform is your personal tool to track and improve your game. You own your performance data and you decide who sees it.
Before a match or training, you'll do a quick self-evaluation on how you're feeling. Afterwards, you'll reflect on how you actually performed. Your coach then sees your ratings, adds their own feedback and scores, and sends it back to you.
You'll get a clear history of all your performances, see your progress on a chart throughout the season, and get a monthly summary of what you've achieved. It's a simple way to understand your strengths, see where you're improving, and get direct feedback to become a better player."

To a Parent
"The Perform module gives you a window into your child's sporting life, so you can support them on their journey.
You'll see what your child sees: their upcoming fixtures, any feedback their coach has shared, and their progress over the season. If you have more than one child using the app, you can easily switch between their profiles.
Most importantly, you are in control. When a coach invites your child to a team, you receive a request to approve it, ensuring you know exactly who has access to your child's information. It’s designed to keep you informed and your child safe."

To an Investor
"The Perform module is the core engagement engine of our platform, creating a high-value, recurring feedback loop that is essential to youth sport. It solves the universal problem of unstructured, inconsistent, and often non-existent player development tracking at the amateur level.
By digitising the weekly cycle of training, matches, and feedback, we create a 'sticky' ecosystem. Coaches are retained through powerful, time-saving administrative tools. Players and parents are engaged through a clear, tangible record of their development journey.
Crucially, our player-centric data model, where the athlete owns their performance data, is a modern, privacy-first approach that builds trust and long-term value. This creates a unique and defensible dataset on athlete development that can be scaled across any sport, geography, and level of play, representing a significant market opportunity in the youth sports technology sector."




Epic 1: The Coach Experience - Team & Performance Management
Goal: To provide coaches with a comprehensive suite of tools to manage their teams, schedule events, conduct evaluations, and track player development effectively and efficiently.
Feature 1.1: Team & Player Administration
As a Coach, I need robust tools to manage my team squad and player information so that I can maintain an organized and up-to-date squad.
Story 1.1.1: Create a New Team: As a coach, I want to create a new team, specifying the club, sport, age group, and gender, so that I can establish a dedicated space for my squad.
Acceptance Criteria:
Must be able to select an associated club (if the coach is linked to multiple).
Must be able to select a sport, which then dictates the terminology used (e.g., Football -> 'Player', Boxing -> 'Boxer').
The form must capture Team Name, Age Group, and Gender.
Story 1.1.2: View Team Squad: As a coach, I want to view a clear list of all players in my selected team, so that I can see my full squad at a glance.
Acceptance Criteria:
The squad list should display each player's name, nickname, and age.
Players should be sortable by position (e.g., GK, DEF, MID, FWD for football).
Story 1.1.3: Invite Players to a Team: As a coach, I want to generate a unique invite link or code for my team that I can share via WhatsApp, email, or SMS, so that I can easily add new players to my squad.
Acceptance Criteria:
The system must generate a unique, shareable link/code per team.
A coach can view and manage pending invitations.
Story 1.1.4: Manage Squad: As a coach, I want the ability to remove a player from my team, so that I can keep my squad list accurate.
Acceptance Criteria:
Removing a player requires a confirmation step.
Removing a player archives their data associated with that team but does not delete their SHOT profile.
Story 1.1.5: Access Player Details: As a coach, I want to click on a player in my squad to view their detailed profile, including their name, DOB, and emergency contact details (parent/guardian), so I have critical information readily available.
Acceptance Criteria:
The emergency contact information must be read-only and is managed by the parent/player.
For U18 athletes, phone number and email address must not be visible to the coach.
Story 1.1.6: Add Notes to Player Profiles: As a coach, I want to add two types of notes to a player's profile: public feedback visible to the player/parent, and private notes for my own records, so I can track development and feedback effectively.
Acceptance Criteria:
The note-taking interface must have a clear toggle or separation for "Public Feedback" and "Private Coach Notes".
Public notes trigger a notification to the player/parent.
Private notes are only ever visible to the coach who wrote them.
Feature 1.2: Event & Fixture Management
As a Coach, I need to easily create, schedule, and manage all team events like training, fixtures, and fights, so I can keep my team organized and informed.
Story 1.2.1: Create a New Event: As a coach, I want to create a new event (Training, Match, Tournament, etc.), specifying the title, date, time, duration, and location, so I can schedule team activities.
Acceptance Criteria:
Event creation automatically calculates and sets the evaluation windows:
Pre-Evaluation opens: 48 hours before the event starts.
Post-Evaluation opens: Immediately after the event ends, and closes 48 hours later.
Coach Review window closes: 96 hours after the event ends.
Story 1.2.2: Send Event Announcements: As a coach, I want to send announcements to all team members for an upcoming event, so that I can remind them to complete their evaluations and confirm attendance.
Acceptance Criteria:
Announcements are sent as in-app notifications.
Story 1.2.3: Track Player Availability & Attendance: As a coach, I want to see which players have confirmed their availability for an upcoming session and mark their attendance (Present, Absent, Injured) during the event, so I can manage my sessions effectively.
Acceptance Criteria:
The UI must clearly distinguish between RSVP status and actual attendance.
Attendance marking should be a simple, quick interface for the coach.
Feature 1.3: Evaluation & Feedback Workflow
As a Coach, I need a streamlined process to review player evaluations, provide feedback, and assess their performance based on the Four Corners model.
Story 1.3.1: Review Pending Evaluations: As a coach, my dashboard should prioritize a list of player evaluations that are awaiting my review, so I know what my immediate tasks are.
Acceptance Criteria:
The dashboard shows a count of pending reviews.
Each pending review has a timer indicating the time left within the 96-hour review window.
Story 1.3.2: View Player's Pre/Post Evaluation: As a coach, when reviewing an evaluation, I want to see the player's pre-event and post-event self-evaluation scores and comments side-by-side, so I can understand their perspective.
Acceptance Criteria:
The view should show the player's 1-10 scores for each of the four corners.
The questions presented to the player must be context-aware (e.g., "How confident were you..." vs "How did you perform...").
Story 1.3.3: Complete Coach Evaluation: As a coach, I want to add my own 1-10 scores and written comments for the player's performance against the Four Corners model, so I can provide my official assessment.
Acceptance Criteria:
The questions I answer are coach-centric (e.g., "Rate [Player Name]'s execution of...").
Submitting my evaluation marks it as "Complete" and notifies the player/parent.
Feature 1.4: Performance Analytics Dashboard
As a Coach, I need a powerful dashboard to visualize individual and team performance trends, so I can make informed decisions about training and player development.
Story 1.4.1: Coach Action-Oriented Dashboard: As a coach, when I land on the Perform tab, I want to see a dashboard with my priority actions (evaluations to review, next training, next match) clearly displayed as timers.
Acceptance Criteria:
Displays three main action cards: "Pending Reviews", "Next Training", "Next Match".
Each card has a countdown timer to the relevant deadline/start time.
Story 1.4.2: Team Evaluation Status Board: As a coach, I want to see a status board for my team's most recent event evaluations, showing who is "Awaiting Pre-Evaluation," "Awaiting Post-Evaluation," "Awaiting Coach Review," or "Overdue."
Acceptance Criteria:
The board should be clear and visually easy to scan.
"Overdue" status is applied if a player misses their 48-hour post-evaluation window.
Story 1.4.3: Player Progression Chart: As a coach viewing a specific player, I want to see a chart showing their evaluation score progression over the year-to-date, broken down by month for each of the Four Corners, so I can track their long-term development.
Acceptance Criteria:
The chart must clearly label the four corner categories.
The x-axis represents months, and the y-axis represents the 1-10 score.
Story 1.4.4: Player Form Guide: As a coach, I want to see a "Form Guide" on my team dashboard that uses simple icons (↗️, ↘️, ➡️) to show which players are trending up, down, or remaining stable based on recent evaluations.
Acceptance Criteria:
The form is calculated based on an average of the last 3-5 evaluations compared to the previous period.
Story 1.4.5: View Monthly Team Summary: As a coach, I want to see a generated end-of-month summary for my team, highlighting the most improved players, overall evaluation completion rate, and average scores across the Four Corners, so I can plan for the next month.
Epic 2: The Player Experience - Skill Development & Feedback Loop
Goal: To empower players to take control of their development by providing a clear, engaging, and actionable interface for self-evaluation and tracking their progress.
Feature 2.1: Personalized Performance Dashboard
As a Player, I need a dashboard that gives me a quick and clear overview of my current status, upcoming events, and performance stats, so I can stay on top of my sporting journey.
Story 2.1.1: Player Action Dashboard: As a player, when I land on the Perform tab, I want to see my most important actions, such as "Must-Do Evaluations," upcoming fixtures, and recent coach comments, so I know what to focus on.
Acceptance Criteria:
Dashboard shows a clear call-to-action for any open evaluations.
Displays the next upcoming fixture or training session.
Shows a snippet of the most recent coach comment received.
Story 2.1.2: View Performance Averages: As a player, I want to see my average monthly scores for each of the Four Corners (Physical, Technical, Tactical, Mental), so I can understand my strengths and weaknesses.
Acceptance Criteria:
Displays four clear stats, one for each corner, averaged for the current month.
Feature 2.2: Evaluation Lifecycle
As a Player, I need to complete pre- and post-event evaluations so that I can reflect on my performance and contribute to my development record.
Story 2.2.1: Receive Evaluation Prompt: As a player, I want to receive a notification when a pre-evaluation window opens (48 hours before an event), so I don't miss the deadline.
Acceptance Criteria:
Notification is sent via in-app alert.
A "must-do" card appears on my Perform dashboard.
Story 2.2.2: Complete Pre-Event Evaluation: As a player, I want to complete a pre-event evaluation by rating myself from 1-10 on focus-specific questions related to the Four Corners, so I can set my intentions before the event.
Acceptance Criteria:
The questions are phrased to gauge confidence and readiness (e.g., "How confident are you...").
The interface is simple and mobile-friendly.
Story 2.2.3: Complete Post-Event Evaluation: As a player, I want to complete a post-event evaluation within 48 hours of the event finishing, rating my performance from 1-10, so I can reflect on how I played.
Acceptance Criteria:
The evaluation must be completed within the 48-hour window.
Questions are phrased to reflect on the performance (e.g., "How do you rate your...").
Submitting the form sends it to the coach for review.
Feature 2.3: Performance History & Progression
As a Player, I want to be able to view my entire evaluation history and see how I'm progressing over time, so I can stay motivated and understand my development path.
Story 2.3.1: View Evaluation History: As a player, I want to access a timeline or list of all my past evaluations, so I can review specific events.
Acceptance Criteria:
The history shows the event, date, and my final scores (including the coach's input).
Story 2.3.2: View Personal Progression Chart: As a player, I want to see the same monthly progression chart that my coach sees, showing my Four Corners scores over time, so I can visualize my own improvement.
Acceptance Criteria:
The chart is identical to the one in the coach's view for that player.
Story 2.3.3: Receive End-of-Month Summary: As a player, on the first of each month, I want to receive a summary of my previous month's performance, including my average scores, number of completed evaluations, and a "most improved" corner, so I can reflect on my progress.
Feature 2.4: Coach Feedback Center
As a Player, I want a dedicated place to see all the feedback my coach has given me, so I can easily act on their advice.
Story 2.4.1: Receive and View Coach Comments: As a player, I want to receive a notification when a coach reviews my evaluation and be able to read their comments and see their scores, so I can get timely feedback.
Acceptance Criteria:
Comments are clearly attributed to the coach.
The view shows the coach's scores alongside my self-evaluation scores for comparison.
Epic 3: The Parent Experience - Monitoring & Support
Goal: To provide parents/guardians with a clear and insightful view into their child's sporting life, allowing them to monitor progress, manage administrative tasks, and provide support.
Feature 3.1: Child's Performance Dashboard
As a Parent, I need a simple and easy-to-understand dashboard that mirrors what my child sees, so I can stay informed about their activities and progress.
Story 3.1.1: View Child's Dashboard: As a parent, I want to see the same Perform dashboard as my selected child, including upcoming events, pending evaluations, and recent coach feedback.
Acceptance Criteria:
The view is identical to the player's view but may contain additional parent-specific actions.
UI language is tailored for a guardian (e.g., "Your athlete's current focus").
Story 3.1.2: Switch Between Children: As a parent of multiple children on the app, I want a simple dropdown or selector on the Perform page to switch the context between my children, so I can view each of their dashboards individually.
Acceptance Criteria:
The selector is prominently displayed if more than one child is linked to the parent account.
Switching children reloads the dashboard with the selected child's data.
Story 3.1.3: View Child's End-of-Month Summary: As a parent, I want to be able to view the same monthly summary my child receives, so I can stay informed and offer encouragement.
Feature 3.2: Guardian Approval & Consent
As a Parent of an under-18 athlete, I need to approve their access to clubs and be the point of contact, ensuring their safety and privacy.
Story 3.2.1: Approve Club Access: As a parent, when my child tries to join a team, I want to receive a request for approval via an expiring link (72 hours), so I can control who has access to my child's data.
Acceptance Criteria:
The link leads to a clear page explaining the club and team the child is requesting to join.
I must give explicit consent before the connection is made.
The coach is notified upon approval.
Story 3.2.2: Act as Emergency Contact: As a parent, my contact details should be automatically listed as the emergency contact for my child, visible to their coach.
Acceptance Criteria:
The parent's contact number is pulled from their own profile.
The child cannot edit this information.
Epic 4: Technical & Architectural Foundation
Goal: To establish the core technical principles and data structures that underpin the entire Perform module, ensuring data integrity, privacy, and scalability.
Feature 4.1: Data Ownership & Permissions Model
As a System Architect, I need to define a clear data ownership model where the player is the source of truth, ensuring their data is secure and only shared with their consent.
Story 4.1.1: Establish Player Data Ownership: As a platform developer, I need to ensure that all performance and evaluation data is fundamentally owned by the player's account. Coaches and parents are granted specific, revocable permissions to view and add to this data, so that the player (or their guardian) remains in control of their personal information.
Acceptance Criteria:
Joining a team creates a permission link between the player and the team's coaches.
For U18s, this link is inactive until a parent/guardian approves it (ref: Story 3.2.1).
Leaving a team revokes the coach's permission to view ongoing player data; historical data related to that team remains accessible to the coach for archival purposes, but no new data is shared.
The underlying database schema (Row Level Security) must enforce this player-centric ownership.
