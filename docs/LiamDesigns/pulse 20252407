
<!DOCTYPE html>
<html lang="en" class="bg-black">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SHOT Clubhouse MVP</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700;800&family=Montserrat:wght@400;500;600&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/feather-icons"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --shot-teal: #1ABC9C; --shot-purple: #6B00DB; --shot-gold: #F7B613;
            --shot-red: #E63946; --shot-bg: #121212; --shot-surface: #1E1E1E;
            --shot-text-primary: #FFFFFF; --shot-text-secondary: #B3B3B3;
            --font-poppins: 'Poppins', sans-serif; --font-montserrat: 'Montserrat', sans-serif;
            --corner-technical: #296DFF; --corner-physical: #2E8B57;
            --corner-psychological: #FF6F3C; --corner-social: #FF5D73; --corner-positional: #A95CFF;
        }
        body { font-family: var(--font-montserrat); background-color: var(--shot-bg); color: var(--shot-text-primary); }
        .font-poppins { font-family: var(--font-poppins); }
        .bg-shot-surface { background-color: var(--shot-surface); }
        .text-secondary { color: var(--shot-text-secondary); }
        .text-shot-teal { color: var(--shot-teal); }
        .border-shot-teal { border-color: var(--shot-teal); }
        .text-shot-purple { color: var(--shot-purple); }
        .text-shot-gold { color: var(--shot-gold); }
        .text-shot-red { color: var(--shot-red); }
        .bg-shot-teal { background-color: var(--shot-teal); }
        .bg-shot-purple { background-color: var(--shot-purple); }
        .bg-shot-gold { background-color: var(--shot-gold); }
        .border-shot-purple { border-color: var(--shot-purple); }
        .border-shot-gold { border-color: var(--shot-gold); }
        .hide-scrollbar::-webkit-scrollbar { display: none; }
        .hide-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }
        
        /* Animations */
        .fade-in { animation: fadeIn 0.5s ease-in-out forwards; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
        
        .sp-popup { animation: sp-popup-animation 1.5s ease-out forwards; }
        @keyframes sp-popup-animation {
            0% { transform: translateY(0) scale(1); opacity: 1; }
            50% { transform: translateY(-20px) scale(1.2); opacity: 0.8; }
            100% { transform: translateY(-40px) scale(1.2); opacity: 0; }
        }

        .like-bounce { animation: like-bounce-animation 0.4s ease-in-out; }
        @keyframes like-bounce-animation { 0% { transform: scale(1); } 50% { transform: scale(1.4); } 100% { transform: scale(1); } }

        .text-shadow-custom { text-shadow: 2px 2px 8px rgba(0,0,0,0.7); }
        .like-btn.liked { color: var(--shot-red); }
        .like-btn.liked i { fill: var(--shot-red); }
        .bookmark-btn.bookmarked { color: var(--shot-teal); }
        .bookmark-btn.bookmarked i { fill: var(--shot-teal); }
        .nav-btn.active i, .nav-btn.active span { color: var(--shot-gold); }
        
        details > summary { list-style: none; }
        details > summary::-webkit-details-marker { display: none; }
        details > summary .icon-rotate { transition: transform 0.2s; }
        details[open] > summary .icon-rotate { transform: rotate(180deg); }

        .skeleton-shimmer { position: relative; overflow: hidden; }
        .skeleton-shimmer::after {
            content: ''; position: absolute; top: 0; left: -100%; width: 100%; height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.08), transparent);
            animation: shimmer 1.5s infinite;
        }
        @keyframes shimmer { 100% { left: 100%; } }
    </style>
</head>
<body class="bg-shot-bg">

    <!-- App Wrapper -->
    <div class="w-full max-w-2xl mx-auto flex flex-col min-h-screen bg-black">

        <!-- App Header -->
        <header id="app-header" class="sticky top-0 bg-shot-bg/80 backdrop-blur-sm px-4 py-3 flex justify-between items-center border-b border-gray-800 z-40">
            <img src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSZNxN2v-tgf_-t2Oic-m3dC2jtm3_zllEu-Q&s" alt="SHOT Logo" class="h-8">
            <div class="flex items-center space-x-4">
                 <div id="sp-tooltip-wrapper" class="relative">
                    <span id="sp-total" class="font-bold text-shot-gold text-lg">0 SP</span>
                    <div id="sp-popup-container" class="absolute top-0 right-0"></div>
                 </div>
                 <div id="profile-switcher" class="cursor-pointer">
                    <img id="header-avatar" src="" alt="User Avatar" class="h-9 w-9 rounded-full border-2 border-shot-purple object-cover">
                </div>
            </div>
        </header>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-1 overflow-y-auto scroll-smooth">
            <!-- Dynamic content will be rendered here -->
        </main>
        
        <!-- Bottom Navigation Bar -->
        <nav id="bottom-nav" class="sticky bottom-0 bg-shot-surface/80 backdrop-blur-sm border-t border-gray-800 grid grid-cols-5 gap-2 px-2 py-2 z-40">
            <!-- Nav buttons will be dynamically rendered here -->
        </nav>
    </div>

    <!-- Modals and Overlays Container -->
    <div id="modal-container" class="fixed inset-0 z-50 pointer-events-none"></div>

    <!-- MOCK DATA SCRIPT (Included for components that need it) -->
    <script type="module" id="mock-data-script">
        const mockData = {
            superUser: {
                id: 'user_super_001', name: 'Alex', email: '<EMAIL>', dob: '2004-05-10', sp: 12540,
                roles: ['Player', 'Coach', 'Parent', 'Member'], sports: ['Football', 'Boxing'],
                avatar: 'https://i.imgur.com/BEfccy3.png',
                achievements: [
                    { id: 'ach_01', title: 'Power User', icon: 'zap', date: '2025-05-20'},
                    { id: 'ach_02', title: 'First 10k SP', icon: 'award', date: '2025-06-10'},
                    { id: 'ach_03', title: 'Team Captain', icon: 'shield', date: '2025-04-15'},
                ],
            },
        };
        window.mockData = mockData;
    </script>

    <!-- MAIN APP SCRIPT -->
    <script type="module">
        // --- App State and Data ---
        let allPulseFeedData = [];
        const appState = {
            currentView: 'pulse',
            currentUser: window.mockData.superUser,
            likedPosts: [],
            sharedPosts: [],
            bookmarkedPosts: [],
            followedTags: [],
            activePulseFilter: 'All',
            pulseSearchQuery: '',
            hasSeenSPTooltip: false
        };
        let searchDebounceTimeout;

        // --- DOM Elements ---
        const mainContent = document.getElementById('main-content');
        const modalContainer = document.getElementById('modal-container');

        // --- Utility Functions ---
        const safeFeatherReplace = () => {
            try {
                if (window.feather) feather.replace({ 'stroke-width': 1.5 });
            } catch (e) { console.warn('Feather icons could not be replaced:', e); }
        };

        const showSPPopup = () => {
            const container = document.getElementById('sp-popup-container');
            if (!container) return;
            const popup = document.createElement('div');
            popup.className = 'absolute right-0 text-shot-gold font-bold sp-popup';
            popup.textContent = '+10 SP';
            container.appendChild(popup);
            setTimeout(() => container.removeChild(popup), 1500);
        };

        const updateSPDisplay = (awardedPoints = false) => {
            const spElement = document.getElementById('sp-total');
            if (spElement) spElement.textContent = `${appState.currentUser.sp.toLocaleString()} SP`;
            if (awardedPoints) showSPPopup();
        };

        const awardPoints = (postId, type) => {
            const stateKey = type === 'like' ? 'likedPosts' : 'sharedPosts';
            const sessionKey = type === 'like' ? 'shotLikedPosts' : 'shotSharedPosts';
            if (!appState[stateKey].includes(postId)) {
                appState.currentUser.sp += 10;
                appState[stateKey].push(postId);
                sessionStorage.setItem(sessionKey, JSON.stringify(appState[stateKey]));
                sessionStorage.setItem('shotUserSP', appState.currentUser.sp.toString());
                updateSPDisplay(true);
                return true;
            }
            return false;
        };
        
        /**
         * [NEW ROBUST PARSER] This function is designed to handle multiple data formats for entities.
         * It can parse:
         * 1. A clean, valid JSON object.
         * 2. A string that contains a single valid JSON object.
         * 3. A string that contains multiple concatenated JSON objects (e.g., "{...}{...}").
         */
        const parseEntities = (entitiesData) => {
            const allTags = new Set();
            const entityTypes = ['players', 'teams', 'leagues_competitions', 'brands', 'music_artists', 'cultural_figures'];

            const processObject = (obj) => {
                entityTypes.forEach(type => {
                    if (Array.isArray(obj[type])) {
                        obj[type].forEach(tag => allTags.add(tag));
                    }
                });
            };

            // Case 1: The data is already a valid object.
            if (typeof entitiesData === 'object' && entitiesData !== null) {
                processObject(entitiesData);
                return Array.from(allTags);
            }

            // Case 2 & 3: The data is a string.
            if (typeof entitiesData === 'string' && entitiesData.trim().startsWith('{')) {
                try {
                    // Try parsing as a single valid JSON string first.
                    const singleParseResult = JSON.parse(entitiesData);
                    processObject(singleParseResult);
                } catch (e) {
                    // If that fails, it's likely the concatenated format.
                    const jsonObjects = entitiesData.match(/{[^{}]*}/g);
                    if (jsonObjects) {
                        jsonObjects.forEach(jsonStr => {
                            try {
                                const chunk = JSON.parse(jsonStr);
                                processObject(chunk);
                            } catch (e) {
                                // Ignore malformed chunks within the string.
                            }
                        });
                    }
                }
            }
            
            return Array.from(allTags);
        };
        
        const getEntityType = (tag, postEntities) => {
            if (postEntities.players && postEntities.players.includes(tag)) return 'user';
            if (postEntities.teams && postEntities.teams.includes(tag)) return 'shield';
            if (postEntities.brands && postEntities.brands.includes(tag)) return 'tag';
            if (postEntities.leagues_competitions && postEntities.leagues_competitions.includes(tag)) return 'award';
            return 'hash';
        };

        // --- Event Handlers ---
        window.handleLikeClick = (postId) => {
            if (awardPoints(postId, 'like')) {
                const button = document.querySelector(`.like-btn[data-post-id="${postId}"]`);
                if (button) {
                    button.classList.add('liked', 'like-bounce');
                    button.innerHTML = '<i data-feather="heart" class="w-5 h-5"></i>';
                    safeFeatherReplace();
                    button.disabled = true;
                }
            }
        };

        window.handleBookmarkClick = (postId) => {
            const button = document.querySelector(`.bookmark-btn[data-post-id="${postId}"]`);
            if (!button) return;
            const index = appState.bookmarkedPosts.indexOf(postId);
            if (index > -1) {
                appState.bookmarkedPosts.splice(index, 1);
                button.classList.remove('bookmarked');
            } else {
                appState.bookmarkedPosts.push(postId);
                button.classList.add('bookmarked');
            }
            sessionStorage.setItem('shotBookmarkedPosts', JSON.stringify(appState.bookmarkedPosts));
            button.classList.add('like-bounce');
            displayPulseFeed(allPulseFeedData);
        };

        window.handleShareClick = async (postId) => {
            const post = allPulseFeedData.find(p => p.id === postId);
            if (!post) return;
            const shareData = { title: post.title, text: post.social_post || post.narrative, url: window.location.href };
            if (navigator.share) {
                try {
                    await navigator.share(shareData);
                    awardPoints(postId, 'share');
                } catch (err) {
                    if (err.name !== 'AbortError') renderPulseDetailModal(postId);
                }
            } else {
                renderPulseDetailModal(postId);
            }
        };

        window.copyToClipboard = (postId, text, button) => {
            awardPoints(postId, 'share');
            const textarea = document.createElement('textarea');
            textarea.value = text;
            document.body.appendChild(textarea);
            textarea.select();
            try {
                document.execCommand('copy');
                const originalText = button.innerHTML;
                button.innerHTML = '<i data-feather="check" class="w-4 h-4 mr-2"></i> Copied!';
                safeFeatherReplace();
                setTimeout(() => { button.innerHTML = originalText; safeFeatherReplace(); }, 2000);
            } catch (err) { console.error('Fallback: Oops, unable to copy', err); }
            document.body.removeChild(textarea);
        };

        window.handleFilterClick = (filter) => {
            appState.activePulseFilter = filter;
            appState.pulseSearchQuery = '';
            const searchInput = document.getElementById('pulse-search-input');
            if(searchInput) searchInput.value = '';
            displayPulseFeed(allPulseFeedData);
        };

        window.handleTagClick = (tag) => {
            appState.pulseSearchQuery = tag;
            appState.activePulseFilter = 'All';
            const searchInput = document.getElementById('pulse-search-input');
            if(searchInput) searchInput.value = tag;
            displayPulseFeed(allPulseFeedData);
        };

        window.handleSearchInput = (event) => {
            clearTimeout(searchDebounceTimeout);
            searchDebounceTimeout = setTimeout(() => {
                appState.pulseSearchQuery = event.target.value.toLowerCase();
                displayPulseFeed(allPulseFeedData);
            }, 300);
        };

        window.handleFollowClick = (tag) => {
            const index = appState.followedTags.indexOf(tag);
            if (index > -1) appState.followedTags.splice(index, 1);
            else appState.followedTags.push(tag);
            sessionStorage.setItem('shotFollowedTags', JSON.stringify(appState.followedTags));
            displayPulseFeed(allPulseFeedData);
        };
        
        window.handleUnfollowClick = (tag) => {
            const index = appState.followedTags.indexOf(tag);
            if (index > -1) appState.followedTags.splice(index, 1);
            sessionStorage.setItem('shotFollowedTags', JSON.stringify(appState.followedTags));
            if (appState.followedTags.length === 0) appState.activePulseFilter = 'All';
            displayPulseFeed(allPulseFeedData);
        };

        // --- Modal Functions ---
        const closeModal = () => {
            if (modalContainer) {
                modalContainer.innerHTML = '';
                modalContainer.classList.add('pointer-events-none');
            }
        };

        const showModal = (modalHTML) => {
            if (!modalContainer) return;
            const overlay = document.createElement('div');
            overlay.className = 'fixed inset-0 bg-black bg-opacity-80 flex justify-center items-center z-50 p-4 fade-in';
            overlay.innerHTML = `<div class="bg-shot-surface rounded-2xl p-6 w-full max-w-lg space-y-4 relative shadow-2xl max-h-[90vh] overflow-y-auto hide-scrollbar" onclick="event.stopPropagation()">${modalHTML}</div>`;
            overlay.addEventListener('click', (e) => { if (e.target === overlay) closeModal(); });
            modalContainer.innerHTML = '';
            modalContainer.appendChild(overlay);
            modalContainer.classList.remove('pointer-events-none');
            safeFeatherReplace();
            const closeBtn = overlay.querySelector('.modal-close-btn');
            if (closeBtn) closeBtn.addEventListener('click', closeModal);
        };

        window.renderPulseDetailModal = (postId) => {
            const post = allPulseFeedData.find(p => p.id === postId);
            if (!post) return;
            const shareTextId = `share-text-${post.id}`;
            const hasBeenShared = appState.sharedPosts.includes(postId);
            const modalContent = `
                <button class="modal-close-btn absolute top-3 right-3 text-gray-400 hover:text-white"><i data-feather="x"></i></button>
                <h2 class="text-2xl font-poppins font-bold">${post.title}</h2>
                ${post.social_post ? `
                <div class="mt-4 pt-4 border-t border-gray-700">
                    <h3 class="font-semibold text-shot-teal mb-2">Share Content</h3>
                    <div class="bg-shot-bg p-3 rounded-lg"><p id="${shareTextId}" class="text-sm text-secondary whitespace-pre-wrap">${post.social_post}</p></div>
                    <button onclick="copyToClipboard('${post.id}', document.getElementById('${shareTextId}').innerText, this)" class="flex items-center justify-center w-full bg-shot-purple text-white font-bold py-2 rounded-lg mt-3">
                        <i data-feather="copy" class="w-4 h-4 mr-2"></i> ${hasBeenShared ? 'Copy Text' : 'Copy Text & Get 10 SP'}
                    </button>
                </div>` : '<p class="text-secondary">No shareable content available for this post.</p>'}
            `;
            showModal(modalContent);
        };

        // --- Core Render Functions ---
        const renderClubhouse = () => { mainContent.innerHTML = `<div class="p-4"><h1 class="text-3xl font-poppins font-bold">Clubhouse</h1><p class="text-secondary">Welcome back!</p></div>`; };
        const renderLocker = () => { mainContent.innerHTML = `<div class="p-4"><h1 class="text-3xl font-poppins font-bold">Locker</h1><p class="text-secondary">Coming soon.</p></div>`; };
        const renderPerform = () => { mainContent.innerHTML = `<div class="p-4"><h1 class="text-3xl font-poppins font-bold">Perform</h1><p class="text-secondary">Coming soon.</p></div>`; };
        const renderImpact = () => { mainContent.innerHTML = `<div class="p-4"><h1 class="text-3xl font-poppins font-bold">Impact</h1><p class="text-secondary">Coming soon.</p></div>`; };

        const displayPulseFeed = (feed) => {
            const filtersContainer = document.getElementById('pulse-filters');
            let categories = ['All'];
            if(appState.followedTags.length > 0) categories.push('Following');
            if(appState.bookmarkedPosts.length > 0) categories.push('Saved');
            categories.push(...new Set(feed.map(post => post.story_type?.category).filter(Boolean)));
            
            if (filtersContainer) {
                filtersContainer.innerHTML = [...new Set(categories)].map(category => {
                    const isActive = appState.activePulseFilter === category;
                    return `<button onclick="handleFilterClick('${category}')" class="px-4 py-2 text-sm font-semibold rounded-full ${isActive ? 'bg-shot-teal text-white' : 'bg-shot-surface text-secondary'} hover:bg-shot-teal hover:text-white transition-colors flex-shrink-0">${category}</button>`;
                }).join('');
            }

            let processedFeed = feed;
            if (appState.activePulseFilter === 'Following') {
                 processedFeed = feed.filter(post => {
                    const postTags = parseEntities(post.entities);
                    return appState.followedTags.some(followedTag => {
                        const lowerFollowedTag = followedTag.toLowerCase();
                        return postTags.some(postTag => postTag.toLowerCase().includes(lowerFollowedTag));
                    });
                 });
            } else if (appState.activePulseFilter === 'Saved') {
                processedFeed = feed.filter(post => appState.bookmarkedPosts.includes(post.id));
            } else if (appState.activePulseFilter !== 'All') {
                processedFeed = processedFeed.filter(post => post.story_type?.category === appState.activePulseFilter);
            }
            
            if (appState.pulseSearchQuery) {
                const query = appState.pulseSearchQuery.toLowerCase();
                processedFeed = processedFeed.filter(post => {
                    const postTags = parseEntities(post.entities);
                    return post.title.toLowerCase().includes(query) ||
                           post.narrative.toLowerCase().includes(query) ||
                           post.desk.toLowerCase().includes(query) ||
                           postTags.some(tag => tag.toLowerCase().includes(query));
                });
            }

            const contextualHeader = document.getElementById('contextual-header');
            if(contextualHeader) {
                const isFiltered = appState.activePulseFilter !== 'All' || appState.pulseSearchQuery;
                if (appState.pulseSearchQuery && appState.activePulseFilter === 'All') {
                    const isFollowing = appState.followedTags.some(ft => ft.toLowerCase() === appState.pulseSearchQuery.toLowerCase());
                    contextualHeader.innerHTML = `
                        <div class="flex items-center justify-between bg-shot-surface p-3 rounded-lg">
                            <p class="text-sm text-secondary">Showing results for <span class="font-bold text-shot-teal">#${appState.pulseSearchQuery}</span></p>
                            <button onclick="handleFollowClick('${appState.pulseSearchQuery}')" class="flex items-center px-3 py-1 text-xs ${isFollowing ? 'bg-shot-red text-white' : 'bg-shot-teal text-white'} rounded-full">
                                <i data-feather="${isFollowing ? 'user-minus' : 'user-plus'}" class="w-4 h-4 mr-1"></i>
                                ${isFollowing ? 'Unfollow' : 'Follow'}
                            </button>
                        </div>
                    `;
                } else if (isFiltered) {
                     contextualHeader.innerHTML = `
                        <div class="flex items-center justify-end">
                            <button onclick="handleFilterClick('All')" class="flex items-center px-3 py-1 text-xs bg-shot-red text-white rounded-full">
                                <i data-feather="x" class="w-4 h-4 mr-1"></i>
                                Clear Filter
                            </button>
                        </div>
                     `;
                } else {
                    contextualHeader.innerHTML = '';
                }
            }
            
            const followingManagementContainer = document.getElementById('following-management-container');
            if (followingManagementContainer) {
                if (appState.activePulseFilter === 'Following' && appState.followedTags.length > 0) {
                    followingManagementContainer.innerHTML = `
                        <details class="bg-shot-surface rounded-lg">
                            <summary class="p-3 cursor-pointer flex justify-between items-center font-semibold">
                                Manage Following
                                <i data-feather="chevron-down" class="icon-rotate"></i>
                            </summary>
                            <div class="p-3 border-t border-gray-700">
                                <p class="text-xs text-secondary mb-2">Click a tag to remove it from your feed.</p>
                                <div class="flex flex-wrap gap-2">
                                    ${appState.followedTags.map(tag => `
                                        <button onclick="handleUnfollowClick('${tag}')" class="flex items-center bg-shot-bg text-shot-red px-3 py-1 text-xs rounded-full hover:bg-shot-red hover:text-white transition-colors">
                                            <span>${tag}</span>
                                            <i data-feather="x" class="w-3 h-3 ml-1.5"></i>
                                        </button>
                                    `).join('')}
                                </div>
                            </div>
                        </details>
                    `;
                } else {
                    followingManagementContainer.innerHTML = '';
                }
            }

            const feedContainer = document.getElementById('pulse-feed-container');
            if (feedContainer) {
                if (processedFeed.length === 0) {
                    let emptyMessage = '<div class="text-center text-secondary py-10"><p>No posts match your criteria.</p></div>';
                    if (appState.activePulseFilter === 'Following') {
                        emptyMessage = `<div class="text-center text-secondary py-10"><p class="font-bold mb-2">Your 'Following' feed is empty.</p><p class="text-sm">Search for a player, team, or brand and tap the 'Follow' button to add them here.</p></div>`;
                    } else if (appState.activePulseFilter === 'Saved') {
                         emptyMessage = `<div class="text-center text-secondary py-10"><p class="font-bold mb-2">You have no saved articles.</p><p class="text-sm">Tap the bookmark icon on any post to save it for later.</p></div>`;
                    }
                    feedContainer.innerHTML = emptyMessage;
                    return;
                }
                feedContainer.innerHTML = `<div class="space-y-6">${processedFeed.map((post, index) => {
                    const isLiked = appState.likedPosts.includes(post.id);
                    const isBookmarked = appState.bookmarkedPosts.includes(post.id);
                    const tags = parseEntities(post.entities);
                    const pillarStyles = {
                        'TAKE YOUR SHOT': { border: 'border-shot-teal', bg: 'bg-shot-teal' },
                        'OWN IT': { border: 'border-shot-purple', bg: 'bg-shot-purple' },
                        'MAKE IMPACT': { border: 'border-shot-gold', bg: 'bg-shot-gold' }
                    };
                    const postStyle = pillarStyles[post.pillar] || { border: 'border-gray-700', bg: 'bg-gray-700' };

                    return `
                    <div class="bg-shot-surface rounded-xl overflow-hidden shadow-lg fade-in border-t-4 ${postStyle.border}" style="animation-delay: ${index * 100}ms;">
                        <div>
                           ${post.image ? `<div class="relative"><img src="${post.image}" onerror="this.parentElement.style.display='none'" alt="${post.title}" class="w-full h-56 object-cover"><div class="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent"></div><h3 class="font-poppins font-extrabold text-2xl sm:text-3xl text-white leading-tight text-shadow-custom absolute bottom-4 left-4 right-4">${post.title}</h3></div>`
                           : `<div class="p-4"><div class="aspect-video rounded-lg ${postStyle.bg} flex items-center justify-center p-4 bg-gradient-to-br from-white/10 to-transparent via-transparent"><h3 class="font-poppins font-extrabold text-2xl sm:text-3xl text-center text-white leading-tight text-shadow-custom">${post.title}</h3></div></div>`}
                           <div class="p-4 ${post.image ? 'pt-2' : 'pt-0'}">
                                <p class="text-secondary mb-4 text-base leading-relaxed">${post.narrative}</p>
                                ${tags.length > 0 ? `<div class="pb-2 flex flex-wrap gap-2">${tags.map(tag => {
                                    const icon = getEntityType(tag, post.entities);
                                    return `<button onclick="handleTagClick('${tag}')" class="flex items-center px-3 py-1 text-xs bg-shot-bg text-shot-teal rounded-full hover:bg-shot-teal hover:text-white transition-colors"><i data-feather="${icon}" class="w-3 h-3 mr-1.5"></i>${tag}</button>`
                                }).join('')}</div>` : ''}
                           </div>
                        </div>
                        <div class="flex justify-between items-center text-xs text-secondary p-4 pt-2 border-t border-gray-800/50">
                            <span class="font-semibold flex items-center"><span class="text-lg mr-1.5">${post.desk_emoji || '🏆'}</span>${post.desk}</span>
                            <div class="flex items-center space-x-4">
                                <button class="bookmark-btn ${isBookmarked ? 'bookmarked' : 'text-secondary'} hover:text-shot-teal transition-colors" data-post-id="${post.id}" onclick="handleBookmarkClick('${post.id}')"><i data-feather="bookmark" class="w-5 h-5"></i></button>
                                <button class="${isLiked ? 'like-btn liked' : 'like-btn text-secondary'} hover:text-shot-red transition-colors" data-post-id="${post.id}" onclick="handleLikeClick('${post.id}')" ${isLiked ? 'disabled' : ''}><i data-feather="heart" class="w-5 h-5"></i></button>
                                <button class="text-secondary hover:text-shot-teal transition-colors" onclick="handleShareClick('${post.id}')"><i data-feather="share-2" class="w-5 h-5"></i></button>
                            </div>
                        </div>
                    </div>`;
                }).join('')}</div>`;
            }
            safeFeatherReplace();
        };

        const renderPulse = async () => {
            mainContent.innerHTML = `
                <div class="p-4 space-y-4">
                    <div class="relative">
                        <input id="pulse-search-input" type="text" placeholder="Search Pulse..." class="w-full bg-shot-surface border border-gray-700 rounded-full py-2 pl-10 pr-4 text-white placeholder-secondary focus:outline-none focus:ring-2 focus:ring-shot-teal" oninput="handleSearchInput(event)">
                        <i data-feather="search" class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-secondary"></i>
                    </div>
                    <div id="contextual-header"></div>
                    <div id="pulse-filters" class="flex space-x-2 overflow-x-auto hide-scrollbar"></div>
                    <div id="following-management-container" class="mt-2"></div>
                </div>
                <div id="pulse-feed-container" class="px-4 pb-4"></div>
            `;
            safeFeatherReplace();

            const skeletonCard = `<div class="bg-shot-surface rounded-xl overflow-hidden shadow-lg"><div class="bg-gray-700 h-56 w-full skeleton-shimmer"></div><div class="p-4"><div class="bg-gray-700 h-4 w-3/4 rounded skeleton-shimmer mb-3"></div><div class="bg-gray-700 h-4 w-1/2 rounded skeleton-shimmer"></div></div></div>`;
            document.getElementById('pulse-feed-container').innerHTML = `<div class="space-y-6">${skeletonCard.repeat(3)}</div>`;

            const CACHE_KEY = 'pulseFeedData';
            const CACHE_DURATION_MS = 1 * 60 * 1000;
            const cachedData = sessionStorage.getItem(CACHE_KEY);
            if (cachedData) {
                const { timestamp, data } = JSON.parse(cachedData);
                if (Date.now() - timestamp < CACHE_DURATION_MS) {
                    allPulseFeedData = data;
                    displayPulseFeed(allPulseFeedData);
                    return;
                }
            }

            try {
                const apiUrl = 'https://script.google.com/macros/s/AKfycbxfd38NYGpHm76QLvQN6Z8Q-6XwAG-nBuE7-HSq-ZZ-PAiPB2tNcVjVT0bi8b5niQFydQ/exec?t=' + new Date().getTime();
                const response = await fetch(apiUrl);
                if (!response.ok) throw new Error(`API Error: ${response.statusText}`);
                const feed = await response.json();
                allPulseFeedData = feed;
                const cachePayload = { timestamp: Date.now(), data: feed };
                sessionStorage.setItem(CACHE_KEY, JSON.stringify(cachePayload));
                displayPulseFeed(allPulseFeedData);
            } catch (error) {
                console.error('Failed to fetch pulse data:', error);
                allPulseFeedData = [{ id: 'error_msg', title: 'Could Not Load Live Feed', story_type: {category: 'System'}, desk: 'System', desk_emoji: '⚠️', narrative: 'The live Pulse feed could not be loaded.', entities: {players:["Error"],teams:[],brands:[]}, social_post: 'Error loading content.' }];
                displayPulseFeed(allPulseFeedData);
            }
        };

        // --- Navigation ---
        const navigateTo = (view) => {
            appState.currentView = view;
            mainContent.innerHTML = '';
            const viewRenderers = {
                'clubhouse': renderClubhouse, 'locker': renderLocker,
                'perform': renderPerform, 'pulse': renderPulse, 'impact': renderImpact,
            };
            const renderFunction = viewRenderers[view] || renderClubhouse;
            renderFunction();
            updateBottomNav();
        };

        const updateBottomNav = () => {
            const nav = document.getElementById('bottom-nav');
            const navItems = [
                { view: 'clubhouse', icon: 'home', label: 'Clubhouse' },
                { view: 'locker', icon: 'shopping-bag', label: 'Locker' },
                { view: 'pulse', icon: 'activity', label: 'Pulse' },
                { view: 'perform', icon: 'trending-up', label: 'Perform' },
                { view: 'impact', icon: 'heart', label: 'Impact' }
            ];
            nav.innerHTML = navItems.map(item => {
                const isActive = item.view === appState.currentView;
                return `<button data-view="${item.view}" class="nav-btn flex flex-col items-center p-2 rounded-lg text-secondary ${isActive ? 'active' : ''}">
                            <i data-feather="${item.icon}"></i><span class="text-xs mt-1 uppercase">${item.label}</span>
                        </button>`;
            }).join('');
            nav.querySelectorAll('.nav-btn').forEach(btn => {
                btn.addEventListener('click', () => navigateTo(btn.dataset.view));
            });
            safeFeatherReplace();
        };
        
        const showSPTooltip = () => {
            if (appState.hasSeenSPTooltip) return;
            const wrapper = document.getElementById('sp-tooltip-wrapper');
            if(!wrapper) return;

            const tooltip = document.createElement('div');
            tooltip.className = 'absolute bottom-full right-0 mb-2 w-48 bg-shot-teal text-white text-xs text-center rounded-lg p-2 fade-in';
            tooltip.innerHTML = `Like & share posts to earn SP! <button id="close-tooltip" class="font-bold ml-2">OK</button>`;
            wrapper.appendChild(tooltip);

            document.getElementById('close-tooltip').addEventListener('click', (e) => {
                e.stopPropagation();
                tooltip.remove();
                appState.hasSeenSPTooltip = true;
                sessionStorage.setItem('shotHasSeenSPTooltip', 'true');
            });
        };

        // --- Initialization ---
        document.addEventListener('DOMContentLoaded', () => {
            const storedLikes = sessionStorage.getItem('shotLikedPosts');
            const storedShares = sessionStorage.getItem('shotSharedPosts');
            const storedFollows = sessionStorage.getItem('shotFollowedTags');
            const storedBookmarks = sessionStorage.getItem('shotBookmarkedPosts');
            const storedSP = sessionStorage.getItem('shotUserSP');
            const storedTooltip = sessionStorage.getItem('shotHasSeenSPTooltip');

            if (storedLikes) appState.likedPosts = JSON.parse(storedLikes);
            if (storedShares) appState.sharedPosts = JSON.parse(storedShares);
            if (storedFollows) appState.followedTags = JSON.parse(storedFollows);
            if (storedBookmarks) appState.bookmarkedPosts = JSON.parse(storedBookmarks);
            if (storedSP) appState.currentUser.sp = parseInt(storedSP, 10);
            if (storedTooltip) appState.hasSeenSPTooltip = JSON.parse(storedTooltip);
            
            document.getElementById('header-avatar').src = appState.currentUser.avatar;
            updateSPDisplay();
            navigateTo(appState.currentView);
            setTimeout(showSPTooltip, 2000);
        });
    </script>
</body>
</html>
