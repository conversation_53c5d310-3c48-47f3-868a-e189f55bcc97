# 🚀 Project Bootstrap & Onboarding Guide

Welcome to the project!  
This guide will walk you through setting up your local environment, running the app, and understanding the workflow.  

---

## 1. Prerequisites

Make sure you have the following installed:

- **Node.js** (LTS version, e.g. 18.x or 20.x)  
- **pnpm** (preferred) or npm/yarn  
- **Supabase CLI** → [Install Guide](https://supabase.com/docs/guides/cli)  
- **Docker** (for running Supabase locally, optional if using hosted Supabase)  
- **Git**  

---

## 2. Clone & Install

```bash
git clone <repo-url>
cd project-root
pnpm install
```

---

## 3. Environment Setup

1. Copy the example env file:

```bash
cp .env.example .env
```

2. Fill in the required values:

```env
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-anon-key
```

3. (Optional) If running Supabase locally:

```bash
supabase start
```

This will spin up a local Postgres + Supabase instance using Docker.

---

## 4. ⚠️ Supabase Connection Safety

Before running **any Supabase command** (especially migrations), always check which project you’re connected to.

### Check current project
```bash
supabase status
```
- Shows which Supabase project/instance you are connected to.  
- Verify that you are connected to **local** or **staging**, not **production**.  

### Switch project
```bash
supabase link --project-ref <project-ref>
```
- Links your CLI to a specific Supabase project.  
- Use this only when you intentionally need to connect to staging or production.  

### Best practices
- ✅ Default to **local Supabase** for development.  
- ✅ Use **staging** for testing migrations before production.  
- ✅ Only connect to **production** when absolutely necessary.  
- ❌ Never run `supabase migration up` against production without review.  
- ❌ Never use production keys in your local `.env`.  

---

## 5. Database & Migrations

### What are migrations?
- A **migration** is a SQL file that describes a change to the database schema (e.g., create table, add column, add index).  
- Migrations are **version-controlled** in `supabase/migrations/`.  
- They ensure every developer and environment (local, staging, production) has the **same schema**.  

---

### When to use migrations
- ✅ **When you change the schema** (add/remove/alter tables, columns, indexes, policies).  
- ✅ **When you need to share schema changes** with the team.  
- ✅ **When deploying to staging/production** (migrations are the only safe way to sync schema).  

---

### When *not* to use migrations
- ❌ Don’t run `supabase migration up` blindly just because it’s in the docs.  
- ❌ Don’t create migrations for **data changes** (use SQL scripts or seed files instead).  
- ❌ Don’t edit the database directly in the Supabase dashboard for production projects.  

---

### Commands

#### Apply existing migrations
```bash
supabase migration up
```
- Applies all **pending** migrations in `supabase/migrations/` to your local database.  
- Use this **only when you’ve pulled new migrations from Git** (e.g., after a teammate added a new table).  
- ⚠️ **Do not run this if you are connected to production. Always check with `supabase status` first.**  

#### Create a new migration
```bash
supabase migration new add_profiles_table
```
- Creates a new SQL file in `supabase/migrations/` with a timestamped name.  
- Edit this file to define schema changes.  

#### Best practices
- Always **review migration files** before running them.  
- Always **commit migration files** to Git.  
- Always **regenerate types** after applying migrations:
  ```bash
  supabase gen types typescript --local > supabase/types/database.ts
  ```
- Never manually edit old migration files — create a new one instead.  
- Never run migrations directly against production without review.  

---

## 6. Generate Types

After applying migrations, regenerate TypeScript types:

```bash
supabase gen types typescript --local > supabase/types/database.ts
```

- Generates types from your current database schema.  
- Ensures type safety when querying Supabase in your React app.  

---

## 7. Frontend Setup

Start the dev server:

```bash
pnpm dev
```

- Runs the Vite dev server at [http://localhost:5173](http://localhost:5173).  

Build for production:

```bash
pnpm build
```

- Compiles the React app into optimized static assets in `dist/`.  

---

## 8. Styling Setup

1. Tailwind is already configured (`tailwind.config.ts`).  
2. Use **shadcn/ui CLI** to add components:

```bash
npx shadcn@latest add button
```

3. Always use the `cn` utility for class merging:

```tsx
import { cn } from "@/lib/utils";

<Button className={cn("w-full", isActive && "bg-primary")}>Click</Button>
```

---

## 9. State Management

- **Server State** → React Query  
  - All Supabase queries/mutations must be wrapped in React Query hooks.  
- **Client State** → Nanostores  
  - Use atoms/maps for global UI state (e.g., theme, sidebar).  
- **Local State** → React `useState`  
  - For component-only state.  

---

## 10. Project Structure

See [📂 Project Folder Structure](./project-folder-structure.md) for details.  

Key points:
- `supabase/` → migrations, edge functions, generated types  
- `src/features/` → feature-based modules (auth, profile, posts)  
- `src/lib/` → singletons (supabase client, query client, utils)  
- `src/components/ui/` → shadcn/ui components  
- `src/stores/` → Nanostore atoms/maps  

---

## 11. Workflow

1. **Schema change?**  
   - Create a migration: `supabase migration new <name>`  
   - Edit the SQL file with schema changes  
   - Run `supabase migration up` (after checking you’re on local/staging)  
   - Regenerate types  

2. **New feature?**  
   - Create folder in `src/features/<feature>/`  
   - Add `components/`, `hooks/`, `services/` as needed  

3. **New UI component?**  
   - Use `npx shadcn@latest add <component>`  
   - Never hand-roll shadcn components  

4. **Data fetching?**  
   - Wrap in a service (`src/services/`)  
   - Expose via React Query hook (`src/features/.../hooks/`)  

5. **Client state?**  
   - Add Nanostore in `src/stores/`  

---

## 12. Common Commands (with descriptions)

### Frontend
- **Start dev server**  
  ```bash
  pnpm dev
  ```
  Runs the app locally at [http://localhost:5173](http://localhost:5173).

- **Build for production**  
  ```bash
  pnpm build
  ```
  Outputs optimized static files to `dist/`.

---

### Supabase
- **Run Supabase locally**  
  ```bash
  supabase start
  ```
  Starts a local Supabase instance (Postgres, API, Auth, Storage).

- **Stop Supabase**  
  ```bash
  supabase stop
  ```
  Stops the local Supabase instance.

- **Check current project**  
  ```bash
  supabase status
  ```
  Shows which Supabase project you are connected to.  
  Always confirm you are on **local** or **staging**, not **production**.

- **Apply migrations**  
  ```bash
  supabase migration up
  ```
  Applies all pending migrations to your local database.  
  ⚠️ Only run after confirming with `supabase status`.

- **Create a new migration**  
  ```bash
  supabase migration new <name>
  ```
  Creates a new SQL migration file in `supabase/migrations/`.  

- **Generate types**  
  ```bash
  supabase gen types typescript --local > supabase/types/database.ts
  ```
  Generates TypeScript types from the current schema.  

---

### Git & Env
- **Copy environment file**  
  ```bash
  cp .env.example .env
  ```
  Creates a local `.env` file from the example template.  
  Fill in your Supabase URL and anon key here.

---

## 13. Best Practices

- Always use **Supabase generated types**.  
- Always enable **RLS** and write policies.  
- Never use `any`.  
- Never edit `ui/` components directly — wrap them.  
- Always validate input in **Edge Functions**.  
- Always use **React Query** for server state.  
- Always use **Nanostore** for client state.  
- Always review migrations before applying them.  
- Always check `supabase status` before running migrations.  

---

# ✅ You’re Ready!

At this point you should be able to:  
- Run the app locally  
- Query Supabase with type safety  
- Add new features in a consistent way  
- Style components with shadcn + Tailwind  
- Manage state with React Query + Nanostores  
- Safely evolve the database schema with migrations  
- Avoid accidentally connecting to production