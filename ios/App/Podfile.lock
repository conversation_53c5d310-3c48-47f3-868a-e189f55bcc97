PODS:
  - Capacitor (6.2.0):
    - Capacitor<PERSON>ordova
  - CapacitorApp (6.0.1):
    - Capacitor
  - CapacitorCamera (6.1.2):
    - Capacitor
  - CapacitorCordova (6.2.0)
  - CapacitorHaptics (6.0.1):
    - Capacitor
  - CapacitorKeyboard (6.0.2):
    - Capacitor
  - CapacitorStatusBar (6.0.1):
    - Capacitor

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorCamera (from `../../node_modules/@capacitor/camera`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorHaptics (from `../../node_modules/@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../../node_modules/@capacitor/keyboard`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorCamera:
    :path: "../../node_modules/@capacitor/camera"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorHaptics:
    :path: "../../node_modules/@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../../node_modules/@capacitor/keyboard"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"

SPEC CHECKSUMS:
  Capacitor: 05d35014f4425b0740fc8776481f6a369ad071bf
  CapacitorApp: f51c08c64df914fc934239fd1b3fc809d62a02f6
  CapacitorCamera: 9bc7b005d0e6f1d5f525b8137045b60cffffce79
  CapacitorCordova: b33e7f4aa4ed105dd43283acdd940964374a87d9
  CapacitorHaptics: 05d33e20e394b1977dfd2a00a2621e04098164c1
  CapacitorKeyboard: 5bf4458dd989b56a60ea15c92cbca5fbfae6e9f9
  CapacitorStatusBar: 9ed7f06318fd436917072a9bf60fda56d12c8bdb

PODFILE CHECKSUM: 3d364089fbea25a4a6298746022e532616ef4206

COCOAPODS: 1.16.2
