name: <PERSON>ull Request Checks

permissions:
  pull-requests: write
  contents: read

on:
  pull_request:
    branches: [ main, master ]

jobs:
  install:
    uses: ./.github/workflows/install.yml

  unit-tests:
    needs: install
    uses: ./.github/workflows/unit-tests.yml
    secrets: inherit


  # Disabled until we have some seeding on preview database
  # e2e-tests:
  #   needs: install
  #   uses: ./.github/workflows/e2e-tests.yml
  #   secrets: inherit

  # report-results:
  #   needs: [unit-tests, e2e-tests]
  #   if: always()
  #   uses: ./.github/workflows/reporting.yml
  #   secrets: inherit

  build:
    needs: [unit-tests]
    # needs: [report-results]
    uses: ./.github/workflows/build.yml 