name: E2E Tests

on:
  workflow_call:

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    env:
      VITE_APP_SUPABASE_URL: ${{ secrets.VITE_APP_SUPABASE_URL }}
      VITE_APP_SUPABASE_ANON_KEY: ${{ secrets.VITE_APP_SUPABASE_ANON_KEY }}
      AIRTABLE_API_KEY: ${{ secrets.AIRTABLE_API_KEY }}
      AIRTABLE_BASE_ID: ${{ secrets.AIRTABLE_BASE_ID }}
      SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
      VITE_APP_STRIPE_PUBLIC_KEY: ${{ secrets.VITE_APP_STRIPE_PUBLIC_KEY }}
      PLAYWRIGHT_TEST_BASE_URL: http://localhost:3000
      TEST_USER_EMAIL: ${{ secrets.TEST_USER_EMAIL }}
      TEST_USER_PASSWORD: ${{ secrets.TEST_USER_PASSWORD }}
      NODE_ENV: test

    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/actions/setup-node
      - uses: ./.github/workflows/actions/setup-playwright

      - name: Start and test application
        uses: ./.github/workflows/actions/test-app
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
        with:
          lighthouse-token: ${{ env.LHCI_GITHUB_APP_TOKEN }} 