name: Unit Tests

on:
  workflow_call:

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    env:
      VITE_APP_SUPABASE_URL: ${{ secrets.VITE_APP_SUPABASE_URL }}
      VITE_APP_SUPABASE_ANON_KEY: ${{ secrets.VITE_APP_SUPABASE_ANON_KEY }}
      AIRTABLE_API_KEY: ${{ secrets.AIRTABLE_API_KEY }}
      AIRTABLE_BASE_ID: ${{ secrets.AIRTABLE_BASE_ID }}
      SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
      VITE_APP_STRIPE_PUBLIC_KEY: ${{ secrets.VITE_APP_STRIPE_PUBLIC_KEY }}
      NODE_ENV: test

    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/workflows/actions/setup-node

      - name: Run unit tests
        run: npm run test | tee unit-test-results.txt

      - name: Upload unit test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: unit-test-results
          path: unit-test-results.txt
          retention-days: 1 