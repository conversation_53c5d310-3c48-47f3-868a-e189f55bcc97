name: Report Test Results

on:
  workflow_call:

jobs:
  report:
    runs-on: ubuntu-latest
    steps:
      - name: Download unit test results
        uses: actions/download-artifact@v4
        with:
          name: unit-test-results
          path: unit-test-results

      - name: Download test results
        uses: actions/download-artifact@v4
        with:
          name: test-results
          path: test-results

      - name: Download lighthouse results
        uses: actions/download-artifact@v4
        with:
          name: lighthouse-results
          path: lighthouse-results

      - name: Install jq
        run: sudo apt-get install jq -y

      - name: Create combined report
        id: create-report
        run: |
          echo "### Test Results Summary" > report.md
          
          # Unit Tests
          echo "#### Unit Tests" >> report.md
          if [ -f "unit-test-results/unit-test-results.txt" ]; then
            echo '```' >> report.md
            cat unit-test-results/unit-test-results.txt >> report.md
            echo '```' >> report.md
          else
            echo "No unit test results found" >> report.md
          fi
          echo "" >> report.md
          
          # E2E Tests
          echo "#### E2E Tests" >> report.md
          if [ -f "test-results/results.json" ]; then
            echo '```' >> report.md
            # Get total counts
            TOTAL=$(cat test-results/results.json | jq -r '.suites | length')
            PASSED=$(cat test-results/results.json | jq -r '[.suites[].specs | map(select(.ok)) | length] | add')
            FAILED=$(cat test-results/results.json | jq -r '[.suites[].specs | map(select(.ok | not)) | length] | add')
            echo "Test Suites: $TOTAL" >> report.md
            echo "✅ Passed: $PASSED" >> report.md
            echo "❌ Failed: $FAILED" >> report.md
            echo "" >> report.md
            echo "Test Suites:" >> report.md
            cat test-results/results.json | jq -r '.suites[] | "• \(.title)"' >> report.md
            echo '```' >> report.md
          else
            echo "No E2E test results found" >> report.md
            echo "Available files in playwright-report:" >> report.md
            ls -l test-results/playwright-report/ >> report.md 2>&1
          fi
          echo "" >> report.md
          
          # Lighthouse Results
          echo "#### Performance Metrics" >> report.md
          # Find the latest lhr json file
          LHR_FILE=$(find lighthouse-results -name "lhr-*.json" | sort -r | head -n1)
          if [ ! -z "$LHR_FILE" ]; then
            echo '```' >> report.md
            cat "$LHR_FILE" | jq -r '"🎯 Performance: \(.categories.performance.score * 100 | floor)%\n🎯 Accessibility: \(.categories.accessibility.score * 100 | floor)%\n🎯 Best Practices: \(.categories[\"best-practices\"].score * 100 | floor)%\n🎯 SEO: \(.categories.seo.score * 100 | floor)%"' >> report.md || echo "Error parsing Lighthouse results"
            echo '```' >> report.md
            # Get and display the Lighthouse report URL
            LINKS_FILE=$(find lighthouse-results -name "*.json" -not -name "lhr-*.json" | grep -i "links" | head -n1)
            if [ ! -z "$LINKS_FILE" ]; then
              echo "" >> report.md
              echo "📊 [View detailed Lighthouse report]($( cat "$LINKS_FILE" | jq -r '.links[0].url' ))" >> report.md
            fi
          else
            echo "No Lighthouse results found" >> report.md
            echo "Available files in .lighthouseci:" >> report.md
            ls -l lighthouse-results/ >> report.md 2>&1
          fi
          
          # Debug information
          echo "#### Debug Information" >> report.md
          echo '```' >> report.md
          echo "Directory structure:" >> report.md
          ls -R test-results >> report.md 2>&1
          echo '```' >> report.md
          
          # Create a summary for the step
          {
            echo 'report<<EOF'
            cat report.md
            echo 'EOF'
          } >> $GITHUB_OUTPUT

      - name: Find Comment
        uses: peter-evans/find-comment@v3
        id: find-comment
        with:
          issue-number: ${{ github.event.pull_request.number }}
          comment-author: 'github-actions[bot]'
          body-includes: "### Test Results Summary"

      - name: Create or Update Comment
        uses: peter-evans/create-or-update-comment@v4
        with:
          comment-id: ${{ steps.find-comment.outputs.comment-id }}
          issue-number: ${{ github.event.pull_request.number }}
          body: ${{ steps.create-report.outputs.report }}
          edit-mode: replace 