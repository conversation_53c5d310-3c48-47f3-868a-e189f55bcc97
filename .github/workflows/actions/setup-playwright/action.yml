name: Setup Playwright
description: Sets up Playwright browsers and cache

runs:
  using: composite
  steps:
    - name: Cache Playwright browsers
      uses: actions/cache@v4
      id: playwright-cache
      with:
        path: ~/.cache/ms-playwright
        key: ${{ runner.os }}-playwright-${{ hashFiles('**/package-lock.json') }}

    - name: Install Playwright browsers
      if: steps.playwright-cache.outputs.cache-hit != 'true'
      shell: bash
      run: npx playwright install --with-deps 