name: Test Application
description: Runs E2E and Lighthouse tests

inputs:
  lighthouse-token:
    description: 'Lighthouse CI GitHub App Token'
    required: true

runs:
  using: composite
  steps:
    - name: Install serve
      shell: bash
      run: npm install -g serve

    - name: Start application and wait for it
      shell: bash
      run: |
        npm run serve-prod &
        timeout=30
        until $(curl --output /dev/null --silent --head --fail http://localhost:3000); do
          if [ $timeout -eq 0 ]; then
            echo "Timeout waiting for server to start"
            exit 1
          fi
          printf '.'
          timeout=$((timeout-1))
          sleep 1
        done
      env:
        PORT: 3000

    - name: Run E2E tests
      shell: bash
      run: |
        mkdir -p test-results
        npx playwright test
      env:
        PLAYWRIGHT_TEST_BASE_URL: http://localhost:3000
        PLAYWRIGHT_TIMEOUT: 30000
        CI: true

    - name: Upload E2E test results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: test-results
        path: |
          playwright-report/
          test-results/
        retention-days: 1

    - name: Install Lighthouse CI
      if: always()  # Run even if E2E tests fail
      shell: bash
      run: npm install -g @lhci/cli@0.13.x

    - name: Run Lighthouse tests
      if: always()  # Run even if E2E tests fail
      shell: bash
      run: |
        mkdir -p test-results/.lighthouseci
        lhci autorun --collect.url=http://localhost:3000 --config=./lighthouserc.cjs || true
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ inputs.lighthouse-token }}

    - name: Upload Lighthouse results
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: lighthouse-results
        path: test-results/.lighthouseci/
        retention-days: 1 