name: Setup Node
description: Sets up Node.js and restores node_modules cache

runs:
  using: composite
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: Restore node_modules
      uses: actions/cache@v4
      with:
        path: |
          **/node_modules
        key: ${{ runner.os }}-node-modules-${{ hashFiles('**/package-lock.json') }} 