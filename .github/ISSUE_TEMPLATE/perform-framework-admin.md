# Perform Framework Administration

## Overview
Create a comprehensive administration interface for managing PERFORM evaluation frameworks across different sports. This feature will allow super admins to create, edit, and manage evaluation frameworks (like the existing SHOT PERFORM for football) and create new frameworks for other sports (e.g., boxing).

## User Story
As a **super admin**, I want to **manage evaluation frameworks for different sports** so that **clubs can use sport-specific evaluation criteria tailored to their needs**.

## Acceptance Criteria

### Access Control
- [ ] Only super admins can access the Perform Framework Administration section
- [ ] The feature appears in the AdminToolsSection for super admins only
- [ ] Non-super admins cannot access the route directly

### Framework Management
- [ ] View a list of all existing evaluation frameworks
- [ ] See framework details including:
  - Framework name and version
  - Sport type
  - Description
  - Duration (weeks)
  - Start/end dates
  - Supported positions
  - Categories (Technical, Physical, Psychological, Social)
- [ ] Create new frameworks with all required metadata
- [ ] Edit existing framework metadata
- [ ] Activate/deactivate frameworks
- [ ] Clone existing frameworks as templates for new sports

### Evaluation Criteria Management
- [ ] View all evaluation criteria for a framework organized by:
  - Week number (1-52)
  - Position (All, position-specific)
  - Category (Technical, Physical, Psychological, Social)
  - Area (sub-categories)
- [ ] Add new evaluation questions with:
  - Position assignment
  - Category selection
  - Area specification
  - Evaluation focus
  - Question text
  - Week assignment
- [ ] Edit existing evaluation questions
- [ ] Delete evaluation questions (with confirmation)
- [ ] Bulk import criteria from CSV/JSON
- [ ] Export criteria to CSV/JSON
- [ ] Preview how questions appear to coaches/players

### Framework Templates
- [ ] Provide pre-built templates for common sports:
  - Football (existing SHOT PERFORM)
  - Boxing (new)
  - Basketball (future)
  - Baseball (future)
- [ ] Allow customization of templates
- [ ] Save custom frameworks as new templates

### Validation & Safety
- [ ] Prevent deletion of frameworks with existing evaluations
- [ ] Validate all required fields before saving
- [ ] Show warnings when modifying active frameworks
- [ ] Maintain version history for audit trail
- [ ] Confirmation dialogs for destructive actions

## Technical Requirements

### File Structure
```
src/pages/section/Coach/supporting/PerformFramework/
├── index.tsx                    # Main administration page
├── components/
│   ├── FrameworkList.tsx       # List of all frameworks
│   ├── FrameworkEditor.tsx     # Create/edit framework metadata
│   ├── CriteriaManager.tsx     # Manage evaluation questions
│   ├── CriteriaGrid.tsx        # Grid view of criteria by week/position
│   ├── CriteriaEditor.tsx      # Add/edit individual criteria
│   ├── ImportExport.tsx        # Import/export functionality
│   └── FrameworkPreview.tsx    # Preview framework as seen by users
├── hooks/
│   ├── useFrameworks.ts        # Framework CRUD operations
│   ├── useCriteria.ts          # Criteria CRUD operations
│   └── useFrameworkTemplates.ts # Template management
├── services/
│   ├── frameworkService.ts     # API calls for frameworks
│   └── criteriaService.ts      # API calls for criteria
└── types/
    └── framework.types.ts       # TypeScript definitions
```

### Database Schema References
- `evaluation_framework_metadata` - Framework definitions
- `evaluation_criteria` - Individual evaluation questions
- Consider adding `framework_templates` table for reusable templates

### Routes
- Main route: `/coach/perform-framework-management`
- Sub-routes:
  - `/coach/perform-framework-management/create`
  - `/coach/perform-framework-management/:frameworkVersion/edit`
  - `/coach/perform-framework-management/:frameworkVersion/criteria`

### API Endpoints Needed
```typescript
// Framework Management
GET    /api/admin/frameworks
POST   /api/admin/frameworks
PUT    /api/admin/frameworks/:version
DELETE /api/admin/frameworks/:version

// Criteria Management
GET    /api/admin/frameworks/:version/criteria
POST   /api/admin/frameworks/:version/criteria
PUT    /api/admin/criteria/:id
DELETE /api/admin/criteria/:id
POST   /api/admin/criteria/bulk-import
GET    /api/admin/criteria/export/:version

// Templates
GET    /api/admin/framework-templates
POST   /api/admin/framework-templates
```

### UI/UX Requirements

#### Main Dashboard
- Table view of all frameworks with key info
- Quick actions: View, Edit, Clone, Deactivate
- Create New Framework button
- Search and filter capabilities

#### Framework Editor
- Multi-step form wizard:
  1. Basic Info (name, sport, description)
  2. Duration & Schedule
  3. Positions Setup
  4. Categories Configuration
  5. Review & Create
- Save as draft functionality
- Validation at each step

#### Criteria Manager
- Weekly calendar view
- Position-based filtering
- Category tabs
- Drag-and-drop reordering
- Bulk actions toolbar
- Search functionality

#### Import/Export
- File upload zone
- Format selection (CSV, JSON)
- Column mapping for imports
- Preview before import
- Download options for exports

### Integration Points
- Add "Perform Framework Management" to AdminToolsSection
- Use existing Shadow DOM components for UI consistency
- Integrate with existing permission system
- Use existing notification system for success/error messages

## Testing Requirements

### Unit Tests
- [ ] Framework CRUD operations
- [ ] Criteria CRUD operations
- [ ] Import/export functionality
- [ ] Validation logic
- [ ] Permission checks

### Integration Tests
- [ ] End-to-end framework creation
- [ ] Criteria management workflow
- [ ] Import/export round trip
- [ ] Permission enforcement

### Manual Testing
- [ ] Test with multiple sport types
- [ ] Verify position-specific criteria work correctly
- [ ] Test bulk operations with large datasets
- [ ] Verify UI responsiveness with many criteria

## Future Enhancements
1. **AI-Assisted Generation**: Use AI to suggest evaluation criteria based on sport and age group
2. **Framework Marketplace**: Share frameworks between clubs
3. **Multi-language Support**: Translate frameworks for international use
4. **Advanced Analytics**: Track which criteria are most/least used
5. **Custom Categories**: Allow clubs to define custom evaluation categories
6. **Age-Group Variants**: Create age-appropriate versions of frameworks
7. **Certification Integration**: Link frameworks to coaching certifications

## Dependencies
- Existing evaluation system must continue working during development
- No breaking changes to current SHOT PERFORM framework
- Maintain backward compatibility with existing evaluations

## Success Metrics
- Super admins can create a new sport framework in < 30 minutes
- Zero downtime for existing evaluation features
- All existing evaluations remain accessible
- New frameworks are immediately available for use

## Notes
- This is a super admin only feature - no access for regular admins or club admins
- The existing SHOT PERFORM framework should be protected from accidental modification
- Consider adding a "beta" flag for new frameworks during testing phase
- Ensure proper database backups before any framework modifications

## Mockups
*[Placeholder for UI mockups - to be added by design team]*

### Framework List View
- Table with columns: Name, Sport, Version, Status, Weeks, Positions, Last Modified, Actions
- Filters for sport type and status
- Search by name

### Framework Editor
- Clean form layout with sections
- Position picker with multi-select
- Week range selector
- Category configuration with descriptions

### Criteria Grid
- Week numbers across top
- Positions down left side
- Category/Area grouping
- Color coding by category
- Click to edit any cell

---

**Priority**: High
**Estimated Effort**: Large (3-4 sprints)
**Team**: Backend, Frontend, QA
**Labels**: `feature`, `super-admin`, `perform`, `administration`