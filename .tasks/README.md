# SHOT Security Fix Tasks

This folder contains organized tasks for fixing critical security vulnerabilities identified in the SHOT codebase.

## Task Organization

- `phase2.1-security/` - Critical security vulnerabilities (MUST FIX FIRST)
- `completed/` - Completed tasks for reference
- `in-progress/` - Currently active tasks
- `blocked/` - Tasks waiting for dependencies

## Priority Order

1. **CRITICAL**: Remove hardcoded credentials
2. **CRITICAL**: Consolidate Supabase clients  
3. **HIGH**: Update Supabase to v2.x
4. **HIGH**: Generate database types
5. **MEDIUM**: Remove TypeScript bypasses

## Usage

Each task file contains:
- Problem description
- Security impact
- Step-by-step solution
- Verification steps
- Dependencies and blockers

Start with files in `phase2.1-security/` folder in numerical order.