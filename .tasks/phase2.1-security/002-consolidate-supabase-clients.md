# Task: Consolidate Supabase Clients

**Priority**: 🚨 CRITICAL - Security & Performance  
**Phase**: 2.1 - Security Fixes  
**Estimated Time**: 3-4 hours  
**Dependencies**: Task 001 (Remove Hardcoded Credentials)  

## Problem Description

Multiple Supabase client instances exist throughout the codebase, creating:
- Inconsistent authentication states
- Memory leaks and performance issues
- Configuration drift between clients
- Potential security vulnerabilities from different client configs

**Current Issues:**
- Multiple client instances in different files
- Over-engineered configurations (circuit breakers, request queues)
- Inconsistent error handling across clients

## Current State Analysis

### Step 1: Find All Supabase Client Instances
```bash
# Search for Supabase client creations
rg -A 5 -B 5 "createClient" --type ts --type tsx
rg -A 5 -B 5 "new.*Client" --type ts --type tsx
rg -A 5 -B 5 "supabase.*client" --type ts --type tsx -i
```

### Step 2: Identify Client Usage Patterns
```bash
# Find all imports of supabase clients
rg "import.*supabase" --type ts --type tsx
rg "from.*supabase" --type ts --type tsx
```

### Step 3: Analyze Configuration Complexity
```bash
# Look for over-engineered configurations
rg -i "circuit.*breaker" --type ts --type tsx
rg -i "request.*queue" --type ts --type tsx
rg -i "retry.*logic" --type ts --type tsx
```

## Solution Steps

### 1. Create Centralized Supabase Client

**File: `src/lib/supabase.ts`**
```typescript
import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/database'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  throw new Error(
    'Missing Supabase environment variables. Please check your .env file.'
  )
}

// Single, centralized Supabase client
export const supabase = createClient<Database>(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Export types for consistent usage
export type { Database }
```

### 2. Remove Duplicate Client Files

**Files to Remove/Consolidate:**
```bash
# These are likely duplicate client files to remove
src/utils/supabaseClient.ts
src/config/supabaseClient.ts  
src/services/supabaseClient.ts
src/contexts/supabaseClient.ts
```

**Before removal, check each file:**
```bash
# For each duplicate file, check what's importing it
rg -l "from.*supabaseClient" --type ts --type tsx
```

### 3. Update All Imports

**Replace all variations with single import:**
```typescript
// Before (multiple variations)
import { supabase } from '../utils/supabaseClient'
import { client } from '../config/supabaseClient'
import supabaseClient from '../services/supabaseClient'

// After (single source of truth)
import { supabase } from '@/lib/supabase'
```

### 4. Remove Over-Engineering

**Simplify Complex Configurations:**
```typescript
// Before (over-engineered)
class SupabaseClientWithCircuitBreaker {
  private requestQueue: RequestQueue
  private circuitBreaker: CircuitBreaker
  private retryLogic: RetryManager
  // ... complex implementation
}

// After (simple and reliable)
export const supabase = createClient<Database>(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})
```

### 5. Update Path Mapping

**Add to `tsconfig.json` or `vite.config.ts`:**
```json
{
  "compilerOptions": {
    "paths": {
      "@/*": ["./src/*"],
      "@/lib/*": ["./src/lib/*"]
    }
  }
}
```

## Implementation Steps

### Step 1: Create the Centralized Client
1. Create `src/lib/` directory
2. Create `src/lib/supabase.ts` with the centralized client
3. Test that environment variables are properly loaded

### Step 2: Audit Existing Clients
```bash
# Create audit report
echo "# Supabase Client Audit" > client-audit.md
rg -l "createClient\|supabaseClient" --type ts --type tsx >> client-audit.md
```

### Step 3: Systematic Replacement
For each file found in audit:
1. Update import statement
2. Remove local client creation
3. Test functionality
4. Remove unused client files

### Step 4: Update Services and Contexts
Update all service files to use centralized client:
```typescript
// Before
import { supabase } from '../utils/supabaseClient'

// After  
import { supabase } from '@/lib/supabase'
```

## Verification Steps

### 1. Import Verification
```bash
# Should find no duplicate client creations
rg "createClient" --type ts --type tsx | grep -v "src/lib/supabase.ts"

# Should find only imports from centralized location
rg "import.*supabase" --type ts --type tsx
```

### 2. Functionality Testing
```bash
# Test authentication flow
npm run dev
# Verify login/logout works
# Check browser network tab for duplicate requests
```

### 3. Build Verification
```bash
# Ensure no broken imports
npm run build
```

### 4. Memory Leak Check
- Monitor browser dev tools for multiple client instances
- Check for duplicate auth listeners
- Verify single connection pool

## Files Likely to Need Updates

**High Priority:**
- `src/supabaseClient.ts` (main client)
- `src/contexts/UserContext.tsx`
- `src/contexts/EnhancedShoppingCartContext*.tsx`
- All files in `src/services/`

**Medium Priority:**
- Component files using direct database calls
- Auth-related components
- Route guards and protected routes

## Success Criteria

- [ ] Single Supabase client instance in `src/lib/supabase.ts`
- [ ] All duplicate client files removed
- [ ] All imports updated to use centralized client
- [ ] No over-engineered configurations (circuit breakers, etc.)
- [ ] Build succeeds without import errors
- [ ] Authentication functionality preserved
- [ ] No memory leaks from multiple clients
- [ ] Consistent error handling across all database operations

## Performance Benefits

- ✅ Single connection pool instead of multiple
- ✅ Consistent auth state management
- ✅ Reduced bundle size (no duplicate clients)
- ✅ Simplified debugging and maintenance
- ✅ Better error handling consistency

## Security Benefits

- ✅ Single point of configuration management
- ✅ Consistent security policies across all database calls
- ✅ Easier to audit and update security settings
- ✅ Reduced attack surface from configuration drift

## Next Steps After Completion

1. Move to Task 003: Update Supabase to v2.x
2. Generate database types for the centralized client
3. Update team documentation for import patterns