# Task: Update Supabase to v2.x

**Priority**: 🔥 HIGH - Security & Performance  
**Phase**: 2.1 - Security Fixes  
**Estimated Time**: 4-6 hours  
**Dependencies**: Task 002 (Consolidate Supabase Clients)  

## Problem Description

The codebase is using Supabase v1.31.1, which is significantly outdated:
- **Security vulnerabilities** in older version
- **Missing performance improvements** from v2.x
- **Deprecated APIs** that may break in future
- **Limited TypeScript support** compared to v2.x

**Current Version**: `@supabase/supabase-js": "^1.31.1"`  
**Target Version**: `@supabase/supabase-js": "^2.45.0"` (latest stable)

## Breaking Changes Analysis

### Major Changes in Supabase v2.x
1. **Auth API Changes**: `signIn` → `signInWithPassword`
2. **Response Structure**: Different error handling patterns
3. **TypeScript Support**: Enhanced type safety
4. **Session Management**: Improved session handling
5. **Real-time**: Updated subscription methods

## Solution Steps

### 1. Update Package Dependencies

```bash
# Update Supabase client to v2.x
npm uninstall @supabase/supabase-js
npm install @supabase/supabase-js@^2.45.0

# Update related packages if needed
npm update @supabase/postgrest-js
npm update @supabase/realtime-js
```

### 2. Update Client Configuration

**File: `src/lib/supabase.ts`**
```typescript
import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/database'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase environment variables')
}

// Updated v2.x configuration
export const supabase = createClient<Database>(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce' // Enhanced security in v2.x
  },
  db: {
    schema: 'public'
  }
})

export type { Database }
```

### 3. Update Authentication Methods

**Before (v1.x):**
```typescript
// Old auth methods
const { user, session, error } = await supabase.auth.signIn({
  email: '<EMAIL>',
  password: 'password'
})

const { error } = await supabase.auth.signOut()
```

**After (v2.x):**
```typescript
// New auth methods
const { data, error } = await supabase.auth.signInWithPassword({
  email: '<EMAIL>',
  password: 'password'
})

const { error } = await supabase.auth.signOut()

// Access user and session from data
const { user, session } = data
```

### 4. Update Response Handling

**Before (v1.x):**
```typescript
const { data, error, count } = await supabase
  .from('teams')
  .select('*')

if (error) {
  console.error('Error:', error.message)
}
```

**After (v2.x):**
```typescript
const { data, error, count } = await supabase
  .from('teams')
  .select('*')

if (error) {
  console.error('Error:', error.message)
  // Error structure is similar but enhanced
}
```

### 5. Update Real-time Subscriptions

**Before (v1.x):**
```typescript
const subscription = supabase
  .from('teams')
  .on('*', (payload) => {
    console.log('Change received:', payload)
  })
  .subscribe()
```

**After (v2.x):**
```typescript
const subscription = supabase
  .channel('teams_changes')
  .on(
    'postgres_changes',
    {
      event: '*',
      schema: 'public',
      table: 'teams'
    },
    (payload) => {
      console.log('Change received:', payload)
    }
  )
  .subscribe()
```

## File-by-File Update Strategy

### Step 1: Authentication Files
**Files to Update:**
- `src/foundation/auth/services/AuthService.ts`
- `src/contexts/UserContext.tsx`  
- `src/hooks/useAuth.ts`
- All login components

**Key Changes:**
- Replace `signIn` with `signInWithPassword`
- Replace `signUp` with `signUp` (similar API)
- Update session handling
- Fix TypeScript types

### Step 2: Service Layer Files
**Files to Update:**
- All files in `src/services/`
- Database query patterns remain mostly the same
- Update error handling patterns

### Step 3: Real-time Features
**Files to Update:**
- Components using Supabase subscriptions
- Real-time data features
- Live updates functionality

## Testing Strategy

### 1. Authentication Testing
```typescript
// Test file: src/__tests__/auth-v2-migration.test.ts
import { supabase } from '@/lib/supabase'

describe('Supabase v2 Auth Migration', () => {
  test('should sign in with new API', async () => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password'
    })
    
    expect(error).toBeNull()
    expect(data.user).toBeDefined()
    expect(data.session).toBeDefined()
  })
})
```

### 2. Database Operations Testing
```bash
# Run existing tests to ensure no regression
npm run test

# Specific tests for database operations
npm run test -- --grep "database|supabase"
```

### 3. Real-time Testing
Test all real-time features manually:
- Live data updates
- Subscription cleanup
- Connection handling

## Implementation Checklist

### Phase 1: Dependencies & Configuration
- [ ] Update `@supabase/supabase-js` to v2.x
- [ ] Update client configuration in `src/lib/supabase.ts`
- [ ] Test basic client instantiation

### Phase 2: Authentication Migration
- [ ] Update `AuthService.ts` authentication methods
- [ ] Update all login/signup components
- [ ] Update session management
- [ ] Update auth hooks and contexts

### Phase 3: Database Operations
- [ ] Verify all database queries work (should be compatible)
- [ ] Update any custom error handling
- [ ] Test CRUD operations

### Phase 4: Real-time Features
- [ ] Update all subscription code
- [ ] Test real-time data updates
- [ ] Verify subscription cleanup

### Phase 5: TypeScript Updates
- [ ] Fix any new TypeScript errors
- [ ] Update type imports if needed
- [ ] Ensure full type safety

## Verification Steps

### 1. Build Verification
```bash
# Ensure clean build with v2.x
npm run build

# Check for any TypeScript errors
npm run type-check
```

### 2. Functionality Testing
```bash
# Start development server
npm run dev

# Test core functionality:
# - User login/logout
# - Data fetching
# - Real-time updates
# - Error handling
```

### 3. Bundle Size Check
```bash
# Compare bundle sizes before/after
npm run build
# Check dist/ folder size compared to before migration
```

## Common Migration Issues & Solutions

### Issue 1: Authentication Errors
```typescript
// If you get auth errors, check session handling
const { data: { session } } = await supabase.auth.getSession()
```

### Issue 2: TypeScript Errors
```typescript
// Update type imports
import type { User, Session } from '@supabase/supabase-js'
```

### Issue 3: Real-time Subscription Errors
```typescript
// Ensure proper channel cleanup
useEffect(() => {
  const channel = supabase.channel('my-channel')
  
  return () => {
    supabase.removeChannel(channel)
  }
}, [])
```

## Performance Benefits of v2.x

- ✅ **Faster Auth**: Improved authentication performance
- ✅ **Better Caching**: Enhanced client-side caching
- ✅ **Smaller Bundle**: Optimized package size
- ✅ **Connection Pooling**: Better database connection management
- ✅ **Memory Management**: Improved memory usage patterns

## Security Benefits of v2.x

- ✅ **Enhanced Auth Flow**: PKCE flow for better security
- ✅ **Session Security**: Improved session token handling
- ✅ **Type Safety**: Better TypeScript support prevents errors
- ✅ **Updated Dependencies**: Latest security patches

## Success Criteria

- [ ] Supabase v2.x successfully installed
- [ ] All authentication flows working
- [ ] All database operations functioning
- [ ] Real-time features operational
- [ ] No TypeScript errors
- [ ] All tests passing
- [ ] No performance regressions
- [ ] Bundle size similar or smaller

## Next Steps After Completion

1. Move to Task 004: Generate Database Types
2. Update team documentation with v2.x patterns
3. Consider implementing new v2.x features (enhanced auth, etc.)

## Rollback Plan

If migration causes critical issues:
```bash
# Rollback to v1.x
npm uninstall @supabase/supabase-js
npm install @supabase/supabase-js@^1.31.1

# Revert client configuration
git checkout HEAD~1 src/lib/supabase.ts
```