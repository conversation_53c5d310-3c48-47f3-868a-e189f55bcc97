# Task: Generate Database Types

**Priority**: 🔥 HIGH - Type Safety & Developer Experience  
**Phase**: 2.1 - Security Fixes  
**Estimated Time**: 2-3 hours  
**Dependencies**: Task 003 (Update Supabase to v2.x)  

## Problem Description

The codebase lacks proper TypeScript types for database operations, leading to:
- **Runtime errors** from incorrect field names
- **Poor developer experience** without autocomplete
- **Type safety gaps** in database queries
- **Maintenance difficulty** when schema changes

**Current Issues:**
- Database queries without type safety
- Manual type definitions that may be outdated
- No IDE autocomplete for database fields
- Potential runtime errors from schema mismatches

## Benefits of Generated Types

### Type Safety Benefits
- ✅ Compile-time error detection for database queries
- ✅ Autocomplete for table names and column names
- ✅ Type inference for query results
- ✅ Prevention of common database query errors

### Developer Experience Benefits
- ✅ IDE autocomplete for all database fields
- ✅ Inline documentation for database schema
- ✅ Refactoring safety across the codebase
- ✅ Consistent type definitions across team

## Solution Steps

### 1. Install Required Dependencies

```bash
# Ensure Supabase CLI is available
npm install -g supabase@latest

# Or use local installation
npm install --save-dev supabase
```

### 2. Configure Supabase Type Generation

**Method 1: Using Project Reference (Recommended)**
```bash
# Generate types from your Supabase project
npx supabase gen types typescript --project-id YOUR_PROJECT_ID > src/types/database.ts
```

**Method 2: Using Local Database**
```bash
# If using local development setup
npx supabase gen types typescript --local > src/types/database.ts
```

### 3. Set Up Environment Variables

**Add to `.env.local`:**
```bash
# Your Supabase project details
SUPABASE_PROJECT_ID=your_project_id_here
SUPABASE_ACCESS_TOKEN=your_access_token_here
```

### 4. Create Type Generation Script

**File: `scripts/generate-types.js`**
```javascript
#!/usr/bin/env node
const { execSync } = require('child_process');
const path = require('path');

const projectId = process.env.SUPABASE_PROJECT_ID;
const outputPath = path.join(__dirname, '../src/types/database.ts');

if (!projectId) {
  console.error('Error: SUPABASE_PROJECT_ID environment variable not set');
  process.exit(1);
}

try {
  console.log('🔄 Generating database types...');
  
  execSync(
    `npx supabase gen types typescript --project-id ${projectId} > ${outputPath}`,
    { stdio: 'inherit' }
  );
  
  console.log('✅ Database types generated successfully!');
  console.log(`📁 Output: ${outputPath}`);
} catch (error) {
  console.error('❌ Error generating types:', error.message);
  process.exit(1);
}
```

**Make script executable:**
```bash
chmod +x scripts/generate-types.js
```

**Add to `package.json`:**
```json
{
  "scripts": {
    "types:generate": "node scripts/generate-types.js",
    "types:watch": "chokidar \"supabase/migrations/*.sql\" -c \"npm run types:generate\""
  }
}
```

### 5. Update Supabase Client with Types

**File: `src/lib/supabase.ts`**
```typescript
import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/database'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase environment variables')
}

// Client with full type safety
export const supabase = createClient<Database>(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  }
})

// Export types for use throughout the app
export type { Database }
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type InsertTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type UpdateTables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']

// Specific table types for common use
export type Profile = Tables<'profiles'>
export type Team = Tables<'teams'>
export type Event = Tables<'events'>
export type Evaluation = Tables<'evaluations'>
```

### 6. Update Service Classes with Types

**Example: `src/services/TeamService.ts`**
```typescript
import { supabase, type Tables, type InsertTables, type UpdateTables } from '@/lib/supabase'

type Team = Tables<'teams'>
type TeamInsert = InsertTables<'teams'>
type TeamUpdate = UpdateTables<'teams'>

export class TeamService {
  static async getTeams(): Promise<Team[]> {
    const { data, error } = await supabase
      .from('teams')
      .select('*')
    
    if (error) throw error
    return data
  }

  static async createTeam(team: TeamInsert): Promise<Team> {
    const { data, error } = await supabase
      .from('teams')
      .insert(team)
      .select()
      .single()
    
    if (error) throw error
    return data
  }

  static async updateTeam(id: string, updates: TeamUpdate): Promise<Team> {
    const { data, error } = await supabase
      .from('teams')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw error
    return data
  }
}
```

### 7. Set Up Automated Type Updates

**File: `.github/workflows/update-types.yml`**
```yaml
name: Update Database Types

on:
  workflow_dispatch:
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  update-types:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Generate types
        env:
          SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID }}
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
        run: npm run types:generate
        
      - name: Check for changes
        id: verify-changed-files
        run: |
          if [ -n "$(git status --porcelain)" ]; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi
          
      - name: Commit changes
        if: steps.verify-changed-files.outputs.changed == 'true'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add src/types/database.ts
          git commit -m "chore: update database types"
          git push
```

## Implementation Steps

### Step 1: Generate Initial Types
```bash
# Install Supabase CLI if not already installed
npm install -g supabase

# Get your project ID from Supabase dashboard
# Generate types
npx supabase gen types typescript --project-id YOUR_PROJECT_ID > src/types/database.ts
```

### Step 2: Update Client Configuration
1. Import generated types in `src/lib/supabase.ts`
2. Add type parameter to `createClient<Database>()`
3. Export useful type helpers

### Step 3: Update Service Layer
For each service file:
1. Import appropriate types
2. Add type annotations to methods
3. Update return types and parameters

### Step 4: Update Components with Types
For components using database operations:
1. Add proper type annotations
2. Use type-safe query building
3. Update state management with correct types

## Verification Steps

### 1. Type Safety Verification
```bash
# Check for TypeScript errors
npm run type-check

# Should show errors for any type mismatches
```

### 2. IDE Autocomplete Test
Open any file with database operations:
- Verify autocomplete works for table names
- Verify autocomplete works for column names
- Verify type inference for query results

### 3. Build Verification
```bash
# Ensure build works with generated types
npm run build
```

### 4. Development Experience Test
```typescript
// This should show autocomplete and type errors
const { data } = await supabase
  .from('teams') // Should autocomplete table names
  .select('id, name, created_at') // Should autocomplete column names
  .eq('invalid_column', 'value') // Should show TypeScript error
```

## Files to Update with Types

### High Priority
- `src/lib/supabase.ts` - Client configuration
- All files in `src/services/` - Service layer
- `src/hooks/useProfile.ts` - User profile hooks
- `src/contexts/UserContext.tsx` - User context

### Medium Priority
- Components with direct database calls
- Custom hooks using Supabase
- Page components with data fetching

### Low Priority
- Test files (can use generated types for better testing)
- Utility functions that work with database data

## Success Criteria

- [ ] Database types successfully generated in `src/types/database.ts`
- [ ] Supabase client configured with type parameter
- [ ] Type helper exports available (Tables, InsertTables, etc.)
- [ ] Service layer methods have proper type annotations
- [ ] IDE autocomplete works for database operations
- [ ] TypeScript errors appear for invalid queries
- [ ] Build succeeds with full type checking
- [ ] No runtime errors from type mismatches
- [ ] Script for regenerating types when schema changes

## Maintenance Strategy

### Regular Type Updates
```bash
# Weekly type regeneration
npm run types:generate

# Or set up automated updates via GitHub Actions
```

### Schema Change Workflow
1. Update database schema in Supabase
2. Run type generation script
3. Fix any TypeScript errors
4. Update affected service methods
5. Test functionality

## Performance Impact

**Positive Impact:**
- ✅ Compile-time error detection (prevents runtime issues)
- ✅ Better IDE performance with autocomplete
- ✅ Reduced debugging time from type errors

**No Negative Impact:**
- Generated types are compile-time only
- No runtime performance cost
- Minimal bundle size impact

## Next Steps After Completion

1. Move to Task 005: Remove TypeScript Bypasses
2. Update all service classes to use generated types
3. Set up automated type regeneration pipeline
4. Train team on using generated types effectively

## Common Issues & Solutions

### Issue 1: Types Not Generated
```bash
# Check Supabase CLI installation
supabase --version

# Check project ID and permissions
npx supabase projects list
```

### Issue 2: Type Import Errors
```typescript
// Ensure proper import paths
import type { Database } from '@/types/database'

// Check tsconfig.json path mapping
```

### Issue 3: Outdated Types
```bash
# Regenerate types after schema changes
npm run types:generate

# Check migration status
npx supabase db remote changes
```