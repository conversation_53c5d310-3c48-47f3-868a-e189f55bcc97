# Task: Remove Hardcoded Credentials

**Priority**: 🚨 CRITICAL - Security Vulnerability  
**Phase**: 2.1 - Security Fixes  
**Estimated Time**: 2-3 hours  
**Dependencies**: None  

## Problem Description

Hardcoded Supabase URLs and API keys are exposed in the codebase, particularly in:
- `src/pages/section/Coach/services/FormSubmissionModule.ts` (line 149 per guide)
- Potentially other files throughout the codebase

**Security Impact**: 
- ❌ API keys exposed in version control
- ❌ Production credentials visible to anyone with code access
- ❌ No separation between development and production environments

## Current State Analysis Required

### Step 1: Scan for Hardcoded Credentials
```bash
# Search for hardcoded Supabase URLs
rg -i "https://.*\.supabase\.co" --type ts --type tsx

# Search for hardcoded API keys  
rg -i "eyJ.*" --type ts --type tsx

# Search for specific patterns
rg -i "supabase.*key" --type ts --type tsx
rg -i "anon.*key" --type ts --type tsx
```

### Step 2: Audit Environment Configuration
```bash
# Check existing environment files
ls -la | grep env
cat .env* 2>/dev/null || echo "No .env files found"
```

## Solution Steps

### 1. Create Environment Configuration
```bash
# Create environment files
touch .env.local
touch .env.production
touch .env.example
```

### 2. Move Credentials to Environment Variables
**`.env.local` (for development):**
```bash
VITE_SUPABASE_URL=http://localhost:54321
VITE_SUPABASE_ANON_KEY=your_local_anon_key
```

**`.env.production` (for production):**
```bash
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_production_anon_key
```

**`.env.example` (template for developers):**
```bash
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_anon_key_here
```

### 3. Update .gitignore
Ensure these patterns exist in `.gitignore`:
```
.env.local
.env.production
.env*.local
```

### 4. Update Code to Use Environment Variables

**Replace hardcoded values in `supabaseClient.ts`:**
```typescript
// Before (INSECURE)
const supabaseUrl = 'https://hardcoded-project.supabase.co'
const supabaseKey = 'eyJhardcodedkey...'

// After (SECURE)
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Missing Supabase environment variables')
}
```

### 5. Update All Files with Hardcoded Credentials
For each file found in the scan:
- Replace hardcoded URLs with `import.meta.env.VITE_SUPABASE_URL`
- Replace hardcoded keys with `import.meta.env.VITE_SUPABASE_ANON_KEY`
- Add proper error handling for missing environment variables

## Verification Steps

### 1. Environment Variables Test
```bash
# Start development server and verify no hardcoded values
npm run dev
# Check browser console for environment variable errors
```

### 2. Code Scan Verification
```bash
# Verify no hardcoded credentials remain
rg -i "https://.*\.supabase\.co" --type ts --type tsx
rg -i "eyJ[A-Za-z0-9]" --type ts --type tsx

# Should return no results if properly cleaned
```

### 3. Build Test
```bash
# Verify production build works with environment variables
npm run build
```

## Files Likely to Need Updates

Based on common patterns, check these files:
- `src/supabaseClient.ts`
- `src/pages/section/Coach/services/FormSubmissionModule.ts`
- Any service files in `src/services/`
- Context files in `src/contexts/`
- Configuration files in `src/config/`

## Success Criteria

- [ ] No hardcoded Supabase URLs or API keys in codebase
- [ ] Environment variables properly configured
- [ ] `.env*` files added to `.gitignore`
- [ ] Application works in both development and production modes
- [ ] Proper error handling for missing environment variables
- [ ] Code scan returns zero hardcoded credentials

## Security Best Practices Applied

- ✅ Separation of development and production credentials
- ✅ Environment variables not committed to version control
- ✅ Proper error handling for missing configuration
- ✅ Template file (`.env.example`) for developer onboarding

## Next Steps After Completion

1. Move to Task 002: Consolidate Supabase Clients
2. Update team documentation with new environment setup
3. Audit CI/CD pipeline for secure credential handling