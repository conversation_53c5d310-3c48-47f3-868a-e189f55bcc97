import type { StorybookConfig } from '@storybook/react-vite';

const config: StorybookConfig = {
  "stories": [
    "./docs/*.mdx",
    "../src/**/*.mdx",
    "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"
  ],
  "addons": [
    "@storybook/addon-docs",
    "@storybook/addon-onboarding"
  ],
  "framework": {
    "name": "@storybook/react-vite",
    "options": {}
  },
  "core": {
    "disableTelemetry": true
  },
  "staticDirs": ['../public'],
  "previewHead": (head) => `
    ${head}
    <base href="/storybook/">
  `,
  async viteFinal(config) {
    const { mergeConfig } = await import('vite');
    
    return mergeConfig(config, {
      optimizeDeps: {
        include: ['@storybook/react', '@mdx-js/react'],
        entries: [
          '../src/**/*.stories.tsx',
          '../src/**/*.stories.ts',
          '../src/**/*.stories.jsx',
          '../src/**/*.stories.js',
        ],
      },
      server: {
        fs: {
          strict: false,
        },
      },
      resolve: {
        alias: {
          '@': '/src',
        },
      },
    });
  },
};
export default config;