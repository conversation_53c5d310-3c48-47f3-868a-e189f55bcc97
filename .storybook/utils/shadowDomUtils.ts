// ABOUTME: Utility functions for Shadow DOM components in Storybook
// Provides helpers for inspecting and documenting Shadow DOM components

/**
 * Waits for a Shadow DOM element to be fully rendered
 */
export const waitForShadowDom = (
  selector: string,
  timeout: number = 5000
): Promise<Element | null> => {
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    const checkElement = () => {
      const element = document.querySelector(selector);
      
      if (element?.shadowRoot) {
        resolve(element);
        return;
      }
      
      if (Date.now() - startTime > timeout) {
        resolve(null);
        return;
      }
      
      requestAnimationFrame(checkElement);
    };
    
    checkElement();
  });
};

/**
 * Extracts CSS from Shadow DOM for documentation
 */
export const extractShadowStyles = (element: Element): string | null => {
  if (!element.shadowRoot) return null;
  
  const styleElements = element.shadowRoot.querySelectorAll('style');
  return Array.from(styleElements)
    .map(style => style.textContent)
    .join('\n');
};

/**
 * Helper to query elements inside Shadow DOM
 */
export const queryShadowDom = (
  host: Element,
  selector: string
): Element | null => {
  return host.shadowRoot?.querySelector(selector) || null;
};

/**
 * Gets all custom elements with Shadow DOM on the page
 */
export const getAllShadowHosts = (): Element[] => {
  const allElements = document.querySelectorAll('*');
  return Array.from(allElements).filter(el => !!el.shadowRoot);
};