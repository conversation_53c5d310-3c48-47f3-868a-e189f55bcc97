// ABOUTME: Storybook decorator to support Shadow DOM components
// Provides utilities for proper Shadow DOM rendering in Storybook

import React, { useEffect, useRef } from 'react';
import type { Decorator } from '@storybook/react';

/**
 * Decorator that ensures Shadow DOM components render properly in Storybook
 * Handles style injection and Shadow DOM lifecycle
 */
export const withShadowDOMSupport: Decorator = (Story, context) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Force re-render of Shadow DOM components when story changes
    if (containerRef.current) {
      const shadowElements = containerRef.current.querySelectorAll('*');
      shadowElements.forEach((element) => {
        if (element.shadowRoot) {
          // Trigger re-render for Shadow DOM elements
          element.dispatchEvent(new Event('storybook-update'));
        }
      });
    }
  }, [context.args]);

  return (
    <div 
      ref={containerRef}
      style={{ 
        padding: '1rem',
        minHeight: '100px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
    >
      <Story />
    </div>
  );
};

/**
 * Global styles for Storybook that don't affect Shadow DOM
 */
export const globalStyles = `
  /* Reset for Storybook container */
  #storybook-root {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }

  /* Ensure Shadow DOM components are visible */
  [data-shadow-dom] {
    display: contents;
  }
  
  /* Prevent color function errors in docs */
  .docs-story,
  .docblock-argstable {
    /* Override any problematic color functions */
    color: inherit;
    background-color: transparent;
  }
`;