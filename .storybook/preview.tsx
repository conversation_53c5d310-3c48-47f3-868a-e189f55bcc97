import type { Preview } from '@storybook/react-vite'
import { withShadowDOMSupport, globalStyles } from './decorators/withShadowDOMSupport';

// Inject global styles
const styleSheet = document.createElement('style');
styleSheet.textContent = globalStyles;
document.head.appendChild(styleSheet);

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
       date: /Date$/i,
      },
    },
    docs: {
      // Removed theme to avoid potential color issues
    },
    backgrounds: {
      default: 'black',
      values: [
        { name: 'black', value: '#000000' },
        { name: 'dark', value: '#1f1f1f' },
        { name: 'white', value: '#ffffff' },
      ],
    },
    // Shadow DOM specific configuration
    shadowDom: {
      enabled: true,
    },
  },
  decorators: [withShadowDOMSupport],
  globalTypes: {
    // Add debug mode for role-based styling
    debugRole: {
      name: 'Debug Role',
      description: 'Global debug role for Shadow DOM components',
      defaultValue: 'none',
      toolbar: {
        icon: 'user',
        items: [
          { value: 'none', title: 'No Role' },
          { value: 'coach', title: 'Coach' },
          { value: 'player', title: 'Player' },
          { value: 'parent', title: 'Parent' },
          { value: 'admin', title: 'Admin' },
          { value: 'superadmin', title: 'Super Admin' },
        ],
        showName: true,
      },
    },
  },
};

export default preview;