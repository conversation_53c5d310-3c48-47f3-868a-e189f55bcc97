import { Meta } from '@storybook/addon-docs/blocks';

<Meta title="SHOT Design System/Shadow DOM Guide" />

# Shadow DOM Components in Storybook

This guide explains how to work with Shadow DOM components in our Storybook setup.

## Overview

Shadow DOM components in the SHOT Design System use Web Components with Shadow DOM for style encapsulation. This ensures that component styles don't leak out and external styles don't leak in.

## Key Features

### 1. Automatic Shadow DOM Support
All stories are wrapped with the `withShadowDOMSupport` decorator that:
- Ensures proper rendering of Shadow DOM components
- Handles Shadow DOM lifecycle events
- Provides consistent spacing and layout

### 2. Debug Role Toolbar
Use the debug role selector in the Storybook toolbar to test role-based styling:
- Coach (Blue theme)
- Player (Green theme)
- Parent (Purple theme)
- Admin (Orange theme)
- Super Admin (Red theme)

### 3. Shadow DOM Utilities
Import utilities for working with Shadow DOM:

```typescript
import { 
  waitForShadowDom, 
  extractShadowStyles, 
  queryShadowDom 
} from '../utils/shadowDomUtils';
```

## Creating Stories for Shadow DOM Components

### Basic Story Structure

```typescript
import type { Meta, StoryObj } from '@storybook/react';
import { ShadowComponent } from './ShadowComponent';

const meta: Meta<typeof ShadowComponent> = {
  title: 'Shadow DOM/Category/ComponentName',
  component: ShadowComponent,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Description of your Shadow DOM component',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    // Your component props
  },
};
```

### Testing Shadow DOM Rendering

To test if Shadow DOM is rendering correctly:

1. Open your story in Storybook
2. Open browser DevTools
3. Inspect the component element
4. Look for `#shadow-root (open)` in the DOM tree

### Common Patterns

1. **Multiple Variants**: Show different visual states
2. **Size Variations**: Display all size options
3. **Interactive States**: Hover, focus, active states
4. **Role-Based Styling**: Show debug mode variations

## Troubleshooting

### Component Not Rendering
- Check if the component is properly exported
- Verify Shadow DOM is being created in the component
- Check browser console for errors

### Styles Not Applying
- Shadow DOM styles are isolated - external CSS won't affect them
- Check if styles are properly defined in the component template
- Use browser DevTools to inspect Shadow DOM styles

### Event Handling
- Events from Shadow DOM components bubble up normally
- Use standard React event handlers
- Check if custom events are properly dispatched

## Best Practices

1. **Component Organization**: Group Shadow DOM components under "Shadow DOM" category
2. **Documentation**: Use JSDoc comments for auto-generated documentation
3. **Visual Testing**: Create stories for all visual states
4. **Accessibility**: Test with keyboard navigation and screen readers
5. **Performance**: Shadow DOM components are performant by design