import { Meta } from '@storybook/addon-docs/blocks';

<Meta title="SHOT Design System/Introduction" />

# SHOT Design System

Welcome to the SHOT Design System built with Shadow DOM components and documented in Storybook.

## 🚀 Getting Started

This design system provides a comprehensive set of components for building consistent user interfaces in the SHOT application.

### For Developers

👉 **[Quick Start Guide](/?path=/docs/shot-design-system-quick-start--docs)** - Copy & paste working code from stories

👉 **[Implementation Guide](/?path=/docs/shot-design-system-implementation-guide--docs)** - Complete examples and patterns

👉 **[Shadow DOM Guide](/?path=/docs/shot-design-system-shadow-dom-guide--docs)** - Technical details about Shadow DOM components

### For Designers

👉 Browse the component library in the sidebar to see all available components and their variants

## 📚 What's in This Storybook

### Components by Category

**🔸 Atoms** - Basic building blocks
- [ShadowButton](/?path=/docs/shadow-dom-atoms-shadowbutton--docs) - Buttons with multiple variants
- [ShadowTextInput](/?path=/docs/shadow-form-shadowtextinput--docs) - Form inputs with validation
- [ShadowStatusBadge](/?path=/docs/shadow-dom-atoms-shadowstatusbadge--docs) - Status indicators

**🔸 Molecules** - Component combinations
- [ShadowEventCard](/?path=/docs/shadow-dom-molecules-shadoweventcard--docs) - Event display cards
- [ShadowInfoCard](/?path=/docs/shadow-dom-molecules-shadowinfocard--docs) - Information cards
- [ShadowStatCard](/?path=/docs/shadow-dom-molecules-shadowstatcard--docs) - Statistics display

**🔸 Forms** - Form components
- [ShadowFormGroup](/?path=/docs/shadow-form-shadowformgroup--docs) - Form grouping
- [ShadowSelect](/?path=/docs/shadow-form-shadowselect--docs) - Dropdown selectors
- [ShadowTextarea](/?path=/docs/shadow-form-shadowtextarea--docs) - Multi-line text input

## 🎯 How to Use This Documentation

### 1. Find Your Component
Browse the sidebar to find the component you need.

### 2. Copy the Code
Each story shows working examples you can copy directly into your code.

### 3. Adapt to Your Needs
Replace mock data with your real data and console.log with your actual handlers.

## 💡 Key Features

### Shadow DOM Encapsulation
All components use Shadow DOM for perfect style isolation. No CSS conflicts!

### Role-Based Styling
Components support different user roles (coach, player, parent, admin) with visual debugging.

### Event Handling
Components emit custom events that work seamlessly with React.

### TypeScript Support
Full TypeScript support with proper type definitions.

## 🔧 Development Workflow

1. **Explore** - Browse components in Storybook
2. **Test** - Use the interactive controls to test different configurations
3. **Copy** - Copy working code from stories
4. **Integrate** - Paste into your application and adapt
5. **Iterate** - Come back to Storybook to refine your implementation

## 📖 Common Patterns

### Basic Button Usage
```typescript
import { ShadowButton } from '@/components/shadow/ShadowButton';

<ShadowButton
  text="Click Me"
  variant="primary"
  onClick={handleClick}
/>
```

### Form Input with Validation
```typescript
import { ShadowTextInput } from '@/components/shadow/form/ShadowTextInput';

<ShadowTextInput
  label="Email"
  type="email"
  value={email}
  onChange={(e) => setEmail(e.detail.value)}
  error={emailError}
  required
/>
```

### Event Card Lists
```typescript
import { ShadowEventCard } from '@/components/shadow/ShadowEventCard';

{events.map(event => (
  <ShadowEventCard
    key={event.id}
    title={event.title}
    date={event.date}
    time={event.time}
    type={event.type}
    onClick={() => navigate(`/events/${event.id}`)}
  />
))}
```

## 🤝 Team Usage

### For Frontend Developers
- Use the **Interactive** stories to test component behavior
- Copy code patterns from complex stories like `LoginForm` and `ContactForm`
- Check the **argTypes** to understand all available props

### For Backend Developers
- Focus on the data structures shown in stories
- Use the API patterns shown in the Implementation Guide
- Check event card examples to understand expected data formats

### For QA/Testing
- Use stories to understand component states and behaviors
- Test edge cases shown in the **ValidationExample** stories
- Verify role-based styling with the debug toolbar

## 📞 Getting Help

- **Component Props**: Check the Controls panel for each story
- **Event Handling**: Look at Interactive stories for working examples
- **Integration**: Use the Implementation Guide for complete patterns
- **Technical Details**: Check the Shadow DOM Guide for troubleshooting

---

**Pro Tip**: Every story in this Storybook is a working example. When you find a component that looks like what you need, copy the entire pattern from the story and adapt it to your use case!

Ready to start building? Check out the [Quick Start Guide](/?path=/docs/shot-design-system-quick-start--docs) 🚀
