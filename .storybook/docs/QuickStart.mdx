import { Meta } from '@storybook/addon-docs/blocks';

<Meta title="SHOT Design System/Quick Start" />

# Quick Start: Co<PERSON> & Paste from Stories

Use this guide to quickly extract working code from your Storybook stories.

## Step 1: Find the Story You Want

Look through your Storybook to find the component example that matches what you need.

## Step 2: Copy the Component Usage

### From Simple Stories (args-based)

**Story:**
```typescript
export const Primary: Story = {
  args: {
    text: 'Primary Button',
    variant: 'primary',
    size: 'medium',
    onClick: () => console.log('primary-click'),
  },
};
```

**Copy this pattern:**
```typescript
<ShadowButton
  text="Your Button Text"
  variant="primary"
  size="medium"
  onClick={yourClickHandler}
/>
```

### From Complex Stories (render-based)

**Story:**
```typescript
export const ContactForm: Story = {
  render: () => {
    const [formData, setFormData] = React.useState({
      name: '',
      email: '',
      phone: '',
    });
    
    const handleChange = (field: string) => (e: any) => {
      setFormData(prev => ({
        ...prev,
        [field]: e.detail.value,
      }));
    };
    
    return (
      <div>
        <ShadowTextInput
          label="Name"
          name="name"
          value={formData.name}
          onChange={handleChange('name')}
          required
        />
        <ShadowTextInput
          label="Email"
          type="email"
          name="email"
          value={formData.email}
          onChange={handleChange('email')}
          required
        />
      </div>
    );
  },
};
```

**Copy this entire pattern:**
```typescript
// 1. Copy the state setup
const [formData, setFormData] = useState({
  name: '',
  email: '',
  phone: '',
});

// 2. Copy the event handler
const handleChange = (field: string) => (e: any) => {
  setFormData(prev => ({
    ...prev,
    [field]: e.detail.value,
  }));
};

// 3. Copy the JSX
return (
  <div>
    <ShadowTextInput
      label="Name"
      name="name"
      value={formData.name}
      onChange={handleChange('name')}
      required
    />
    <ShadowTextInput
      label="Email"
      type="email"
      name="email"
      value={formData.email}
      onChange={handleChange('email')}
      required
    />
  </div>
);
```

## Step 3: Adapt to Your Needs

### Replace Mock Data with Real Data

**Story has static data:**
```typescript
<ShadowEventCard 
  title="Monday Practice"
  date="Mon, Mar 18"
  time="4:00 PM"
  type="training"
/>
```

**Replace with dynamic data:**
```typescript
<ShadowEventCard 
  title={event.title}
  date={event.date}
  time={event.time}
  type={event.type}
/>
```

### Replace Console.log with Real Handlers

**Story has:**
```typescript
onClick={() => console.log('clicked')}
```

**Replace with:**
```typescript
onClick={handleSubmit}
onClick={() => navigate('/somewhere')}
onClick={() => setModalOpen(true)}
```

## Common Patterns from Your Stories

### 1. Button Click Handlers
```typescript
// From stories: onClick: () => console.log('clicked')
// In your code:
const handleClick = () => {
  // Your logic here
};

<ShadowButton
  text="Click Me"
  variant="primary"
  onClick={handleClick}
/>
```

### 2. Form Input Handlers
```typescript
// From stories: onChange={(e) => setFormData(prev => ({ ...prev, field: e.detail.value }))}
// In your code:
const [inputValue, setInputValue] = useState('');

<ShadowTextInput
  label="Your Label"
  name="yourField"
  value={inputValue}
  onChange={(e) => setInputValue(e.detail.value)}
/>
```

### 3. Lists of Components
```typescript
// From stories: Static list
// In your code:
const items = [/* your data */];

return (
  <div>
    {items.map(item => (
      <ShadowEventCard
        key={item.id}
        title={item.title}
        date={item.date}
        onClick={() => handleItemClick(item.id)}
      />
    ))}
  </div>
);
```

## Your Specific Components

### ShadowButton
Copy any of these patterns:
```typescript
// Basic button
<ShadowButton text="Click Me" variant="primary" onClick={handleClick} />

// Submit button
<ShadowButton text="Submit" type="submit" variant="primary" size="large" />

// Disabled button
<ShadowButton text="Loading..." variant="primary" disabled={isLoading} />

// Full width button
<ShadowButton text="Continue" variant="primary" fullWidth />
```

### ShadowTextInput
Copy any of these patterns:
```typescript
// Basic text input
<ShadowTextInput
  label="Name"
  name="name"
  value={name}
  onChange={(e) => setName(e.detail.value)}
/>

// Email input with validation
<ShadowTextInput
  label="Email"
  type="email"
  name="email"
  value={email}
  onChange={(e) => setEmail(e.detail.value)}
  error={emailError}
  required
/>

// Password input
<ShadowTextInput
  label="Password"
  type="password"
  name="password"
  value={password}
  onChange={(e) => setPassword(e.detail.value)}
  showPasswordToggle
/>
```

### ShadowEventCard
Copy any of these patterns:
```typescript
// Basic event card
<ShadowEventCard
  title={event.title}
  date={event.date}
  time={event.time}
  type={event.type}
/>

// Clickable event card
<ShadowEventCard
  title={event.title}
  date={event.date}
  time={event.time}
  type={event.type}
  onClick={() => navigate(`/events/${event.id}`)}
/>

// Event card with status
<ShadowEventCard
  title={event.title}
  date={event.date}
  time={event.time}
  type={event.type}
  status={event.status}
/>
```

## Pro Tips

1. **Start with the closest story**: Find the story that most closely matches your use case
2. **Copy the entire render function**: Don't just copy the component - copy the state and handlers too
3. **Check argTypes**: Look at the story's argTypes to see all available props
4. **Use interactive stories**: The interactive stories show real event handling patterns
5. **Check complex stories**: Stories like `LoginForm` and `ContactForm` show complete integration patterns

## Need More Help?

- Look at the **Interactive** stories for working event handling examples
- Check the **ValidationExample** story for form validation patterns
- Use the **AllVariants** stories to see all the options available
- Look at **CalendarView** and similar stories for list/grid layouts

Remember: Every story in your Storybook is a working example you can copy and adapt!
