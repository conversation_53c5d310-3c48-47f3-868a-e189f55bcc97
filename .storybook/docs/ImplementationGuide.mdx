import { Meta } from '@storybook/addon-docs/blocks';

<Meta title="SHOT Design System/Implementation Guide" />

# Implementation Guide: From Stories to Code

This guide shows how to use components from your Storybook in actual application code.

## How Stories Map to Real Implementation

### 1. Basic Component Usage

**Story Definition:**
```typescript
// ShadowButton.stories.tsx
export const Primary: Story = {
  args: {
    text: 'Primary Button',
    variant: 'primary',
    size: 'medium',
    onClick: () => console.log('primary-click'),
  },
};
```

**Real Implementation:**
```typescript
// In your React component
import { ShadowButton } from '@/components/shadow/ShadowButton';

function MyPage() {
  const handleSubmit = () => {
    // Your submit logic here
    console.log('Form submitted');
  };

  return (
    <div>
      <ShadowButton
        text="Submit Form"
        variant="primary"
        size="medium"
        onClick={handleSubmit}
      />
    </div>
  );
}
```

### 2. Form Components

**Story Definition:**
```typescript
// ShadowTextInput.stories.tsx
export const LoginForm: Story = {
  render: () => {
    const [formData, setFormData] = useState({
      username: '',
      password: '',
    });
    
    return (
      <div>
        <ShadowTextInput
          label="Username"
          name="username"
          value={formData.username}
          onChange={(e) => setFormData(prev => ({ ...prev, username: e.detail.value }))}
        />
        <ShadowTextInput
          label="Password"
          type="password"
          name="password"
          value={formData.password}
          onChange={(e) => setFormData(prev => ({ ...prev, password: e.detail.value }))}
        />
      </div>
    );
  },
};
```

**Real Implementation:**
```typescript
// LoginPage.tsx
import { useState } from 'react';
import { ShadowTextInput } from '@/components/shadow/form/ShadowTextInput';
import { ShadowButton } from '@/components/shadow/ShadowButton';

function LoginPage() {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  
  const [errors, setErrors] = useState({
    username: '',
    password: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    const newErrors = {
      username: !formData.username ? 'Username is required' : '',
      password: !formData.password ? 'Password is required' : '',
    };
    
    setErrors(newErrors);
    
    if (!newErrors.username && !newErrors.password) {
      try {
        // Your authentication logic
        await authenticate(formData.username, formData.password);
        // Redirect or update state
      } catch (error) {
        // Handle error
      }
    }
  };

  return (
    <form onSubmit={handleSubmit} className="login-form">
      <ShadowTextInput
        label="Username"
        name="username"
        value={formData.username}
        placeholder="Enter username"
        onChange={(e) => setFormData(prev => ({ ...prev, username: e.detail.value }))}
        error={errors.username}
        required
      />
      
      <ShadowTextInput
        label="Password"
        type="password"
        name="password"
        value={formData.password}
        placeholder="Enter password"
        onChange={(e) => setFormData(prev => ({ ...prev, password: e.detail.value }))}
        error={errors.password}
        showPasswordToggle
        required
      />
      
      <ShadowButton
        text="Sign In"
        type="submit"
        variant="primary"
        size="large"
        fullWidth
      />
    </form>
  );
}
```

### 3. Event Cards in Lists

**Story Definition:**
```typescript
// ShadowEventCard.stories.tsx
export const CalendarView: Story = {
  render: () => (
    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))', gap: '1rem' }}>
      <ShadowEventCard 
        title="Monday Practice"
        date="Mon, Mar 18"
        time="4:00 PM"
        type="training"
      />
      <ShadowEventCard 
        title="League Game"
        date="Sat, Mar 23"
        time="2:00 PM"
        type="match"
        status="upcoming"
      />
    </div>
  ),
};
```

**Real Implementation:**
```typescript
// EventsPage.tsx
import { useEffect, useState } from 'react';
import { ShadowEventCard } from '@/components/shadow/ShadowEventCard';

interface Event {
  id: string;
  title: string;
  date: string;
  time: string;
  type: 'training' | 'match' | 'session';
  status: 'upcoming' | 'completed' | 'cancelled';
}

function EventsPage() {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch events from your API
    const fetchEvents = async () => {
      try {
        const response = await fetch('/api/events');
        const data = await response.json();
        setEvents(data);
      } catch (error) {
        console.error('Failed to fetch events:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, []);

  const handleEventClick = (eventId: string) => {
    // Navigate to event detail page
    navigate(`/events/${eventId}`);
  };

  if (loading) {
    return <div>Loading events...</div>;
  }

  return (
    <div className="events-page">
      <h1>Upcoming Events</h1>
      
      <div className="events-grid">
        {events.map((event) => (
          <ShadowEventCard
            key={event.id}
            title={event.title}
            date={event.date}
            time={event.time}
            type={event.type}
            status={event.status}
            onClick={() => handleEventClick(event.id)}
          />
        ))}
      </div>
    </div>
  );
}
```

## Common Patterns

### 1. Using Component Props from Stories

Every story's `args` property shows exactly what props you can pass to the component:

```typescript
// From story args:
export const Primary: Story = {
  args: {
    text: 'Primary Button',
    variant: 'primary',
    size: 'medium',
    disabled: false,
    fullWidth: false,
    flash: false,
    onClick: () => console.log('clicked'),
  },
};

// In your code:
<ShadowButton
  text="Save Changes"
  variant="primary"
  size="medium"
  disabled={isSubmitting}
  fullWidth={true}
  flash={hasUrgentUpdate}
  onClick={handleSave}
/>
```

### 2. Event Handling

Shadow DOM components use custom events. Here's how to handle them:

```typescript
// Text Input Events
<ShadowTextInput
  onChange={(e) => {
    // e.detail.value contains the input value
    setInputValue(e.detail.value);
  }}
  onFocus={(e) => {
    // Handle focus
    console.log('Input focused');
  }}
  onBlur={(e) => {
    // Handle blur
    validateInput(e.detail.value);
  }}
/>

// Button Events
<ShadowButton
  onClick={(e) => {
    // Standard click event
    handleButtonClick();
  }}
/>
```

### 3. State Management

```typescript
// Managing form state
const [formData, setFormData] = useState({
  name: '',
  email: '',
  phone: '',
});

const [errors, setErrors] = useState({
  name: '',
  email: '',
  phone: '',
});

// Update individual field
const handleFieldChange = (field: keyof typeof formData) => (e: CustomEvent) => {
  setFormData(prev => ({
    ...prev,
    [field]: e.detail.value
  }));
  
  // Clear error when user starts typing
  if (errors[field]) {
    setErrors(prev => ({
      ...prev,
      [field]: ''
    }));
  }
};

// Use in JSX
<ShadowTextInput
  label="Name"
  name="name"
  value={formData.name}
  onChange={handleFieldChange('name')}
  error={errors.name}
/>
```

## Component Integration Examples

### 1. Dashboard with Multiple Components

```typescript
// Dashboard.tsx
import { ShadowButton } from '@/components/shadow/ShadowButton';
import { ShadowEventCard } from '@/components/shadow/ShadowEventCard';
import { ShadowStatCard } from '@/components/shadow/ShadowStatCard';

function Dashboard() {
  return (
    <div className="dashboard">
      {/* Header Actions */}
      <div className="dashboard-header">
        <h1>Team Dashboard</h1>
        <div className="actions">
          <ShadowButton
            text="Add Event"
            variant="primary"
            size="medium"
            onClick={() => navigate('/events/new')}
          />
          <ShadowButton
            text="Settings"
            variant="outline"
            size="medium"
            onClick={() => navigate('/settings')}
          />
        </div>
      </div>

      {/* Stats Grid */}
      <div className="stats-grid">
        <ShadowStatCard
          title="Total Players"
          value="24"
          change="+2"
          variant="positive"
        />
        <ShadowStatCard
          title="This Week's Games"
          value="3"
          change="0"
          variant="neutral"
        />
      </div>

      {/* Upcoming Events */}
      <div className="upcoming-events">
        <h2>Upcoming Events</h2>
        <div className="events-list">
          {upcomingEvents.map((event) => (
            <ShadowEventCard
              key={event.id}
              title={event.title}
              date={event.date}
              time={event.time}
              type={event.type}
              onClick={() => navigate(`/events/${event.id}`)}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
```

### 2. Form with Validation

```typescript
// ContactForm.tsx
import { useState } from 'react';
import { ShadowTextInput } from '@/components/shadow/form/ShadowTextInput';
import { ShadowTextarea } from '@/components/shadow/form/ShadowTextarea';
import { ShadowButton } from '@/components/shadow/ShadowButton';

function ContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
  });
  
  const [errors, setErrors] = useState({});
  const [submitting, setSubmitting] = useState(false);

  const validate = () => {
    const newErrors = {};
    
    if (!formData.name) newErrors.name = 'Name is required';
    if (!formData.email) newErrors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    if (!formData.message) newErrors.message = 'Message is required';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validate()) return;
    
    setSubmitting(true);
    try {
      await submitContactForm(formData);
      // Success handling
      resetForm();
    } catch (error) {
      // Error handling
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="contact-form">
      <ShadowTextInput
        label="Name"
        name="name"
        value={formData.name}
        onChange={(e) => setFormData(prev => ({ ...prev, name: e.detail.value }))}
        error={errors.name}
        required
      />
      
      <ShadowTextInput
        label="Email"
        type="email"
        name="email"
        value={formData.email}
        onChange={(e) => setFormData(prev => ({ ...prev, email: e.detail.value }))}
        error={errors.email}
        required
      />
      
      <ShadowTextInput
        label="Subject"
        name="subject"
        value={formData.subject}
        onChange={(e) => setFormData(prev => ({ ...prev, subject: e.detail.value }))}
      />
      
      <ShadowTextarea
        label="Message"
        name="message"
        value={formData.message}
        onChange={(e) => setFormData(prev => ({ ...prev, message: e.detail.value }))}
        error={errors.message}
        required
      />
      
      <ShadowButton
        text={submitting ? 'Sending...' : 'Send Message'}
        type="submit"
        variant="primary"
        size="large"
        disabled={submitting}
        fullWidth
      />
    </form>
  );
}
```

## Best Practices

### 1. Use TypeScript for Better Development Experience

```typescript
// Define your own interfaces that match the component props
interface ButtonProps {
  text: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'green' | 'purple' | 'gold';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  onClick?: () => void;
}

// Use the interface in your components
function MyComponent({ onSave }: { onSave: () => void }) {
  return (
    <ShadowButton
      text="Save"
      variant="primary"
      size="medium"
      onClick={onSave}
    />
  );
}
```

### 2. Create Wrapper Components for Complex Logic

```typescript
// SaveButton.tsx - Wrapper component
import { ShadowButton } from '@/components/shadow/ShadowButton';

interface SaveButtonProps {
  onSave: () => Promise<void>;
  disabled?: boolean;
  text?: string;
}

export function SaveButton({ onSave, disabled, text = 'Save' }: SaveButtonProps) {
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    setSaving(true);
    try {
      await onSave();
    } finally {
      setSaving(false);
    }
  };

  return (
    <ShadowButton
      text={saving ? 'Saving...' : text}
      variant="primary"
      size="medium"
      onClick={handleSave}
      disabled={disabled || saving}
    />
  );
}
```

### 3. Use Stories for Testing Props

Before implementing a component, check its stories to understand:
- What props are available
- What the expected behavior is
- What events it emits
- What variants/states it supports

## Getting Help

- **Component Props**: Check the `argTypes` section in each story file
- **Event Handling**: Look at the interactive stories for examples
- **Styling**: Shadow DOM components are self-contained
- **Integration**: Use the pattern examples above as templates

Remember: Every story shows a working example you can copy and adapt!
