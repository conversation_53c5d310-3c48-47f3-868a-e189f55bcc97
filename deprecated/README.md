# Deprecated Files

This folder contains TypeScript and CSS files that were identified as unused in the codebase as of 2025-08-15.

## Analysis Summary

- **Total files moved**: 39
- **Analysis method**: Automated import/reference checking across the entire codebase
- **Confidence level**: High - these files had no detected imports or references

## Files Moved by Category

### Debug/Development Components (17 files)
Components used for debugging and development that are no longer referenced:
- `components/ComponentDebugWrapper.tsx`
- `components/GlobalDebugProvider.tsx`
- `components/RegistrationDebugger.tsx`
- `components/ScrollDebug.tsx`
- `components/ScrollDebugger.tsx`
- `components/debug/DebugPreEvaluationData.tsx`
- `components/debug/TestLastTeamPersistence.tsx`
- `components/domains/DomainMockupPagesVite.tsx`
- `components/domains/DomainMockupViewer.tsx`
- `components/domains/DomainsIndexSimple.tsx`
- `components/domains/DomainsTestBasic.tsx`
- `components/domains/DomainTest.tsx`
- `components/shadow/ExampleIntegration.tsx`
- `components/shadow/PreEvaluationProgressExample.tsx`
- `components/v2/DebugModeExample.tsx`
- `components/v2/NotificationModalExamples.tsx`
- `components/v2/RoleSpecificExamples.tsx`

### Unused Business Components (5 files)
Components that were developed but no longer used:
- `components/AddonSelector.tsx`
- `components/BigCommerceProductList.tsx`
- `components/CurrentPlanCard.tsx`
- `components/ExploreContainer.tsx`
- `components/MembershipSlider.tsx`

### Deprecated Routes (5 files)
Route configuration files from earlier versions:
- `routes/AppRoutesStage1.tsx`
- `routes/DeprecatedRoutes.tsx`
- `routes/player-evaluation-routes.tsx`
- `routes/Stage1Routes.tsx`
- `routes/V2RoutesTest.tsx`

### Unused Services (5 files)
Service layer files with no references:
- `services/ImageService.ts`
- `services/PlayerManagementService.ts`
- `services/PreEvaluationService.ts`
- `services/TeamCoachService.ts`
- `services/TeamStatisticsService.ts`

### Unused CSS Files (7 files)
Style files for components that are no longer used:
- `styles/HeaderFix.css`
- `styles/overrides.css`
- `styles/TeamCoaches.css`
- `pages/Home.css`
- `pages/Interstitial.css`
- `pages/TestBigCommerce.css`
- `pages/ViewIDP.css`

## Restoration Instructions

If any of these files are needed again:

1. **Check the git history** to see the last commit where the file was used
2. **Copy the file back** to its original location in the src/ directory
3. **Update imports** in other files that might reference it
4. **Remove it from this deprecated folder**

## Notes

- All files were verified to have no import statements referencing them
- Dynamic imports and string-based references were also checked
- Test files, stories, and configuration files were excluded from the analysis
- Files like `index.css`, `global.css`, `App.tsx`, `main.tsx` were preserved as they are standard entry points

## Safe to Delete

These files can be safely deleted if you're confident they won't be needed in the future. Consider keeping them for a few months to ensure no functionality is broken.