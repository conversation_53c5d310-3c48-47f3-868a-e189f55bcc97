// ABOUTME: Deprecated routes - Pages that are no longer actively used but kept for reference
// These routes will be removed after confirming they're not needed

import React from 'react';
import { Route, Switch } from 'react-router-dom';

// Import all deprecated pages that were found to have no active routes
// These imports will fail initially - we'll comment out missing ones

// Authentication variants (keeping UpdatedLoginV2 as primary)
const Login = React.lazy(() => import('../pages/Login'));
const UpdatedLogin = React.lazy(() => import('../pages/UpdatedLogin'));
const SimpleLogin = React.lazy(() => import('../pages/SimpleLogin'));
const NonIonicLogin = React.lazy(() => import('../pages/NonIonicLogin'));

// Account/Profile pages (some may be replaced by unified versions)
const Account = React.lazy(() => import('../pages/Account'));
const Profile = React.lazy(() => import('../pages/Profile'));
const Dashboard = React.lazy(() => import('../pages/Dashboard'));

// Family management (being consolidated into Identity area)
const Family = React.lazy(() => import('../pages/Family'));
const EditFamily = React.lazy(() => import('../pages/EditFamily'));
const AddFamily = React.lazy(() => import('../pages/AddFamily'));

// Registration variants
const Registration = React.lazy(() => import('../pages/Registration'));
const InvitedRegistrationPage = React.lazy(() => import('../pages/InvitedRegistrationPage'));
const ResetPassword = React.lazy(() => import('../pages/ResetPassword'));
const Signup = React.lazy(() => import('../pages/Signup'));

// Event pages (moving to Schedule area)
const Events = React.lazy(() => import('../pages/Events'));
const EventDetails = React.lazy(() => import('../pages/EventDetails'));

// Other top-level pages
const Activities = React.lazy(() => import('../pages/Activities'));
const Home = React.lazy(() => import('../pages/Home'));
const Splash = React.lazy(() => import('../pages/Splash'));
const ImproveIDP = React.lazy(() => import('../pages/ImproveIDP'));
const ViewIDP = React.lazy(() => import('../pages/ViewIDP'));
const Membership = React.lazy(() => import('../pages/Membership'));
const GenerateCode = React.lazy(() => import('../pages/GenerateCode'));

// V2 DesignSystem pages (consolidated to main design-system route)
const DesignSystemIndex = React.lazy(() => import('../pages/v2/DesignSystem/index'));
const DesignSystemMinimal = React.lazy(() => import('../pages/v2/DesignSystem/MinimalTest'));

// Perform area pages (being reorganized)
const MemberPerform = React.lazy(() => import('../pages/perform/MemberPerform'));
const CoachPerform = React.lazy(() => import('../pages/v2/perform/CoachPerform'));
const PlayerPerform = React.lazy(() => import('../pages/v2/perform/PlayerPerform'));
const ParentPerform = React.lazy(() => import('../pages/v2/perform/ParentPerform'));
const PerformDebug = React.lazy(() => import('../pages/v2/perform/PerformDebug'));

// Coach section pages (many being moved to appropriate areas)
const UpdatedCoachDashboard = React.lazy(() => import('../pages/section/Coach/UpdatedCoachDashboard'));
const UpdatedCoachHome = React.lazy(() => import('../pages/section/Coach/UpdatedCoachHome'));
const WeeklyEvaluation = React.lazy(() => import('../pages/section/Coach/WeeklyEvaluation'));
const createEvent = React.lazy(() => import('../pages/section/Coach/createEvent'));

// Player section
const Player = React.lazy(() => import('../pages/Player'));

// Admin pages
const SuperAdminIndex = React.lazy(() => import('../pages/SuperAdmin/index'));
const SmsNotificationDashboard = React.lazy(() => import('../pages/section/Admin/SmsNotificationDashboard'));

export const DeprecatedRoutes: React.FC = () => {
  return (
    <Switch>
      {/* ========== DEPRECATED AUTH ROUTES ========== */}
      {/* These login variants are replaced by UpdatedLoginV2 */}
      <Route exact path="/deprecated/login-old" component={Login} />
      <Route exact path="/deprecated/login-updated" component={UpdatedLogin} />
      <Route exact path="/deprecated/login-simple" component={SimpleLogin} />
      <Route exact path="/deprecated/login-nonionic" component={NonIonicLogin} />
      
      {/* ========== DEPRECATED ACCOUNT ROUTES ========== */}
      <Route exact path="/deprecated/account" component={Account} />
      <Route exact path="/deprecated/profile" component={Profile} />
      <Route exact path="/deprecated/dashboard" component={Dashboard} />
      
      {/* ========== DEPRECATED FAMILY ROUTES ========== */}
      <Route exact path="/deprecated/family" component={Family} />
      <Route exact path="/deprecated/edit-family/:id" component={EditFamily} />
      <Route exact path="/deprecated/add-family" component={AddFamily} />
      
      {/* ========== DEPRECATED REGISTRATION ROUTES ========== */}
      <Route exact path="/deprecated/registration" component={Registration} />
      <Route exact path="/deprecated/invited-registration" component={InvitedRegistrationPage} />
      <Route exact path="/deprecated/reset-password" component={ResetPassword} />
      <Route exact path="/deprecated/signup" component={Signup} />
      
      {/* ========== DEPRECATED EVENT ROUTES ========== */}
      <Route exact path="/deprecated/events" component={Events} />
      <Route exact path="/deprecated/events/:id" component={EventDetails} />
      
      {/* ========== DEPRECATED TOP-LEVEL PAGES ========== */}
      <Route exact path="/deprecated/activities" component={Activities} />
      <Route exact path="/deprecated/home" component={Home} />
      <Route exact path="/deprecated/splash" component={Splash} />
      <Route exact path="/deprecated/improve-idp" component={ImproveIDP} />
      <Route exact path="/deprecated/view-idp" component={ViewIDP} />
      <Route exact path="/deprecated/membership" component={Membership} />
      <Route exact path="/deprecated/generate-code" component={GenerateCode} />
      
      {/* ========== DEPRECATED V2 ROUTES ========== */}
      <Route exact path="/deprecated/v2/design-system" component={DesignSystemIndex} />
      <Route exact path="/deprecated/v2/design-system/minimal" component={DesignSystemMinimal} />
      <Route exact path="/deprecated/v2/perform/member" component={MemberPerform} />
      <Route exact path="/deprecated/v2/perform/coach" component={CoachPerform} />
      <Route exact path="/deprecated/v2/perform/player" component={PlayerPerform} />
      <Route exact path="/deprecated/v2/perform/parent" component={ParentPerform} />
      <Route exact path="/deprecated/v2/perform/debug" component={PerformDebug} />
      
      {/* ========== DEPRECATED COACH ROUTES ========== */}
      <Route exact path="/deprecated/coach/dashboard-updated" component={UpdatedCoachDashboard} />
      <Route exact path="/deprecated/coach/home-updated" component={UpdatedCoachHome} />
      <Route exact path="/deprecated/coach/weekly-evaluation" component={WeeklyEvaluation} />
      <Route exact path="/deprecated/coach/create-event-old" component={createEvent} />
      
      {/* ========== DEPRECATED MISC ROUTES ========== */}
      <Route exact path="/deprecated/player" component={Player} />
      <Route exact path="/deprecated/superadmin-old" component={SuperAdminIndex} />
      <Route exact path="/deprecated/admin/sms-dashboard" component={SmsNotificationDashboard} />
    </Switch>
  );
};

// DEPRECATION NOTES:
// =================
// 1. AUTH: Multiple login pages consolidated to UpdatedLoginV2
// 2. FAMILY: Moving to Identity area with unified family management
// 3. EVENTS: Moving to Schedule area with new event architecture
// 4. PERFORM: V2 perform pages being reorganized into role-based dashboards
// 5. COACH: Large coach section being split across Perform, Schedule, and Assess areas
// 6. DESIGN SYSTEM: V2 design system pages consolidated to main /design-system route

// MIGRATION CHECKLIST:
// ===================
// [ ] Verify no production traffic to these routes
// [ ] Check for any hardcoded links to these paths
// [ ] Update any documentation referencing old routes
// [ ] Remove imports from pages/index.ts
// [ ] After 2 months in production, delete deprecated files

// FALSE POSITIVE WARNINGS:
// =======================
// Some files marked as "unused" are actually components, not pages:
// - Coach components (modals, forms, cards)
// - Player components (evaluation forms)
// - Design system section components
// These should NOT be added to deprecated routes