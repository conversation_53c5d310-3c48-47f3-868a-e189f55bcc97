// Stage 1 Routes Configuration
// This file defines the routing for Stage 1 of the registration changes
// It uses the V2 components for the new open registration flow

import React from 'react';
import { Route, Redirect } from 'react-router-dom';

// Import the V2 components for Stage 1
import OpenRegistration from '../pages/registration/OpenRegistration';
import MemberPerformV2 from '../pages/perform/MemberPerformV2';
import UpdatedLoginV2 from '../pages/UpdatedLoginV2';

export const Stage1Routes: React.FC = () => {
  return (
    <>
      {/* Stage 1: Open Registration Routes */}
      
      {/* Registration now points to open registration (no invite code required) */}
      <Route exact path="/register" component={OpenRegistration} />
      
      {/* Legacy invited registration redirects to open registration */}
      <Route exact path="/register-invited" render={() => <Redirect to="/register" />} />
      
      {/* Perform tab shows options for all members */}
      <Route exact path="/perform" component={MemberPerformV2} />
      
      {/* Login routes - using V2 login page */}
      <Route exact path="/login" component={UpdatedLoginV2} />
      
      {/* Additional routes for specific perform flows */}
      <Route exact path="/perform/instant-session/:sessionId" render={({ match }) => {
        // This will be implemented in Stage 2 for "Just for Me" feature
        return <div className="min-h-screen bg-black text-white flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Instant Session Coming Soon!</h1>
            <p className="text-gray-400">Session ID: {match.params.sessionId}</p>
          </div>
        </div>;
      }} />
    </>
  );
};

export default Stage1Routes;
