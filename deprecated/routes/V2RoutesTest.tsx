// src/routes/V2RoutesTest.tsx
import React, { lazy, Suspense } from 'react';
import { Switch, Route } from 'react-router-dom';

// Lazy load Perform pages
const Perform = lazy(() => import('../pages/v2/perform/Perform'));
const CoachPerform = lazy(() => import('../pages/v2/perform/CoachPerform'));
const PlayerPerform = lazy(() => import('../pages/v2/perform/PlayerPerform'));
const ParentPerform = lazy(() => import('../pages/v2/perform/ParentPerform'));
const MemberPerform = lazy(() => import('../pages/v2/perform/MemberPerform'));

export const V2RoutesTest: React.FC = () => {
  console.log('[V2RoutesTest] Rendering, current path:', window.location.pathname);
  
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="p-4 bg-gray-900 mb-4">
        <h1 className="text-xl font-bold">V2 Routes Test (No Layout)</h1>
        <p className="text-sm text-gray-400">Current path: {window.location.pathname}</p>
      </div>
      
      <Suspense fallback={<div className="p-8">Loading...</div>}>
        <Switch>
          <Route exact path="/v2/perform/coach" render={() => {
            console.log('[V2RoutesTest] Rendering CoachPerform');
            return <CoachPerform />;
          }} />
          <Route exact path="/v2/perform/player" render={() => {
            console.log('[V2RoutesTest] Rendering PlayerPerform');
            return <PlayerPerform />;
          }} />
          <Route exact path="/v2/perform/parent" render={() => {
            console.log('[V2RoutesTest] Rendering ParentPerform');
            return <ParentPerform />;
          }} />
          <Route exact path="/v2/perform/member" render={() => {
            console.log('[V2RoutesTest] Rendering MemberPerform');
            return <MemberPerform />;
          }} />
          <Route exact path="/v2/perform" render={() => {
            console.log('[V2RoutesTest] Rendering Perform router');
            return <Perform />;
          }} />
          <Route path="*" render={({ location }) => {
            console.log('[V2RoutesTest] No match for:', location.pathname);
            return <div className="p-8">No route matched: {location.pathname}</div>;
          }} />
        </Switch>
      </Suspense>
    </div>
  );
};
