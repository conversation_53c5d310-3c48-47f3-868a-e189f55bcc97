// AppRoutesStage1.tsx
// This file shows how to integrate Stage 1 routes into your main application
// Copy this implementation into your AppRoutes.tsx file

import React, { useState, Suspense } from 'react';
import { Route, Redirect, Switch, useLocation } from 'react-router-dom';
import { useUserContext } from '../contexts/UserContext';
import { supabase } from '@/lib/supabase';

// Import Stage 1 Components
import OpenRegistration from '../pages/registration/OpenRegistration';
import MemberPerformV2 from '../pages/perform/MemberPerformV2';
import UpdatedLoginV2 from '../pages/UpdatedLoginV2';

// Import other necessary components (keep your existing imports)
import EmailVerificationPage from '../pages/EmailVerificationPage';
import SplashPage from '../pages/Splash';
import HomePage from '../pages/Home';

// Example of updated routing for Stage 1
const AppRoutesStage1: React.FC = () => {
  const location = useLocation();
  const { user, loading } = useUserContext();
  
  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-gray-900">
        <div className="text-white text-lg">Loading...</div>
      </div>
    );
  }
  
  // If user is not authenticated, only show public routes
  if (!user) {
    return (
      <Switch>
        {/* Public Routes */}
        <Route exact path="/" component={SplashPage} />
        
        {/* STAGE 1: Updated Login Page - Points to open registration */}
        <Route exact path="/login" component={UpdatedLoginV2} />
        
        {/* STAGE 1: Open Registration - No invite code required */}
        <Route exact path="/register" component={OpenRegistration} />
        
        {/* Handle invite parameter for future use */}
        <Route exact path="/register-invited" render={() => <Redirect to="/register" />} />
        
        {/* Reset password flow */}
        <Route exact path="/reset-password" render={() => (
          <div className="min-h-screen bg-black text-white flex items-center justify-center">
            <div className="text-center">
              <h1 className="text-2xl font-bold mb-4">Reset Password</h1>
              <p className="text-gray-400">Reset password functionality here</p>
            </div>
          </div>
        )} />
        
        {/* Redirect all other routes to splash for unauthenticated users */}
        <Route path="*">
          <Redirect to="/" />
        </Route>
      </Switch>
    );
  }
  
  // Check if user's email is verified
  const isEmailVerified = user?.email_confirmed_at != null;
  
  // If user is authenticated but email not verified, show verification screen
  if (!isEmailVerified) {
    return (
      <Switch>
        <Route exact path="/email-verification" render={() => (
          <EmailVerificationPage user={user} />
        )} />
        <Route exact path="/logout" render={() => {
          supabase.auth.signOut();
          return <Redirect to="/" />;
        }} />
        {/* Redirect all other routes to email verification */}
        <Route path="*">
          <Redirect to="/email-verification" />
        </Route>
      </Switch>
    );
  }

  // User is authenticated and verified, show protected routes
  return (
    <Switch>
      {/* STAGE 1: All authenticated users go to MemberPerformV2 */}
      <Route exact path="/perform" component={MemberPerformV2} />
      
      {/* Home page */}
      <Route exact path="/home" component={HomePage} />
      
      {/* Account/Profile pages */}
      <Route exact path="/account" render={() => (
        <div className="min-h-screen bg-black text-white p-8">
          <h1 className="text-2xl font-bold mb-4">Account Page</h1>
          <p className="text-gray-400">Account management functionality here</p>
        </div>
      )} />
      
      {/* Future Stage 2: Instant Session for "Just for Me" */}
      <Route exact path="/perform/instant-session/:sessionId" render={({ match }) => (
        <div className="min-h-screen bg-black text-white flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Instant Session (Stage 2)</h1>
            <p className="text-gray-400">Session ID: {match.params.sessionId}</p>
            <p className="text-sm text-gray-500 mt-4">This feature will be implemented in Stage 2</p>
          </div>
        </div>
      )} />
      
      {/* Coach Dashboard - for users with coach privileges */}
      <Route exact path="/coach/dashboard" render={() => (
        <div className="min-h-screen bg-black text-white p-8">
          <h1 className="text-2xl font-bold mb-4">Coach Dashboard</h1>
          <p className="text-gray-400">Coach dashboard functionality here</p>
        </div>
      )} />
      
      {/* Logout */}
      <Route exact path="/logout" render={() => {
        supabase.auth.signOut();
        return <Redirect to="/" />;
      }} />
      
      {/* Root redirect - authenticated users go to perform page */}
      <Route exact path="/" render={() => <Redirect to="/perform" />} />
      
      {/* Catch-all route */}
      <Route path="*" render={() => <Redirect to="/perform" />} />
    </Switch>
  );
};

export default AppRoutesStage1;
