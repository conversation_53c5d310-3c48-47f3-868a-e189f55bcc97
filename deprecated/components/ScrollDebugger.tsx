// ScrollDebugger.tsx
import React, { useState } from 'react';
import { IonButton, IonContent, IonToggle, IonLabel, IonItem } from '@ionic/react';
import { applyScrollFix, removeScrollFix, fixIonicContentScroll } from '../utils/ScrollFixUtility';

/**
 * A utility component to help diagnose scrolling issues
 * This can be temporarily added to any page to debug scroll problems
 */
const ScrollDebugger: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [log, setLog] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLog(prev => [...prev, message]);
  };

  // Analyze the CSS of elements that might block scrolling
  const analyzeScrollIssues = () => {
    addLog(`Starting scroll analysis at ${new Date().toLocaleTimeString()}`);
    
    try {
      // Check IonContent settings
      const ionContent = document.querySelector('ion-content');
      if (ionContent) {
        const ionContentStyle = window.getComputedStyle(ionContent);
        addLog(`IonContent overflow: ${ionContentStyle.overflow}`);
        addLog(`IonContent position: ${ionContentStyle.position}`);
        addLog(`IonContent height: ${ionContentStyle.height}`);
      } else {
        addLog('No IonContent found!');
      }
      
      // Check main content div
      const pageContent = document.querySelector('.page-content');
      if (pageContent) {
        const pageContentStyle = window.getComputedStyle(pageContent);
        addLog(`Page content overflow: ${pageContentStyle.overflow}`);
        addLog(`Page content position: ${pageContentStyle.position}`);
        addLog(`Page content height: ${pageContentStyle.height}`);
      } else {
        addLog('No .page-content found!');
      }
      
      // Check for any fixed position elements that might cover scrollable area
      const fixedElements = document.querySelectorAll('[style*="position: fixed"], [style*="position:fixed"]');
      addLog(`Found ${fixedElements.length} fixed position elements`);
      
      // Check body and html overflow
      const bodyStyle = window.getComputedStyle(document.body);
      const htmlStyle = window.getComputedStyle(document.documentElement);
      addLog(`Body overflow: ${bodyStyle.overflow}, height: ${bodyStyle.height}`);
      addLog(`HTML overflow: ${htmlStyle.overflow}, height: ${htmlStyle.height}`);
      
      // Try to remove any overflow: hidden temporarily
      const overflowBlockers: HTMLElement[] = [];
      document.querySelectorAll('*').forEach(el => {
        const style = window.getComputedStyle(el);
        if (style.overflow === 'hidden' || style.overflowY === 'hidden') {
          overflowBlockers.push(el as HTMLElement);
        }
      });
      
      addLog(`Found ${overflowBlockers.length} elements with overflow: hidden`);
      
      // Look for flexbox issues
      const flexContainers = document.querySelectorAll('[style*="display: flex"], [style*="display:flex"]');
      addLog(`Found ${flexContainers.length} flex containers`);
      
      // Look for any elements with very large height
      const tallElements = Array.from(document.querySelectorAll('*')).filter(el => {
        const rect = el.getBoundingClientRect();
        return rect.height > window.innerHeight * 1.5;
      });
      
      addLog(`Found ${tallElements.length} elements taller than 1.5x viewport height`);
    } catch (error) {
      addLog(`Error during analysis: ${error}`);
    }
  };
  
  // Try some emergency fixes
  const tryScrollFix = () => {
    addLog('Attempting emergency scroll fix...');
    
    try {
      // Apply our emergency fix utility
      const result = applyScrollFix();
      
      if (result) {
        addLog('Applied comprehensive emergency scroll fix');
      } else {
        addLog('Failed to apply comprehensive fix, trying backup methods...');
        
        // Try direct approach
        fixIonicContentScroll();
        
        // 1. Fix IonContent overflow
        const ionContents = document.querySelectorAll('ion-content');
        ionContents.forEach((content: any) => {
          if (content.style) {
            content.style.overflow = 'auto';
            content.style.overflowY = 'auto';
          }
        });
        
        // 2. Fix page content
        const pageContents = document.querySelectorAll('.page-content');
        pageContents.forEach((content: any) => {
          if (content.style) {
            content.style.overflow = 'auto';
            content.style.overflowY = 'auto';
            content.style.height = 'auto';
            content.style.maxHeight = 'none';
          }
        });
        
        // 3. Fix any overflow: hidden issues
        document.querySelectorAll('*').forEach((el: any) => {
          if (el.style && (el.style.overflow === 'hidden' || el.style.overflowY === 'hidden')) {
            el.style.overflow = 'auto';
            el.style.overflowY = 'auto';
          }
        });
      }
      
      addLog('Emergency fixes applied. Try scrolling now!');
    } catch (error) {
      addLog(`Error applying fixes: ${error}`);
    }
  };
  
  // Toggle z-index of potential blocker elements
  const toggleZIndexFix = () => {
    addLog('Toggling z-index of potential blocker elements...');
    
    try {
      // Find elements with high z-index
      document.querySelectorAll('*').forEach((el: any) => {
        const style = window.getComputedStyle(el);
        const zIndex = parseInt(style.zIndex);
        
        if (!isNaN(zIndex) && zIndex > 10) {
          addLog(`Found element with z-index ${zIndex}: ${el.tagName}`);
          
          // Store original z-index in a data attribute if not already there
          if (!el.dataset.originalZIndex) {
            el.dataset.originalZIndex = style.zIndex;
            el.style.zIndex = '0';
          } else {
            // Restore original z-index
            el.style.zIndex = el.dataset.originalZIndex;
            delete el.dataset.originalZIndex;
          }
        }
      });
      
      addLog('Z-index toggling complete');
    } catch (error) {
      addLog(`Error toggling z-index: ${error}`);
    }
  };

  if (!isVisible) {
    return (
      <div style={{ 
        position: 'fixed', 
        bottom: '10px', 
        right: '10px', 
        zIndex: 9999,
        backgroundColor: 'rgba(0,0,0,0.7)',
        padding: '5px',
        borderRadius: '5px'
      }}>
        <IonButton 
          size="small" 
          onClick={() => setIsVisible(true)}
        >
          Debug Scroll
        </IonButton>
      </div>
    );
  }

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      right: '10px', 
      zIndex: 9999,
      backgroundColor: 'rgba(0,0,0,0.9)',
      color: 'white',
      padding: '10px',
      borderRadius: '5px',
      maxHeight: '80vh',
      overflowY: 'auto',
      width: '300px'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
        <h3 style={{ margin: 0 }}>Scroll Debugger</h3>
        <IonButton 
          size="small" 
          color="danger" 
          onClick={() => setIsVisible(false)}
        >
          Close
        </IonButton>
      </div>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '5px', marginBottom: '10px' }}>
        <IonButton size="small" color="primary" onClick={analyzeScrollIssues}>
          Analyze Scroll Issues
        </IonButton>
        <IonButton size="small" color="success" onClick={tryScrollFix}>
          Apply Emergency Fix
        </IonButton>
        <IonButton size="small" color="danger" onClick={() => {
          removeScrollFix();
          addLog('Emergency fix removed');
        }}>
          Remove Fix
        </IonButton>
        <IonButton size="small" color="warning" onClick={toggleZIndexFix}>
          Toggle Z-Index Fix
        </IonButton>
        <IonButton size="small" color="medium" onClick={() => setLog([])}>
          Clear Log
        </IonButton>
      </div>
      
      <div>
        <h4 style={{ margin: '5px 0' }}>Debug Log:</h4>
        <div style={{ 
          backgroundColor: '#111', 
          padding: '5px',
          borderRadius: '3px',
          fontSize: '12px',
          fontFamily: 'monospace',
          maxHeight: '300px',
          overflowY: 'auto'
        }}>
          {log.length === 0 ? (
            <p style={{ color: '#888' }}>Click "Analyze Scroll Issues" to start debugging</p>
          ) : (
            log.map((message, i) => (
              <div key={i} style={{ marginBottom: '5px', borderBottom: '1px solid #333', paddingBottom: '5px' }}>
                {message}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default ScrollDebugger;