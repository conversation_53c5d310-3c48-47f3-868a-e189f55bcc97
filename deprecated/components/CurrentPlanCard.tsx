import { IonIcon } from '@ionic/react';
import { checkmarkCircleOutline } from 'ionicons/icons';
import { Membership, Addon } from '../types/membership';

interface CurrentPlanCardProps {
  membership?: Membership;
  addons: Addon[];
  onModify: () => void;
}

const CurrentPlanCard: React.FC<CurrentPlanCardProps> = ({
  membership,
  addons,
  onModify,
}) => {
  const totalPrice = (membership?.price || 0) + addons.reduce((sum, addon) => sum + addon.price, 0);

  return (
    <div className="bg-gray-800 rounded-xl p-6 h-full">
      <div className="flex justify-between items-start mb-6">
        <h2 className="text-xl font-bold text-white">Your Current Plan</h2>
        <button
          onClick={onModify}
          className="text-sm text-emerald-500 hover:text-emerald-400"
        >
          Modify
        </button>
      </div>

      {membership ? (
        <>
          <div className="mb-6">
            <div className="flex justify-between items-baseline mb-2">
              <h3 className="text-lg font-semibold text-emerald-500">
                {membership.name}
              </h3>
              <span className="text-lg text-white">
                ${membership.price}
                <span className="text-sm text-gray-400">/{membership.billing_period}</span>
              </span>
            </div>
            <div className="space-y-2">
              {membership.features.slice(0, 3).map((feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  <IonIcon 
                    icon={checkmarkCircleOutline} 
                    className="text-emerald-500 w-5 h-5 flex-shrink-0" 
                  />
                  <span className="text-sm text-gray-300">{feature}</span>
                </div>
              ))}
              {membership.features.length > 3 && (
                <div className="text-sm text-gray-400">
                  +{membership.features.length - 3} more features
                </div>
              )}
            </div>
          </div>

          {addons.length > 0 && (
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-white mb-3">Active Add-ons</h3>
              <div className="space-y-3">
                {addons.map((addon) => (
                  <div 
                    key={addon.id} 
                    className="flex justify-between items-center bg-gray-700 rounded-lg p-3"
                  >
                    <div>
                      <div className="text-sm font-medium text-white">
                        {addon.name}
                      </div>
                      <div className="text-xs text-gray-400">
                        {addon.category}
                      </div>
                    </div>
                    <div className="text-sm text-white">
                      ${addon.price}/{addon.billing_period}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="pt-4 border-t border-gray-700">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-400">Total</div>
              <div className="text-xl font-bold text-white">
                ${totalPrice.toFixed(2)}/{membership.billing_period}
              </div>
            </div>
          </div>
        </>
      ) : (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-4">No active plan</div>
          <button
            onClick={onModify}
            className="px-6 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700"
          >
            Choose a Plan
          </button>
        </div>
      )}
    </div>
  );
};

export default CurrentPlanCard;