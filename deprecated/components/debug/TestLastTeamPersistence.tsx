// ABOUTME: Debug component to test last accessed team persistence functionality
// This component displays the current state and allows manual testing

import React from 'react';
import { useCoachContext } from '../../foundation/contexts/CoachContext';
import { useUserContext } from '../../contexts/UserContext';

export const TestLastTeamPersistence: React.FC = () => {
  const { user } = useUserContext();
  const { teams, currentTeam, switchTeam, loading } = useCoachContext();
  
  const STORAGE_KEY = 'shot_coach_last_team';
  const storageKey = user?.id ? `${STORAGE_KEY}_${user.id}` : null;
  const localStorageValue = storageKey ? localStorage.getItem(storageKey) : null;
  
  if (loading) {
    return <div>Loading...</div>;
  }
  
  return (
    <div className="p-4 bg-gray-800 text-white rounded-lg m-4">
      <h3 className="text-lg font-bold mb-4">Last Team Persistence Debug</h3>
      
      <div className="space-y-2">
        <div className="flex gap-2">
          <span className="font-semibold">User ID:</span>
          <span className="font-mono text-sm">{user?.id || 'Not logged in'}</span>
        </div>
        
        <div className="flex gap-2">
          <span className="font-semibold">Current Team:</span>
          <span>{currentTeam?.name || 'None selected'} ({currentTeam?.id || 'N/A'})</span>
        </div>
        
        <div className="flex gap-2">
          <span className="font-semibold">LocalStorage Value:</span>
          <span className="font-mono text-sm">{localStorageValue || 'Not set'}</span>
        </div>
        
        <div className="flex gap-2">
          <span className="font-semibold">Total Teams:</span>
          <span>{teams.length}</span>
        </div>
      </div>
      
      {teams.length > 0 && (
        <div className="mt-4">
          <h4 className="font-semibold mb-2">Switch Team:</h4>
          <div className="space-y-2">
            {teams.map(team => (
              <button
                key={team.id}
                onClick={() => switchTeam(team.id)}
                className={`block w-full text-left p-2 rounded ${
                  currentTeam?.id === team.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 hover:bg-gray-600'
                }`}
              >
                {team.name} (ID: {team.id})
              </button>
            ))}
          </div>
        </div>
      )}
      
      <div className="mt-4 text-sm text-gray-400">
        <p>To test persistence:</p>
        <ol className="list-decimal list-inside">
          <li>Note the current team</li>
          <li>Switch to a different team</li>
          <li>Log out and log back in</li>
          <li>Check if the same team is selected</li>
        </ol>
      </div>
    </div>
  );
};