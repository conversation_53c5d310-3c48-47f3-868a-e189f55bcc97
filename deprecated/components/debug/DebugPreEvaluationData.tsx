import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';

interface DebugPreEvalProps {
  eventIds?: string[];
}

/**
 * Debug component to verify pre-evaluation data directly from the view
 */
export const DebugPreEvaluationData: React.FC<DebugPreEvalProps> = ({ eventIds }) => {
  const [data, setData] = useState<any[]>([]);
  
  useEffect(() => {
    const fetchData = async () => {
      console.log('🔬 DEBUG: Fetching directly from event_comprehensive_summary view...');
      
      let query = supabase
        .from('event_comprehensive_summary')
        .select('event_id, event_name, pre_eval_total, pre_eval_completed, pre_eval_completion_percentage');
      
      if (eventIds && eventIds.length > 0) {
        query = query.in('event_id', eventIds);
      }
      
      const { data: summaries, error } = await query;
      
      if (error) {
        console.error('🔴 DEBUG: Error fetching from view:', error);
      } else {
        console.log('🟢 DEBUG: Raw data from view:', summaries);
        setData(summaries || []);
      }
    };
    
    fetchData();
  }, [eventIds]);
  
  return (
    <div style={{ 
      position: 'fixed', 
      bottom: 20, 
      right: 20, 
      background: 'black', 
      color: 'white', 
      padding: '10px',
      border: '2px solid yellow',
      maxWidth: '400px',
      fontSize: '12px',
      zIndex: 9999
    }}>
      <h4 style={{ color: 'yellow', margin: '0 0 10px 0' }}>🔍 Pre-Eval Debug</h4>
      {data.map(item => (
        <div key={item.event_id} style={{ marginBottom: '8px', borderBottom: '1px solid #333', paddingBottom: '8px' }}>
          <div><strong>{item.event_name}</strong></div>
          <div>ID: {item.event_id}</div>
          <div style={{ color: item.pre_eval_completion_percentage === '0' ? 'lightgreen' : 'orange' }}>
            Percentage: {item.pre_eval_completion_percentage}%
          </div>
          <div>Total: {item.pre_eval_total} | Completed: {item.pre_eval_completed}</div>
        </div>
      ))}
    </div>
  );
};

// Export a global function to test specific events
(window as any).debugPreEval = async (eventId: string) => {
  const { data, error } = await supabase
    .from('event_comprehensive_summary')
    .select('*')
    .eq('event_id', eventId)
    .single();
  
  console.log('🔍 Debug Pre-Eval for event', eventId, ':', data, error);
  return data;
};
