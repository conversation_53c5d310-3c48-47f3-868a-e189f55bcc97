// ABOUTME: Global debug provider that shows component names on hover for ALL components
// Uses React Profiler API and component stack to display debug information

import React, { useEffect, useState, useRef, useCallback } from 'react';
import { isDebugMode } from '../utils/debugMode';
import './GlobalDebugProvider.css';

interface DebugInfo {
  componentName: string;
  x: number;
  y: number;
  id: string;
}

export const GlobalDebugProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [debugInfos, setDebugInfos] = useState<DebugInfo[]>([]);
  const [hoveredId, setHoveredId] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isDebugMode()) return;

    let idCounter = 0;
    const activeElements = new Map<HTMLElement, string>();

    const handleMouseMove = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      
      // Find React Fiber
      const fiberKey = Object.keys(target).find(key => 
        key.startsWith('__reactInternalInstance') || 
        key.startsWith('__reactFiber')
      );
      
      if (!fiberKey) return;
      
      const fiber = (target as any)[fiberKey];
      if (!fiber) return;

      // Walk up the fiber tree to find components
      let currentFiber = fiber;
      const components: string[] = [];
      
      while (currentFiber) {
        if (currentFiber.elementType && typeof currentFiber.elementType === 'function') {
          const name = currentFiber.elementType.displayName || 
                      currentFiber.elementType.name || 
                      'Unknown';
          
          // Skip common wrapper components
          if (!['withRouter', 'Connect', 'Unknown', 'div', 'span'].includes(name)) {
            components.push(name);
          }
        }
        currentFiber = currentFiber.return;
      }

      if (components.length > 0) {
        const id = `debug-${idCounter++}`;
        const rect = target.getBoundingClientRect();
        
        setDebugInfos(prev => {
          // Remove old entry for this element
          const filtered = prev.filter(info => !activeElements.has(target));
          
          // Add new entry
          activeElements.set(target, id);
          return [...filtered, {
            componentName: components[0], // Show nearest component
            x: rect.left + window.scrollX,
            y: rect.top + window.scrollY - 25,
            id
          }];
        });
        
        setHoveredId(id);
      }
    };

    const handleMouseOut = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const id = activeElements.get(target);
      if (id) {
        activeElements.delete(target);
        setDebugInfos(prev => prev.filter(info => info.id !== id));
        if (hoveredId === id) {
          setHoveredId(null);
        }
      }
    };

    // Add global listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseout', handleMouseOut);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseout', handleMouseOut);
    };
  }, [isDebugMode(), hoveredId]);

  if (!isDebugMode()) {
    return <>{children}</>;
  }

  return (
    <>
      {children}
      <div ref={containerRef} className="global-debug-container">
        {debugInfos.map(info => (
          <div
            key={info.id}
            className={`global-debug-label ${hoveredId === info.id ? 'active' : ''}`}
            style={{
              position: 'absolute',
              left: `${info.x}px`,
              top: `${info.y}px`,
            }}
          >
            {info.componentName}
          </div>
        ))}
      </div>
    </>
  );
};

// HOC version for individual component wrapping with file paths
export const withComponentPath = <P extends object>(
  Component: React.ComponentType<P>,
  filePath: string
): React.ComponentType<P> => {
  const WrappedComponent = React.forwardRef<any, P>((props, ref) => {
    const [showPath, setShowPath] = useState(false);
    
    if (!isDebugMode()) {
      return <Component {...props} ref={ref} />;
    }

    return (
      <div 
        className="component-with-path"
        onMouseEnter={() => setShowPath(true)}
        onMouseLeave={() => setShowPath(false)}
        style={{ position: 'relative' }}
      >
        {showPath && (
          <div className="component-path-tooltip">
            {filePath}
          </div>
        )}
        <Component {...props} ref={ref} />
      </div>
    );
  });

  WrappedComponent.displayName = `withPath(${Component.displayName || Component.name})`;
  return WrappedComponent;
};