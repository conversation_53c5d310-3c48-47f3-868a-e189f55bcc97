// Example of how to use debug mode in your components
import React from 'react';
import { ShadowStatCard } from '../shadow/ShadowStatCard';
import { ShadowButton } from '../shadow/ShadowButton';
import { ShadowActionGrid } from '../shadow/ShadowActionGrid';
import { ShadowInfoCard } from '../shadow/ShadowInfoCard';

// This would typically come from your auth context or user state
interface UserContext {
  role: 'coach' | 'player' | 'parent' | 'admin' | 'superadmin';
  // other user properties...
}

// Example: Component that's coach-specific
export const CoachDashboard: React.FC = () => {
  // In a real app, this would come from your auth context
  const currentUserRole: UserContext['role'] = 'coach';
  
  return (
    <div className="p-4 space-y-6">
      <h2 className="text-xl font-bold text-white">Coach Dashboard</h2>
      
      {/* Pass the role prop to components that should show debug indicators */}
      <div className="grid grid-cols-3 gap-4">
        <ShadowStatCard 
          value={5}
          label="Teams"
          color="teal"
          role={currentUserRole} // This will show the orange coach indicator
        />
        <ShadowStatCard 
          value={42}
          label="Players"
          color="purple"
          role={currentUserRole}
        />
        <ShadowStatCard 
          value={156}
          label="Sessions"
          color="green"
          role={currentUserRole}
        />
      </div>
      
      <ShadowButton 
        text="Create New Team"
        variant="primary"
        size="medium"
        role={currentUserRole}
        onClick={() => console.log('Create team')}
      />
    </div>
  );
};

// Example: Component that's player-specific
export const PlayerStats: React.FC = () => {
  const currentUserRole: UserContext['role'] = 'player';
  
  return (
    <div className="p-4 space-y-6">
      <h2 className="text-xl font-bold text-white">My Performance</h2>
      
      <div className="grid grid-cols-2 gap-4">
        <ShadowStatCard 
          value={89}
          label="Goals"
          color="green"
          role={currentUserRole} // This will show the blue player indicator
        />
        <ShadowStatCard 
          value={34}
          label="Assists"
          color="teal"
          role={currentUserRole}
        />
      </div>
      
      <ShadowButton 
        text="View Full Stats"
        variant="secondary"
        size="medium"
        role={currentUserRole}
      />
    </div>
  );
};

// Example: Using with conditional role assignment
export const TeamView: React.FC<{ userRole: UserContext['role'] }> = ({ userRole }) => {
  // Only show debug indicators for role-specific components
  const isCoachView = userRole === 'coach';
  const isPlayerView = userRole === 'player';
  
  return (
    <div className="space-y-4">
      {/* Common components - no role prop */}
      <ShadowInfoCard 
        variant="info"
        title="Team Schedule"
        description="View upcoming matches and training sessions"
      />
      
      {/* Coach-only section */}
      {isCoachView && (
        <div className="space-y-4">
          <ShadowActionGrid 
            columns={2}
            items={[
              { id: 'lineup', icon: 'users', label: 'Set Lineup' },
              { id: 'tactics', icon: 'clipboard', label: 'Tactics' }
            ]}
            role="coach" // Only coaches see this with orange indicator
          />
          
          <ShadowButton 
            text="Edit Team Settings"
            variant="primary"
            role="coach"
          />
        </div>
      )}
      
      {/* Player-only section */}
      {isPlayerView && (
        <div className="space-y-4">
          <ShadowStatCard 
            value="A+"
            label="Last Match Rating"
            color="green"
            role="player" // Only players see this with blue indicator
          />
          
          <ShadowButton 
            text="Update Availability"
            variant="secondary"
            role="player"
          />
        </div>
      )}
    </div>
  );
};

// Example: Hook for getting current user role
export const useUserRole = (): UserContext['role'] | undefined => {
  // In a real app, this would come from your auth/user context
  // For now, let's simulate it
  
  // This could check your actual user state/context
  // return useContext(AuthContext)?.user?.role;
  
  // For demo purposes:
  return 'coach';
};

// Example: HOC for automatically adding role to components
export const withRoleDebug = <P extends { role?: UserContext['role'] }>(
  Component: React.ComponentType<P>,
  roleOverride?: UserContext['role']
) => {
  return (props: Omit<P, 'role'>) => {
    const userRole = useUserRole();
    const role = roleOverride || userRole;
    
    return <Component {...props as P} role={role} />;
  };
};

// Usage of HOC:
// const DebugStatCard = withRoleDebug(ShadowStatCard);
// const CoachButton = withRoleDebug(ShadowButton, 'coach');
