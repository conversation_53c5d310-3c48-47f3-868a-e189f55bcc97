// Example: How to update pages to use role-specific debug indicators

import React from 'react';
import { PageWithNavigation } from '../../pages/section/Navigation';
import { RoleSpecificContent, RoleGate, withRoleDebug } from './RoleSpecificContent';
import { ShadowButton } from '../shadow/ShadowButton';
import { ShadowStatCard } from '../shadow/ShadowStatCard';
import { ShadowInfoCard } from '../shadow/ShadowInfoCard';

// Method 1: Wrap the entire page component
const CoachHomePageExample = withRoleDebug(() => {
  return (
    <PageWithNavigation
      showBackButton={true}
      title="Football PERFORM Hub" // Remove "Coach Home" - just use the feature name
      className="bg-gray-900"
    >
      <div className="px-4 py-4">
        {/* Page content */}
      </div>
    </PageWithNavigation>
  );
}, 'coach');

// Method 2: Wrap specific sections
export const CoachDashboardExample: React.FC = () => {
  const userRole = 'coach'; // Get from your auth context
  
  return (
    <PageWithNavigation
      showBackButton={true}
      title="Football PERFORM Hub"
      className="bg-gray-900"
    >
      <div className="px-4 py-4 space-y-6">
        {/* Common content - no role indicator */}
        <div className="bg-gray-800 rounded-xl p-6">
          <h2 className="text-white text-lg font-semibold mb-4">
            Welcome to PERFORM Hub
          </h2>
          <p className="text-gray-300">
            Manage your teams and track performance
          </p>
        </div>

        {/* Coach-specific section - wrapped with RoleSpecificContent */}
        <RoleSpecificContent role="coach" className="space-y-4">
          <div className="bg-gray-800 rounded-xl p-6">
            <h3 className="text-shot-teal text-lg font-semibold mb-4">
              Team Management
            </h3>
            
            {/* Use role prop on individual components */}
            <div className="grid grid-cols-3 gap-4">
              <ShadowStatCard 
                value={5}
                label="Active Teams"
                color="teal"
                role="coach" // This adds debug indicator to just this component
              />
              <ShadowStatCard 
                value={42}
                label="Total Players"
                color="purple"
                role="coach"
              />
              <ShadowStatCard 
                value={156}
                label="Sessions"
                color="green"
                role="coach"
              />
            </div>
          </div>

          <div className="bg-gray-800 rounded-xl p-6">
            <h3 className="text-shot-teal text-lg font-semibold mb-4">
              Quick Actions
            </h3>
            
            <div className="space-y-3">
              <ShadowInfoCard 
                variant="action"
                title="Create Training Session"
                icon="chevronRight"
                role="coach"
                onClick={() => console.log('Create session')}
              />
              <ShadowInfoCard 
                variant="action"
                title="Evaluate Players"
                icon="chevronRight"
                role="coach"
                onClick={() => console.log('Evaluate')}
              />
            </div>
          </div>
        </RoleSpecificContent>

        {/* Player-specific section example */}
        <RoleGate allowedRoles={['player']}>
          <div className="bg-gray-800 rounded-xl p-6">
            <h3 className="text-shot-teal text-lg font-semibold mb-4">
              My Performance
            </h3>
            <ShadowStatCard 
              value="A+"
              label="Latest Rating"
              color="green"
              role="player"
            />
          </div>
        </RoleGate>
      </div>
    </PageWithNavigation>
  );
};

// Method 3: Define role-specific pages/routes
export const PerformHubPage: React.FC = () => {
  const userRole = 'coach'; // Get from auth context
  
  // Define which content sections belong to which roles
  const roleContent = {
    coach: {
      title: "Team Management Hub",
      sections: ['teams', 'evaluations', 'sessions', 'players']
    },
    player: {
      title: "My Performance Hub", 
      sections: ['stats', 'evaluations', 'schedule', 'achievements']
    },
    parent: {
      title: "Family Hub",
      sections: ['children', 'schedule', 'payments', 'reports']
    }
  };

  const content = roleContent[userRole as keyof typeof roleContent];

  return (
    <PageWithNavigation
      showBackButton={true}
      title={content.title}
      className="bg-gray-900"
    >
      <RoleSpecificContent role={userRole} className="min-h-full">
        <div className="px-4 py-4">
          {/* Render sections based on role */}
          {content.sections.includes('teams') && (
            <div className="mb-6">
              <TeamManagementSection role={userRole} />
            </div>
          )}
          
          {content.sections.includes('stats') && (
            <div className="mb-6">
              <PlayerStatsSection role={userRole} />
            </div>
          )}
          
          {/* etc... */}
        </div>
      </RoleSpecificContent>
    </PageWithNavigation>
  );
};

// Example section components
const TeamManagementSection: React.FC<{ role: string }> = ({ role }) => (
  <div className="bg-gray-800 rounded-xl p-6">
    <h3 className="text-shot-teal text-lg font-semibold mb-4">My Teams</h3>
    <div className="space-y-3">
      <ShadowInfoCard 
        variant="action"
        title="U12 Skylarks"
        description="Next match: Saturday 2PM"
        icon="chevronRight"
        role={role as any}
      />
      <ShadowInfoCard 
        variant="action"
        title="U14 Eagles"
        description="Training: Thursday 5PM"
        icon="chevronRight"
        role={role as any}
      />
    </div>
  </div>
);

const PlayerStatsSection: React.FC<{ role: string }> = ({ role }) => (
  <div className="bg-gray-800 rounded-xl p-6">
    <h3 className="text-shot-teal text-lg font-semibold mb-4">My Stats</h3>
    <div className="grid grid-cols-2 gap-4">
      <ShadowStatCard 
        value={89}
        label="Goals"
        color="green"
        role={role as any}
      />
      <ShadowStatCard 
        value={45}
        label="Assists"
        color="teal"
        role={role as any}
      />
    </div>
  </div>
);
