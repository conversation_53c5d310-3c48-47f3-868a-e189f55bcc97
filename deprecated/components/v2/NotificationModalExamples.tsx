// NotificationModalExamples.tsx - Examples of using ShadowNotificationModal in various scenarios
import React, { useState } from 'react';
import ShadowNotificationModal from '../shadow/ShadowNotificationModal';
import { ShadowButton } from '../shadow/ShadowButton';

// Registration Success Example
export const RegistrationSuccessModal: React.FC = () => {
  const [showWelcome, setShowWelcome] = useState(true);

  return (
    <ShadowNotificationModal
      isOpen={showWelcome}
      title="Account Created!"
      message="Welcome to SHOT! Explore the app or head to the Perform tab to get started."
      buttonText="Got it"
      variant="default"
      onClose={() => {
        setShowWelcome(false);
        // Navigate to dashboard or onboarding
        console.log('Navigate to dashboard');
      }}
    />
  );
};

// Evaluation Submission Example
export const EvaluationSubmittedModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  playerName: string;
}> = ({ isOpen, onClose, playerName }) => {
  return (
    <ShadowNotificationModal
      isOpen={isOpen}
      title="Evaluation Submitted!"
      message={`Your evaluation for ${playerName} has been saved. They will be notified of your feedback.`}
      buttonText="Continue"
      variant="success"
      onClose={onClose}
    />
  );
};

// Error Handler Example
export const ErrorModal: React.FC<{
  error: Error | null;
  onRetry?: () => void;
}> = ({ error, onRetry }) => {
  const [showError, setShowError] = useState(!!error);

  return (
    <ShadowNotificationModal
      isOpen={showError && !!error}
      title="Error Occurred"
      message={error?.message || "Something went wrong. Please try again."}
      buttonText={onRetry ? "Try Again" : "Dismiss"}
      variant="error"
      onClose={() => {
        setShowError(false);
        if (onRetry) {
          onRetry();
        }
      }}
    />
  );
};

// Subscription Warning Example
export const SubscriptionWarningModal: React.FC<{
  daysRemaining: number;
  onUpdatePayment: () => void;
}> = ({ daysRemaining, onUpdatePayment }) => {
  const [showWarning, setShowWarning] = useState(true);

  return (
    <ShadowNotificationModal
      isOpen={showWarning}
      title="Subscription Expiring"
      message={`Your SHOT subscription will expire in ${daysRemaining} days. Update your payment method to continue enjoying all features.`}
      buttonText="Update Payment"
      variant="warning"
      onClose={() => {
        setShowWarning(false);
        onUpdatePayment();
      }}
    />
  );
};

// Team Join Success Example
export const TeamJoinSuccessModal: React.FC<{
  teamName: string;
  isOpen: boolean;
  onClose: () => void;
}> = ({ teamName, isOpen, onClose }) => {
  return (
    <ShadowNotificationModal
      isOpen={isOpen}
      title="Welcome to the Team!"
      message={`You've successfully joined ${teamName}. Check the team schedule to see upcoming events.`}
      buttonText="View Team"
      variant="success"
      onClose={onClose}
    />
  );
};

// Parent Consent Required Example
export const ParentConsentModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
}> = ({ isOpen, onClose }) => {
  return (
    <ShadowNotificationModal
      isOpen={isOpen}
      title="Parent Consent Required"
      message="A consent request has been sent to your parent or guardian. You'll be notified once they approve."
      buttonText="Understood"
      variant="default"
      onClose={onClose}
    />
  );
};

// Demo Component showing all modals
export const NotificationModalDemo: React.FC = () => {
  const [modals, setModals] = useState({
    registration: false,
    evaluation: false,
    error: false,
    subscription: false,
    teamJoin: false,
    consent: false
  });

  const openModal = (modalKey: keyof typeof modals) => {
    setModals(prev => ({ ...prev, [modalKey]: true }));
  };

  const closeModal = (modalKey: keyof typeof modals) => {
    setModals(prev => ({ ...prev, [modalKey]: false }));
  };

  return (
    <div className="p-6 space-y-4">
      <h2 className="text-2xl font-bold text-white mb-6">Notification Modal Examples</h2>
      
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        <ShadowButton
          text="Registration Success"
          variant="secondary"
          onClick={() => openModal('registration')}
        />
        <ShadowButton
          text="Evaluation Submitted"
          variant="secondary"
          onClick={() => openModal('evaluation')}
        />
        <ShadowButton
          text="Show Error"
          variant="secondary"
          onClick={() => openModal('error')}
        />
        <ShadowButton
          text="Subscription Warning"
          variant="secondary"
          onClick={() => openModal('subscription')}
        />
        <ShadowButton
          text="Team Join Success"
          variant="secondary"
          onClick={() => openModal('teamJoin')}
        />
        <ShadowButton
          text="Parent Consent"
          variant="secondary"
          onClick={() => openModal('consent')}
        />
      </div>

      {/* Modals */}
      <ShadowNotificationModal
        isOpen={modals.registration}
        title="Account Created!"
        message="Welcome to SHOT! Explore the app or head to the Perform tab to get started."
        onClose={() => closeModal('registration')}
      />

      <EvaluationSubmittedModal
        isOpen={modals.evaluation}
        playerName="Jamie Smith"
        onClose={() => closeModal('evaluation')}
      />

      <ErrorModal
        error={modals.error ? new Error("Network connection failed") : null}
        onRetry={() => {
          console.log('Retrying...');
          closeModal('error');
        }}
      />

      <SubscriptionWarningModal
        daysRemaining={3}
        onUpdatePayment={() => {
          console.log('Navigate to payment settings');
          closeModal('subscription');
        }}
      />

      <TeamJoinSuccessModal
        teamName="U12 Skylarks"
        isOpen={modals.teamJoin}
        onClose={() => closeModal('teamJoin')}
      />

      <ParentConsentModal
        isOpen={modals.consent}
        onClose={() => closeModal('consent')}
      />
    </div>
  );
};
