import React, { useRef, useState } from 'react';
import { IonIcon } from '@ionic/react';
import { chevronBackOutline, chevronForwardOutline } from 'ionicons/icons';
import MembershipCard from './MembershipCard';
import { Membership, Addon } from '../types/membership';

interface MembershipSliderProps {
  memberships: Membership[];
  includedAddonsByMembership: Map<string, Addon[]>;
  selectedMembershipId: string | null;
  onSelect: (membershipId: string) => void;
}

const MembershipSlider: React.FC<MembershipSliderProps> = ({
  memberships,
  includedAddonsByMembership,
  selectedMembershipId,
  onSelect,
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);

  const scroll = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const scrollAmount = container.clientWidth * 0.8;
      const newScrollLeft = direction === 'left' 
        ? container.scrollLeft - scrollAmount
        : container.scrollLeft + scrollAmount;
      
      container.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      setShowLeftArrow(container.scrollLeft > 0);
      setShowRightArrow(
        container.scrollLeft < container.scrollWidth - container.clientWidth - 10
      );
    }
  };

  return (
    <div className="relative group">
      {/* Left Arrow */}
      {showLeftArrow && (
        <button
          onClick={() => scroll('left')}
          className="absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white"
        >
          <IonIcon icon={chevronBackOutline} />
        </button>
      )}

      {/* Right Arrow */}
      {showRightArrow && (
        <button
          onClick={() => scroll('right')}
          className="absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white"
        >
          <IonIcon icon={chevronForwardOutline} />
        </button>
      )}

      {/* Membership Cards Container */}
      <div
        ref={scrollContainerRef}
        onScroll={handleScroll}
        className="flex overflow-x-auto scrollbar-hide snap-x snap-mandatory gap-4 px-4 py-2"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {memberships.map((membership) => (
          <div
            key={membership.id}
            className="flex-none w-[300px] snap-start"
          >
            <MembershipCard
              membership={membership}
              isPopular={membership.name === 'Premium'}
              includedAddons={includedAddonsByMembership.get(membership.id) || []}
              isSelected={selectedMembershipId === membership.id}
              onSelect={() => onSelect(membership.id)}
            />
          </div>
        ))}
      </div>

      {/* Dots Indicator */}
      <div className="flex justify-center mt-4 gap-2">
        {memberships.map((membership, index) => (
          <div
            key={membership.id}
            className={`h-2 w-2 rounded-full transition-all ${
              selectedMembershipId === membership.id
                ? 'bg-emerald-500 w-4'
                : 'bg-gray-600'
            }`}
          />
        ))}
      </div>
    </div>
  );
};

export default MembershipSlider;