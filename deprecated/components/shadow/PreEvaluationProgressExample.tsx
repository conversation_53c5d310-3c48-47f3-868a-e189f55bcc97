import React from 'react';
import { 
  calculatePreEvaluationPercentage, 
  getPreEvaluationStatusColor,
  formatPreEvaluationText 
} from '../../../utils/preEvaluationUtils';

interface PreEvaluationProgressExampleProps {
  preEvalCompleted: number;
  preEvalTotal: number;
  // DO NOT pass participant count here - use only pre-evaluation counts
}

/**
 * Example component showing how to correctly display pre-evaluation progress
 * This fixes the 29% bug by ensuring we use pre_eval_total, not participant count
 */
export const PreEvaluationProgressExample: React.FC<PreEvaluationProgressExampleProps> = ({
  preEvalCompleted,
  preEvalTotal
}) => {
  // FIX: Calculate percentage using the utility function
  const percentage = calculatePreEvaluationPercentage(preEvalCompleted, preEvalTotal);
  const statusColor = getPreEvaluationStatusColor(percentage, preEvalTotal);
  const statusText = formatPreEvaluationText(preEvalCompleted, preEvalTotal);

  return (
    <div style={{
      padding: '20px',
      background: '#1F1F1F',
      borderRadius: '12px',
      fontFamily: "'Montserrat', sans-serif"
    }}>
      {/* Percentage Display */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '16px',
        marginBottom: '16px'
      }}>
        <div style={{
          fontSize: '48px',
          fontWeight: '800',
          color: statusColor,
          lineHeight: '1'
        }}>
          {percentage}%
        </div>
        <div>
          <div style={{
            fontSize: '12px',
            fontWeight: '600',
            color: '#9CA3AF',
            textTransform: 'uppercase',
            letterSpacing: '0.05em'
          }}>
            PRE-EVALUATIONS COMPLETE
          </div>
          <div style={{
            fontSize: '14px',
            color: '#FFFFFF',
            marginTop: '4px'
          }}>
            {statusText}
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div style={{
        width: '100%',
        height: '8px',
        background: 'rgba(75, 75, 75, 0.3)',
        borderRadius: '4px',
        overflow: 'hidden'
      }}>
        <div style={{
          width: `${percentage}%`,
          height: '100%',
          background: statusColor,
          transition: 'width 0.3s ease'
        }} />
      </div>

      {/* Debug Info (remove in production) */}
      <div style={{
        marginTop: '16px',
        padding: '12px',
        background: 'rgba(75, 75, 75, 0.2)',
        borderRadius: '8px',
        fontSize: '12px',
        color: '#6B7280'
      }}>
        <div>Debug Info:</div>
        <div>Pre-Eval Completed: {preEvalCompleted}</div>
        <div>Pre-Eval Total: {preEvalTotal}</div>
        <div>Calculated Percentage: {percentage}%</div>
        <div style={{ color: '#F7B613', marginTop: '8px' }}>
          ⚠️ Never use participant count for pre-eval percentage!
        </div>
      </div>
    </div>
  );
};

export default PreEvaluationProgressExample;
