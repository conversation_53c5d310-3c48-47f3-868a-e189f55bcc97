import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { ShadowEvaluationComparisonCard, ShadowEvaluationComparisonGrid, convertEvaluationData } from '../../../components/shadow/evaluation';
import type { PlayerEvaluation } from '../../../components/shadow/evaluation';

// Example: Single Player Evaluation Comparison
export const SinglePlayerEvaluationComparison: React.FC<{ playerId: string; eventId: string }> = ({ 
  playerId, 
  eventId 
}) => {
  const [loading, setLoading] = useState(true);
  const [evaluation, setEvaluation] = useState<PlayerEvaluation | null>(null);

  useEffect(() => {
    fetchEvaluationData();
  }, [playerId, eventId]);

  const fetchEvaluationData = async () => {
    try {
      // Fetch player info
      const { data: playerData } = await supabase
        .from('profiles')
        .select('display_name, avatar_url')
        .eq('id', playerId)
        .single();

      // Fetch event info
      const { data: eventData } = await supabase
        .from('events')
        .select('name, event_date')
        .eq('id', eventId)
        .single();

      // Fetch pre-evaluations (self)
      const { data: preEvals } = await supabase
        .from('player_evaluations')
        .select('category, area, player_rating')
        .eq('player_id', playerId)
        .eq('event_id', eventId)
        .not('player_rating', 'is', null);

      // Fetch post-evaluations (coach)
      const { data: postEvals } = await supabase
        .from('player_evaluations')
        .select('category, area, rating')
        .eq('player_id', playerId)
        .eq('event_id', eventId)
        .not('rating', 'is', null);

      if (preEvals && postEvals) {
        const categories = convertEvaluationData(preEvals, postEvals);
        
        setEvaluation({
          playerId,
          playerName: playerData?.display_name || 'Unknown Player',
          eventName: eventData?.name,
          eventDate: new Date(eventData?.event_date).toLocaleDateString(),
          categories
        });
      }
    } catch (error) {
      console.error('Error fetching evaluation data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading...</div>;
  if (!evaluation) return <div>No evaluation data found</div>;

  return (
    <ShadowEvaluationComparisonCard
      categories={evaluation.categories}
      playerName={evaluation.playerName}
      eventName={evaluation.eventName}
      date={evaluation.eventDate}
      variant="detailed"
      onCategoryClick={(category) => {
        console.log('Navigate to category details:', category);
        // Navigate to detailed view
      }}
    />
  );
};

// Example: Team Evaluation Comparison Grid
export const TeamEvaluationComparisonGrid: React.FC<{ teamId: string; eventId: string }> = ({ 
  teamId, 
  eventId 
}) => {
  const [loading, setLoading] = useState(true);
  const [evaluations, setEvaluations] = useState<PlayerEvaluation[]>([]);

  useEffect(() => {
    fetchTeamEvaluations();
  }, [teamId, eventId]);

  const fetchTeamEvaluations = async () => {
    try {
      // Fetch team players
      const { data: teamPlayers } = await supabase
        .from('team_players')
        .select(`
          player_id,
          profiles:player_id (
            display_name,
            avatar_url
          )
        `)
        .eq('team_id', teamId)
        .eq('status', 'active');

      if (!teamPlayers) return;

      // Fetch event info
      const { data: eventData } = await supabase
        .from('events')
        .select('name, event_date')
        .eq('id', eventId)
        .single();

      // Fetch evaluations for all players
      const evaluationPromises = teamPlayers.map(async (tp: any) => {
        const playerId = tp.player_id;
        
        // Fetch pre and post evaluations
        const [preEvalsResult, postEvalsResult] = await Promise.all([
          supabase
            .from('player_evaluations')
            .select('category, area, player_rating')
            .eq('player_id', playerId)
            .eq('event_id', eventId)
            .not('player_rating', 'is', null),
          
          supabase
            .from('player_evaluations')
            .select('category, area, rating')
            .eq('player_id', playerId)
            .eq('event_id', eventId)
            .not('rating', 'is', null)
        ]);

        if (preEvalsResult.data && postEvalsResult.data) {
          const categories = convertEvaluationData(
            preEvalsResult.data,
            postEvalsResult.data
          );

          return {
            playerId,
            playerName: tp.profiles?.display_name || 'Unknown Player',
            eventName: eventData?.name,
            eventDate: new Date(eventData?.event_date).toLocaleDateString(),
            categories
          };
        }
        return null;
      });

      const results = await Promise.all(evaluationPromises);
      setEvaluations(results.filter((e): e is PlayerEvaluation => e !== null));
    } catch (error) {
      console.error('Error fetching team evaluations:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading...</div>;
  if (evaluations.length === 0) return <div>No evaluations found</div>;

  return (
    <ShadowEvaluationComparisonGrid
      evaluations={evaluations}
      columns={2}
      variant="compact"
      onPlayerClick={(evaluation) => {
        // Navigate to player detail
        window.location.href = `/player/${evaluation.playerId}/evaluation-history`;
      }}
      onCategoryClick={(playerId, category) => {
        console.log('Category clicked for player:', playerId, category);
        // Navigate to category detail
      }}
    />
  );
};

// Example: Usage in a coach dashboard
export const CoachEvaluationDashboard: React.FC<{ eventId: string }> = ({ eventId }) => {
  const [selectedTeamId, setSelectedTeamId] = useState<string>('');
  
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Event Evaluation Comparison</h2>
      
      {/* Team selector would go here */}
      
      {selectedTeamId && (
        <TeamEvaluationComparisonGrid 
          teamId={selectedTeamId} 
          eventId={eventId} 
        />
      )}
    </div>
  );
};

// Example: Player progress over multiple events
export const PlayerProgressComparison: React.FC<{ playerId: string }> = ({ playerId }) => {
  const [evaluations, setEvaluations] = useState<PlayerEvaluation[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPlayerProgress();
  }, [playerId]);

  const fetchPlayerProgress = async () => {
    try {
      // Fetch recent events with evaluations
      const { data: events } = await supabase
        .from('player_evaluation_summary_by_event')
        .select('event_id, event_name, event_date')
        .eq('player_id', playerId)
        .order('event_date', { ascending: false })
        .limit(4);

      if (!events) return;

      // Fetch evaluation data for each event
      const evaluationPromises = events.map(async (event: any) => {
        const [preEvalsResult, postEvalsResult] = await Promise.all([
          supabase
            .from('player_evaluations')
            .select('category, area, player_rating')
            .eq('player_id', playerId)
            .eq('event_id', event.event_id)
            .not('player_rating', 'is', null),
          
          supabase
            .from('player_evaluations')
            .select('category, area, rating')
            .eq('player_id', playerId)
            .eq('event_id', event.event_id)
            .not('rating', 'is', null)
        ]);

        if (preEvalsResult.data && postEvalsResult.data) {
          const categories = convertEvaluationData(
            preEvalsResult.data,
            postEvalsResult.data
          );

          return {
            playerId: `${playerId}-${event.event_id}`,
            playerName: event.event_name,
            eventDate: new Date(event.event_date).toLocaleDateString(),
            categories
          };
        }
        return null;
      });

      const results = await Promise.all(evaluationPromises);
      setEvaluations(results.filter((e): e is PlayerEvaluation => e !== null));
    } catch (error) {
      console.error('Error fetching player progress:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading...</div>;

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-white">Evaluation Progress</h3>
      <ShadowEvaluationComparisonGrid
        evaluations={evaluations}
        columns={2}
        variant="compact"
        onPlayerClick={(evaluation) => {
          console.log('View event details:', evaluation);
        }}
      />
    </div>
  );
};