// ABOUTME: Example component demonstrating BigCommerce product listing
// Shows how to fetch and display products from BigCommerce API

import React, { useEffect, useState } from 'react';
import { bigCommerceService } from '../services/BigCommerceService';
import type { BigCommerceProduct } from '../services/BigCommerceService';
import './BigCommerceProductList.css';

interface ProductListState {
  products: BigCommerceProduct[];
  loading: boolean;
  error: string | null;
  page: number;
  totalPages: number;
}

export const BigCommerceProductList: React.FC = () => {
  const [state, setState] = useState<ProductListState>({
    products: [],
    loading: false,
    error: null,
    page: 1,
    totalPages: 1,
  });

  useEffect(() => {
    loadProducts();
  }, [state.page]);

  const loadProducts = async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const response = await bigCommerceService.getProducts({
        page: state.page,
        limit: 10,
        include: ['variants', 'images', 'custom_fields'],
        is_visible: true,
      });

      setState(prev => ({
        ...prev,
        products: response.data,
        totalPages: response.meta.pagination.total_pages,
        loading: false,
      }));
    } catch (error) {
      console.error('Error loading products:', error);
      setState(prev => ({
        ...prev,
        error: 'Failed to load products. Please check your BigCommerce credentials.',
        loading: false,
      }));
    }
  };

  const handlePageChange = (newPage: number) => {
    setState(prev => ({ ...prev, page: newPage }));
  };

  const formatPrice = (price: string | undefined) => {
    if (!price) return 'N/A';
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
    }).format(parseFloat(price));
  };

  if (state.loading) {
    return <div className="bigcommerce-loading">Loading products...</div>;
  }

  if (state.error) {
    return (
      <div className="bigcommerce-error">
        <p>{state.error}</p>
        <button onClick={loadProducts}>Retry</button>
      </div>
    );
  }

  return (
    <div className="bigcommerce-product-list">
      <h2>BigCommerce Products</h2>
      
      {state.products.length === 0 ? (
        <p>No products found.</p>
      ) : (
        <>
          <div className="product-grid">
            {state.products.map((product) => (
              <div key={product.id} className="product-card">
                {product.images && product.images[0] && (
                  <img 
                    src={product.images[0].url_standard} 
                    alt={product.name}
                    className="product-image"
                  />
                )}
                <h3>{product.name}</h3>
                <p className="product-sku">SKU: {product.sku}</p>
                <p className="product-description">{product.description}</p>
                <div className="product-price">
                  {product.sale_price ? (
                    <>
                      <span className="original-price">{formatPrice(product.price)}</span>
                      <span className="sale-price">{formatPrice(product.sale_price)}</span>
                    </>
                  ) : (
                    <span>{formatPrice(product.price)}</span>
                  )}
                </div>
                {product.inventory_level !== undefined && (
                  <p className="product-stock">
                    Stock: {product.inventory_level > 0 ? `${product.inventory_level} available` : 'Out of stock'}
                  </p>
                )}
                {product.variants && product.variants.length > 0 && (
                  <div className="product-variants">
                    <p>Available variants: {product.variants.length}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="pagination">
            <button 
              onClick={() => handlePageChange(state.page - 1)}
              disabled={state.page === 1}
            >
              Previous
            </button>
            <span>Page {state.page} of {state.totalPages}</span>
            <button 
              onClick={() => handlePageChange(state.page + 1)}
              disabled={state.page === state.totalPages}
            >
              Next
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default BigCommerceProductList;