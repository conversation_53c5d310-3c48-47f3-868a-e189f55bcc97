import React, { useState, useRef, useEffect } from 'react';
import { IonIcon, IonChip } from '@ionic/react';
import { chevronBackOutline, chevronForwardOutline } from 'ionicons/icons';
import AddonCard from './AddonCard';
import { Addon } from '../types/membership';

interface AddonSelectorProps {
  addons: Addon[];
  allowedAddonIds: string[];
  includedAddonIds: string[];
  selectedAddonIds: Set<string>;
  onToggleAddon: (addonId: string) => void;
}

const AddonSelector: React.FC<AddonSelectorProps> = ({
  addons,
  allowedAddonIds,
  includedAddonIds,
  selectedAddonIds,
  onToggleAddon,
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);

  // Get unique categories
  const categories = ['all', ...new Set(addons.map(addon => addon.category))];

  // Filter addons by category
  const filteredAddons = selectedCategory === 'all'
    ? addons
    : addons.filter(addon => addon.category === selectedCategory);

  const scroll = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const scrollAmount = container.clientWidth * 0.8;
      const newScrollLeft = direction === 'left' 
        ? container.scrollLeft - scrollAmount
        : container.scrollLeft + scrollAmount;
      
      container.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      setShowLeftArrow(container.scrollLeft > 0);
      setShowRightArrow(
        container.scrollLeft < container.scrollWidth - container.clientWidth - 10
      );
    }
  };

  useEffect(() => {
    handleScroll();
    window.addEventListener('resize', handleScroll);
    return () => window.removeEventListener('resize', handleScroll);
  }, [filteredAddons]);

  return (
    <div>
      {/* Category Tabs */}
      <div className="flex overflow-x-auto scrollbar-hide gap-2 mb-6 px-4">
        {categories.map((category) => (
          <IonChip
            key={category}
            color={selectedCategory === category ? 'primary' : 'medium'}
            onClick={() => setSelectedCategory(category)}
            className={`capitalize ${
              selectedCategory === category 
                ? 'bg-emerald-600 text-white' 
                : 'bg-gray-700 text-gray-300'
            }`}
          >
            {category}
          </IonChip>
        ))}
      </div>

      {/* Add-ons Slider */}
      <div className="relative group">
        {showLeftArrow && (
          <button
            onClick={() => scroll('left')}
            className="absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white"
          >
            <IonIcon icon={chevronBackOutline} />
          </button>
        )}

        {showRightArrow && (
          <button
            onClick={() => scroll('right')}
            className="absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white"
          >
            <IonIcon icon={chevronForwardOutline} />
          </button>
        )}

        <div
          ref={scrollContainerRef}
          onScroll={handleScroll}
          className="flex overflow-x-auto scrollbar-hide snap-x snap-mandatory gap-4 px-4"
          style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
        >
          {filteredAddons.map((addon) => (
            <div
              key={addon.id}
              className="flex-none w-[300px] snap-start"
            >
              <AddonCard
                addon={addon}
                isAllowed={allowedAddonIds.includes(addon.id)}
                isIncluded={includedAddonIds.includes(addon.id)}
                isSelected={selectedAddonIds.has(addon.id)}
                onSelect={() => onToggleAddon(addon.id)}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AddonSelector;