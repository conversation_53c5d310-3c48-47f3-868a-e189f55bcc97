// ABOUTME: React wrapper component and HOC that displays component file paths using existing debug mode
// Integrates with the superadmin debug mode to show component paths above each component

import React from 'react';
import { isComponentPathDebugEnabled, getComponentPathStyles } from '../utils/debugMode';

interface ComponentDebugWrapperProps {
  children: React.ReactNode;
  componentPath: string;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * Wrapper component that shows the component file path when debug mode is enabled
 */
export const ComponentDebugWrapper: React.FC<ComponentDebugWrapperProps> = ({
  children,
  componentPath,
  className = '',
  style = {}
}) => {
  // Only show path if component path debugging is enabled
  if (!isComponentPathDebugEnabled()) {
    return <>{children}</>;
  }

  // Extract just the filename from the path for display
  const displayPath = componentPath.startsWith('src/') 
    ? componentPath 
    : `src/${componentPath}`;

  return (
    <div className={`component-debug-wrapper ${className}`} style={style}>
      <style dangerouslySetInnerHTML={{ __html: getComponentPathStyles() }} />
      <div className="component-debug-path" title={displayPath}>
        {displayPath}
      </div>
      {children}
    </div>
  );
};

/**
 * Higher-Order Component that wraps a component with debug path display
 * Usage: export default withDebugPath(MyComponent, 'components/MyComponent.tsx');
 */
export function withDebugPath<P extends object>(
  Component: React.ComponentType<P>,
  componentPath: string
): React.ComponentType<P> {
  const WrappedComponent = React.forwardRef<any, P>((props, ref) => (
    <ComponentDebugWrapper componentPath={componentPath}>
      <Component {...props} ref={ref} />
    </ComponentDebugWrapper>
  ));

  // Set display name for debugging
  WrappedComponent.displayName = `withDebugPath(${Component.displayName || Component.name || 'Component'})`;

  return WrappedComponent;
}

/**
 * Hook to conditionally apply debug wrapper
 * Useful for components that need conditional wrapping
 */
export const useDebugPath = (componentPath: string) => {
  const isEnabled = isComponentPathDebugEnabled();

  const DebugWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    if (!isEnabled) return <>{children}</>;
    
    return (
      <ComponentDebugWrapper componentPath={componentPath}>
        {children}
      </ComponentDebugWrapper>
    );
  };

  return { DebugWrapper, isEnabled };
};