import React, { useEffect } from 'react';

export const ScrollDebug: React.FC = () => {
  useEffect(() => {
    const checkScrollability = () => {
      const elements = [
        { name: 'html', el: document.documentElement },
        { name: 'body', el: document.body },
        { name: 'ion-app', el: document.querySelector('ion-app') },
        { name: 'ion-content', el: document.querySelector('ion-content') },
        { name: 'ion-router-outlet', el: document.querySelector('ion-router-outlet') },
        { name: '.v2-page', el: document.querySelector('.v2-page') }
      ];

      console.group('🔍 Scroll Debug');
      elements.forEach(({ name, el }) => {
        if (el) {
          const styles = window.getComputedStyle(el);
          console.log(`${name}:`, {
            overflow: styles.overflow,
            overflowY: styles.overflowY,
            height: styles.height,
            maxHeight: styles.maxHeight,
            position: styles.position,
            contain: styles.contain
          });
        }
      });
      console.groupEnd();
    };

    // Check on mount
    checkScrollability();

    // Check again after a delay
    setTimeout(checkScrollability, 1000);
  }, []);

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      zIndex: 99999
    }}>
      Check console for scroll debug info
    </div>
  );
};