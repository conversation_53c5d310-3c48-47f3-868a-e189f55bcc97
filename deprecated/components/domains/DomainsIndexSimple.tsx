import React from 'react';
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonBackButton, IonButtons } from '@ionic/react';

const DomainsIndexSimple: React.FC = () => {
  console.log('[DOMAINS INDEX SIMPLE] Component rendering');
  
  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonButtons slot="start">
            <IonBackButton defaultHref="/home" />
          </IonButtons>
          <IonTitle>Domain Mockups</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent>
        <div style={{ padding: '20px' }}>
          <h1>Domain Mockups Index</h1>
          <p>If you see this, the component is working!</p>
          <ul>
            <li><a href="/domains/identity-profile">Identity & Profile</a></li>
            <li><a href="/domains/organization">Organization</a></li>
            <li><a href="/domains/people-roles">People & Roles</a></li>
            <li><a href="/domains/communication">Communication</a></li>
            <li><a href="/domains/evaluation">Evaluation</a></li>
          </ul>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default DomainsIndexSimple;
