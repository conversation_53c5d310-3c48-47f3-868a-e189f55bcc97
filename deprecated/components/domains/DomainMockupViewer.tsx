import React, { useEffect, useState } from 'react';
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonBackButton, IonButtons, IonSpinner } from '@ionic/react';
import { useHistory } from 'react-router-dom';

interface DomainMockupViewerProps {
  mockupPath: string;
  title: string;
}

const DomainMockupViewer: React.FC<DomainMockupViewerProps> = ({ mockupPath, title }) => {
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const history = useHistory();

  useEffect(() => {
    const loadMockup = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Construct the full path
        const fullPath = `/src/domains/${mockupPath}`;
        
        // Fetch the HTML file
        const response = await fetch(fullPath);
        if (!response.ok) {
          throw new Error(`Failed to load mockup: ${response.statusText}`);
        }
        
        let html = await response.text();
        
        // Modify the HTML to fix relative paths
        // Update CSS path to absolute
        html = html.replace(
          /href="\.\.\/\.\.\/theme\/shot-brand-enhanced\.css"/g,
          'href="/src/theme/shot-brand-enhanced.css"'
        );
        
        // Update any other relative paths if needed
        html = html.replace(/src="\.\.\/\.\.\//g, 'src="/src/');
        html = html.replace(/href="\.\.\/\.\.\//g, 'href="/src/');
        
        setHtmlContent(html);
      } catch (err) {
        console.error('Error loading mockup:', err);
        setError(err instanceof Error ? err.message : 'Failed to load mockup');
      } finally {
        setLoading(false);
      }
    };

    loadMockup();
  }, [mockupPath]);

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar style={{ '--background': 'var(--shot-black)' }}>
          <IonButtons slot="start">
            <IonBackButton defaultHref="/domains" />
          </IonButtons>
          <IonTitle>{title}</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent fullscreen style={{ '--background': 'var(--shot-black)' }}>
        {loading && (
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '100%' 
          }}>
            <IonSpinner name="crescent" color="primary" />
          </div>
        )}
        
        {error && (
          <div style={{ 
            padding: '20px', 
            textAlign: 'center',
            color: 'var(--shot-white)'
          }}>
            <h2>Error Loading Mockup</h2>
            <p>{error}</p>
            <p>Path: {mockupPath}</p>
          </div>
        )}
        
        {!loading && !error && htmlContent && (
          <iframe
            srcDoc={htmlContent}
            style={{
              width: '100%',
              height: '100%',
              border: 'none',
              background: 'var(--shot-black)'
            }}
            title={title}
          />
        )}
      </IonContent>
    </IonPage>
  );
};

export default DomainMockupViewer;
