import React from 'react';
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonBackButton, IonButtons } from '@ionic/react';

// Import HTML files as raw strings
// Note: You may need to add ?raw to these imports or configure Vite to handle HTML imports
// @ts-ignore
import identityProfileHtml from '../../domains/identity-profile/profile-management.html?raw';
// @ts-ignore
import organizationHtml from '../../domains/organization/team-management.html?raw';
// @ts-ignore
import peopleRolesHtml from '../../domains/people-roles/role-management.html?raw';
// @ts-ignore
import communicationHtml from '../../domains/communication/communication-hub.html?raw';
// @ts-ignore
import evaluationHtml from '../../domains/evaluation/evaluation-flow.html?raw';

interface DomainMockupPageProps {
  title: string;
  htmlContent: string;
}

const DomainMockupPage: React.FC<DomainMockupPageProps> = ({ title, htmlContent }) => {
  // Update relative paths in the HTML content
  const updatedHtml = htmlContent
    .replace(/href="\.\.\/\.\.\/theme\/shot-brand-enhanced\.css"/g, 'href="/src/theme/shot-brand-enhanced.css"')
    .replace(/src="\.\.\/\.\.\//g, 'src="/src/')
    .replace(/href="\.\.\/\.\.\//g, 'href="/src/');

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar style={{ '--background': 'var(--shot-black)' }}>
          <IonButtons slot="start">
            <IonBackButton defaultHref="/domains" />
          </IonButtons>
          <IonTitle>{title}</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent fullscreen style={{ '--background': 'var(--shot-black)' }}>
        <iframe
          srcDoc={updatedHtml}
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            background: 'var(--shot-black)'
          }}
          title={title}
        />
      </IonContent>
    </IonPage>
  );
};

// Individual domain mockup components
export const IdentityProfileMockup: React.FC = () => (
  <DomainMockupPage 
    title="Identity & Profile Domain" 
    htmlContent={identityProfileHtml}
  />
);

export const OrganizationMockup: React.FC = () => (
  <DomainMockupPage 
    title="Organization Domain" 
    htmlContent={organizationHtml}
  />
);

export const PeopleRolesMockup: React.FC = () => (
  <DomainMockupPage 
    title="People & Roles Domain" 
    htmlContent={peopleRolesHtml}
  />
);

export const CommunicationMockup: React.FC = () => (
  <DomainMockupPage 
    title="Communication Domain" 
    htmlContent={communicationHtml}
  />
);

export const EvaluationMockup: React.FC = () => (
  <DomainMockupPage 
    title="Evaluation Domain" 
    htmlContent={evaluationHtml}
  />
);

export default DomainMockupPage;
