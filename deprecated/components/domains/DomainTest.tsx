import React from 'react';
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonBackButton, IonButtons } from '@ionic/react';

const DomainTest: React.FC = () => {
  return (
    <IonPage>
      <IonHeader>
        <IonToolbar style={{ '--background': 'var(--shot-black)' }}>
          <IonButtons slot="start">
            <IonBackButton defaultHref="/home" />
          </IonButtons>
          <IonTitle>Domain Routes Test</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent fullscreen style={{ '--background': 'var(--shot-black)' }}>
        <div style={{ padding: '20px', color: 'white' }}>
          <h1>Domain Routes Working!</h1>
          <p>If you see this page, the domain routes are properly configured.</p>
          <p>Current path: {window.location.pathname}</p>
          <div style={{ marginTop: '20px' }}>
            <a href="/domains" style={{ color: 'var(--shot-teal)' }}>Go to Domains Index</a>
          </div>
        </div>
      </IonContent>
    </IonPage>
  );
};

export default DomainTest;
