import React, { useState } from 'react';
import { IonButton, IonInput, IonItem, IonLabel, IonCard, IonCardContent, IonCardHeader, IonCardTitle } from '@ionic/react';
import { supabase } from '@/lib/supabase';
import { verifyRegistrationData, fixRegistrationData } from '../utils/registrationDebug';

const RegistrationDebugger: React.FC = () => {
  const [userId, setUserId] = useState('');
  const [email, setEmail] = useState('');
  const [results, setResults] = useState<any>(null);
  
  // For fix function
  const [fullName, setFullName] = useState('');
  const [avatarUrl, setAvatarUrl] = useState('');
  const [teamId, setTeamId] = useState('');
  const [teamName, setTeamName] = useState('');
  
  const handleVerify = async () => {
    setResults(null);
    
    try {
      // If email is provided, look up user ID first
      if (email && !userId) {
        const { data, error } = await supabase
          .from('users_view')
          .select('id')
          .eq('email', email)
          .single();
          
        if (error) {
          console.error('Error finding user by email:', error);
          setResults({ error: 'User not found with this email' });
          return;
        }
        
        if (data) {
          setUserId(data.id);
        }
      }
      
      // Now verify with the user ID
      if (userId) {
        // We're just logging to console in the verification function
        await verifyRegistrationData(supabase, userId);
        setResults({ success: true, message: 'Verification complete - check console for results' });
      } else {
        setResults({ error: 'Please provide either a user ID or email' });
      }
    } catch (error) {
      console.error('Verification error:', error);
      setResults({ error: 'An error occurred during verification' });
    }
  };
  
  const handleFix = async () => {
    if (!userId) {
      setResults({ error: 'User ID is required for fixes' });
      return;
    }
    
    try {
      await fixRegistrationData(supabase, userId, {
        fullName,
        avatarUrl,
        teamId,
        teamName
      });
      
      setResults({ success: true, message: 'Fix applied - check console for results' });
    } catch (error) {
      console.error('Fix error:', error);
      setResults({ error: 'An error occurred during the fix process' });
    }
  };
  
  return (
    <div className="p-4">
      <h1 className="text-xl font-bold mb-4">Registration Debugger</h1>
      
      <div className="mb-6">
        <h2 className="text-lg font-medium mb-2">Verify Registration Data</h2>
        <IonItem>
          <IonLabel position="floating">User ID</IonLabel>
          <IonInput value={userId} onIonChange={e => setUserId(e.detail.value!)} />
        </IonItem>
        
        <IonItem className="mt-2">
          <IonLabel position="floating">Or Email</IonLabel>
          <IonInput value={email} onIonChange={e => setEmail(e.detail.value!)} />
        </IonItem>
        
        <IonButton className="mt-4" onClick={handleVerify}>Verify User Data</IonButton>
      </div>
      
      <div className="mb-6">
        <h2 className="text-lg font-medium mb-2">Fix Registration Data</h2>
        <IonItem>
          <IonLabel position="floating">Full Name</IonLabel>
          <IonInput value={fullName} onIonChange={e => setFullName(e.detail.value!)} />
        </IonItem>
        
        <IonItem className="mt-2">
          <IonLabel position="floating">Avatar URL</IonLabel>
          <IonInput value={avatarUrl} onIonChange={e => setAvatarUrl(e.detail.value!)} />
        </IonItem>
        
        <IonItem className="mt-2">
          <IonLabel position="floating">Team ID</IonLabel>
          <IonInput value={teamId} onIonChange={e => setTeamId(e.detail.value!)} />
        </IonItem>
        
        <IonItem className="mt-2">
          <IonLabel position="floating">Team Name</IonLabel>
          <IonInput value={teamName} onIonChange={e => setTeamName(e.detail.value!)} />
        </IonItem>
        
        <IonButton className="mt-4" onClick={handleFix}>Apply Fix</IonButton>
      </div>
      
      {results && (
        <IonCard>
          <IonCardHeader>
            <IonCardTitle>{results.success ? 'Success' : 'Error'}</IonCardTitle>
          </IonCardHeader>
          <IonCardContent>
            {results.message || results.error}
          </IonCardContent>
        </IonCard>
      )}
    </div>
  );
};

export default RegistrationDebugger;
