.custom-content {
  --background: #000;
  --padding-bottom: 60px;
}

.scrollable-content {
  overflow-y: auto;
  height: 100%;
  padding-bottom: 80px;
}

/* Ensure the IonContent fills the entire screen */
ion-content {
  --overflow: hidden;
}

/* Add some additional margin to the bottom for better spacing */
.bottom-spacing {
  margin-bottom: 100px;
}

/* Skill bar animations */
.skill-bar {
  transition: width 1s ease-in-out;
}

/* Category section styling */
.category-section {
  transition: all 0.3s ease;
}

.category-section:hover {
  transform: translateX(5px);
}

/* Make rating bars interactive */
.skill-bar-container:hover .skill-bar {
  filter: brightness(1.2);
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.2);
}

/* Tooltip animations for performance chart */
.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group-hover\:opacity-100 {
  transition: opacity 0.2s ease-in-out;
}

/* Animation for bar hover */
.hover\:w-1\.5:hover {
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

/* Time series graph animations */
@keyframes draw-line {
  0% {
    stroke-dashoffset: 1000;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes fade-in-dots {
  0% {
    opacity: 0;
  }
  70% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.graph-line {
  stroke-dasharray: 1000;
  stroke-dashoffset: 1000;
  animation: draw-line 2.5s ease-out forwards;
}

.technical-line {
  animation-delay: 0.1s;
}

.physical-line {
  animation-delay: 0.3s;
}

.psychological-line {
  animation-delay: 0.5s;
}

.social-line {
  animation-delay: 0.7s;
}

.positional-line {
  animation-delay: 0.9s;
}

.data-points circle {
  opacity: 0;
  animation: fade-in-dots 3s ease-in-out forwards;
}

.technical-points circle {
  animation-delay: 1.2s;
}

.physical-points circle {
  animation-delay: 1.4s;
}

.psychological-points circle {
  animation-delay: 1.6s;
}

.social-points circle {
  animation-delay: 1.8s;
}

.positional-points circle {
  animation-delay: 2.0s;
}

/* Hover effects for graph lines */
.graph-line:hover, .graph-line.highlighted {
  stroke-width: 3;
  filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.5));
}

.technical-line:hover, .technical-line.highlighted {
  stroke: #60a5fa;
}

.physical-line:hover, .physical-line.highlighted {
  stroke: #f87171;
}

.psychological-line:hover, .psychological-line.highlighted {
  stroke: #fcd34d;
}

.social-line:hover, .social-line.highlighted {
  stroke: #4ade80;
}

.positional-line:hover, .positional-line.highlighted {
  stroke: #c084fc;
}
