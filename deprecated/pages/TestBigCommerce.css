/* BigCommerce Test Page Styles */

.test-bigcommerce-page {
  min-height: 100vh;
  background-color: var(--ion-background-color, #f5f5f5);
}

.page-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.test-info-card {
  margin-bottom: 30px;
}

.test-description {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-description p {
  margin-bottom: 10px;
  color: var(--ion-text-color, #333);
}

.test-description ul {
  margin-left: 20px;
  color: var(--ion-text-color, #333);
}

.test-description li {
  margin-bottom: 5px;
}

.test-button-container {
  margin-bottom: 30px;
}

.test-results {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-results.success {
  border-left: 4px solid #10dc60;
}

.test-results.error {
  border-left: 4px solid #f04141;
}

.test-results h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--ion-text-color, #333);
}

.test-output {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Courier New', Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
}

.log-line {
  margin-bottom: 5px;
  color: #333;
}

.log-line.error-line {
  color: #f04141;
  font-weight: bold;
}

.next-steps-card,
.troubleshooting-card {
  margin-bottom: 30px;
}

.next-steps-content,
.troubleshooting-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-top: -10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.next-steps-content p,
.troubleshooting-content p {
  margin-bottom: 10px;
  color: var(--ion-text-color, #333);
}

.next-steps-content ul,
.troubleshooting-content ul {
  margin-left: 20px;
  color: var(--ion-text-color, #333);
}

.next-steps-content li,
.troubleshooting-content li {
  margin-bottom: 5px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .test-bigcommerce-page {
    background-color: #1e1e1e;
  }

  .test-description,
  .test-results,
  .next-steps-content,
  .troubleshooting-content {
    background: #2a2a2a;
  }

  .test-output {
    background: #1a1a1a;
  }

  .log-line {
    color: #e0e0e0;
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .page-content {
    padding: 15px;
  }

  .test-description,
  .test-results,
  .next-steps-content,
  .troubleshooting-content {
    padding: 15px;
  }

  .test-output {
    font-size: 12px;
    padding: 10px;
  }
}