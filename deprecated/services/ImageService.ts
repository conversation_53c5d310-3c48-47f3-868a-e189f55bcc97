/**
 * Image Service for handling image-related operations
 */

// Array of club logo paths - in a real app, these would be served from an API
export const clubLogos = {
  'arsenal': '/assets/clubLogos/arsenal.png',
  'chelsea': '/assets/clubLogos/chelsea.png',
  'manunited': '/assets/clubLogos/manunited.png',
  'liverpool': '/assets/clubLogos/liverpool.png',
  'spurs': '/assets/clubLogos/spurs.png',
  'westham': '/assets/clubLogos/westham.png',
  'everton': '/assets/clubLogos/everton.png',
  'leicester': '/assets/clubLogos/leicester.png',
  'astonvilla': '/assets/clubLogos/astonvilla.png',
  'generic': '/assets/clubLogos/generic.png'
};

/**
 * Returns a fallback image URL for a specific entity type
 * @param entityType The type of entity (club, player, coach, etc.)
 * @returns URL to the fallback image
 */
export const getFallbackImage = (entityType: 'club' | 'player' | 'coach'): string => {
  switch (entityType) {
    case 'club':
      return '/assets/clubLogos/generic.png';
    case 'player':
      return '/assets/avatars/player_generic.png';
    case 'coach':
      return '/assets/avatars/coach_generic.png';
    default:
      return '/assets/avatars/generic.png';
  }
};

/**
 * Function to check if an image exists
 * @param url URL of the image to check
 * @returns Promise that resolves to true if image exists, false otherwise
 */
export const imageExists = async (url: string): Promise<boolean> => {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error(`Error checking if image exists at ${url}:`, error);
    return false;
  }
};

/**
 * Function to get a resolved image URL, falling back to a placeholder if the image doesn't exist
 * @param url Original image URL
 * @param fallbackUrl Fallback URL to use if the original doesn't exist
 * @returns Promise that resolves to the final URL to use
 */
export const getResolvedImageUrl = async (url: string, fallbackUrl: string): Promise<string> => {
  if (!url) return fallbackUrl;
  
  const exists = await imageExists(url);
  return exists ? url : fallbackUrl;
};
