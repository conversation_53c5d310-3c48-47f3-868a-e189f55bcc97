// TeamStatisticsService.ts
// Service for managing team statistics

import { supabase } from '@/lib/supabase';
import { MatchStatistics, PlayerMatchPerformance } from '../models/TeamManagementModels';
import { v4 as uuidv4 } from 'uuid';

/**
 * Get team statistics overview
 */
export async function getTeamStatisticsOverview(
  teamId: string,
  seasonId?: string
): Promise<{
  wins: number;
  draws: number;
  losses: number;
  goalsScored: number;
  goalsConceded: number;
  cleanSheets: number;
}> {
  let query = supabase
    .from('match_statistics')
    .select('*')
    .eq('team_id', teamId);
  
  if (seasonId) {
    // If season ID is provided, filter by it
    // This assumes you store seasonId in match_statistics
    query = query.eq('season_id', seasonId);
  }
  
  const { data, error } = await query;
  
  if (error) throw error;
  
  // Initialize stats
  const stats = {
    wins: 0,
    draws: 0,
    losses: 0,
    goalsScored: 0,
    goalsConceded: 0,
    cleanSheets: 0
  };
  
  // Process match data
  data.forEach(match => {
    // Parse result (assuming format like "2-1")
    const [goalsFor, goalsAgainst] = match.result.split('-').map(Number);
    
    // Update stats
    stats.goalsScored += goalsFor;
    stats.goalsConceded += goalsAgainst;
    
    if (goalsFor > goalsAgainst) {
      stats.wins += 1;
    } else if (goalsFor === goalsAgainst) {
      stats.draws += 1;
    } else {
      stats.losses += 1;
    }
    
    if (goalsAgainst === 0) {
      stats.cleanSheets += 1;
    }
  });
  
  return stats;
}

/**
 * Record match statistics
 */
export async function recordMatchStatistics(
  matchData: Omit<MatchStatistics, 'match_id' | 'created_at'>,
  playerPerformances: Array<Omit<PlayerMatchPerformance, 'performance_id' | 'match_id' | 'created_at'>>
): Promise<MatchStatistics> {
  // Start a transaction
  const { data: client } = await supabase.rpc('begin_transaction');
  
  try {
    // Create match statistics record
    const matchId = uuidv4();
    const { data, error } = await supabase
      .from('match_statistics')
      .insert([{
        match_id: matchId,
        team_id: matchData.team_id,
        opponent: matchData.opponent,
        match_date: matchData.match_date,
        result: matchData.result,
        competition: matchData.competition,
        location: matchData.location,
        possession: matchData.possession,
        shots: matchData.shots,
        shots_on_target: matchData.shots_on_target,
        corners: matchData.corners,
        free_kicks: matchData.free_kicks,
        yellow_cards: matchData.yellow_cards,
        red_cards: matchData.red_cards,
        notes: matchData.notes,
        created_by: matchData.created_by,
        created_at: new Date()
      }])
      .select();
    
    if (error) throw error;
    
    // Create player performance records
    if (playerPerformances.length > 0) {
      const performanceRecords = playerPerformances.map(performance => ({
        performance_id: uuidv4(),
        match_id: matchId,
        player_id: performance.player_id,
        goals: performance.goals,
        assists: performance.assists,
        minutes_played: performance.minutes_played,
        rating: performance.rating,
        position_played: performance.position_played,
        notes: performance.notes,
        created_by: matchData.created_by,
        created_at: new Date()
      }));
      
      const { error: perfError } = await supabase
        .from('player_match_performance')
        .insert(performanceRecords);
      
      if (perfError) throw perfError;
    }
    
    // Update team season statistics
    // This would typically update aggregated stats in the team_seasons table
    // Implementation depends on your data model
    
    // Commit transaction
    await supabase.rpc('commit_transaction');
    
    // Return the created match with player performances
    return {
      ...data[0],
      player_performances: playerPerformances.map(perf => ({
        ...perf,
        performance_id: uuidv4(), // This is just a placeholder
        match_id: matchId,
        created_by: matchData.created_by,
        created_at: new Date()
      }))
    };
  } catch (error) {
    // Rollback transaction
    await supabase.rpc('rollback_transaction');
    throw error;
  }
}

/**
 * Get match details
 */
export async function getMatchDetails(matchId: string): Promise<MatchStatistics> {
  // Get match statistics
  const { data: match, error } = await supabase
    .from('match_statistics')
    .select('*')
    .eq('match_id', matchId)
    .single();
  
  if (error) throw error;
  
  // Get player performances for this match
  const { data: performances, error: perfError } = await supabase
    .from('player_match_performance')
    .select('*')
    .eq('match_id', matchId);
  
  if (perfError) throw perfError;
  
  return {
    ...match,
    player_performances: performances || []
  };
}

/**
 * Get team matches
 */
export async function getTeamMatches(
  teamId: string,
  seasonId?: string,
  limit?: number,
  offset?: number
): Promise<MatchStatistics[]> {
  let query = supabase
    .from('match_statistics')
    .select('*')
    .eq('team_id', teamId)
    .order('match_date', { ascending: false });
  
  if (seasonId) {
    query = query.eq('season_id', seasonId);
  }
  
  if (limit) {
    query = query.limit(limit);
  }
  
  if (offset) {
    query = query.range(offset, offset + (limit || 10) - 1);
  }
  
  const { data, error } = await query;
  
  if (error) throw error;
  return data || [];
}

/**
 * Get player performance statistics
 */
export async function getPlayerStatistics(
  playerId: string,
  teamId: string,
  seasonId?: string
): Promise<{
  matches: number;
  goals: number;
  assists: number;
  minutesPlayed: number;
  averageRating: number;
}> {
  // Get all performances for this player
  let query = supabase
    .from('player_match_performance')
    .select('*, match:match_statistics!inner(*)')
    .eq('player_id', playerId)
    .eq('match_statistics.team_id', teamId);
  
  if (seasonId) {
    query = query.eq('match_statistics.season_id', seasonId);
  }
  
  const { data, error } = await query;
  
  if (error) throw error;
  
  // Calculate statistics
  const statistics = {
    matches: data.length,
    goals: 0,
    assists: 0,
    minutesPlayed: 0,
    averageRating: 0
  };
  
  if (data.length > 0) {
    data.forEach(perf => {
      statistics.goals += perf.goals || 0;
      statistics.assists += perf.assists || 0;
      statistics.minutesPlayed += perf.minutes_played || 0;
      statistics.averageRating += perf.rating || 0;
    });
    
    statistics.averageRating = statistics.averageRating / data.length;
  }
  
  return statistics;
}

/**
 * Get team performance trends
 */
export async function getTeamPerformanceTrends(
  teamId: string,
  limit: number = 10
): Promise<any[]> {
  const { data, error } = await supabase
    .from('match_statistics')
    .select('*')
    .eq('team_id', teamId)
    .order('match_date', { ascending: false })
    .limit(limit);
  
  if (error) throw error;
  
  // Process data to create trend information
  // This would depend on your specific requirements
  const trends = data.map(match => {
    const [goalsFor, goalsAgainst] = match.result.split('-').map(Number);
    
    return {
      date: match.match_date,
      opponent: match.opponent,
      goalsFor,
      goalsAgainst,
      possession: match.possession,
      shots: match.shots,
      shotsOnTarget: match.shots_on_target,
      result: goalsFor > goalsAgainst ? 'W' : goalsFor < goalsAgainst ? 'L' : 'D'
    };
  });
  
  return trends;
}

/**
 * Update match statistics
 */
export async function updateMatchStatistics(
  matchId: string,
  updates: Partial<MatchStatistics>,
  playerPerformances?: Array<Partial<PlayerMatchPerformance> & { performance_id?: string }>
): Promise<MatchStatistics> {
  // Start a transaction
  const { data: client } = await supabase.rpc('begin_transaction');
  
  try {
    // Update match statistics
    const { data: match, error } = await supabase
      .from('match_statistics')
      .update(updates)
      .eq('match_id', matchId)
      .select();
    
    if (error) throw error;
    
    // Update player performances if provided
    if (playerPerformances && playerPerformances.length > 0) {
      for (const performance of playerPerformances) {
        if (performance.performance_id) {
          // Update existing performance
          const { error: perfError } = await supabase
            .from('player_match_performance')
            .update({
              goals: performance.goals,
              assists: performance.assists,
              minutes_played: performance.minutes_played,
              rating: performance.rating,
              position_played: performance.position_played,
              notes: performance.notes
            })
            .eq('performance_id', performance.performance_id);
          
          if (perfError) throw perfError;
        } else if (performance.player_id) {
          // Create new performance
          const { error: perfError } = await supabase
            .from('player_match_performance')
            .insert([{
              performance_id: uuidv4(),
              match_id: matchId,
              player_id: performance.player_id,
              goals: performance.goals || 0,
              assists: performance.assists || 0,
              minutes_played: performance.minutes_played || 0,
              rating: performance.rating || 0,
              position_played: performance.position_played,
              notes: performance.notes,
              created_by: updates.created_by,
              created_at: new Date()
            }]);
          
          if (perfError) throw perfError;
        }
      }
    }
    
    // Commit transaction
    await supabase.rpc('commit_transaction');
    
    // Get updated match with all performances
    return await getMatchDetails(matchId);
  } catch (error) {
    // Rollback transaction
    await supabase.rpc('rollback_transaction');
    throw error;
  }
}

/**
 * Delete match statistics
 */
export async function deleteMatchStatistics(matchId: string): Promise<void> {
  // Start a transaction
  const { data: client } = await supabase.rpc('begin_transaction');
  
  try {
    // Delete player performances first (foreign key constraint)
    const { error: perfError } = await supabase
      .from('player_match_performance')
      .delete()
      .eq('match_id', matchId);
    
    if (perfError) throw perfError;
    
    // Delete match statistics
    const { error } = await supabase
      .from('match_statistics')
      .delete()
      .eq('match_id', matchId);
    
    if (error) throw error;
    
    // Commit transaction
    await supabase.rpc('commit_transaction');
  } catch (error) {
    // Rollback transaction
    await supabase.rpc('rollback_transaction');
    throw error;
  }
}
