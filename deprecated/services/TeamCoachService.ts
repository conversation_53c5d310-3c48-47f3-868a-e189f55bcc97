// TeamCoachService.ts
// Service for managing team coaches

import { supabase } from '@/lib/supabase';
import { TeamCoach, CoachPermissions } from '../models/TeamManagementModels';

/**
 * Service for managing team coaches and their permissions.
 * Provides methods for assigning coaches to teams, updating permissions,
 * and retrieving coach information.
 */

/**
 * Get all coaches for a team
 */
export async function getTeamCoaches(teamId: string): Promise<TeamCoach[]> {
  const { data, error } = await supabase
    .from('team_coaches')
    .select('*, permissions:coach_permissions(*)')
    .eq('team_id', teamId)
    .eq('status', 'active')
    .order('is_primary', { ascending: false });
  
  if (error) throw error;
  return data || [];
}

/**
 * Get coach history for a team
 */
export async function getTeamCoachHistory(teamId: string): Promise<TeamCoach[]> {
  const { data, error } = await supabase
    .from('team_coaches')
    .select('*, permissions:coach_permissions(*)')
    .eq('team_id', teamId)
    .order('assigned_at', { ascending: false });
  
  if (error) throw error;
  return data || [];
}

/**
 * Get a specific coach assignment
 */
export async function getCoachAssignment(assignmentId: string): Promise<TeamCoach | null> {
  const { data, error } = await supabase
    .from('team_coaches')
    .select('*, permissions:coach_permissions(*)')
    .eq('coach_assignment_id', assignmentId)
    .single();
  
  if (error) throw error;
  return data;
}

/**
 * Assign a coach to a team
 */
export async function assignCoachToTeam(
  teamId: string,
  userId: string,
  role: string,
  isPrimary: boolean,
  assignedBy: string,
  permissions?: Partial<CoachPermissions>
): Promise<TeamCoach> {
  // Start a transaction
  const { data: client } = await supabase.rpc('begin_transaction');
  
  try {
    // If this is a primary coach, update existing primary coaches
    if (isPrimary) {
      await supabase
        .from('team_coaches')
        .update({ is_primary: false })
        .eq('team_id', teamId)
        .eq('role', role)
        .eq('is_primary', true)
        .eq('status', 'active');
    }
    
    // Create new coach assignment
    const { data, error } = await supabase
      .from('team_coaches')
      .insert([{
        team_id: teamId,
        user_id: userId,
        role: role,
        is_primary: isPrimary,
        assigned_by: assignedBy,
        assigned_at: new Date(),
        status: 'active'
      }])
      .select();
    
    if (error) throw error;
    
    const coachAssignment = data[0];
    
    // Add permissions if provided
    if (permissions && coachAssignment) {
      await supabase
        .from('coach_permissions')
        .insert([{
          coach_assignment_id: coachAssignment.coach_assignment_id,
          ...permissions,
          created_by: assignedBy
        }]);
    }
    
    // Commit transaction
    await supabase.rpc('commit_transaction');
    
    return coachAssignment;
  } catch (error) {
    // Rollback transaction
    await supabase.rpc('rollback_transaction');
    throw error;
  }
}

/**
 * Update coach assignment
 */
export async function updateCoachAssignment(
  assignmentId: string,
  updates: Partial<TeamCoach>,
  permissions?: Partial<CoachPermissions>
): Promise<TeamCoach> {
  // Start a transaction
  const { data: client } = await supabase.rpc('begin_transaction');
  
  try {
    // Update coach assignment
    const { data, error } = await supabase
      .from('team_coaches')
      .update(updates)
      .eq('coach_assignment_id', assignmentId)
      .select();
    
    if (error) throw error;
    
    // Update permissions if provided
    if (permissions) {
      const { error: permError } = await supabase
        .from('coach_permissions')
        .update(permissions)
        .eq('coach_assignment_id', assignmentId);
      
      if (permError) throw permError;
    }
    
    // Commit transaction
    await supabase.rpc('commit_transaction');
    
    return data[0];
  } catch (error) {
    // Rollback transaction
    await supabase.rpc('rollback_transaction');
    throw error;
  }
}

/**
 * Remove a coach from a team
 */
export async function removeCoachFromTeam(
  assignmentId: string,
  removedBy: string
): Promise<void> {
  const { error } = await supabase
    .from('team_coaches')
    .update({
      status: 'inactive',
      removed_at: new Date(),
      removed_by: removedBy
    })
    .eq('coach_assignment_id', assignmentId);
  
  if (error) throw error;
}

/**
 * Get coaches by user
 */
export async function getCoachAssignmentsByUser(userId: string): Promise<TeamCoach[]> {
  const { data, error } = await supabase
    .from('team_coaches')
    .select('*, permissions:coach_permissions(*)')
    .eq('user_id', userId)
    .eq('status', 'active')
    .order('assigned_at', { ascending: false });
  
  if (error) throw error;
  return data || [];
}
