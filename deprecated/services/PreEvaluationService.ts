import { supabase } from '@/lib/supabase';

export interface PreEvaluationStats {
  total: number;
  pending: number;
  in_progress: number;
  completed: number;
  percentage: number;
}

export interface PreEvaluation {
  id: string;
  event_id: string;
  player_id: string;
  team_id: string;
  status: 'pending' | 'in_progress' | 'completed' | 'submitted';
  created_at: string;
  updated_at: string;
}

class PreEvaluationServiceClass {
  /**
   * Get pre-evaluation statistics for an event
   */
  async getEventPreEvaluationStats(eventId: string): Promise<PreEvaluationStats> {
    try {
      console.log('Fetching pre-evaluation stats for event:', eventId);
      
      // Get all pre-evaluations for this event
      const { data: preEvals, error } = await supabase
        .from('pre_evaluations')
        .select('id, status')
        .eq('event_id', eventId);

      if (error) {
        console.error('Error fetching pre-evaluations:', error);
        throw error;
      }

      if (!preEvals || preEvals.length === 0) {
        return {
          total: 0,
          pending: 0,
          in_progress: 0,
          completed: 0,
          percentage: 0
        };
      }

      // Calculate stats
      const stats = {
        total: preEvals.length,
        pending: preEvals.filter(e => e.status === 'pending').length,
        in_progress: preEvals.filter(e => e.status === 'in_progress').length,
        completed: preEvals.filter(e => e.status === 'completed' || e.status === 'submitted').length,
        percentage: 0
      };

      // Calculate percentage
      stats.percentage = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0;

      console.log('Pre-evaluation stats:', stats);
      return stats;
    } catch (error) {
      console.error('Error in getEventPreEvaluationStats:', error);
      return {
        total: 0,
        pending: 0,
        in_progress: 0,
        completed: 0,
        percentage: 0
      };
    }
  }

  /**
   * Check if an event has pre-evaluation enabled
   */
  async hasPreEvaluationEnabled(eventId: string): Promise<boolean> {
    try {
      const { data: event, error } = await supabase
        .from('events')
        .select('is_pre_session_evaluation')
        .eq('id', eventId)
        .single();

      if (error) {
        console.error('Error checking pre-evaluation status:', error);
        return false;
      }

      return event?.is_pre_session_evaluation || false;
    } catch (error) {
      console.error('Error in hasPreEvaluationEnabled:', error);
      return false;
    }
  }

  /**
   * Create pre-evaluation requests for all participants
   */
  async createPreEvaluationRequests(eventId: string, playerIds: string[]): Promise<void> {
    // Implementation would go here
    console.log('Creating pre-evaluation requests for event:', eventId, 'and players:', playerIds);
  }
}