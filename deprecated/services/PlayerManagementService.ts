// PlayerManagementService.ts
// Service for managing team players and invitations

import { supabase } from '@/lib/supabase';
import { PlayerInvitation, PlayerStatus } from '../models/TeamManagementModels';
import { v4 as uuidv4 } from 'uuid';

/**
 * Generate a unique invitation code
 */
function generateInvitationCode(): string {
  // Generate a random 8-character code
  return Math.random().toString(36).substring(2, 10).toUpperCase();
}

/**
 * Send invitation email
 * Note: This would typically integrate with your email service
 */
async function sendInvitationEmail(
  email: string,
  name: string,
  teamId: string,
  teamName: string,
  accessCode: string
): Promise<void> {
  // Placeholder for email sending functionality
  console.log(`Email invitation sent to ${email} for team ${teamName} with code ${accessCode}`);
  // Implementation would depend on your email service
}

/**
 * Send parent notification email
 */
async function sendParentNotification(
  email: string,
  playerName: string,
  teamId: string,
  teamName: string
): Promise<void> {
  // Placeholder for email sending functionality
  console.log(`Parent notification sent to ${email} for ${playerName} joining team ${teamName}`);
  // Implementation would depend on your email service
}

/**
 * Create and send player invitation
 */
export async function invitePlayerToTeam(
  teamId: string,
  playerData: {
    name: string,
    email: string,
    position?: string,
    jersey_number?: number,
    parent_email?: string,
    require_parent_approval?: boolean
  },
  teamName: string,
  invitedBy: string
): Promise<PlayerInvitation> {
  // Generate a unique access code
  const accessCode = generateInvitationCode();
  
  // Create invitation record
  const { data, error } = await supabase
    .from('player_invitations')
    .insert([{
      invitation_id: uuidv4(),
      team_id: teamId,
      email: playerData.email,
      name: playerData.name,
      position: playerData.position,
      jersey_number: playerData.jersey_number,
      invited_by: invitedBy,
      invited_at: new Date(),
      status: 'pending',
      access_code: accessCode,
      parent_approval_required: playerData.require_parent_approval || false,
      parent_email: playerData.parent_email
    }])
    .select();
  
  if (error) throw error;
  
  const invitation = data[0];
  
  // Send email invitation
  await sendInvitationEmail(playerData.email, playerData.name, teamId, teamName, accessCode);
  
  // Send parent notification if needed
  if (playerData.parent_email) {
    await sendParentNotification(playerData.parent_email, playerData.name, teamId, teamName);
  }
  
  return invitation;
}

/**
 * Get all pending invitations for a team
 */
export async function getPendingInvitations(teamId: string): Promise<PlayerInvitation[]> {
  const { data, error } = await supabase
    .from('player_invitations')
    .select('*')
    .eq('team_id', teamId)
    .eq('status', 'pending')
    .order('invited_at', { ascending: false });
  
  if (error) throw error;
  return data || [];
}

/**
 * Get all invitations for a team
 */
export async function getTeamInvitations(teamId: string): Promise<PlayerInvitation[]> {
  const { data, error } = await supabase
    .from('player_invitations')
    .select('*')
    .eq('team_id', teamId)
    .order('invited_at', { ascending: false });
  
  if (error) throw error;
  return data || [];
}

/**
 * Cancel an invitation
 */
export async function cancelInvitation(invitationId: string): Promise<void> {
  const { error } = await supabase
    .from('player_invitations')
    .update({ status: 'expired' })
    .eq('invitation_id', invitationId);
  
  if (error) throw error;
}

/**
 * Resend an invitation
 */
export async function resendInvitation(
  invitationId: string,
  teamName: string
): Promise<PlayerInvitation> {
  // Get the invitation details
  const { data: invitation, error } = await supabase
    .from('player_invitations')
    .select('*')
    .eq('invitation_id', invitationId)
    .single();
  
  if (error) throw error;
  
  // Update the invitation with a new timestamp
  const { data, error: updateError } = await supabase
    .from('player_invitations')
    .update({
      invited_at: new Date(),
      status: 'pending'
    })
    .eq('invitation_id', invitationId)
    .select();
  
  if (updateError) throw updateError;
  
  // Send the invitation email again
  await sendInvitationEmail(
    invitation.email,
    invitation.name,
    invitation.team_id,
    teamName,
    invitation.access_code
  );
  
  // Send parent notification if needed
  if (invitation.parent_email) {
    await sendParentNotification(
      invitation.parent_email,
      invitation.name,
      invitation.team_id,
      teamName
    );
  }
  
  return data[0];
}

/**
 * Process invitation acceptance
 */
export async function acceptInvitation(
  accessCode: string,
  userId: string
): Promise<boolean> {
  // Find the invitation by access code
  const { data: invitation, error } = await supabase
    .from('player_invitations')
    .select('*')
    .eq('access_code', accessCode)
    .eq('status', 'pending')
    .single();
  
  if (error) throw error;
  
  if (!invitation) {
    return false;
  }
  
  // Handle parent approval requirement
  if (invitation.parent_approval_required && !invitation.parent_approved) {
    const { error: updateError } = await supabase
      .from('player_invitations')
      .update({ parent_approved: true })
      .eq('invitation_id', invitation.invitation_id);
    
    if (updateError) throw updateError;
    return false; // Still waiting for acceptance
  }
  
  // Start a transaction
  const { data: client } = await supabase.rpc('begin_transaction');
  
  try {
    // Update invitation status
    await supabase
      .from('player_invitations')
      .update({
        status: 'accepted',
        accepted_at: new Date()
      })
      .eq('invitation_id', invitation.invitation_id);
    
    // Add user to team_members
    await supabase
      .from('team_members')
      .insert([{
        membership_id: uuidv4(),
        team_id: invitation.team_id,
        user_id: userId,
        role: invitation.role || 'player',
        jersey_number: invitation.jersey_number,
        position: invitation.position,
        joined_at: new Date(),
        status: 'active',
        invited_by: invitation.invited_by,
        accepted_at: new Date()
      }]);
    
    // Commit transaction
    await supabase.rpc('commit_transaction');
    
    return true;
  } catch (error) {
    // Rollback transaction
    await supabase.rpc('rollback_transaction');
    throw error;
  }
}

/**
 * Decline invitation
 */
export async function declineInvitation(accessCode: string): Promise<void> {
  const { error } = await supabase
    .from('player_invitations')
    .update({ status: 'declined' })
    .eq('access_code', accessCode)
    .eq('status', 'pending');
  
  if (error) throw error;
}

/**
 * Create bulk invitations
 */
export async function createBulkInvitations(
  teamId: string,
  players: Array<{
    name: string;
    email: string;
    position?: string;
    jersey_number?: number;
    parent_email?: string;
    require_parent_approval?: boolean;
  }>,
  teamName: string,
  invitedBy: string
): Promise<PlayerInvitation[]> {
  const invitations = players.map(player => ({
    invitation_id: uuidv4(),
    team_id: teamId,
    email: player.email,
    name: player.name,
    position: player.position,
    jersey_number: player.jersey_number,
    invited_by: invitedBy,
    invited_at: new Date(),
    status: 'pending',
    access_code: generateInvitationCode(),
    parent_approval_required: player.require_parent_approval || false,
    parent_email: player.parent_email
  }));
  
  const { data, error } = await supabase
    .from('player_invitations')
    .insert(invitations)
    .select();
  
  if (error) throw error;
  
  // Send emails
  for (const invitation of data) {
    await sendInvitationEmail(
      invitation.email,
      invitation.name,
      teamId,
      teamName,
      invitation.access_code
    );
    
    if (invitation.parent_email) {
      await sendParentNotification(
        invitation.parent_email,
        invitation.name,
        teamId,
        teamName
      );
    }
  }
  
  return data;
}

/**
 * Update player status
 */
export async function updatePlayerStatus(
  playerId: string,
  teamId: string,
  statusData: {
    status_type: 'active' | 'injured' | 'suspended' | 'inactive';
    start_date: Date;
    end_date?: Date;
    reason?: string;
    notes?: string;
  },
  createdBy: string
): Promise<PlayerStatus> {
  // Start a transaction
  const { data: client } = await supabase.rpc('begin_transaction');
  
  try {
    // Create status history record
    const { data, error } = await supabase
      .from('player_status_history')
      .insert([{
        status_id: uuidv4(),
        player_id: playerId,
        team_id: teamId,
        status_type: statusData.status_type,
        start_date: statusData.start_date,
        end_date: statusData.end_date,
        reason: statusData.reason,
        notes: statusData.notes,
        created_by: createdBy,
        created_at: new Date()
      }])
      .select();
    
    if (error) throw error;
    
    // Update player status in team_members
    const { error: updateError } = await supabase
      .from('team_members')
      .update({ status: statusData.status_type })
      .eq('membership_id', playerId);
    
    if (updateError) throw updateError;
    
    // Commit transaction
    await supabase.rpc('commit_transaction');
    
    return data[0];
  } catch (error) {
    // Rollback transaction
    await supabase.rpc('rollback_transaction');
    throw error;
  }
}

/**
 * Get player status history
 */
export async function getPlayerStatusHistory(playerId: string): Promise<PlayerStatus[]> {
  const { data, error } = await supabase
    .from('player_status_history')
    .select('*')
    .eq('player_id', playerId)
    .order('start_date', { ascending: false });
  
  if (error) throw error;
  return data || [];
}

/**
 * Update player details
 */
export async function updatePlayerDetails(
  playerId: string,
  updates: {
    jersey_number?: number;
    position?: string;
    role?: string;
  }
): Promise<void> {
  const { error } = await supabase
    .from('team_members')
    .update(updates)
    .eq('membership_id', playerId);
  
  if (error) throw error;
}
