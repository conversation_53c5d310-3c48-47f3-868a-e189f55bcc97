/* Direct override for .entity-actions to ensure proper styling */
.entity-card .entity-actions {
  display: flex !important;
  flex-wrap: nowrap !important;
  gap: 10px !important;
  justify-content: center !important;
  margin-top: 16px !important;
  border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  padding-top: 16px !important;
}

/* Direct override for .action-button to ensure proper styling */
.entity-card .action-button {
  --color: var(--shot-teal) !important;
  --border-color: var(--shot-teal) !important;
  font-size: 0.8rem !important;
  height: 34px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.05em !important;
  --padding-start: 12px !important;
  --padding-end: 12px !important;
  min-width: unset !important;
  flex-grow: 0 !important;
  width: auto !important;
  max-width: fit-content !important;
}

.entity-card .action-button ion-icon {
  font-size: 16px !important;
}