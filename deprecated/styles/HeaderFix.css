/* Global fixes for the header logo green box issue */

/* Override any green backgrounds in the header */
ion-header, 
ion-toolbar, 
.standard-header-toolbar, 
.standard-header-toolbar div,
.standard-header-toolbar span {
  background-color: black !important;
}

/* Make sure the logo has no background */
.standard-header-toolbar img,
img[src*="/logo.png"],
img[src*="/logo-black.png"] {
  background-color: transparent !important;
  background: transparent !important;
}

/* The green box in the top left */
ion-header > ion-toolbar > div > div:first-child,
ion-header > ion-toolbar > div > div:first-child > div,
ion-header > ion-toolbar > div > div:first-child > div > ion-button {
  background-color: black !important;
  background: black !important;
  --background: black !important;
}

/* Make sure all buttons in the header have transparent backgrounds */
.standard-header-toolbar ion-button,
.standard-header-toolbar ion-button::part(native) {
  --background: transparent !important;
  background-color: transparent !important;
}

/* Ensure no padding or margins create green spaces */
.standard-header-toolbar ion-buttons,
.standard-header-toolbar ion-button::part(native),
.standard-header-toolbar div {
  padding: 0 !important;
  margin: 0 !important;
}

/* Force black background on the logo container */
.flex > div:first-child {
  background-color: black !important;
}
