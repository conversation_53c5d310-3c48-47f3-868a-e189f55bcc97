import { Page } from '@playwright/test';

export async function acceptCookieConsent(page: Page) {
  try {
    // Wait for the CookieConsent component
    await page.waitForSelector('.CookieConsent', { timeout: 5000 });
    
    // Click the Accept button
    const acceptButton = await page.getByRole('button', { name: 'Accept cookies' });
    if (await acceptButton.count() > 0) {
      await acceptButton.click();
      
      // Wait for localStorage to be updated
      await page.waitForFunction(() => {
        return Object.keys(localStorage).some(key => key.startsWith('cookie_consent_'));
      }, { timeout: 5000 });
    }
  } catch (e) {
    console.log('Cookie consent handling failed:', e);
  }
} 