import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  const { baseURL } = config.projects[0].use;
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // Navigate to the app
  await page.goto(baseURL!);
  
  // Wait for and handle cookie consent
  try {
    // Wait for the CookieConsent component from react-cookie-consent
    await page.waitForSelector('.CookieConsent', { timeout: 5000 });
    
    // Debug: Log the button we find
    const buttons = await page.$$eval('button', (btns) => 
      btns.map(btn => ({
        text: btn.textContent,
        ariaLabel: btn.getAttribute('aria-label'),
        class: btn.className
      }))
    );
    console.log('Available buttons:', buttons);
    
    // Try to find the accept button using multiple strategies
    const acceptButton = await page.locator([
      'button:has-text("Accept")',
      '.CookieConsent button:first-of-type',
      'button[style*="background: rgb(16, 185, 129)"]'
    ].join(', ')).first();
    
    if (await acceptButton.count() > 0) {
      await acceptButton.click();
      
      // Wait for localStorage to be updated (max 5 seconds)
      let attempts = 0;
      while (attempts < 50) {
        const consent = await page.evaluate(() => {
          const consents = Object.keys(localStorage).filter(key => key.startsWith('cookie_consent_'));
          console.log('Current consents:', consents);
          return consents.length > 0;
        });
        
        if (consent) {
          console.log('Cookie consent set successfully');
          break;
        }
        
        await page.waitForTimeout(100);
        attempts++;
      }
      
      if (attempts >= 50) {
        console.log('Timed out waiting for cookie consent to be set');
      }
    } else {
      console.log('Accept button not found');
      
      // Debug: Log the HTML structure
      const html = await page.$eval('.CookieConsent', el => el.outerHTML);
      console.log('Cookie consent HTML:', html);
    }
  } catch (e) {
    console.log('Cookie banner interaction failed:', e);
    
    // Debug: Log the entire page HTML if the banner is not found
    const html = await page.$eval('body', el => el.outerHTML);
    console.log('Page HTML:', html);
  } finally {
    await context.close();
    await browser.close();
  }
}

export default globalSetup; 