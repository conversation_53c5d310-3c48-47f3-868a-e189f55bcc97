import { test, expect } from '@playwright/test';
import { acceptCookieConsent } from './test-utils';

test.describe('Login Flow', () => {
  const TEST_EMAIL = process.env.TEST_USER_EMAIL || '<EMAIL>';
  const TEST_PASSWORD = process.env.TEST_USER_PASSWORD || 'password';

  test.beforeEach(async ({ page }) => {
    test.setTimeout(60000);
    await page.goto('/');
    await acceptCookieConsent(page);
  });

  test('should open login form when clicking sign in', async ({ page }) => {
    await expect(page).toHaveURL('/');
    
    await page.waitForLoadState('networkidle');
    
    // Click the initial sign in button to open the modal
    const openModalButton = page.getByRole('button', { name: /sign in/i });
    await openModalButton.click();
    
    // Verify the login form elements in the modal
    await expect(page.getByRole('textbox', { name: /email/i })).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
    await expect(page.getByRole('button', { name: /login/i })).toBeVisible();
  });

  test('should display validation errors for empty form submission', async ({ page }) => {
    await page.goto('/');
    const openModalButton = page.getByRole('button', { name: /sign in/i });
    await openModalButton.click();
    
    // Click the Login button in the modal
    const loginButton = page.getByRole('button', { name: /login/i });
    await loginButton.click();
    
    // Wait for and verify the error toast message
    await expect(
      page.getByText('You must provide either an email, phone number, a third-party provider or OpenID Connect.', 
        { exact: true }
      )
    ).toBeVisible({ timeout: 5000 });
  });

  test('should display error for invalid credentials', async ({ page }) => {
    await page.goto('/');
    const openModalButton = page.getByRole('button', { name: /sign in/i });
    await openModalButton.click();
    
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'wrongpassword');
    
    const loginButton = page.getByRole('button', { name: /login/i });
    await loginButton.click();

    // Wait for the error message with a more specific selector
    await expect(
      page.getByText(/invalid login credentials/i, { exact: false })
    ).toBeVisible({ timeout: 5000 });
  });

  test('should successfully log in and redirect to home', async ({ page }) => {
    await page.goto('/');
    const openModalButton = page.getByRole('button', { name: /sign in/i });
    await openModalButton.click();
    
    await page.fill('input[type="email"]', TEST_EMAIL);
    await page.fill('input[type="password"]', TEST_PASSWORD);
    
    const loginButton = page.getByRole('button', { name: /login/i });
    await loginButton.click();

    // After successful login, app redirects to /home
    await expect(page).toHaveURL('/home');
    await expect(page.locator('ion-tab-bar')).toBeVisible();
  });

  test('should prompt for invite code when signing up', async ({ page }) => {
    await page.goto('/');
    const signUpButton = page.getByRole('button', { name: /sign up/i });
    await signUpButton.click();
    
    // Verify the title shows "Enter Invite Code"
    await expect(page.getByRole('heading', { name: 'Enter Invite Code' })).toBeVisible();
    
    // Verify the input field with stacked label
    await expect(page.getByText('Invite Code')).toBeVisible();
    await expect(page.getByPlaceholder('Enter your invite code')).toBeVisible();
    
    // Verify the verify button is present
    await expect(page.getByRole('button', { name: 'Verify Code' })).toBeVisible();
  });
}); 