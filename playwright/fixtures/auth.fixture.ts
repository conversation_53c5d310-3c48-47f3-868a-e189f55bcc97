import { test as base } from '@playwright/test';

export type AuthFixtures = {
  login: (email?: string, password?: string) => Promise<void>;
};

export const test = base.extend<AuthFixtures>({
  login: async ({ page }, use) => {
    await use(async (
      email = process.env.TEST_USER_EMAIL,
      password = process.env.TEST_USER_PASSWORD
    ) => {
      await page.goto('/login');
      await page.fill('input[type="email"]', email || '');
      await page.fill('input[type="password"]', password || '');
      const submitButton = page.getByRole('button', { name: /sign in/i });
      await submitButton.click();
      await page.waitForURL('/home');
    });
  },
}); 