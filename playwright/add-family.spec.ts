import { test, expect } from '@playwright/test';
import { acceptCookieConsent } from './test-utils';

test.describe('Add Family Flow', () => {
  const TEST_EMAIL = process.env.TEST_USER_EMAIL || '<EMAIL>';
  const TEST_PASSWORD = process.env.TEST_USER_PASSWORD || 'password';

  test.beforeEach(async ({ page }) => {
    test.setTimeout(60000);
    await page.goto('/');
    await acceptCookieConsent(page);
    
    // Log in
    const openModalButton = page.getByRole('button', { name: /sign in/i });
    await openModalButton.click();
    
    await page.fill('input[type="email"]', TEST_EMAIL);
    await page.fill('input[type="password"]', TEST_PASSWORD);
    
    const loginButton = page.getByRole('button', { name: /login/i });
    await loginButton.click();

    // Wait for redirect to home page
    await expect(page).toHaveURL('/home');
  });

  test('should navigate to add family page and add a child', async ({ page }) => {
    // Click the Add Family button on the home page
    await page.getByText('Add Family').click();
    await expect(page).toHaveURL('/add-family');

    // Fill in child's basic information
    await page.fill('input[placeholder="Enter child\'s name"]', 'Test Child');
    
    // Click the date field to show picker
    await page.locator('.mt-1.p-2.w-full.border').click();
    // Set date of birth
    await page.locator('ion-datetime').evaluate((el: any) => {
      el.value = '2020-01-01';
    });
    // Click done to confirm date
    await page.getByRole('button', { name: 'Done' }).click();
    
    // Add an allergy
    await page.fill('input[placeholder="Add allergy"]', 'Peanuts');
    await page.locator('button[aria-label="Add allergy"]').click();
    
    // Fill in emergency contact (required fields)
    await page.fill('input[placeholder="Emergency contact name"]', 'Emergency Contact');
    await page.fill('input[placeholder="Emergency contact phone"]', '1234567890');
    await page.fill('input[placeholder="Relationship to child"]', 'Parent');

    // Save the child and wait for navigation
    await Promise.all([
      page.waitForURL('/family'),
      page.getByRole('button', { name: 'Save Child' }).click()
    ]);

    // Verify the child appears in the family list
    await expect(page.getByText('Test Child')).toBeVisible();
  });
}); 