// ABOUTME: Page object for the product detail page
// Provides methods for interacting with product details, variants, and add to cart

import { Page, Locator } from '@playwright/test';

export class ProductPage {
  readonly page: Page;
  readonly productName: Locator;
  readonly productPrice: Locator;
  readonly productDescription: Locator;
  readonly addToCartButton: Locator;
  readonly quantityInput: Locator;
  readonly variantSelectors: Locator;
  readonly productImages: Locator;
  readonly backToShopLink: Locator;

  constructor(page: Page) {
    this.page = page;
    this.productName = page.locator('[data-testid="product-name"]');
    this.productPrice = page.locator('[data-testid="product-price"]');
    this.productDescription = page.locator('[data-testid="product-description"]');
    this.addToCartButton = page.locator('[data-testid="add-to-cart-button"]');
    this.quantityInput = page.locator('[data-testid="quantity-input"]');
    this.variantSelectors = page.locator('[data-testid^="variant-selector-"]');
    this.productImages = page.locator('[data-testid="product-image"]');
    this.backToShopLink = page.locator('[data-testid="back-to-shop"]');
  }

  async goto(productId: string) {
    await this.page.goto(`/locker/product/${productId}`);
    await this.page.waitForLoadState('networkidle');
  }

  async selectVariant(variantType: string, variantValue: string) {
    const selector = this.page.locator(`[data-testid="variant-selector-${variantType}"]`);
    await selector.selectOption(variantValue);
  }

  async setQuantity(quantity: number) {
    await this.quantityInput.fill(quantity.toString());
  }

  async addToCart() {
    await this.addToCartButton.click();
    // Wait for cart drawer to open
    await this.page.locator('[data-testid="cart-drawer"]').waitFor({ state: 'visible' });
  }

  async getProductInfo() {
    return {
      name: await this.productName.textContent(),
      price: await this.productPrice.textContent(),
      description: await this.productDescription.textContent()
    };
  }

  async isAddToCartEnabled(): Promise<boolean> {
    return await this.addToCartButton.isEnabled();
  }

  async waitForProductToLoad() {
    await this.productName.waitFor({ state: 'visible' });
    await this.productPrice.waitFor({ state: 'visible' });
  }
}