// ABOUTME: Page object for the shopping cart page
// Provides methods for cart management including updating quantities and checkout

import { Page, Locator } from '@playwright/test';

export class CartPage {
  readonly page: Page;
  readonly cartItems: Locator;
  readonly emptyCartMessage: Locator;
  readonly subtotal: Locator;
  readonly tax: Locator;
  readonly shipping: Locator;
  readonly total: Locator;
  readonly checkoutButton: Locator;
  readonly continueShoppingButton: Locator;
  readonly clearCartButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.cartItems = page.locator('[data-testid^="cart-item-"]');
    this.emptyCartMessage = page.locator('[data-testid="empty-cart-message"]');
    this.subtotal = page.locator('[data-testid="cart-subtotal"]');
    this.tax = page.locator('[data-testid="cart-tax"]');
    this.shipping = page.locator('[data-testid="cart-shipping"]');
    this.total = page.locator('[data-testid="cart-total"]');
    this.checkoutButton = page.locator('[data-testid="checkout-button"]');
    this.continueShoppingButton = page.locator('[data-testid="continue-shopping-button"]');
    this.clearCartButton = page.locator('[data-testid="clear-cart-button"]');
  }

  async goto() {
    await this.page.goto('/locker/cart');
    await this.page.waitForLoadState('networkidle');
  }

  async getCartItemCount(): Promise<number> {
    return await this.cartItems.count();
  }

  async updateItemQuantity(itemId: string, quantity: number) {
    const quantityInput = this.page.locator(`[data-testid="quantity-input-${itemId}"]`);
    await quantityInput.fill(quantity.toString());
    // Wait for cart to update
    await this.page.waitForTimeout(500);
  }

  async removeItem(itemId: string) {
    const removeButton = this.page.locator(`[data-testid="remove-item-${itemId}"]`);
    await removeButton.click();
    // Wait for item to be removed
    await this.page.waitForTimeout(500);
  }

  async clearCart() {
    await this.clearCartButton.click();
    // Confirm if there's a dialog
    const dialog = this.page.locator('[data-testid="confirm-clear-cart"]');
    if (await dialog.isVisible()) {
      await dialog.click();
    }
  }

  async proceedToCheckout() {
    await this.checkoutButton.click();
    await this.page.waitForURL('**/locker/checkout');
  }

  async continueShopping() {
    await this.continueShoppingButton.click();
  }

  async getCartTotals() {
    return {
      subtotal: await this.subtotal.textContent(),
      tax: await this.tax.textContent(),
      shipping: await this.shipping.textContent(),
      total: await this.total.textContent()
    };
  }

  async isCartEmpty(): Promise<boolean> {
    return await this.emptyCartMessage.isVisible();
  }

  async waitForCartToLoad() {
    // Wait for either cart items or empty message
    await this.page.waitForSelector('[data-testid^="cart-item-"], [data-testid="empty-cart-message"]');
  }
}