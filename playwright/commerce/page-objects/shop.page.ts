// ABOUTME: Page object for the shop/product listing page
// Provides reusable methods for interacting with the shop page in tests

import { Page, Locator } from '@playwright/test';

export class ShopPage {
  readonly page: Page;
  readonly productGrid: Locator;
  readonly loadMoreButton: Locator;
  readonly cartIcon: Locator;
  readonly cartCount: Locator;

  constructor(page: Page) {
    this.page = page;
    this.productGrid = page.locator('[data-testid="product-grid"]');
    this.loadMoreButton = page.getByText('Load More');
    this.cartIcon = page.locator('[data-testid="cart-icon"]');
    this.cartCount = page.locator('[data-testid="cart-count"]');
  }

  async goto() {
    await this.page.goto('/locker/shop');
    await this.page.waitForLoadState('networkidle');
  }

  async getProductCards() {
    return this.page.locator('[data-testid^="product-card-"]').all();
  }

  async getProductCard(productId: string) {
    return this.page.locator(`[data-testid="product-card-${productId}"]`);
  }

  async clickProduct(productId: string) {
    const productCard = await this.getProductCard(productId);
    await productCard.click();
  }

  async addToCartFromListing(productId: string) {
    const productCard = await this.getProductCard(productId);
    const addButton = productCard.locator('[data-testid="add-to-cart-button"]');
    await addButton.click();
  }

  async waitForProductsToLoad() {
    await this.productGrid.waitFor({ state: 'visible' });
    // Wait for at least one product card
    await this.page.locator('[data-testid^="product-card-"]').first().waitFor();
  }

  async loadMoreProducts() {
    await this.loadMoreButton.click();
    // Wait for additional products to load
    await this.page.waitForTimeout(1000);
  }

  async getCartItemCount(): Promise<number> {
    const countText = await this.cartCount.textContent();
    return parseInt(countText || '0', 10);
  }

  async openCartDrawer() {
    await this.cartIcon.click();
    await this.page.locator('[data-testid="cart-drawer"]').waitFor({ state: 'visible' });
  }
}