// ABOUTME: Test product data fixtures for commerce E2E tests
// Provides sample products that should exist in BigCommerce for testing

export const testProducts = {
  simpleProduct: {
    id: '123', // Replace with actual product ID from BigCommerce
    name: 'SHOT Training Jersey',
    price: 45.00,
    sku: 'SHOT-TJ-001'
  },
  
  productWithVariants: {
    id: '124', // Replace with actual product ID
    name: 'SHOT Team Kit',
    basePrice: 75.00,
    sku: 'SHOT-KIT-001',
    variants: {
      size: ['Small', 'Medium', 'Large', 'XL'],
      color: ['Red', 'Blue', 'Black']
    }
  },
  
  freeShippingProduct: {
    id: '125', // Replace with actual product ID
    name: 'SHOT Premium Bundle',
    price: 150.00,
    sku: 'SHOT-BUNDLE-001'
  },
  
  outOfStockProduct: {
    id: '126', // Replace with actual product ID
    name: 'Limited Edition Jersey',
    price: 99.00,
    sku: 'SHOT-LTD-001'
  }
};

// Helper to get random test product
export function getRandomProduct() {
  const products = Object.values(testProducts);
  return products[Math.floor(Math.random() * products.length)];
}