// ABOUTME: Core shopping flow E2E tests for the commerce section
// Tests the complete user journey from browsing to checkout

import { test, expect } from '@playwright/test';
import { ShopPage } from './page-objects/shop.page';
import { ProductPage } from './page-objects/product.page';
import { CartPage } from './page-objects/cart.page';
import { testProducts } from './fixtures/test-products';

test.describe('Commerce Shopping Flow', () => {
  let shopPage: ShopPage;
  let productPage: ProductPage;
  let cartPage: CartPage;

  test.beforeEach(async ({ page }) => {
    shopPage = new ShopPage(page);
    productPage = new ProductPage(page);
    cartPage = new CartPage(page);
    
    // Start fresh - clear any existing cart
    await page.goto('/locker/cart');
    const clearButton = page.locator('[data-testid="clear-cart-button"]');
    if (await clearButton.isVisible()) {
      await clearButton.click();
    }
  });

  test('complete purchase flow - browse to checkout', async ({ page }) => {
    // Step 1: Browse products
    await shopPage.goto();
    await shopPage.waitForProductsToLoad();
    
    // Verify products are displayed
    const productCards = await shopPage.getProductCards();
    expect(productCards.length).toBeGreaterThan(0);
    
    // Step 2: Click on first product
    const firstProduct = productCards[0];
    const productId = await firstProduct.getAttribute('data-testid');
    const extractedId = productId?.replace('product-card-', '') || '';
    
    await shopPage.clickProduct(extractedId);
    
    // Step 3: View product details
    await productPage.waitForProductToLoad();
    const productInfo = await productPage.getProductInfo();
    expect(productInfo.name).toBeTruthy();
    expect(productInfo.price).toBeTruthy();
    
    // Step 4: Add to cart
    await productPage.setQuantity(2);
    await productPage.addToCart();
    
    // Verify cart drawer opens and shows item
    await expect(page.locator('[data-testid="cart-drawer"]')).toBeVisible();
    const cartCount = await shopPage.getCartItemCount();
    expect(cartCount).toBe(2);
    
    // Step 5: Go to cart
    await cartPage.goto();
    await cartPage.waitForCartToLoad();
    
    const itemCount = await cartPage.getCartItemCount();
    expect(itemCount).toBe(1); // 1 product line item
    
    // Step 6: Verify totals
    const totals = await cartPage.getCartTotals();
    expect(totals.subtotal).toBeTruthy();
    expect(totals.total).toBeTruthy();
    
    // Step 7: Proceed to checkout
    await cartPage.proceedToCheckout();
    await expect(page).toHaveURL(/.*locker\/checkout/);
  });

  test('add multiple products to cart', async ({ page }) => {
    // Add first product
    await shopPage.goto();
    await shopPage.waitForProductsToLoad();
    
    const productCards = await shopPage.getProductCards();
    expect(productCards.length).toBeGreaterThan(1);
    
    // Add first product from listing
    const firstProductId = await productCards[0].getAttribute('data-testid');
    const firstId = firstProductId?.replace('product-card-', '') || '';
    await shopPage.addToCartFromListing(firstId);
    
    // Wait for notification
    await page.waitForTimeout(1000);
    
    // Add second product
    const secondProductId = await productCards[1].getAttribute('data-testid');
    const secondId = secondProductId?.replace('product-card-', '') || '';
    await shopPage.addToCartFromListing(secondId);
    
    // Verify cart count
    const cartCount = await shopPage.getCartItemCount();
    expect(cartCount).toBe(2);
    
    // Go to cart and verify
    await cartPage.goto();
    const itemCount = await cartPage.getCartItemCount();
    expect(itemCount).toBe(2);
  });

  test('update cart quantities', async ({ page }) => {
    // Add a product first
    await shopPage.goto();
    await shopPage.waitForProductsToLoad();
    
    const productCards = await shopPage.getProductCards();
    const firstProductId = await productCards[0].getAttribute('data-testid');
    const productId = firstProductId?.replace('product-card-', '') || '';
    
    await shopPage.clickProduct(productId);
    await productPage.waitForProductToLoad();
    await productPage.addToCart();
    
    // Go to cart
    await cartPage.goto();
    await cartPage.waitForCartToLoad();
    
    // Update quantity
    const cartItemId = await page.locator('[data-testid^="cart-item-"]').first().getAttribute('data-testid');
    const itemId = cartItemId?.replace('cart-item-', '') || '';
    
    await cartPage.updateItemQuantity(itemId, 3);
    
    // Verify cart updates
    const totals = await cartPage.getCartTotals();
    expect(totals.subtotal).toBeTruthy();
  });

  test('remove items from cart', async ({ page }) => {
    // Add a product first
    await shopPage.goto();
    await shopPage.waitForProductsToLoad();
    
    const productCards = await shopPage.getProductCards();
    const firstProductId = await productCards[0].getAttribute('data-testid');
    const productId = firstProductId?.replace('product-card-', '') || '';
    
    await shopPage.clickProduct(productId);
    await productPage.waitForProductToLoad();
    await productPage.addToCart();
    
    // Go to cart
    await cartPage.goto();
    await cartPage.waitForCartToLoad();
    
    // Remove item
    const cartItemId = await page.locator('[data-testid^="cart-item-"]').first().getAttribute('data-testid');
    const itemId = cartItemId?.replace('cart-item-', '') || '';
    
    await cartPage.removeItem(itemId);
    
    // Verify cart is empty
    await expect(cartPage.emptyCartMessage).toBeVisible();
    const isEmpty = await cartPage.isCartEmpty();
    expect(isEmpty).toBe(true);
  });

  test('cart persists on page refresh', async ({ page }) => {
    // Add a product
    await shopPage.goto();
    await shopPage.waitForProductsToLoad();
    
    const productCards = await shopPage.getProductCards();
    const firstProductId = await productCards[0].getAttribute('data-testid');
    const productId = firstProductId?.replace('product-card-', '') || '';
    
    await shopPage.clickProduct(productId);
    await productPage.waitForProductToLoad();
    await productPage.addToCart();
    
    // Get cart count before refresh
    const cartCountBefore = await shopPage.getCartItemCount();
    
    // Refresh page
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Verify cart persists
    const cartCountAfter = await shopPage.getCartItemCount();
    expect(cartCountAfter).toBe(cartCountBefore);
    
    // Go to cart and verify items
    await cartPage.goto();
    const isEmpty = await cartPage.isCartEmpty();
    expect(isEmpty).toBe(false);
  });

  test('free shipping threshold', async ({ page }) => {
    // This test assumes free shipping over $50
    // Add a low-value product first
    await shopPage.goto();
    await shopPage.waitForProductsToLoad();
    
    const productCards = await shopPage.getProductCards();
    const firstProductId = await productCards[0].getAttribute('data-testid');
    const productId = firstProductId?.replace('product-card-', '') || '';
    
    await shopPage.clickProduct(productId);
    await productPage.waitForProductToLoad();
    await productPage.addToCart();
    
    // Go to cart
    await cartPage.goto();
    await cartPage.waitForCartToLoad();
    
    // Check shipping cost
    const totals = await cartPage.getCartTotals();
    const shippingText = totals.shipping || '';
    
    // If subtotal is under $50, shipping should not be free
    // This is a simplified test - adjust based on your actual shipping rules
    expect(shippingText).toBeTruthy();
  });
});