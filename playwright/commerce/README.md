# Commerce Playwright Tests

This directory contains E2E tests for the commerce/locker section of the SHOT app.

## Structure

```
commerce/
├── fixtures/          # Test data (products, users, addresses)
├── page-objects/      # Page object models for maintainability
├── utils/            # Helper functions
├── shopping-flow.spec.ts      # Core shopping flow tests
├── cart-persistence.spec.ts   # Cart state management tests
├── guardian-approval.spec.ts  # Youth user approval flows
└── error-handling.spec.ts     # Edge cases and error scenarios
```

## Running Tests

```bash
# Run all commerce tests
npm run test:e2e -- playwright/commerce/

# Run specific test file
npm run test:e2e -- playwright/commerce/shopping-flow.spec.ts

# Run in UI mode for debugging
npm run test:e2e:ui -- playwright/commerce/

# Run with specific browser
npm run test:e2e -- --project=chromium playwright/commerce/
```

## Before Running Tests

1. **Update Product IDs**: Edit `fixtures/test-products.ts` with actual product IDs from your BigCommerce store
2. **Ensure Test Data**: Make sure test products exist and are visible in BigCommerce
3. **Database Setup**: Ensure cart_sessions table and RPC functions are properly configured

## Writing New Tests

1. **Use Page Objects**: Create reusable page objects for new pages
2. **Add data-testid**: Ensure all interactive elements have `data-testid` attributes
3. **Handle Loading States**: Always wait for elements to be visible/ready
4. **Clean Up**: Start tests with a clean slate (clear cart, logout, etc.)

## Test Categories

### Core Tests (Priority 1)
- ✅ Complete shopping flow
- ✅ Cart management (add, update, remove)
- ✅ Cart persistence
- ⏳ Checkout process
- ⏳ Order confirmation

### Advanced Tests (Priority 2)
- ⏳ Product variants selection
- ⏳ Coupon codes
- ⏳ Shipping calculations
- ⏳ Guest vs authenticated checkout

### Edge Cases (Priority 3)
- ⏳ Out of stock handling
- ⏳ Network errors
- ⏳ Invalid data handling
- ⏳ Session expiration

## Debugging Tips

1. **Use UI Mode**: `npm run test:e2e:ui` for visual debugging
2. **Add Screenshots**: Use `await page.screenshot({ path: 'debug.png' })` 
3. **Check Selectors**: Verify data-testid attributes are present
4. **Network Tab**: Monitor API calls in Playwright Inspector
5. **Slow Motion**: Add `--slow-mo=1000` to see actions in real-time

## CI Integration

These tests should run on every PR that touches commerce code:

```yaml
# .github/workflows/commerce-tests.yml
on:
  pull_request:
    paths:
      - 'src/features/locker/**'
      - 'src/pages/shop/**'
      - 'src/services/BigCommerceService.ts'
      - 'src/contexts/EnhancedShoppingCartContext.tsx'
```

## Common Issues

1. **Product Not Found**: Ensure test product IDs match actual BigCommerce products
2. **Cart Not Persisting**: Check database connection and RPC functions
3. **Timeout Errors**: Increase timeout in playwright.config.ts
4. **Auth Issues**: Ensure test user has proper permissions